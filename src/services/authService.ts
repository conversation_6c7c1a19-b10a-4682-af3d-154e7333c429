import type { User, RegisterForm } from "../types";
import { apiService } from "./api";

interface LoginResponse {
  token: string;
  user: User;
}

interface RegisterResponse {
  message: string;
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
}

class AuthService {
  public setToken(token: string) {
    apiService.setToken(token);
  }

  public removeToken() {
    apiService.removeToken();
  }

  public getToken(): string | null {
    return apiService.getToken();
  }

  public isAuthenticated(): boolean {
    return !!this.getToken();
  }

  public async login(email: string, password: string): Promise<LoginResponse> {
    const response = await apiService.post<LoginResponse>("/auth/login", {
      email,
      password,
    });
    return response;
  }

  public async register(userData: RegisterForm): Promise<RegisterResponse> {
    const response = await apiService.post<RegisterResponse>(
      "/auth/register",
      userData
    );
    return response;
  }

  public async logout(): Promise<void> {
    await apiService.post("/auth/logout");
  }

  public async getCurrentUser(): Promise<User> {
    // Add cache-busting parameter to prevent 304 responses
    const timestamp = Date.now();
    const response = await apiService.get<User>(`/auth/me?t=${timestamp}`);
    return response;
  }

  public async refreshToken(): Promise<LoginResponse> {
    const response = await apiService.post<LoginResponse>("/auth/refresh");
    return response;
  }

  public async forgotPassword(email: string): Promise<{ message: string }> {
    const response = await apiService.post<{ message: string }>(
      "/auth/forgot-password",
      {
        email,
      }
    );
    return response;
  }

  public async resetPassword(
    token: string,
    password: string
  ): Promise<{ message: string }> {
    const response = await apiService.post<{ message: string }>(
      "/auth/reset-password",
      {
        token,
        password,
      }
    );
    return response;
  }

  public async changePassword(
    currentPassword: string,
    newPassword: string
  ): Promise<{ message: string }> {
    const response = await apiService.post<{ message: string }>(
      "/auth/change-password",
      {
        currentPassword,
        newPassword,
      }
    );
    return response;
  }

  public async updateProfile(userData: Partial<User>): Promise<User> {
    const response = await apiService.put<User>("/auth/profile", userData);
    return response;
  }

  public async verifyEmail(token: string): Promise<{ message: string }> {
    const response = await apiService.post<{ message: string }>(
      "/auth/verify-email",
      {
        token,
      }
    );
    return response;
  }

  public async resendVerificationEmail(): Promise<{ message: string }> {
    const response = await apiService.post<{ message: string }>(
      "/auth/resend-verification"
    );
    return response;
  }
}

export const authService = new AuthService();
