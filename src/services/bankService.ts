// import { apiService } from './api';

export interface BankAccount {
  id: string;
  companyId: string;
  bankName: string;
  accountName: string;
  accountNumber: string;
  routingNumber?: string;
  accountType: 'CHECKING' | 'SAVINGS' | 'CREDIT' | 'INVESTMENT' | 'LOAN';
  balance: number;
  currency: string;
  isActive: boolean;
  autoReconciliation: boolean;
  syncFrequency: 'REAL_TIME' | 'HOURLY' | 'DAILY' | 'WEEKLY';
  alertSettings: Record<string, any>;
  reconciliationRules: any[];
  lastSyncAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BankTransaction {
  id: string;
  bankAccountId: string;
  companyId: string;
  transactionId: string;
  amount: number;
  description: string;
  category?: string;
  date: string;
  pending: boolean;
  paymentChannel: string;
  isReconciled: boolean;
  reconciliationStatus: 'UNRECONCILED' | 'MATCHED' | 'IGNORED' | 'DISPUTED';
  accountingTransactionId?: string;
  autoMatched: boolean;
  matchConfidence?: number;
  reference?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BankConnectionConfig {
  bankName: string;
  accountType: BankAccount['accountType'];
  credentials: Record<string, any>;
  autoReconciliation?: boolean;
  syncFrequency?: BankAccount['syncFrequency'];
  alertSettings?: Record<string, any>;
}

export interface ReconciliationRule {
  id: string;
  companyId: string;
  bankAccountId: string;
  name: string;
  conditions: Array<{
    field: string;
    operator: string;
    value: any;
  }>;
  actions: Array<{
    type: string;
    parameters: Record<string, any>;
  }>;
  priority: number;
  isActive: boolean;
  usageCount: number;
  lastUsedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BankAlert {
  id: string;
  companyId: string;
  bankAccountId: string;
  alertType: 'LOW_BALANCE' | 'LARGE_TRANSACTION' | 'FAILED_SYNC' | 'DUPLICATE_TRANSACTION';
  alertData: Record<string, any>;
  isRead: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: string;
  createdAt: string;
}

class BankService {
  /**
   * Get bank accounts
   */
  async getBankAccounts(companyId: string): Promise<BankAccount[]> {
    // Mock data for now - replace with real API call when backend is ready
    return [
      {
        id: 'bank-1',
        companyId,
        bankName: 'Chase Bank',
        accountName: 'Business Checking',
        accountNumber: '****1234',
        routingNumber: '*********',
        accountType: 'CHECKING',
        balance: 25430.50,
        currency: 'USD',
        isActive: true,
        autoReconciliation: true,
        syncFrequency: 'DAILY',
        alertSettings: {},
        reconciliationRules: [],
        lastSyncAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'bank-2',
        companyId,
        bankName: 'Bank of America',
        accountName: 'Business Savings',
        accountNumber: '****5678',
        accountType: 'SAVINGS',
        balance: 150000.00,
        currency: 'USD',
        isActive: true,
        autoReconciliation: false,
        syncFrequency: 'WEEKLY',
        alertSettings: {},
        reconciliationRules: [],
        lastSyncAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];
    // TODO: Replace with real API call
    // return await apiService.get<BankAccount[]>(`/companies/${companyId}/bank-accounts`);
  }

  /**
   * Get a specific bank account
   */
  async getBankAccount(companyId: string, accountId: string): Promise<BankAccount> {
    return await apiService.get<BankAccount>(`/companies/${companyId}/bank-accounts/${accountId}`);
  }

  /**
   * Connect a new bank account
   */
  async connectBankAccount(companyId: string, config: BankConnectionConfig): Promise<BankAccount> {
    return await apiService.post<BankAccount>(`/companies/${companyId}/bank-accounts/connect`, config);
  }

  /**
   * Update bank account settings
   */
  async updateBankAccount(
    companyId: string,
    accountId: string,
    updates: Partial<BankAccount>
  ): Promise<BankAccount> {
    return await apiService.put<BankAccount>(`/companies/${companyId}/bank-accounts/${accountId}`, updates);
  }

  /**
   * Delete/disconnect bank account
   */
  async deleteBankAccount(companyId: string, accountId: string): Promise<void> {
    // Mock implementation - just log
    console.log(`Deleting bank account ${accountId}`);
    // TODO: Replace with real API call
    // await apiService.delete(`/companies/${companyId}/bank-accounts/${accountId}`);
  }

  /**
   * Sync bank account transactions
   */
  async syncBankAccount(companyId: string, accountId: string): Promise<{
    newTransactions: number;
    updatedTransactions: number;
    errors: string[];
  }> {
    // Mock implementation
    console.log(`Syncing bank account ${accountId}`);
    // Simulate sync delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    return {
      newTransactions: 5,
      updatedTransactions: 2,
      errors: [],
    };
    // TODO: Replace with real API call
    // return await apiService.post(`/companies/${companyId}/bank-accounts/${accountId}/sync`);
  }

  /**
   * Get bank transactions
   */
  async getBankTransactions(
    companyId: string,
    accountId: string,
    filters: {
      dateFrom?: string;
      dateTo?: string;
      reconciliationStatus?: string;
      pending?: boolean;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{
    transactions: BankTransaction[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const params = new URLSearchParams();
    
    if (filters.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters.dateTo) params.append('dateTo', filters.dateTo);
    if (filters.reconciliationStatus) params.append('reconciliationStatus', filters.reconciliationStatus);
    if (filters.pending !== undefined) params.append('pending', filters.pending.toString());
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());

    const response = await apiClient.get(
      `/companies/${companyId}/bank-accounts/${accountId}/transactions?${params}`
    );
    return response.data;
  }

  /**
   * Match bank transaction with accounting transaction
   */
  async matchTransaction(
    companyId: string,
    bankTransactionId: string,
    accountingTransactionId: string
  ): Promise<BankTransaction> {
    const response = await apiClient.post(
      `/companies/${companyId}/bank-transactions/${bankTransactionId}/match`,
      { accountingTransactionId }
    );
    return response.data;
  }

  /**
   * Unmatch bank transaction
   */
  async unmatchTransaction(companyId: string, bankTransactionId: string): Promise<BankTransaction> {
    const response = await apiClient.post(
      `/companies/${companyId}/bank-transactions/${bankTransactionId}/unmatch`
    );
    return response.data;
  }

  /**
   * Ignore bank transaction
   */
  async ignoreTransaction(companyId: string, bankTransactionId: string): Promise<BankTransaction> {
    const response = await apiClient.post(
      `/companies/${companyId}/bank-transactions/${bankTransactionId}/ignore`
    );
    return response.data;
  }

  /**
   * Create accounting transaction from bank transaction
   */
  async createTransactionFromBank(
    companyId: string,
    bankTransactionId: string,
    transactionData: {
      description: string;
      accountId: string;
      categoryId?: string;
    }
  ): Promise<{ bankTransaction: BankTransaction; accountingTransaction: any }> {
    const response = await apiClient.post(
      `/companies/${companyId}/bank-transactions/${bankTransactionId}/create-transaction`,
      transactionData
    );
    return response.data;
  }

  /**
   * Get reconciliation suggestions
   */
  async getReconciliationSuggestions(
    companyId: string,
    bankTransactionId: string
  ): Promise<Array<{
    transactionId: string;
    confidence: number;
    reasons: string[];
    transaction: any;
  }>> {
    const response = await apiClient.get(
      `/companies/${companyId}/bank-transactions/${bankTransactionId}/suggestions`
    );
    return response.data;
  }

  /**
   * Get reconciliation rules
   */
  async getReconciliationRules(companyId: string, bankAccountId?: string): Promise<ReconciliationRule[]> {
    const params = bankAccountId ? `?bankAccountId=${bankAccountId}` : '';
    const response = await apiClient.get(`/companies/${companyId}/reconciliation-rules${params}`);
    return response.data;
  }

  /**
   * Create reconciliation rule
   */
  async createReconciliationRule(
    companyId: string,
    ruleData: Omit<ReconciliationRule, 'id' | 'companyId' | 'usageCount' | 'lastUsedAt' | 'createdAt' | 'updatedAt'>
  ): Promise<ReconciliationRule> {
    const response = await apiClient.post(`/companies/${companyId}/reconciliation-rules`, ruleData);
    return response.data;
  }

  /**
   * Update reconciliation rule
   */
  async updateReconciliationRule(
    companyId: string,
    ruleId: string,
    updates: Partial<ReconciliationRule>
  ): Promise<ReconciliationRule> {
    const response = await apiClient.put(`/companies/${companyId}/reconciliation-rules/${ruleId}`, updates);
    return response.data;
  }

  /**
   * Delete reconciliation rule
   */
  async deleteReconciliationRule(companyId: string, ruleId: string): Promise<void> {
    await apiClient.delete(`/companies/${companyId}/reconciliation-rules/${ruleId}`);
  }

  /**
   * Get bank alerts
   */
  async getBankAlerts(
    companyId: string,
    filters: {
      bankAccountId?: string;
      alertType?: string;
      isRead?: boolean;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{
    alerts: BankAlert[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const params = new URLSearchParams();
    
    if (filters.bankAccountId) params.append('bankAccountId', filters.bankAccountId);
    if (filters.alertType) params.append('alertType', filters.alertType);
    if (filters.isRead !== undefined) params.append('isRead', filters.isRead.toString());
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());

    const response = await apiClient.get(`/companies/${companyId}/bank-alerts?${params}`);
    return response.data;
  }

  /**
   * Mark alert as read
   */
  async markAlertAsRead(companyId: string, alertId: string): Promise<BankAlert> {
    const response = await apiClient.post(`/companies/${companyId}/bank-alerts/${alertId}/read`);
    return response.data;
  }

  /**
   * Acknowledge alert
   */
  async acknowledgeAlert(companyId: string, alertId: string): Promise<BankAlert> {
    const response = await apiClient.post(`/companies/${companyId}/bank-alerts/${alertId}/acknowledge`);
    return response.data;
  }

  /**
   * Get supported banks
   */
  async getSupportedBanks(): Promise<Array<{
    id: string;
    name: string;
    logo?: string;
    country: string;
    features: string[];
    connectionType: 'PLAID' | 'YODLEE' | 'DIRECT' | 'MANUAL';
  }>> {
    const response = await apiClient.get('/supported-banks');
    return response.data;
  }

  /**
   * Get bank connection status
   */
  async getBankConnectionStatus(companyId: string, accountId: string): Promise<{
    status: 'CONNECTED' | 'DISCONNECTED' | 'ERROR' | 'REQUIRES_UPDATE';
    lastSync: string;
    nextSync: string;
    errorMessage?: string;
  }> {
    const response = await apiClient.get(`/companies/${companyId}/bank-accounts/${accountId}/status`);
    return response.data;
  }

  /**
   * Test bank connection
   */
  async testBankConnection(companyId: string, accountId: string): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    const response = await apiClient.post(`/companies/${companyId}/bank-accounts/${accountId}/test`);
    return response.data;
  }

  /**
   * Get reconciliation statistics
   */
  async getReconciliationStats(
    companyId: string,
    accountId?: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<{
    totalTransactions: number;
    reconciledTransactions: number;
    unreconciledTransactions: number;
    autoMatchedTransactions: number;
    reconciliationRate: number;
    averageMatchConfidence: number;
  }> {
    const params = new URLSearchParams();
    
    if (accountId) params.append('accountId', accountId);
    if (dateFrom) params.append('dateFrom', dateFrom);
    if (dateTo) params.append('dateTo', dateTo);

    const response = await apiClient.get(`/companies/${companyId}/reconciliation-stats?${params}`);
    return response.data;
  }
}

export const bankService = new BankService();
export default bankService;
