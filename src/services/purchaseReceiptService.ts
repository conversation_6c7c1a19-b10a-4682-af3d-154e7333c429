import api from './api';
import type {
  PurchaseReceipt,
  CreatePurchaseReceiptRequest,
  UpdatePurchaseReceiptRequest,
  PurchaseReceiptFilters,
  PurchaseReceiptListResponse,
  UpdatePurchaseReceiptStatusRequest,
  QualityCheckRequest,
} from '../types/purchaseReceipt';

class PurchaseReceiptService {
  private baseUrl = '/purchase-receipts';

  /**
   * Get all purchase receipts for a company
   */
  async getPurchaseReceipts(
    companyId: string,
    filters?: PurchaseReceiptFilters
  ): Promise<PurchaseReceiptListResponse> {
    const params = new URLSearchParams();
    
    if (filters?.status) params.append('status', filters.status);
    if (filters?.vendorId) params.append('vendorId', filters.vendorId);
    if (filters?.purchaseOrderId) params.append('purchaseOrderId', filters.purchaseOrderId);
    if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters?.dateTo) params.append('dateTo', filters.dateTo);
    if (filters?.search) params.append('search', filters.search);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());

    const queryString = params.toString();
    const url = `${this.baseUrl}/${companyId}${queryString ? `?${queryString}` : ''}`;
    
    const response = await api.get(url);
    return response.data;
  }

  /**
   * Get a single purchase receipt by ID
   */
  async getPurchaseReceipt(companyId: string, receiptId: string): Promise<PurchaseReceipt> {
    const response = await api.get(`${this.baseUrl}/${companyId}/${receiptId}`);
    return response.data;
  }

  /**
   * Create a new purchase receipt
   */
  async createPurchaseReceipt(data: CreatePurchaseReceiptRequest): Promise<PurchaseReceipt> {
    const response = await api.post(this.baseUrl, data);
    return response.data;
  }

  /**
   * Update an existing purchase receipt
   */
  async updatePurchaseReceipt(
    companyId: string,
    receiptId: string,
    data: UpdatePurchaseReceiptRequest
  ): Promise<PurchaseReceipt> {
    const response = await api.put(`${this.baseUrl}/${companyId}/${receiptId}`, data);
    return response.data;
  }

  /**
   * Update purchase receipt status
   */
  async updatePurchaseReceiptStatus(
    companyId: string,
    receiptId: string,
    data: UpdatePurchaseReceiptStatusRequest
  ): Promise<{ message: string; status: string }> {
    const response = await api.post(`${this.baseUrl}/${companyId}/${receiptId}/status`, data);
    return response.data;
  }

  /**
   * Perform quality check on purchase receipt
   */
  async performQualityCheck(
    companyId: string,
    receiptId: string,
    data: QualityCheckRequest
  ): Promise<{ message: string }> {
    const response = await api.post(`${this.baseUrl}/${companyId}/${receiptId}/quality-check`, data);
    return response.data;
  }

  /**
   * Delete a purchase receipt
   */
  async deletePurchaseReceipt(companyId: string, receiptId: string): Promise<{ message: string }> {
    const response = await api.delete(`${this.baseUrl}/${companyId}/${receiptId}`);
    return response.data;
  }

  /**
   * Mark receipt as received
   */
  async markAsReceived(companyId: string, receiptId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updatePurchaseReceiptStatus(companyId, receiptId, {
      status: 'RECEIVED',
      notes: notes || 'Items received',
    });
  }

  /**
   * Mark receipt as inspected
   */
  async markAsInspected(companyId: string, receiptId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updatePurchaseReceiptStatus(companyId, receiptId, {
      status: 'INSPECTED',
      notes: notes || 'Items inspected',
    });
  }

  /**
   * Accept receipt
   */
  async acceptReceipt(companyId: string, receiptId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updatePurchaseReceiptStatus(companyId, receiptId, {
      status: 'ACCEPTED',
      notes: notes || 'Receipt accepted',
    });
  }

  /**
   * Reject receipt
   */
  async rejectReceipt(companyId: string, receiptId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updatePurchaseReceiptStatus(companyId, receiptId, {
      status: 'REJECTED',
      notes: notes || 'Receipt rejected',
    });
  }

  /**
   * Partially accept receipt
   */
  async partiallyAcceptReceipt(companyId: string, receiptId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updatePurchaseReceiptStatus(companyId, receiptId, {
      status: 'PARTIALLY_ACCEPTED',
      notes: notes || 'Receipt partially accepted',
    });
  }

  /**
   * Format currency amount
   */
  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }

  /**
   * Format date
   */
  formatDate(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  /**
   * Format date and time
   */
  formatDateTime(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  /**
   * Calculate receipt completion percentage
   */
  getReceiptCompletionPercentage(receipt: PurchaseReceipt): number {
    if (!receipt.lineItems || receipt.lineItems.length === 0) return 0;
    
    let totalOrdered = 0;
    let totalReceived = 0;
    
    receipt.lineItems.forEach(item => {
      totalOrdered += item.quantityOrdered;
      totalReceived += item.quantityReceived;
    });
    
    if (totalOrdered === 0) return 0;
    return (totalReceived / totalOrdered) * 100;
  }

  /**
   * Calculate acceptance percentage
   */
  getAcceptancePercentage(receipt: PurchaseReceipt): number {
    if (!receipt.lineItems || receipt.lineItems.length === 0) return 0;
    
    let totalReceived = 0;
    let totalAccepted = 0;
    
    receipt.lineItems.forEach(item => {
      totalReceived += item.quantityReceived;
      totalAccepted += item.quantityAccepted;
    });
    
    if (totalReceived === 0) return 0;
    return (totalAccepted / totalReceived) * 100;
  }

  /**
   * Check if receipt has quality issues
   */
  hasQualityIssues(receipt: PurchaseReceipt): boolean {
    if (!receipt.lineItems) return false;
    
    return receipt.lineItems.some(item => 
      item.conditionOnArrival !== 'GOOD' || 
      item.quantityRejected > 0
    );
  }

  /**
   * Get quality issues summary
   */
  getQualityIssuesSummary(receipt: PurchaseReceipt): string[] {
    if (!receipt.lineItems) return [];
    
    const issues: string[] = [];
    
    receipt.lineItems.forEach(item => {
      if (item.conditionOnArrival !== 'GOOD') {
        issues.push(`${item.description}: ${item.conditionOnArrival}`);
      }
      if (item.quantityRejected > 0) {
        issues.push(`${item.description}: ${item.quantityRejected} items rejected`);
      }
    });
    
    return issues;
  }

  /**
   * Validate purchase receipt data
   */
  validatePurchaseReceipt(data: CreatePurchaseReceiptRequest | UpdatePurchaseReceiptRequest): string[] {
    const errors: string[] = [];

    if ('vendorId' in data && !data.vendorId) {
      errors.push('Vendor is required');
    }

    if ('receiptDate' in data && !data.receiptDate) {
      errors.push('Receipt date is required');
    }

    if ('lineItems' in data && data.lineItems) {
      if (data.lineItems.length === 0) {
        errors.push('At least one line item is required');
      }

      data.lineItems.forEach((item, index) => {
        if (!item.description?.trim()) {
          errors.push(`Line item ${index + 1}: Description is required`);
        }
        if (!item.quantityOrdered || item.quantityOrdered <= 0) {
          errors.push(`Line item ${index + 1}: Quantity ordered must be greater than 0`);
        }
        if (item.quantityReceived < 0) {
          errors.push(`Line item ${index + 1}: Quantity received cannot be negative`);
        }
        if (item.quantityAccepted < 0) {
          errors.push(`Line item ${index + 1}: Quantity accepted cannot be negative`);
        }
        if (item.quantityRejected < 0) {
          errors.push(`Line item ${index + 1}: Quantity rejected cannot be negative`);
        }
        if ((item.quantityAccepted + item.quantityRejected) > item.quantityReceived) {
          errors.push(`Line item ${index + 1}: Accepted + Rejected quantities cannot exceed received quantity`);
        }
      });
    }

    return errors;
  }

  /**
   * Generate receipt number (client-side preview)
   */
  generateReceiptNumber(lastNumber: number = 0): string {
    return `PR-${String(lastNumber + 1).padStart(6, '0')}`;
  }

  /**
   * Get default delivery instructions
   */
  getDefaultDeliveryInstructions(): string {
    return `Delivery Instructions:

1. Please deliver during business hours (8 AM - 5 PM).
2. Contact the receiving department upon arrival.
3. Ensure all items are properly packaged and labeled.
4. Provide delivery note and any relevant documentation.
5. Wait for inspection and sign-off before leaving.

Thank you for your cooperation!`;
  }

  /**
   * Get quality check guidelines
   */
  getQualityCheckGuidelines(): string {
    return `Quality Check Guidelines:

1. Verify item quantities against purchase order.
2. Check item condition and packaging.
3. Inspect for damage, defects, or missing components.
4. Verify item specifications and model numbers.
5. Check expiry dates for applicable items.
6. Document any discrepancies or issues.
7. Take photos of damaged or defective items.
8. Complete inspection within 24 hours of receipt.

Report any issues immediately to procurement team.`;
  }

  /**
   * Get receipt status workflow
   */
  getStatusWorkflow(): Array<{
    status: string;
    label: string;
    description: string;
    nextStatuses: string[];
  }> {
    return [
      {
        status: 'DRAFT',
        label: 'Draft',
        description: 'Receipt is being prepared',
        nextStatuses: ['RECEIVED'],
      },
      {
        status: 'RECEIVED',
        label: 'Received',
        description: 'Items have been received',
        nextStatuses: ['INSPECTED'],
      },
      {
        status: 'INSPECTED',
        label: 'Inspected',
        description: 'Items have been inspected',
        nextStatuses: ['ACCEPTED', 'REJECTED', 'PARTIALLY_ACCEPTED'],
      },
      {
        status: 'ACCEPTED',
        label: 'Accepted',
        description: 'All items accepted',
        nextStatuses: [],
      },
      {
        status: 'REJECTED',
        label: 'Rejected',
        description: 'Items rejected',
        nextStatuses: [],
      },
      {
        status: 'PARTIALLY_ACCEPTED',
        label: 'Partially Accepted',
        description: 'Some items accepted, some rejected',
        nextStatuses: [],
      },
    ];
  }

  /**
   * Calculate line item totals
   */
  calculateLineItemTotals(item: {
    quantityOrdered: number;
    quantityReceived: number;
    quantityAccepted: number;
    quantityRejected: number;
  }): {
    receivedPercentage: number;
    acceptedPercentage: number;
    rejectedPercentage: number;
    pendingQuantity: number;
  } {
    const receivedPercentage = item.quantityOrdered > 0 
      ? (item.quantityReceived / item.quantityOrdered) * 100 
      : 0;
    
    const acceptedPercentage = item.quantityReceived > 0 
      ? (item.quantityAccepted / item.quantityReceived) * 100 
      : 0;
    
    const rejectedPercentage = item.quantityReceived > 0 
      ? (item.quantityRejected / item.quantityReceived) * 100 
      : 0;
    
    const pendingQuantity = item.quantityOrdered - item.quantityReceived;

    return {
      receivedPercentage,
      acceptedPercentage,
      rejectedPercentage,
      pendingQuantity,
    };
  }

  /**
   * Get receipt summary statistics
   */
  getReceiptSummary(receipt: PurchaseReceipt): {
    totalItems: number;
    totalOrdered: number;
    totalReceived: number;
    totalAccepted: number;
    totalRejected: number;
    completionPercentage: number;
    acceptancePercentage: number;
    hasIssues: boolean;
  } {
    if (!receipt.lineItems || receipt.lineItems.length === 0) {
      return {
        totalItems: 0,
        totalOrdered: 0,
        totalReceived: 0,
        totalAccepted: 0,
        totalRejected: 0,
        completionPercentage: 0,
        acceptancePercentage: 0,
        hasIssues: false,
      };
    }

    let totalOrdered = 0;
    let totalReceived = 0;
    let totalAccepted = 0;
    let totalRejected = 0;
    let hasIssues = false;

    receipt.lineItems.forEach(item => {
      totalOrdered += item.quantityOrdered;
      totalReceived += item.quantityReceived;
      totalAccepted += item.quantityAccepted;
      totalRejected += item.quantityRejected;
      
      if (item.conditionOnArrival !== 'GOOD' || item.quantityRejected > 0) {
        hasIssues = true;
      }
    });

    const completionPercentage = totalOrdered > 0 ? (totalReceived / totalOrdered) * 100 : 0;
    const acceptancePercentage = totalReceived > 0 ? (totalAccepted / totalReceived) * 100 : 0;

    return {
      totalItems: receipt.lineItems.length,
      totalOrdered,
      totalReceived,
      totalAccepted,
      totalRejected,
      completionPercentage,
      acceptancePercentage,
      hasIssues,
    };
  }
}

export default new PurchaseReceiptService();
