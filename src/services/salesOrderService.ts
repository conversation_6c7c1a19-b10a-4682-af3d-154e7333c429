import api from './api';
import type {
  SalesOrder,
  CreateSalesOrderRequest,
  UpdateSalesOrderRequest,
  SalesOrderFilters,
  SalesOrderListResponse,
  UpdateSalesOrderStatusRequest,
  CreateShipmentRequest,
} from '../types/salesOrder';

class SalesOrderService {
  private baseUrl = '/sales-orders';

  /**
   * Get all sales orders for a company
   */
  async getSalesOrders(
    companyId: string,
    filters?: SalesOrderFilters
  ): Promise<SalesOrderListResponse> {
    const params = new URLSearchParams();
    
    if (filters?.status) params.append('status', filters.status);
    if (filters?.customerId) params.append('customerId', filters.customerId);
    if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters?.dateTo) params.append('dateTo', filters.dateTo);
    if (filters?.deliveryDateFrom) params.append('deliveryDateFrom', filters.deliveryDateFrom);
    if (filters?.deliveryDateTo) params.append('deliveryDateTo', filters.deliveryDateTo);
    if (filters?.search) params.append('search', filters.search);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());

    const queryString = params.toString();
    const url = `${this.baseUrl}/${companyId}${queryString ? `?${queryString}` : ''}`;
    
    const response = await api.get(url);
    return response.data;
  }

  /**
   * Get a single sales order by ID
   */
  async getSalesOrder(companyId: string, salesOrderId: string): Promise<SalesOrder> {
    const response = await api.get(`${this.baseUrl}/${companyId}/${salesOrderId}`);
    return response.data;
  }

  /**
   * Create a new sales order
   */
  async createSalesOrder(data: CreateSalesOrderRequest): Promise<SalesOrder> {
    const response = await api.post(this.baseUrl, data);
    return response.data;
  }

  /**
   * Update an existing sales order
   */
  async updateSalesOrder(
    companyId: string,
    salesOrderId: string,
    data: UpdateSalesOrderRequest
  ): Promise<SalesOrder> {
    const response = await api.put(`${this.baseUrl}/${companyId}/${salesOrderId}`, data);
    return response.data;
  }

  /**
   * Update sales order status
   */
  async updateSalesOrderStatus(
    companyId: string,
    salesOrderId: string,
    data: UpdateSalesOrderStatusRequest
  ): Promise<{ message: string; status: string }> {
    const response = await api.post(`${this.baseUrl}/${companyId}/${salesOrderId}/status`, data);
    return response.data;
  }

  /**
   * Convert estimate to sales order
   */
  async convertFromEstimate(
    companyId: string,
    estimateId: string
  ): Promise<{ message: string; salesOrderId: string; salesOrderNumber: string }> {
    const response = await api.post(`${this.baseUrl}/${companyId}/convert-from-estimate/${estimateId}`);
    return response.data;
  }

  /**
   * Delete a sales order
   */
  async deleteSalesOrder(companyId: string, salesOrderId: string): Promise<{ message: string }> {
    const response = await api.delete(`${this.baseUrl}/${companyId}/${salesOrderId}`);
    return response.data;
  }

  /**
   * Confirm a sales order
   */
  async confirmSalesOrder(companyId: string, salesOrderId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updateSalesOrderStatus(companyId, salesOrderId, {
      status: 'CONFIRMED',
      notes: notes || 'Sales order confirmed',
    });
  }

  /**
   * Start progress on a sales order
   */
  async startProgress(companyId: string, salesOrderId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updateSalesOrderStatus(companyId, salesOrderId, {
      status: 'IN_PROGRESS',
      notes: notes || 'Sales order in progress',
    });
  }

  /**
   * Ship a sales order
   */
  async shipSalesOrder(companyId: string, salesOrderId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updateSalesOrderStatus(companyId, salesOrderId, {
      status: 'SHIPPED',
      notes: notes || 'Sales order shipped',
    });
  }

  /**
   * Mark sales order as delivered
   */
  async deliverSalesOrder(companyId: string, salesOrderId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updateSalesOrderStatus(companyId, salesOrderId, {
      status: 'DELIVERED',
      notes: notes || 'Sales order delivered',
    });
  }

  /**
   * Complete a sales order
   */
  async completeSalesOrder(companyId: string, salesOrderId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updateSalesOrderStatus(companyId, salesOrderId, {
      status: 'COMPLETED',
      notes: notes || 'Sales order completed',
    });
  }

  /**
   * Cancel a sales order
   */
  async cancelSalesOrder(companyId: string, salesOrderId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updateSalesOrderStatus(companyId, salesOrderId, {
      status: 'CANCELLED',
      notes: notes || 'Sales order cancelled',
    });
  }

  /**
   * Create shipment for sales order
   */
  async createShipment(
    companyId: string,
    salesOrderId: string,
    data: CreateShipmentRequest
  ): Promise<{ message: string; shipmentId: string }> {
    const response = await api.post(`${this.baseUrl}/${companyId}/${salesOrderId}/shipments`, data);
    return response.data;
  }

  /**
   * Format currency amount
   */
  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }

  /**
   * Format date
   */
  formatDate(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  /**
   * Calculate line item total
   */
  calculateLineTotal(quantity: number, unitPrice: number, discountPercentage: number = 0): number {
    const subtotal = quantity * unitPrice;
    const discountAmount = (subtotal * discountPercentage) / 100;
    return subtotal - discountAmount;
  }

  /**
   * Calculate line item tax
   */
  calculateLineTax(lineTotal: number, taxRate: number = 0): number {
    return (lineTotal * taxRate) / 100;
  }

  /**
   * Calculate sales order totals
   */
  calculateTotals(lineItems: Array<{
    quantityOrdered: number;
    unitPrice: number;
    discountPercentage?: number;
    taxRate?: number;
  }>, shippingAmount: number = 0): {
    subtotal: number;
    totalDiscount: number;
    totalTax: number;
    shippingAmount: number;
    total: number;
  } {
    let subtotal = 0;
    let totalDiscount = 0;
    let totalTax = 0;

    lineItems.forEach(item => {
      const lineSubtotal = item.quantityOrdered * item.unitPrice;
      const discountAmount = (lineSubtotal * (item.discountPercentage || 0)) / 100;
      const lineTotal = lineSubtotal - discountAmount;
      const taxAmount = (lineTotal * (item.taxRate || 0)) / 100;

      subtotal += lineSubtotal;
      totalDiscount += discountAmount;
      totalTax += taxAmount;
    });

    const total = subtotal - totalDiscount + totalTax + shippingAmount;

    return {
      subtotal,
      totalDiscount,
      totalTax,
      shippingAmount,
      total,
    };
  }

  /**
   * Generate sales order number (client-side preview)
   */
  generateSalesOrderNumber(lastNumber: number = 0): string {
    return `SO-${String(lastNumber + 1).padStart(6, '0')}`;
  }

  /**
   * Validate sales order data
   */
  validateSalesOrder(data: CreateSalesOrderRequest | UpdateSalesOrderRequest): string[] {
    const errors: string[] = [];

    if ('customerId' in data && !data.customerId) {
      errors.push('Customer is required');
    }

    if ('orderDate' in data && !data.orderDate) {
      errors.push('Order date is required');
    }

    if ('lineItems' in data && data.lineItems) {
      if (data.lineItems.length === 0) {
        errors.push('At least one line item is required');
      }

      data.lineItems.forEach((item, index) => {
        if (!item.description?.trim()) {
          errors.push(`Line item ${index + 1}: Description is required`);
        }
        if (!item.quantityOrdered || item.quantityOrdered <= 0) {
          errors.push(`Line item ${index + 1}: Quantity must be greater than 0`);
        }
        if (!item.unitPrice || item.unitPrice <= 0) {
          errors.push(`Line item ${index + 1}: Unit price must be greater than 0`);
        }
      });
    }

    return errors;
  }

  /**
   * Get delivery status for sales order
   */
  getDeliveryStatus(salesOrder: SalesOrder): {
    status: 'on-time' | 'overdue' | 'delivered' | 'no-date';
    message: string;
    daysRemaining?: number;
  } {
    if (!salesOrder.deliveryDate) {
      return { status: 'no-date', message: 'No delivery date set' };
    }

    if (['DELIVERED', 'COMPLETED'].includes(salesOrder.status)) {
      return { status: 'delivered', message: 'Delivered' };
    }

    const deliveryDate = new Date(salesOrder.deliveryDate);
    const today = new Date();
    const diffDays = Math.ceil((deliveryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return {
        status: 'overdue',
        message: `Overdue by ${Math.abs(diffDays)} days`,
        daysRemaining: 0,
      };
    }
    
    return {
      status: 'on-time',
      message: `Due in ${diffDays} days`,
      daysRemaining: diffDays,
    };
  }

  /**
   * Calculate fulfillment percentage
   */
  getFulfillmentPercentage(salesOrder: SalesOrder): number {
    if (!salesOrder.lineItems || salesOrder.lineItems.length === 0) return 0;
    
    let totalOrdered = 0;
    let totalShipped = 0;
    
    salesOrder.lineItems.forEach(item => {
      totalOrdered += item.quantityOrdered;
      totalShipped += item.quantityShipped;
    });
    
    if (totalOrdered === 0) return 0;
    return (totalShipped / totalOrdered) * 100;
  }

  /**
   * Calculate default delivery date
   */
  calculateDeliveryDate(orderDate: string, leadTimeDays: number = 7): string {
    const date = new Date(orderDate);
    date.setDate(date.getDate() + leadTimeDays);
    return date.toISOString().split('T')[0];
  }

  /**
   * Get default terms and conditions
   */
  getDefaultTermsAndConditions(): string {
    return `Terms and Conditions:

1. Delivery will be made according to the agreed schedule.
2. Payment terms: Net 30 days from delivery date.
3. All goods remain the property of the seller until payment is received in full.
4. The buyer is responsible for inspection of goods upon delivery.
5. Any damages or discrepancies must be reported within 48 hours of delivery.

Thank you for your business!`;
  }

  /**
   * Get shipping methods
   */
  getShippingMethods(): Array<{ value: string; label: string; estimatedDays: number }> {
    return [
      { value: 'STANDARD', label: 'Standard Shipping', estimatedDays: 5 },
      { value: 'EXPRESS', label: 'Express Shipping', estimatedDays: 2 },
      { value: 'OVERNIGHT', label: 'Overnight Shipping', estimatedDays: 1 },
      { value: 'PICKUP', label: 'Customer Pickup', estimatedDays: 0 },
      { value: 'DELIVERY', label: 'Local Delivery', estimatedDays: 1 },
    ];
  }

  /**
   * Get shipping carriers
   */
  getShippingCarriers(): Array<{ value: string; label: string }> {
    return [
      { value: 'FEDEX', label: 'FedEx' },
      { value: 'UPS', label: 'UPS' },
      { value: 'DHL', label: 'DHL' },
      { value: 'USPS', label: 'USPS' },
      { value: 'LOCAL', label: 'Local Courier' },
      { value: 'PICKUP', label: 'Customer Pickup' },
    ];
  }

  /**
   * Generate tracking URL
   */
  generateTrackingUrl(carrier: string, trackingNumber: string): string | null {
    const trackingUrls: Record<string, string> = {
      FEDEX: `https://www.fedex.com/fedextrack/?trknbr=${trackingNumber}`,
      UPS: `https://www.ups.com/track?tracknum=${trackingNumber}`,
      DHL: `https://www.dhl.com/en/express/tracking.html?AWB=${trackingNumber}`,
      USPS: `https://tools.usps.com/go/TrackConfirmAction?tLabels=${trackingNumber}`,
    };

    return trackingUrls[carrier] || null;
  }
}

export default new SalesOrderService();
