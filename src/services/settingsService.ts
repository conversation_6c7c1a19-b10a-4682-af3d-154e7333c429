import { apiService } from "./api";

export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  isActive: boolean;
  emailVerified: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme?: "light" | "dark" | "system";
  language?: string;
  timeZone?: string;
  dateFormat?: string;
  numberFormat?: string;
  dashboardLayout?: string[];
  notifications?: {
    email?: boolean;
    browser?: boolean;
    mobile?: boolean;
    frequency?: "immediate" | "daily" | "weekly";
  };
  reports?: {
    defaultPeriod?: string;
    autoRefresh?: boolean;
    exportFormat?: "pdf" | "csv" | "excel";
  };
}

export interface CompanySettings {
  id: string;
  name: string;
  legalName?: string;
  taxId?: string;
  registrationNumber?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  phone?: string;
  email?: string;
  website?: string;
  baseCurrency: string;
  settings: {
    fiscalYearStart?: string;
    timeZone?: string;
    dateFormat?: string;
    numberFormat?: string;
    defaultPaymentTerms?: string;
    autoNumbering?: {
      invoices?: boolean;
      transactions?: boolean;
      contacts?: boolean;
    };
    notifications?: {
      emailNotifications?: boolean;
      overdueReminders?: boolean;
      paymentConfirmations?: boolean;
      reportSchedules?: boolean;
    };
    security?: {
      sessionTimeout?: number;
      passwordExpiry?: number;
      twoFactorAuth?: boolean;
      ipWhitelist?: string[];
    };
    backup?: {
      autoBackup?: boolean;
      backupFrequency?: string;
      retentionDays?: number;
    };
    integrations?: {
      bankingApi?: {
        enabled?: boolean;
        provider?: string;
        apiKey?: string;
      };
      paymentGateway?: {
        enabled?: boolean;
        provider?: string;
        apiKey?: string;
        webhookUrl?: string;
      };
      exchangeRates?: {
        enabled?: boolean;
        provider?: string;
        apiKey?: string;
        updateFrequency?: string;
      };
    };
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuditLog {
  id: string;
  userEmail: string;
  action: string;
  tableName: string;
  recordId: string;
  description: string;
  ipAddress: string;
  createdAt: string;
}

export interface SystemInfo {
  version: string;
  environment: string;
  database: {
    type: string;
    version: string;
    status: string;
  };
  server: {
    uptime: number;
    memory: any;
    platform: string;
    nodeVersion: string;
  };
  features: {
    multiCurrency: boolean;
    auditTrail: boolean;
    reporting: boolean;
    integrations: boolean;
    backup: boolean;
    twoFactorAuth: boolean;
    sso: boolean;
  };
}

export interface DataRetentionPolicy {
  id: string;
  tableName: string;
  retentionDays: number;
  isActive: boolean;
  description: string;
  createdAt: string;
  updatedAt: string;
}

class SettingsService {
  /**
   * Get user profile
   */
  public async getUserProfile(companyId: string): Promise<UserProfile> {
    const response = await apiService.get<{ user: UserProfile }>(
      `/settings/${companyId}/user-profile`
    );
    return response.user;
  }

  /**
   * Update user profile
   */
  public async updateUserProfile(
    companyId: string,
    data: Partial<
      Pick<UserProfile, "firstName" | "lastName" | "phone" | "email">
    >
  ): Promise<UserProfile> {
    const response = await apiService.put<{ user: UserProfile }>(
      `/settings/${companyId}/user-profile`,
      data
    );
    return response.user;
  }

  /**
   * Change password
   */
  public async changePassword(
    companyId: string,
    data: {
      currentPassword: string;
      newPassword: string;
      confirmPassword: string;
    }
  ): Promise<{ message: string }> {
    return await apiService.put<{ message: string }>(
      `/settings/${companyId}/change-password`,
      data
    );
  }

  /**
   * Get company settings
   */
  public async getCompanySettings(companyId: string): Promise<CompanySettings> {
    const response = await apiService.get<{ company: CompanySettings }>(
      `/settings/${companyId}/company`
    );
    return response.company;
  }

  /**
   * Update company settings
   */
  public async updateCompanySettings(
    companyId: string,
    data: Partial<CompanySettings>
  ): Promise<CompanySettings> {
    const response = await apiService.put<{ company: CompanySettings }>(
      `/settings/${companyId}/company`,
      data
    );
    return response.company;
  }

  /**
   * Update user preferences
   */
  public async updateUserPreferences(
    companyId: string,
    preferences: UserPreferences
  ): Promise<UserPreferences> {
    const response = await apiService.put<{ preferences: UserPreferences }>(
      `/settings/${companyId}/user-preferences`,
      { preferences }
    );
    return response.preferences;
  }

  /**
   * Get audit logs
   */
  public async getAuditLogs(
    companyId: string,
    params?: {
      page?: number;
      limit?: number;
      action?: string;
      userId?: string;
      startDate?: string;
      endDate?: string;
    }
  ): Promise<{
    auditLogs: AuditLog[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> {
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());
    if (params?.action) queryParams.append("action", params.action);
    if (params?.userId) queryParams.append("userId", params.userId);
    if (params?.startDate) queryParams.append("startDate", params.startDate);
    if (params?.endDate) queryParams.append("endDate", params.endDate);

    const queryString = queryParams.toString();
    const url = `/settings/${companyId}/audit-logs${
      queryString ? `?${queryString}` : ""
    }`;

    return await apiService.get(url);
  }

  /**
   * Get system information
   */
  public async getSystemInfo(companyId: string): Promise<SystemInfo> {
    const response = await apiService.get<{ systemInfo: SystemInfo }>(
      `/settings/${companyId}/system-info`
    );
    return response.systemInfo;
  }

  /**
   * Get data retention policies
   */
  public async getDataRetentionPolicies(
    companyId: string
  ): Promise<DataRetentionPolicy[]> {
    const response = await apiService.get<{ policies: DataRetentionPolicy[] }>(
      `/settings/${companyId}/data-retention`
    );
    return response.policies;
  }

  /**
   * Format uptime in human readable format
   */
  public formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  /**
   * Format memory usage
   */
  public formatMemory(bytes: number): string {
    const mb = bytes / 1024 / 1024;
    return `${mb.toFixed(1)} MB`;
  }

  /**
   * Get available currencies
   */
  public getCurrencies(): Array<{
    code: string;
    name: string;
    symbol: string;
  }> {
    return [
      { code: "USD", name: "US Dollar", symbol: "$" },
      { code: "EUR", name: "Euro", symbol: "€" },
      { code: "GBP", name: "British Pound", symbol: "£" },
      { code: "JPY", name: "Japanese Yen", symbol: "¥" },
      { code: "CAD", name: "Canadian Dollar", symbol: "C$" },
      { code: "AUD", name: "Australian Dollar", symbol: "A$" },
      { code: "CHF", name: "Swiss Franc", symbol: "CHF" },
      { code: "CNY", name: "Chinese Yuan", symbol: "¥" },
      { code: "INR", name: "Indian Rupee", symbol: "₹" },
      { code: "BRL", name: "Brazilian Real", symbol: "R$" },
    ];
  }

  /**
   * Get available time zones
   */
  public getTimeZones(): Array<{ value: string; label: string }> {
    return [
      { value: "America/New_York", label: "Eastern Time (ET)" },
      { value: "America/Chicago", label: "Central Time (CT)" },
      { value: "America/Denver", label: "Mountain Time (MT)" },
      { value: "America/Los_Angeles", label: "Pacific Time (PT)" },
      { value: "Europe/London", label: "Greenwich Mean Time (GMT)" },
      { value: "Europe/Paris", label: "Central European Time (CET)" },
      { value: "Asia/Tokyo", label: "Japan Standard Time (JST)" },
      { value: "Asia/Shanghai", label: "China Standard Time (CST)" },
      { value: "Asia/Kolkata", label: "India Standard Time (IST)" },
      { value: "Australia/Sydney", label: "Australian Eastern Time (AET)" },
    ];
  }

  /**
   * Get available date formats
   */
  public getDateFormats(): Array<{
    value: string;
    label: string;
    example: string;
  }> {
    // const now = new Date();
    return [
      { value: "MM/DD/YYYY", label: "MM/DD/YYYY", example: "12/31/2023" },
      { value: "DD/MM/YYYY", label: "DD/MM/YYYY", example: "31/12/2023" },
      { value: "YYYY-MM-DD", label: "YYYY-MM-DD", example: "2023-12-31" },
      { value: "DD-MM-YYYY", label: "DD-MM-YYYY", example: "31-12-2023" },
      { value: "MMM DD, YYYY", label: "MMM DD, YYYY", example: "Dec 31, 2023" },
      { value: "DD MMM YYYY", label: "DD MMM YYYY", example: "31 Dec 2023" },
    ];
  }

  /**
   * Get available number formats
   */
  public getNumberFormats(): Array<{
    value: string;
    label: string;
    example: string;
  }> {
    return [
      { value: "en-US", label: "US Format", example: "1,234.56" },
      { value: "en-GB", label: "UK Format", example: "1,234.56" },
      { value: "de-DE", label: "German Format", example: "1.234,56" },
      { value: "fr-FR", label: "French Format", example: "1 234,56" },
      { value: "es-ES", label: "Spanish Format", example: "1.234,56" },
      { value: "it-IT", label: "Italian Format", example: "1.234,56" },
    ];
  }

  /**
   * Get available payment terms
   */
  public getPaymentTerms(): Array<{ value: string; label: string }> {
    return [
      { value: "NET_15", label: "Net 15 days" },
      { value: "NET_30", label: "Net 30 days" },
      { value: "NET_45", label: "Net 45 days" },
      { value: "NET_60", label: "Net 60 days" },
      { value: "NET_90", label: "Net 90 days" },
      { value: "DUE_ON_RECEIPT", label: "Due on receipt" },
      { value: "CASH_ON_DELIVERY", label: "Cash on delivery" },
      { value: "PREPAID", label: "Prepaid" },
    ];
  }
}

export const settingsService = new SettingsService();
