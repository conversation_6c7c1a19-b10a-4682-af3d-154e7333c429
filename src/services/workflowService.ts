import { apiService } from './api';

export interface Workflow {
  id: string;
  companyId: string;
  name: string;
  description: string;
  triggerType: 'EVENT' | 'SCHEDULE' | 'MANUAL';
  triggerConfig: Record<string, any>;
  isActive: boolean;
  version: number;
  steps: WorkflowStep[];
  actions?: WorkflowAction[];
  conditions?: WorkflowCondition[];
  createdBy: string;
  lastRunAt?: string;
  nextRunAt?: string;
  runCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface WorkflowAction {
  type: 'EMAIL' | 'NOTIFICATION' | 'CREATE_TRANSACTION' | 'UPDATE_RECORD' | 'GENERATE_REPORT' | 'WEBHOOK' | 'APPROVAL_REQUEST';
  config: {
    // Email action
    to?: string[];
    subject?: string;
    template?: string;
    data?: any;

    // Notification action
    message?: string;
    users?: string[];

    // Transaction action
    accountId?: string;
    amount?: number;
    description?: string;

    // Update record action
    table?: string;
    recordId?: string;
    updates?: any;

    // Report action
    reportType?: string;
    format?: string;
    recipients?: string[];

    // Webhook action
    url?: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    headers?: any;
    payload?: any;

    // Approval action
    approvers?: string[];
    approvalType?: string;
    requiredApprovals?: number;
  };
}

export interface WorkflowCondition {
  field: string;
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains' | 'not_equals' | 'is_null' | 'is_not_null';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

export interface WorkflowStep {
  id: string;
  workflowId: string;
  stepType: 'APPROVAL' | 'NOTIFICATION' | 'CONDITION' | 'ACTION';
  name: string;
  config: Record<string, any>;
  positionX: number;
  positionY: number;
  stepOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  entityType: string;
  entityId: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  currentStepId?: string;
  context: Record<string, any>;
  startedAt: string;
  completedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface WorkflowStepExecution {
  id: string;
  workflowExecutionId: string;
  workflowStepId: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'SKIPPED';
  assignedTo?: string;
  result: Record<string, any>;
  errorMessage?: string;
  startedAt?: string;
  completedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateWorkflowData {
  name: string;
  description: string;
  triggerType: Workflow['triggerType'];
  triggerConfig: Record<string, any>;
  steps: Omit<WorkflowStep, 'id' | 'workflowId' | 'createdAt' | 'updatedAt'>[];
}

export interface WorkflowFilters {
  isActive?: boolean;
  triggerType?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface ApprovalTask {
  id: string;
  workflowExecutionId: string;
  stepExecutionId: string;
  assignedTo: string;
  entityType: string;
  entityId: string;
  title: string;
  description: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'DELEGATED';
  dueDate?: string;
  approvedBy?: string;
  approvedAt?: string;
  rejectedBy?: string;
  rejectedAt?: string;
  rejectionReason?: string;
  createdAt: string;
  updatedAt: string;
}

export interface WorkflowStats {
  activeWorkflows: number;
  totalRuns: number;
  successRate: number;
  needsAttention: number;
  recentExecutions: WorkflowExecution[];
  pendingApprovals: number;
  completedToday: number;
  failedStalled: number;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  triggerType: string;
  steps: any[];
  actions: WorkflowAction[];
}

class WorkflowService {
  /**
   * Get workflows
   */
  async getWorkflows(companyId: string, filters: WorkflowFilters = {}): Promise<{
    workflows: Workflow[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const params = new URLSearchParams();
      if (filters.isActive !== undefined) params.append('isActive', filters.isActive.toString());
      if (filters.triggerType) params.append('triggerType', filters.triggerType);
      if (filters.search) params.append('search', filters.search);
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response = await apiService.get(`/workflows/${companyId}?${params}`);
      return response as any;
    } catch (error) {
      console.error('Failed to fetch workflows, using fallback data:', error);

      // Fallback to mock data if API fails
      const mockWorkflows: Workflow[] = [
        {
          id: 'workflow-1',
          companyId,
          name: 'Transaction Approval',
          description: 'Approve transactions over $1,000',
          triggerType: 'EVENT',
          triggerConfig: { event: 'transaction.created', condition: 'amount > 1000' },
          isActive: true,
          version: 1,
          steps: [],
          actions: [{ type: 'APPROVAL_REQUEST', config: { approvers: ['manager'], requiredApprovals: 1 } }],
          runCount: 15,
          createdBy: 'user-1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 'workflow-2',
          companyId,
          name: 'Monthly Report Generation',
          description: 'Generate monthly financial reports',
          triggerType: 'SCHEDULE',
          triggerConfig: { schedule: '0 0 1 * *' },
          isActive: true,
          version: 1,
          steps: [],
          actions: [{ type: 'GENERATE_REPORT', config: { reportType: 'monthly', format: 'PDF' } }],
          runCount: 8,
          createdBy: 'user-1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 'workflow-3',
          companyId,
          name: 'Expense Approval',
          description: 'Approve expense reports',
          triggerType: 'MANUAL',
          triggerConfig: {},
          isActive: false,
          version: 1,
          steps: [],
          actions: [{ type: 'EMAIL', config: { to: ['<EMAIL>'], subject: 'Expense Report' } }],
          runCount: 3,
          createdBy: 'user-1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      // Apply filters to mock data
      let filteredWorkflows = mockWorkflows;
      if (filters.isActive !== undefined) {
        filteredWorkflows = filteredWorkflows.filter(w => w.isActive === filters.isActive);
      }
      if (filters.triggerType) {
        filteredWorkflows = filteredWorkflows.filter(w => w.triggerType === filters.triggerType);
      }
      if (filters.search) {
        filteredWorkflows = filteredWorkflows.filter(w =>
          w.name.toLowerCase().includes(filters.search!.toLowerCase()) ||
          w.description.toLowerCase().includes(filters.search!.toLowerCase())
        );
      }

      return {
        workflows: filteredWorkflows,
        total: filteredWorkflows.length,
        page: filters.page || 1,
        totalPages: Math.ceil(filteredWorkflows.length / (filters.limit || 10)),
      };
    }
  }

  /**
   * Get workflow statistics
   */
  async getWorkflowStats(companyId: string): Promise<WorkflowStats> {
    try {
      const response = await apiService.get(`/workflows/${companyId}/stats`);
      return response as any;
    } catch (error) {
      console.error('Failed to fetch workflow stats, using fallback data:', error);

      // Fallback mock stats
      return {
        activeWorkflows: 2,
        totalRuns: 26,
        successRate: 92,
        needsAttention: 1,
        pendingApprovals: 12,
        completedToday: 8,
        failedStalled: 2,
        recentExecutions: [
          {
            id: 'exec-1',
            workflowId: 'workflow-1',
            entityType: 'transaction',
            entityId: 'txn-123',
            status: 'COMPLETED',
            context: {},
            startedAt: new Date(Date.now() - 3600000).toISOString(),
            completedAt: new Date().toISOString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: 'exec-2',
            workflowId: 'workflow-2',
            entityType: 'report',
            entityId: 'rpt-456',
            status: 'RUNNING',
            context: {},
            startedAt: new Date(Date.now() - 1800000).toISOString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ],
      };
    }
  }

  /**
   * Get a specific workflow
   */
  async getWorkflow(companyId: string, workflowId: string): Promise<Workflow> {
    try {
      const response = await apiService.get(`/workflows/${companyId}/${workflowId}`);
      return response as any;
    } catch (error) {
      console.error('Failed to fetch workflow:', error);
      throw error;
    }
  }

  /**
   * Create a new workflow
   */
  async createWorkflow(companyId: string, workflowData: CreateWorkflowData): Promise<Workflow> {
    try {
      const response = await apiService.post(`/workflows/${companyId}`, workflowData);
      return response as any;
    } catch (error) {
      console.error('Failed to create workflow:', error);
      throw error;
    }
  }

  /**
   * Update a workflow
   */
  async updateWorkflow(
    companyId: string,
    workflowId: string,
    workflowData: Partial<CreateWorkflowData>
  ): Promise<Workflow> {
    try {
      const response = await apiService.put(`/workflows/${companyId}/${workflowId}`, workflowData);
      return response as any;
    } catch (error) {
      console.error('Failed to update workflow:', error);
      throw error;
    }
  }

  /**
   * Delete a workflow
   */
  async deleteWorkflow(companyId: string, workflowId: string): Promise<void> {
    try {
      await apiService.delete(`/workflows/${companyId}/${workflowId}`);
    } catch (error) {
      console.error('Failed to delete workflow:', error);
      throw error;
    }
  }

  /**
   * Execute a workflow manually
   */
  async executeWorkflow(
    companyId: string,
    workflowId: string,
    entityType: string,
    entityId: string,
    context: Record<string, any> = {}
  ): Promise<WorkflowExecution> {
    try {
      const response = await apiService.post(`/workflows/${companyId}/${workflowId}/execute`, {
        entityType,
        entityId,
        context,
      });
      return response as any;
    } catch (error) {
      console.error('Failed to execute workflow:', error);
      throw error;
    }
  }

  /**
   * Get workflow executions
   */
  async getWorkflowExecutions(
    companyId: string,
    filters: {
      workflowId?: string;
      status?: string;
      entityType?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{
    executions: WorkflowExecution[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const params = new URLSearchParams();
    
    if (filters.workflowId) params.append('workflowId', filters.workflowId);
    if (filters.status) params.append('status', filters.status);
    if (filters.entityType) params.append('entityType', filters.entityType);
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());

    const response = await apiClient.get(`/companies/${companyId}/workflow-executions?${params}`);
    return response.data;
  }

  /**
   * Get workflow execution details
   */
  async getWorkflowExecution(companyId: string, executionId: string): Promise<WorkflowExecution & {
    workflow: Workflow;
    stepExecutions: WorkflowStepExecution[];
  }> {
    const response = await apiClient.get(`/companies/${companyId}/workflow-executions/${executionId}`);
    return response.data;
  }

  /**
   * Cancel workflow execution
   */
  async cancelWorkflowExecution(companyId: string, executionId: string): Promise<void> {
    await apiClient.post(`/companies/${companyId}/workflow-executions/${executionId}/cancel`);
  }

  /**
   * Get approval tasks for current user
   */
  async getApprovalTasks(
    companyId: string,
    filters: {
      status?: string;
      priority?: string;
      assignedTo?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{
    tasks: ApprovalTask[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const params = new URLSearchParams();
    
    if (filters.status) params.append('status', filters.status);
    if (filters.priority) params.append('priority', filters.priority);
    if (filters.assignedTo) params.append('assignedTo', filters.assignedTo);
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());

    const response = await apiClient.get(`/companies/${companyId}/approval-tasks?${params}`);
    return response.data;
  }

  /**
   * Approve a task
   */
  async approveTask(
    companyId: string,
    taskId: string,
    comment?: string
  ): Promise<ApprovalTask> {
    const response = await apiClient.post(`/companies/${companyId}/approval-tasks/${taskId}/approve`, {
      comment,
    });
    return response.data;
  }

  /**
   * Reject a task
   */
  async rejectTask(
    companyId: string,
    taskId: string,
    reason: string,
    comment?: string
  ): Promise<ApprovalTask> {
    const response = await apiClient.post(`/companies/${companyId}/approval-tasks/${taskId}/reject`, {
      reason,
      comment,
    });
    return response.data;
  }

  /**
   * Delegate a task
   */
  async delegateTask(
    companyId: string,
    taskId: string,
    delegateTo: string,
    comment?: string
  ): Promise<ApprovalTask> {
    const response = await apiClient.post(`/companies/${companyId}/approval-tasks/${taskId}/delegate`, {
      delegateTo,
      comment,
    });
    return response.data;
  }

  /**
   * Get workflow templates
   */
  async getWorkflowTemplates(): Promise<WorkflowTemplate[]> {
    try {
      const response = await apiService.get('/workflows/templates');
      return response as any;
    } catch (error) {
      console.error('Failed to fetch workflow templates, using fallback data:', error);

      // Fallback templates
      return [
        {
          id: 'template-1',
          name: 'Transaction Approval',
          description: 'Approve high-value transactions automatically',
          category: 'Finance',
          triggerType: 'EVENT',
          steps: [],
          actions: [{ type: 'APPROVAL_REQUEST', config: { approvers: ['manager'], requiredApprovals: 1 } }],
        },
        {
          id: 'template-2',
          name: 'Monthly Reporting',
          description: 'Generate and distribute monthly financial reports',
          category: 'Reporting',
          triggerType: 'SCHEDULE',
          steps: [],
          actions: [{ type: 'GENERATE_REPORT', config: { reportType: 'monthly', format: 'PDF' } }],
        },
        {
          id: 'template-3',
          name: 'Expense Approval',
          description: 'Multi-level approval for expense reports',
          category: 'Finance',
          triggerType: 'MANUAL',
          steps: [],
          actions: [{ type: 'EMAIL', config: { to: ['<EMAIL>'], subject: 'Expense Report' } }],
        },
        {
          id: 'template-4',
          name: 'Invoice Processing',
          description: 'Automated invoice validation and approval',
          category: 'Accounts Payable',
          triggerType: 'EVENT',
          steps: [],
          actions: [{ type: 'APPROVAL_REQUEST', config: { approvers: ['accounting'], requiredApprovals: 1 } }],
        },
      ];
    }
  }

  /**
   * Create workflow from template
   */
  async createFromTemplate(
    companyId: string,
    templateId: string,
    customizations: {
      name: string;
      description?: string;
      triggerConfig?: Record<string, any>;
    }
  ): Promise<Workflow> {
    const response = await apiClient.post(`/companies/${companyId}/workflows/from-template`, {
      templateId,
      ...customizations,
    });
    return response.data;
  }

  /**
   * Get workflow analytics
   */
  async getWorkflowAnalytics(
    companyId: string,
    workflowId?: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<{
    totalExecutions: number;
    completedExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    executionsByDay: Array<{ date: string; count: number }>;
    stepPerformance: Array<{ stepName: string; averageTime: number; successRate: number }>;
  }> {
    const params = new URLSearchParams();
    
    if (workflowId) params.append('workflowId', workflowId);
    if (dateFrom) params.append('dateFrom', dateFrom);
    if (dateTo) params.append('dateTo', dateTo);

    const response = await apiClient.get(`/companies/${companyId}/workflow-analytics?${params}`);
    return response.data;
  }

  /**
   * Get step types for workflow builder
   */
  getStepTypes(): Array<{
    type: string;
    name: string;
    description: string;
    icon: string;
    configSchema: any;
  }> {
    return [
      {
        type: 'APPROVAL',
        name: 'Approval',
        description: 'Require approval from specific users or roles',
        icon: 'CheckCircleIcon',
        configSchema: {
          approvers: { type: 'array', required: true },
          approvalType: { type: 'string', enum: ['ANY', 'ALL'], default: 'ANY' },
          dueDate: { type: 'string', format: 'date' },
          escalation: { type: 'object' },
        },
      },
      {
        type: 'NOTIFICATION',
        name: 'Notification',
        description: 'Send notifications via email, SMS, or in-app',
        icon: 'BellIcon',
        configSchema: {
          recipients: { type: 'array', required: true },
          channels: { type: 'array', enum: ['EMAIL', 'SMS', 'IN_APP'] },
          template: { type: 'string' },
          variables: { type: 'object' },
        },
      },
      {
        type: 'CONDITION',
        name: 'Condition',
        description: 'Branch workflow based on conditions',
        icon: 'CodeBracketIcon',
        configSchema: {
          conditions: { type: 'array', required: true },
          operator: { type: 'string', enum: ['AND', 'OR'], default: 'AND' },
          trueStep: { type: 'string' },
          falseStep: { type: 'string' },
        },
      },
      {
        type: 'ACTION',
        name: 'Action',
        description: 'Perform automated actions',
        icon: 'CogIcon',
        configSchema: {
          actionType: { type: 'string', required: true },
          parameters: { type: 'object' },
          retryPolicy: { type: 'object' },
        },
      },
    ];
  }

  /**
   * Validate workflow configuration
   */
  validateWorkflow(workflow: CreateWorkflowData): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation
    if (!workflow.name?.trim()) {
      errors.push('Workflow name is required');
    }

    if (!workflow.steps || workflow.steps.length === 0) {
      errors.push('Workflow must have at least one step');
    }

    // Validate steps
    workflow.steps?.forEach((step, index) => {
      if (!step.name?.trim()) {
        errors.push(`Step ${index + 1} must have a name`);
      }

      if (!step.stepType) {
        errors.push(`Step ${index + 1} must have a type`);
      }

      // Validate step-specific configuration
      if (step.stepType === 'APPROVAL' && !step.config.approvers?.length) {
        errors.push(`Approval step "${step.name}" must have at least one approver`);
      }

      if (step.stepType === 'NOTIFICATION' && !step.config.recipients?.length) {
        errors.push(`Notification step "${step.name}" must have at least one recipient`);
      }
    });

    // Check for circular dependencies
    const stepIds = workflow.steps.map((_, index) => index.toString());
    const hasCircularDependency = this.checkCircularDependency(workflow.steps, stepIds);
    if (hasCircularDependency) {
      errors.push('Workflow contains circular dependencies');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  private checkCircularDependency(steps: any[], stepIds: string[]): boolean {
    // Simplified circular dependency check
    // In a real implementation, this would be more sophisticated
    return false;
  }
}

export const workflowService = new WorkflowService();
export default workflowService;
