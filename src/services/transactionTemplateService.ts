import { apiService } from './api';

export interface TransactionTemplate {
  id: string;
  companyId?: string;
  name: string;
  description?: string;
  category: TemplateCategory;
  isActive: boolean;
  isSystemTemplate: boolean;
  usageCount: number;
  defaultValues: Record<string, any>;
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
  entries: TransactionTemplateEntry[];
}

export interface TransactionTemplateEntry {
  id: string;
  templateId: string;
  accountId: string;
  accountCode?: string;
  accountName?: string;
  description?: string;
  debitAmount: number;
  creditAmount: number;
  isVariableAmount: boolean;
  amountFormula?: string;
  lineNumber: number;
  metadata: Record<string, any>;
}

export type TemplateCategory = 
  | 'GENERAL'
  | 'SALES'
  | 'PURCHASES'
  | 'PAYROLL'
  | 'BANKING'
  | 'ADJUSTMENTS'
  | 'DEPRECIATION'
  | 'TAX'
  | 'INVENTORY'
  | 'FIXED_ASSETS';

export interface CreateTemplateData {
  name: string;
  description?: string;
  category: TemplateCategory;
  defaultValues?: Record<string, any>;
  entries: Array<{
    accountId: string;
    description?: string;
    debitAmount: number;
    creditAmount: number;
    isVariableAmount?: boolean;
    amountFormula?: string;
    lineNumber: number;
    metadata?: Record<string, any>;
  }>;
}

export interface ApplyTemplateData {
  transactionDate: string;
  description?: string;
  reference?: string;
  variables?: Record<string, any>;
  entryOverrides?: Array<{
    lineNumber: number;
    debitAmount?: number;
    creditAmount?: number;
    description?: string;
  }>;
}

export interface TemplateFilters {
  category?: TemplateCategory;
  isActive?: boolean;
  includeSystemTemplates?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

export interface TemplateListResponse {
  templates: TransactionTemplate[];
  total: number;
  page: number;
  totalPages: number;
}

class TransactionTemplateService {
  /**
   * Get all templates for a company
   */
  public async getTemplates(
    companyId: string,
    filters?: TemplateFilters
  ): Promise<TemplateListResponse> {
    const params = new URLSearchParams();

    if (filters?.category) {
      params.append('category', filters.category);
    }

    if (filters?.isActive !== undefined) {
      params.append('isActive', filters.isActive.toString());
    }

    if (filters?.includeSystemTemplates !== undefined) {
      params.append('includeSystemTemplates', filters.includeSystemTemplates.toString());
    }

    if (filters?.search) {
      params.append('search', filters.search);
    }

    if (filters?.page) {
      params.append('page', filters.page.toString());
    }

    if (filters?.limit) {
      params.append('limit', filters.limit.toString());
    }

    const queryString = params.toString();
    const url = `/transaction-templates/${companyId}${queryString ? `?${queryString}` : ''}`;

    return await apiService.get<TemplateListResponse>(url);
  }

  /**
   * Get a single template by ID
   */
  public async getTemplate(companyId: string, templateId: string): Promise<TransactionTemplate> {
    return await apiService.get<TransactionTemplate>(
      `/transaction-templates/${companyId}/${templateId}`
    );
  }

  /**
   * Create a new template
   */
  public async createTemplate(
    companyId: string,
    data: CreateTemplateData
  ): Promise<TransactionTemplate> {
    return await apiService.post<TransactionTemplate>(
      `/transaction-templates/${companyId}`,
      data
    );
  }

  /**
   * Update an existing template
   */
  public async updateTemplate(
    companyId: string,
    templateId: string,
    data: Partial<CreateTemplateData>
  ): Promise<TransactionTemplate> {
    return await apiService.put<TransactionTemplate>(
      `/transaction-templates/${companyId}/${templateId}`,
      data
    );
  }

  /**
   * Delete a template
   */
  public async deleteTemplate(companyId: string, templateId: string): Promise<void> {
    return await apiService.delete<void>(
      `/transaction-templates/${companyId}/${templateId}`
    );
  }

  /**
   * Apply template to generate transaction data
   */
  public async applyTemplate(
    companyId: string,
    templateId: string,
    data: ApplyTemplateData
  ): Promise<{
    transactionDate: string;
    description: string;
    reference?: string;
    entries: Array<{
      accountId: string;
      description?: string;
      debitAmount: number;
      creditAmount: number;
    }>;
  }> {
    return await apiService.post(
      `/transaction-templates/${companyId}/${templateId}/apply`,
      data
    );
  }

  /**
   * Get template categories
   */
  public async getCategories(): Promise<Array<{ value: TemplateCategory; label: string }>> {
    return await apiService.get<Array<{ value: TemplateCategory; label: string }>>(
      '/transaction-templates/categories'
    );
  }

  /**
   * Validate template entries balance
   */
  public validateTemplateBalance(
    entries: Array<{ debitAmount: number; creditAmount: number }>
  ): {
    isBalanced: boolean;
    totalDebits: number;
    totalCredits: number;
    difference: number;
  } {
    const totalDebits = entries.reduce(
      (sum, entry) => sum + (entry.debitAmount || 0),
      0
    );
    const totalCredits = entries.reduce(
      (sum, entry) => sum + (entry.creditAmount || 0),
      0
    );
    const difference = Math.abs(totalDebits - totalCredits);

    return {
      isBalanced: difference < 0.01,
      totalDebits,
      totalCredits,
      difference,
    };
  }

  /**
   * Get category color for UI display
   */
  public getCategoryColor(category: TemplateCategory): string {
    const colors = {
      GENERAL: 'gray',
      SALES: 'green',
      PURCHASES: 'blue',
      PAYROLL: 'purple',
      BANKING: 'indigo',
      ADJUSTMENTS: 'yellow',
      DEPRECIATION: 'orange',
      TAX: 'red',
      INVENTORY: 'teal',
      FIXED_ASSETS: 'pink',
    };

    return colors[category] || 'gray';
  }

  /**
   * Get category icon for UI display
   */
  public getCategoryIcon(category: TemplateCategory): string {
    const icons = {
      GENERAL: 'DocumentTextIcon',
      SALES: 'CurrencyDollarIcon',
      PURCHASES: 'ShoppingCartIcon',
      PAYROLL: 'UsersIcon',
      BANKING: 'BuildingLibraryIcon',
      ADJUSTMENTS: 'AdjustmentsHorizontalIcon',
      DEPRECIATION: 'TrendingDownIcon',
      TAX: 'ReceiptPercentIcon',
      INVENTORY: 'CubeIcon',
      FIXED_ASSETS: 'HomeModernIcon',
    };

    return icons[category] || 'DocumentTextIcon';
  }

  /**
   * Format template usage count
   */
  public formatUsageCount(count: number): string {
    if (count === 0) return 'Never used';
    if (count === 1) return 'Used once';
    return `Used ${count} times`;
  }
}

export const transactionTemplateService = new TransactionTemplateService();
