import { apiService } from "./api";

export interface DashboardStats {
  revenue: number;
  expenses: number;
  netIncome: number;
  pendingInvoices: number;
  overdueInvoices: number;
  totalContacts: number;
  totalTransactions: number;
  accountsReceivable: number;
  accountsPayable: number;
  cashBalance: number;
  // Enterprise metrics
  grossProfitMargin: number;
  operatingCashFlow: number;
  workingCapital: number;
  burnRate: number;
  avgCollectionPeriod: number;
  avgPaymentPeriod: number;
  revenueGrowth: number;
  expenseGrowth: number;
}

export interface RecentActivity {
  id: string;
  type: "invoice" | "transaction" | "contact" | "payment";
  title: string;
  description: string;
  amount?: number;
  currency?: string;
  date: string;
  status?: string;
}

export interface DashboardData {
  stats: DashboardStats;
  recentActivity: RecentActivity[];
}

export interface HistoricalDataPoint {
  period: string;
  value: number;
  label?: string;
}

export interface ExpenseBreakdown {
  label: string;
  value: number;
  color: string;
}

class DashboardService {
  /**
   * Get dashboard statistics and recent activity
   */
  async getDashboardData(companyId: string): Promise<DashboardData> {
    return await apiService.get(`/dashboard/${companyId}`);
  }

  /**
   * Get financial summary for dashboard
   */
  async getFinancialSummary(
    companyId: string,
    period: "month" | "quarter" | "year" = "month"
  ): Promise<DashboardStats> {
    return await apiService.get(
      `/dashboard/${companyId}/financial-summary?period=${period}`
    );
  }

  /**
   * Get recent activity for dashboard
   */
  async getRecentActivity(
    companyId: string,
    limit: number = 10
  ): Promise<RecentActivity[]> {
    return await apiService.get(
      `/dashboard/${companyId}/recent-activity?limit=${limit}`
    );
  }

  /**
   * Format currency for display
   */
  formatCurrency(amount: number, currency: string = "USD"): string {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  }

  /**
   * Format date for display
   */
  formatDate(date: string): string {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  }

  /**
   * Format relative time (e.g., "2 hours ago")
   */
  formatRelativeTime(date: string): string {
    const now = new Date();
    const activityDate = new Date(date);
    const diffInMs = now.getTime() - activityDate.getTime();
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
      return diffInMinutes < 1
        ? "Just now"
        : `${diffInMinutes} minute${diffInMinutes > 1 ? "s" : ""} ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? "s" : ""} ago`;
    } else if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays > 1 ? "s" : ""} ago`;
    } else {
      return this.formatDate(date);
    }
  }

  /**
   * Get activity icon based on type
   */
  getActivityIcon(type: string): string {
    switch (type) {
      case "invoice":
        return "📄";
      case "transaction":
        return "💰";
      case "contact":
        return "👤";
      case "payment":
        return "💳";
      default:
        return "📋";
    }
  }

  /**
   * Get status color class
   */
  getStatusColor(status: string): string {
    switch (status?.toLowerCase()) {
      case "paid":
        return "text-green-600 bg-green-100";
      case "sent":
      case "pending":
        return "text-yellow-600 bg-yellow-100";
      case "draft":
        return "text-gray-600 bg-gray-100";
      case "overdue":
        return "text-red-600 bg-red-100";
      case "posted":
        return "text-blue-600 bg-blue-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  }

  /**
   * Get revenue trend data from database
   */
  async getRevenueTrend(companyId: string): Promise<HistoricalDataPoint[]> {
    return await apiService.get(`/dashboard/${companyId}/charts/revenue-trend`);
  }

  /**
   * Get expense trend data from database
   */
  async getExpenseTrend(companyId: string): Promise<HistoricalDataPoint[]> {
    return await apiService.get(`/dashboard/${companyId}/charts/expense-trend`);
  }

  /**
   * Get cash flow trend data from database
   */
  async getCashFlowTrend(companyId: string): Promise<HistoricalDataPoint[]> {
    return await apiService.get(
      `/dashboard/${companyId}/charts/cash-flow-trend`
    );
  }

  /**
   * Get expense breakdown data from database
   */
  async getExpenseBreakdown(
    companyId: string,
    period: "month" | "quarter" | "year" = "month"
  ): Promise<ExpenseBreakdown[]> {
    // Add cache-busting parameter to force fresh data
    const timestamp = Date.now();
    return await apiService.get(
      `/dashboard/${companyId}/charts/expense-breakdown?period=${period}&_t=${timestamp}`
    );
  }

  /**
   * Get revenue and expense comparison data
   */
  async getRevenueExpenseComparison(companyId: string): Promise<{
    revenue: HistoricalDataPoint[];
    expenses: HistoricalDataPoint[];
  }> {
    return await apiService.get(
      `/dashboard/${companyId}/charts/revenue-expense-comparison`
    );
  }
}

export const dashboardService = new DashboardService();
