import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';
// import { saveAs } from 'file-saver';
import html2canvas from 'html2canvas';

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

export interface ExportOptions {
  filename?: string;
  title?: string;
  subtitle?: string;
  companyName?: string;
  companyAddress?: string;
  reportDate?: string;
  orientation?: 'portrait' | 'landscape';
  format?: 'a4' | 'letter';
  includeHeader?: boolean;
  includeFooter?: boolean;
  watermark?: string;
}

export interface TableColumn {
  header: string;
  dataKey: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  format?: 'currency' | 'number' | 'percentage' | 'date';
}

export interface TableData {
  columns: TableColumn[];
  rows: any[];
  totals?: any;
  subtotals?: any[];
}

export interface ChartData {
  type: 'bar' | 'line' | 'pie' | 'doughnut';
  data: any;
  options?: any;
  title?: string;
}

class ExportService {
  private defaultOptions: ExportOptions = {
    orientation: 'portrait',
    format: 'a4',
    includeHeader: true,
    includeFooter: true,
  };

  /**
   * Export table data to PDF
   */
  public async exportToPDF(
    tableData: TableData,
    options: ExportOptions = {}
  ): Promise<void> {
    const opts = { ...this.defaultOptions, ...options };
    const doc = new jsPDF({
      orientation: opts.orientation,
      format: opts.format,
    });

    // Add header
    if (opts.includeHeader) {
      this.addPDFHeader(doc, opts);
    }

    // Calculate starting Y position
    let startY = opts.includeHeader ? 60 : 20;

    // Add table
    const tableColumns = tableData.columns.map(col => ({
      header: col.header,
      dataKey: col.dataKey,
      width: col.width,
    }));

    const tableRows = tableData.rows.map(row => {
      const formattedRow: any = {};
      tableData.columns.forEach(col => {
        formattedRow[col.dataKey] = this.formatCellValue(
          row[col.dataKey],
          col.format
        );
      });
      return formattedRow;
    });

    doc.autoTable({
      columns: tableColumns,
      body: tableRows,
      startY: startY,
      styles: {
        fontSize: 9,
        cellPadding: 3,
      },
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold',
      },
      alternateRowStyles: {
        fillColor: [245, 245, 245],
      },
      didDrawPage: (_data: any) => {
        // Add footer
        if (opts.includeFooter) {
          this.addPDFFooter(doc, opts);
        }
      },
    });

    // Add totals if provided
    if (tableData.totals) {
      this.addTotalsSection(doc, tableData.totals, tableData.columns);
    }

    // Save the PDF
    const filename = opts.filename || `report_${Date.now()}.pdf`;
    doc.save(filename);
  }

  /**
   * Export table data to Excel
   */
  public async exportToExcel(
    tableData: TableData,
    options: ExportOptions = {}
  ): Promise<void> {
    const opts = { ...this.defaultOptions, ...options };
    
    // Create workbook
    const wb = XLSX.utils.book_new();
    
    // Prepare data for Excel
    const excelData: any[] = [];
    
    // Add header information
    if (opts.includeHeader && opts.companyName) {
      excelData.push([opts.companyName]);
      excelData.push([opts.title || 'Financial Report']);
      if (opts.subtitle) excelData.push([opts.subtitle]);
      if (opts.reportDate) excelData.push([`Report Date: ${opts.reportDate}`]);
      excelData.push([]); // Empty row
    }
    
    // Add column headers
    excelData.push(tableData.columns.map(col => col.header));
    
    // Add data rows
    tableData.rows.forEach(row => {
      const excelRow = tableData.columns.map(col => {
        const value = row[col.dataKey];
        return this.formatExcelValue(value, col.format);
      });
      excelData.push(excelRow);
    });
    
    // Add totals if provided
    if (tableData.totals) {
      excelData.push([]); // Empty row
      const totalsRow = tableData.columns.map(col => {
        if (col.dataKey in tableData.totals!) {
          return this.formatExcelValue(tableData.totals![col.dataKey], col.format);
        }
        return col.dataKey === tableData.columns[0].dataKey ? 'TOTAL:' : '';
      });
      excelData.push(totalsRow);
    }
    
    // Create worksheet
    const ws = XLSX.utils.aoa_to_sheet(excelData);
    
    // Style the worksheet
    this.styleExcelWorksheet(ws, tableData.columns, opts);
    
    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Report');
    
    // Save the file
    const filename = opts.filename || `report_${Date.now()}.xlsx`;
    XLSX.writeFile(wb, filename);
  }

  /**
   * Export chart to PDF
   */
  public async exportChartToPDF(
    chartElement: HTMLElement,
    options: ExportOptions = {}
  ): Promise<void> {
    const opts = { ...this.defaultOptions, ...options };
    
    // Convert chart to canvas
    const canvas = await html2canvas(chartElement, {
      backgroundColor: '#ffffff',
      scale: 2,
    });
    
    const doc = new jsPDF({
      orientation: opts.orientation,
      format: opts.format,
    });
    
    // Add header
    if (opts.includeHeader) {
      this.addPDFHeader(doc, opts);
    }
    
    // Calculate image dimensions
    const imgWidth = doc.internal.pageSize.getWidth() - 40;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    
    // Add chart image
    const startY = opts.includeHeader ? 60 : 20;
    doc.addImage(
      canvas.toDataURL('image/png'),
      'PNG',
      20,
      startY,
      imgWidth,
      imgHeight
    );
    
    // Add footer
    if (opts.includeFooter) {
      this.addPDFFooter(doc, opts);
    }
    
    // Save the PDF
    const filename = opts.filename || `chart_${Date.now()}.pdf`;
    doc.save(filename);
  }

  /**
   * Export multiple reports to a single PDF
   */
  public async exportMultiReportPDF(
    reports: Array<{
      title: string;
      tableData?: TableData;
      chartElement?: HTMLElement;
    }>,
    options: ExportOptions = {}
  ): Promise<void> {
    const opts = { ...this.defaultOptions, ...options };
    const doc = new jsPDF({
      orientation: opts.orientation,
      format: opts.format,
    });

    let isFirstPage = true;

    for (const report of reports) {
      if (!isFirstPage) {
        doc.addPage();
      }

      // Add header
      if (opts.includeHeader) {
        this.addPDFHeader(doc, { ...opts, title: report.title });
      }

      let startY = opts.includeHeader ? 60 : 20;

      if (report.tableData) {
        // Add table
        const tableColumns = report.tableData.columns.map(col => ({
          header: col.header,
          dataKey: col.dataKey,
          width: col.width,
        }));

        const tableRows = report.tableData.rows.map(row => {
          const formattedRow: any = {};
          report.tableData!.columns.forEach(col => {
            formattedRow[col.dataKey] = this.formatCellValue(
              row[col.dataKey],
              col.format
            );
          });
          return formattedRow;
        });

        doc.autoTable({
          columns: tableColumns,
          body: tableRows,
          startY: startY,
          styles: {
            fontSize: 9,
            cellPadding: 3,
          },
          headStyles: {
            fillColor: [41, 128, 185],
            textColor: 255,
            fontStyle: 'bold',
          },
          alternateRowStyles: {
            fillColor: [245, 245, 245],
          },
        });
      }

      if (report.chartElement) {
        // Add chart
        const canvas = await html2canvas(report.chartElement, {
          backgroundColor: '#ffffff',
          scale: 2,
        });

        const imgWidth = doc.internal.pageSize.getWidth() - 40;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        doc.addImage(
          canvas.toDataURL('image/png'),
          'PNG',
          20,
          startY,
          imgWidth,
          imgHeight
        );
      }

      // Add footer
      if (opts.includeFooter) {
        this.addPDFFooter(doc, opts);
      }

      isFirstPage = false;
    }

    // Save the PDF
    const filename = opts.filename || `multi_report_${Date.now()}.pdf`;
    doc.save(filename);
  }

  /**
   * Add header to PDF
   */
  private addPDFHeader(doc: jsPDF, options: ExportOptions): void {
    const pageWidth = doc.internal.pageSize.getWidth();
    
    // Company name
    if (options.companyName) {
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text(options.companyName, 20, 20);
    }
    
    // Company address
    if (options.companyAddress) {
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(options.companyAddress, 20, 30);
    }
    
    // Report title
    if (options.title) {
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text(options.title, pageWidth / 2, 20, { align: 'center' });
    }
    
    // Subtitle
    if (options.subtitle) {
      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.text(options.subtitle, pageWidth / 2, 30, { align: 'center' });
    }
    
    // Report date
    if (options.reportDate) {
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(`Report Date: ${options.reportDate}`, pageWidth - 20, 20, { align: 'right' });
    }
    
    // Add line separator
    doc.setLineWidth(0.5);
    doc.line(20, 45, pageWidth - 20, 45);
  }

  /**
   * Add footer to PDF
   */
  private addPDFFooter(doc: jsPDF, options: ExportOptions): void {
    const pageHeight = doc.internal.pageSize.getHeight();
    const pageWidth = doc.internal.pageSize.getWidth();
    
    // Add line separator
    doc.setLineWidth(0.5);
    doc.line(20, pageHeight - 25, pageWidth - 20, pageHeight - 25);
    
    // Page number
    doc.setFontSize(8);
    doc.setFont('helvetica', 'normal');
    doc.text(
      `Page ${doc.getCurrentPageInfo().pageNumber}`,
      pageWidth / 2,
      pageHeight - 15,
      { align: 'center' }
    );
    
    // Generation date
    const now = new Date().toLocaleString();
    doc.text(`Generated: ${now}`, pageWidth - 20, pageHeight - 15, { align: 'right' });
    
    // Watermark
    if (options.watermark) {
      doc.setFontSize(6);
      doc.text(options.watermark, 20, pageHeight - 15);
    }
  }

  /**
   * Add totals section to PDF
   */
  private addTotalsSection(doc: jsPDF, totals: any, columns: TableColumn[]): void {
    const finalY = (doc as any).lastAutoTable.finalY || 100;
    
    doc.setFontSize(10);
    doc.setFont('helvetica', 'bold');
    
    let yPosition = finalY + 15;
    
    Object.entries(totals).forEach(([key, value]) => {
      const column = columns.find(col => col.dataKey === key);
      if (column) {
        const formattedValue = this.formatCellValue(value, column.format);
        doc.text(`${column.header}: ${formattedValue}`, 20, yPosition);
        yPosition += 10;
      }
    });
  }

  /**
   * Format cell value based on type
   */
  private formatCellValue(value: any, format?: string): string {
    if (value === null || value === undefined) return '';
    
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
        }).format(Number(value) || 0);
      
      case 'number':
        return new Intl.NumberFormat('en-US').format(Number(value) || 0);
      
      case 'percentage':
        return `${(Number(value) || 0).toFixed(2)}%`;
      
      case 'date':
        return new Date(value).toLocaleDateString();
      
      default:
        return String(value);
    }
  }

  /**
   * Format value for Excel
   */
  private formatExcelValue(value: any, format?: string): any {
    if (value === null || value === undefined) return '';
    
    switch (format) {
      case 'currency':
      case 'number':
        return Number(value) || 0;
      
      case 'percentage':
        return (Number(value) || 0) / 100; // Excel expects decimal for percentage
      
      case 'date':
        return new Date(value);
      
      default:
        return value;
    }
  }

  /**
   * Style Excel worksheet
   */
  private styleExcelWorksheet(
    ws: XLSX.WorkSheet,
    columns: TableColumn[],
    _options: ExportOptions
  ): void {
    // Set column widths
    const colWidths = columns.map(col => ({ wch: col.width || 15 }));
    ws['!cols'] = colWidths;
    
    // Add formatting for currency and number columns
    const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
    
    for (let R = range.s.r; R <= range.e.r; ++R) {
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
        const cell = ws[cellAddress];
        
        if (cell && R > 0) { // Skip header row
          const column = columns[C];
          if (column?.format === 'currency') {
            cell.z = '"$"#,##0.00';
          } else if (column?.format === 'number') {
            cell.z = '#,##0.00';
          } else if (column?.format === 'percentage') {
            cell.z = '0.00%';
          }
        }
      }
    }
  }
}

export const exportService = new ExportService();
