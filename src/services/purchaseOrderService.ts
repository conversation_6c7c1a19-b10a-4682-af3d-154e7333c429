import api from './api';
import type {
  PurchaseOrder,
  CreatePurchaseOrderRequest,
  UpdatePurchaseOrderRequest,
  PurchaseOrderFilters,
  PurchaseOrderListResponse,
  UpdatePurchaseOrderStatusRequest,
} from '../types/purchaseOrder';

class PurchaseOrderService {
  private baseUrl = '/purchase-orders';

  /**
   * Get all purchase orders for a company
   */
  async getPurchaseOrders(
    companyId: string,
    filters?: PurchaseOrderFilters
  ): Promise<PurchaseOrderListResponse> {
    const params = new URLSearchParams();
    
    if (filters?.status) params.append('status', filters.status);
    if (filters?.vendorId) params.append('vendorId', filters.vendorId);
    if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters?.dateTo) params.append('dateTo', filters.dateTo);
    if (filters?.search) params.append('search', filters.search);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());

    const queryString = params.toString();
    const url = `${this.baseUrl}/${companyId}${queryString ? `?${queryString}` : ''}`;
    
    const response = await api.get(url);
    return response.data;
  }

  /**
   * Get a single purchase order by ID
   */
  async getPurchaseOrder(companyId: string, poId: string): Promise<PurchaseOrder> {
    const response = await api.get(`${this.baseUrl}/${companyId}/${poId}`);
    return response.data;
  }

  /**
   * Create a new purchase order
   */
  async createPurchaseOrder(data: CreatePurchaseOrderRequest): Promise<PurchaseOrder> {
    const response = await api.post(this.baseUrl, data);
    return response.data;
  }

  /**
   * Update an existing purchase order
   */
  async updatePurchaseOrder(
    companyId: string,
    poId: string,
    data: UpdatePurchaseOrderRequest
  ): Promise<PurchaseOrder> {
    const response = await api.put(`${this.baseUrl}/${companyId}/${poId}`, data);
    return response.data;
  }

  /**
   * Update purchase order status
   */
  async updatePurchaseOrderStatus(
    companyId: string,
    poId: string,
    data: UpdatePurchaseOrderStatusRequest
  ): Promise<{ message: string; status: string }> {
    const response = await api.post(`${this.baseUrl}/${companyId}/${poId}/status`, data);
    return response.data;
  }

  /**
   * Delete a purchase order
   */
  async deletePurchaseOrder(companyId: string, poId: string): Promise<{ message: string }> {
    const response = await api.delete(`${this.baseUrl}/${companyId}/${poId}`);
    return response.data;
  }

  /**
   * Approve a purchase order
   */
  async approvePurchaseOrder(companyId: string, poId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updatePurchaseOrderStatus(companyId, poId, {
      status: 'APPROVED',
      notes: notes || 'Purchase order approved',
    });
  }

  /**
   * Send a purchase order to vendor
   */
  async sendPurchaseOrder(companyId: string, poId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updatePurchaseOrderStatus(companyId, poId, {
      status: 'SENT',
      notes: notes || 'Purchase order sent to vendor',
    });
  }

  /**
   * Cancel a purchase order
   */
  async cancelPurchaseOrder(companyId: string, poId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updatePurchaseOrderStatus(companyId, poId, {
      status: 'CANCELLED',
      notes: notes || 'Purchase order cancelled',
    });
  }

  /**
   * Mark purchase order as partially received
   */
  async markPartiallyReceived(companyId: string, poId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updatePurchaseOrderStatus(companyId, poId, {
      status: 'PARTIALLY_RECEIVED',
      notes: notes || 'Purchase order partially received',
    });
  }

  /**
   * Mark purchase order as fully received
   */
  async markFullyReceived(companyId: string, poId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updatePurchaseOrderStatus(companyId, poId, {
      status: 'FULLY_RECEIVED',
      notes: notes || 'Purchase order fully received',
    });
  }

  /**
   * Close a purchase order
   */
  async closePurchaseOrder(companyId: string, poId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updatePurchaseOrderStatus(companyId, poId, {
      status: 'CLOSED',
      notes: notes || 'Purchase order closed',
    });
  }

  /**
   * Submit purchase order for approval
   */
  async submitForApproval(companyId: string, poId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updatePurchaseOrderStatus(companyId, poId, {
      status: 'PENDING_APPROVAL',
      notes: notes || 'Purchase order submitted for approval',
    });
  }

  /**
   * Format currency amount
   */
  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }

  /**
   * Format date
   */
  formatDate(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  /**
   * Calculate line item total
   */
  calculateLineTotal(quantity: number, unitCost: number, discountPercentage: number = 0): number {
    const subtotal = quantity * unitCost;
    const discountAmount = (subtotal * discountPercentage) / 100;
    return subtotal - discountAmount;
  }

  /**
   * Calculate line item tax
   */
  calculateLineTax(lineTotal: number, taxRate: number = 0): number {
    return (lineTotal * taxRate) / 100;
  }

  /**
   * Calculate purchase order totals
   */
  calculateTotals(lineItems: Array<{
    quantityOrdered: number;
    unitCost: number;
    discountPercentage?: number;
    taxRate?: number;
  }>): {
    subtotal: number;
    totalDiscount: number;
    totalTax: number;
    total: number;
  } {
    let subtotal = 0;
    let totalDiscount = 0;
    let totalTax = 0;

    lineItems.forEach(item => {
      const lineSubtotal = item.quantityOrdered * item.unitCost;
      const discountAmount = (lineSubtotal * (item.discountPercentage || 0)) / 100;
      const lineTotal = lineSubtotal - discountAmount;
      const taxAmount = (lineTotal * (item.taxRate || 0)) / 100;

      subtotal += lineSubtotal;
      totalDiscount += discountAmount;
      totalTax += taxAmount;
    });

    const total = subtotal - totalDiscount + totalTax;

    return {
      subtotal,
      totalDiscount,
      totalTax,
      total,
    };
  }

  /**
   * Generate PO number (client-side preview)
   */
  generatePONumber(lastNumber: number = 0): string {
    return `PO-${String(lastNumber + 1).padStart(6, '0')}`;
  }

  /**
   * Validate purchase order data
   */
  validatePurchaseOrder(data: CreatePurchaseOrderRequest | UpdatePurchaseOrderRequest): string[] {
    const errors: string[] = [];

    if ('vendorId' in data && !data.vendorId) {
      errors.push('Vendor is required');
    }

    if ('poDate' in data && !data.poDate) {
      errors.push('PO date is required');
    }

    if ('lineItems' in data && data.lineItems) {
      if (data.lineItems.length === 0) {
        errors.push('At least one line item is required');
      }

      data.lineItems.forEach((item, index) => {
        if (!item.description?.trim()) {
          errors.push(`Line item ${index + 1}: Description is required`);
        }
        if (!item.quantityOrdered || item.quantityOrdered <= 0) {
          errors.push(`Line item ${index + 1}: Quantity must be greater than 0`);
        }
        if (!item.unitCost || item.unitCost <= 0) {
          errors.push(`Line item ${index + 1}: Unit cost must be greater than 0`);
        }
      });
    }

    return errors;
  }
}

export default new PurchaseOrderService();
