import { apiService } from "./api";

export interface ReportFilters {
  startDate?: string;
  endDate?: string;
  asOfDate?: string;
  accountType?: string;
  includeInactive?: boolean;
  currency?: string;
  reportType?: string;
}

export interface BalanceSheetData {
  reportType: "BALANCE_SHEET";
  asOfDate: string;
  data: {
    assets: {
      accounts: Record<string, any[]>;
      total: number;
    };
    liabilities: {
      accounts: Record<string, any[]>;
      total: number;
    };
    equity: {
      accounts: Record<string, any[]>;
      total: number;
    };
    totals: {
      totalAssets: number;
      totalLiabilities: number;
      totalEquity: number;
      netIncome: number;
      totalEquityWithNetIncome: number;
      totalLiabilitiesAndEquity: number;
      isBalanced: boolean;
    };
  };
}

export interface IncomeStatementData {
  reportType: "INCOME_STATEMENT";
  startDate: string;
  endDate: string;
  data: {
    revenue: {
      accounts: Record<string, any>;
      total: number;
    };
    expenses: {
      accounts: Record<string, any>;
      total: number;
    };
    costOfGoodsSold: {
      accounts: Array<{
        id: string;
        code: string;
        name: string;
        balance: number;
      }>;
      total: number;
    };
  };
  totals: {
    totalRevenue: number;
    totalExpenses: number;
    totalCOGS: number;
    grossProfit: number;
    operatingRevenue: number;
    operatingExpenses: number;
    operatingIncome: number;
    netIncome: number;
  };
}

export interface TrialBalanceData {
  reportType: "TRIAL_BALANCE";
  asOfDate: string;
  data: {
    accounts: Array<{
      accountId: string;
      accountCode: string;
      accountName: string;
      accountType: string;
      debitBalance: number;
      creditBalance: number;
      netBalance: number;
    }>;
    accountsByType: any; // Additional data from backend
  };
  totals: {
    totalDebits: number;
    totalCredits: number;
    difference: number;
    isBalanced: boolean;
  };
  summary?: {
    totalAccounts: number;
    accountsWithDebits: number;
    accountsWithCredits: number;
  };
}

export interface AgingReportData {
  reportType: "AGING_REPORT";
  reportSubType: string;
  asOfDate: string;
  data: {
    accounts: Array<{
      accountId: string;
      accountCode: string;
      accountName: string;
      accountType: string;
      current: number;
      "1-30": number;
      "31-60": number;
      "61-90": number;
      over90: number;
      total: number;
    }>;
    buckets: string[];
  };
  totals: {
    current: number;
    "1-30": number;
    "31-60": number;
    "61-90": number;
    over90: number;
    total: number;
  };
  summary: {
    totalAccounts: number;
    totalOutstanding: number;
    currentPercentage: number;
    overduePercentage: number;
  };
}

export interface CashFlowData {
  reportType: "CASH_FLOW_STATEMENT";
  startDate: string;
  endDate: string;
  data: {
    operatingActivities: {
      netIncome: number;
      adjustments: {
        depreciation: number;
        changes: {
          accountsReceivable: number;
          accountsPayable: number;
          inventory: number;
          otherCurrentAssets: number;
          otherCurrentLiabilities: number;
        };
      };
      netCash: number;
    };
    investingActivities: {
      purchases: number;
      proceeds: number;
      netCash: number;
    };
    financingActivities: {
      stockIssuance: number;
      dividends: number;
      borrowing: number;
      debtRepayment: number;
      netCash: number;
    };
    netChange: number;
    beginningCash: number;
    endingCash: number;
  };
}

export interface CustomReport {
  id: string;
  name: string;
  description: string;
  type: "TABLE" | "CHART" | "SUMMARY";
  dataSource: string;
  companyId: string;
  createdAt: string;
  updatedAt: string;
  lastRun?: string;
  status: "ACTIVE" | "DRAFT";
  config?: any;
  schedule_frequency?: string;
  schedule_delivery?: string;
  schedule_recipient?: string;
}

export interface CreateCustomReportRequest {
  name: string;
  description: string;
  type: "TABLE" | "CHART" | "SUMMARY";
  dataSource: string;
  config?: any;
}

class ReportService {
  /**
   * Generate Balance Sheet report
   */
  public async getBalanceSheet(
    companyId: string,
    filters?: ReportFilters
  ): Promise<BalanceSheetData> {
    const params = new URLSearchParams();

    if (filters?.asOfDate) {
      params.append("asOfDate", filters.asOfDate);
    }

    const queryString = params.toString();
    const url = `/reports/${companyId}/balance-sheet${
      queryString ? `?${queryString}` : ""
    }`;

    return await apiService.get<BalanceSheetData>(url);
  }

  /**
   * Generate Income Statement report
   */
  public async getIncomeStatement(
    companyId: string,
    filters?: ReportFilters
  ): Promise<IncomeStatementData> {
    const params = new URLSearchParams();

    if (filters?.startDate) {
      params.append("startDate", filters.startDate);
    }

    if (filters?.endDate) {
      params.append("endDate", filters.endDate);
    }

    const queryString = params.toString();
    const url = `/reports/${companyId}/income-statement${
      queryString ? `?${queryString}` : ""
    }`;

    return await apiService.get<IncomeStatementData>(url);
  }

  /**
   * Generate Trial Balance report
   */
  public async getTrialBalance(
    companyId: string,
    filters?: ReportFilters
  ): Promise<TrialBalanceData> {
    const params = new URLSearchParams();

    if (filters?.asOfDate) {
      params.append("asOfDate", filters.asOfDate);
    }

    const queryString = params.toString();
    const url = `/reports/${companyId}/trial-balance${
      queryString ? `?${queryString}` : ""
    }`;

    return await apiService.get<TrialBalanceData>(url);
  }

  /**
   * Generate Aging Report
   */
  public async getAgingReport(
    companyId: string,
    filters?: ReportFilters
  ): Promise<AgingReportData> {
    const params = new URLSearchParams();

    if (filters?.reportType) {
      params.append("reportType", filters.reportType);
    }

    if (filters?.asOfDate) {
      params.append("asOfDate", filters.asOfDate);
    }

    const queryString = params.toString();
    const url = `/reports/${companyId}/aging-report${
      queryString ? `?${queryString}` : ""
    }`;

    return await apiService.get<AgingReportData>(url);
  }

  /**
   * Generate Cash Flow Statement
   */
  public async getCashFlow(
    companyId: string,
    filters?: ReportFilters
  ): Promise<CashFlowData> {
    const params = new URLSearchParams();

    if (filters?.startDate) {
      params.append("startDate", filters.startDate);
    }

    if (filters?.endDate) {
      params.append("endDate", filters.endDate);
    }

    const queryString = params.toString();
    const url = `/reports/${companyId}/cash-flow${
      queryString ? `?${queryString}` : ""
    }`;

    return await apiService.get<CashFlowData>(url);
  }

  /**
   * Format currency amount
   */
  public formatCurrency(amount: number, currency: string = "USD"): string {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount || 0);
  }

  /**
   * Format percentage
   */
  public formatPercentage(value: number, decimals: number = 1): string {
    return `${value.toFixed(decimals)}%`;
  }

  /**
   * Format date
   */
  public formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return "Invalid Date";
      }
      return new Intl.DateTimeFormat("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      }).format(date);
    } catch (error) {
      console.error("Date formatting error:", error);
      return "Invalid Date";
    }
  }

  /**
   * Get account type display name
   */
  public getAccountTypeDisplayName(accountType: string): string {
    const typeMap: Record<string, string> = {
      ASSET: "Assets",
      LIABILITY: "Liabilities",
      EQUITY: "Equity",
      REVENUE: "Revenue",
      EXPENSE: "Expenses",
    };
    return typeMap[accountType] || accountType;
  }

  /**
   * Get account subtype display name
   */
  public getAccountSubtypeDisplayName(subtype: string): string {
    const subtypeMap: Record<string, string> = {
      CURRENT_ASSET: "Current Assets",
      NON_CURRENT_ASSET: "Non-Current Assets",
      CURRENT_LIABILITY: "Current Liabilities",
      NON_CURRENT_LIABILITY: "Non-Current Liabilities",
      OWNER_EQUITY: "Owner's Equity",
      RETAINED_EARNINGS: "Retained Earnings",
      OPERATING_REVENUE: "Operating Revenue",
      NON_OPERATING_REVENUE: "Non-Operating Revenue",
      OPERATING_EXPENSE: "Operating Expenses",
      NON_OPERATING_EXPENSE: "Non-Operating Expenses",
      COST_OF_GOODS_SOLD: "Cost of Goods Sold",
    };
    return subtypeMap[subtype] || subtype.replace(/_/g, " ");
  }

  /**
   * Get aging bucket display name
   */
  public getAgingBucketDisplayName(bucket: string): string {
    const bucketMap: Record<string, string> = {
      current: "Current",
      "1-30": "1-30 Days",
      "31-60": "31-60 Days",
      "61-90": "61-90 Days",
      over90: "Over 90 Days",
    };
    return bucketMap[bucket] || bucket;
  }

  /**
   * Get aging bucket color for styling
   */
  public getAgingBucketColor(bucket: string): string {
    const colorMap: Record<string, string> = {
      current: "text-green-600",
      "1-30": "text-yellow-600",
      "31-60": "text-orange-600",
      "61-90": "text-red-600",
      over90: "text-red-800",
    };
    return colorMap[bucket] || "text-gray-600";
  }

  /**
   * Calculate percentage of total
   */
  public calculatePercentage(amount: number, total: number): number {
    if (total === 0) return 0;
    return (amount / total) * 100;
  }

  /**
   * Get report period display
   */
  public getReportPeriodDisplay(startDate: string, endDate: string): string {
    const start = this.formatDate(startDate);
    const end = this.formatDate(endDate);
    return `${start} to ${end}`;
  }

  /**
   * Get common date ranges
   */
  public getDateRangeOptions(): Array<{
    value: string;
    label: string;
    startDate: string;
    endDate: string;
  }> {
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();

    return [
      {
        value: "current_month",
        label: "Current Month",
        startDate: new Date(currentYear, currentMonth, 1)
          .toISOString()
          .split("T")[0],
        endDate: today.toISOString().split("T")[0],
      },
      {
        value: "last_month",
        label: "Last Month",
        startDate: new Date(currentYear, currentMonth - 1, 1)
          .toISOString()
          .split("T")[0],
        endDate: new Date(currentYear, currentMonth, 0)
          .toISOString()
          .split("T")[0],
      },
      {
        value: "current_quarter",
        label: "Current Quarter",
        startDate: new Date(currentYear, Math.floor(currentMonth / 3) * 3, 1)
          .toISOString()
          .split("T")[0],
        endDate: today.toISOString().split("T")[0],
      },
      {
        value: "current_year",
        label: "Current Year",
        startDate: new Date(currentYear, 0, 1).toISOString().split("T")[0],
        endDate: today.toISOString().split("T")[0],
      },
      {
        value: "last_year",
        label: "Last Year",
        startDate: new Date(currentYear - 1, 0, 1).toISOString().split("T")[0],
        endDate: new Date(currentYear - 1, 11, 31).toISOString().split("T")[0],
      },
    ];
  }

  /**
   * Export report data to CSV
   */
  public exportToCSV(data: any[], filename: string): void {
    if (!data.length) return;

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(","),
      ...data.map((row) =>
        headers
          .map((header) => {
            const value = row[header];
            // Escape commas and quotes in CSV
            if (
              typeof value === "string" &&
              (value.includes(",") || value.includes('"'))
            ) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
          })
          .join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `${filename}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * Print report
   */
  public printReport(): void {
    window.print();
  }

  /**
   * Get report statistics
   */
  public async getReportStats(companyId: string): Promise<{
    customReports: number;
    scheduledReports: number;
    reportsThisMonth: number;
    avgGenerationTime: string;
  }> {
    try {
      const response = await apiService.get(`/reports/${companyId}/stats`);
      return response as any;
    } catch (error) {
      console.error("Failed to fetch report stats:", error);
      // Return fallback stats
      return {
        customReports: 12,
        scheduledReports: 5,
        reportsThisMonth: 24,
        avgGenerationTime: "2.3s",
      };
    }
  }

  /**
   * Create a custom report
   */
  public async createCustomReport(
    companyId: string,
    reportData: CreateCustomReportRequest
  ): Promise<CustomReport> {
    try {
      const response = await apiService.post(
        `/reports/${companyId}/custom`,
        reportData
      );
      return response as CustomReport;
    } catch (error) {
      console.error("Failed to create custom report:", error);
      throw new Error("Failed to create custom report. Please try again.");
    }
  }

  /**
   * Get all custom reports for a company
   */
  public async getCustomReports(companyId: string): Promise<CustomReport[]> {
    try {
      const response = await apiService.get(`/custom-reports/${companyId}`);
      // The backend returns { success: true, data: [...] }
      return response.data || (response as CustomReport[]);
    } catch (error) {
      console.error("Failed to fetch custom reports:", error);
      throw new Error("Failed to load custom reports. Please try again.");
    }
  }

  /**
   * Get a specific custom report
   */
  public async getCustomReport(
    companyId: string,
    reportId: string
  ): Promise<CustomReport> {
    try {
      const response = await apiService.get(
        `/reports/${companyId}/custom/${reportId}`
      );
      return response as CustomReport;
    } catch (error) {
      console.error("Failed to fetch custom report:", error);
      throw new Error("Failed to load custom report. Please try again.");
    }
  }

  /**
   * Update a custom report
   */
  public async updateCustomReport(
    companyId: string,
    reportId: string,
    reportData: Partial<CreateCustomReportRequest>
  ): Promise<CustomReport> {
    try {
      const response = await apiService.put(
        `/reports/${companyId}/custom/${reportId}`,
        reportData
      );
      return response as CustomReport;
    } catch (error) {
      console.error("Failed to update custom report:", error);
      throw new Error("Failed to update custom report. Please try again.");
    }
  }

  /**
   * Delete a custom report
   */
  public async deleteCustomReport(
    companyId: string,
    reportId: string
  ): Promise<void> {
    try {
      await apiService.delete(`/reports/${companyId}/custom/${reportId}`);
    } catch (error) {
      console.error("Failed to delete custom report:", error);
      throw new Error("Failed to delete custom report. Please try again.");
    }
  }

  /**
   * Run a custom report
   */
  public async runCustomReport(
    companyId: string,
    reportId: string
  ): Promise<any> {
    try {
      const response = await apiService.post(
        `/reports/${companyId}/custom/${reportId}/run`
      );
      return response;
    } catch (error) {
      console.error("Failed to run custom report:", error);
      throw new Error("Failed to run custom report. Please try again.");
    }
  }

  /**
   * Schedule a custom report
   */
  public async scheduleCustomReport(
    companyId: string,
    reportId: string,
    frequency: string,
    deliveryMethod: string,
    recipientEmail?: string
  ): Promise<any> {
    try {
      const response = await apiService.post(
        `/reports/${companyId}/custom/${reportId}/schedule`,
        {
          frequency,
          deliveryMethod,
          recipientEmail,
        }
      );
      return response;
    } catch (error) {
      console.error("Failed to schedule custom report:", error);
      throw new Error("Failed to schedule custom report. Please try again.");
    }
  }

  /**
   * Get results of a custom report
   */
  public async getCustomReportResults(
    companyId: string,
    reportId: string
  ): Promise<any> {
    try {
      const response = await apiService.get(
        `/reports/${companyId}/custom/${reportId}/results`
      );
      return response;
    } catch (error) {
      console.error("Failed to fetch custom report results:", error);
      throw new Error(
        "Failed to load custom report results. Please try again."
      );
    }
  }
}

export const reportService = new ReportService();
