import { apiService } from "./api";
import type { Contact, ContactType } from "../types";

export interface ContactFilters {
  contactType?: ContactType;
  isActive?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

export interface ContactListResponse {
  data: Contact[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface CreateContactData {
  companyId: string;
  contactType: ContactType;
  name: string;
  displayName?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  website?: string;
  taxId?: string;
  billingAddress?: string;
  billingCity?: string;
  billingState?: string;
  billingPostalCode?: string;
  billingCountry?: string;
  shippingAddress?: string;
  shippingCity?: string;
  shippingState?: string;
  shippingPostalCode?: string;
  shippingCountry?: string;
  currency?: string;
  paymentTerms?: string;
  creditLimit?: number;
  customFields?: Record<string, any>;
  notes?: string;
}

export interface UpdateContactData
  extends Partial<Omit<CreateContactData, "companyId">> {}

class ContactService {
  /**
   * Get all contacts for a company
   */
  public async getContacts(
    companyId: string,
    filters?: ContactFilters
  ): Promise<ContactListResponse> {
    const params = new URLSearchParams();

    if (filters?.contactType) {
      params.append("contactType", filters.contactType);
    }

    if (filters?.isActive !== undefined) {
      params.append("isActive", filters.isActive.toString());
    }

    if (filters?.search) {
      params.append("search", filters.search);
    }

    if (filters?.page) {
      params.append("page", filters.page.toString());
    }

    if (filters?.limit) {
      params.append("limit", filters.limit.toString());
    }

    const queryString = params.toString();
    const url = `/contacts/${companyId}${queryString ? `?${queryString}` : ""}`;

    return await apiService.get<ContactListResponse>(url);
  }

  /**
   * Get a single contact by ID
   */
  public async getContact(
    companyId: string,
    contactId: string
  ): Promise<Contact> {
    return await apiService.get<Contact>(`/contacts/${companyId}/${contactId}`);
  }

  /**
   * Create a new contact
   */
  public async createContact(data: CreateContactData): Promise<Contact> {
    return await apiService.post<Contact>("/contacts", data);
  }

  /**
   * Update an existing contact
   */
  public async updateContact(
    companyId: string,
    contactId: string,
    data: UpdateContactData
  ): Promise<Contact> {
    return await apiService.put<Contact>(
      `/contacts/${companyId}/${contactId}`,
      data
    );
  }

  /**
   * Delete a contact
   */
  public async deleteContact(
    companyId: string,
    contactId: string
  ): Promise<void> {
    return await apiService.delete<void>(`/contacts/${companyId}/${contactId}`);
  }

  /**
   * Get contact type display name
   */
  public getContactTypeDisplayName(type: ContactType): string {
    const typeMap: Record<ContactType, string> = {
      CUSTOMER: "Customer",
      VENDOR: "Vendor",
      EMPLOYEE: "Employee",
      OTHER: "Other",
    };
    return typeMap[type] || type;
  }

  /**
   * Get contact type color for badges
   */
  public getContactTypeColor(type: ContactType): string {
    const colorMap: Record<ContactType, string> = {
      CUSTOMER: "bg-green-100 text-green-800",
      VENDOR: "bg-blue-100 text-blue-800",
      EMPLOYEE: "bg-purple-100 text-purple-800",
      OTHER: "bg-gray-100 text-gray-800",
    };
    return colorMap[type] || "bg-gray-100 text-gray-800";
  }

  /**
   * Format contact display name
   */
  public getDisplayName(contact: Contact): string {
    return contact.displayName || contact.name;
  }

  /**
   * Get contact's full address
   */
  public getFullAddress(
    contact: Contact,
    type: "billing" | "shipping" = "billing"
  ): string {
    const prefix = type === "billing" ? "billing" : "shipping";
    const address = contact[`${prefix}Address` as keyof Contact] as string;
    const city = contact[`${prefix}City` as keyof Contact] as string;
    const state = contact[`${prefix}State` as keyof Contact] as string;
    const postalCode = contact[
      `${prefix}PostalCode` as keyof Contact
    ] as string;
    const country = contact[`${prefix}Country` as keyof Contact] as string;

    const parts = [address, city, state, postalCode, country].filter(Boolean);
    return parts.join(", ");
  }

  /**
   * Validate email format
   */
  public isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone number format
   */
  public isValidPhone(phone: string): boolean {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ""));
  }

  /**
   * Format currency amount
   */
  public formatCurrency(amount: number, currency: string = "USD"): string {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount || 0);
  }

  /**
   * Get payment terms options
   */
  public getPaymentTermsOptions(): Array<{ value: string; label: string }> {
    return [
      { value: "NET_15", label: "Net 15 days" },
      { value: "NET_30", label: "Net 30 days" },
      { value: "NET_45", label: "Net 45 days" },
      { value: "NET_60", label: "Net 60 days" },
      { value: "NET_90", label: "Net 90 days" },
      { value: "COD", label: "Cash on Delivery" },
      { value: "PREPAID", label: "Prepaid" },
      { value: "DUE_ON_RECEIPT", label: "Due on Receipt" },
    ];
  }

  /**
   * Get currency options
   */
  public getCurrencyOptions(): Array<{ value: string; label: string }> {
    return [
      { value: "USD", label: "US Dollar (USD)" },
      { value: "EUR", label: "Euro (EUR)" },
      { value: "GBP", label: "British Pound (GBP)" },
      { value: "CAD", label: "Canadian Dollar (CAD)" },
      { value: "AUD", label: "Australian Dollar (AUD)" },
      { value: "JPY", label: "Japanese Yen (JPY)" },
      { value: "CHF", label: "Swiss Franc (CHF)" },
      { value: "CNY", label: "Chinese Yuan (CNY)" },
    ];
  }

  /**
   * Get country options
   */
  public getCountryOptions(): Array<{ value: string; label: string }> {
    return [
      { value: "US", label: "United States" },
      { value: "CA", label: "Canada" },
      { value: "GB", label: "United Kingdom" },
      { value: "DE", label: "Germany" },
      { value: "FR", label: "France" },
      { value: "IT", label: "Italy" },
      { value: "ES", label: "Spain" },
      { value: "AU", label: "Australia" },
      { value: "JP", label: "Japan" },
      { value: "CN", label: "China" },
      { value: "IN", label: "India" },
      { value: "BR", label: "Brazil" },
      { value: "MX", label: "Mexico" },
    ];
  }
}

export const contactService = new ContactService();
