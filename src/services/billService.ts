import api from './api';
import type {
  Bill,
  CreateBillRequest,
  UpdateBillRequest,
  BillFilters,
  BillListResponse,
  UpdateBillStatusRequest,
  PayBillRequest,
} from '../types/bill';

class BillService {
  private baseUrl = '/bills';

  /**
   * Get all bills for a company
   */
  async getBills(
    companyId: string,
    filters?: BillFilters
  ): Promise<BillListResponse> {
    const params = new URLSearchParams();
    
    if (filters?.status) params.append('status', filters.status);
    if (filters?.vendorId) params.append('vendorId', filters.vendorId);
    if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters?.dateTo) params.append('dateTo', filters.dateTo);
    if (filters?.dueDateFrom) params.append('dueDateFrom', filters.dueDateFrom);
    if (filters?.dueDateTo) params.append('dueDateTo', filters.dueDateTo);
    if (filters?.search) params.append('search', filters.search);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());

    const queryString = params.toString();
    const url = `${this.baseUrl}/${companyId}${queryString ? `?${queryString}` : ''}`;
    
    const response = await api.get(url);
    return response.data;
  }

  /**
   * Get a single bill by ID
   */
  async getBill(companyId: string, billId: string): Promise<Bill> {
    const response = await api.get(`${this.baseUrl}/${companyId}/${billId}`);
    return response.data;
  }

  /**
   * Create a new bill
   */
  async createBill(data: CreateBillRequest): Promise<Bill> {
    const response = await api.post(this.baseUrl, data);
    return response.data;
  }

  /**
   * Update an existing bill
   */
  async updateBill(
    companyId: string,
    billId: string,
    data: UpdateBillRequest
  ): Promise<Bill> {
    const response = await api.put(`${this.baseUrl}/${companyId}/${billId}`, data);
    return response.data;
  }

  /**
   * Update bill status
   */
  async updateBillStatus(
    companyId: string,
    billId: string,
    data: UpdateBillStatusRequest
  ): Promise<{ message: string; status: string }> {
    const response = await api.post(`${this.baseUrl}/${companyId}/${billId}/status`, data);
    return response.data;
  }

  /**
   * Record payment for a bill
   */
  async payBill(
    companyId: string,
    billId: string,
    data: PayBillRequest
  ): Promise<{ message: string; newStatus: string; paidAmount: number; balanceDue: number }> {
    const response = await api.post(`${this.baseUrl}/${companyId}/${billId}/pay`, data);
    return response.data;
  }

  /**
   * Delete a bill
   */
  async deleteBill(companyId: string, billId: string): Promise<{ message: string }> {
    const response = await api.delete(`${this.baseUrl}/${companyId}/${billId}`);
    return response.data;
  }

  /**
   * Approve a bill
   */
  async approveBill(companyId: string, billId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updateBillStatus(companyId, billId, {
      status: 'APPROVED',
      notes: notes || 'Bill approved',
    });
  }

  /**
   * Cancel a bill
   */
  async cancelBill(companyId: string, billId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updateBillStatus(companyId, billId, {
      status: 'CANCELLED',
      notes: notes || 'Bill cancelled',
    });
  }

  /**
   * Submit bill for approval
   */
  async submitForApproval(companyId: string, billId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updateBillStatus(companyId, billId, {
      status: 'PENDING_APPROVAL',
      notes: notes || 'Bill submitted for approval',
    });
  }

  /**
   * Format currency amount
   */
  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }

  /**
   * Format date
   */
  formatDate(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  /**
   * Calculate line item total
   */
  calculateLineTotal(quantity: number, unitCost: number, discountPercentage: number = 0): number {
    const subtotal = quantity * unitCost;
    const discountAmount = (subtotal * discountPercentage) / 100;
    return subtotal - discountAmount;
  }

  /**
   * Calculate line item tax
   */
  calculateLineTax(lineTotal: number, taxRate: number = 0): number {
    return (lineTotal * taxRate) / 100;
  }

  /**
   * Calculate bill totals
   */
  calculateTotals(lineItems: Array<{
    quantity: number;
    unitCost: number;
    discountPercentage?: number;
    taxRate?: number;
  }>): {
    subtotal: number;
    totalDiscount: number;
    totalTax: number;
    total: number;
  } {
    let subtotal = 0;
    let totalDiscount = 0;
    let totalTax = 0;

    lineItems.forEach(item => {
      const lineSubtotal = item.quantity * item.unitCost;
      const discountAmount = (lineSubtotal * (item.discountPercentage || 0)) / 100;
      const lineTotal = lineSubtotal - discountAmount;
      const taxAmount = (lineTotal * (item.taxRate || 0)) / 100;

      subtotal += lineSubtotal;
      totalDiscount += discountAmount;
      totalTax += taxAmount;
    });

    const total = subtotal - totalDiscount + totalTax;

    return {
      subtotal,
      totalDiscount,
      totalTax,
      total,
    };
  }

  /**
   * Generate bill number (client-side preview)
   */
  generateBillNumber(lastNumber: number = 0): string {
    return `BILL-${String(lastNumber + 1).padStart(6, '0')}`;
  }

  /**
   * Validate bill data
   */
  validateBill(data: CreateBillRequest | UpdateBillRequest): string[] {
    const errors: string[] = [];

    if ('vendorId' in data && !data.vendorId) {
      errors.push('Vendor is required');
    }

    if ('billDate' in data && !data.billDate) {
      errors.push('Bill date is required');
    }

    if ('lineItems' in data && data.lineItems) {
      if (data.lineItems.length === 0) {
        errors.push('At least one line item is required');
      }

      data.lineItems.forEach((item, index) => {
        if (!item.description?.trim()) {
          errors.push(`Line item ${index + 1}: Description is required`);
        }
        if (!item.quantity || item.quantity <= 0) {
          errors.push(`Line item ${index + 1}: Quantity must be greater than 0`);
        }
        if (!item.unitCost || item.unitCost <= 0) {
          errors.push(`Line item ${index + 1}: Unit cost must be greater than 0`);
        }
      });
    }

    return errors;
  }

  /**
   * Calculate payment percentage
   */
  getPaymentPercentage(bill: Bill): number {
    if (bill.totalAmount === 0) return 0;
    return (bill.paidAmount / bill.totalAmount) * 100;
  }

  /**
   * Get aging category for bill
   */
  getAgingCategory(bill: Bill): string {
    if (bill.status === 'PAID') return 'Paid';
    if (!bill.dueDate) return 'No Due Date';
    
    const dueDate = new Date(bill.dueDate);
    const today = new Date();
    const diffDays = Math.ceil((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays <= 0) return 'Current';
    if (diffDays <= 30) return '1-30 Days';
    if (diffDays <= 60) return '31-60 Days';
    if (diffDays <= 90) return '61-90 Days';
    return '90+ Days';
  }

  /**
   * Get payment terms options
   */
  getPaymentTermsOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'NET_15', label: 'Net 15' },
      { value: 'NET_30', label: 'Net 30' },
      { value: 'NET_45', label: 'Net 45' },
      { value: 'NET_60', label: 'Net 60' },
      { value: 'NET_90', label: 'Net 90' },
      { value: 'DUE_ON_RECEIPT', label: 'Due on Receipt' },
      { value: '2_10_NET_30', label: '2/10 Net 30' },
      { value: 'CUSTOM', label: 'Custom' },
    ];
  }

  /**
   * Calculate due date based on payment terms
   */
  calculateDueDate(billDate: string, paymentTerms: string): string | null {
    const date = new Date(billDate);
    
    switch (paymentTerms) {
      case 'NET_15':
        date.setDate(date.getDate() + 15);
        break;
      case 'NET_30':
        date.setDate(date.getDate() + 30);
        break;
      case 'NET_45':
        date.setDate(date.getDate() + 45);
        break;
      case 'NET_60':
        date.setDate(date.getDate() + 60);
        break;
      case 'NET_90':
        date.setDate(date.getDate() + 90);
        break;
      case 'DUE_ON_RECEIPT':
        return billDate;
      case '2_10_NET_30':
        date.setDate(date.getDate() + 30);
        break;
      default:
        return null;
    }
    
    return date.toISOString().split('T')[0];
  }
}

export default new BillService();
