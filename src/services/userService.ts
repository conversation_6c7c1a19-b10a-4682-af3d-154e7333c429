import { apiService as api } from "./api";
import type { User, Role } from "../types";

export interface CreateUserRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  roleId: string;
  password: string;
  isActive: boolean;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  roleId?: string;
  password?: string;
  isActive?: boolean;
}

export interface CreateRoleRequest {
  name: string;
  description?: string;
  permissions: string[];
  isActive: boolean;
}

export interface UpdateRoleRequest {
  name?: string;
  description?: string;
  permissions?: string[];
  isActive?: boolean;
}

export interface InviteUserRequest {
  email: string;
  firstName: string;
  lastName: string;
  roleId: string;
  companyId: string;
}

export interface UserFilters {
  companyId?: string;
  search?: string;
  roleId?: string;
  isActive?: boolean;
  page?: number;
  limit?: number;
}

export interface UsersResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
}

class UserService {
  /**
   * Get users with optional filtering
   */
  async getUsers(filters: UserFilters = {}): Promise<UsersResponse> {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const response = await api.get(`/users?${params.toString()}`);
    return {
      users: (response as any).data || (response as any).users || [],
      total: (response as any).total || 0,
      page: (response as any).page || 1,
      limit: (response as any).limit || 10,
    };
  }

  /**
   * Get a specific user by ID
   */
  async getUser(userId: string): Promise<User> {
    const response = await api.get(`/users/${userId}`);
    return (response as any).data;
  }

  /**
   * Create a new user
   */
  async createUser(userData: CreateUserRequest): Promise<User> {
    const response = await api.post("/users", userData);
    return (response as any).data;
  }

  /**
   * Update an existing user
   */
  async updateUser(userId: string, userData: UpdateUserRequest): Promise<User> {
    const response = await api.put(`/users/${userId}`, userData);
    return (response as any).data;
  }

  /**
   * Delete a user
   */
  async deleteUser(userId: string): Promise<void> {
    await api.delete(`/users/${userId}`);
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<User> {
    const response = await api.get("/users/me");
    return (response as any).data;
  }

  /**
   * Update current user profile
   */
  async updateCurrentUser(userData: UpdateUserRequest): Promise<User> {
    const response = await api.put("/users/me", userData);
    return (response as any).data;
  }

  /**
   * Get all available roles
   */
  async getRoles(): Promise<Role[]> {
    const response = await api.get("/roles");
    return response as Role[];
  }

  /**
   * Get a specific role by ID
   */
  async getRole(roleId: string): Promise<Role> {
    const response = await api.get(`/roles/${roleId}`);
    return (response as any).data;
  }

  /**
   * Create a new role
   */
  async createRole(roleData: CreateRoleRequest): Promise<Role> {
    const response = await api.post("/roles", roleData);
    return (response as any).data;
  }

  /**
   * Update an existing role
   */
  async updateRole(roleId: string, roleData: UpdateRoleRequest): Promise<Role> {
    const response = await api.put(`/roles/${roleId}`, roleData);
    return (response as any).data;
  }

  /**
   * Delete a role
   */
  async deleteRole(roleId: string): Promise<void> {
    await api.delete(`/roles/${roleId}`);
  }

  /**
   * Invite a user to join the system
   */
  async inviteUser(invitationData: InviteUserRequest): Promise<void> {
    await api.post("/users/invite", invitationData);
  }

  /**
   * Resend invitation to a user
   */
  async resendInvitation(invitationId: string): Promise<void> {
    await api.post(`/users/invitations/${invitationId}/resend`);
  }

  /**
   * Get pending invitations
   */
  async getInvitations(companyId?: string): Promise<any[]> {
    const params = companyId ? `?companyId=${companyId}` : "";
    const response = await api.get(`/users/invitations${params}`);
    return (response as any).data;
  }

  /**
   * Cancel an invitation
   */
  async cancelInvitation(invitationId: string): Promise<void> {
    await api.delete(`/users/invitations/${invitationId}`);
  }

  /**
   * Assign user to company with role
   */
  async assignUserToCompany(
    userId: string,
    companyId: string,
    roleId: string
  ): Promise<void> {
    await api.post(`/users/${userId}/companies`, {
      companyId,
      roleId,
    });
  }

  /**
   * Remove user from company
   */
  async removeUserFromCompany(
    userId: string,
    companyId: string
  ): Promise<void> {
    await api.delete(`/users/${userId}/companies/${companyId}`);
  }

  /**
   * Update user's role in a company
   */
  async updateUserCompanyRole(
    userId: string,
    companyId: string,
    roleId: string
  ): Promise<void> {
    await api.put(`/users/${userId}/companies/${companyId}`, {
      roleId,
    });
  }

  /**
   * Activate/deactivate user
   */
  async toggleUserStatus(userId: string, isActive: boolean): Promise<User> {
    const response = await api.patch(`/users/${userId}/status`, { isActive });
    return (response as any).data;
  }

  /**
   * Reset user password (admin action)
   */
  async resetUserPassword(
    userId: string
  ): Promise<{ temporaryPassword: string }> {
    const response = await api.post(`/users/${userId}/reset-password`);
    return (response as any).data;
  }

  /**
   * Get user activity logs
   */
  async getUserActivity(userId: string, limit = 50): Promise<any[]> {
    const response = await api.get(`/users/${userId}/activity?limit=${limit}`);
    return (response as any).data;
  }

  /**
   * Get user permissions for current company
   */
  async getUserPermissions(
    userId: string,
    companyId: string
  ): Promise<string[]> {
    const response = await api.get(
      `/users/${userId}/permissions?companyId=${companyId}`
    );
    return (response as any).data.permissions;
  }

  /**
   * Check if user has specific permission
   */
  async hasPermission(
    permission: string,
    companyId?: string
  ): Promise<boolean> {
    try {
      const params = companyId ? `?companyId=${companyId}` : "";
      const response = await api.get(
        `/users/me/permissions/check/${permission}${params}`
      );
      return (response as any).data.hasPermission;
    } catch (error) {
      return false;
    }
  }

  /**
   * Bulk operations
   */
  async bulkUpdateUsers(
    userIds: string[],
    updates: Partial<UpdateUserRequest>
  ): Promise<void> {
    await api.patch("/users/bulk", {
      userIds,
      updates,
    });
  }

  async bulkDeleteUsers(userIds: string[]): Promise<void> {
    await api.delete("/users/bulk", {
      data: { userIds },
    });
  }

  /**
   * Export users data
   */
  async exportUsers(
    format: "csv" | "excel" = "csv",
    filters: UserFilters = {}
  ): Promise<Blob> {
    const params = new URLSearchParams();
    params.append("format", format);

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const response = await api.get(`/users/export?${params.toString()}`, {
      responseType: "blob",
    });
    return (response as any).data;
  }

  /**
   * Import users from file
   */
  async importUsers(file: File): Promise<{ success: number; errors: any[] }> {
    const formData = new FormData();
    formData.append("file", file);

    const response = await api.post("/users/import", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return (response as any).data;
  }
}

export const userService = new UserService();
