import { apiService } from './api';
import type { Invoice, InvoiceType, InvoiceStatus, InvoiceLineItem } from '../types';

export interface InvoiceFilters {
  invoiceType?: InvoiceType;
  status?: InvoiceStatus;
  contactId?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  page?: number;
  limit?: number;
}

export interface InvoiceListResponse {
  data: Invoice[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface CreateInvoiceData {
  companyId: string;
  contactId: string;
  invoiceType: InvoiceType;
  invoiceDate: string;
  dueDate: string;
  currency?: string;
  exchangeRate?: number;
  termsAndConditions?: string;
  notes?: string;
  lineItems: Array<{
    itemCode?: string;
    description: string;
    quantity: number;
    unit?: string;
    unitPrice: number;
    discountPercent?: number;
    accountId: string;
    taxDetails?: Record<string, any>;
  }>;
}

export interface UpdateInvoiceData extends Partial<Omit<CreateInvoiceData, 'companyId'>> {}

class InvoiceService {
  /**
   * Get all invoices for a company
   */
  public async getInvoices(companyId: string, filters?: InvoiceFilters): Promise<InvoiceListResponse> {
    const params = new URLSearchParams();
    
    if (filters?.invoiceType) {
      params.append('invoiceType', filters.invoiceType);
    }
    
    if (filters?.status) {
      params.append('status', filters.status);
    }
    
    if (filters?.contactId) {
      params.append('contactId', filters.contactId);
    }
    
    if (filters?.search) {
      params.append('search', filters.search);
    }

    if (filters?.dateFrom) {
      params.append('dateFrom', filters.dateFrom);
    }

    if (filters?.dateTo) {
      params.append('dateTo', filters.dateTo);
    }

    if (filters?.page) {
      params.append('page', filters.page.toString());
    }

    if (filters?.limit) {
      params.append('limit', filters.limit.toString());
    }

    const queryString = params.toString();
    const url = `/invoices/${companyId}${queryString ? `?${queryString}` : ''}`;
    
    return await apiService.get<InvoiceListResponse>(url);
  }

  /**
   * Get a single invoice by ID
   */
  public async getInvoice(companyId: string, invoiceId: string): Promise<Invoice> {
    return await apiService.get<Invoice>(`/invoices/${companyId}/${invoiceId}`);
  }

  /**
   * Create a new invoice
   */
  public async createInvoice(data: CreateInvoiceData): Promise<Invoice> {
    return await apiService.post<Invoice>('/invoices', data);
  }

  /**
   * Update an existing invoice
   */
  public async updateInvoice(
    companyId: string,
    invoiceId: string,
    data: UpdateInvoiceData
  ): Promise<Invoice> {
    return await apiService.put<Invoice>(`/invoices/${companyId}/${invoiceId}`, data);
  }

  /**
   * Delete an invoice
   */
  public async deleteInvoice(companyId: string, invoiceId: string): Promise<void> {
    return await apiService.delete<void>(`/invoices/${companyId}/${invoiceId}`);
  }

  /**
   * Send an invoice
   */
  public async sendInvoice(companyId: string, invoiceId: string): Promise<Invoice> {
    return await apiService.post<Invoice>(`/invoices/${companyId}/${invoiceId}/send`, {});
  }

  /**
   * Mark invoice as paid
   */
  public async markInvoicePaid(
    companyId: string,
    invoiceId: string,
    paidAmount?: number,
    paymentDate?: string
  ): Promise<Invoice> {
    return await apiService.post<Invoice>(`/invoices/${companyId}/${invoiceId}/mark-paid`, {
      paidAmount,
      paymentDate,
    });
  }

  /**
   * Get invoice type display name
   */
  public getInvoiceTypeDisplayName(type: InvoiceType): string {
    const typeMap: Record<InvoiceType, string> = {
      SALES: 'Sales Invoice',
      PURCHASE: 'Purchase Invoice',
    };
    return typeMap[type] || type;
  }

  /**
   * Get invoice status display name
   */
  public getInvoiceStatusDisplayName(status: InvoiceStatus): string {
    const statusMap: Record<InvoiceStatus, string> = {
      DRAFT: 'Draft',
      SENT: 'Sent',
      PAID: 'Paid',
      OVERDUE: 'Overdue',
      CANCELLED: 'Cancelled',
    };
    return statusMap[status] || status;
  }

  /**
   * Get invoice status color for badges
   */
  public getInvoiceStatusColor(status: InvoiceStatus): string {
    const colorMap: Record<InvoiceStatus, string> = {
      DRAFT: 'bg-gray-100 text-gray-800',
      SENT: 'bg-blue-100 text-blue-800',
      PAID: 'bg-green-100 text-green-800',
      OVERDUE: 'bg-red-100 text-red-800',
      CANCELLED: 'bg-red-100 text-red-800',
    };
    return colorMap[status] || 'bg-gray-100 text-gray-800';
  }

  /**
   * Get invoice type color for badges
   */
  public getInvoiceTypeColor(type: InvoiceType): string {
    const colorMap: Record<InvoiceType, string> = {
      SALES: 'bg-green-100 text-green-800',
      PURCHASE: 'bg-blue-100 text-blue-800',
    };
    return colorMap[type] || 'bg-gray-100 text-gray-800';
  }

  /**
   * Calculate line item total
   */
  public calculateLineTotal(quantity: number, unitPrice: number, discountPercent: number = 0): number {
    const subtotal = quantity * unitPrice;
    const discountAmount = (subtotal * discountPercent) / 100;
    return subtotal - discountAmount;
  }

  /**
   * Calculate invoice totals
   */
  public calculateInvoiceTotals(lineItems: InvoiceLineItem[]): {
    subtotal: number;
    discountAmount: number;
    taxAmount: number;
    totalAmount: number;
  } {
    let subtotal = 0;
    let discountAmount = 0;
    let taxAmount = 0;

    lineItems.forEach(item => {
      const lineSubtotal = item.quantity * item.unitPrice;
      subtotal += lineSubtotal;
      discountAmount += item.discountAmount || 0;
      // Tax calculation would go here based on taxDetails
    });

    const totalAmount = subtotal - discountAmount + taxAmount;

    return {
      subtotal,
      discountAmount,
      taxAmount,
      totalAmount,
    };
  }

  /**
   * Format currency amount
   */
  public formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount || 0);
  }

  /**
   * Format date
   */
  public formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      }).format(date);
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'Invalid Date';
    }
  }

  /**
   * Check if invoice is overdue
   */
  public isOverdue(invoice: Invoice): boolean {
    if (invoice.status === 'PAID' || invoice.status === 'CANCELLED') {
      return false;
    }
    
    const dueDate = new Date(invoice.dueDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return dueDate < today && invoice.balanceDue > 0;
  }

  /**
   * Get days overdue
   */
  public getDaysOverdue(invoice: Invoice): number {
    if (!this.isOverdue(invoice)) {
      return 0;
    }
    
    const dueDate = new Date(invoice.dueDate);
    const today = new Date();
    const diffTime = today.getTime() - dueDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  }

  /**
   * Get payment terms options
   */
  public getPaymentTermsOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'NET_15', label: 'Net 15 days' },
      { value: 'NET_30', label: 'Net 30 days' },
      { value: 'NET_45', label: 'Net 45 days' },
      { value: 'NET_60', label: 'Net 60 days' },
      { value: 'NET_90', label: 'Net 90 days' },
      { value: 'COD', label: 'Cash on Delivery' },
      { value: 'PREPAID', label: 'Prepaid' },
      { value: 'DUE_ON_RECEIPT', label: 'Due on Receipt' },
    ];
  }

  /**
   * Get unit options
   */
  public getUnitOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'EA', label: 'Each' },
      { value: 'HR', label: 'Hour' },
      { value: 'DAY', label: 'Day' },
      { value: 'WEEK', label: 'Week' },
      { value: 'MONTH', label: 'Month' },
      { value: 'KG', label: 'Kilogram' },
      { value: 'LB', label: 'Pound' },
      { value: 'M', label: 'Meter' },
      { value: 'FT', label: 'Foot' },
      { value: 'SQM', label: 'Square Meter' },
      { value: 'SQFT', label: 'Square Foot' },
    ];
  }

  /**
   * Validate invoice data
   */
  public validateInvoiceData(data: CreateInvoiceData): string[] {
    const errors: string[] = [];

    if (!data.contactId) {
      errors.push('Contact is required');
    }

    if (!data.invoiceDate) {
      errors.push('Invoice date is required');
    }

    if (!data.dueDate) {
      errors.push('Due date is required');
    }

    if (!data.lineItems || data.lineItems.length === 0) {
      errors.push('At least one line item is required');
    }

    data.lineItems?.forEach((item, index) => {
      if (!item.description) {
        errors.push(`Line item ${index + 1}: Description is required`);
      }
      if (!item.quantity || item.quantity <= 0) {
        errors.push(`Line item ${index + 1}: Quantity must be greater than 0`);
      }
      if (!item.unitPrice || item.unitPrice < 0) {
        errors.push(`Line item ${index + 1}: Unit price must be 0 or greater`);
      }
      if (!item.accountId) {
        errors.push(`Line item ${index + 1}: Account is required`);
      }
    });

    return errors;
  }
}

export const invoiceService = new InvoiceService();
