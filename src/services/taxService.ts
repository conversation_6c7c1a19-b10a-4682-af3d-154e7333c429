import { apiService as api } from "./api";
import type {
  TaxRate,
  TaxCategory,
  TaxReport,
  TaxCalculation,
  TaxSettings,
  TaxExemption,
  TaxJurisdiction,
  CreateTaxRateRequest,
  UpdateTaxRateRequest,
  CreateTaxCategoryRequest,
  UpdateTaxCategoryRequest,
  TaxCalculationRequest,
  TaxCalculationResponse,
  TaxReportFilters,
  TaxComplianceStatus,
} from "../types/tax";

class TaxService {
  // Tax Rates
  async getTaxRates(companyId: string, params?: any): Promise<TaxRate[]> {
    const queryParams = new URLSearchParams({ companyId });
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        queryParams.append(key, String(value));
      });
    }
    const response = await api.get(`/tax/rates?${queryParams.toString()}`);
    return response as TaxRate[];
  }

  async getTaxRate(taxRateId: string): Promise<TaxRate> {
    const response = await api.get(`/tax/rates/${taxRateId}`);
    return response as TaxRate;
  }

  async createTaxRate(data: CreateTaxRateRequest): Promise<TaxRate> {
    const response = await api.post("/tax/rates", data);
    return response as TaxRate;
  }

  async updateTaxRate(
    taxRateId: string,
    data: UpdateTaxRateRequest
  ): Promise<TaxRate> {
    const response = await api.put(`/tax/rates/${taxRateId}`, data);
    return response as TaxRate;
  }

  async deleteTaxRate(taxRateId: string): Promise<void> {
    await api.delete(`/tax/rates/${taxRateId}`);
  }

  async setDefaultTaxRate(companyId: string, taxRateId: string): Promise<void> {
    await api.post(`/tax/rates/${taxRateId}/set-default`, { companyId });
  }

  // Tax Categories
  async getTaxCategories(
    companyId: string,
    params?: any
  ): Promise<TaxCategory[]> {
    const queryParams = new URLSearchParams({ companyId });
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        queryParams.append(key, String(value));
      });
    }
    const response = await api.get(`/tax/categories?${queryParams.toString()}`);
    return response as TaxCategory[];
  }

  async getTaxCategory(categoryId: string): Promise<TaxCategory> {
    const response = await api.get(`/tax/categories/${categoryId}`);
    return response as TaxCategory;
  }

  async createTaxCategory(
    data: CreateTaxCategoryRequest
  ): Promise<TaxCategory> {
    const response = await api.post("/tax/categories", data);
    return response as TaxCategory;
  }

  async updateTaxCategory(
    categoryId: string,
    data: UpdateTaxCategoryRequest
  ): Promise<TaxCategory> {
    const response = await api.put(`/tax/categories/${categoryId}`, data);
    return response as TaxCategory;
  }

  async deleteTaxCategory(categoryId: string): Promise<void> {
    await api.delete(`/tax/categories/${categoryId}`);
  }

  // Tax Calculations
  async calculateTax(
    data: TaxCalculationRequest
  ): Promise<TaxCalculationResponse> {
    const response = await api.post("/tax/calculate", data);
    return (response as any).data;
  }

  async getTaxCalculations(
    transactionId?: string,
    invoiceId?: string
  ): Promise<TaxCalculation[]> {
    const params = new URLSearchParams();
    if (transactionId) params.append("transactionId", transactionId);
    if (invoiceId) params.append("invoiceId", invoiceId);

    const response = await api.get(`/tax/calculations?${params.toString()}`);
    return (response as any).data;
  }

  async recalculateTax(transactionId: string): Promise<TaxCalculation[]> {
    const response = await api.post(`/tax/recalculate/${transactionId}`);
    return (response as any).data;
  }

  // Tax Reports
  async getTaxReports(
    companyId: string,
    filters?: TaxReportFilters
  ): Promise<TaxReport[]> {
    const params = new URLSearchParams({ companyId });

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }

    const response = await api.get(`/tax/reports?${params.toString()}`);
    return response as TaxReport[];
  }

  async getTaxReport(reportId: string): Promise<TaxReport> {
    const response = await api.get(`/tax/reports/${reportId}`);
    return response as TaxReport;
  }

  async generateTaxReport(
    companyId: string,
    filters: TaxReportFilters
  ): Promise<TaxReport> {
    const response = await api.post("/tax/reports/generate", {
      companyId,
      reportType: filters.reportType,
      periodType: (filters as any).periodType,
      startDate: (filters as any).startDate,
      endDate: (filters as any).endDate,
    });
    return response as TaxReport;
  }

  async downloadTaxReport(reportId: string): Promise<void> {
    const response = await api.get(`/tax/reports/${reportId}/download`, {
      responseType: "blob",
    });

    // Create a download link
    const url = window.URL.createObjectURL(new Blob([(response as any).data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `tax-report-${reportId}.json`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  }

  async updateTaxReportStatus(
    reportId: string,
    status: string
  ): Promise<TaxReport> {
    const response = await api.patch(`/tax/reports/${reportId}/status`, {
      status,
    });
    return response as TaxReport;
  }

  async exportTaxReport(
    reportId: string,
    format: "pdf" | "csv" | "excel" = "pdf"
  ): Promise<Blob> {
    const response = await api.get(
      `/tax/reports/${reportId}/export?format=${format}`,
      {
        responseType: "blob",
      }
    );
    return (response as any).data;
  }

  // Tax Settings
  async getTaxSettings(companyId: string): Promise<TaxSettings> {
    const response = await api.get(`/tax/settings?companyId=${companyId}`);
    return response as TaxSettings;
  }

  async updateTaxSettings(
    companyId: string,
    settings: Partial<TaxSettings>
  ): Promise<TaxSettings> {
    const response = await api.put(`/tax/settings`, { companyId, ...settings });
    return response as TaxSettings;
  }

  // Tax Exemptions
  async getTaxExemptions(
    companyId: string,
    contactId?: string
  ): Promise<TaxExemption[]> {
    const params = new URLSearchParams({ companyId });
    if (contactId) params.append("contactId", contactId);

    const response = await api.get(`/tax/exemptions?${params.toString()}`);
    return (response as any).data;
  }

  async createTaxExemption(data: Partial<TaxExemption>): Promise<TaxExemption> {
    const response = await api.post("/tax/exemptions", data);
    return (response as any).data;
  }

  async updateTaxExemption(
    exemptionId: string,
    data: Partial<TaxExemption>
  ): Promise<TaxExemption> {
    const response = await api.put(`/tax/exemptions/${exemptionId}`, data);
    return (response as any).data;
  }

  async deleteTaxExemption(exemptionId: string): Promise<void> {
    await api.delete(`/tax/exemptions/${exemptionId}`);
  }

  // Tax Jurisdictions
  async getTaxJurisdictions(): Promise<TaxJurisdiction[]> {
    const response = await api.get("/tax/jurisdictions");
    return (response as any).data;
  }

  async getTaxJurisdiction(jurisdictionId: string): Promise<TaxJurisdiction> {
    const response = await api.get(`/tax/jurisdictions/${jurisdictionId}`);
    return (response as any).data;
  }

  // Tax Compliance
  async getTaxComplianceStatus(
    companyId: string
  ): Promise<TaxComplianceStatus[]> {
    const response = await api.get(`/tax/compliance?companyId=${companyId}`);
    return (response as any).data;
  }

  async checkTaxCompliance(
    companyId: string,
    jurisdiction?: string
  ): Promise<TaxComplianceStatus> {
    const params = new URLSearchParams({ companyId });
    if (jurisdiction) params.append("jurisdiction", jurisdiction);

    const response = await api.get(
      `/tax/compliance/check?${params.toString()}`
    );
    return (response as any).data;
  }

  // Utility Methods
  async validateTaxNumber(
    taxNumber: string,
    jurisdiction: string
  ): Promise<{ isValid: boolean; details?: any }> {
    const response = await api.post("/tax/validate-number", {
      taxNumber,
      jurisdiction,
    });
    return (response as any).data;
  }

  async getTaxRatesByLocation(address: string): Promise<TaxRate[]> {
    const response = await api.get(
      `/tax/rates/by-location?address=${encodeURIComponent(address)}`
    );
    return (response as any).data;
  }

  async importTaxRates(
    file: File,
    companyId: string
  ): Promise<{ success: number; errors: any[] }> {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("companyId", companyId);

    const response = await api.post("/tax/rates/import", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return (response as any).data;
  }

  async exportTaxRates(
    companyId: string,
    format: "csv" | "excel" = "csv"
  ): Promise<Blob> {
    const response = await api.get(
      `/tax/rates/export?companyId=${companyId}&format=${format}`,
      {
        responseType: "blob",
      }
    );
    return (response as any).data;
  }

  // Tax Audit Support
  async getTaxAuditTrail(
    companyId: string,
    startDate: string,
    endDate: string
  ): Promise<any[]> {
    const response = await api.get(
      `/tax/audit-trail?companyId=${companyId}&startDate=${startDate}&endDate=${endDate}`
    );
    return (response as any).data;
  }

  async generateTaxAuditReport(
    companyId: string,
    auditPeriod: string
  ): Promise<Blob> {
    const response = await api.post(
      "/tax/audit-report",
      { companyId, auditPeriod },
      {
        responseType: "blob",
      }
    );
    return (response as any).data;
  }

  // Tax Integration APIs
  async syncWithTaxAuthority(
    companyId: string,
    jurisdiction: string
  ): Promise<{ success: boolean; message: string }> {
    const response = await api.post("/tax/sync", { companyId, jurisdiction });
    return (response as any).data;
  }

  async submitTaxReturn(
    reportId: string
  ): Promise<{ success: boolean; confirmationNumber?: string }> {
    const response = await api.post(`/tax/reports/${reportId}/submit`);
    return (response as any).data;
  }
}

export const taxService = new TaxService();
