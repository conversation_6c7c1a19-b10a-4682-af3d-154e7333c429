import { apiService } from './api';
import type { Account, AccountType, AccountSubtype } from '../types';

export interface CreateAccountData {
  companyId: string;
  code: string;
  name: string;
  description?: string;
  accountType: AccountType;
  accountSubtype?: AccountSubtype;
  parentAccountId?: string;
  openingBalance?: number;
  currency?: string;
}

export interface UpdateAccountData {
  code?: string;
  name?: string;
  description?: string;
  accountType?: AccountType;
  accountSubtype?: AccountSubtype;
  parentAccountId?: string;
  openingBalance?: number;
  currency?: string;
  isActive?: boolean;
}

export interface AccountFilters {
  type?: AccountType;
  active?: boolean;
  search?: string;
}

class AccountService {
  /**
   * Get all accounts for a company
   */
  public async getAccounts(companyId: string, filters?: AccountFilters): Promise<Account[]> {
    const params = new URLSearchParams();
    
    if (filters?.type) {
      params.append('type', filters.type);
    }
    
    if (filters?.active !== undefined) {
      params.append('active', filters.active.toString());
    }
    
    if (filters?.search) {
      params.append('search', filters.search);
    }

    const queryString = params.toString();
    const url = `/accounts/${companyId}${queryString ? `?${queryString}` : ''}`;
    
    return await apiService.get<Account[]>(url);
  }

  /**
   * Get a single account by ID
   */
  public async getAccount(companyId: string, accountId: string): Promise<Account> {
    return await apiService.get<Account>(`/accounts/${companyId}/${accountId}`);
  }

  /**
   * Create a new account
   */
  public async createAccount(data: CreateAccountData): Promise<Account> {
    return await apiService.post<Account>('/accounts', data);
  }

  /**
   * Update an existing account
   */
  public async updateAccount(
    companyId: string,
    accountId: string,
    data: UpdateAccountData
  ): Promise<Account> {
    return await apiService.put<Account>(`/accounts/${companyId}/${accountId}`, data);
  }

  /**
   * Delete an account
   */
  public async deleteAccount(companyId: string, accountId: string): Promise<void> {
    return await apiService.delete<void>(`/accounts/${companyId}/${accountId}`);
  }

  /**
   * Get accounts organized in a tree structure
   */
  public async getAccountsTree(companyId: string, filters?: AccountFilters): Promise<Account[]> {
    const accounts = await this.getAccounts(companyId, filters);
    return this.buildAccountTree(accounts);
  }

  /**
   * Build a hierarchical tree structure from flat account list
   */
  private buildAccountTree(accounts: Account[]): Account[] {
    const accountMap = new Map<string, Account>();
    const rootAccounts: Account[] = [];

    // First pass: create map and initialize children arrays
    accounts.forEach(account => {
      accountMap.set(account.id, { ...account, children: [] });
    });

    // Second pass: build tree structure
    accounts.forEach(account => {
      const accountWithChildren = accountMap.get(account.id)!;
      
      if (account.parentAccountId) {
        const parent = accountMap.get(account.parentAccountId);
        if (parent) {
          parent.children!.push(accountWithChildren);
        } else {
          // Parent not found, treat as root
          rootAccounts.push(accountWithChildren);
        }
      } else {
        rootAccounts.push(accountWithChildren);
      }
    });

    // Sort accounts by type and code
    const sortAccounts = (accounts: Account[]): Account[] => {
      return accounts.sort((a, b) => {
        // First sort by account type
        const typeOrder = ['ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE'];
        const typeComparison = typeOrder.indexOf(a.accountType) - typeOrder.indexOf(b.accountType);
        
        if (typeComparison !== 0) {
          return typeComparison;
        }
        
        // Then sort by code
        return a.code.localeCompare(b.code);
      }).map(account => ({
        ...account,
        children: account.children ? sortAccounts(account.children) : []
      }));
    };

    return sortAccounts(rootAccounts);
  }

  /**
   * Get account type display name
   */
  public getAccountTypeDisplayName(type: AccountType): string {
    const typeNames: Record<AccountType, string> = {
      ASSET: 'Assets',
      LIABILITY: 'Liabilities',
      EQUITY: 'Equity',
      REVENUE: 'Revenue',
      EXPENSE: 'Expenses'
    };
    return typeNames[type];
  }

  /**
   * Get account subtype display name
   */
  public getAccountSubtypeDisplayName(subtype: AccountSubtype): string {
    const subtypeNames: Record<AccountSubtype, string> = {
      CURRENT_ASSET: 'Current Assets',
      NON_CURRENT_ASSET: 'Non-Current Assets',
      CURRENT_LIABILITY: 'Current Liabilities',
      NON_CURRENT_LIABILITY: 'Non-Current Liabilities',
      OWNER_EQUITY: 'Owner\'s Equity',
      RETAINED_EARNINGS: 'Retained Earnings',
      OPERATING_REVENUE: 'Operating Revenue',
      NON_OPERATING_REVENUE: 'Non-Operating Revenue',
      OPERATING_EXPENSE: 'Operating Expenses',
      NON_OPERATING_EXPENSE: 'Non-Operating Expenses',
      COST_OF_GOODS_SOLD: 'Cost of Goods Sold'
    };
    return subtypeNames[subtype];
  }

  /**
   * Get valid subtypes for an account type
   */
  public getValidSubtypes(accountType: AccountType): AccountSubtype[] {
    const subtypeMap: Record<AccountType, AccountSubtype[]> = {
      ASSET: ['CURRENT_ASSET', 'NON_CURRENT_ASSET'],
      LIABILITY: ['CURRENT_LIABILITY', 'NON_CURRENT_LIABILITY'],
      EQUITY: ['OWNER_EQUITY', 'RETAINED_EARNINGS'],
      REVENUE: ['OPERATING_REVENUE', 'NON_OPERATING_REVENUE'],
      EXPENSE: ['OPERATING_EXPENSE', 'NON_OPERATING_EXPENSE', 'COST_OF_GOODS_SOLD']
    };
    return subtypeMap[accountType] || [];
  }

  /**
   * Validate account code format
   */
  public validateAccountCode(code: string): { isValid: boolean; error?: string } {
    if (!code || code.trim().length === 0) {
      return { isValid: false, error: 'Account code is required' };
    }

    if (code.length > 20) {
      return { isValid: false, error: 'Account code must be 20 characters or less' };
    }

    // Check for valid characters (alphanumeric, hyphens, underscores)
    const validCodeRegex = /^[A-Za-z0-9\-_]+$/;
    if (!validCodeRegex.test(code)) {
      return { isValid: false, error: 'Account code can only contain letters, numbers, hyphens, and underscores' };
    }

    return { isValid: true };
  }

  /**
   * Generate suggested account code based on type and existing accounts
   */
  public async generateAccountCode(
    companyId: string,
    accountType: AccountType,
    accountSubtype?: AccountSubtype
  ): Promise<string> {
    const accounts = await this.getAccounts(companyId, { type: accountType });
    
    // Define code prefixes for each account type
    const typePrefixes: Record<AccountType, string> = {
      ASSET: '1',
      LIABILITY: '2',
      EQUITY: '3',
      REVENUE: '4',
      EXPENSE: '5'
    };

    // Define subtype prefixes
    const subtypePrefixes: Partial<Record<AccountSubtype, string>> = {
      CURRENT_ASSET: '10',
      NON_CURRENT_ASSET: '15',
      CURRENT_LIABILITY: '20',
      NON_CURRENT_LIABILITY: '25',
      OWNER_EQUITY: '30',
      RETAINED_EARNINGS: '35',
      OPERATING_REVENUE: '40',
      NON_OPERATING_REVENUE: '45',
      OPERATING_EXPENSE: '50',
      NON_OPERATING_EXPENSE: '55',
      COST_OF_GOODS_SOLD: '52'
    };

    let prefix = typePrefixes[accountType];
    
    if (accountSubtype && subtypePrefixes[accountSubtype]) {
      prefix = subtypePrefixes[accountSubtype];
    }

    // Find the next available number
    const existingCodes = accounts
      .map(account => account.code)
      .filter(code => code.startsWith(prefix))
      .map(code => {
        const numPart = code.substring(prefix.length);
        return parseInt(numPart) || 0;
      })
      .sort((a, b) => a - b);

    let nextNumber = 1;
    for (const num of existingCodes) {
      if (num === nextNumber) {
        nextNumber++;
      } else {
        break;
      }
    }

    return `${prefix}${nextNumber.toString().padStart(3, '0')}`;
  }
}

export const accountService = new AccountService();
