// import { apiService } from './api';

export interface CustomReport {
  id: string;
  companyId: string;
  name: string;
  description: string;
  reportType: 'TABLE' | 'CHART' | 'SUMMARY';
  config: Record<string, any>;
  isPublic: boolean;
  isScheduled: boolean;
  scheduleConfig: Record<string, any>;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface ReportExecution {
  id: string;
  reportId: string;
  executedBy?: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';
  parameters: Record<string, any>;
  resultData?: any;
  filePath?: string;
  fileSize?: number;
  recordCount?: number;
  executionTimeMs?: number;
  errorMessage?: string;
  startedAt: string;
  completedAt?: string;
  createdAt: string;
}

export interface ReportFilters {
  reportType?: string;
  isScheduled?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

export interface CreateReportData {
  name: string;
  description: string;
  reportType: CustomReport['reportType'];
  config: Record<string, any>;
  isPublic?: boolean;
  isScheduled?: boolean;
  scheduleConfig?: Record<string, any>;
}

class AdvancedReportService {
  /**
   * Get custom reports
   */
  async getReports(companyId: string, filters: ReportFilters = {}): Promise<{
    reports: CustomReport[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    // Mock data for now - replace with real API call when backend is ready
    const mockReports: CustomReport[] = [
      {
        id: 'report-1',
        companyId,
        name: 'Monthly P&L',
        description: 'Monthly Profit & Loss statement',
        reportType: 'TABLE',
        config: { dataSource: 'transactions', groupBy: 'month' },
        isPublic: false,
        isScheduled: true,
        scheduleConfig: { frequency: 'monthly' },
        createdBy: 'user-1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'report-2',
        companyId,
        name: 'Revenue Trends',
        description: 'Revenue trends over time',
        reportType: 'CHART',
        config: { chartType: 'line', xAxis: 'date', yAxis: 'revenue' },
        isPublic: true,
        isScheduled: false,
        scheduleConfig: {},
        createdBy: 'user-1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'report-3',
        companyId,
        name: 'Expense Summary',
        description: 'Summary of expenses by category',
        reportType: 'SUMMARY',
        config: { groupBy: 'category', aggregation: 'sum' },
        isPublic: false,
        isScheduled: false,
        scheduleConfig: {},
        createdBy: 'user-1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    // Apply filters
    let filteredReports = mockReports;
    if (filters.reportType) {
      filteredReports = filteredReports.filter(r => r.reportType === filters.reportType);
    }
    if (filters.isScheduled !== undefined) {
      filteredReports = filteredReports.filter(r => r.isScheduled === filters.isScheduled);
    }
    if (filters.search) {
      filteredReports = filteredReports.filter(r =>
        r.name.toLowerCase().includes(filters.search!.toLowerCase()) ||
        r.description.toLowerCase().includes(filters.search!.toLowerCase())
      );
    }

    return {
      reports: filteredReports,
      total: filteredReports.length,
      page: filters.page || 1,
      totalPages: Math.ceil(filteredReports.length / (filters.limit || 10)),
    };

    // TODO: Replace with real API call
    // const params = new URLSearchParams();
    // if (filters.reportType) params.append('reportType', filters.reportType);
    // if (filters.isScheduled !== undefined) params.append('isScheduled', filters.isScheduled.toString());
    // if (filters.search) params.append('search', filters.search);
    // if (filters.page) params.append('page', filters.page.toString());
    // if (filters.limit) params.append('limit', filters.limit.toString());
    // return await apiService.get(`/companies/${companyId}/custom-reports?${params}`);
  }

  /**
   * Get a specific report
   */
  async getReport(companyId: string, reportId: string): Promise<CustomReport> {
    const response = await apiClient.get(`/companies/${companyId}/custom-reports/${reportId}`);
    return response.data;
  }

  /**
   * Create a new report
   */
  async createReport(companyId: string, reportData: CreateReportData): Promise<CustomReport> {
    const response = await apiClient.post(`/companies/${companyId}/custom-reports`, reportData);
    return response.data;
  }

  /**
   * Update a report
   */
  async updateReport(
    companyId: string, 
    reportId: string, 
    reportData: Partial<CreateReportData>
  ): Promise<CustomReport> {
    const response = await apiClient.put(`/companies/${companyId}/custom-reports/${reportId}`, reportData);
    return response.data;
  }

  /**
   * Delete a report
   */
  async deleteReport(companyId: string, reportId: string): Promise<void> {
    // Mock implementation - just log
    console.log(`Deleting report ${reportId}`);
    // TODO: Replace with real API call
    // await apiService.delete(`/companies/${companyId}/custom-reports/${reportId}`);
  }

  /**
   * Execute a report
   */
  async executeReport(
    companyId: string,
    reportId: string,
    parameters: Record<string, any> = {}
  ): Promise<ReportExecution> {
    // Mock implementation
    console.log(`Executing report ${reportId} with parameters:`, parameters);
    return {
      id: 'execution-' + Date.now(),
      reportId,
      executedBy: 'user-1',
      status: 'COMPLETED',
      parameters,
      resultData: { rows: 100, columns: 5 },
      recordCount: 100,
      executionTimeMs: 1500,
      startedAt: new Date().toISOString(),
      completedAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
    };
    // TODO: Replace with real API call
    // return await apiService.post(`/companies/${companyId}/custom-reports/${reportId}/execute`, { parameters });
  }

  /**
   * Get report executions
   */
  async getReportExecutions(
    companyId: string,
    reportId?: string,
    filters: {
      status?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{
    executions: ReportExecution[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const params = new URLSearchParams();
    
    if (reportId) params.append('reportId', reportId);
    if (filters.status) params.append('status', filters.status);
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());

    const response = await apiClient.get(`/companies/${companyId}/report-executions?${params}`);
    return response.data;
  }

  /**
   * Get report execution details
   */
  async getReportExecution(companyId: string, executionId: string): Promise<ReportExecution> {
    const response = await apiClient.get(`/companies/${companyId}/report-executions/${executionId}`);
    return response.data;
  }

  /**
   * Download report file
   */
  async downloadReportFile(companyId: string, executionId: string): Promise<Blob> {
    const response = await apiClient.get(
      `/companies/${companyId}/report-executions/${executionId}/download`,
      { responseType: 'blob' }
    );
    return response.data;
  }

  /**
   * Get report templates
   */
  async getReportTemplates(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    category: string;
    reportType: string;
    config: Record<string, any>;
    preview?: string;
  }>> {
    const response = await apiClient.get('/report-templates');
    return response.data;
  }

  /**
   * Create report from template
   */
  async createFromTemplate(
    companyId: string,
    templateId: string,
    customizations: {
      name: string;
      description?: string;
      config?: Record<string, any>;
    }
  ): Promise<CustomReport> {
    const response = await apiClient.post(`/companies/${companyId}/custom-reports/from-template`, {
      templateId,
      ...customizations,
    });
    return response.data;
  }

  /**
   * Get available data sources for report building
   */
  async getDataSources(companyId: string): Promise<Array<{
    id: string;
    name: string;
    description: string;
    type: 'TABLE' | 'VIEW' | 'QUERY';
    fields: Array<{
      name: string;
      type: string;
      description: string;
      aggregatable: boolean;
      filterable: boolean;
    }>;
  }>> {
    const response = await apiClient.get(`/companies/${companyId}/report-data-sources`);
    return response.data;
  }

  /**
   * Preview report data
   */
  async previewReport(
    companyId: string,
    config: Record<string, any>,
    parameters: Record<string, any> = {}
  ): Promise<{
    columns: Array<{ name: string; type: string; label: string }>;
    rows: any[];
    totalRows: number;
    executionTime: number;
  }> {
    const response = await apiClient.post(`/companies/${companyId}/custom-reports/preview`, {
      config,
      parameters,
    });
    return response.data;
  }

  /**
   * Validate report configuration
   */
  validateReportConfig(config: Record<string, any>): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation
    if (!config.dataSource) {
      errors.push('Data source is required');
    }

    if (!config.fields || config.fields.length === 0) {
      errors.push('At least one field must be selected');
    }

    // Validate chart-specific configuration
    if (config.reportType === 'CHART') {
      if (!config.chartType) {
        errors.push('Chart type is required for chart reports');
      }

      if (!config.xAxis) {
        errors.push('X-axis field is required for chart reports');
      }

      if (!config.yAxis || config.yAxis.length === 0) {
        errors.push('At least one Y-axis field is required for chart reports');
      }
    }

    // Validate filters
    if (config.filters) {
      config.filters.forEach((filter: any, index: number) => {
        if (!filter.field) {
          errors.push(`Filter ${index + 1} must have a field`);
        }
        if (!filter.operator) {
          errors.push(`Filter ${index + 1} must have an operator`);
        }
      });
    }

    // Validate aggregations
    if (config.aggregations) {
      config.aggregations.forEach((agg: any, index: number) => {
        if (!agg.field) {
          errors.push(`Aggregation ${index + 1} must have a field`);
        }
        if (!agg.function) {
          errors.push(`Aggregation ${index + 1} must have a function`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Get report categories
   */
  getReportCategories(): Array<{ value: string; label: string; description: string }> {
    return [
      { value: 'financial', label: 'Financial', description: 'Financial statements and analysis' },
      { value: 'operational', label: 'Operational', description: 'Business operations and performance' },
      { value: 'compliance', label: 'Compliance', description: 'Regulatory and compliance reports' },
      { value: 'custom', label: 'Custom', description: 'Custom business reports' },
    ];
  }

  /**
   * Get chart types
   */
  getChartTypes(): Array<{ value: string; label: string; description: string }> {
    return [
      { value: 'line', label: 'Line Chart', description: 'Show trends over time' },
      { value: 'bar', label: 'Bar Chart', description: 'Compare values across categories' },
      { value: 'pie', label: 'Pie Chart', description: 'Show proportions of a whole' },
      { value: 'area', label: 'Area Chart', description: 'Show cumulative values over time' },
      { value: 'scatter', label: 'Scatter Plot', description: 'Show correlation between variables' },
      { value: 'donut', label: 'Donut Chart', description: 'Pie chart with center space' },
    ];
  }

  /**
   * Get aggregation functions
   */
  getAggregationFunctions(): Array<{ value: string; label: string; description: string }> {
    return [
      { value: 'sum', label: 'Sum', description: 'Total of all values' },
      { value: 'avg', label: 'Average', description: 'Mean of all values' },
      { value: 'count', label: 'Count', description: 'Number of records' },
      { value: 'min', label: 'Minimum', description: 'Smallest value' },
      { value: 'max', label: 'Maximum', description: 'Largest value' },
      { value: 'median', label: 'Median', description: 'Middle value' },
    ];
  }

  /**
   * Get filter operators
   */
  getFilterOperators(): Array<{ value: string; label: string; description: string }> {
    return [
      { value: 'equals', label: 'Equals', description: 'Exact match' },
      { value: 'not_equals', label: 'Not Equals', description: 'Does not match' },
      { value: 'contains', label: 'Contains', description: 'Contains text' },
      { value: 'starts_with', label: 'Starts With', description: 'Begins with text' },
      { value: 'ends_with', label: 'Ends With', description: 'Ends with text' },
      { value: 'greater_than', label: 'Greater Than', description: 'Numeric comparison' },
      { value: 'less_than', label: 'Less Than', description: 'Numeric comparison' },
      { value: 'between', label: 'Between', description: 'Range comparison' },
      { value: 'in', label: 'In List', description: 'Matches any value in list' },
      { value: 'is_null', label: 'Is Empty', description: 'No value' },
      { value: 'is_not_null', label: 'Is Not Empty', description: 'Has value' },
    ];
  }
}

export const reportService = new AdvancedReportService();
export default reportService;
