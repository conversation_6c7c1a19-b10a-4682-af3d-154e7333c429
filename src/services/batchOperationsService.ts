import { apiService } from './api';

export interface BatchImportOptions {
  format: 'CSV' | 'EXCEL';
  hasHeaders: boolean;
  dateFormat: string;
  mapping: Record<string, string>;
  validateOnly: boolean;
  skipErrors: boolean;
}

export interface BatchExportOptions {
  format: 'CSV' | 'EXCEL' | 'PDF';
  includeHeaders: boolean;
  dateFormat: string;
  filters?: Record<string, any>;
  columns?: string[];
  groupBy?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ImportResult {
  success: boolean;
  totalRows: number;
  successfulRows: number;
  failedRows: number;
  errors: Array<{
    row: number;
    field: string;
    message: string;
    value: any;
  }>;
  warnings: Array<{
    row: number;
    field: string;
    message: string;
    value: any;
  }>;
  importId: string;
  previewData?: any[];
}

export interface ExportResult {
  success: boolean;
  filename: string;
  downloadUrl: string;
  fileSize: number;
  recordCount: number;
  exportId: string;
}

export interface BatchJob {
  id: string;
  type: 'IMPORT' | 'EXPORT';
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  progress: number;
  totalRecords: number;
  processedRecords: number;
  errorCount: number;
  warningCount: number;
  startedAt: string;
  completedAt?: string;
  result?: ImportResult | ExportResult;
  error?: string;
}

class BatchOperationsService {
  /**
   * Import transactions from file
   */
  public async importTransactions(
    companyId: string,
    file: File,
    options: BatchImportOptions
  ): Promise<ImportResult> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('options', JSON.stringify(options));

    return await apiService.post<ImportResult>(
      `/batch-operations/${companyId}/import/transactions`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
  }

  /**
   * Preview import data before actual import
   */
  public async previewImport(
    companyId: string,
    file: File,
    options: Partial<BatchImportOptions>
  ): Promise<{
    preview: any[];
    detectedColumns: string[];
    suggestedMapping: Record<string, string>;
    rowCount: number;
  }> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('options', JSON.stringify({ ...options, validateOnly: true }));

    return await apiService.post(
      `/batch-operations/${companyId}/import/preview`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
  }

  /**
   * Export transactions to file
   */
  public async exportTransactions(
    companyId: string,
    options: BatchExportOptions
  ): Promise<ExportResult> {
    return await apiService.post<ExportResult>(
      `/batch-operations/${companyId}/export/transactions`,
      options
    );
  }

  /**
   * Export transaction templates
   */
  public async exportTemplates(
    companyId: string,
    templateIds: string[],
    options: BatchExportOptions
  ): Promise<ExportResult> {
    return await apiService.post<ExportResult>(
      `/batch-operations/${companyId}/export/templates`,
      { templateIds, options }
    );
  }

  /**
   * Import transaction templates
   */
  public async importTemplates(
    companyId: string,
    file: File,
    options: BatchImportOptions
  ): Promise<ImportResult> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('options', JSON.stringify(options));

    return await apiService.post<ImportResult>(
      `/batch-operations/${companyId}/import/templates`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
  }

  /**
   * Get batch job status
   */
  public async getBatchJob(companyId: string, jobId: string): Promise<BatchJob> {
    return await apiService.get<BatchJob>(
      `/batch-operations/${companyId}/jobs/${jobId}`
    );
  }

  /**
   * Get all batch jobs for a company
   */
  public async getBatchJobs(
    companyId: string,
    options: {
      type?: 'IMPORT' | 'EXPORT';
      status?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{
    jobs: BatchJob[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const params = new URLSearchParams();
    
    if (options.type) params.append('type', options.type);
    if (options.status) params.append('status', options.status);
    if (options.page) params.append('page', options.page.toString());
    if (options.limit) params.append('limit', options.limit.toString());

    const queryString = params.toString();
    const url = `/batch-operations/${companyId}/jobs${queryString ? `?${queryString}` : ''}`;

    return await apiService.get(url);
  }

  /**
   * Cancel a batch job
   */
  public async cancelBatchJob(companyId: string, jobId: string): Promise<void> {
    return await apiService.post<void>(
      `/batch-operations/${companyId}/jobs/${jobId}/cancel`
    );
  }

  /**
   * Download export file
   */
  public async downloadExport(companyId: string, exportId: string): Promise<Blob> {
    const response = await apiService.get(
      `/batch-operations/${companyId}/exports/${exportId}/download`,
      { responseType: 'blob' }
    );
    return response;
  }

  /**
   * Get import/export templates
   */
  public async getImportTemplates(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    format: string;
    mapping: Record<string, string>;
    sampleFile?: string;
  }>> {
    return await apiService.get('/batch-operations/templates/import');
  }

  /**
   * Get export templates
   */
  public async getExportTemplates(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    format: string;
    columns: string[];
    defaultOptions: BatchExportOptions;
  }>> {
    return await apiService.get('/batch-operations/templates/export');
  }

  /**
   * Validate import data
   */
  public validateImportData(
    data: any[],
    mapping: Record<string, string>
  ): {
    isValid: boolean;
    errors: Array<{
      row: number;
      field: string;
      message: string;
      value: any;
    }>;
    warnings: Array<{
      row: number;
      field: string;
      message: string;
      value: any;
    }>;
  } {
    const errors: any[] = [];
    const warnings: any[] = [];

    data.forEach((row, index) => {
      // Validate required fields
      if (!row[mapping.transactionDate]) {
        errors.push({
          row: index + 1,
          field: 'transactionDate',
          message: 'Transaction date is required',
          value: row[mapping.transactionDate],
        });
      }

      if (!row[mapping.description]) {
        errors.push({
          row: index + 1,
          field: 'description',
          message: 'Description is required',
          value: row[mapping.description],
        });
      }

      // Validate amounts
      const debitAmount = parseFloat(row[mapping.debitAmount] || '0');
      const creditAmount = parseFloat(row[mapping.creditAmount] || '0');

      if (isNaN(debitAmount) && isNaN(creditAmount)) {
        errors.push({
          row: index + 1,
          field: 'amount',
          message: 'Either debit or credit amount must be specified',
          value: `Debit: ${row[mapping.debitAmount]}, Credit: ${row[mapping.creditAmount]}`,
        });
      }

      if (debitAmount > 0 && creditAmount > 0) {
        warnings.push({
          row: index + 1,
          field: 'amount',
          message: 'Both debit and credit amounts specified, credit will be ignored',
          value: `Debit: ${debitAmount}, Credit: ${creditAmount}`,
        });
      }

      // Validate date format
      if (row[mapping.transactionDate]) {
        const date = new Date(row[mapping.transactionDate]);
        if (isNaN(date.getTime())) {
          errors.push({
            row: index + 1,
            field: 'transactionDate',
            message: 'Invalid date format',
            value: row[mapping.transactionDate],
          });
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Get field mapping suggestions
   */
  public suggestFieldMapping(columns: string[]): Record<string, string> {
    const mapping: Record<string, string> = {};
    const lowerColumns = columns.map(col => col.toLowerCase());

    // Common field mappings
    const fieldMappings = {
      transactionDate: ['date', 'transaction_date', 'trans_date', 'posting_date'],
      description: ['description', 'desc', 'memo', 'narrative'],
      reference: ['reference', 'ref', 'ref_no', 'reference_number'],
      debitAmount: ['debit', 'debit_amount', 'dr', 'dr_amount'],
      creditAmount: ['credit', 'credit_amount', 'cr', 'cr_amount'],
      accountCode: ['account', 'account_code', 'acc_code', 'gl_account'],
      accountName: ['account_name', 'acc_name', 'account_description'],
    };

    Object.entries(fieldMappings).forEach(([field, patterns]) => {
      const matchedColumn = columns.find((col, index) => {
        const lowerCol = lowerColumns[index];
        return patterns.some(pattern => lowerCol.includes(pattern));
      });

      if (matchedColumn) {
        mapping[field] = matchedColumn;
      }
    });

    return mapping;
  }

  /**
   * Format file size for display
   */
  public formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get supported file formats
   */
  public getSupportedFormats(): Array<{
    format: string;
    extensions: string[];
    mimeTypes: string[];
    description: string;
  }> {
    return [
      {
        format: 'CSV',
        extensions: ['.csv'],
        mimeTypes: ['text/csv', 'application/csv'],
        description: 'Comma Separated Values',
      },
      {
        format: 'EXCEL',
        extensions: ['.xlsx', '.xls'],
        mimeTypes: [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-excel',
        ],
        description: 'Microsoft Excel',
      },
    ];
  }
}

export const batchOperationsService = new BatchOperationsService();
