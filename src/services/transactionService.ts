import { apiService } from "./api";
import type { Transaction, TransactionStatus } from "../types";

export interface CreateTransactionData {
  companyId: string;
  transactionDate: string;
  description: string;
  reference?: string;
  entries: Array<{
    accountId: string;
    description?: string;
    debitAmount: number;
    creditAmount: number;
  }>;
}

export interface UpdateTransactionData {
  transactionDate?: string;
  description?: string;
  reference?: string;
  entries?: Array<{
    accountId: string;
    description?: string;
    debitAmount: number;
    creditAmount: number;
  }>;
}

export interface TransactionFilters {
  status?: TransactionStatus;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface TransactionListResponse {
  data: Transaction[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

class TransactionService {
  /**
   * Get all transactions for a company
   */
  public async getTransactions(
    companyId: string,
    filters?: TransactionFilters
  ): Promise<TransactionListResponse> {
    const params = new URLSearchParams();

    if (filters?.status) {
      params.append("status", filters.status);
    }

    if (filters?.dateFrom) {
      params.append("dateFrom", filters.dateFrom);
    }

    if (filters?.dateTo) {
      params.append("dateTo", filters.dateTo);
    }

    if (filters?.search) {
      params.append("search", filters.search);
    }

    if (filters?.page) {
      params.append("page", filters.page.toString());
    }

    if (filters?.limit) {
      params.append("limit", filters.limit.toString());
    }

    const queryString = params.toString();
    const url = `/transactions/${companyId}${
      queryString ? `?${queryString}` : ""
    }`;

    return await apiService.get<TransactionListResponse>(url);
  }

  /**
   * Get a single transaction by ID
   */
  public async getTransaction(
    companyId: string,
    transactionId: string
  ): Promise<Transaction> {
    return await apiService.get<Transaction>(
      `/transactions/${companyId}/${transactionId}`
    );
  }

  /**
   * Create a new transaction
   */
  public async createTransaction(
    data: CreateTransactionData
  ): Promise<Transaction> {
    return await apiService.post<Transaction>("/transactions", data);
  }

  /**
   * Update an existing transaction
   */
  public async updateTransaction(
    companyId: string,
    transactionId: string,
    data: UpdateTransactionData
  ): Promise<Transaction> {
    return await apiService.put<Transaction>(
      `/transactions/${companyId}/${transactionId}`,
      data
    );
  }

  /**
   * Delete a transaction
   */
  public async deleteTransaction(
    companyId: string,
    transactionId: string
  ): Promise<void> {
    return await apiService.delete<void>(
      `/transactions/${companyId}/${transactionId}`
    );
  }

  /**
   * Approve a transaction
   */
  public async approveTransaction(
    companyId: string,
    transactionId: string
  ): Promise<Transaction> {
    return await apiService.post<Transaction>(
      `/transactions/${companyId}/${transactionId}/approve`
    );
  }

  /**
   * Post a transaction
   */
  public async postTransaction(
    companyId: string,
    transactionId: string
  ): Promise<Transaction> {
    return await apiService.post<Transaction>(
      `/transactions/${companyId}/${transactionId}/post`
    );
  }

  /**
   * Submit a transaction for approval
   */
  public async submitTransaction(
    companyId: string,
    transactionId: string
  ): Promise<Transaction> {
    return await apiService.post<Transaction>(
      `/transactions/${companyId}/${transactionId}/submit`
    );
  }

  /**
   * Cancel a transaction
   */
  public async cancelTransaction(
    companyId: string,
    transactionId: string
  ): Promise<Transaction> {
    return await apiService.post<Transaction>(
      `/transactions/${companyId}/${transactionId}/cancel`
    );
  }

  /**
   * Reverse a transaction
   */
  public async reverseTransaction(
    companyId: string,
    transactionId: string
  ): Promise<Transaction> {
    return await apiService.post<Transaction>(
      `/transactions/${companyId}/${transactionId}/reverse`
    );
  }

  /**
   * Validate transaction balance
   */
  public validateBalance(
    entries: Array<{ debitAmount: number; creditAmount: number }>
  ): {
    isBalanced: boolean;
    totalDebits: number;
    totalCredits: number;
    difference: number;
  } {
    const totalDebits = entries.reduce(
      (sum, entry) => sum + (entry.debitAmount || 0),
      0
    );
    const totalCredits = entries.reduce(
      (sum, entry) => sum + (entry.creditAmount || 0),
      0
    );
    const difference = Math.abs(totalDebits - totalCredits);

    return {
      isBalanced: difference < 0.01,
      totalDebits,
      totalCredits,
      difference,
    };
  }

  /**
   * Get transaction status options
   */
  public getStatusOptions(): Array<{
    value: TransactionStatus;
    label: string;
  }> {
    return [
      { value: "DRAFT", label: "Draft" },
      { value: "PENDING", label: "Pending" },
      { value: "APPROVED", label: "Approved" },
      { value: "POSTED", label: "Posted" },
      { value: "CANCELLED", label: "Cancelled" },
      { value: "REVERSED", label: "Reversed" },
    ];
  }

  /**
   * Get status color for UI display
   */
  public getStatusColor(status: TransactionStatus): string {
    const colors = {
      DRAFT: "gray",
      PENDING: "yellow",
      APPROVED: "blue",
      POSTED: "green",
      CANCELLED: "red",
      REVERSED: "purple",
    };

    return colors[status] || "gray";
  }

  /**
   * Check if transaction can be edited
   */
  public canEdit(status: TransactionStatus): boolean {
    return status === "DRAFT" || status === "PENDING";
  }

  /**
   * Check if transaction can be deleted
   */
  public canDelete(status: TransactionStatus): boolean {
    return status === "DRAFT" || status === "PENDING";
  }

  /**
   * Check if transaction can be submitted for approval
   */
  public canSubmit(status: TransactionStatus): boolean {
    return status === "DRAFT";
  }

  /**
   * Check if transaction can be approved
   */
  public canApprove(status: TransactionStatus): boolean {
    return status === "PENDING";
  }

  /**
   * Check if transaction can be posted
   */
  public canPost(status: TransactionStatus): boolean {
    return status === "APPROVED";
  }

  /**
   * Check if transaction can be cancelled
   */
  public canCancel(status: TransactionStatus): boolean {
    return ["DRAFT", "PENDING", "APPROVED"].includes(status);
  }

  /**
   * Check if transaction can be reversed
   */
  public canReverse(status: TransactionStatus): boolean {
    return status === "POSTED";
  }
}

export const transactionService = new TransactionService();
