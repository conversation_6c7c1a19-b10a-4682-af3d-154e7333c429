import api from './api';
import type {
  Estimate,
  CreateEstimateRequest,
  UpdateEstimateRequest,
  EstimateFilters,
  EstimateListResponse,
  UpdateEstimateStatusRequest,
  SendEstimateRequest,
} from '../types/estimate';

class EstimateService {
  private baseUrl = '/estimates';

  /**
   * Get all estimates for a company
   */
  async getEstimates(
    companyId: string,
    filters?: EstimateFilters
  ): Promise<EstimateListResponse> {
    const params = new URLSearchParams();
    
    if (filters?.status) params.append('status', filters.status);
    if (filters?.customerId) params.append('customerId', filters.customerId);
    if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters?.dateTo) params.append('dateTo', filters.dateTo);
    if (filters?.expiryDateFrom) params.append('expiryDateFrom', filters.expiryDateFrom);
    if (filters?.expiryDateTo) params.append('expiryDateTo', filters.expiryDateTo);
    if (filters?.search) params.append('search', filters.search);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());

    const queryString = params.toString();
    const url = `${this.baseUrl}/${companyId}${queryString ? `?${queryString}` : ''}`;
    
    const response = await api.get(url);
    return response.data;
  }

  /**
   * Get a single estimate by ID
   */
  async getEstimate(companyId: string, estimateId: string): Promise<Estimate> {
    const response = await api.get(`${this.baseUrl}/${companyId}/${estimateId}`);
    return response.data;
  }

  /**
   * Create a new estimate
   */
  async createEstimate(data: CreateEstimateRequest): Promise<Estimate> {
    const response = await api.post(this.baseUrl, data);
    return response.data;
  }

  /**
   * Update an existing estimate
   */
  async updateEstimate(
    companyId: string,
    estimateId: string,
    data: UpdateEstimateRequest
  ): Promise<Estimate> {
    const response = await api.put(`${this.baseUrl}/${companyId}/${estimateId}`, data);
    return response.data;
  }

  /**
   * Update estimate status
   */
  async updateEstimateStatus(
    companyId: string,
    estimateId: string,
    data: UpdateEstimateStatusRequest
  ): Promise<{ message: string; status: string }> {
    const response = await api.post(`${this.baseUrl}/${companyId}/${estimateId}/status`, data);
    return response.data;
  }

  /**
   * Send estimate to customer
   */
  async sendEstimate(
    companyId: string,
    estimateId: string,
    data: SendEstimateRequest
  ): Promise<{ message: string; status: string }> {
    const response = await api.post(`${this.baseUrl}/${companyId}/${estimateId}/send`, data);
    return response.data;
  }

  /**
   * Convert estimate to invoice
   */
  async convertToInvoice(
    companyId: string,
    estimateId: string
  ): Promise<{ message: string; status: string; invoiceId?: string }> {
    const response = await api.post(`${this.baseUrl}/${companyId}/${estimateId}/convert-to-invoice`);
    return response.data;
  }

  /**
   * Delete an estimate
   */
  async deleteEstimate(companyId: string, estimateId: string): Promise<{ message: string }> {
    const response = await api.delete(`${this.baseUrl}/${companyId}/${estimateId}`);
    return response.data;
  }

  /**
   * Accept an estimate
   */
  async acceptEstimate(companyId: string, estimateId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updateEstimateStatus(companyId, estimateId, {
      status: 'ACCEPTED',
      notes: notes || 'Estimate accepted',
    });
  }

  /**
   * Decline an estimate
   */
  async declineEstimate(companyId: string, estimateId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updateEstimateStatus(companyId, estimateId, {
      status: 'DECLINED',
      notes: notes || 'Estimate declined',
    });
  }

  /**
   * Cancel an estimate
   */
  async cancelEstimate(companyId: string, estimateId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updateEstimateStatus(companyId, estimateId, {
      status: 'CANCELLED',
      notes: notes || 'Estimate cancelled',
    });
  }

  /**
   * Mark estimate as sent
   */
  async markAsSent(companyId: string, estimateId: string, notes?: string): Promise<{ message: string; status: string }> {
    return this.updateEstimateStatus(companyId, estimateId, {
      status: 'SENT',
      notes: notes || 'Estimate sent to customer',
    });
  }

  /**
   * Format currency amount
   */
  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }

  /**
   * Format date
   */
  formatDate(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  /**
   * Calculate line item total
   */
  calculateLineTotal(quantity: number, unitPrice: number, discountPercentage: number = 0): number {
    const subtotal = quantity * unitPrice;
    const discountAmount = (subtotal * discountPercentage) / 100;
    return subtotal - discountAmount;
  }

  /**
   * Calculate line item tax
   */
  calculateLineTax(lineTotal: number, taxRate: number = 0): number {
    return (lineTotal * taxRate) / 100;
  }

  /**
   * Calculate estimate totals
   */
  calculateTotals(lineItems: Array<{
    quantity: number;
    unitPrice: number;
    discountPercentage?: number;
    taxRate?: number;
  }>): {
    subtotal: number;
    totalDiscount: number;
    totalTax: number;
    total: number;
  } {
    let subtotal = 0;
    let totalDiscount = 0;
    let totalTax = 0;

    lineItems.forEach(item => {
      const lineSubtotal = item.quantity * item.unitPrice;
      const discountAmount = (lineSubtotal * (item.discountPercentage || 0)) / 100;
      const lineTotal = lineSubtotal - discountAmount;
      const taxAmount = (lineTotal * (item.taxRate || 0)) / 100;

      subtotal += lineSubtotal;
      totalDiscount += discountAmount;
      totalTax += taxAmount;
    });

    const total = subtotal - totalDiscount + totalTax;

    return {
      subtotal,
      totalDiscount,
      totalTax,
      total,
    };
  }

  /**
   * Generate estimate number (client-side preview)
   */
  generateEstimateNumber(lastNumber: number = 0): string {
    return `EST-${String(lastNumber + 1).padStart(6, '0')}`;
  }

  /**
   * Validate estimate data
   */
  validateEstimate(data: CreateEstimateRequest | UpdateEstimateRequest): string[] {
    const errors: string[] = [];

    if ('customerId' in data && !data.customerId) {
      errors.push('Customer is required');
    }

    if ('estimateDate' in data && !data.estimateDate) {
      errors.push('Estimate date is required');
    }

    if ('lineItems' in data && data.lineItems) {
      if (data.lineItems.length === 0) {
        errors.push('At least one line item is required');
      }

      data.lineItems.forEach((item, index) => {
        if (!item.description?.trim()) {
          errors.push(`Line item ${index + 1}: Description is required`);
        }
        if (!item.quantity || item.quantity <= 0) {
          errors.push(`Line item ${index + 1}: Quantity must be greater than 0`);
        }
        if (!item.unitPrice || item.unitPrice <= 0) {
          errors.push(`Line item ${index + 1}: Unit price must be greater than 0`);
        }
      });
    }

    return errors;
  }

  /**
   * Get validity status for estimate
   */
  getValidityStatus(estimate: Estimate): {
    status: 'valid' | 'expiring' | 'expired';
    message: string;
    daysRemaining?: number;
  } {
    if (!estimate.expiryDate) {
      return { status: 'valid', message: 'No expiry date set' };
    }

    const expiryDate = new Date(estimate.expiryDate);
    const today = new Date();
    const diffDays = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return {
        status: 'expired',
        message: `Expired ${Math.abs(diffDays)} days ago`,
        daysRemaining: 0,
      };
    }
    
    if (diffDays <= 7) {
      return {
        status: 'expiring',
        message: `Expires in ${diffDays} days`,
        daysRemaining: diffDays,
      };
    }
    
    return {
      status: 'valid',
      message: `Valid for ${diffDays} more days`,
      daysRemaining: diffDays,
    };
  }

  /**
   * Calculate default expiry date
   */
  calculateExpiryDate(estimateDate: string, validityDays: number = 30): string {
    const date = new Date(estimateDate);
    date.setDate(date.getDate() + validityDays);
    return date.toISOString().split('T')[0];
  }

  /**
   * Get default terms and conditions
   */
  getDefaultTermsAndConditions(): string {
    return `Terms and Conditions:

1. This estimate is valid for 30 days from the date of issue.
2. Prices are subject to change without notice after expiry.
3. Payment terms: Net 30 days from invoice date.
4. All work will be completed according to specifications.
5. Additional charges may apply for changes to original scope.

Thank you for your business!`;
  }

  /**
   * Generate email subject for estimate
   */
  generateEmailSubject(estimate: Estimate): string {
    return `Estimate ${estimate.estimateNumber} from ${estimate.companyId}`;
  }

  /**
   * Generate email body for estimate
   */
  generateEmailBody(estimate: Estimate): string {
    return `Dear ${estimate.customerName || 'Valued Customer'},

Please find attached our estimate ${estimate.estimateNumber} for your review.

Estimate Details:
- Estimate Number: ${estimate.estimateNumber}
- Date: ${this.formatDate(estimate.estimateDate)}
- Total Amount: ${this.formatCurrency(estimate.totalAmount, estimate.currency)}
${estimate.expiryDate ? `- Valid Until: ${this.formatDate(estimate.expiryDate)}` : ''}

${estimate.notes ? `\nNotes:\n${estimate.notes}` : ''}

Please review the estimate and let us know if you have any questions. We look forward to working with you.

Best regards,
Your Team`;
  }
}

export default new EstimateService();
