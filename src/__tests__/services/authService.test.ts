import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { authService } from '../../services/authService';
import { apiService } from '../../services/api';

// Mock the API service
vi.mock('../../services/api', () => ({
  apiService: {
    post: vi.fn(),
    get: vi.fn(),
    setToken: vi.fn(),
    removeToken: vi.fn(),
    getToken: vi.fn()
  }
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('AuthService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.clear();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('login', () => {
    const mockLoginResponse = {
      token: 'mock-jwt-token',
      user: {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: {
          name: 'admin',
          permissions: ['*']
        },
        companies: [
          {
            id: '1',
            name: 'Test Company',
            baseCurrency: 'USD'
          }
        ]
      }
    };

    it('should login successfully with valid credentials', async () => {
      vi.mocked(apiService.post).mockResolvedValue(mockLoginResponse);

      const result = await authService.login('<EMAIL>', 'password123');

      expect(apiService.post).toHaveBeenCalledWith('/auth/login', {
        email: '<EMAIL>',
        password: 'password123'
      });

      expect(result).toEqual(mockLoginResponse);
      expect(localStorageMock.setItem).toHaveBeenCalledWith('token', 'mock-jwt-token');
      expect(apiService.setToken).toHaveBeenCalledWith('mock-jwt-token');
    });

    it('should handle login failure', async () => {
      const mockError = new Error('Invalid credentials');
      vi.mocked(apiService.post).mockRejectedValue(mockError);

      await expect(authService.login('<EMAIL>', 'wrongpassword'))
        .rejects.toThrow('Invalid credentials');

      expect(localStorageMock.setItem).not.toHaveBeenCalled();
      expect(apiService.setToken).not.toHaveBeenCalled();
    });

    it('should validate email format', async () => {
      await expect(authService.login('invalid-email', 'password123'))
        .rejects.toThrow();
    });

    it('should validate password length', async () => {
      await expect(authService.login('<EMAIL>', '123'))
        .rejects.toThrow();
    });
  });

  describe('register', () => {
    const mockRegisterData = {
      email: '<EMAIL>',
      password: 'password123',
      confirmPassword: 'password123',
      firstName: 'New',
      lastName: 'User',
      phone: '+1234567890'
    };

    const mockRegisterResponse = {
      token: 'mock-jwt-token',
      user: {
        id: '2',
        email: '<EMAIL>',
        firstName: 'New',
        lastName: 'User',
        role: {
          name: 'user',
          permissions: ['users:read']
        },
        companies: []
      }
    };

    it('should register successfully with valid data', async () => {
      vi.mocked(apiService.post).mockResolvedValue(mockRegisterResponse);

      const result = await authService.register(mockRegisterData);

      expect(apiService.post).toHaveBeenCalledWith('/auth/register', mockRegisterData);
      expect(result).toEqual(mockRegisterResponse);
      expect(localStorageMock.setItem).toHaveBeenCalledWith('token', 'mock-jwt-token');
      expect(apiService.setToken).toHaveBeenCalledWith('mock-jwt-token');
    });

    it('should handle registration failure', async () => {
      const mockError = new Error('Email already exists');
      vi.mocked(apiService.post).mockRejectedValue(mockError);

      await expect(authService.register(mockRegisterData))
        .rejects.toThrow('Email already exists');

      expect(localStorageMock.setItem).not.toHaveBeenCalled();
      expect(apiService.setToken).not.toHaveBeenCalled();
    });

    it('should validate required fields', async () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'password123'
        // Missing firstName and lastName
      };

      await expect(authService.register(invalidData as any))
        .rejects.toThrow();
    });
  });

  describe('logout', () => {
    it('should clear token and call API service', async () => {
      vi.mocked(apiService.post).mockResolvedValue({});

      await authService.logout();

      expect(apiService.post).toHaveBeenCalledWith('/auth/logout');
    });
  });

  describe('getCurrentUser', () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: {
        name: 'admin',
        permissions: ['*']
      },
      companies: []
    };

    it('should fetch current user successfully', async () => {
      vi.mocked(apiService.get).mockResolvedValue(mockUser);

      const result = await authService.getCurrentUser();

      expect(apiService.get).toHaveBeenCalledWith(expect.stringMatching(/\/auth\/me\?t=\d+/));
      expect(result).toEqual(mockUser);
    });

    it('should handle fetch user failure', async () => {
      const mockError = new Error('Unauthorized');
      vi.mocked(apiService.get).mockRejectedValue(mockError);

      await expect(authService.getCurrentUser())
        .rejects.toThrow('Unauthorized');
    });

    it('should add cache-busting parameter', async () => {
      vi.mocked(apiService.get).mockResolvedValue(mockUser);

      await authService.getCurrentUser();

      const callArgs = vi.mocked(apiService.get).mock.calls[0][0];
      expect(callArgs).toMatch(/\/auth\/me\?t=\d+/);
    });
  });

  describe('setToken', () => {
    it('should set token in localStorage and API service', () => {
      const token = 'new-token';

      authService.setToken(token);

      expect(localStorageMock.setItem).toHaveBeenCalledWith('token', token);
      expect(apiService.setToken).toHaveBeenCalledWith(token);
    });
  });

  describe('getToken', () => {
    it('should return token from API service', () => {
      const token = 'stored-token';
      vi.mocked(apiService.getToken).mockReturnValue(token);

      const result = authService.getToken();

      expect(apiService.getToken).toHaveBeenCalled();
      expect(result).toBe(token);
    });

    it('should return null if no token stored', () => {
      vi.mocked(apiService.getToken).mockReturnValue(null);

      const result = authService.getToken();

      expect(result).toBeNull();
    });
  });

  describe('isAuthenticated', () => {
    it('should return true if token exists', () => {
      vi.mocked(apiService.getToken).mockReturnValue('some-token');

      const result = authService.isAuthenticated();

      expect(result).toBe(true);
    });

    it('should return false if no token exists', () => {
      vi.mocked(apiService.getToken).mockReturnValue(null);

      const result = authService.isAuthenticated();

      expect(result).toBe(false);
    });
  });

  describe('forgotPassword', () => {
    it('should send forgot password request', async () => {
      const mockResponse = { message: 'Password reset email sent' };
      vi.mocked(apiService.post).mockResolvedValue(mockResponse);

      const result = await authService.forgotPassword('<EMAIL>');

      expect(apiService.post).toHaveBeenCalledWith('/auth/forgot-password', {
        email: '<EMAIL>'
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle forgot password failure', async () => {
      const mockError = new Error('User not found');
      vi.mocked(apiService.post).mockRejectedValue(mockError);

      await expect(authService.forgotPassword('<EMAIL>'))
        .rejects.toThrow('User not found');
    });

    it('should validate email format', async () => {
      await expect(authService.forgotPassword('invalid-email'))
        .rejects.toThrow();
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      const mockResponse = {
        token: 'new-token',
        user: {
          id: '1',
          email: '<EMAIL>'
        }
      };
      vi.mocked(apiService.post).mockResolvedValue(mockResponse);

      const result = await authService.refreshToken();

      expect(apiService.post).toHaveBeenCalledWith('/auth/refresh');
      expect(result).toEqual(mockResponse);
    });

    it('should handle refresh token failure', async () => {
      const mockError = new Error('Token expired');
      vi.mocked(apiService.post).mockRejectedValue(mockError);

      await expect(authService.refreshToken())
        .rejects.toThrow('Token expired');
    });
  });
});
