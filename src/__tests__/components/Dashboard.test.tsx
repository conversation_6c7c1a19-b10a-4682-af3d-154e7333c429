import { render, screen, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import Dashboard from '../../pages/Dashboard';
import { AuthProvider } from '../../contexts/AuthContext';
import { CompanyProvider } from '../../contexts/CompanyContext';
import { NotificationProvider } from '../../contexts/NotificationContext';
import { BrowserRouter } from 'react-router-dom';
import React from 'react';

// Mock the dashboard service
vi.mock('../../services/dashboardService', () => ({
  dashboardService: {
    getStats: vi.fn(),
    getRecentActivity: vi.fn(),
    formatCurrency: vi.fn((amount, currency) => `${currency} ${amount.toFixed(2)}`),
  }
}));

// Mock the chart data hook
vi.mock('../../hooks/useChartData', () => ({
  useChartData: vi.fn(() => ({
    revenueTrend: [
      { month: 'Jan', revenue: 1000 },
      { month: 'Feb', revenue: 1200 },
      { month: 'Mar', revenue: 1100 }
    ],
    cashFlowTrend: [],
    expenseBreakdown: [
      { name: 'Office Rent', value: 2500 },
      { name: 'Utilities', value: 450 }
    ],
    revenueExpenseComparison: [],
    loading: false,
    refresh: vi.fn()
  }))
}));

// Mock the dashboard hook
vi.mock('../../hooks/useDashboard', () => ({
  useDashboard: vi.fn(() => ({
    data: {
      stats: {
        totalRevenue: 50000,
        totalExpenses: 30000,
        netIncome: 20000,
        cashBalance: 15000,
        accountsReceivable: 8000,
        accountsPayable: 5000,
        totalAssets: 100000,
        totalLiabilities: 40000,
        equity: 60000
      },
      recentActivity: [
        {
          id: '1',
          type: 'invoice',
          description: 'Invoice #INV-001 created',
          amount: 1500,
          date: '2024-01-15'
        }
      ]
    },
    stats: {
      totalRevenue: 50000,
      totalExpenses: 30000,
      netIncome: 20000,
      cashBalance: 15000,
      accountsReceivable: 8000,
      accountsPayable: 5000,
      totalAssets: 100000,
      totalLiabilities: 40000,
      equity: 60000
    },
    recentActivity: [
      {
        id: '1',
        type: 'invoice',
        description: 'Invoice #INV-001 created',
        amount: 1500,
        date: '2024-01-15'
      }
    ],
    loading: false,
    error: null,
    refresh: vi.fn(),
    fetchFinancialSummary: vi.fn(),
    fetchRecentActivity: vi.fn()
  }))
}));

// Mock chart components
vi.mock('../../components/charts/TrendChart', () => ({
  default: ({ title, data }: any) => (
    <div data-testid="trend-chart">
      <h3>{title}</h3>
      <div>Data points: {data?.length || 0}</div>
    </div>
  )
}));

vi.mock('../../components/charts/PieChart', () => ({
  default: ({ title, data }: any) => (
    <div data-testid="pie-chart">
      <h3>{title}</h3>
      <div>Data points: {data?.length || 0}</div>
    </div>
  )
}));

vi.mock('../../components/charts/BarChart', () => ({
  default: ({ title, data }: any) => (
    <div data-testid="bar-chart">
      <h3>{title}</h3>
      <div>Data points: {data?.length || 0}</div>
    </div>
  )
}));

// Mock other components
vi.mock('../../components/dashboard/AlertSystem', () => ({
  default: ({ alerts }: any) => (
    <div data-testid="alert-system">
      Alerts: {alerts?.length || 0}
    </div>
  ),
  generateAlerts: vi.fn(() => [])
}));

vi.mock('../../components/dashboard/QuickActions', () => ({
  default: () => <div data-testid="quick-actions">Quick Actions</div>
}));

vi.mock('../../components/dashboard/KPICards', () => ({
  default: ({ stats }: any) => (
    <div data-testid="kpi-cards">
      <div>Revenue: {stats?.totalRevenue}</div>
      <div>Expenses: {stats?.totalExpenses}</div>
    </div>
  )
}));

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    <AuthProvider>
      <CompanyProvider>
        <NotificationProvider>
          {children}
        </NotificationProvider>
      </CompanyProvider>
    </AuthProvider>
  </BrowserRouter>
);

// Mock auth and company contexts
const mockUser = {
  id: '1',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  role: { name: 'admin', permissions: ['*'] }
};

const mockCompany = {
  id: '1',
  name: 'Test Company',
  baseCurrency: 'USD'
};

vi.mock('../../contexts/AuthContext', async () => {
  const actual = await vi.importActual('../../contexts/AuthContext');
  return {
    ...actual,
    useAuth: () => ({
      user: mockUser,
      isAuthenticated: true,
      isLoading: false
    })
  };
});

vi.mock('../../contexts/CompanyContext', async () => {
  const actual = await vi.importActual('../../contexts/CompanyContext');
  return {
    ...actual,
    useCompany: () => ({
      currentCompany: mockCompany,
      companies: [mockCompany],
      isLoading: false
    })
  };
});

describe('Dashboard Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders dashboard with all main sections', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    // Check for main dashboard elements
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Financial Overview')).toBeInTheDocument();
    
    // Check for KPI cards
    await waitFor(() => {
      expect(screen.getByTestId('kpi-cards')).toBeInTheDocument();
    });

    // Check for charts
    expect(screen.getByTestId('trend-chart')).toBeInTheDocument();
    
    // Check for quick actions
    expect(screen.getByTestId('quick-actions')).toBeInTheDocument();
  });

  it('displays financial metrics correctly', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      // Check if revenue is displayed
      expect(screen.getByText(/Revenue: 50000/)).toBeInTheDocument();
      expect(screen.getByText(/Expenses: 30000/)).toBeInTheDocument();
    });
  });

  it('renders revenue trend chart with data', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      const trendChart = screen.getByTestId('trend-chart');
      expect(trendChart).toBeInTheDocument();
      expect(trendChart).toHaveTextContent('Revenue Trend');
      expect(trendChart).toHaveTextContent('Data points: 3');
    });
  });

  it('renders expense breakdown chart when data is available', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      const pieChart = screen.getByTestId('pie-chart');
      expect(pieChart).toBeInTheDocument();
      expect(pieChart).toHaveTextContent('Data points: 2');
    });
  });

  it('shows period selector', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    // Check for period selector buttons
    expect(screen.getByText('Month')).toBeInTheDocument();
    expect(screen.getByText('Quarter')).toBeInTheDocument();
    expect(screen.getByText('Year')).toBeInTheDocument();
  });

  it('displays refresh button', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    expect(refreshButton).toBeInTheDocument();
  });

  it('shows loading state initially', async () => {
    // Mock loading state
    const { useDashboard } = await import('../../hooks/useDashboard');
    vi.mocked(useDashboard).mockReturnValue({
      data: null,
      stats: null,
      recentActivity: [],
      loading: true,
      error: null,
      refresh: vi.fn(),
      fetchFinancialSummary: vi.fn(),
      fetchRecentActivity: vi.fn()
    });

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it('handles error state', async () => {
    // Mock error state
    const { useDashboard } = await import('../../hooks/useDashboard');
    vi.mocked(useDashboard).mockReturnValue({
      data: null,
      stats: null,
      recentActivity: [],
      loading: false,
      error: 'Failed to load dashboard data',
      refresh: vi.fn(),
      fetchFinancialSummary: vi.fn(),
      fetchRecentActivity: vi.fn()
    });

    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    expect(screen.getByText(/error/i)).toBeInTheDocument();
  });

  it('formats currency correctly', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      // Check if currency formatting is applied
      expect(screen.getByText(/USD/)).toBeInTheDocument();
    });
  });

  it('renders alert system', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('alert-system')).toBeInTheDocument();
    });
  });
});
