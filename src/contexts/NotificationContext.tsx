import React, { createContext, useContext, useState, useCallback } from "react";
import type { Notification, NotificationType } from "../types";

interface ShowNotificationOptions {
  type: NotificationType;
  title: string;
  message: string;
  duration?: number;
}

interface NotificationContextType {
  notifications: Notification[];
  addNotification: (
    type: NotificationType,
    title: string,
    message: string,
    duration?: number
  ) => void;
  showNotification: (options: ShowNotificationOptions) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export function NotificationProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const addNotification = useCallback(
    (
      type: NotificationType,
      title: string,
      message: string,
      duration: number = 5000
    ) => {
      const id = Math.random().toString(36).substring(2, 11);
      const notification: Notification = {
        id,
        type,
        title,
        message,
        duration,
        createdAt: new Date().toISOString(),
      };

      setNotifications((prev) => [...prev, notification]);

      // Auto remove notification after duration
      if (duration > 0) {
        setTimeout(() => {
          removeNotification(id);
        }, duration);
      }
    },
    []
  );

  const removeNotification = useCallback((id: string) => {
    setNotifications((prev) =>
      prev.filter((notification) => notification.id !== id)
    );
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const showNotification = useCallback(
    (options: ShowNotificationOptions) => {
      addNotification(
        options.type,
        options.title,
        options.message,
        options.duration
      );
    },
    [addNotification]
  );

  const value: NotificationContextType = {
    notifications,
    addNotification,
    showNotification,
    removeNotification,
    clearNotifications,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotification() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotification must be used within a NotificationProvider"
    );
  }
  return context;
}
