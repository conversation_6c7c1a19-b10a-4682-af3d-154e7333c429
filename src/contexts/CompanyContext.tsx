import React, { createContext, useContext, useState, useEffect } from "react";
import type { Company } from "../types";
import { useAuth } from "./AuthContext";

interface CompanyContextType {
  currentCompany: Company | null;
  companies: Company[];
  setCurrentCompany: (company: Company) => void;
  isLoading: boolean;
}

const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

export function CompanyProvider({ children }: { children: React.ReactNode }) {
  const { user, isAuthenticated } = useAuth();
  const [currentCompany, setCurrentCompanyState] = useState<Company | null>(
    null
  );
  const [companies, setCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);

  // Initialize companies when user data is available
  useEffect(() => {
    if (!isAuthenticated) {
      // Clear everything when not authenticated
      setCurrentCompanyState(null);
      setCompanies([]);
      localStorage.removeItem("currentCompanyId");
      setIsLoading(false);
      setInitialized(false);
      return;
    }

    if (
      isAuthenticated &&
      user?.companies &&
      user.companies.length > 0 &&
      !initialized
    ) {
      // Transform user companies to Company objects
      const userCompanies = user.companies.map((uc) => ({
        id: uc.id,
        name: uc.name,
        baseCurrency: uc.baseCurrency || "USD",
        legalName: "",
        taxId: "",
        registrationNumber: "",
        address: "",
        city: "",
        state: "",
        postalCode: "",
        country: "",
        phone: "",
        email: "",
        website: "",
        settings: {},
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }));

      setCompanies(userCompanies);

      // Restore saved company or use first one
      const savedCompanyId = localStorage.getItem("currentCompanyId");
      let companyToSet: Company | null = null;

      if (savedCompanyId) {
        companyToSet =
          userCompanies.find((c) => c.id === savedCompanyId) || null;
      }

      if (!companyToSet && userCompanies.length > 0) {
        companyToSet = userCompanies[0];
        localStorage.setItem("currentCompanyId", companyToSet.id);
      }

      if (companyToSet) {
        setCurrentCompanyState(companyToSet);
      }

      setIsLoading(false);
      setInitialized(true);
    } else if (
      isAuthenticated &&
      (!user?.companies || user.companies.length === 0)
    ) {
      setIsLoading(true);
    }
  }, [isAuthenticated, user?.companies, initialized]);

  const setCurrentCompany = (company: Company) => {
    setCurrentCompanyState(company);
    localStorage.setItem("currentCompanyId", company.id);
  };

  const value: CompanyContextType = {
    currentCompany,
    companies,
    setCurrentCompany,
    isLoading,
  };

  return (
    <CompanyContext.Provider value={value}>{children}</CompanyContext.Provider>
  );
}

export function useCompany() {
  const context = useContext(CompanyContext);
  if (context === undefined) {
    throw new Error("useCompany must be used within a CompanyProvider");
  }
  return context;
}
