import React, { createContext, useContext, useReducer, useEffect } from "react";
import type { User, AuthState } from "../types";
import { authService } from "../services/authService";

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  register: (userData: any) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

type AuthAction =
  | { type: "AUTH_START" }
  | { type: "AUTH_SUCCESS"; payload: { user: User; token: string } }
  | { type: "AUTH_FAILURE" }
  | { type: "LOGOUT" }
  | { type: "UPDATE_USER"; payload: User };

const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: true,
};

function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case "AUTH_START":
      return {
        ...state,
        isLoading: true,
      };
    case "AUTH_SUCCESS":
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
      };
    case "AUTH_FAILURE":
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      };
    case "LOGOUT":
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      };
    case "UPDATE_USER":
      return {
        ...state,
        user: action.payload,
      };
    default:
      return state;
  }
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    // Check for existing token on app load
    const token = localStorage.getItem("token");

    if (token) {
      // Set token first
      authService.setToken(token);
      // Start loading state
      dispatch({ type: "AUTH_START" });
      // Verify token and get user data
      refreshUser();
    } else {
      dispatch({ type: "AUTH_FAILURE" });
    }
  }, []);

  const login = async (email: string, password: string) => {
    try {
      dispatch({ type: "AUTH_START" });
      const response = await authService.login(email, password);

      localStorage.setItem("token", response.token);
      authService.setToken(response.token);

      dispatch({
        type: "AUTH_SUCCESS",
        payload: {
          user: response.user,
          token: response.token,
        },
      });
    } catch (error) {
      dispatch({ type: "AUTH_FAILURE" });
      throw error;
    }
  };

  const register = async (userData: any) => {
    try {
      dispatch({ type: "AUTH_START" });
      await authService.register(userData);
      dispatch({ type: "AUTH_FAILURE" }); // User needs to login after registration
    } catch (error) {
      dispatch({ type: "AUTH_FAILURE" });
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem("token");
    authService.removeToken();
    dispatch({ type: "LOGOUT" });
  };

  const refreshUser = async () => {
    try {
      const response = await authService.getCurrentUser();
      const token = localStorage.getItem("token");

      // Extract user data from the response
      // The API returns { user: { ... } } but we need the user object directly
      const user = (response as any).user || response;

      // Validate that we have the required user data
      if (!user.email || !user.companies) {
        throw new Error("Invalid user data received from API");
      }

      dispatch({
        type: "AUTH_SUCCESS",
        payload: {
          user,
          token: token || "",
        },
      });
    } catch (error) {
      // Token is invalid, logout user
      logout();
    }
  };

  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
