export type SalesOrderStatus = 
  | 'DRAFT'
  | 'CONFIRMED'
  | 'IN_PROGRESS'
  | 'SHIPPED'
  | 'DELIVERED'
  | 'COMPLETED'
  | 'CANCELLED';

export interface SalesOrderLineItem {
  id?: string;
  salesOrderId?: string;
  lineNumber: number;
  inventoryItemId?: string;
  inventoryItemName?: string;
  inventoryItemSku?: string;
  itemCode?: string;
  description: string;
  unitOfMeasure?: string;
  quantityOrdered: number;
  quantityShipped: number;
  quantityDelivered: number;
  unitPrice: number;
  discountPercentage: number;
  discountAmount: number;
  lineTotal: number;
  taxRate: number;
  taxAmount: number;
  taxCode?: string;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface SalesOrderAttachment {
  id: string;
  salesOrderId: string;
  fileName: string;
  filePath: string;
  fileType?: string;
  fileSize?: number;
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface SalesOrderHistory {
  id: string;
  salesOrderId: string;
  oldStatus?: SalesOrderStatus;
  newStatus: SalesOrderStatus;
  oldAmount?: number;
  newAmount?: number;
  notes?: string;
  changedBy: string;
  changedByName?: string;
  changedAt: string;
}

export interface SalesOrderComment {
  id: string;
  salesOrderId: string;
  comment: string;
  isInternal: boolean;
  createdBy: string;
  createdByName?: string;
  createdAt: string;
}

export interface SalesOrderShipment {
  id: string;
  salesOrderId: string;
  shipmentNumber: string;
  trackingNumber?: string;
  shippingCarrier?: string;
  shippingMethod?: string;
  shippedDate: string;
  estimatedDeliveryDate?: string;
  actualDeliveryDate?: string;
  shippingCost: number;
  notes?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface SalesOrder {
  id: string;
  companyId: string;
  customerId: string;
  customerName?: string;
  customerDisplayName?: string;
  customerEmail?: string;
  customerPhone?: string;
  customerAddress?: string;
  salesOrderNumber: string;
  orderDate: string;
  deliveryDate?: string;
  currency: string;
  exchangeRate: number;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  shippingAmount: number;
  totalAmount: number;
  status: SalesOrderStatus;
  billingAddress?: string;
  shippingAddress?: string;
  deliveryInstructions?: string;
  notes?: string;
  termsAndConditions?: string;
  
  // Source tracking
  estimateId?: string;
  convertedFromEstimateAt?: string;
  
  // Delivery tracking
  shippedAt?: string;
  deliveredAt?: string;
  trackingNumber?: string;
  shippingCarrier?: string;
  
  // Audit fields
  createdBy: string;
  createdByName?: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
  
  // Related data
  lineItems?: SalesOrderLineItem[];
  attachments?: SalesOrderAttachment[];
  history?: SalesOrderHistory[];
  comments?: SalesOrderComment[];
  shipments?: SalesOrderShipment[];
}

export interface CreateSalesOrderRequest {
  companyId: string;
  customerId: string;
  orderDate: string;
  deliveryDate?: string;
  currency?: string;
  exchangeRate?: number;
  shippingAmount?: number;
  billingAddress?: string;
  shippingAddress?: string;
  deliveryInstructions?: string;
  notes?: string;
  termsAndConditions?: string;
  estimateId?: string;
  lineItems: Omit<SalesOrderLineItem, 'id' | 'salesOrderId' | 'lineNumber' | 'quantityShipped' | 'quantityDelivered' | 'discountAmount' | 'lineTotal' | 'taxAmount' | 'createdAt' | 'updatedAt'>[];
}

export interface UpdateSalesOrderRequest {
  customerId?: string;
  orderDate?: string;
  deliveryDate?: string;
  currency?: string;
  exchangeRate?: number;
  shippingAmount?: number;
  billingAddress?: string;
  shippingAddress?: string;
  deliveryInstructions?: string;
  notes?: string;
  termsAndConditions?: string;
  lineItems?: Omit<SalesOrderLineItem, 'id' | 'salesOrderId' | 'lineNumber' | 'quantityShipped' | 'quantityDelivered' | 'discountAmount' | 'lineTotal' | 'taxAmount' | 'createdAt' | 'updatedAt'>[];
}

export interface SalesOrderFilters {
  status?: SalesOrderStatus;
  customerId?: string;
  dateFrom?: string;
  dateTo?: string;
  deliveryDateFrom?: string;
  deliveryDateTo?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface SalesOrderListResponse {
  data: SalesOrder[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface UpdateSalesOrderStatusRequest {
  status: SalesOrderStatus;
  notes?: string;
}

export interface CreateShipmentRequest {
  trackingNumber?: string;
  shippingCarrier?: string;
  shippingMethod?: string;
  shippedDate: string;
  estimatedDeliveryDate?: string;
  shippingCost?: number;
  notes?: string;
  lineItems: Array<{
    salesOrderLineItemId: string;
    quantityShipped: number;
  }>;
}

// Status display helpers
export const SalesOrderStatusLabels: Record<SalesOrderStatus, string> = {
  DRAFT: 'Draft',
  CONFIRMED: 'Confirmed',
  IN_PROGRESS: 'In Progress',
  SHIPPED: 'Shipped',
  DELIVERED: 'Delivered',
  COMPLETED: 'Completed',
  CANCELLED: 'Cancelled',
};

export const SalesOrderStatusColors: Record<SalesOrderStatus, string> = {
  DRAFT: 'gray',
  CONFIRMED: 'blue',
  IN_PROGRESS: 'yellow',
  SHIPPED: 'orange',
  DELIVERED: 'green',
  COMPLETED: 'green',
  CANCELLED: 'red',
};

// Helper functions
export const canEditSalesOrder = (status: SalesOrderStatus): boolean => {
  return ['DRAFT', 'CONFIRMED'].includes(status);
};

export const canDeleteSalesOrder = (status: SalesOrderStatus): boolean => {
  return ['DRAFT', 'CANCELLED'].includes(status);
};

export const canConfirmSalesOrder = (status: SalesOrderStatus): boolean => {
  return status === 'DRAFT';
};

export const canStartProgress = (status: SalesOrderStatus): boolean => {
  return status === 'CONFIRMED';
};

export const canShipSalesOrder = (status: SalesOrderStatus): boolean => {
  return ['CONFIRMED', 'IN_PROGRESS'].includes(status);
};

export const canDeliverSalesOrder = (status: SalesOrderStatus): boolean => {
  return status === 'SHIPPED';
};

export const canCompleteSalesOrder = (status: SalesOrderStatus): boolean => {
  return status === 'DELIVERED';
};

export const canCancelSalesOrder = (status: SalesOrderStatus): boolean => {
  return !['COMPLETED', 'CANCELLED'].includes(status);
};

export const isOverdue = (salesOrder: SalesOrder): boolean => {
  if (!salesOrder.deliveryDate || ['DELIVERED', 'COMPLETED', 'CANCELLED'].includes(salesOrder.status)) {
    return false;
  }
  const deliveryDate = new Date(salesOrder.deliveryDate);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return deliveryDate < today;
};

export const getDaysOverdue = (salesOrder: SalesOrder): number => {
  if (!isOverdue(salesOrder)) return 0;
  const deliveryDate = new Date(salesOrder.deliveryDate!);
  const today = new Date();
  const diffTime = today.getTime() - deliveryDate.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

export const getNextPossibleStatuses = (currentStatus: SalesOrderStatus): SalesOrderStatus[] => {
  switch (currentStatus) {
    case 'DRAFT':
      return ['CONFIRMED', 'CANCELLED'];
    case 'CONFIRMED':
      return ['IN_PROGRESS', 'SHIPPED', 'CANCELLED'];
    case 'IN_PROGRESS':
      return ['SHIPPED', 'CANCELLED'];
    case 'SHIPPED':
      return ['DELIVERED', 'CANCELLED'];
    case 'DELIVERED':
      return ['COMPLETED'];
    case 'COMPLETED':
      return [];
    case 'CANCELLED':
      return [];
    default:
      return [];
  }
};

export const getDeliveryStatus = (salesOrder: SalesOrder): {
  status: 'on-time' | 'overdue' | 'delivered' | 'no-date';
  message: string;
  daysRemaining?: number;
} => {
  if (!salesOrder.deliveryDate) {
    return { status: 'no-date', message: 'No delivery date set' };
  }

  if (['DELIVERED', 'COMPLETED'].includes(salesOrder.status)) {
    return { status: 'delivered', message: 'Delivered' };
  }

  const deliveryDate = new Date(salesOrder.deliveryDate);
  const today = new Date();
  const diffDays = Math.ceil((deliveryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) {
    return {
      status: 'overdue',
      message: `Overdue by ${Math.abs(diffDays)} days`,
      daysRemaining: 0,
    };
  }
  
  return {
    status: 'on-time',
    message: `Due in ${diffDays} days`,
    daysRemaining: diffDays,
  };
};

export const getFulfillmentPercentage = (salesOrder: SalesOrder): number => {
  if (!salesOrder.lineItems || salesOrder.lineItems.length === 0) return 0;
  
  let totalOrdered = 0;
  let totalShipped = 0;
  
  salesOrder.lineItems.forEach(item => {
    totalOrdered += item.quantityOrdered;
    totalShipped += item.quantityShipped;
  });
  
  if (totalOrdered === 0) return 0;
  return (totalShipped / totalOrdered) * 100;
};

export const getDefaultTermsAndConditions = (): string => {
  return `Terms and Conditions:

1. Delivery will be made according to the agreed schedule.
2. Payment terms: Net 30 days from delivery date.
3. All goods remain the property of the seller until payment is received in full.
4. The buyer is responsible for inspection of goods upon delivery.
5. Any damages or discrepancies must be reported within 48 hours of delivery.

Thank you for your business!`;
};

export const calculateDeliveryDate = (orderDate: string, leadTimeDays: number = 7): string => {
  const date = new Date(orderDate);
  date.setDate(date.getDate() + leadTimeDays);
  return date.toISOString().split('T')[0];
};

export const getShippingMethods = (): Array<{ value: string; label: string; estimatedDays: number }> => {
  return [
    { value: 'STANDARD', label: 'Standard Shipping', estimatedDays: 5 },
    { value: 'EXPRESS', label: 'Express Shipping', estimatedDays: 2 },
    { value: 'OVERNIGHT', label: 'Overnight Shipping', estimatedDays: 1 },
    { value: 'PICKUP', label: 'Customer Pickup', estimatedDays: 0 },
    { value: 'DELIVERY', label: 'Local Delivery', estimatedDays: 1 },
  ];
};

export const getShippingCarriers = (): Array<{ value: string; label: string }> => {
  return [
    { value: 'FEDEX', label: 'FedEx' },
    { value: 'UPS', label: 'UPS' },
    { value: 'DHL', label: 'DHL' },
    { value: 'USPS', label: 'USPS' },
    { value: 'LOCAL', label: 'Local Courier' },
    { value: 'PICKUP', label: 'Customer Pickup' },
  ];
};
