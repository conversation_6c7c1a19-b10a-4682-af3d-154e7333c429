export type EstimateStatus = 
  | 'DRAFT'
  | 'SENT'
  | 'VIEWED'
  | 'ACCEPTED'
  | 'DECLINED'
  | 'EXPIRED'
  | 'CONVERTED'
  | 'CANCELLED';

export interface EstimateLineItem {
  id?: string;
  estimateId?: string;
  lineNumber: number;
  inventoryItemId?: string;
  inventoryItemName?: string;
  inventoryItemSku?: string;
  itemCode?: string;
  description: string;
  unitOfMeasure?: string;
  quantity: number;
  unitPrice: number;
  discountPercentage: number;
  discountAmount: number;
  lineTotal: number;
  taxRate: number;
  taxAmount: number;
  taxCode?: string;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface EstimateAttachment {
  id: string;
  estimateId: string;
  fileName: string;
  filePath: string;
  fileType?: string;
  fileSize?: number;
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface EstimateHistory {
  id: string;
  estimateId: string;
  oldStatus?: EstimateStatus;
  newStatus: EstimateStatus;
  oldAmount?: number;
  newAmount?: number;
  notes?: string;
  changedBy: string;
  changedByName?: string;
  changedAt: string;
}

export interface EstimateComment {
  id: string;
  estimateId: string;
  comment: string;
  isInternal: boolean;
  createdBy: string;
  createdByName?: string;
  createdAt: string;
}

export interface EstimateEmail {
  id: string;
  estimateId: string;
  recipientEmail: string;
  subject?: string;
  body?: string;
  sentAt: string;
  openedAt?: string;
  clickedAt?: string;
  emailStatus: 'SENT' | 'DELIVERED' | 'OPENED' | 'CLICKED' | 'BOUNCED' | 'FAILED';
  sentBy: string;
  sentByName?: string;
  createdAt: string;
}

export interface Estimate {
  id: string;
  companyId: string;
  customerId: string;
  customerName?: string;
  customerDisplayName?: string;
  customerEmail?: string;
  customerPhone?: string;
  customerAddress?: string;
  estimateNumber: string;
  estimateDate: string;
  expiryDate?: string;
  currency: string;
  exchangeRate: number;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  status: EstimateStatus;
  billingAddress?: string;
  shippingAddress?: string;
  notes?: string;
  termsAndConditions?: string;
  
  // Conversion tracking
  convertedToInvoiceId?: string;
  convertedToSalesOrderId?: string;
  convertedAt?: string;
  
  // Customer interaction tracking
  sentAt?: string;
  viewedAt?: string;
  acceptedAt?: string;
  declinedAt?: string;
  
  // Audit fields
  createdBy: string;
  createdByName?: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
  
  // Related data
  lineItems?: EstimateLineItem[];
  attachments?: EstimateAttachment[];
  history?: EstimateHistory[];
  comments?: EstimateComment[];
  emails?: EstimateEmail[];
}

export interface CreateEstimateRequest {
  companyId: string;
  customerId: string;
  estimateDate: string;
  expiryDate?: string;
  currency?: string;
  exchangeRate?: number;
  billingAddress?: string;
  shippingAddress?: string;
  notes?: string;
  termsAndConditions?: string;
  lineItems: Omit<EstimateLineItem, 'id' | 'estimateId' | 'lineNumber' | 'discountAmount' | 'lineTotal' | 'taxAmount' | 'createdAt' | 'updatedAt'>[];
}

export interface UpdateEstimateRequest {
  customerId?: string;
  estimateDate?: string;
  expiryDate?: string;
  currency?: string;
  exchangeRate?: number;
  billingAddress?: string;
  shippingAddress?: string;
  notes?: string;
  termsAndConditions?: string;
  lineItems?: Omit<EstimateLineItem, 'id' | 'estimateId' | 'lineNumber' | 'discountAmount' | 'lineTotal' | 'taxAmount' | 'createdAt' | 'updatedAt'>[];
}

export interface EstimateFilters {
  status?: EstimateStatus;
  customerId?: string;
  dateFrom?: string;
  dateTo?: string;
  expiryDateFrom?: string;
  expiryDateTo?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface EstimateListResponse {
  data: Estimate[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface UpdateEstimateStatusRequest {
  status: EstimateStatus;
  notes?: string;
}

export interface SendEstimateRequest {
  recipientEmail: string;
  subject?: string;
  body?: string;
  includeAttachments?: boolean;
}

// Status display helpers
export const EstimateStatusLabels: Record<EstimateStatus, string> = {
  DRAFT: 'Draft',
  SENT: 'Sent',
  VIEWED: 'Viewed',
  ACCEPTED: 'Accepted',
  DECLINED: 'Declined',
  EXPIRED: 'Expired',
  CONVERTED: 'Converted',
  CANCELLED: 'Cancelled',
};

export const EstimateStatusColors: Record<EstimateStatus, string> = {
  DRAFT: 'gray',
  SENT: 'blue',
  VIEWED: 'yellow',
  ACCEPTED: 'green',
  DECLINED: 'red',
  EXPIRED: 'red',
  CONVERTED: 'purple',
  CANCELLED: 'red',
};

// Helper functions
export const canEditEstimate = (status: EstimateStatus): boolean => {
  return ['DRAFT', 'SENT'].includes(status);
};

export const canDeleteEstimate = (status: EstimateStatus): boolean => {
  return ['DRAFT', 'CANCELLED'].includes(status);
};

export const canSendEstimate = (status: EstimateStatus): boolean => {
  return ['DRAFT', 'VIEWED'].includes(status);
};

export const canAcceptEstimate = (status: EstimateStatus): boolean => {
  return ['SENT', 'VIEWED'].includes(status);
};

export const canDeclineEstimate = (status: EstimateStatus): boolean => {
  return ['SENT', 'VIEWED'].includes(status);
};

export const canConvertEstimate = (status: EstimateStatus): boolean => {
  return status === 'ACCEPTED';
};

export const canCancelEstimate = (status: EstimateStatus): boolean => {
  return !['ACCEPTED', 'CONVERTED', 'CANCELLED'].includes(status);
};

export const isExpired = (estimate: Estimate): boolean => {
  if (!estimate.expiryDate || estimate.status === 'ACCEPTED' || estimate.status === 'CONVERTED') {
    return false;
  }
  const expiryDate = new Date(estimate.expiryDate);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return expiryDate < today;
};

export const getDaysUntilExpiry = (estimate: Estimate): number => {
  if (!estimate.expiryDate) return 0;
  const expiryDate = new Date(estimate.expiryDate);
  const today = new Date();
  const diffTime = expiryDate.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

export const getDaysOverdue = (estimate: Estimate): number => {
  if (!isExpired(estimate)) return 0;
  const expiryDate = new Date(estimate.expiryDate!);
  const today = new Date();
  const diffTime = today.getTime() - expiryDate.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

export const getNextPossibleStatuses = (currentStatus: EstimateStatus): EstimateStatus[] => {
  switch (currentStatus) {
    case 'DRAFT':
      return ['SENT', 'CANCELLED'];
    case 'SENT':
      return ['VIEWED', 'ACCEPTED', 'DECLINED', 'EXPIRED', 'CANCELLED'];
    case 'VIEWED':
      return ['ACCEPTED', 'DECLINED', 'EXPIRED', 'CANCELLED'];
    case 'ACCEPTED':
      return ['CONVERTED'];
    case 'DECLINED':
      return ['SENT', 'CANCELLED'];
    case 'EXPIRED':
      return ['SENT', 'CANCELLED'];
    case 'CONVERTED':
      return [];
    case 'CANCELLED':
      return [];
    default:
      return [];
  }
};

export const getEstimateValidityStatus = (estimate: Estimate): {
  status: 'valid' | 'expiring' | 'expired';
  message: string;
  daysRemaining?: number;
} => {
  if (!estimate.expiryDate) {
    return { status: 'valid', message: 'No expiry date set' };
  }

  const daysRemaining = getDaysUntilExpiry(estimate);
  
  if (daysRemaining < 0) {
    return {
      status: 'expired',
      message: `Expired ${Math.abs(daysRemaining)} days ago`,
      daysRemaining: 0,
    };
  }
  
  if (daysRemaining <= 7) {
    return {
      status: 'expiring',
      message: `Expires in ${daysRemaining} days`,
      daysRemaining,
    };
  }
  
  return {
    status: 'valid',
    message: `Valid for ${daysRemaining} more days`,
    daysRemaining,
  };
};

// Template helpers
export const getDefaultEstimateTerms = (): string => {
  return `Terms and Conditions:

1. This estimate is valid for 30 days from the date of issue.
2. Prices are subject to change without notice after expiry.
3. Payment terms: Net 30 days from invoice date.
4. All work will be completed according to specifications.
5. Additional charges may apply for changes to original scope.

Thank you for your business!`;
};

export const calculateEstimateValidityPeriod = (estimateDate: string, validityDays: number = 30): string => {
  const date = new Date(estimateDate);
  date.setDate(date.getDate() + validityDays);
  return date.toISOString().split('T')[0];
};
