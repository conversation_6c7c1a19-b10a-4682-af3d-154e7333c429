// Tax Management Types

export interface TaxRate {
  id: string;
  companyId: string;
  name: string;
  description?: string;
  rate: number | string; // Percentage (e.g., 8.25 for 8.25%) - can be string from DB
  type: TaxType;
  jurisdiction: string; // e.g., "Federal", "California", "Los Angeles County"
  jurisdictionCode?: string; // e.g., "US", "CA", "LAC"
  isActive: boolean;
  isDefault: boolean;
  effectiveDate: string;
  expiryDate?: string;
  applicableToProducts: boolean;
  applicableToServices: boolean;
  createdAt: string;
  updatedAt: string;
}

export type TaxType = 
  | "sales_tax"
  | "vat"
  | "gst"
  | "income_tax"
  | "property_tax"
  | "excise_tax"
  | "customs_duty"
  | "other";

export interface TaxCategory {
  id: string;
  companyId: string;
  name: string;
  description?: string;
  code: string; // e.g., "TAXABLE", "EXEMPT", "REDUCED"
  defaultTaxRateId?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TaxCalculation {
  id: string;
  transactionId?: string;
  invoiceId?: string;
  lineItemId?: string;
  taxRateId: string;
  taxCategoryId?: string;
  baseAmount: number;
  taxableAmount: number;
  taxAmount: number;
  taxRate: number;
  jurisdiction: string;
  calculationMethod: TaxCalculationMethod;
  createdAt: string;
}

export type TaxCalculationMethod = 
  | "inclusive" // Tax included in price
  | "exclusive" // Tax added to price
  | "compound"; // Tax on tax

export interface TaxReport {
  id: string;
  companyId: string;
  reportType: TaxReportType;
  periodStart: string;
  periodEnd: string;
  status: TaxReportStatus;
  totalSales: number;
  totalTaxCollected: number;
  totalTaxPaid: number;
  netTaxDue: number;
  filingDueDate?: string;
  filedDate?: string;
  createdAt: string;
  updatedAt: string;
  lineItems: TaxReportLineItem[];
}

export type TaxReportType = 
  | "sales_tax_return"
  | "vat_return"
  | "gst_return"
  | "tax_summary"
  | "tax_liability";

export type TaxReportStatus = 
  | "draft"
  | "pending_review"
  | "ready_to_file"
  | "filed"
  | "amended";

export interface TaxReportLineItem {
  id: string;
  taxReportId: string;
  jurisdiction: string;
  taxRateId: string;
  taxRate: number;
  taxableAmount: number;
  taxAmount: number;
  description: string;
}

export interface TaxJurisdiction {
  id: string;
  name: string;
  code: string;
  type: JurisdictionType;
  parentId?: string; // For hierarchical jurisdictions
  isActive: boolean;
  taxAuthority: string;
  filingFrequency: FilingFrequency;
  nextFilingDate?: string;
}

export type JurisdictionType = 
  | "federal"
  | "state"
  | "province"
  | "county"
  | "city"
  | "district";

export type FilingFrequency = 
  | "monthly"
  | "quarterly"
  | "annually"
  | "semi_annually";

export interface TaxSettings {
  id: string;
  companyId: string;
  defaultTaxRateId?: string;
  taxCalculationMethod: TaxCalculationMethod;
  roundingMethod: TaxRoundingMethod;
  roundingPrecision: number; // Number of decimal places
  includeTaxInPrice: boolean;
  enableTaxExemptions: boolean;
  requireTaxCertificates: boolean;
  autoCalculateTax: boolean;
  taxReportingCurrency: string;
  settings: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export type TaxRoundingMethod = 
  | "round" // Standard rounding
  | "round_up" // Always round up
  | "round_down" // Always round down
  | "truncate"; // Cut off decimal places

export interface TaxExemption {
  id: string;
  companyId: string;
  contactId: string;
  exemptionType: TaxExemptionType;
  exemptionNumber: string;
  jurisdiction: string;
  isActive: boolean;
  effectiveDate: string;
  expiryDate?: string;
  certificateUrl?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export type TaxExemptionType = 
  | "resale"
  | "nonprofit"
  | "government"
  | "manufacturing"
  | "agriculture"
  | "export"
  | "other";

// API Request/Response Types
export interface CreateTaxRateRequest {
  companyId: string;
  name: string;
  description?: string;
  rate: number;
  type: TaxType;
  jurisdiction: string;
  jurisdictionCode?: string;
  isActive?: boolean;
  isDefault?: boolean;
  effectiveDate: string;
  expiryDate?: string;
  applicableToProducts?: boolean;
  applicableToServices?: boolean;
}

export interface UpdateTaxRateRequest {
  name?: string;
  description?: string;
  rate?: number;
  type?: TaxType;
  jurisdiction?: string;
  jurisdictionCode?: string;
  isActive?: boolean;
  isDefault?: boolean;
  effectiveDate?: string;
  expiryDate?: string;
  applicableToProducts?: boolean;
  applicableToServices?: boolean;
}

export interface CreateTaxCategoryRequest {
  companyId: string;
  name: string;
  description?: string;
  code: string;
  defaultTaxRateId?: string;
  isActive?: boolean;
}

export interface UpdateTaxCategoryRequest {
  name?: string;
  description?: string;
  code?: string;
  defaultTaxRateId?: string;
  isActive?: boolean;
}

export interface TaxCalculationRequest {
  amount: number;
  taxRateId?: string;
  taxCategoryId?: string;
  jurisdiction?: string;
  calculationMethod?: TaxCalculationMethod;
  includeTax?: boolean;
}

export interface TaxCalculationResponse {
  baseAmount: number;
  taxableAmount: number;
  taxAmount: number;
  totalAmount: number;
  taxRate: number;
  jurisdiction: string;
  calculations: TaxCalculation[];
}

export interface TaxReportFilters {
  reportType?: TaxReportType;
  status?: TaxReportStatus;
  periodStart?: string;
  periodEnd?: string;
  jurisdiction?: string;
}

export interface TaxComplianceStatus {
  companyId: string;
  jurisdiction: string;
  isCompliant: boolean;
  nextFilingDate?: string;
  overdueReturns: number;
  pendingReturns: number;
  lastFiledDate?: string;
  issues: TaxComplianceIssue[];
}

export interface TaxComplianceIssue {
  id: string;
  type: "overdue_return" | "missing_data" | "calculation_error" | "exemption_expired";
  severity: "low" | "medium" | "high" | "critical";
  description: string;
  dueDate?: string;
  actionRequired: string;
}
