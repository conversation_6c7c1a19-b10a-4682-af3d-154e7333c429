export type BillStatus = 
  | 'DRAFT'
  | 'PENDING_APPROVAL'
  | 'APPROVED'
  | 'OVERDUE'
  | 'PAID'
  | 'PARTIALLY_PAID'
  | 'CANCELLED';

export type PaymentMethod = 
  | 'CASH'
  | 'CHECK'
  | 'BANK_TRANSFER'
  | 'CREDIT_CARD'
  | 'ACH'
  | 'WIRE_TRANSFER'
  | 'OTHER';

export interface BillLineItem {
  id?: string;
  billId?: string;
  lineNumber: number;
  accountId?: string;
  accountName?: string;
  accountCode?: string;
  inventoryItemId?: string;
  inventoryItemName?: string;
  inventoryItemSku?: string;
  itemCode?: string;
  description: string;
  unitOfMeasure?: string;
  quantity: number;
  unitCost: number;
  discountPercentage: number;
  discountAmount: number;
  lineTotal: number;
  taxRate: number;
  taxAmount: number;
  taxCode?: string;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface BillAttachment {
  id: string;
  billId: string;
  fileName: string;
  filePath: string;
  fileType?: string;
  fileSize?: number;
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface BillPayment {
  id: string;
  billId: string;
  paymentTransactionId?: string;
  paymentDate: string;
  paymentAmount: number;
  paymentMethod: PaymentMethod;
  paymentReference?: string;
  notes?: string;
  createdBy: string;
  createdByName?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BillHistory {
  id: string;
  billId: string;
  oldStatus?: BillStatus;
  newStatus: BillStatus;
  oldAmount?: number;
  newAmount?: number;
  notes?: string;
  changedBy: string;
  changedByName?: string;
  changedAt: string;
}

export interface Bill {
  id: string;
  companyId: string;
  transactionId: string;
  vendorId: string;
  vendorName?: string;
  vendorDisplayName?: string;
  vendorEmail?: string;
  vendorPhone?: string;
  vendorBillingAddress?: string;
  billNumber: string;
  vendorInvoiceNumber?: string;
  billDate: string;
  dueDate?: string;
  paymentTerms?: string;
  currency: string;
  exchangeRate: number;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  paidAmount: number;
  balanceDue: number;
  status: BillStatus;
  approvalStatus?: string;
  approvedBy?: string;
  approvedByName?: string;
  approvedAt?: string;
  purchaseOrderId?: string;
  billingAddress?: string;
  shippingAddress?: string;
  notes?: string;
  termsAndConditions?: string;
  createdBy: string;
  createdByName?: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
  
  // Related data
  lineItems?: BillLineItem[];
  attachments?: BillAttachment[];
  payments?: BillPayment[];
  history?: BillHistory[];
}

export interface CreateBillRequest {
  companyId: string;
  vendorId: string;
  vendorInvoiceNumber?: string;
  billDate: string;
  dueDate?: string;
  paymentTerms?: string;
  currency?: string;
  exchangeRate?: number;
  billingAddress?: string;
  shippingAddress?: string;
  notes?: string;
  termsAndConditions?: string;
  purchaseOrderId?: string;
  lineItems: Omit<BillLineItem, 'id' | 'billId' | 'lineNumber' | 'discountAmount' | 'lineTotal' | 'taxAmount' | 'createdAt' | 'updatedAt'>[];
}

export interface UpdateBillRequest {
  vendorId?: string;
  vendorInvoiceNumber?: string;
  billDate?: string;
  dueDate?: string;
  paymentTerms?: string;
  currency?: string;
  exchangeRate?: number;
  billingAddress?: string;
  shippingAddress?: string;
  notes?: string;
  termsAndConditions?: string;
  lineItems?: Omit<BillLineItem, 'id' | 'billId' | 'lineNumber' | 'discountAmount' | 'lineTotal' | 'taxAmount' | 'createdAt' | 'updatedAt'>[];
}

export interface BillFilters {
  status?: BillStatus;
  vendorId?: string;
  dateFrom?: string;
  dateTo?: string;
  dueDateFrom?: string;
  dueDateTo?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface BillListResponse {
  data: Bill[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface UpdateBillStatusRequest {
  status: BillStatus;
  notes?: string;
}

export interface PayBillRequest {
  paymentDate: string;
  paymentAmount: number;
  paymentMethod: PaymentMethod;
  paymentReference?: string;
  notes?: string;
}

// Status display helpers
export const BillStatusLabels: Record<BillStatus, string> = {
  DRAFT: 'Draft',
  PENDING_APPROVAL: 'Pending Approval',
  APPROVED: 'Approved',
  OVERDUE: 'Overdue',
  PAID: 'Paid',
  PARTIALLY_PAID: 'Partially Paid',
  CANCELLED: 'Cancelled',
};

export const BillStatusColors: Record<BillStatus, string> = {
  DRAFT: 'gray',
  PENDING_APPROVAL: 'yellow',
  APPROVED: 'blue',
  OVERDUE: 'red',
  PAID: 'green',
  PARTIALLY_PAID: 'orange',
  CANCELLED: 'red',
};

export const PaymentMethodLabels: Record<PaymentMethod, string> = {
  CASH: 'Cash',
  CHECK: 'Check',
  BANK_TRANSFER: 'Bank Transfer',
  CREDIT_CARD: 'Credit Card',
  ACH: 'ACH',
  WIRE_TRANSFER: 'Wire Transfer',
  OTHER: 'Other',
};

// Helper functions
export const canEditBill = (status: BillStatus): boolean => {
  return !['PAID', 'CANCELLED'].includes(status);
};

export const canDeleteBill = (status: BillStatus): boolean => {
  return ['DRAFT', 'CANCELLED'].includes(status);
};

export const canApproveBill = (status: BillStatus): boolean => {
  return status === 'PENDING_APPROVAL';
};

export const canPayBill = (status: BillStatus): boolean => {
  return ['APPROVED', 'OVERDUE', 'PARTIALLY_PAID'].includes(status);
};

export const canCancelBill = (status: BillStatus): boolean => {
  return !['PAID', 'CANCELLED'].includes(status);
};

export const isOverdue = (bill: Bill): boolean => {
  if (!bill.dueDate || bill.status === 'PAID') return false;
  const dueDate = new Date(bill.dueDate);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return dueDate < today && bill.balanceDue > 0;
};

export const getDaysOverdue = (bill: Bill): number => {
  if (!isOverdue(bill)) return 0;
  const dueDate = new Date(bill.dueDate!);
  const today = new Date();
  const diffTime = today.getTime() - dueDate.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

export const getNextPossibleStatuses = (currentStatus: BillStatus): BillStatus[] => {
  switch (currentStatus) {
    case 'DRAFT':
      return ['PENDING_APPROVAL', 'APPROVED', 'CANCELLED'];
    case 'PENDING_APPROVAL':
      return ['APPROVED', 'DRAFT', 'CANCELLED'];
    case 'APPROVED':
      return ['PAID', 'PARTIALLY_PAID', 'CANCELLED'];
    case 'OVERDUE':
      return ['PAID', 'PARTIALLY_PAID', 'CANCELLED'];
    case 'PARTIALLY_PAID':
      return ['PAID', 'CANCELLED'];
    case 'PAID':
      return [];
    case 'CANCELLED':
      return [];
    default:
      return [];
  }
};
