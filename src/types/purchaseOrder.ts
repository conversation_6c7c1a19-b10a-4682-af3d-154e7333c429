export type PurchaseOrderStatus = 
  | 'DRAFT'
  | 'PENDING_APPROVAL'
  | 'APPROVED'
  | 'SENT'
  | 'PARTIALLY_RECEIVED'
  | 'FULLY_RECEIVED'
  | 'CANCELLED'
  | 'CLOSED';

export interface PurchaseOrderLineItem {
  id?: string;
  purchaseOrderId?: string;
  lineNumber: number;
  inventoryItemId?: string;
  inventoryItemName?: string;
  inventoryItemSku?: string;
  itemCode?: string;
  description: string;
  unitOfMeasure?: string;
  quantityOrdered: number;
  quantityReceived: number;
  quantityBilled: number;
  unitCost: number;
  discountPercentage: number;
  discountAmount: number;
  lineTotal: number;
  taxRate: number;
  taxAmount: number;
  expectedDeliveryDate?: string;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface PurchaseOrderAttachment {
  id: string;
  purchaseOrderId: string;
  fileName: string;
  filePath: string;
  fileType?: string;
  fileSize?: number;
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface PurchaseOrderHistory {
  id: string;
  purchaseOrderId: string;
  oldStatus?: PurchaseOrderStatus;
  newStatus: PurchaseOrderStatus;
  notes?: string;
  changedBy: string;
  changedByName?: string;
  changedAt: string;
}

export interface PurchaseOrder {
  id: string;
  companyId: string;
  poNumber: string;
  vendorId: string;
  vendorName?: string;
  vendorDisplayName?: string;
  vendorEmail?: string;
  vendorPhone?: string;
  vendorBillingAddress?: string;
  poDate: string;
  expectedDeliveryDate?: string;
  deliveryDate?: string;
  status: PurchaseOrderStatus;
  approvedBy?: string;
  approvedAt?: string;
  currency: string;
  exchangeRate: number;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  billingAddress?: string;
  shippingAddress?: string;
  notes?: string;
  termsAndConditions?: string;
  referenceNumber?: string;
  createdBy: string;
  createdByName?: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
  
  // Related data
  lineItems?: PurchaseOrderLineItem[];
  attachments?: PurchaseOrderAttachment[];
  history?: PurchaseOrderHistory[];
}

export interface CreatePurchaseOrderRequest {
  companyId: string;
  vendorId: string;
  poDate: string;
  expectedDeliveryDate?: string;
  currency?: string;
  exchangeRate?: number;
  billingAddress?: string;
  shippingAddress?: string;
  notes?: string;
  termsAndConditions?: string;
  referenceNumber?: string;
  lineItems: Omit<PurchaseOrderLineItem, 'id' | 'purchaseOrderId' | 'lineNumber' | 'quantityReceived' | 'quantityBilled' | 'discountAmount' | 'lineTotal' | 'taxAmount' | 'createdAt' | 'updatedAt'>[];
}

export interface UpdatePurchaseOrderRequest {
  vendorId?: string;
  poDate?: string;
  expectedDeliveryDate?: string;
  currency?: string;
  exchangeRate?: number;
  billingAddress?: string;
  shippingAddress?: string;
  notes?: string;
  termsAndConditions?: string;
  referenceNumber?: string;
  lineItems?: Omit<PurchaseOrderLineItem, 'id' | 'purchaseOrderId' | 'lineNumber' | 'quantityReceived' | 'quantityBilled' | 'discountAmount' | 'lineTotal' | 'taxAmount' | 'createdAt' | 'updatedAt'>[];
}

export interface PurchaseOrderFilters {
  status?: PurchaseOrderStatus;
  vendorId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface PurchaseOrderListResponse {
  data: PurchaseOrder[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface UpdatePurchaseOrderStatusRequest {
  status: PurchaseOrderStatus;
  notes?: string;
}

// Status display helpers
export const PurchaseOrderStatusLabels: Record<PurchaseOrderStatus, string> = {
  DRAFT: 'Draft',
  PENDING_APPROVAL: 'Pending Approval',
  APPROVED: 'Approved',
  SENT: 'Sent',
  PARTIALLY_RECEIVED: 'Partially Received',
  FULLY_RECEIVED: 'Fully Received',
  CANCELLED: 'Cancelled',
  CLOSED: 'Closed',
};

export const PurchaseOrderStatusColors: Record<PurchaseOrderStatus, string> = {
  DRAFT: 'gray',
  PENDING_APPROVAL: 'yellow',
  APPROVED: 'blue',
  SENT: 'purple',
  PARTIALLY_RECEIVED: 'orange',
  FULLY_RECEIVED: 'green',
  CANCELLED: 'red',
  CLOSED: 'gray',
};

// Helper functions
export const canEditPurchaseOrder = (status: PurchaseOrderStatus): boolean => {
  return !['FULLY_RECEIVED', 'CANCELLED', 'CLOSED'].includes(status);
};

export const canDeletePurchaseOrder = (status: PurchaseOrderStatus): boolean => {
  return ['DRAFT', 'CANCELLED'].includes(status);
};

export const canApprovePurchaseOrder = (status: PurchaseOrderStatus): boolean => {
  return status === 'PENDING_APPROVAL';
};

export const canSendPurchaseOrder = (status: PurchaseOrderStatus): boolean => {
  return status === 'APPROVED';
};

export const canReceivePurchaseOrder = (status: PurchaseOrderStatus): boolean => {
  return ['SENT', 'PARTIALLY_RECEIVED'].includes(status);
};

export const canCancelPurchaseOrder = (status: PurchaseOrderStatus): boolean => {
  return !['FULLY_RECEIVED', 'CANCELLED', 'CLOSED'].includes(status);
};

export const getNextPossibleStatuses = (currentStatus: PurchaseOrderStatus): PurchaseOrderStatus[] => {
  switch (currentStatus) {
    case 'DRAFT':
      return ['PENDING_APPROVAL', 'APPROVED', 'CANCELLED'];
    case 'PENDING_APPROVAL':
      return ['APPROVED', 'DRAFT', 'CANCELLED'];
    case 'APPROVED':
      return ['SENT', 'CANCELLED'];
    case 'SENT':
      return ['PARTIALLY_RECEIVED', 'FULLY_RECEIVED', 'CANCELLED'];
    case 'PARTIALLY_RECEIVED':
      return ['FULLY_RECEIVED', 'CANCELLED'];
    case 'FULLY_RECEIVED':
      return ['CLOSED'];
    case 'CANCELLED':
      return [];
    case 'CLOSED':
      return [];
    default:
      return [];
  }
};
