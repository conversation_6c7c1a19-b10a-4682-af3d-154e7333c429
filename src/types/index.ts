// User and Authentication Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: Role;
  companies: UserCompany[];
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: string[];
  isActive: boolean;
}

export interface UserCompany {
  id: string;
  name: string;
  baseCurrency: string;
  roleId: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Company Types
export interface Company {
  id: string;
  name: string;
  legalName?: string;
  taxId?: string;
  registrationNumber?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  phone?: string;
  email?: string;
  website?: string;
  baseCurrency: string;
  settings: Record<string, any>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Account Types
export type AccountType =
  | "ASSET"
  | "LIABILITY"
  | "EQUITY"
  | "REVENUE"
  | "EXPENSE";

export type AccountSubtype =
  | "CURRENT_ASSET"
  | "NON_CURRENT_ASSET"
  | "CURRENT_LIABILITY"
  | "NON_CURRENT_LIABILITY"
  | "OWNER_EQUITY"
  | "RETAINED_EARNINGS"
  | "OPERATING_REVENUE"
  | "NON_OPERATING_REVENUE"
  | "OPERATING_EXPENSE"
  | "NON_OPERATING_EXPENSE"
  | "COST_OF_GOODS_SOLD";

export interface Account {
  id: string;
  companyId: string;
  code: string;
  name: string;
  description?: string;
  accountType: AccountType;
  accountSubtype?: AccountSubtype;
  parentAccountId?: string;
  parentAccountName?: string;
  isActive: boolean;
  isSystem: boolean;
  openingBalance: number;
  currency: string;
  taxSettings: Record<string, any>;
  metadata: Record<string, any>;
  children?: Account[];
  currentBalance?: AccountBalance;
  createdAt: string;
  updatedAt: string;
}

export interface AccountBalance {
  id: string;
  accountId: string;
  balanceDate: string;
  debitBalance: number;
  creditBalance: number;
  netBalance: number;
  createdAt: string;
  updatedAt: string;
}

// Transaction Types
export type TransactionStatus =
  | "DRAFT"
  | "PENDING"
  | "APPROVED"
  | "POSTED"
  | "CANCELLED"
  | "REVERSED";

export interface Transaction {
  id: string;
  companyId: string;
  transactionNumber: string;
  transactionDate: string;
  description: string;
  reference?: string;
  status: TransactionStatus;
  totalAmount: number;
  currency: string;
  exchangeRate: number;
  createdBy: string;
  approvedBy?: string;
  approvedAt?: string;
  reversedBy?: string;
  reversedAt?: string;
  reversalTransactionId?: string;
  metadata: Record<string, any>;
  entries: TransactionEntry[];
  createdAt: string;
  updatedAt: string;
}

export interface TransactionEntry {
  id: string;
  transactionId: string;
  accountId: string;
  account?: Account;
  accountCode?: string; // Added for backend compatibility
  accountName?: string; // Added for backend compatibility
  description?: string;
  debitAmount: number;
  creditAmount: number;
  currency: string;
  exchangeRate: number;
  baseDebitAmount: number;
  baseCreditAmount: number;
  lineNumber: number;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// Contact Types
export type ContactType = "CUSTOMER" | "VENDOR" | "EMPLOYEE" | "OTHER";

export interface Contact {
  id: string;
  companyId: string;
  contactNumber: string;
  contactType: ContactType;
  name: string;
  displayName?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  website?: string;
  taxId?: string;
  billingAddress?: string;
  billingCity?: string;
  billingState?: string;
  billingPostalCode?: string;
  billingCountry?: string;
  shippingAddress?: string;
  shippingCity?: string;
  shippingState?: string;
  shippingPostalCode?: string;
  shippingCountry?: string;
  currency: string;
  paymentTerms?: string;
  creditLimit: number;
  isActive: boolean;
  customFields: Record<string, any>;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// Invoice Types
export type InvoiceType = "SALES" | "PURCHASE";
export type InvoiceStatus = "DRAFT" | "SENT" | "PAID" | "OVERDUE" | "CANCELLED";

export interface Invoice {
  id: string;
  companyId: string;
  invoiceNumber: string;
  contactId: string;
  contact?: Contact;
  // Contact fields from JOIN query (camelCase after humps transformation)
  contactName?: string;
  contactDisplayName?: string;
  contactNumber?: string;
  contactEmail?: string;
  invoiceType: InvoiceType;
  invoiceDate: string;
  dueDate: string;
  status: InvoiceStatus;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  paidAmount: number;
  balanceDue: number;
  currency: string;
  exchangeRate: number;
  termsAndConditions?: string;
  notes?: string;
  createdBy: string;
  transactionId?: string; // Link to journal entry transaction
  lineItems: InvoiceLineItem[];
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceLineItem {
  id: string;
  invoiceId: string;
  lineNumber: number;
  itemCode?: string;
  description: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  discountPercent: number;
  discountAmount: number;
  lineTotal: number;
  accountId: string;
  account?: Account;
  taxDetails: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// API Response Types
export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
  status: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Form Types - Updated for cache busting
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

// Notification Types
export type NotificationType = "success" | "error" | "warning" | "info";

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  duration?: number;
  createdAt: string;
}
