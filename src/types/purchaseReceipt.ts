export type PurchaseReceiptStatus = 
  | 'DRAFT'
  | 'RECEIVED'
  | 'INSPECTED'
  | 'ACCEPTED'
  | 'REJECTED'
  | 'PARTIALLY_ACCEPTED';

export type ItemCondition = 
  | 'GOOD'
  | 'DAMAGED'
  | 'DEFECTIVE'
  | 'INCOMPLETE'
  | 'WRONG_ITEM';

export type AttachmentType = 
  | 'DOCUMENT'
  | 'PHOTO'
  | 'DELIVERY_NOTE'
  | 'INSPECTION_REPORT'
  | 'OTHER';

export interface PurchaseReceiptLineItem {
  id?: string;
  purchaseReceiptId?: string;
  purchaseOrderLineItemId?: string;
  lineNumber: number;
  inventoryItemId?: string;
  inventoryItemName?: string;
  inventoryItemSku?: string;
  itemCode?: string;
  description: string;
  unitOfMeasure?: string;
  quantityOrdered: number;
  quantityReceived: number;
  quantityAccepted: number;
  quantityRejected: number;
  conditionOnArrival: ItemCondition;
  inspectionNotes?: string;
  batchNumber?: string;
  serialNumbers?: string;
  expiryDate?: string;
  location?: string;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface PurchaseReceiptAttachment {
  id: string;
  purchaseReceiptId: string;
  fileName: string;
  filePath: string;
  fileType?: string;
  fileSize?: number;
  attachmentType: AttachmentType;
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface PurchaseReceiptHistory {
  id: string;
  purchaseReceiptId: string;
  oldStatus?: PurchaseReceiptStatus;
  newStatus: PurchaseReceiptStatus;
  notes?: string;
  changedBy: string;
  changedByName?: string;
  changedAt: string;
}

export interface PurchaseReceiptComment {
  id: string;
  purchaseReceiptId: string;
  comment: string;
  isInternal: boolean;
  createdBy: string;
  createdByName?: string;
  createdAt: string;
}

export interface PurchaseReceipt {
  id: string;
  companyId: string;
  vendorId: string;
  vendorName?: string;
  vendorEmail?: string;
  vendorPhone?: string;
  vendorAddress?: string;
  purchaseOrderId?: string;
  receiptNumber: string;
  receiptDate: string;
  deliveryNoteNumber?: string;
  currency: string;
  exchangeRate: number;
  status: PurchaseReceiptStatus;
  deliveryAddress?: string;
  deliveryInstructions?: string;
  receivedBy?: string;
  inspectionNotes?: string;
  notes?: string;
  
  // Quality control
  qualityCheckPassed?: boolean;
  qualityCheckNotes?: string;
  qualityCheckedBy?: string;
  qualityCheckedByName?: string;
  qualityCheckedAt?: string;
  
  // Audit fields
  createdBy: string;
  createdByName?: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
  
  // Related data
  lineItems?: PurchaseReceiptLineItem[];
  attachments?: PurchaseReceiptAttachment[];
  history?: PurchaseReceiptHistory[];
  comments?: PurchaseReceiptComment[];
}

export interface CreatePurchaseReceiptRequest {
  companyId: string;
  vendorId: string;
  purchaseOrderId?: string;
  receiptDate: string;
  deliveryNoteNumber?: string;
  currency?: string;
  exchangeRate?: number;
  deliveryAddress?: string;
  deliveryInstructions?: string;
  receivedBy?: string;
  inspectionNotes?: string;
  notes?: string;
  lineItems: Omit<PurchaseReceiptLineItem, 'id' | 'purchaseReceiptId' | 'lineNumber' | 'createdAt' | 'updatedAt'>[];
}

export interface UpdatePurchaseReceiptRequest {
  vendorId?: string;
  receiptDate?: string;
  deliveryNoteNumber?: string;
  currency?: string;
  exchangeRate?: number;
  deliveryAddress?: string;
  deliveryInstructions?: string;
  receivedBy?: string;
  inspectionNotes?: string;
  notes?: string;
  lineItems?: Omit<PurchaseReceiptLineItem, 'id' | 'purchaseReceiptId' | 'lineNumber' | 'createdAt' | 'updatedAt'>[];
}

export interface PurchaseReceiptFilters {
  status?: PurchaseReceiptStatus;
  vendorId?: string;
  purchaseOrderId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface PurchaseReceiptListResponse {
  data: PurchaseReceipt[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface UpdatePurchaseReceiptStatusRequest {
  status: PurchaseReceiptStatus;
  notes?: string;
}

export interface QualityCheckRequest {
  passed: boolean;
  notes?: string;
  lineItemUpdates?: Array<{
    lineItemId: string;
    quantityAccepted: number;
    quantityRejected: number;
    conditionOnArrival: ItemCondition;
    inspectionNotes?: string;
  }>;
}

// Status display helpers
export const PurchaseReceiptStatusLabels: Record<PurchaseReceiptStatus, string> = {
  DRAFT: 'Draft',
  RECEIVED: 'Received',
  INSPECTED: 'Inspected',
  ACCEPTED: 'Accepted',
  REJECTED: 'Rejected',
  PARTIALLY_ACCEPTED: 'Partially Accepted',
};

export const PurchaseReceiptStatusColors: Record<PurchaseReceiptStatus, string> = {
  DRAFT: 'gray',
  RECEIVED: 'blue',
  INSPECTED: 'yellow',
  ACCEPTED: 'green',
  REJECTED: 'red',
  PARTIALLY_ACCEPTED: 'orange',
};

export const ItemConditionLabels: Record<ItemCondition, string> = {
  GOOD: 'Good',
  DAMAGED: 'Damaged',
  DEFECTIVE: 'Defective',
  INCOMPLETE: 'Incomplete',
  WRONG_ITEM: 'Wrong Item',
};

export const ItemConditionColors: Record<ItemCondition, string> = {
  GOOD: 'green',
  DAMAGED: 'orange',
  DEFECTIVE: 'red',
  INCOMPLETE: 'yellow',
  WRONG_ITEM: 'red',
};

export const AttachmentTypeLabels: Record<AttachmentType, string> = {
  DOCUMENT: 'Document',
  PHOTO: 'Photo',
  DELIVERY_NOTE: 'Delivery Note',
  INSPECTION_REPORT: 'Inspection Report',
  OTHER: 'Other',
};

// Helper functions
export const canEditPurchaseReceipt = (status: PurchaseReceiptStatus): boolean => {
  return ['DRAFT', 'RECEIVED'].includes(status);
};

export const canDeletePurchaseReceipt = (status: PurchaseReceiptStatus): boolean => {
  return status === 'DRAFT';
};

export const canInspectPurchaseReceipt = (status: PurchaseReceiptStatus): boolean => {
  return ['RECEIVED', 'INSPECTED'].includes(status);
};

export const canAcceptPurchaseReceipt = (status: PurchaseReceiptStatus): boolean => {
  return ['INSPECTED'].includes(status);
};

export const canRejectPurchaseReceipt = (status: PurchaseReceiptStatus): boolean => {
  return ['INSPECTED'].includes(status);
};

export const getNextPossibleStatuses = (currentStatus: PurchaseReceiptStatus): PurchaseReceiptStatus[] => {
  switch (currentStatus) {
    case 'DRAFT':
      return ['RECEIVED'];
    case 'RECEIVED':
      return ['INSPECTED'];
    case 'INSPECTED':
      return ['ACCEPTED', 'REJECTED', 'PARTIALLY_ACCEPTED'];
    case 'ACCEPTED':
    case 'REJECTED':
    case 'PARTIALLY_ACCEPTED':
      return [];
    default:
      return [];
  }
};

export const getReceiptCompletionPercentage = (receipt: PurchaseReceipt): number => {
  if (!receipt.lineItems || receipt.lineItems.length === 0) return 0;
  
  let totalOrdered = 0;
  let totalReceived = 0;
  
  receipt.lineItems.forEach(item => {
    totalOrdered += item.quantityOrdered;
    totalReceived += item.quantityReceived;
  });
  
  if (totalOrdered === 0) return 0;
  return (totalReceived / totalOrdered) * 100;
};

export const getAcceptancePercentage = (receipt: PurchaseReceipt): number => {
  if (!receipt.lineItems || receipt.lineItems.length === 0) return 0;
  
  let totalReceived = 0;
  let totalAccepted = 0;
  
  receipt.lineItems.forEach(item => {
    totalReceived += item.quantityReceived;
    totalAccepted += item.quantityAccepted;
  });
  
  if (totalReceived === 0) return 0;
  return (totalAccepted / totalReceived) * 100;
};

export const hasQualityIssues = (receipt: PurchaseReceipt): boolean => {
  if (!receipt.lineItems) return false;
  
  return receipt.lineItems.some(item => 
    item.conditionOnArrival !== 'GOOD' || 
    item.quantityRejected > 0
  );
};

export const getQualityIssuesSummary = (receipt: PurchaseReceipt): string[] => {
  if (!receipt.lineItems) return [];
  
  const issues: string[] = [];
  
  receipt.lineItems.forEach(item => {
    if (item.conditionOnArrival !== 'GOOD') {
      issues.push(`${item.description}: ${ItemConditionLabels[item.conditionOnArrival]}`);
    }
    if (item.quantityRejected > 0) {
      issues.push(`${item.description}: ${item.quantityRejected} items rejected`);
    }
  });
  
  return issues;
};

export const getDefaultDeliveryInstructions = (): string => {
  return `Delivery Instructions:

1. Please deliver during business hours (8 AM - 5 PM).
2. Contact the receiving department upon arrival.
3. Ensure all items are properly packaged and labeled.
4. Provide delivery note and any relevant documentation.
5. Wait for inspection and sign-off before leaving.

Thank you for your cooperation!`;
};

export const getQualityCheckGuidelines = (): string => {
  return `Quality Check Guidelines:

1. Verify item quantities against purchase order.
2. Check item condition and packaging.
3. Inspect for damage, defects, or missing components.
4. Verify item specifications and model numbers.
5. Check expiry dates for applicable items.
6. Document any discrepancies or issues.
7. Take photos of damaged or defective items.
8. Complete inspection within 24 hours of receipt.

Report any issues immediately to procurement team.`;
};

export const getReceiptStatuses = (): Array<{ value: PurchaseReceiptStatus; label: string; description: string }> => {
  return [
    { value: 'DRAFT', label: 'Draft', description: 'Receipt is being prepared' },
    { value: 'RECEIVED', label: 'Received', description: 'Items have been received' },
    { value: 'INSPECTED', label: 'Inspected', description: 'Items have been inspected' },
    { value: 'ACCEPTED', label: 'Accepted', description: 'All items accepted' },
    { value: 'REJECTED', label: 'Rejected', description: 'Items rejected' },
    { value: 'PARTIALLY_ACCEPTED', label: 'Partially Accepted', description: 'Some items accepted, some rejected' },
  ];
};

export const getItemConditions = (): Array<{ value: ItemCondition; label: string; description: string }> => {
  return [
    { value: 'GOOD', label: 'Good', description: 'Item is in good condition' },
    { value: 'DAMAGED', label: 'Damaged', description: 'Item has physical damage' },
    { value: 'DEFECTIVE', label: 'Defective', description: 'Item is defective or not working' },
    { value: 'INCOMPLETE', label: 'Incomplete', description: 'Item is missing components' },
    { value: 'WRONG_ITEM', label: 'Wrong Item', description: 'Wrong item was delivered' },
  ];
};

export const getAttachmentTypes = (): Array<{ value: AttachmentType; label: string; description: string }> => {
  return [
    { value: 'DOCUMENT', label: 'Document', description: 'General document' },
    { value: 'PHOTO', label: 'Photo', description: 'Photo of items or issues' },
    { value: 'DELIVERY_NOTE', label: 'Delivery Note', description: 'Delivery note from supplier' },
    { value: 'INSPECTION_REPORT', label: 'Inspection Report', description: 'Quality inspection report' },
    { value: 'OTHER', label: 'Other', description: 'Other type of attachment' },
  ];
};
