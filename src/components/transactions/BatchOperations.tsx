import { useState, useRef } from 'react';
import {
  ArrowUpTrayIcon,
  ArrowDownTrayIcon,
  DocumentArrowUpIcon,
  DocumentArrowDownIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import { batchOperationsService, type BatchImportOptions, type BatchExportOptions } from '../../services/batchOperationsService';
import { useCompany } from '../../contexts/CompanyContext';
import { useNotification } from '../../contexts/NotificationContext';

interface BatchOperationsProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
  mode: 'import' | 'export';
  type: 'transactions' | 'templates';
}

export default function BatchOperations({
  isOpen,
  onClose,
  onComplete,
  mode,
  type,
}: BatchOperationsProps) {
  const [step, setStep] = useState(1);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importOptions, setImportOptions] = useState<BatchImportOptions>({
    format: 'CSV',
    hasHeaders: true,
    dateFormat: 'YYYY-MM-DD',
    mapping: {},
    validateOnly: false,
    skipErrors: false,
  });
  const [exportOptions, setExportOptions] = useState<BatchExportOptions>({
    format: 'CSV',
    includeHeaders: true,
    dateFormat: 'YYYY-MM-DD',
    columns: [],
  });
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [detectedColumns, setDetectedColumns] = useState<string[]>([]);
  const [suggestedMapping, setSuggestedMapping] = useState<Record<string, string>>({});
  const [processing, setProcessing] = useState(false);
  const [result, setResult] = useState<any>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !currentCompany) return;

    setSelectedFile(file);

    // Auto-detect format
    const extension = file.name.toLowerCase().split('.').pop();
    if (extension === 'csv') {
      setImportOptions(prev => ({ ...prev, format: 'CSV' }));
    } else if (extension === 'xlsx' || extension === 'xls') {
      setImportOptions(prev => ({ ...prev, format: 'EXCEL' }));
    }

    // Preview the file
    try {
      setProcessing(true);
      const preview = await batchOperationsService.previewImport(
        currentCompany.id,
        file,
        importOptions
      );

      setPreviewData(preview.preview);
      setDetectedColumns(preview.detectedColumns);
      setSuggestedMapping(preview.suggestedMapping);
      setImportOptions(prev => ({ ...prev, mapping: preview.suggestedMapping }));
      setStep(2);
    } catch (error) {
      console.error('Failed to preview file:', error);
      showNotification({
        type: 'error',
        title: 'Failed to preview file',
        message: 'Could not read the selected file',
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleImport = async () => {
    if (!selectedFile || !currentCompany) return;

    try {
      setProcessing(true);
      let result;

      if (type === 'transactions') {
        result = await batchOperationsService.importTransactions(
          currentCompany.id,
          selectedFile,
          importOptions
        );
      } else {
        result = await batchOperationsService.importTemplates(
          currentCompany.id,
          selectedFile,
          importOptions
        );
      }

      setResult(result);
      setStep(3);

      if (result.success) {
        showNotification({
          type: 'success',
          title: 'Import completed',
          message: `Successfully imported ${result.successfulRows} of ${result.totalRows} records`,
        });
      } else {
        showNotification({
          type: 'warning',
          title: 'Import completed with errors',
          message: `Imported ${result.successfulRows} of ${result.totalRows} records with ${result.failedRows} errors`,
        });
      }
    } catch (error) {
      console.error('Import failed:', error);
      showNotification({
        type: 'error',
        title: 'Import failed',
        message: 'Could not import the file',
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleExport = async () => {
    if (!currentCompany) return;

    try {
      setProcessing(true);
      let result;

      if (type === 'transactions') {
        result = await batchOperationsService.exportTransactions(
          currentCompany.id,
          exportOptions
        );
      } else {
        result = await batchOperationsService.exportTemplates(
          currentCompany.id,
          [], // All templates
          exportOptions
        );
      }

      setResult(result);
      setStep(3);

      if (result.success) {
        showNotification({
          type: 'success',
          title: 'Export completed',
          message: `Successfully exported ${result.recordCount} records`,
        });

        // Auto-download the file
        const blob = await batchOperationsService.downloadExport(
          currentCompany.id,
          result.exportId
        );
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = result.filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Export failed:', error);
      showNotification({
        type: 'error',
        title: 'Export failed',
        message: 'Could not export the data',
      });
    } finally {
      setProcessing(false);
    }
  };

  const resetForm = () => {
    setStep(1);
    setSelectedFile(null);
    setPreviewData([]);
    setDetectedColumns([]);
    setSuggestedMapping({});
    setResult(null);
    setProcessing(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleComplete = () => {
    resetForm();
    onComplete();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div
          className="fixed inset-0"
          style={{ backgroundColor: 'rgba(0,0,0,0.6)' }}
          onClick={handleClose}
        />

        <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            {mode === 'import' ? (
              <ArrowUpTrayIcon className="h-6 w-6 text-primary-600 mr-3" />
            ) : (
              <ArrowDownTrayIcon className="h-6 w-6 text-primary-600 mr-3" />
            )}
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {mode === 'import' ? 'Import' : 'Export'} {type === 'transactions' ? 'Transactions' : 'Templates'}
            </h2>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            {[1, 2, 3].map((stepNumber) => (
              <div key={stepNumber} className="flex items-center">
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full ${
                    step >= stepNumber
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
                  }`}
                >
                  {step > stepNumber ? (
                    <CheckCircleIcon className="h-5 w-5" />
                  ) : (
                    stepNumber
                  )}
                </div>
                {stepNumber < 3 && (
                  <div
                    className={`w-16 h-1 mx-2 ${
                      step > stepNumber
                        ? 'bg-primary-600'
                        : 'bg-gray-200 dark:bg-gray-700'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between mt-2 text-sm text-gray-500 dark:text-gray-400">
            <span>{mode === 'import' ? 'Select File' : 'Configure Export'}</span>
            <span>{mode === 'import' ? 'Map Fields' : 'Review'}</span>
            <span>Complete</span>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Step 1: File Selection or Export Configuration */}
          {step === 1 && (
            <div className="space-y-6">
              {mode === 'import' ? (
                <>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Select File to Import
                    </h3>
                    <div
                      className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center hover:border-primary-500 dark:hover:border-primary-400 transition-colors cursor-pointer"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <DocumentArrowUpIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        Choose a file to import
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                        Supported formats: CSV, Excel (.xlsx, .xls)
                      </p>
                      <button
                        type="button"
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                      >
                        Select File
                      </button>
                    </div>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".csv,.xlsx,.xls"
                      onChange={handleFileSelect}
                      className="hidden"
                    />
                  </div>

                  {/* Import Options */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Date Format
                      </label>
                      <select
                        value={importOptions.dateFormat}
                        onChange={(e) => setImportOptions(prev => ({ ...prev, dateFormat: e.target.value }))}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                        <option value="DD-MM-YYYY">DD-MM-YYYY</option>
                      </select>
                    </div>

                    <div className="space-y-4">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={importOptions.hasHeaders}
                          onChange={(e) => setImportOptions(prev => ({ ...prev, hasHeaders: e.target.checked }))}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                          File has headers
                        </span>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={importOptions.skipErrors}
                          onChange={(e) => setImportOptions(prev => ({ ...prev, skipErrors: e.target.checked }))}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                          Skip rows with errors
                        </span>
                      </label>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Export Configuration
                    </h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Export Format
                      </label>
                      <select
                        value={exportOptions.format}
                        onChange={(e) => setExportOptions(prev => ({ ...prev, format: e.target.value as any }))}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="CSV">CSV</option>
                        <option value="EXCEL">Excel</option>
                        <option value="PDF">PDF</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Date Format
                      </label>
                      <select
                        value={exportOptions.dateFormat}
                        onChange={(e) => setExportOptions(prev => ({ ...prev, dateFormat: e.target.value }))}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                        <option value="DD-MM-YYYY">DD-MM-YYYY</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={exportOptions.includeHeaders}
                        onChange={(e) => setExportOptions(prev => ({ ...prev, includeHeaders: e.target.checked }))}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Include column headers
                      </span>
                    </label>
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={handleExport}
                      disabled={processing}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"
                    >
                      {processing ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Exporting...
                        </>
                      ) : (
                        <>
                          <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                          Start Export
                        </>
                      )}
                    </button>
                  </div>
                </>
              )}
            </div>
          )}

          {/* Step 2: Field Mapping (Import only) */}
          {step === 2 && mode === 'import' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Map Fields
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                  Map the columns in your file to the corresponding fields in the system.
                </p>
              </div>

              {/* Field Mapping */}
              <div className="space-y-4">
                {Object.entries({
                  transactionDate: 'Transaction Date *',
                  description: 'Description *',
                  reference: 'Reference',
                  debitAmount: 'Debit Amount',
                  creditAmount: 'Credit Amount',
                  accountCode: 'Account Code',
                  accountName: 'Account Name',
                }).map(([field, label]) => (
                  <div key={field} className="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {label}
                    </label>
                    <select
                      value={importOptions.mapping[field] || ''}
                      onChange={(e) => setImportOptions(prev => ({
                        ...prev,
                        mapping: { ...prev.mapping, [field]: e.target.value }
                      }))}
                      className="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">-- Select Column --</option>
                      {detectedColumns.map((column) => (
                        <option key={column} value={column}>
                          {column}
                        </option>
                      ))}
                    </select>
                  </div>
                ))}
              </div>

              {/* Preview Data */}
              {previewData.length > 0 && (
                <div>
                  <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
                    Data Preview (First 5 rows)
                  </h4>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          {detectedColumns.map((column) => (
                            <th
                              key={column}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                            >
                              {column}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        {previewData.slice(0, 5).map((row, index) => (
                          <tr key={index}>
                            {detectedColumns.map((column) => (
                              <td
                                key={column}
                                className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
                              >
                                {row[column]}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              <div className="flex justify-between">
                <button
                  onClick={() => setStep(1)}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Back
                </button>
                <button
                  onClick={handleImport}
                  disabled={processing || !importOptions.mapping.transactionDate || !importOptions.mapping.description}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"
                >
                  {processing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Importing...
                    </>
                  ) : (
                    <>
                      <DocumentArrowUpIcon className="h-4 w-4 mr-2" />
                      Start Import
                    </>
                  )}
                </button>
              </div>
            </div>
          )}

          {/* Step 3: Results */}
          {step === 3 && result && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  {mode === 'import' ? 'Import' : 'Export'} Results
                </h3>
              </div>

              {/* Success/Error Summary */}
              <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  {result.success ? (
                    <CheckCircleIcon className="h-8 w-8 text-green-500 mr-3" />
                  ) : (
                    <ExclamationTriangleIcon className="h-8 w-8 text-yellow-500 mr-3" />
                  )}
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                      {result.success ? 'Completed Successfully' : 'Completed with Issues'}
                    </h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {mode === 'import'
                        ? `Processed ${result.totalRows} rows`
                        : `Exported ${result.recordCount} records`
                      }
                    </p>
                  </div>
                </div>

                {mode === 'import' && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {result.successfulRows}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">Successful</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                        {result.failedRows}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">Failed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                        {result.warnings?.length || 0}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">Warnings</div>
                    </div>
                  </div>
                )}

                {mode === 'export' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {result.recordCount}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">Records Exported</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {batchOperationsService.formatFileSize(result.fileSize)}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">File Size</div>
                    </div>
                  </div>
                )}
              </div>

              {/* Errors and Warnings */}
              {mode === 'import' && (result.errors?.length > 0 || result.warnings?.length > 0) && (
                <div className="space-y-4">
                  {result.errors?.length > 0 && (
                    <div>
                      <h5 className="text-md font-medium text-red-600 dark:text-red-400 mb-2">
                        Errors ({result.errors.length})
                      </h5>
                      <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 max-h-40 overflow-y-auto">
                        {result.errors.slice(0, 10).map((error: any, index: number) => (
                          <div key={index} className="text-sm text-red-700 dark:text-red-300 mb-1">
                            Row {error.row}: {error.message} (Field: {error.field})
                          </div>
                        ))}
                        {result.errors.length > 10 && (
                          <div className="text-sm text-red-600 dark:text-red-400 mt-2">
                            ... and {result.errors.length - 10} more errors
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {result.warnings?.length > 0 && (
                    <div>
                      <h5 className="text-md font-medium text-yellow-600 dark:text-yellow-400 mb-2">
                        Warnings ({result.warnings.length})
                      </h5>
                      <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 max-h-40 overflow-y-auto">
                        {result.warnings.slice(0, 10).map((warning: any, index: number) => (
                          <div key={index} className="text-sm text-yellow-700 dark:text-yellow-300 mb-1">
                            Row {warning.row}: {warning.message} (Field: {warning.field})
                          </div>
                        ))}
                        {result.warnings.length > 10 && (
                          <div className="text-sm text-yellow-600 dark:text-yellow-400 mt-2">
                            ... and {result.warnings.length - 10} more warnings
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              <div className="flex justify-end space-x-3">
                <button
                  onClick={handleClose}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Close
                </button>
                <button
                  onClick={handleComplete}
                  className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
                >
                  Done
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
      </div>
    </div>
  );
}
