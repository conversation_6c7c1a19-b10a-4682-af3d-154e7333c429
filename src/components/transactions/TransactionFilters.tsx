import { useState } from "react";
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import type { TransactionStatus } from "../../types";
import { transactionService } from "../../services/transactionService";

interface TransactionFiltersProps {
  onFiltersChange: (filters: {
    status?: TransactionStatus;
    dateFrom?: string;
    dateTo?: string;
    search?: string;
  }) => void;
}

export default function TransactionFilters({
  onFiltersChange,
}: TransactionFiltersProps) {
  const [filters, setFilters] = useState({
    status: "" as TransactionStatus | "",
    dateFrom: "",
    dateTo: "",
    search: "",
  });

  const [showAdvanced, setShowAdvanced] = useState(false);

  const statusOptions = transactionService.getStatusOptions();

  const handleFilterChange = (field: string, value: string) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);

    // Convert empty strings to undefined for the API
    const apiFilters = Object.fromEntries(
      Object.entries(newFilters).filter(([_, v]) => v !== "")
    );

    onFiltersChange(apiFilters);
  };

  const clearFilters = () => {
    const clearedFilters = {
      status: "" as TransactionStatus | "",
      dateFrom: "",
      dateTo: "",
      search: "",
    };
    setFilters(clearedFilters);
    onFiltersChange({});
  };

  const hasActiveFilters = Object.values(filters).some((value) => value !== "");

  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
        </div>
        <input
          type="text"
          placeholder="Search transactions..."
          value={filters.search}
          onChange={(e) => handleFilterChange("search", e.target.value)}
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 placeholder-gray-500 dark:placeholder-gray-400 dark:placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 dark:placeholder-gray-500 dark:placeholder-gray-400 dark:placeholder-gray-500 dark:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Quick Filters */}
      <div className="flex flex-wrap items-center gap-2">
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
        >
          <FunnelIcon className="h-4 w-4 mr-2" />
          Filters
          {hasActiveFilters && (
            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800">
              Active
            </span>
          )}
        </button>

        {/* Status Filter */}
        <select
          value={filters.status}
          onChange={(e) => handleFilterChange("status", e.target.value)}
          className="border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md text-sm py-2 px-3 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="">All Statuses</option>
          {statusOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="inline-flex items-center px-2 py-1 border border-transparent text-sm font-medium rounded text-red-700 dark:text-red-400 bg-red-100 dark:bg-red-900/20 hover:bg-red-200"
          >
            <XMarkIcon className="h-4 w-4 mr-1" />
            Clear
          </button>
        )}
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="border-t pt-4 space-y-4">
          <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Date From
              </label>
              <input
                type="date"
                value={filters.dateFrom}
                onChange={(e) => handleFilterChange("dateFrom", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Date To
              </label>
              <input
                type="date"
                value={filters.dateTo}
                onChange={(e) => handleFilterChange("dateTo", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          {/* Quick Date Ranges */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Quick Date Ranges
            </label>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => {
                  const today = new Date().toISOString().split("T")[0];
                  handleFilterChange("dateFrom", today);
                  handleFilterChange("dateTo", today);
                }}
                className="px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700"
              >
                Today
              </button>
              <button
                onClick={() => {
                  const today = new Date();
                  const weekAgo = new Date(
                    today.getTime() - 7 * 24 * 60 * 60 * 1000
                  );
                  handleFilterChange(
                    "dateFrom",
                    weekAgo.toISOString().split("T")[0]
                  );
                  handleFilterChange(
                    "dateTo",
                    today.toISOString().split("T")[0]
                  );
                }}
                className="px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700"
              >
                Last 7 Days
              </button>
              <button
                onClick={() => {
                  const today = new Date();
                  const monthAgo = new Date(
                    today.getFullYear(),
                    today.getMonth() - 1,
                    today.getDate()
                  );
                  handleFilterChange(
                    "dateFrom",
                    monthAgo.toISOString().split("T")[0]
                  );
                  handleFilterChange(
                    "dateTo",
                    today.toISOString().split("T")[0]
                  );
                }}
                className="px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700"
              >
                Last 30 Days
              </button>
              <button
                onClick={() => {
                  const today = new Date();
                  const startOfMonth = new Date(
                    today.getFullYear(),
                    today.getMonth(),
                    1
                  );
                  const endOfMonth = new Date(
                    today.getFullYear(),
                    today.getMonth() + 1,
                    0
                  );
                  handleFilterChange(
                    "dateFrom",
                    startOfMonth.toISOString().split("T")[0]
                  );
                  handleFilterChange(
                    "dateTo",
                    endOfMonth.toISOString().split("T")[0]
                  );
                }}
                className="px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700"
              >
                This Month
              </button>
              <button
                onClick={() => {
                  const today = new Date();
                  const startOfLastMonth = new Date(
                    today.getFullYear(),
                    today.getMonth() - 1,
                    1
                  );
                  const endOfLastMonth = new Date(
                    today.getFullYear(),
                    today.getMonth(),
                    0
                  );
                  handleFilterChange(
                    "dateFrom",
                    startOfLastMonth.toISOString().split("T")[0]
                  );
                  handleFilterChange(
                    "dateTo",
                    endOfLastMonth.toISOString().split("T")[0]
                  );
                }}
                className="px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700"
              >
                Last Month
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
