import { useState, useEffect } from "react";
import { format } from "date-fns";
import {
  TrashIcon,
  EyeIcon,
  PaperAirplaneIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import type { Transaction, TransactionStatus } from "../../types";
import { transactionService } from "../../services/transactionService";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { useConfirmDialog } from "../../hooks/useConfirmDialog";
import ConfirmDialog from "../ui/ConfirmDialog";
import humps from "humps";
import { Edit } from "lucide-react";

interface TransactionListProps {
  onEdit?: (transaction: Transaction) => void;
  onView?: (transaction: Transaction) => void;
  filters?: {
    status?: TransactionStatus;
    dateFrom?: string;
    dateTo?: string;
    search?: string;
  };
}

export default function TransactionList({
  onEdit,
  onView,
  filters,
}: TransactionListProps) {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
  });

  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  const { confirm, dialogProps } = useConfirmDialog();

  const loadTransactions = async (page = 1) => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      const response = await transactionService.getTransactions(
        currentCompany.id,
        {
          ...filters,
          page,
          limit: pagination.limit,
        }
      );

      const camelizedTransactions = humps.camelizeKeys(
        response.data
      ) as Transaction[];

      setTransactions(camelizedTransactions);
      setPagination(response.pagination);
    } catch (error) {
      console.error("Failed to load transactions:", error);
      showNotification({
        type: "error",
        title: "Failed to load transactions",
        message: "Failed to load transactions",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTransactions();
  }, [currentCompany, filters]);

  const handleSubmit = async (transaction: Transaction) => {
    if (!currentCompany) return;

    try {
      await transactionService.submitTransaction(
        currentCompany.id,
        transaction.id
      );
      showNotification({
        type: "success",
        title: "Transaction submitted",
        message: "Transaction submitted for approval successfully",
      });
      loadTransactions(pagination.page);
    } catch (error) {
      console.error("Failed to submit transaction:", error);
      showNotification({
        type: "error",
        title: "Failed to submit transaction",
        message: "Failed to submit transaction for approval",
      });
    }
  };

  const handleCancel = async (transaction: Transaction) => {
    if (!currentCompany) return;

    const confirmed = await confirm({
      title: "Cancel Transaction",
      message: `Are you sure you want to cancel transaction ${transaction.transactionNumber}? This action cannot be undone.`,
      confirmLabel: "Cancel Transaction",
      cancelLabel: "Keep Transaction",
      type: "danger",
    });

    if (!confirmed) {
      return;
    }

    try {
      await transactionService.cancelTransaction(
        currentCompany.id,
        transaction.id
      );
      showNotification({
        type: "success",
        title: "Transaction cancelled",
        message: "Transaction cancelled successfully",
      });
      loadTransactions(pagination.page);
    } catch (error) {
      console.error("Failed to cancel transaction:", error);
      showNotification({
        type: "error",
        title: "Failed to cancel transaction",
        message: "Failed to cancel transaction",
      });
    }
  };

  const handleDelete = async (transaction: Transaction) => {
    if (!currentCompany) return;

    if (!transactionService.canDelete(transaction.status)) {
      showNotification({
        type: "error",
        title: "Cannot delete transaction",
        message: "Only draft and pending transactions can be deleted",
      });
      return;
    }

    const confirmed = await confirm({
      title: "Delete Transaction",
      message: `Are you sure you want to delete transaction ${transaction.transactionNumber}? This action cannot be undone.`,
      confirmLabel: "Delete",
      cancelLabel: "Cancel",
      type: "danger",
    });

    if (!confirmed) {
      return;
    }

    try {
      await transactionService.deleteTransaction(
        currentCompany.id,
        transaction.id
      );
      showNotification({
        type: "success",
        title: "Transaction deleted",
        message: "Transaction deleted successfully",
      });
      loadTransactions(pagination.page);
    } catch (error) {
      console.error("Failed to delete transaction:", error);
      showNotification({
        type: "error",
        title: "Failed to delete transaction",
        message: "Failed to delete transaction",
      });
    }
  };

  const getStatusBadge = (status: TransactionStatus) => {
    const color = transactionService.getStatusColor(status);
    const colorClasses = {
      gray: "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200",
      yellow: "bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800",
      blue: "bg-blue-100 dark:bg-blue-900/20 text-blue-800",
      green: "bg-green-100 dark:bg-green-900/20 text-green-800",
      red: "bg-red-100 dark:bg-red-900/20 text-red-800",
      purple: "bg-purple-100 text-purple-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          colorClasses[color as keyof typeof colorClasses] || colorClasses.gray
        }`}
      >
        {status}
      </span>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount || 0);
  };

  const formatDate = (dateString: string) => {
    console.log("formatDate", dateString);
    try {
      const date = new Date(dateString);

      if (isNaN(date.getTime())) {
        return "Invalid Date";
      }

      return format(date, "MMM dd, yyyy");
    } catch (error) {
      console.error("Date formatting error:", error);
      return "Invalid Date";
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="text-center bg-white dark:bg-gray-800 py-12">
        <svg
          className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          No transactions found
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
          Get started by recording your first transaction.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4 bg-white dark:bg-gray-800">
      <div className="overflow-hidden bg-white dark:bg-gray-800 shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table className="min-w-full bg-white dark:bg-gray-800 divide-y divide-gray-300 dark:divide-gray-600">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr className="bg-white dark:bg-gray-800">
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                Transaction
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                Amount
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 dark:divide-gray-700">
            {transactions.map((transaction) => {
              return (
                <tr
                  key={transaction.id}
                  className="hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 group cursor-pointer"
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {transaction.transactionNumber}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    {formatDate(transaction.transactionDate)}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    <div className="max-w-xs truncate">
                      {transaction.description}
                    </div>
                    {transaction.reference && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        Ref: {transaction.reference}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {formatCurrency(transaction.totalAmount)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(transaction.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        onClick={() => onView?.(transaction)}
                        className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:bg-blue-900/20 rounded"
                        title="View transaction"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      {transactionService.canEdit(transaction.status) && (
                        <button
                          onClick={() => onEdit?.(transaction)}
                          className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:bg-blue-900/20 rounded"
                          title="Edit transaction"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                      )}
                      {transactionService.canSubmit(transaction.status) && (
                        <button
                          onClick={() => handleSubmit(transaction)}
                          className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-indigo-600 hover:bg-indigo-50 rounded"
                          title="Submit for approval"
                        >
                          <PaperAirplaneIcon className="h-4 w-4" />
                        </button>
                      )}
                      {transactionService.canCancel(transaction.status) && (
                        <button
                          onClick={() => handleCancel(transaction)}
                          className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-red-600 dark:text-red-400 hover:bg-red-50 dark:bg-red-900/20 rounded"
                          title="Cancel transaction"
                        >
                          <XCircleIcon className="h-4 w-4" />
                        </button>
                      )}
                      {transactionService.canDelete(transaction.status) && (
                        <button
                          onClick={() => handleDelete(transaction)}
                          className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-red-600 dark:text-red-400 hover:bg-red-50 dark:bg-red-900/20 rounded"
                          title="Delete transaction"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 sm:px-6">
          <div className="flex flex-1 justify-between sm:hidden">
            <button
              onClick={() => loadTransactions(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className="relative inline-flex items-center rounded-md border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => loadTransactions(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages}
              className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Showing{" "}
                <span className="font-medium">
                  {(pagination.page - 1) * pagination.limit + 1}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(
                    pagination.page * pagination.limit,
                    pagination.total
                  )}
                </span>{" "}
                of <span className="font-medium">{pagination.total}</span>{" "}
                results
              </p>
            </div>
            <div>
              <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm">
                <button
                  onClick={() => loadTransactions(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                >
                  Previous
                </button>
                {/* Page numbers would go here */}
                <button
                  onClick={() => loadTransactions(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages}
                  className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Dialog */}
      <ConfirmDialog {...dialogProps} />
    </div>
  );
}
