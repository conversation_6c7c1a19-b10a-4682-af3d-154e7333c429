import { useState, useEffect } from "react";
import {
  XMarkIcon,
  PlusIcon,
  TrashIcon,
  DocumentDuplicateIcon,
  CalculatorIcon,
} from "@heroicons/react/24/outline";
import type { Account } from "../../types";
import { accountService } from "../../services/accountService";
import {
  transactionTemplateService,
  type CreateTemplateData,
  type TemplateCategory,
} from "../../services/transactionTemplateService";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";

interface TemplateEntry {
  id?: string;
  accountId: string;
  description: string;
  debitAmount: number;
  creditAmount: number;
  isVariableAmount: boolean;
  amountFormula?: string;
  lineNumber: number;
}

interface TransactionTemplateFormProps {
  template?: any;
  isOpen: boolean;
  onClose: () => void;
  onSave: (template: any) => void;
}

export default function TransactionTemplateForm({
  template,
  isOpen,
  onClose,
  onSave,
}: TransactionTemplateFormProps) {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "GENERAL" as TemplateCategory,
    defaultValues: {},
  });

  const [entries, setEntries] = useState<TemplateEntry[]>([
    {
      accountId: "",
      description: "",
      debitAmount: 0,
      creditAmount: 0,
      isVariableAmount: false,
      lineNumber: 1,
    },
    {
      accountId: "",
      description: "",
      debitAmount: 0,
      creditAmount: 0,
      isVariableAmount: false,
      lineNumber: 2,
    },
  ]);

  const [accounts, setAccounts] = useState<Account[]>([]);
  const [categories, setCategories] = useState<
    Array<{ value: TemplateCategory; label: string }>
  >([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();

  useEffect(() => {
    if (isOpen && currentCompany) {
      const loadData = async () => {
        try {
          setLoading(true);
          const [accountsData, categoriesData] = await Promise.all([
            accountService.getAccounts(currentCompany.id),
            transactionTemplateService.getCategories(),
          ]);
          setAccounts(accountsData);
          setCategories(categoriesData);

          if (template) {
            populateForm();
          } else {
            resetForm();
          }
        } catch (error) {
          console.error("Failed to load data:", error);
          showNotification({
            type: "error",
            title: "Failed to load data",
            message: "Could not load required data for the form",
          });
        } finally {
          setLoading(false);
        }
      };

      loadData();
    }
  }, [isOpen, template, currentCompany]);

  const populateForm = () => {
    if (!template) return;

    setFormData({
      name: template.name || "",
      description: template.description || "",
      category: template.category || "GENERAL",
      defaultValues: template.defaultValues || {},
    });

    if (template.entries && template.entries.length > 0) {
      setEntries(
        template.entries.map((entry: any, index: number) => ({
          id: entry.id,
          accountId: entry.accountId || "",
          description: entry.description || "",
          debitAmount: entry.debitAmount || 0,
          creditAmount: entry.creditAmount || 0,
          isVariableAmount: entry.isVariableAmount || false,
          amountFormula: entry.amountFormula || "",
          lineNumber: index + 1,
        }))
      );
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      category: "GENERAL",
      defaultValues: {},
    });

    setEntries([
      {
        accountId: "",
        description: "",
        debitAmount: 0,
        creditAmount: 0,
        isVariableAmount: false,
        lineNumber: 1,
      },
      {
        accountId: "",
        description: "",
        debitAmount: 0,
        creditAmount: 0,
        isVariableAmount: false,
        lineNumber: 2,
      },
    ]);
  };

  const handleFormChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleEntryChange = (index: number, field: string, value: any) => {
    setEntries((prev) =>
      prev.map((entry, i) =>
        i === index ? { ...entry, [field]: value } : entry
      )
    );
  };

  const addEntry = () => {
    const newEntry: TemplateEntry = {
      accountId: "",
      description: "",
      debitAmount: 0,
      creditAmount: 0,
      isVariableAmount: false,
      lineNumber: entries.length + 1,
    };
    setEntries((prev) => [...prev, newEntry]);
  };

  const removeEntry = (index: number) => {
    if (entries.length <= 2) {
      showNotification({
        type: "error",
        title: "Cannot remove entry",
        message: "Template must have at least two entries",
      });
      return;
    }

    setEntries((prev) =>
      prev
        .filter((_, i) => i !== index)
        .map((entry, i) => ({ ...entry, lineNumber: i + 1 }))
    );
  };

  const duplicateEntry = (index: number) => {
    const entryToDuplicate = entries[index];
    const newEntry: TemplateEntry = {
      ...entryToDuplicate,
      id: undefined,
      lineNumber: entries.length + 1,
    };
    setEntries((prev) => [...prev, newEntry]);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div
          className="fixed inset-0"
          style={{ backgroundColor: "rgba(0,0,0,0.6)" }}
          onClick={onClose}
        />

        <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {template ? "Edit Template" : "Create Template"}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
                  <p className="text-gray-600 dark:text-gray-400">Loading...</p>
                </div>
              </div>
            ) : (
              <form onSubmit={(e) => e.preventDefault()} className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Template Name *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleFormChange("name", e.target.value)}
                      className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="Enter template name"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Category *
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) =>
                        handleFormChange("category", e.target.value)
                      }
                      className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    >
                      {categories &&
                        categories.map((category) => (
                          <option key={category.value} value={category.value}>
                            {category.label}
                          </option>
                        ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) =>
                      handleFormChange("description", e.target.value)
                    }
                    rows={3}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Enter template description"
                  />
                </div>

                {/* Template Entries */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      Template Entries
                    </h3>
                    <button
                      type="button"
                      onClick={addEntry}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      <PlusIcon className="h-4 w-4 mr-1" />
                      Add Entry
                    </button>
                  </div>

                  <div className="space-y-4">
                    {entries.map((entry, index) => (
                      <div
                        key={index}
                        className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-900"
                      >
                        <div className="flex items-center justify-between mb-4">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            Entry {index + 1}
                          </span>
                          <div className="flex space-x-2">
                            <button
                              type="button"
                              onClick={() => duplicateEntry(index)}
                              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                              title="Duplicate entry"
                            >
                              <DocumentDuplicateIcon className="h-4 w-4" />
                            </button>
                            {entries.length > 2 && (
                              <button
                                type="button"
                                onClick={() => removeEntry(index)}
                                className="text-red-400 hover:text-red-600"
                                title="Remove entry"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            )}
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                          <div className="lg:col-span-2">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Account *
                            </label>
                            <select
                              value={entry.accountId}
                              onChange={(e) =>
                                handleEntryChange(
                                  index,
                                  "accountId",
                                  e.target.value
                                )
                              }
                              className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                              required
                            >
                              <option value="">Select account</option>
                              {accounts &&
                                accounts.map((account) => (
                                  <option key={account.id} value={account.id}>
                                    {account.code} - {account.name}
                                  </option>
                                ))}
                            </select>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Debit Amount
                            </label>
                            <input
                              type="number"
                              step="0.01"
                              value={entry.debitAmount}
                              onChange={(e) =>
                                handleEntryChange(
                                  index,
                                  "debitAmount",
                                  parseFloat(e.target.value) || 0
                                )
                              }
                              className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                              disabled={entry.creditAmount > 0}
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Credit Amount
                            </label>
                            <input
                              type="number"
                              step="0.01"
                              value={entry.creditAmount}
                              onChange={(e) =>
                                handleEntryChange(
                                  index,
                                  "creditAmount",
                                  parseFloat(e.target.value) || 0
                                )
                              }
                              className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                              disabled={entry.debitAmount > 0}
                            />
                          </div>
                        </div>

                        <div className="mt-4">
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Description
                          </label>
                          <input
                            type="text"
                            value={entry.description}
                            onChange={(e) =>
                              handleEntryChange(
                                index,
                                "description",
                                e.target.value
                              )
                            }
                            className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                            placeholder="Entry description (supports variables like {{amount}}, {{date}})"
                          />
                        </div>

                        <div className="mt-4 flex items-center space-x-4">
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={entry.isVariableAmount}
                              onChange={(e) =>
                                handleEntryChange(
                                  index,
                                  "isVariableAmount",
                                  e.target.checked
                                )
                              }
                              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                            />
                            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                              Variable Amount
                            </span>
                          </label>

                          {entry.isVariableAmount && (
                            <div className="flex-1">
                              <div className="flex">
                                <input
                                  type="text"
                                  value={entry.amountFormula || ""}
                                  onChange={(e) =>
                                    handleEntryChange(
                                      index,
                                      "amountFormula",
                                      e.target.value
                                    )
                                  }
                                  className="flex-1 border border-gray-300 dark:border-gray-600 rounded-l-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                                  placeholder="Formula (e.g., {{amount}} * 0.18)"
                                />
                                <button
                                  type="button"
                                  className="px-3 py-2 border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-md bg-gray-50 dark:bg-gray-800 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                                  title="Formula help"
                                >
                                  <CalculatorIcon className="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Balance Check */}
                  <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Balance Check:
                      </span>
                      <div className="text-sm text-blue-600 dark:text-blue-300">
                        {(() => {
                          const balance =
                            transactionTemplateService.validateTemplateBalance(
                              entries
                            );
                          return balance.isBalanced ? (
                            <span className="text-green-600 dark:text-green-400">
                              ✓ Balanced
                            </span>
                          ) : (
                            <span className="text-red-600 dark:text-red-400">
                              ✗ Difference: ${balance.difference.toFixed(2)}
                            </span>
                          );
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={async () => {
                if (!currentCompany) return;

                // Validate form
                if (!formData.name.trim()) {
                  showNotification({
                    type: "error",
                    title: "Template name is required",
                    message: "Please enter a template name",
                  });
                  return;
                }

                const validEntries = entries.filter(
                  (entry) =>
                    entry.accountId &&
                    (entry.debitAmount > 0 || entry.creditAmount > 0)
                );

                if (validEntries.length < 2) {
                  showNotification({
                    type: "error",
                    title: "At least two entries are required",
                    message: "Template must have at least two valid entries",
                  });
                  return;
                }

                const balance =
                  transactionTemplateService.validateTemplateBalance(
                    validEntries
                  );
                if (!balance.isBalanced) {
                  showNotification({
                    type: "error",
                    title: "Template must be balanced",
                    message: "Total debits must equal total credits",
                  });
                  return;
                }

                setSaving(true);
                try {
                  const templateData: CreateTemplateData = {
                    name: formData.name,
                    description: formData.description,
                    category: formData.category,
                    defaultValues: formData.defaultValues,
                    entries: validEntries.map((entry, index) => ({
                      accountId: entry.accountId,
                      description: entry.description,
                      debitAmount: entry.debitAmount,
                      creditAmount: entry.creditAmount,
                      isVariableAmount: entry.isVariableAmount,
                      amountFormula: entry.amountFormula,
                      lineNumber: index + 1,
                      metadata: {},
                    })),
                  };

                  let savedTemplate;
                  if (template) {
                    savedTemplate =
                      await transactionTemplateService.updateTemplate(
                        currentCompany.id,
                        template.id,
                        templateData
                      );
                    showNotification({
                      type: "success",
                      title: "Template updated",
                      message: "Transaction template updated successfully",
                    });
                  } else {
                    savedTemplate =
                      await transactionTemplateService.createTemplate(
                        currentCompany.id,
                        templateData
                      );
                    showNotification({
                      type: "success",
                      title: "Template created",
                      message: "Transaction template created successfully",
                    });
                  }

                  onSave(savedTemplate);
                  onClose();
                } catch (error) {
                  console.error("Failed to save template:", error);
                  showNotification({
                    type: "error",
                    title: "Failed to save template",
                    message: "Could not save transaction template",
                  });
                } finally {
                  setSaving(false);
                }
              }}
              disabled={saving || loading}
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {saving
                ? "Saving..."
                : template
                ? "Update Template"
                : "Create Template"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
