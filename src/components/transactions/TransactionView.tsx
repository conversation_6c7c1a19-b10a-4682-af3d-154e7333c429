// import React from "react";
import { format } from "date-fns";
import {
  XMarkIcon,
  PencilIcon,
  CheckIcon,
  XCircleIcon,
  ArrowPathIcon,
  PaperAirplaneIcon,
} from "@heroicons/react/24/outline";
import type { Transaction } from "../../types";
import { transactionService } from "../../services/transactionService";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { useConfirmDialog } from "../../hooks/useConfirmDialog";
import ConfirmDialog from "../ui/ConfirmDialog";

interface TransactionViewProps {
  transaction: Transaction;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: () => void;
  onUpdate?: (transaction: Transaction) => void;
}

export default function TransactionView({
  transaction,
  isOpen,
  onClose,
  onEdit,
  onUpdate,
}: TransactionViewProps) {
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  const { confirm, dialogProps } = useConfirmDialog();

  const getStatusBadge = (status: string) => {
    const color = transactionService.getStatusColor(status as any);
    const colorClasses = {
      gray: "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200",
      yellow: "bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800",
      blue: "bg-blue-100 dark:bg-blue-900/20 text-blue-800",
      green: "bg-green-100 dark:bg-green-900/20 text-green-800",
      red: "bg-red-100 dark:bg-red-900/20 text-red-800",
      purple: "bg-purple-100 text-purple-800",
    };

    return (
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          colorClasses[color as keyof typeof colorClasses] || colorClasses.gray
        }`}
      >
        {status}
      </span>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount || 0);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return "Invalid Date";
      }
      return format(date, "MMMM dd, yyyy");
    } catch (error) {
      console.error("Date formatting error:", error);
      return "Invalid Date";
    }
  };

  const formatDateTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return "Invalid Date";
      }
      return format(date, "MMM dd, yyyy HH:mm");
    } catch (error) {
      console.error("DateTime formatting error:", error);
      return "Invalid Date";
    }
  };

  const handleStatusAction = async (action: "submit" | "approve" | "post" | "reverse" | "cancel") => {
    if (!currentCompany) return;

    try {
      let updatedTransaction: Transaction;

      switch (action) {
        case "submit":
          updatedTransaction = await transactionService.submitTransaction(
            currentCompany.id,
            transaction.id
          );
          showNotification({
            type: "success",
            title: "Transaction submitted",
            message: "Transaction submitted for approval successfully",
          });
          break;
        case "approve":
          updatedTransaction = await transactionService.approveTransaction(
            currentCompany.id,
            transaction.id
          );
          showNotification({
            type: "success",
            title: "Transaction approved",
            message: "Transaction approved successfully",
          });
          break;
        case "post":
          updatedTransaction = await transactionService.postTransaction(
            currentCompany.id,
            transaction.id
          );
          showNotification({
            type: "success",
            title: "Transaction posted",
            message: "Transaction posted successfully",
          });
          break;
        case "cancel":
          const cancelConfirmed = await confirm({
            title: "Cancel Transaction",
            message:
              "Are you sure you want to cancel this transaction? This action cannot be undone.",
            confirmLabel: "Cancel Transaction",
            cancelLabel: "Keep Transaction",
            type: "danger",
          });

          if (cancelConfirmed) {
            updatedTransaction = await transactionService.cancelTransaction(
              currentCompany.id,
              transaction.id
            );
            showNotification({
              type: "success",
              title: "Transaction cancelled",
              message: "Transaction cancelled successfully",
            });
          } else {
            return;
          }
          break;
        case "reverse":
          const reverseConfirmed = await confirm({
            title: "Reverse Transaction",
            message:
              "Are you sure you want to reverse this transaction? This action cannot be undone.",
            confirmLabel: "Reverse",
            cancelLabel: "Cancel",
            type: "danger",
          });

          if (reverseConfirmed) {
            updatedTransaction = await transactionService.reverseTransaction(
              currentCompany.id,
              transaction.id
            );
            showNotification({
              type: "success",
              title: "Transaction reversed",
              message: "Transaction reversed successfully",
            });
          } else {
            return;
          }
          break;
        default:
          return;
      }

      onUpdate?.(updatedTransaction);
    } catch (error) {
      console.error(`Failed to ${action} transaction:`, error);
      showNotification({
        type: "error",
        title: "Error",
        message: `Failed to ${action} transaction`,
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div
          className="fixed inset-0"
          style={{ backgroundColor: 'rgba(0,0,0,0.6)' }}
          onClick={onClose}
        />

        <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Transaction {transaction.transactionNumber}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">
                Created on {formatDateTime(transaction.createdAt)}
              </p>
            </div>
            <div className="flex items-center space-x-3">
              {getStatusBadge(transaction.status)}
              <button
                onClick={onClose}
                className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>

          <div className="p-6 space-y-6">
            {/* Transaction Details */}
            <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4 bg-white dark:bg-gray-800">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Date
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {formatDate(transaction.transactionDate)}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Description
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {transaction.description}
                  </p>
                </div>

                {transaction.reference && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Reference
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {transaction.reference}
                    </p>
                  </div>
                )}
              </div>

              <div className="space-y-4 bg-white dark:bg-gray-800">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Total Amount
                  </label>
                  <p className="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                    {formatCurrency(transaction.totalAmount)}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Currency
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {transaction.currency}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Status
                  </label>
                  <div className="mt-1">
                    {getStatusBadge(transaction.status)}
                  </div>
                </div>
              </div>
            </div>

            {/* Transaction Entries */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Transaction Entries
              </h3>
              <div className="overflow-hidden shadow bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 rounded-lg">
                <table className="min-w-full bg-white dark:bg-gray-800 divide-y divide-gray-300 dark:divide-gray-600">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr className="bg-white dark:bg-gray-800">
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                        Account
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                        Debit
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                        Credit
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 dark:divide-gray-700">
                    {transaction.entries.map((entry) => (
                      <tr key={entry.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {entry.accountCode || entry.account?.code} -{" "}
                            {entry.accountName || entry.account?.name}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900 dark:text-white">
                            {entry.description || "-"}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900 dark:text-white">
                          {entry.debitAmount > 0
                            ? formatCurrency(entry.debitAmount)
                            : "-"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900 dark:text-white">
                          {entry.creditAmount > 0
                            ? formatCurrency(entry.creditAmount)
                            : "-"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot className="bg-gray-50 dark:bg-gray-800">
                    <tr className="bg-white dark:bg-gray-800">
                      <td
                        colSpan={2}
                        className="px-6 py-3 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Total
                      </td>
                      <td className="px-6 py-3 text-right text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(
                          transaction.entries.reduce(
                            (sum, entry) => sum + entry.debitAmount,
                            0
                          )
                        )}
                      </td>
                      <td className="px-6 py-3 text-right text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(
                          transaction.entries.reduce(
                            (sum, entry) => sum + entry.creditAmount,
                            0
                          )
                        )}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>

            {/* Audit Information */}
            {(transaction.approvedBy || transaction.reversedBy) && (
              <div className="border-t pt-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Audit Trail
                </h3>
                <div className="space-y-3">
                  {transaction.approvedBy && transaction.approvedAt && (
                    <div className="flex items-center text-sm">
                      <CheckIcon className="h-4 w-4 text-green-500 dark:text-green-400 mr-2" />
                      <span>
                        Approved on {formatDateTime(transaction.approvedAt)}
                      </span>
                    </div>
                  )}
                  {transaction.reversedBy && transaction.reversedAt && (
                    <div className="flex items-center text-sm">
                      <ArrowPathIcon className="h-4 w-4 text-purple-500 mr-2" />
                      <span>
                        Reversed on {formatDateTime(transaction.reversedAt)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-between items-center p-6 border-t bg-gray-50 dark:bg-gray-800">
            <div className="flex space-x-3">
              {transactionService.canEdit(transaction.status) && onEdit && (
                <button
                  onClick={onEdit}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
                >
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit
                </button>
              )}

              {transactionService.canSubmit(transaction.status) && (
                <button
                  onClick={() => handleStatusAction("submit")}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                  Submit for Approval
                </button>
              )}

              {transactionService.canApprove(transaction.status) && (
                <button
                  onClick={() => handleStatusAction("approve")}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <CheckIcon className="h-4 w-4 mr-2" />
                  Approve
                </button>
              )}

              {transactionService.canPost(transaction.status) && (
                <button
                  onClick={() => handleStatusAction("post")}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                >
                  <CheckIcon className="h-4 w-4 mr-2" />
                  Post
                </button>
              )}

              {transactionService.canCancel(transaction.status) && (
                <button
                  onClick={() => handleStatusAction("cancel")}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                >
                  <XCircleIcon className="h-4 w-4 mr-2" />
                  Cancel
                </button>
              )}

              {transactionService.canReverse(transaction.status) && (
                <button
                  onClick={() => handleStatusAction("reverse")}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
                >
                  <ArrowPathIcon className="h-4 w-4 mr-2" />
                  Reverse
                </button>
              )}
            </div>

            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700"
            >
              Close
            </button>
          </div>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <ConfirmDialog {...dialogProps} />
    </div>
  );
}
