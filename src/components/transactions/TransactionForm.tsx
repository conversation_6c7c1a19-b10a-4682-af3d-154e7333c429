import { useState, useEffect } from "react";
import {
  XMarkIcon,
  PlusIcon,
  TrashIcon,
  DocumentDuplicateIcon,
} from "@heroicons/react/24/outline";
import type { Transaction, Account } from "../../types";
import { transactionService } from "../../services/transactionService";
import { accountService } from "../../services/accountService";
import { transactionTemplateService } from "../../services/transactionTemplateService";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";

interface TransactionEntry {
  id?: string;
  accountId: string;
  description: string;
  debitAmount: number;
  creditAmount: number;
}

interface TransactionFormProps {
  transaction?: Transaction;
  isOpen: boolean;
  onClose: () => void;
  onSave: (transaction: Transaction) => void;
}

export default function TransactionForm({
  transaction,
  isOpen,
  onClose,
  onSave,
}: TransactionFormProps) {
  const [formData, setFormData] = useState({
    transactionDate: new Date().toISOString().split("T")[0],
    description: "",
    reference: "",
  });

  const [entries, setEntries] = useState<TransactionEntry[]>([
    { accountId: "", description: "", debitAmount: 0, creditAmount: 0 },
    { accountId: "", description: "", debitAmount: 0, creditAmount: 0 },
  ]);

  const [accounts, setAccounts] = useState<Account[]>([]);
  const [templates, setTemplates] = useState<any[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();

  useEffect(() => {
    if (isOpen) {
      loadAccounts();
      loadTemplates();
      if (transaction) {
        setFormData({
          transactionDate: transaction.transactionDate,
          description: transaction.description,
          reference: transaction.reference || "",
        });
        setEntries(
          transaction.entries.map((entry) => ({
            id: entry.id,
            accountId: entry.accountId,
            description: entry.description || "",
            debitAmount: entry.debitAmount,
            creditAmount: entry.creditAmount,
          }))
        );
      } else {
        // Reset form for new transaction
        setFormData({
          transactionDate: new Date().toISOString().split("T")[0],
          description: "",
          reference: "",
        });
        setEntries([
          { accountId: "", description: "", debitAmount: 0, creditAmount: 0 },
          { accountId: "", description: "", debitAmount: 0, creditAmount: 0 },
        ]);
      }
    }
  }, [isOpen, transaction]);

  const loadAccounts = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      const accountsData = await accountService.getAccounts(currentCompany.id, {
        active: true,
      });
      setAccounts(accountsData);
    } catch (error) {
      console.error("Failed to load accounts:", error);
      showNotification({
        type: "error",
        title: "Failed to load accounts",
        message: "Failed to load accounts",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadTemplates = async () => {
    if (!currentCompany) return;

    try {
      const templatesData = await transactionTemplateService.getTemplates(currentCompany.id, {
        limit: 50,
        sortBy: 'usage',
        sortOrder: 'desc',
      });
      setTemplates(templatesData.templates);
    } catch (error) {
      console.error("Failed to load templates:", error);
      // Don't show error notification for templates as it's optional
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleEntryChange = (
    index: number,
    field: keyof TransactionEntry,
    value: string | number
  ) => {
    setEntries((prev) => {
      const newEntries = [...prev];
      newEntries[index] = { ...newEntries[index], [field]: value };

      // If changing debit amount, clear credit amount and vice versa
      if (field === "debitAmount" && Number(value) > 0) {
        newEntries[index].creditAmount = 0;
      } else if (field === "creditAmount" && Number(value) > 0) {
        newEntries[index].debitAmount = 0;
      }

      return newEntries;
    });
  };

  const addEntry = () => {
    setEntries((prev) => [
      ...prev,
      { accountId: "", description: "", debitAmount: 0, creditAmount: 0 },
    ]);
  };

  const handleApplyTemplate = async () => {
    if (!selectedTemplate || !currentCompany) return;

    try {
      setLoading(true);
      const transactionData = await transactionTemplateService.applyTemplate(
        currentCompany.id,
        selectedTemplate,
        {
          transactionDate: formData.transactionDate,
          description: formData.description || 'Transaction from template',
          variables: {},
        }
      );

      // Apply template data to form
      setFormData(prev => ({
        ...prev,
        description: transactionData.description,
        reference: transactionData.reference || prev.reference,
      }));

      setEntries(transactionData.entries.map((entry: any) => ({
        accountId: entry.accountId,
        description: entry.description,
        debitAmount: entry.debitAmount,
        creditAmount: entry.creditAmount,
      })));

      setShowTemplateSelector(false);
      setSelectedTemplate('');
      showNotification({
        type: 'success',
        title: 'Template applied',
        message: 'Transaction template has been applied successfully',
      });
    } catch (error) {
      console.error('Failed to apply template:', error);
      showNotification({
        type: 'error',
        title: 'Failed to apply template',
        message: 'Could not apply the selected template',
      });
    } finally {
      setLoading(false);
    }
  };

  const removeEntry = (index: number) => {
    if (entries.length > 2) {
      setEntries((prev) => prev.filter((_, i) => i !== index));
    }
  };

  const getBalance = () => {
    return transactionService.validateBalance(entries);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await saveTransaction(false);
  };

  const handleSubmitForApproval = async () => {
    await saveTransaction(true);
  };

  const saveTransaction = async (submitForApproval: boolean = false) => {
    if (!currentCompany) return;

    // Validate form
    if (!formData.description.trim()) {
      showNotification({
        type: "error",
        title: "Description is required",
        message: "Description is required",
      });
      return;
    }

    // Validate entries
    const validEntries = entries.filter(
      (entry) =>
        entry.accountId && (entry.debitAmount > 0 || entry.creditAmount > 0)
    );

    if (validEntries.length < 2) {
      showNotification({
        type: "error",
        title: "At least two entries are required",
        message: "At least two entries are required",
      });
      return;
    }

    // Validate balance
    const balance = getBalance();
    if (!balance.isBalanced) {
      showNotification(
       {
        type: "error",
        title: "Transaction must be balanced",
        message: "Transaction must be balanced (debits = credits)",
      }
      );
      return;
    }

    if (submitForApproval) {
      setSubmitting(true);
    } else {
      setSaving(true);
    }

    try {
      const transactionData = {
        companyId: currentCompany.id,
        transactionDate: formData.transactionDate,
        description: formData.description,
        reference: formData.reference || undefined,
        entries: validEntries.map((entry) => ({
          accountId: entry.accountId,
          description: entry.description || undefined,
          debitAmount: entry.debitAmount,
          creditAmount: entry.creditAmount,
        })),
      };

      let savedTransaction: Transaction;

      if (transaction) {
        savedTransaction = await transactionService.updateTransaction(
          currentCompany.id,
          transaction.id,
          transactionData
        );
        showNotification({
          type: "success",
          title: "Transaction updated",
          message: "Transaction updated successfully",
        });
      } else {
        savedTransaction = await transactionService.createTransaction(
          transactionData
        );
        showNotification({
          type: "success",
          title: "Transaction created",
          message: "Transaction created successfully",
        });
      }

      // If submitting for approval, submit the transaction
      if (submitForApproval && savedTransaction.status === "DRAFT") {
        savedTransaction = await transactionService.submitTransaction(
          currentCompany.id,
          savedTransaction.id
        );
        showNotification({
          type: "success",
          title: "Transaction submitted",
          message: "Transaction submitted for approval successfully",
        });
      }

      onSave(savedTransaction);
      onClose();
    } catch (error) {
      console.error("Failed to save transaction:", error);
      showNotification({
        type: "error",
        title: "Failed to save transaction",
        message: "Failed to save transaction",
      });
    } finally {
      setSaving(false);
      setSubmitting(false);
    }
  };

  const balance = getBalance();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div
          className="fixed inset-0"
          style={{ backgroundColor: 'rgba(0,0,0,0.6)' }}
          onClick={onClose}
        />

        <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              {transaction ? "Edit Transaction" : "New Transaction"}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-pointer"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Template Selector */}
            {!transaction && templates.length > 0 && (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                    Use Transaction Template
                  </h3>
                  <button
                    type="button"
                    onClick={() => setShowTemplateSelector(!showTemplateSelector)}
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                  >
                    <DocumentDuplicateIcon className="h-5 w-5" />
                  </button>
                </div>

                {showTemplateSelector && (
                  <div className="space-y-3">
                    <select
                      value={selectedTemplate}
                      onChange={(e) => setSelectedTemplate(e.target.value)}
                      className="w-full px-3 py-2 border border-blue-300 dark:border-blue-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Select a template...</option>
                      {templates.map((template) => (
                        <option key={template.id} value={template.id}>
                          {template.name} ({template.category})
                        </option>
                      ))}
                    </select>

                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={handleApplyTemplate}
                        disabled={!selectedTemplate}
                        className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Apply Template
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setShowTemplateSelector(false);
                          setSelectedTemplate('');
                        }}
                        className="px-3 py-1 text-sm bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-400 dark:hover:bg-gray-500"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Transaction Details */}
            <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Date *
                </label>
                <input
                  type="date"
                  value={formData.transactionDate}
                  onChange={(e) =>
                    handleInputChange("transactionDate", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description *
                </label>
                <input
                  type="text"
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter transaction description"
                  required
                />
              </div>

              <div className="md:col-span-3">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Reference
                </label>
                <input
                  type="text"
                  value={formData.reference}
                  onChange={(e) =>
                    handleInputChange("reference", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter reference number or note"
                />
              </div>
            </div>

            {/* Transaction Entries */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Transaction Entries
                </h3>
                <button
                  type="button"
                  onClick={addEntry}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 dark:bg-blue-900/20 hover:bg-blue-200 cursor-pointer"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Add Entry
                </button>
              </div>

              <div className="space-y-3">
                {entries.map((entry, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-12 gap-3 items-end p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  >
                    <div className="col-span-4">
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Account *
                      </label>
                      <select
                        value={entry.accountId}
                        onChange={(e) =>
                          handleEntryChange(index, "accountId", e.target.value)
                        }
                        className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        required
                      >
                        <option value="">Select account</option>
                        {accounts.map((account) => (
                          <option key={account.id} value={account.id}>
                            {account.code} - {account.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="col-span-3">
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Description
                      </label>
                      <input
                        type="text"
                        value={entry.description}
                        onChange={(e) =>
                          handleEntryChange(
                            index,
                            "description",
                            e.target.value
                          )
                        }
                        className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        placeholder="Entry description"
                      />
                    </div>

                    <div className="col-span-2">
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Debit
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={entry.debitAmount || ""}
                        onChange={(e) =>
                          handleEntryChange(
                            index,
                            "debitAmount",
                            Number(e.target.value)
                          )
                        }
                        className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        placeholder="0.00"
                      />
                    </div>

                    <div className="col-span-2">
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Credit
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={entry.creditAmount || ""}
                        onChange={(e) =>
                          handleEntryChange(
                            index,
                            "creditAmount",
                            Number(e.target.value)
                          )
                        }
                        className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        placeholder="0.00"
                      />
                    </div>

                    <div className="col-span-1">
                      {entries.length > 2 && (
                        <button
                          type="button"
                          onClick={() => removeEntry(index)}
                          className="p-1 text-red-600 dark:text-red-400 hover:text-red-800 cursor-pointer"
                          title="Remove entry"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Balance Summary */}
              <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
                <div className="grid bg-white dark:bg-gray-800 grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Total Debits:</span>
                    <span className="ml-2">
                      ${balance.totalDebits.toFixed(2)}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Total Credits:</span>
                    <span className="ml-2">
                      ${balance.totalCredits.toFixed(2)}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Difference:</span>
                    <span
                      className={`ml-2 ${
                        balance.isBalanced ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
                      }`}
                    >
                      ${balance.difference.toFixed(2)}
                    </span>
                  </div>
                </div>
                {!balance.isBalanced && (
                  <p className="text-red-600 dark:text-red-400 text-xs mt-1">
                    Transaction must be balanced (debits must equal credits)
                  </p>
                )}
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 cursor-pointer"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={saving || submitting || !balance.isBalanced}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
              >
                {saving
                  ? "Saving..."
                  : transaction
                  ? "Update Transaction"
                  : "Save as Draft"}
              </button>
              {!transaction && (
                <button
                  type="button"
                  onClick={handleSubmitForApproval}
                  disabled={saving || submitting || !balance.isBalanced}
                  className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                >
                  {submitting ? "Submitting..." : "Save & Submit for Approval"}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
