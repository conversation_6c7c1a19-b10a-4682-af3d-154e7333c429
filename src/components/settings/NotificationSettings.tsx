import { useState, useEffect } from "react";
import { settingsService, type CompanySettings } from "../../services/settingsService";
import { useCompany } from "../../contexts/CompanyContext";

export default function NotificationSettings() {
  const { currentCompany } = useCompany();
  const [companySettings, setCompanySettings] = useState<CompanySettings | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [notificationForm, setNotificationForm] = useState({
    emailNotifications: true,
    overdueReminders: true,
    paymentConfirmations: true,
    reportSchedules: false,
  });

  useEffect(() => {
    loadNotificationSettings();
  }, [currentCompany]);

  const loadNotificationSettings = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      setError(null);
      const settings = await settingsService.getCompanySettings(currentCompany.id);
      setCompanySettings(settings);
      
      if (settings.settings?.notifications) {
        setNotificationForm({
          emailNotifications: settings.settings.notifications.emailNotifications ?? true,
          overdueReminders: settings.settings.notifications.overdueReminders ?? true,
          paymentConfirmations: settings.settings.notifications.paymentConfirmations ?? true,
          reportSchedules: settings.settings.notifications.reportSchedules ?? false,
        });
      }
    } catch (err) {
      console.error("Failed to load notification settings:", err);
      setError("Failed to load notification settings. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentCompany || !companySettings) return;

    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const updatedSettings = await settingsService.updateCompanySettings(currentCompany.id, {
        settings: {
          ...companySettings.settings,
          notifications: notificationForm,
        },
      });
      setCompanySettings(updatedSettings);
      setSuccess("Notification settings updated successfully!");
    } catch (err: any) {
      console.error("Failed to update notification settings:", err);
      setError(err.response?.data?.error || "Failed to update notification settings. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Notification Settings</h2>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
          Configure email alerts and notification preferences
        </p>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="rounded-md bg-green-50 dark:bg-green-900/20 p-4">
          <div className="text-sm text-green-700">{success}</div>
        </div>
      )}

      {error && (
        <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
          <div className="text-sm text-red-700 dark:text-red-400">{error}</div>
        </div>
      )}

      {/* Notification Configuration */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Email Notifications</h3>
        </div>
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="space-y-4 bg-white dark:bg-gray-800">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">Email Notifications</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Receive general email notifications</p>
              </div>
              <input
                type="checkbox"
                checked={notificationForm.emailNotifications}
                onChange={(e) => setNotificationForm(prev => ({ ...prev, emailNotifications: e.target.checked }))}
                className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">Overdue Reminders</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Get notified about overdue invoices and payments</p>
              </div>
              <input
                type="checkbox"
                checked={notificationForm.overdueReminders}
                onChange={(e) => setNotificationForm(prev => ({ ...prev, overdueReminders: e.target.checked }))}
                className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">Payment Confirmations</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Receive confirmations when payments are received</p>
              </div>
              <input
                type="checkbox"
                checked={notificationForm.paymentConfirmations}
                onChange={(e) => setNotificationForm(prev => ({ ...prev, paymentConfirmations: e.target.checked }))}
                className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">Report Schedules</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Get scheduled financial reports via email</p>
              </div>
              <input
                type="checkbox"
                checked={notificationForm.reportSchedules}
                onChange={(e) => setNotificationForm(prev => ({ ...prev, reportSchedules: e.target.checked }))}
                className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={saving}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {saving ? "Saving..." : "Save Changes"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
