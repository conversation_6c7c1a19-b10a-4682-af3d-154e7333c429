import { useState, useEffect } from "react";
import {
  ServerIcon,
  CircleStackIcon,
  CpuChipIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import {
  settingsService,
  type SystemInfo,
} from "../../services/settingsService";
import { useCompany } from "../../contexts/CompanyContext";

export default function SystemSettings() {
  const { currentCompany } = useCompany();
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadSystemInfo();
  }, [currentCompany]);

  const loadSystemInfo = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      setError(null);
      const info = await settingsService.getSystemInfo(currentCompany.id);
      setSystemInfo(info);
    } catch (err) {
      console.error("Failed to load system info:", err);
      setError("Failed to load system information. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white dark:text-white dark:text-white">
          System Information
        </h2>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
          View system status, performance metrics, and feature availability
        </p>
      </div>

      {error && (
        <div className="rounded-md bg-red-50 dark:bg-red-900/20 dark:bg-red-900/20 dark:bg-red-900/20 p-4">
          <div className="text-sm text-red-700 dark:text-red-400 dark:text-red-400 dark:text-red-400">{error}</div>
        </div>
      )}

      {systemInfo && (
        <>
          {/* System Overview */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 dark:border-gray-700 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white dark:text-white dark:text-white">
                System Overview
              </h3>
            </div>
            <div className="p-6">
              <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
                    <ServerIcon className="h-6 w-6" />
                  </div>
                  <div className="mt-3">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white dark:text-white dark:text-white">
                      Version
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      {systemInfo.version}
                    </p>
                  </div>
                </div>

                <div className="text-center">
                  <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-md bg-green-500 text-white">
                    <CircleStackIcon className="h-6 w-6" />
                  </div>
                  <div className="mt-3">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white dark:text-white dark:text-white">
                      Database
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      {systemInfo.database.type}
                    </p>
                    <p className="text-xs text-green-600 dark:text-green-400 dark:text-green-400 dark:text-green-400">
                      {systemInfo.database.status}
                    </p>
                  </div>
                </div>

                <div className="text-center">
                  <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
                    <CpuChipIcon className="h-6 w-6" />
                  </div>
                  <div className="mt-3">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white dark:text-white dark:text-white">
                      Environment
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 capitalize">
                      {systemInfo.environment}
                    </p>
                  </div>
                </div>

                <div className="text-center">
                  <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-md bg-purple-500 text-white">
                    <ClockIcon className="h-6 w-6" />
                  </div>
                  <div className="mt-3">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white dark:text-white dark:text-white">
                      Uptime
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      {settingsService.formatUptime(systemInfo.server.uptime)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Server Information */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 dark:border-gray-700 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white dark:text-white dark:text-white">
                Server Information
              </h3>
            </div>
            <div className="p-6">
              <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white dark:text-white mb-3">
                    Platform Details
                  </h4>
                  <dl className="space-y-2">
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Platform:</dt>
                      <dd className="text-sm text-gray-900 dark:text-white dark:text-white capitalize">
                        {systemInfo.server.platform}
                      </dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        Node.js Version:
                      </dt>
                      <dd className="text-sm text-gray-900 dark:text-white dark:text-white">
                        {systemInfo.server.nodeVersion}
                      </dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        Database Version:
                      </dt>
                      <dd className="text-sm text-gray-900 dark:text-white dark:text-white">
                        {systemInfo.database.version}
                      </dd>
                    </div>
                  </dl>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white dark:text-white mb-3">
                    Memory Usage
                  </h4>
                  <dl className="space-y-2">
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">RSS:</dt>
                      <dd className="text-sm text-gray-900 dark:text-white dark:text-white">
                        {settingsService.formatMemory(
                          systemInfo.server.memory.rss
                        )}
                      </dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Heap Used:</dt>
                      <dd className="text-sm text-gray-900 dark:text-white dark:text-white">
                        {settingsService.formatMemory(
                          systemInfo.server.memory.heapUsed
                        )}
                      </dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Heap Total:</dt>
                      <dd className="text-sm text-gray-900 dark:text-white dark:text-white">
                        {settingsService.formatMemory(
                          systemInfo.server.memory.heapTotal
                        )}
                      </dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">External:</dt>
                      <dd className="text-sm text-gray-900 dark:text-white dark:text-white">
                        {settingsService.formatMemory(
                          systemInfo.server.memory.external
                        )}
                      </dd>
                    </div>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* Feature Availability */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white dark:text-white">
                Feature Availability
              </h3>
            </div>
            <div className="p-6">
              <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white dark:text-white mb-3">
                    Core Features
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        Multi-Currency Support
                      </span>
                      {systemInfo.features.multiCurrency ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-500 dark:text-green-400" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-red-500 dark:text-red-400" />
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Audit Trail</span>
                      {systemInfo.features.auditTrail ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-500 dark:text-green-400" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-red-500 dark:text-red-400" />
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        Financial Reporting
                      </span>
                      {systemInfo.features.reporting ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-500 dark:text-green-400" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-red-500 dark:text-red-400" />
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        Backup & Recovery
                      </span>
                      {systemInfo.features.backup ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-500 dark:text-green-400" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-red-500 dark:text-red-400" />
                      )}
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white dark:text-white mb-3">
                    Advanced Features
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        Third-party Integrations
                      </span>
                      {systemInfo.features.integrations ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-500 dark:text-green-400" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-red-500 dark:text-red-400" />
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        Two-Factor Authentication
                      </span>
                      {systemInfo.features.twoFactorAuth ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-500 dark:text-green-400" />
                      ) : (
                        <div className="flex items-center">
                          <XCircleIcon className="h-5 w-5 text-yellow-500 dark:text-yellow-400" />
                          <span className="ml-1 text-xs text-yellow-600 dark:text-yellow-400 dark:text-yellow-400">
                            Coming Soon
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        Single Sign-On (SSO)
                      </span>
                      {systemInfo.features.sso ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-500 dark:text-green-400" />
                      ) : (
                        <div className="flex items-center">
                          <XCircleIcon className="h-5 w-5 text-yellow-500 dark:text-yellow-400" />
                          <span className="ml-1 text-xs text-yellow-600 dark:text-yellow-400 dark:text-yellow-400">
                            Coming Soon
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* System Health */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white dark:text-white">
                System Health
              </h3>
            </div>
            <div className="p-6">
              <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/20 dark:bg-green-900/20">
                    <CheckCircleIcon className="h-6 w-6 text-green-600 dark:text-green-400 dark:text-green-400" />
                  </div>
                  <div className="mt-3">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white dark:text-white">
                      API Status
                    </h4>
                    <p className="text-sm text-green-600 dark:text-green-400 dark:text-green-400">Operational</p>
                  </div>
                </div>

                <div className="text-center">
                  <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/20 dark:bg-green-900/20">
                    <CheckCircleIcon className="h-6 w-6 text-green-600 dark:text-green-400 dark:text-green-400" />
                  </div>
                  <div className="mt-3">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white dark:text-white">
                      Database
                    </h4>
                    <p className="text-sm text-green-600 dark:text-green-400 dark:text-green-400">Connected</p>
                  </div>
                </div>

                <div className="text-center">
                  <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/20 dark:bg-green-900/20">
                    <CheckCircleIcon className="h-6 w-6 text-green-600 dark:text-green-400 dark:text-green-400" />
                  </div>
                  <div className="mt-3">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white dark:text-white">
                      Services
                    </h4>
                    <p className="text-sm text-green-600 dark:text-green-400 dark:text-green-400">All Running</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* System Actions */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white dark:text-white">
                System Actions
              </h3>
            </div>
            <div className="p-6">
              <div className="space-y-4 bg-white dark:bg-gray-800">
                <button
                  onClick={loadSystemInfo}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700"
                >
                  Refresh System Info
                </button>

                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  <p>Last updated: {new Date().toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
