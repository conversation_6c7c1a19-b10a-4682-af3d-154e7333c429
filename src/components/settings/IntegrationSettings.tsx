import { useState, useEffect } from "react";
import { EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";
import { settingsService, type CompanySettings } from "../../services/settingsService";
import { useCompany } from "../../contexts/CompanyContext";

export default function IntegrationSettings() {
  const { currentCompany } = useCompany();
  const [companySettings, setCompanySettings] = useState<CompanySettings | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showApiKeys, setShowApiKeys] = useState({
    banking: false,
    payment: false,
    exchange: false,
  });

  const [integrationForm, setIntegrationForm] = useState({
    bankingApi: {
      enabled: false,
      provider: "",
      apiKey: "",
    },
    paymentGateway: {
      enabled: false,
      provider: "",
      apiKey: "",
      webhookUrl: "",
    },
    exchangeRates: {
      enabled: false,
      provider: "",
      apiKey: "",
      updateFrequency: "daily",
    },
  });

  useEffect(() => {
    loadIntegrationSettings();
  }, [currentCompany]);

  const loadIntegrationSettings = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      setError(null);
      const settings = await settingsService.getCompanySettings(currentCompany.id);
      setCompanySettings(settings);
      
      if (settings.settings?.integrations) {
        setIntegrationForm({
          bankingApi: {
            enabled: settings.settings.integrations.bankingApi?.enabled || false,
            provider: settings.settings.integrations.bankingApi?.provider || "",
            apiKey: settings.settings.integrations.bankingApi?.apiKey || "",
          },
          paymentGateway: {
            enabled: settings.settings.integrations.paymentGateway?.enabled || false,
            provider: settings.settings.integrations.paymentGateway?.provider || "",
            apiKey: settings.settings.integrations.paymentGateway?.apiKey || "",
            webhookUrl: settings.settings.integrations.paymentGateway?.webhookUrl || "",
          },
          exchangeRates: {
            enabled: settings.settings.integrations.exchangeRates?.enabled || false,
            provider: settings.settings.integrations.exchangeRates?.provider || "",
            apiKey: settings.settings.integrations.exchangeRates?.apiKey || "",
            updateFrequency: settings.settings.integrations.exchangeRates?.updateFrequency || "daily",
          },
        });
      }
    } catch (err) {
      console.error("Failed to load integration settings:", err);
      setError("Failed to load integration settings. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentCompany || !companySettings) return;

    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const updatedSettings = await settingsService.updateCompanySettings(currentCompany.id, {
        settings: {
          ...companySettings.settings,
          integrations: integrationForm,
        },
      });
      setCompanySettings(updatedSettings);
      setSuccess("Integration settings updated successfully!");
    } catch (err: any) {
      console.error("Failed to update integration settings:", err);
      setError(err.response?.data?.error || "Failed to update integration settings. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const bankingProviders = [
    { value: "plaid", label: "Plaid" },
    { value: "yodlee", label: "Yodlee" },
    { value: "finicity", label: "Finicity" },
    { value: "open_banking", label: "Open Banking" },
  ];

  const paymentProviders = [
    { value: "stripe", label: "Stripe" },
    { value: "paypal", label: "PayPal" },
    { value: "square", label: "Square" },
    { value: "authorize_net", label: "Authorize.Net" },
  ];

  const exchangeProviders = [
    { value: "fixer", label: "Fixer.io" },
    { value: "openexchangerates", label: "Open Exchange Rates" },
    { value: "currencylayer", label: "CurrencyLayer" },
    { value: "exchangerate_api", label: "ExchangeRate-API" },
  ];

  const updateFrequencyOptions = [
    { value: "hourly", label: "Hourly" },
    { value: "daily", label: "Daily" },
    { value: "weekly", label: "Weekly" },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Integration Settings</h2>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
          Configure third-party services and API connections
        </p>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="rounded-md bg-green-50 dark:bg-green-900/20 p-4">
          <div className="text-sm text-green-700">{success}</div>
        </div>
      )}

      {error && (
        <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
          <div className="text-sm text-red-700 dark:text-red-400">{error}</div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Banking API */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Banking API</h3>
              <input
                type="checkbox"
                checked={integrationForm.bankingApi.enabled}
                onChange={(e) => setIntegrationForm(prev => ({
                  ...prev,
                  bankingApi: { ...prev.bankingApi, enabled: e.target.checked }
                }))}
                className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
            </div>
          </div>
          {integrationForm.bankingApi.enabled && (
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Provider
                </label>
                <select
                  value={integrationForm.bankingApi.provider}
                  onChange={(e) => setIntegrationForm(prev => ({
                    ...prev,
                    bankingApi: { ...prev.bankingApi, provider: e.target.value }
                  }))}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select a provider</option>
                  {bankingProviders.map((provider) => (
                    <option key={provider.value} value={provider.value}>
                      {provider.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  API Key
                </label>
                <div className="relative">
                  <input
                    type={showApiKeys.banking ? "text" : "password"}
                    value={integrationForm.bankingApi.apiKey}
                    onChange={(e) => setIntegrationForm(prev => ({
                      ...prev,
                      bankingApi: { ...prev.bankingApi, apiKey: e.target.value }
                    }))}
                    className="block w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    type="button"
                    onClick={() => setShowApiKeys(prev => ({ ...prev, banking: !prev.banking }))}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showApiKeys.banking ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Payment Gateway */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Payment Gateway</h3>
              <input
                type="checkbox"
                checked={integrationForm.paymentGateway.enabled}
                onChange={(e) => setIntegrationForm(prev => ({
                  ...prev,
                  paymentGateway: { ...prev.paymentGateway, enabled: e.target.checked }
                }))}
                className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
            </div>
          </div>
          {integrationForm.paymentGateway.enabled && (
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Provider
                </label>
                <select
                  value={integrationForm.paymentGateway.provider}
                  onChange={(e) => setIntegrationForm(prev => ({
                    ...prev,
                    paymentGateway: { ...prev.paymentGateway, provider: e.target.value }
                  }))}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select a provider</option>
                  {paymentProviders.map((provider) => (
                    <option key={provider.value} value={provider.value}>
                      {provider.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  API Key
                </label>
                <div className="relative">
                  <input
                    type={showApiKeys.payment ? "text" : "password"}
                    value={integrationForm.paymentGateway.apiKey}
                    onChange={(e) => setIntegrationForm(prev => ({
                      ...prev,
                      paymentGateway: { ...prev.paymentGateway, apiKey: e.target.value }
                    }))}
                    className="block w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    type="button"
                    onClick={() => setShowApiKeys(prev => ({ ...prev, payment: !prev.payment }))}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showApiKeys.payment ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                    )}
                  </button>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Webhook URL
                </label>
                <input
                  type="url"
                  value={integrationForm.paymentGateway.webhookUrl}
                  onChange={(e) => setIntegrationForm(prev => ({
                    ...prev,
                    paymentGateway: { ...prev.paymentGateway, webhookUrl: e.target.value }
                  }))}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          )}
        </div>

        {/* Exchange Rates */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Exchange Rates</h3>
              <input
                type="checkbox"
                checked={integrationForm.exchangeRates.enabled}
                onChange={(e) => setIntegrationForm(prev => ({
                  ...prev,
                  exchangeRates: { ...prev.exchangeRates, enabled: e.target.checked }
                }))}
                className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
            </div>
          </div>
          {integrationForm.exchangeRates.enabled && (
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Provider
                </label>
                <select
                  value={integrationForm.exchangeRates.provider}
                  onChange={(e) => setIntegrationForm(prev => ({
                    ...prev,
                    exchangeRates: { ...prev.exchangeRates, provider: e.target.value }
                  }))}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select a provider</option>
                  {exchangeProviders.map((provider) => (
                    <option key={provider.value} value={provider.value}>
                      {provider.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  API Key
                </label>
                <div className="relative">
                  <input
                    type={showApiKeys.exchange ? "text" : "password"}
                    value={integrationForm.exchangeRates.apiKey}
                    onChange={(e) => setIntegrationForm(prev => ({
                      ...prev,
                      exchangeRates: { ...prev.exchangeRates, apiKey: e.target.value }
                    }))}
                    className="block w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    type="button"
                    onClick={() => setShowApiKeys(prev => ({ ...prev, exchange: !prev.exchange }))}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showApiKeys.exchange ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                    )}
                  </button>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Update Frequency
                </label>
                <select
                  value={integrationForm.exchangeRates.updateFrequency}
                  onChange={(e) => setIntegrationForm(prev => ({
                    ...prev,
                    exchangeRates: { ...prev.exchangeRates, updateFrequency: e.target.value }
                  }))}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  {updateFrequencyOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={saving}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {saving ? "Saving..." : "Save Changes"}
          </button>
        </div>
      </form>
    </div>
  );
}
