import { useState, useEffect } from 'react';
import { 
  CogIcon, 
  PlusIcon, 
  PlayIcon, 
  PauseIcon, 
  TrashIcon,
  ClockIcon,
  BellIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';
import { apiService } from '../../services/api';

interface Workflow {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  trigger: {
    type: 'SCHEDULE' | 'EVENT' | 'MANUAL' | 'CONDITION';
    config: any;
  };
  actions: Array<{
    type: string;
    config: any;
  }>;
  lastRunAt?: string;
  nextRunAt?: string;
  runCount: number;
}

interface WorkflowExecution {
  id: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  startedAt: string;
  completedAt?: string;
  error?: string;
}

export default function WorkflowSettings() {
  const { currentCompany } = useCompany();
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [loading, setLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newWorkflow, setNewWorkflow] = useState({
    name: '',
    description: '',
    triggerType: 'SCHEDULE',
    schedule: '',
    actionType: 'EMAIL',
    actionConfig: {}
  });

  useEffect(() => {
    if (currentCompany) {
      loadWorkflows();
      loadExecutions();
    }
  }, [currentCompany]);

  const loadWorkflows = async () => {
    if (!currentCompany) return;
    
    try {
      setLoading(true);
      const response = await apiService.get(`/workflows/${currentCompany.id}`);
      setWorkflows((response as any).workflows || []);
    } catch (error) {
      console.error('Failed to load workflows:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadExecutions = async () => {
    if (!currentCompany) return;
    
    try {
      const response = await apiService.get(`/workflows/${currentCompany.id}/executions`);
      setExecutions((response as any).executions || []);
    } catch (error) {
      console.error('Failed to load executions:', error);
    }
  };

  const createWorkflow = async () => {
    if (!currentCompany || !newWorkflow.name) return;
    
    try {
      const workflowData = {
        name: newWorkflow.name,
        description: newWorkflow.description,
        isActive: true,
        trigger: {
          type: newWorkflow.triggerType,
          config: newWorkflow.triggerType === 'SCHEDULE' 
            ? { schedule: newWorkflow.schedule }
            : {}
        },
        actions: [{
          type: newWorkflow.actionType,
          config: newWorkflow.actionConfig
        }]
      };

      await apiService.post(`/workflows/${currentCompany.id}`, workflowData);
      setShowCreateForm(false);
      setNewWorkflow({
        name: '',
        description: '',
        triggerType: 'SCHEDULE',
        schedule: '',
        actionType: 'EMAIL',
        actionConfig: {}
      });
      await loadWorkflows();
    } catch (error) {
      console.error('Failed to create workflow:', error);
      alert('Failed to create workflow');
    }
  };

  const toggleWorkflow = async (workflowId: string, isActive: boolean) => {
    if (!currentCompany) return;
    
    try {
      await apiService.put(`/workflows/${currentCompany.id}/${workflowId}`, {
        isActive: !isActive
      });
      await loadWorkflows();
    } catch (error) {
      console.error('Failed to toggle workflow:', error);
    }
  };

  const executeWorkflow = async (workflowId: string) => {
    if (!currentCompany) return;
    
    try {
      await apiService.post(`/workflows/${currentCompany.id}/${workflowId}/execute`, {});
      alert('Workflow executed successfully');
      await loadExecutions();
    } catch (error) {
      console.error('Failed to execute workflow:', error);
      alert('Failed to execute workflow');
    }
  };

  const deleteWorkflow = async (workflowId: string) => {
    if (!currentCompany) return;
    
    if (!confirm('Are you sure you want to delete this workflow?')) {
      return;
    }
    
    try {
      await apiService.delete(`/workflows/${currentCompany.id}/${workflowId}`);
      await loadWorkflows();
    } catch (error) {
      console.error('Failed to delete workflow:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getTriggerDescription = (trigger: any) => {
    switch (trigger.type) {
      case 'SCHEDULE':
        return `Scheduled: ${trigger.config.schedule || 'Not configured'}`;
      case 'EVENT':
        return `Event: ${trigger.config.event || 'Not configured'}`;
      case 'MANUAL':
        return 'Manual execution only';
      default:
        return 'Unknown trigger';
    }
  };

  const getActionDescription = (actions: any[]) => {
    if (actions.length === 0) return 'No actions';
    if (actions.length === 1) return `${actions[0].type} action`;
    return `${actions.length} actions`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Workflow Automation</h3>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Automate repetitive tasks and business processes
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Create Workflow
        </button>
      </div>

      {/* Create Workflow Form */}
      {showCreateForm && (
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Create New Workflow</h4>
          <div className="grid bg-white dark:bg-gray-800 grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
              <input
                type="text"
                value={newWorkflow.name}
                onChange={(e) => setNewWorkflow({ ...newWorkflow, name: e.target.value })}
                className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Workflow name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Trigger Type</label>
              <select
                value={newWorkflow.triggerType}
                onChange={(e) => setNewWorkflow({ ...newWorkflow, triggerType: e.target.value })}
                className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="SCHEDULE">Schedule</option>
                <option value="EVENT">Event</option>
                <option value="MANUAL">Manual</option>
              </select>
            </div>
            {newWorkflow.triggerType === 'SCHEDULE' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Schedule (Cron)</label>
                <input
                  type="text"
                  value={newWorkflow.schedule}
                  onChange={(e) => setNewWorkflow({ ...newWorkflow, schedule: e.target.value })}
                  className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="0 9 * * 1 (Every Monday at 9 AM)"
                />
              </div>
            )}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Action Type</label>
              <select
                value={newWorkflow.actionType}
                onChange={(e) => setNewWorkflow({ ...newWorkflow, actionType: e.target.value })}
                className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="EMAIL">Send Email</option>
                <option value="NOTIFICATION">Send Notification</option>
                <option value="GENERATE_REPORT">Generate Report</option>
                <option value="WEBHOOK">Call Webhook</option>
              </select>
            </div>
            <div className="sm:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
              <textarea
                value={newWorkflow.description}
                onChange={(e) => setNewWorkflow({ ...newWorkflow, description: e.target.value })}
                rows={3}
                className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Describe what this workflow does..."
              />
            </div>
          </div>
          <div className="mt-4 flex justify-end space-x-3">
            <button
              onClick={() => setShowCreateForm(false)}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800"
            >
              Cancel
            </button>
            <button
              onClick={createWorkflow}
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              Create Workflow
            </button>
          </div>
        </div>
      )}

      {/* Workflows List */}
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white">Active Workflows</h4>
        </div>
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {loading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Loading workflows...</p>
            </div>
          ) : workflows.length === 0 ? (
            <div className="p-6 text-center">
              <CogIcon className="h-12 w-12 mx-auto text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No workflows</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Get started by creating your first workflow.</p>
            </div>
          ) : (
            workflows.map((workflow) => (
              <div key={workflow.id} className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white">{workflow.name}</h4>
                      <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        workflow.isActive 
                          ? 'bg-green-100 dark:bg-green-900/20 text-green-800' 
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
                      }`}>
                        {workflow.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    {workflow.description && (
                      <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{workflow.description}</p>
                    )}
                    <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      <span className="flex items-center">
                        <ClockIcon className="h-4 w-4 mr-1" />
                        {getTriggerDescription(workflow.trigger)}
                      </span>
                      <span className="flex items-center">
                        <BellIcon className="h-4 w-4 mr-1" />
                        {getActionDescription(workflow.actions)}
                      </span>
                      <span>Runs: {workflow.runCount}</span>
                      {workflow.lastRunAt && (
                        <span>Last: {formatDate(workflow.lastRunAt)}</span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => executeWorkflow(workflow.id)}
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-600 dark:text-blue-400"
                      title="Execute now"
                    >
                      <PlayIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => toggleWorkflow(workflow.id, workflow.isActive)}
                      className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-300"
                      title={workflow.isActive ? 'Pause' : 'Activate'}
                    >
                      {workflow.isActive ? (
                        <PauseIcon className="h-4 w-4" />
                      ) : (
                        <PlayIcon className="h-4 w-4" />
                      )}
                    </button>
                    <button
                      onClick={() => deleteWorkflow(workflow.id)}
                      className="text-red-600 dark:text-red-400 hover:text-red-500 dark:text-red-400"
                      title="Delete"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Recent Executions */}
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white">Recent Executions</h4>
        </div>
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {executions.length === 0 ? (
            <div className="p-6 text-center">
              <DocumentTextIcon className="h-12 w-12 mx-auto text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No executions</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Workflow executions will appear here.</p>
            </div>
          ) : (
            executions.slice(0, 10).map((execution) => (
              <div key={execution.id} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      execution.status === 'COMPLETED' 
                        ? 'bg-green-100 dark:bg-green-900/20 text-green-800'
                        : execution.status === 'FAILED'
                        ? 'bg-red-100 dark:bg-red-900/20 text-red-800'
                        : execution.status === 'RUNNING'
                        ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
                    }`}>
                      {execution.status}
                    </span>
                    <span className="ml-3 text-sm text-gray-900 dark:text-white">
                      Started: {formatDate(execution.startedAt)}
                    </span>
                    {execution.completedAt && (
                      <span className="ml-3 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        Completed: {formatDate(execution.completedAt)}
                      </span>
                    )}
                  </div>
                </div>
                {execution.error && (
                  <div className="mt-2 text-sm text-red-600 dark:text-red-400">
                    Error: {execution.error}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
