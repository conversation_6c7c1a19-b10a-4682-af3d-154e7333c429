import { useState, useEffect } from "react";
import { settingsService, type CompanySettings } from "../../services/settingsService";
import { useCompany } from "../../contexts/CompanyContext";

export default function AccountingSettings() {
  const { currentCompany } = useCompany();
  const [companySettings, setCompanySettings] = useState<CompanySettings | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [accountingForm, setAccountingForm] = useState({
    fiscalYearStart: "01-01",
    timeZone: "America/New_York",
    dateFormat: "MM/DD/YYYY",
    numberFormat: "en-US",
    defaultPaymentTerms: "NET_30",
    autoNumbering: {
      invoices: true,
      transactions: true,
      contacts: true,
    },
  });

  useEffect(() => {
    loadAccountingSettings();
  }, [currentCompany]);

  const loadAccountingSettings = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      setError(null);
      const settings = await settingsService.getCompanySettings(currentCompany.id);
      setCompanySettings(settings);
      
      if (settings.settings) {
        setAccountingForm({
          fiscalYearStart: settings.settings.fiscalYearStart || "01-01",
          timeZone: settings.settings.timeZone || "America/New_York",
          dateFormat: settings.settings.dateFormat || "MM/DD/YYYY",
          numberFormat: settings.settings.numberFormat || "en-US",
          defaultPaymentTerms: settings.settings.defaultPaymentTerms || "NET_30",
          autoNumbering: {
            invoices: settings.settings.autoNumbering?.invoices ?? true,
            transactions: settings.settings.autoNumbering?.transactions ?? true,
            contacts: settings.settings.autoNumbering?.contacts ?? true,
          },
        });
      }
    } catch (err) {
      console.error("Failed to load accounting settings:", err);
      setError("Failed to load accounting settings. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentCompany || !companySettings) return;

    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const updatedSettings = await settingsService.updateCompanySettings(currentCompany.id, {
        settings: {
          ...companySettings.settings,
          ...accountingForm,
        },
      });
      setCompanySettings(updatedSettings);
      setSuccess("Accounting settings updated successfully!");
    } catch (err: any) {
      console.error("Failed to update accounting settings:", err);
      setError(err.response?.data?.error || "Failed to update accounting settings. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const timeZones = settingsService.getTimeZones();
  const dateFormats = settingsService.getDateFormats();
  const numberFormats = settingsService.getNumberFormats();
  const paymentTerms = settingsService.getPaymentTerms();

  const fiscalYearOptions = [
    { value: "01-01", label: "January 1st (Calendar Year)" },
    { value: "04-01", label: "April 1st" },
    { value: "07-01", label: "July 1st" },
    { value: "10-01", label: "October 1st" },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Accounting Settings</h2>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
          Configure fiscal year, currencies, and accounting preferences
        </p>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="rounded-md bg-green-50 dark:bg-green-900/20 p-4">
          <div className="text-sm text-green-700">{success}</div>
        </div>
      )}

      {error && (
        <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
          <div className="text-sm text-red-700 dark:text-red-400">{error}</div>
        </div>
      )}

      {/* Accounting Configuration */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Accounting Configuration</h3>
        </div>
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Fiscal Year */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Fiscal Year Start
            </label>
            <select
              value={accountingForm.fiscalYearStart}
              onChange={(e) => setAccountingForm(prev => ({ ...prev, fiscalYearStart: e.target.value }))}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {fiscalYearOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
              This determines when your fiscal year begins for financial reporting.
            </p>
          </div>

          {/* Time Zone */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Time Zone
            </label>
            <select
              value={accountingForm.timeZone}
              onChange={(e) => setAccountingForm(prev => ({ ...prev, timeZone: e.target.value }))}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {timeZones.map((tz) => (
                <option key={tz.value} value={tz.value}>
                  {tz.label}
                </option>
              ))}
            </select>
          </div>

          {/* Date Format */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Date Format
            </label>
            <select
              value={accountingForm.dateFormat}
              onChange={(e) => setAccountingForm(prev => ({ ...prev, dateFormat: e.target.value }))}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {dateFormats.map((format) => (
                <option key={format.value} value={format.value}>
                  {format.label} - {format.example}
                </option>
              ))}
            </select>
          </div>

          {/* Number Format */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Number Format
            </label>
            <select
              value={accountingForm.numberFormat}
              onChange={(e) => setAccountingForm(prev => ({ ...prev, numberFormat: e.target.value }))}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {numberFormats.map((format) => (
                <option key={format.value} value={format.value}>
                  {format.label} - {format.example}
                </option>
              ))}
            </select>
          </div>

          {/* Default Payment Terms */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Default Payment Terms
            </label>
            <select
              value={accountingForm.defaultPaymentTerms}
              onChange={(e) => setAccountingForm(prev => ({ ...prev, defaultPaymentTerms: e.target.value }))}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {paymentTerms.map((term) => (
                <option key={term.value} value={term.value}>
                  {term.label}
                </option>
              ))}
            </select>
          </div>

          {/* Auto-Numbering */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">Auto-Numbering</h4>
            <div className="space-y-4 bg-white dark:bg-gray-800">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="auto-invoices"
                  checked={accountingForm.autoNumbering.invoices}
                  onChange={(e) => setAccountingForm(prev => ({
                    ...prev,
                    autoNumbering: {
                      ...prev.autoNumbering,
                      invoices: e.target.checked,
                    },
                  }))}
                  className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                />
                <label htmlFor="auto-invoices" className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Auto-number invoices
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="auto-transactions"
                  checked={accountingForm.autoNumbering.transactions}
                  onChange={(e) => setAccountingForm(prev => ({
                    ...prev,
                    autoNumbering: {
                      ...prev.autoNumbering,
                      transactions: e.target.checked,
                    },
                  }))}
                  className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                />
                <label htmlFor="auto-transactions" className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Auto-number transactions
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="auto-contacts"
                  checked={accountingForm.autoNumbering.contacts}
                  onChange={(e) => setAccountingForm(prev => ({
                    ...prev,
                    autoNumbering: {
                      ...prev.autoNumbering,
                      contacts: e.target.checked,
                    },
                  }))}
                  className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                />
                <label htmlFor="auto-contacts" className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  Auto-number contacts
                </label>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={saving}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {saving ? "Saving..." : "Save Changes"}
            </button>
          </div>
        </form>
      </div>

      {/* Additional Information */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Important Notes
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Changing the fiscal year start will affect financial reporting periods</li>
                <li>Date and number formats will be applied to all new transactions</li>
                <li>Auto-numbering settings only apply to new records created after enabling</li>
                <li>These settings affect all users in your organization</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
