import { useState, useEffect } from "react";
import { ShieldCheckIcon, ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import { settingsService, type CompanySettings } from "../../services/settingsService";
import { useCompany } from "../../contexts/CompanyContext";

export default function SecuritySettings() {
  const { currentCompany } = useCompany();
  const [companySettings, setCompanySettings] = useState<CompanySettings | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [securityForm, setSecurityForm] = useState({
    sessionTimeout: 480, // 8 hours in minutes
    passwordExpiry: 90, // days
    twoFactorAuth: false,
    ipWhitelist: [] as string[],
  });

  const [newIpAddress, setNewIpAddress] = useState("");

  useEffect(() => {
    loadSecuritySettings();
  }, [currentCompany]);

  const loadSecuritySettings = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      setError(null);
      const settings = await settingsService.getCompanySettings(currentCompany.id);
      setCompanySettings(settings);
      
      if (settings.settings?.security) {
        setSecurityForm({
          sessionTimeout: settings.settings.security.sessionTimeout || 480,
          passwordExpiry: settings.settings.security.passwordExpiry || 90,
          twoFactorAuth: settings.settings.security.twoFactorAuth || false,
          ipWhitelist: settings.settings.security.ipWhitelist || [],
        });
      }
    } catch (err) {
      console.error("Failed to load security settings:", err);
      setError("Failed to load security settings. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentCompany || !companySettings) return;

    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const updatedSettings = await settingsService.updateCompanySettings(currentCompany.id, {
        settings: {
          ...companySettings.settings,
          security: securityForm,
        },
      });
      setCompanySettings(updatedSettings);
      setSuccess("Security settings updated successfully!");
    } catch (err: any) {
      console.error("Failed to update security settings:", err);
      setError(err.response?.data?.error || "Failed to update security settings. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const addIpAddress = () => {
    if (newIpAddress && !securityForm.ipWhitelist.includes(newIpAddress)) {
      setSecurityForm(prev => ({
        ...prev,
        ipWhitelist: [...prev.ipWhitelist, newIpAddress],
      }));
      setNewIpAddress("");
    }
  };

  const removeIpAddress = (ip: string) => {
    setSecurityForm(prev => ({
      ...prev,
      ipWhitelist: prev.ipWhitelist.filter(address => address !== ip),
    }));
  };

  const sessionTimeoutOptions = [
    { value: 60, label: "1 hour" },
    { value: 120, label: "2 hours" },
    { value: 240, label: "4 hours" },
    { value: 480, label: "8 hours" },
    { value: 720, label: "12 hours" },
    { value: 1440, label: "24 hours" },
  ];

  const passwordExpiryOptions = [
    { value: 30, label: "30 days" },
    { value: 60, label: "60 days" },
    { value: 90, label: "90 days" },
    { value: 180, label: "180 days" },
    { value: 365, label: "1 year" },
    { value: 0, label: "Never" },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Security Settings</h2>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
          Configure security policies and access controls for your organization
        </p>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="rounded-md bg-green-50 dark:bg-green-900/20 p-4">
          <div className="text-sm text-green-700">{success}</div>
        </div>
      )}

      {error && (
        <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
          <div className="text-sm text-red-700 dark:text-red-400">{error}</div>
        </div>
      )}

      {/* Security Configuration */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Security Configuration</h3>
        </div>
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Session Management */}
          <div>
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">Session Management</h4>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Session Timeout
              </label>
              <select
                value={securityForm.sessionTimeout}
                onChange={(e) => setSecurityForm(prev => ({ ...prev, sessionTimeout: Number(e.target.value) }))}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                {sessionTimeoutOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                Users will be automatically logged out after this period of inactivity.
              </p>
            </div>
          </div>

          {/* Password Policy */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">Password Policy</h4>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Password Expiry
              </label>
              <select
                value={securityForm.passwordExpiry}
                onChange={(e) => setSecurityForm(prev => ({ ...prev, passwordExpiry: Number(e.target.value) }))}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                {passwordExpiryOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                Users will be required to change their password after this period.
              </p>
            </div>
          </div>

          {/* Two-Factor Authentication */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">Two-Factor Authentication</h4>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="two-factor-auth"
                checked={securityForm.twoFactorAuth}
                onChange={(e) => setSecurityForm(prev => ({ ...prev, twoFactorAuth: e.target.checked }))}
                className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
              <label htmlFor="two-factor-auth" className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                Require two-factor authentication for all users
              </label>
            </div>
            <div className="mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 rounded-md">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    Two-factor authentication is currently in development and will be available in a future update.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* IP Whitelist */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">IP Address Whitelist</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-4">
              Restrict access to your account from specific IP addresses. Leave empty to allow access from any IP.
            </p>
            
            <div className="space-y-4 bg-white dark:bg-gray-800">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newIpAddress}
                  onChange={(e) => setNewIpAddress(e.target.value)}
                  placeholder="*********** or ***********/24"
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  type="button"
                  onClick={addIpAddress}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700"
                >
                  Add
                </button>
              </div>

              {securityForm.ipWhitelist.length > 0 && (
                <div className="space-y-2">
                  <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300">Allowed IP Addresses:</h5>
                  {securityForm.ipWhitelist.map((ip, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
                      <span className="text-sm text-gray-900 dark:text-white">{ip}</span>
                      <button
                        type="button"
                        onClick={() => removeIpAddress(ip)}
                        className="text-sm text-red-600 dark:text-red-400 hover:text-red-500 dark:text-red-400"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={saving}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {saving ? "Saving..." : "Save Changes"}
            </button>
          </div>
        </form>
      </div>

      {/* Security Recommendations */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Security Recommendations</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4 bg-white dark:bg-gray-800">
            <div className="flex items-start">
              <ShieldCheckIcon className="h-5 w-5 text-green-500 dark:text-green-400 mt-0.5" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">Strong Password Policy</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Require users to use strong passwords with a mix of uppercase, lowercase, numbers, and symbols.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <ShieldCheckIcon className="h-5 w-5 text-green-500 dark:text-green-400 mt-0.5" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">Regular Security Audits</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Review user access and permissions regularly to ensure only authorized personnel have access.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <ShieldCheckIcon className="h-5 w-5 text-green-500 dark:text-green-400 mt-0.5" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">Backup and Recovery</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Maintain regular backups of your data and test recovery procedures periodically.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <ShieldCheckIcon className="h-5 w-5 text-green-500 dark:text-green-400 mt-0.5" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">Monitor Access Logs</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Regularly review audit logs to detect any suspicious activity or unauthorized access attempts.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
