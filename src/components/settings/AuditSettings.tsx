import { useState, useEffect } from "react";
import {
  MagnifyingGlassIcon,
  DocumentArrowDownIcon,
  PlusIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import {
  settingsService,
  type AuditLog,
  type DataRetentionPolicy,
} from "../../services/settingsService";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";

// Enhanced interfaces for new audit system
interface ComprehensiveAuditLog {
  id: string;
  tableName: string;
  recordId: string;
  actionType: 'INSERT' | 'UPDATE' | 'DELETE';
  userEmail: string;
  userRole: string;
  ipAddress: string;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  timestamp: string;
  reason: string;
  complianceFlags?: any;
}

interface AuditSummary {
  totalLogs: number;
  riskDistribution: { [key: string]: number };
  actionDistribution: { [key: string]: number };
  recentHighRiskActivities: number;
  complianceIssues: number;
}

export default function AuditSettings() {
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [comprehensiveAuditLogs, setComprehensiveAuditLogs] = useState<ComprehensiveAuditLog[]>([]);
  const [auditSummary, setAuditSummary] = useState<AuditSummary | null>(null);
  const [retentionPolicies, setRetentionPolicies] = useState<
    DataRetentionPolicy[]
  >([]);
  const [loading, setLoading] = useState(false);
  const [summaryLoading, setSummaryLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [creatingData, setCreatingData] = useState(false);
  const [activeTab, setActiveTab] = useState<'basic' | 'comprehensive' | 'compliance'>('basic');
  const [filters, setFilters] = useState({
    action: "",
    userId: "",
    startDate: "",
    endDate: "",
    riskLevel: "",
    tableName: "",
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 25,
    total: 0,
    pages: 0,
  });

  useEffect(() => {
    if (currentCompany) {
      if (activeTab === 'basic') {
        loadAuditLogs();
      } else if (activeTab === 'comprehensive') {
        // Only fetch if we have data, otherwise use mock data
        if (comprehensiveAuditLogs.length === 0) {
          loadMockComprehensiveData();
        }
      } else if (activeTab === 'compliance') {
        // Only fetch if we have data, otherwise use mock data
        if (!auditSummary) {
          loadMockAuditSummary();
        }
      }
      loadRetentionPolicies();
    }
  }, [currentCompany, filters, pagination.page, activeTab]);

  const loadAuditLogs = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      setError(null);
      const response = await settingsService.getAuditLogs(currentCompany.id, {
        page: pagination.page,
        limit: pagination.limit,
        ...filters,
      });
      setAuditLogs(response.auditLogs);
      setPagination((prev) => ({ ...prev, ...response.pagination }));
    } catch (err) {
      console.error("Failed to load audit logs:", err);
      setError("Failed to load audit logs. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const loadRetentionPolicies = async () => {
    if (!currentCompany) return;

    try {
      const policies = await settingsService.getDataRetentionPolicies(
        currentCompany.id
      );
      setRetentionPolicies(policies);
    } catch (err) {
      console.error("Failed to load retention policies:", err);
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const clearFilters = () => {
    setFilters({
      action: "",
      userId: "",
      startDate: "",
      endDate: "",
      riskLevel: "",
      tableName: "",
    });
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  // Mock data loading functions for development
  const loadMockComprehensiveData = () => {
    const mockData: ComprehensiveAuditLog[] = [
      {
        id: '1',
        tableName: 'transactions',
        recordId: 'txn-001',
        actionType: 'INSERT',
        userEmail: '<EMAIL>',
        userRole: 'ADMIN',
        ipAddress: '*************',
        riskLevel: 'HIGH',
        timestamp: new Date().toISOString(),
        reason: 'Large transaction created'
      },
      {
        id: '2',
        tableName: 'accounts',
        recordId: 'acc-001',
        actionType: 'UPDATE',
        userEmail: '<EMAIL>',
        userRole: 'USER',
        ipAddress: '*************',
        riskLevel: 'MEDIUM',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        reason: 'Account balance updated'
      },
      {
        id: '3',
        tableName: 'users',
        recordId: 'user-001',
        actionType: 'DELETE',
        userEmail: '<EMAIL>',
        userRole: 'ADMIN',
        ipAddress: '*************',
        riskLevel: 'CRITICAL',
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        reason: 'User account deleted'
      }
    ];

    setComprehensiveAuditLogs(mockData);
    setPagination(prev => ({ ...prev, total: mockData.length, pages: 1 }));
  };

  const loadMockAuditSummary = () => {
    const mockSummary: AuditSummary = {
      totalLogs: 1247,
      riskDistribution: {
        'LOW': 800,
        'MEDIUM': 300,
        'HIGH': 120,
        'CRITICAL': 27
      },
      actionDistribution: {
        'INSERT': 600,
        'UPDATE': 500,
        'DELETE': 147
      },
      recentHighRiskActivities: 23,
      complianceIssues: 5
    };

    setAuditSummary(mockSummary);
  };

  // New function to fetch comprehensive audit logs (with fallback to mock data)
  const fetchComprehensiveAuditLogs = async () => {
    if (!currentCompany) return;

    setLoading(true);
    setError(null);

    try {
      // Try to fetch from API first
      const queryParams = new URLSearchParams({
        companyId: currentCompany.id,
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.startDate && { startDate: filters.startDate }),
        ...(filters.endDate && { endDate: filters.endDate }),
        ...(filters.riskLevel && { riskLevel: filters.riskLevel }),
        ...(filters.tableName && { tableName: filters.tableName }),
      });

      const response = await fetch(`/api/audit-reports/logs?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setComprehensiveAuditLogs(data.data || []);
        setPagination((prev) => ({
          ...prev,
          total: data.pagination?.total || 0,
          pages: data.pagination?.totalPages || 0,
        }));
      } else {
        // Fallback to mock data
        loadMockComprehensiveData();
      }
    } catch (err) {
      // Fallback to mock data on error
      console.log("API not available, using mock data");
      loadMockComprehensiveData();
    } finally {
      setLoading(false);
    }
  };

  // New function to fetch audit summary (with fallback to mock data)
  const fetchAuditSummary = async () => {
    if (!currentCompany) return;

    setSummaryLoading(true);

    try {
      const queryParams = new URLSearchParams({
        companyId: currentCompany.id,
        ...(filters.startDate && { startDate: filters.startDate }),
        ...(filters.endDate && { endDate: filters.endDate }),
      });

      const response = await fetch(`/api/audit-reports/summary?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAuditSummary(data.data);
      } else {
        // Fallback to mock data
        loadMockAuditSummary();
      }
    } catch (err) {
      // Fallback to mock data on error
      console.log("API not available, using mock audit summary");
      loadMockAuditSummary();
    } finally {
      setSummaryLoading(false);
    }
  };

  const createSampleData = async () => {
    if (!currentCompany) return;

    try {
      setCreatingData(true);
      const response = await fetch(
        `/api/settings/${currentCompany.id}/audit-logs/sample-data`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        showNotification({
          type: "success",
          title: "Success",
          message: "Sample audit data created successfully",
        });
        loadAuditLogs(); // Reload the audit logs
      } else {
        const errorData = await response.json();
        showNotification({
          type: "error",
          title: "Error",
          message: errorData.error || "Failed to create sample data",
        });
      }
    } catch (err) {
      console.error("Failed to create sample data:", err);
      showNotification({
        type: "error",
        title: "Error",
        message: "Failed to create sample data",
      });
    } finally {
      setCreatingData(false);
    }
  };

  const exportAuditLogs = async () => {
    if (!currentCompany) return;

    try {
      // This would typically generate and download a CSV file
      const response = await settingsService.getAuditLogs(currentCompany.id, {
        ...filters,
        limit: 10000, // Get all records for export
      });

      const csvContent = [
        [
          "Date",
          "User",
          "Action",
          "Table",
          "Record ID",
          "Description",
          "IP Address",
        ].join(","),
        ...response.auditLogs.map((log) =>
          [
            new Date(log.createdAt).toLocaleString(),
            log.userEmail,
            log.action,
            log.tableName,
            log.recordId,
            `"${log.description}"`,
            log.ipAddress,
          ].join(",")
        ),
      ].join("\n");

      const blob = new Blob([csvContent], { type: "text/csv" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `audit-logs-${new Date().toISOString().split("T")[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error("Failed to export audit logs:", err);
    }
  };

  const actionTypes = [
    { value: "", label: "All Actions" },
    { value: "CREATE", label: "Create" },
    { value: "UPDATE", label: "Update" },
    { value: "DELETE", label: "Delete" },
    { value: "LOGIN", label: "Login" },
    { value: "LOGOUT", label: "Logout" },
    { value: "APPROVE", label: "Approve" },
    { value: "REJECT", label: "Reject" },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white dark:text-white">
          Audit & Compliance
        </h2>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
          Monitor system activity, compliance checks, and manage data retention policies
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('basic')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
              activeTab === 'basic'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:border-gray-600 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <div className="flex items-center space-x-2">
              <ClockIcon className="h-4 w-4" />
              <span>Basic Audit Logs</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('comprehensive')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
              activeTab === 'comprehensive'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:border-gray-600 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <div className="flex items-center space-x-2">
              <ShieldCheckIcon className="h-4 w-4" />
              <span>Comprehensive Auditing</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('compliance')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
              activeTab === 'compliance'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:border-gray-600 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <div className="flex items-center space-x-2">
              <ChartBarIcon className="h-4 w-4" />
              <span>Tanzania Compliance</span>
            </div>
          </button>
        </nav>
      </div>

      {error && (
        <div className="rounded-md bg-red-50 dark:bg-red-900/20 dark:bg-red-900/20 p-4">
          <div className="text-sm text-red-700 dark:text-red-400 dark:text-red-400">{error}</div>
        </div>
      )}

      {/* Basic Audit Logs Tab */}
      {activeTab === 'basic' && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Audit Logs</h3>
            <div className="flex space-x-2">
              {process.env.NODE_ENV === "development" && (
                <button
                  onClick={createSampleData}
                  disabled={creatingData}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 disabled:opacity-50"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  {creatingData ? "Creating..." : "Create Sample Data"}
                </button>
              )}
              <button
                onClick={exportAuditLogs}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                Export
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Action
              </label>
              <select
                value={filters.action}
                onChange={(e) => handleFilterChange("action", e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                {actionTypes.map((action) => (
                  <option key={action.value} value={action.value}>
                    {action.label}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Start Date
              </label>
              <input
                type="date"
                value={filters.startDate}
                onChange={(e) =>
                  handleFilterChange("startDate", e.target.value)
                }
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                End Date
              </label>
              <input
                type="date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange("endDate", e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>
            <div className="flex items-end">
              <button
                onClick={clearFilters}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Audit Log Table */}
        <div className="overflow-x-auto bg-white dark:bg-gray-800">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <table className="min-w-full bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr className="bg-white dark:bg-gray-800">
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                    Date & Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                    Action
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                    IP Address
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {auditLogs.length > 0 ? (
                  auditLogs.map((log) => (
                    <tr key={log.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {new Date(log.createdAt).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {log.userEmail}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            log.action === "CREATE"
                              ? "bg-green-100 dark:bg-green-900/20 text-green-800"
                              : log.action === "UPDATE"
                              ? "bg-blue-100 dark:bg-blue-900/20 text-blue-800"
                              : log.action === "DELETE"
                              ? "bg-red-100 dark:bg-red-900/20 text-red-800"
                              : "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                          }`}
                        >
                          {log.action}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate">
                        {log.description}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        {log.ipAddress}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr className="bg-white dark:bg-gray-800">
                    <td colSpan={5} className="px-6 py-12 text-center">
                      <div className="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                          No audit logs found
                        </h3>
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          Audit logs will appear here as users perform actions
                          in the system.
                        </p>
                        {process.env.NODE_ENV === "development" && (
                          <div className="mt-4">
                            <button
                              onClick={createSampleData}
                              disabled={creatingData}
                              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 cursor-pointer"
                            >
                              <PlusIcon className="h-4 w-4 mr-2" />
                              {creatingData
                                ? "Creating..."
                                : "Create Sample Data"}
                            </button>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          )}
        </div>

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <div className="text-sm text-gray-700 dark:text-gray-300">
              Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
              {Math.min(pagination.page * pagination.limit, pagination.total)}{" "}
              of {pagination.total} results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() =>
                  setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
                }
                disabled={pagination.page === 1}
                className="px-3 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 text-sm rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() =>
                  setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
                }
                disabled={pagination.page === pagination.pages}
                className="px-3 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 text-sm rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}

          {/* Data Retention Policies */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white dark:text-white">
                Data Retention Policies
              </h3>
            </div>
            <div className="p-6">
              {retentionPolicies.length > 0 ? (
                <div className="space-y-4 bg-white dark:bg-gray-800">
                  {retentionPolicies.map((policy) => (
                    <div
                      key={policy.id}
                      className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 dark:border-gray-700 rounded-lg"
                    >
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white dark:text-white">
                          {policy.tableName}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          {policy.description}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-gray-900 dark:text-white dark:text-white">
                          {policy.retentionDays === 0
                            ? "Forever"
                            : `${policy.retentionDays} days`}
                        </div>
                        <div
                          className={`text-xs ${
                            policy.isActive ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
                          }`}
                        >
                          {policy.isActive ? "Active" : "Inactive"}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  No data retention policies configured.
                </p>
              )}
            </div>
          </div>

          {/* Compliance Information */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <MagnifyingGlassIcon className="h-5 w-5 text-blue-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800 dark:text-blue-300">
                  Basic Audit Trail
                </h3>
                <div className="mt-2 text-sm text-blue-700 dark:text-blue-400">
                  <ul className="list-disc list-inside space-y-1">
                    <li>User actions are automatically logged for audit purposes</li>
                    <li>Audit logs are retained according to your data retention policies</li>
                    <li>Export audit logs regularly for compliance reporting</li>
                    <li>Monitor for unusual activity patterns or unauthorized access</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Comprehensive Auditing Tab */}
      {activeTab === 'comprehensive' && (
        <div className="space-y-6">
          {/* Comprehensive Audit Summary */}
          <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-700 dark:border-gray-700">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ShieldCheckIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Total Audit Logs</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white dark:text-white">
                    {summaryLoading ? '...' : auditSummary?.totalLogs || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-700 dark:border-gray-700">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ExclamationTriangleIcon className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">High Risk Activities</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white dark:text-white">
                    {summaryLoading ? '...' : auditSummary?.riskDistribution?.HIGH || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-700 dark:border-gray-700">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Compliance Issues</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white dark:text-white">
                    {summaryLoading ? '...' : auditSummary?.complianceIssues || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-700 dark:border-gray-700">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Recent Activities</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white dark:text-white">
                    {summaryLoading ? '...' : auditSummary?.recentHighRiskActivities || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Filters */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white dark:text-white mb-4">Advanced Filters</h3>
            <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 dark:text-gray-300 mb-1">
                  Risk Level
                </label>
                <select
                  value={filters.riskLevel}
                  onChange={(e) => setFilters({ ...filters, riskLevel: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">All Risk Levels</option>
                  <option value="LOW">Low</option>
                  <option value="MEDIUM">Medium</option>
                  <option value="HIGH">High</option>
                  <option value="CRITICAL">Critical</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 dark:text-gray-300 mb-1">
                  Table Name
                </label>
                <select
                  value={filters.tableName}
                  onChange={(e) => setFilters({ ...filters, tableName: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">All Tables</option>
                  <option value="transactions">Transactions</option>
                  <option value="accounts">Accounts</option>
                  <option value="users">Users</option>
                  <option value="companies">Companies</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 dark:text-gray-300 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  value={filters.startDate}
                  onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 dark:text-gray-300 mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  value={filters.endDate}
                  onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <div className="mt-4 flex space-x-3">
              <button
                onClick={() => fetchComprehensiveAuditLogs()}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
              >
                Apply Filters
              </button>
              <button
                onClick={clearFilters}
                className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
              >
                Clear Filters
              </button>
            </div>
          </div>

          {/* Comprehensive Audit Logs Table */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white dark:text-white">Comprehensive Audit Logs</h3>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                Detailed audit trail with risk assessment and compliance flags
              </p>
            </div>

            {loading ? (
              <div className="p-6 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Loading comprehensive audit logs...</p>
              </div>
            ) : comprehensiveAuditLogs.length > 0 ? (
              <div className="overflow-x-auto bg-white dark:bg-gray-800">
                <table className="min-w-full bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700 dark:bg-gray-800">
                    <tr className="bg-white dark:bg-gray-800">
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-300 uppercase tracking-wider">
                        Action
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-300 uppercase tracking-wider">
                        Table
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-300 uppercase tracking-wider">
                        User
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-300 uppercase tracking-wider">
                        Risk Level
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-300 uppercase tracking-wider">
                        IP Address
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-300 uppercase tracking-wider">
                        Timestamp
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {comprehensiveAuditLogs.map((log) => (
                      <tr key={log.id} className="hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              log.actionType === 'INSERT' ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                              log.actionType === 'UPDATE' ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                              'bg-red-100 dark:bg-red-900/20 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                            }`}>
                              {log.actionType}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white dark:text-gray-300">
                          {log.tableName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white dark:text-gray-300">{log.userEmail}</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{log.userRole}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            log.riskLevel === 'CRITICAL' ? 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                            log.riskLevel === 'HIGH' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400' :
                            log.riskLevel === 'MEDIUM' ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                            'bg-green-100 dark:bg-green-900/20 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          }`}>
                            {log.riskLevel}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          {log.ipAddress}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          {new Date(log.timestamp).toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="p-6 text-center">
                <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">No comprehensive audit logs found.</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Tanzania Compliance Tab */}
      {activeTab === 'compliance' && (
        <div className="space-y-6">
          {/* Compliance Overview */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white dark:text-white mb-4">Tanzania Regulatory Compliance</h3>

            <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-3 gap-6">
              {/* TRA Compliance */}
              <div className="border border-gray-200 dark:border-gray-700 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <div className="flex-shrink-0">
                    <ShieldCheckIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h4 className="ml-2 text-sm font-medium text-gray-900 dark:text-white dark:text-white">TRA Compliance</h4>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">VAT Threshold:</span>
                    <span className="font-medium text-gray-900 dark:text-white dark:text-white">100M TZS</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Withholding Tax:</span>
                    <span className="font-medium text-gray-900 dark:text-white dark:text-white">30K TZS</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">EFD Required:</span>
                    <span className="font-medium text-gray-900 dark:text-white dark:text-white">5K TZS</span>
                  </div>
                </div>
              </div>

              {/* BOT Compliance */}
              <div className="border border-gray-200 dark:border-gray-700 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <div className="flex-shrink-0">
                    <ChartBarIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <h4 className="ml-2 text-sm font-medium text-gray-900 dark:text-white dark:text-white">BOT Compliance</h4>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Forex Reporting:</span>
                    <span className="font-medium text-gray-900 dark:text-white dark:text-white">$10K USD</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Large Cash:</span>
                    <span className="font-medium text-gray-900 dark:text-white dark:text-white">5M TZS</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Cross-border:</span>
                    <span className="font-medium text-gray-900 dark:text-white dark:text-white">$10K USD</span>
                  </div>
                </div>
              </div>

              {/* AML Compliance */}
              <div className="border border-gray-200 dark:border-gray-700 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <div className="flex-shrink-0">
                    <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <h4 className="ml-2 text-sm font-medium text-gray-900 dark:text-white dark:text-white">AML Compliance</h4>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Suspicious Cash:</span>
                    <span className="font-medium text-gray-900 dark:text-white dark:text-white">10M TZS</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Daily Frequency:</span>
                    <span className="font-medium text-gray-900 dark:text-white dark:text-white">5 transactions</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">PEP Screening:</span>
                    <span className="font-medium text-green-600 dark:text-green-400">Active</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Compliance Actions */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white dark:text-white mb-4">Compliance Actions</h3>

            <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4">
              <button className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-20050855">
                <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
                Generate TRA Monthly Report
              </button>

              <button className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-20051544">
                <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
                Generate BOT Quarterly Report
              </button>

              <button className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-20052235">
                <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
                Suspicious Activity Report
              </button>

              <button className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-20052925">
                <ShieldCheckIcon className="h-5 w-5 mr-2" />
                Run Compliance Check
              </button>
            </div>
          </div>

          {/* Compliance Information */}
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <ShieldCheckIcon className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800 dark:text-green-300">
                  Tanzania Regulatory Compliance
                </h3>
                <div className="mt-2 text-sm text-green-700 dark:text-green-400">
                  <ul className="list-disc list-inside space-y-1">
                    <li>Automatic TRA compliance checks for VAT, withholding tax, and EFD requirements</li>
                    <li>Bank of Tanzania (BOT) monitoring for forex and large cash transactions</li>
                    <li>Anti-Money Laundering (AML) screening with PEP and sanctions list checking</li>
                    <li>Automated compliance reporting for regulatory authorities</li>
                    <li>Real-time risk assessment and suspicious activity detection</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
