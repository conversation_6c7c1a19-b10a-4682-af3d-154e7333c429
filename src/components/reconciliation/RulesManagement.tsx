import React, { useState, useEffect } from 'react';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CogIcon,
  CheckCircleIcon,
  XMarkIcon,
  ExclamationTriangleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';

interface ReconciliationRule {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  priority: number;
  conditions: {
    amountRange?: { min?: number; max?: number };
    descriptionContains?: string[];
    descriptionRegex?: string;
    dateRange?: { days: number };
    accountTypes?: string[];
    transactionTypes?: string[];
  };
  actions: {
    autoMatch: boolean;
    category?: string;
    tags?: string[];
    notes?: string;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export default function RulesManagement() {
  const { currentCompany } = useCompany();
  const [rules, setRules] = useState<ReconciliationRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingRule, setEditingRule] = useState<ReconciliationRule | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isActive: true,
    priority: 0,
    conditions: {
      amountRange: { min: undefined, max: undefined },
      descriptionContains: [''],
      descriptionRegex: '',
      dateRange: { days: 3 },
      accountTypes: [],
      transactionTypes: []
    },
    actions: {
      autoMatch: true,
      category: '',
      tags: [''],
      notes: ''
    }
  });

  useEffect(() => {
    if (currentCompany) {
      fetchRules();
    }
  }, [currentCompany]);

  const fetchRules = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch(
        `/api/reconciliation/${currentCompany?.id}/rules`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setRules(data || []);
    } catch (err: any) {
      console.error('Error fetching rules:', err);
      setError(err.message || 'Failed to load rules');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRule = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(
        `/api/reconciliation/${currentCompany?.id}/rules`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...formData,
            conditions: {
              ...formData.conditions,
              descriptionContains: formData.conditions.descriptionContains.filter(s => s.trim()),
              amountRange: formData.conditions.amountRange.min || formData.conditions.amountRange.max ? formData.conditions.amountRange : undefined
            },
            actions: {
              ...formData.actions,
              tags: formData.actions.tags.filter(s => s.trim())
            }
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      setShowCreateModal(false);
      resetForm();
      await fetchRules();
    } catch (err: any) {
      console.error('Error creating rule:', err);
      setError(err.message || 'Failed to create rule');
    }
  };

  const handleUpdateRule = async () => {
    if (!editingRule) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(
        `/api/reconciliation/${currentCompany?.id}/rules/${editingRule.id}`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...formData,
            conditions: {
              ...formData.conditions,
              descriptionContains: formData.conditions.descriptionContains.filter(s => s.trim()),
              amountRange: formData.conditions.amountRange.min || formData.conditions.amountRange.max ? formData.conditions.amountRange : undefined
            },
            actions: {
              ...formData.actions,
              tags: formData.actions.tags.filter(s => s.trim())
            }
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      setEditingRule(null);
      resetForm();
      await fetchRules();
    } catch (err: any) {
      console.error('Error updating rule:', err);
      setError(err.message || 'Failed to update rule');
    }
  };

  const handleDeleteRule = async (ruleId: string) => {
    if (!confirm('Are you sure you want to delete this rule?')) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(
        `/api/reconciliation/${currentCompany?.id}/rules/${ruleId}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      await fetchRules();
    } catch (err: any) {
      console.error('Error deleting rule:', err);
      setError(err.message || 'Failed to delete rule');
    }
  };

  const handleToggleRule = async (ruleId: string, isActive: boolean) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(
        `/api/reconciliation/${currentCompany?.id}/rules/${ruleId}`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ isActive }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      await fetchRules();
    } catch (err: any) {
      console.error('Error toggling rule:', err);
      setError(err.message || 'Failed to toggle rule');
    }
  };

  const handleChangePriority = async (ruleId: string, newPriority: number) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(
        `/api/reconciliation/${currentCompany?.id}/rules/${ruleId}`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ priority: newPriority }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      await fetchRules();
    } catch (err: any) {
      console.error('Error updating priority:', err);
      setError(err.message || 'Failed to update priority');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      isActive: true,
      priority: 0,
      conditions: {
        amountRange: { min: undefined, max: undefined },
        descriptionContains: [''],
        descriptionRegex: '',
        dateRange: { days: 3 },
        accountTypes: [],
        transactionTypes: []
      },
      actions: {
        autoMatch: true,
        category: '',
        tags: [''],
        notes: ''
      }
    });
  };

  const openEditModal = (rule: ReconciliationRule) => {
    setEditingRule(rule);
    setFormData({
      name: rule.name,
      description: rule.description,
      isActive: rule.isActive,
      priority: rule.priority,
      conditions: {
        amountRange: rule.conditions.amountRange || { min: undefined, max: undefined },
        descriptionContains: rule.conditions.descriptionContains || [''],
        descriptionRegex: rule.conditions.descriptionRegex || '',
        dateRange: rule.conditions.dateRange || { days: 3 },
        accountTypes: rule.conditions.accountTypes || [],
        transactionTypes: rule.conditions.transactionTypes || []
      },
      actions: {
        autoMatch: rule.actions.autoMatch,
        category: rule.actions.category || '',
        tags: rule.actions.tags || [''],
        notes: rule.actions.notes || ''
      }
    });
  };

  const addDescriptionKeyword = () => {
    setFormData({
      ...formData,
      conditions: {
        ...formData.conditions,
        descriptionContains: [...formData.conditions.descriptionContains, '']
      }
    });
  };

  const removeDescriptionKeyword = (index: number) => {
    const newKeywords = formData.conditions.descriptionContains.filter((_, i) => i !== index);
    setFormData({
      ...formData,
      conditions: {
        ...formData.conditions,
        descriptionContains: newKeywords
      }
    });
  };

  const addTag = () => {
    setFormData({
      ...formData,
      actions: {
        ...formData.actions,
        tags: [...formData.actions.tags, '']
      }
    });
  };

  const removeTag = (index: number) => {
    const newTags = formData.actions.tags.filter((_, i) => i !== index);
    setFormData({
      ...formData,
      actions: {
        ...formData.actions,
        tags: newTags
      }
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Reconciliation Rules</h2>
          <p className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Manage automated matching rules for bank reconciliation</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 cursor-pointer"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Create Rule
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Rules List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Active Rules ({rules.filter(r => r.isActive).length})</h3>
        </div>

        {rules.length === 0 ? (
          <div className="text-center bg-white dark:bg-gray-800 py-12">
            <CogIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No rules configured</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Get started by creating your first reconciliation rule.</p>
            <div className="mt-6">
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 cursor-pointer"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create Rule
              </button>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {rules.sort((a, b) => b.priority - a.priority).map((rule) => (
              <div key={rule.id} className={`p-6 ${!rule.isActive ? 'bg-gray-50 dark:bg-gray-700' : ''}`}>
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h4 className="text-lg font-medium text-gray-900 dark:text-white">{rule.name}</h4>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        rule.isActive ? 'bg-green-100 dark:bg-green-900/20 text-green-800' : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                      }`}>
                        {rule.isActive ? 'Active' : 'Inactive'}
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800">
                        Priority: {rule.priority}
                      </span>
                    </div>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{rule.description}</p>
                    
                    {/* Rule Details */}
                    <div className="mt-3 grid bg-white dark:bg-gray-800 grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Conditions:</span>
                        <ul className="mt-1 text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          {rule.conditions.amountRange && (
                            <li>Amount: ${rule.conditions.amountRange.min || 0} - ${rule.conditions.amountRange.max || '∞'}</li>
                          )}
                          {rule.conditions.descriptionContains && rule.conditions.descriptionContains.length > 0 && (
                            <li>Description contains: {rule.conditions.descriptionContains.join(', ')}</li>
                          )}
                          {rule.conditions.dateRange && (
                            <li>Date range: ±{rule.conditions.dateRange.days} days</li>
                          )}
                        </ul>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Actions:</span>
                        <ul className="mt-1 text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <li>Auto-match: {rule.actions.autoMatch ? 'Yes' : 'No'}</li>
                          {rule.actions.category && <li>Category: {rule.actions.category}</li>}
                          {rule.actions.tags && rule.actions.tags.length > 0 && (
                            <li>Tags: {rule.actions.tags.join(', ')}</li>
                          )}
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* Priority Controls */}
                    <div className="flex flex-col bg-white dark:bg-gray-800 space-y-1">
                      <button
                        onClick={() => handleChangePriority(rule.id, rule.priority + 1)}
                        className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-pointer"
                        title="Increase priority"
                      >
                        <ArrowUpIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleChangePriority(rule.id, Math.max(0, rule.priority - 1))}
                        className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-pointer"
                        title="Decrease priority"
                      >
                        <ArrowDownIcon className="h-4 w-4" />
                      </button>
                    </div>

                    {/* Toggle Active */}
                    <button
                      onClick={() => handleToggleRule(rule.id, !rule.isActive)}
                      className={`p-2 rounded-md cursor-pointer ${
                        rule.isActive 
                          ? 'text-green-600 dark:text-green-400 hover:bg-green-50 dark:bg-green-900/20' 
                          : 'text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700'
                      }`}
                      title={rule.isActive ? 'Deactivate rule' : 'Activate rule'}
                    >
                      {rule.isActive ? <EyeIcon className="h-4 w-4" /> : <EyeSlashIcon className="h-4 w-4" />}
                    </button>

                    {/* Edit */}
                    <button
                      onClick={() => openEditModal(rule)}
                      className="p-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:bg-blue-900/20 rounded-md cursor-pointer"
                      title="Edit rule"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>

                    {/* Delete */}
                    <button
                      onClick={() => handleDeleteRule(rule.id)}
                      className="p-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:bg-red-900/20 rounded-md cursor-pointer"
                      title="Delete rule"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
