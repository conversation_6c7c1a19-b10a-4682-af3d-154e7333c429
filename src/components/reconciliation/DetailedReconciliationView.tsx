import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  CheckCircleIcon,
  XMarkIcon,
  ArrowLeftIcon,
  CogIcon,
  BoltIcon,
  DocumentCheckIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  UserIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';

interface ReconciliationMatch {
  bankTransactionId: string;
  transactionId: string;
  matchType: 'EXACT' | 'FUZZY' | 'MANUAL' | 'RULE_BASED';
  confidence: number;
  matchingFactors: {
    amount: boolean;
    date: boolean;
    description: boolean;
    reference: boolean;
  };
  suggestedAction: 'AUTO_MATCH' | 'REVIEW' | 'MANUAL_REVIEW';
  bankTransaction?: any;
  transaction?: any;
}

interface ReconciliationItem {
  id: string;
  type: 'TRANSACTION' | 'BANK_TRANSACTION' | 'ADJUSTMENT';
  description: string;
  amount: number;
  transactionDate: string;
  checkNumber?: string;
  reference?: string;
  isReconciled: boolean;
  matchConfidence?: number;
  matchedTransactionId?: string;
}

interface ReconciliationSummary {
  totalTransactions: number;
  reconciledTransactions: number;
  unreconciledTransactions: number;
  totalAmount: number;
  reconciledAmount: number;
  unreconciledAmount: number;
  outstandingChecks: number;
  outstandingDeposits: number;
  adjustments: number;
}

interface BankReconciliation {
  id: string;
  bankAccountId: string;
  statementDate: string;
  statementBeginningBalance: number;
  statementEndingBalance: number;
  reconciledBalance: number;
  difference: number;
  status: 'IN_PROGRESS' | 'COMPLETED' | 'REVIEWED' | 'LOCKED';
  reconciledBy: string;
  notes?: string;
}

export default function DetailedReconciliationView() {
  const { reconciliationId } = useParams<{ reconciliationId: string }>();
  const { currentCompany } = useCompany();
  const navigate = useNavigate();
  
  const [reconciliation, setReconciliation] = useState<BankReconciliation | null>(null);
  const [items, setItems] = useState<ReconciliationItem[]>([]);
  const [suggestions, setSuggestions] = useState<ReconciliationMatch[]>([]);
  const [summary, setSummary] = useState<ReconciliationSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [autoMatchThreshold, setAutoMatchThreshold] = useState(0.9);
  const [filterStatus, setFilterStatus] = useState<'all' | 'reconciled' | 'unreconciled'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (reconciliationId && currentCompany) {
      fetchReconciliationData();
    }
  }, [reconciliationId, currentCompany]);

  const fetchReconciliationData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch(
        `/api/reconciliation/${currentCompany?.id}/reconciliations/${reconciliationId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setReconciliation(data.reconciliation);
      setItems(data.items || []);
      setSuggestions(data.suggestions || []);
      setSummary(data.summary);
    } catch (err: any) {
      console.error('Error fetching reconciliation data:', err);
      setError(err.message || 'Failed to load reconciliation data');
    } finally {
      setLoading(false);
    }
  };

  const handleAutoMatch = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(
        `/api/reconciliation/${currentCompany?.id}/reconciliations/${reconciliationId}/auto-match`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ confidenceThreshold: autoMatchThreshold }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const results = await response.json();
      console.log('Auto-match results:', results);
      
      // Refresh data
      await fetchReconciliationData();
    } catch (err: any) {
      console.error('Error performing auto-match:', err);
      setError(err.message || 'Failed to perform auto-match');
    }
  };

  const handleMatchTransactions = async (bankTransactionId: string, transactionId: string, matchType: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(
        `/api/reconciliation/${currentCompany?.id}/reconciliations/${reconciliationId}/match`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ bankTransactionId, transactionId, matchType }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Refresh data
      await fetchReconciliationData();
    } catch (err: any) {
      console.error('Error matching transactions:', err);
      setError(err.message || 'Failed to match transactions');
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedItems.size === 0) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(
        `/api/reconciliation/${currentCompany?.id}/reconciliations/${reconciliationId}/bulk-action`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action,
            itemIds: Array.from(selectedItems),
            notes: `Bulk ${action.toLowerCase()} operation`,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Clear selection and refresh data
      setSelectedItems(new Set());
      await fetchReconciliationData();
    } catch (err: any) {
      console.error('Error performing bulk action:', err);
      setError(err.message || 'Failed to perform bulk action');
    }
  };

  const updateReconciliationStatus = async (newStatus: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(
        `/api/reconciliation/${currentCompany?.id}/reconciliations/${reconciliationId}/status`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            status: newStatus,
            notes: `Status updated to ${newStatus}`,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Refresh data
      await fetchReconciliationData();
    } catch (err: any) {
      console.error('Error updating status:', err);
      setError(err.message || 'Failed to update status');
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20';
    if (confidence >= 0.7) return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20';
    return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20';
  };

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 0.9) return 'High';
    if (confidence >= 0.7) return 'Medium';
    return 'Low';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const filteredItems = items.filter(item => {
    const matchesFilter = filterStatus === 'all' || 
      (filterStatus === 'reconciled' && item.isReconciled) ||
      (filterStatus === 'unreconciled' && !item.isReconciled);
    
    const matchesSearch = searchTerm === '' || 
      item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.reference?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.checkNumber?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 rounded-md p-4">
        <div className="flex">
          <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
          <div className="ml-3">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!reconciliation) {
    return (
      <div className="text-center bg-white dark:bg-gray-800 py-12">
        <p className="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Reconciliation not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/reconciliation')}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700 cursor-pointer"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Reconciliations
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Bank Reconciliation - {formatDate(reconciliation.statementDate)}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
              Statement Balance: {formatCurrency(reconciliation.statementEndingBalance)} | 
              Difference: <span className={reconciliation.difference === 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                {formatCurrency(reconciliation.difference)}
              </span>
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Status Badge */}
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
            reconciliation.status === 'IN_PROGRESS' ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800' :
            reconciliation.status === 'COMPLETED' ? 'bg-green-100 dark:bg-green-900/20 text-green-800' :
            reconciliation.status === 'REVIEWED' ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800' :
            'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
          }`}>
            {reconciliation.status.replace('_', ' ')}
          </span>

          {/* Status Actions */}
          {reconciliation.status === 'IN_PROGRESS' && (
            <button
              onClick={() => updateReconciliationStatus('COMPLETED')}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 cursor-pointer"
            >
              <DocumentCheckIcon className="h-4 w-4 mr-2" />
              Mark Complete
            </button>
          )}
          
          {reconciliation.status === 'COMPLETED' && (
            <button
              onClick={() => updateReconciliationStatus('REVIEWED')}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 cursor-pointer"
            >
              <UserIcon className="h-4 w-4 mr-2" />
              Mark Reviewed
            </button>
          )}
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Total Transactions</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">{summary.totalTransactions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Reconciled</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">{summary.reconciledTransactions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Unreconciled</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">{summary.unreconciledTransactions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-8 w-8 text-red-600 dark:text-red-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Difference</p>
                <p className={`text-2xl font-semibold ${reconciliation.difference === 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                  {formatCurrency(reconciliation.difference)}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* AI Suggestions Panel */}
      {suggestions.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <BoltIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">AI Matching Suggestions</h3>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800">
                  {suggestions.length} suggestions
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-700 dark:text-gray-300">Auto-match threshold:</label>
                  <select
                    value={autoMatchThreshold}
                    onChange={(e) => setAutoMatchThreshold(parseFloat(e.target.value))}
                    className="text-sm border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded px-2 py-1 cursor-pointer"
                  >
                    <option value={0.95}>95%</option>
                    <option value={0.9}>90%</option>
                    <option value={0.8}>80%</option>
                    <option value={0.7}>70%</option>
                  </select>
                </div>
                <button
                  onClick={handleAutoMatch}
                  className="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 cursor-pointer"
                >
                  <BoltIcon className="h-4 w-4 mr-2" />
                  Auto-Match
                </button>
                <button
                  onClick={() => setShowSuggestions(!showSuggestions)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700 cursor-pointer"
                >
                  {showSuggestions ? <ChevronUpIcon className="h-4 w-4" /> : <ChevronDownIcon className="h-4 w-4" />}
                </button>
              </div>
            </div>
          </div>

          {showSuggestions && (
            <div className="p-6">
              <div className="space-y-4 bg-white dark:bg-gray-800">
                {suggestions.slice(0, 10).map((suggestion, index) => (
                  <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getConfidenceColor(suggestion.confidence)}`}>
                          {getConfidenceLabel(suggestion.confidence)} ({Math.round(suggestion.confidence * 100)}%)
                        </span>
                        <span className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{suggestion.matchType} Match</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleMatchTransactions(suggestion.bankTransactionId, suggestion.transactionId, 'MANUAL')}
                          className="inline-flex items-center px-3 py-1 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 cursor-pointer"
                        >
                          <CheckCircleIcon className="h-4 w-4 mr-1" />
                          Accept
                        </button>
                        <button className="inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700 cursor-pointer">
                          <XMarkIcon className="h-4 w-4 mr-1" />
                          Reject
                        </button>
                      </div>
                    </div>

                    <div className="grid bg-white dark:bg-gray-800 grid-cols-2 gap-4">
                      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                        <h4 className="text-sm font-medium text-blue-900 mb-2">Bank Transaction</h4>
                        <p className="text-sm text-blue-800">{suggestion.bankTransaction?.description || 'Bank transaction'}</p>
                        <p className="text-sm text-blue-600 dark:text-blue-400">
                          {formatCurrency(suggestion.bankTransaction?.amount || 0)} • {formatDate(suggestion.bankTransaction?.date || '')}
                        </p>
                      </div>
                      <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
                        <h4 className="text-sm font-medium text-green-900 mb-2">Accounting Transaction</h4>
                        <p className="text-sm text-green-800">{suggestion.transaction?.description || 'Accounting transaction'}</p>
                        <p className="text-sm text-green-600 dark:text-green-400">
                          {formatCurrency(suggestion.transaction?.amount || 0)} • {formatDate(suggestion.transaction?.date || '')}
                        </p>
                      </div>
                    </div>

                    <div className="mt-3 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      <span className={suggestion.matchingFactors.amount ? 'text-green-600 dark:text-green-400' : 'text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500'}>
                        Amount {suggestion.matchingFactors.amount ? '✓' : '✗'}
                      </span>
                      <span className={suggestion.matchingFactors.date ? 'text-green-600 dark:text-green-400' : 'text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500'}>
                        Date {suggestion.matchingFactors.date ? '✓' : '✗'}
                      </span>
                      <span className={suggestion.matchingFactors.description ? 'text-green-600 dark:text-green-400' : 'text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500'}>
                        Description {suggestion.matchingFactors.description ? '✓' : '✗'}
                      </span>
                      <span className={suggestion.matchingFactors.reference ? 'text-green-600 dark:text-green-400' : 'text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500'}>
                        Reference {suggestion.matchingFactors.reference ? '✓' : '✗'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Transactions List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Transactions</h3>

            {/* Bulk Actions */}
            {selectedItems.size > 0 && (
              <div className="flex items-center space-x-3">
                <span className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{selectedItems.size} selected</span>
                <button
                  onClick={() => handleBulkAction('RECONCILE')}
                  className="inline-flex items-center px-3 py-1 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 cursor-pointer"
                >
                  Bulk Reconcile
                </button>
                <button
                  onClick={() => handleBulkAction('UNRECONCILE')}
                  className="inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700 cursor-pointer"
                >
                  Bulk Unreconcile
                </button>
                <button
                  onClick={() => setSelectedItems(new Set())}
                  className="inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700 cursor-pointer"
                >
                  Clear Selection
                </button>
              </div>
            )}
          </div>

          {/* Filters and Search */}
          <div className="mt-4 flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <MagnifyingGlassIcon className="h-4 w-4 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                placeholder="Search transactions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
            >
              <option value="all">All Transactions</option>
              <option value="reconciled">Reconciled</option>
              <option value="unreconciled">Unreconciled</option>
            </select>
            <button
              onClick={() => {
                const allItemIds = filteredItems.map(item => item.id);
                setSelectedItems(new Set(allItemIds));
              }}
              className="inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700 cursor-pointer"
            >
              Select All
            </button>
          </div>
        </div>

        <div className="overflow-x-auto bg-white dark:bg-gray-800">
          <table className="min-w-full bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr className="bg-white dark:bg-gray-800">
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    checked={selectedItems.size === filteredItems.length && filteredItems.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedItems(new Set(filteredItems.map(item => item.id)));
                      } else {
                        setSelectedItems(new Set());
                      }
                    }}
                    className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded cursor-pointer"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                  Reference
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                  Confidence
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 dark:divide-gray-700">
              {filteredItems.map((item) => (
                <tr key={item.id} className={`hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700 ${selectedItems.has(item.id) ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedItems.has(item.id)}
                      onChange={(e) => {
                        const newSelected = new Set(selectedItems);
                        if (e.target.checked) {
                          newSelected.add(item.id);
                        } else {
                          newSelected.delete(item.id);
                        }
                        setSelectedItems(newSelected);
                      }}
                      className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded cursor-pointer"
                    />
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">{item.description}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{item.type}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`text-sm font-medium ${item.amount >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                      {formatCurrency(item.amount)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {formatDate(item.transactionDate)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {item.reference || item.checkNumber || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      item.isReconciled ? 'bg-green-100 dark:bg-green-900/20 text-green-800' : 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800'
                    }`}>
                      {item.isReconciled ? 'Reconciled' : 'Unreconciled'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {item.matchConfidence ? (
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getConfidenceColor(item.matchConfidence)}`}>
                        {Math.round(item.matchConfidence * 100)}%
                      </span>
                    ) : (
                      <span className="text-sm text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">-</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {!item.isReconciled ? (
                      <button
                        onClick={() => handleBulkAction('RECONCILE')}
                        className="text-green-600 dark:text-green-400 hover:text-green-900 cursor-pointer"
                      >
                        Reconcile
                      </button>
                    ) : (
                      <button
                        onClick={() => handleBulkAction('UNRECONCILE')}
                        className="text-red-600 dark:text-red-400 hover:text-red-900 cursor-pointer"
                      >
                        Unreconcile
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
