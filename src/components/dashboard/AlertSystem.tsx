// import React from "react";
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";

export interface Alert {
  id: string;
  type: "warning" | "error" | "info" | "success";
  title: string;
  message: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  dismissible?: boolean;
}

interface AlertSystemProps {
  alerts: Alert[];
  onDismiss?: (alertId: string) => void;
}

export default function AlertSystem({ alerts, onDismiss }: AlertSystemProps) {
  if (!alerts || alerts.length === 0) {
    return null;
  }

  const getAlertIcon = (type: Alert["type"]) => {
    switch (type) {
      case "warning":
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />;
      case "error":
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />;
      case "success":
        return <CheckCircleIcon className="h-5 w-5 text-green-400" />;
      case "info":
      default:
        return <InformationCircleIcon className="h-5 w-5 text-blue-400" />;
    }
  };

  const getAlertStyles = (type: Alert["type"]) => {
    switch (type) {
      case "warning":
        return "bg-yellow-50 dark:bg-yellow-900/20 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800";
      case "error":
        return "bg-red-50 dark:bg-red-900/20 dark:bg-red-900/20 border-red-200 dark:border-red-800";
      case "success":
        return "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800";
      case "info":
      default:
        return "bg-blue-50 dark:bg-blue-900/20 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800";
    }
  };

  const getTextStyles = (type: Alert["type"]) => {
    switch (type) {
      case "warning":
        return "text-yellow-800 dark:text-yellow-200";
      case "error":
        return "text-red-800 dark:text-red-200";
      case "success":
        return "text-green-800 dark:text-green-200";
      case "info":
      default:
        return "text-blue-800 dark:text-blue-200";
    }
  };

  const getButtonStyles = (type: Alert["type"]) => {
    switch (type) {
      case "warning":
        return "bg-yellow-100 dark:bg-yellow-800/30 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800/50";
      case "error":
        return "bg-red-100 dark:bg-red-800/30 text-red-800 dark:text-red-200 hover:bg-red-200 dark:hover:bg-red-800/50";
      case "success":
        return "bg-green-100 dark:bg-green-900/20 dark:bg-green-800/30 text-green-800 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-800/50";
      case "info":
      default:
        return "bg-blue-100 dark:bg-blue-800/30 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800/50";
    }
  };

  return (
    <div className="space-y-3">
      {alerts.map((alert) => (
        <div
          key={alert.id}
          className={`rounded-lg border p-4 ${getAlertStyles(alert.type)}`}
        >
          <div className="flex">
            <div className="flex-shrink-0">{getAlertIcon(alert.type)}</div>
            <div className="ml-3 flex-1">
              <h3
                className={`text-sm font-medium ${getTextStyles(alert.type)}`}
              >
                {alert.title}
              </h3>
              <div className={`mt-1 text-sm ${getTextStyles(alert.type)}`}>
                {alert.message}
              </div>
              {alert.action && (
                <div className="mt-3">
                  <button
                    onClick={alert.action.onClick}
                    className={`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md ${getButtonStyles(
                      alert.type
                    )} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 cursor-pointer`}
                  >
                    {alert.action.label}
                  </button>
                </div>
              )}
            </div>
            {alert.dismissible && onDismiss && (
              <div className="ml-auto pl-3">
                <div className="-mx-1.5 -my-1.5">
                  <button
                    onClick={() => onDismiss(alert.id)}
                    className={`inline-flex rounded-md p-1.5 ${getTextStyles(
                      alert.type
                    )} hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer`}
                  >
                    <span className="sr-only">Dismiss</span>
                    <svg
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}

// Helper function to generate alerts based on dashboard stats
export function generateAlerts(stats: any, companySettings?: any): Alert[] {
  const alerts: Alert[] = [];

  // Low cash balance alert
  const minCashThreshold = companySettings?.minCashBalance || 10000;
  if (stats.cashBalance < minCashThreshold) {
    alerts.push({
      id: "low-cash",
      type: "warning",
      title: "Low Cash Balance",
      message: `Your cash balance is below the recommended threshold of ${new Intl.NumberFormat(
        "en-US",
        { style: "currency", currency: "USD" }
      ).format(minCashThreshold)}.`,
      action: {
        label: "View Cash Flow",
        onClick: () => {
          // Navigate to cash flow report
          window.location.href = "/reports/cash-flow";
        },
      },
      dismissible: true,
    });
  }

  // Overdue invoices alert
  if (stats.overdueInvoices > 0) {
    alerts.push({
      id: "overdue-invoices",
      type: "error",
      title: "Overdue Invoices",
      message: `You have ${stats.overdueInvoices} overdue invoice${
        stats.overdueInvoices > 1 ? "s" : ""
      } that need attention.`,
      action: {
        label: "View Overdue Invoices",
        onClick: () => {
          // Navigate to overdue invoices
          window.location.href = "/invoices?status=overdue";
        },
      },
      dismissible: true,
    });
  }

  // High burn rate alert
  const maxBurnRateThreshold = companySettings?.maxBurnRate || 50000;
  if (stats.burnRate > maxBurnRateThreshold) {
    alerts.push({
      id: "high-burn-rate",
      type: "warning",
      title: "High Burn Rate",
      message: `Your monthly burn rate of ${new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(stats.burnRate)} is above the threshold.`,
      action: {
        label: "Review Expenses",
        onClick: () => {
          // Navigate to expense analysis
          window.location.href = "/reports/expenses";
        },
      },
      dismissible: true,
    });
  }

  // Negative cash flow alert
  if (stats.operatingCashFlow < 0) {
    alerts.push({
      id: "negative-cash-flow",
      type: "error",
      title: "Negative Operating Cash Flow",
      message:
        "Your operating cash flow is negative, indicating potential liquidity issues.",
      action: {
        label: "Analyze Cash Flow",
        onClick: () => {
          // Navigate to cash flow analysis
          window.location.href = "/reports/cash-flow";
        },
      },
      dismissible: true,
    });
  }

  // Long collection period alert
  if (stats.avgCollectionPeriod > 45) {
    alerts.push({
      id: "long-collection-period",
      type: "warning",
      title: "Long Collection Period",
      message: `Your average collection period is ${Math.round(
        stats.avgCollectionPeriod
      )} days. Consider improving collection processes.`,
      action: {
        label: "Review A/R Aging",
        onClick: () => {
          // Navigate to A/R aging report
          window.location.href = "/reports/ar-aging";
        },
      },
      dismissible: true,
    });
  }

  // Positive performance alert
  if (stats.revenueGrowth > 10) {
    alerts.push({
      id: "revenue-growth",
      type: "success",
      title: "Strong Revenue Growth",
      message: `Revenue has grown by ${stats.revenueGrowth.toFixed(
        1
      )}% compared to the previous period.`,
      dismissible: true,
    });
  }

  return alerts;
}
