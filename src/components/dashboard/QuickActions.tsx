import React from "react";
import { useNavigate } from "react-router-dom";
import {
  PlusIcon,
  DocumentTextIcon,
  CreditCardIcon,
  ReceiptPercentIcon,
  ChartBarIcon,
  UserPlusIcon,
} from "@heroicons/react/24/outline";

interface QuickAction {
  id: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  onClick: () => void;
}

interface QuickActionsProps {
  onCreateInvoice?: () => void;
  onRecordPayment?: () => void;
  onAddExpense?: () => void;
  onGenerateReport?: () => void;
  onAddContact?: () => void;
  onCreateTransaction?: () => void;
}

export default function QuickActions({
  onCreateInvoice,
  onRecordPayment,
  onAddExpense,
  onGenerateReport,
  onAddContact,
  onCreateTransaction,
}: QuickActionsProps) {
  const navigate = useNavigate();

  const actions: QuickAction[] = [
    {
      id: "create-invoice",
      label: "Create Invoice",
      description: "Generate a new sales or purchase invoice",
      icon: <DocumentTextIcon className="h-6 w-6" />,
      color: "bg-blue-500 hover:bg-blue-600",
      onClick: onCreateInvoice || (() => navigate("/invoices")),
    },
    {
      id: "record-payment",
      label: "Record Payment",
      description: "Log a payment received or made",
      icon: <CreditCardIcon className="h-6 w-6" />,
      color: "bg-green-500 hover:bg-green-600",
      onClick: onRecordPayment || (() => navigate("/transactions")),
    },
    {
      id: "add-expense",
      label: "Add Expense",
      description: "Record a business expense",
      icon: <ReceiptPercentIcon className="h-6 w-6" />,
      color: "bg-red-500 hover:bg-red-600",
      onClick: onAddExpense || (() => navigate("/transactions")),
    },
    {
      id: "create-transaction",
      label: "New Transaction",
      description: "Create a general journal entry",
      icon: <PlusIcon className="h-6 w-6" />,
      color: "bg-purple-500 hover:bg-purple-600",
      onClick: onCreateTransaction || (() => navigate("/transactions")),
    },
    {
      id: "add-contact",
      label: "Add Contact",
      description: "Add a new customer or vendor",
      icon: <UserPlusIcon className="h-6 w-6" />,
      color: "bg-yellow-500 hover:bg-yellow-600",
      onClick: onAddContact || (() => navigate("/contacts")),
    },
    {
      id: "generate-report",
      label: "Generate Report",
      description: "Create financial reports",
      icon: <ChartBarIcon className="h-6 w-6" />,
      color: "bg-indigo-500 hover:bg-indigo-600",
      onClick: onGenerateReport || (() => navigate("/reports")),
    },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white dark:text-white">Quick Actions</h3>
        <div className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Streamline your workflow</div>
      </div>

      <div className="grid bg-white dark:bg-gray-800 grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
        {actions.map((action) => (
          <button
            key={action.id}
            onClick={action.onClick}
            className="group relative flex flex-col items-center p-4 rounded-xl border border-gray-200 dark:border-gray-700 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 bg-gradient-to-br from-white to-gray-50 dark:from-gray-700 dark:to-gray-800 hover:from-blue-50 hover:to-white dark:hover:from-gray-600 dark:hover:to-gray-700 cursor-pointer"
          >
            {/* Icon */}
            <div
              className={`flex items-center justify-center w-12 h-12 rounded-xl text-white ${action.color} transition-all duration-200 mb-3 shadow-sm group-hover:shadow-md group-hover:scale-105`}
            >
              {action.icon}
            </div>

            {/* Label */}
            <div className="text-sm font-medium text-gray-900 dark:text-white dark:text-white text-center mb-1">
              {action.label}
            </div>

            {/* Description */}
            <div className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-center leading-tight">
              {action.description}
            </div>

            {/* Hover effect */}
            <div className="absolute inset-0 rounded-lg bg-gray-50 dark:bg-gray-800 dark:bg-gray-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200 -z-10" />
          </button>
        ))}
      </div>

      {/* Additional shortcuts */}
      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 dark:border-gray-600">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white dark:text-white mb-3">Quick Links</h4>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => navigate("/invoices")}
            className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 dark:bg-gray-800 text-gray-700 dark:text-gray-300 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200 cursor-pointer"
          >
            Draft Invoices
          </button>
          <button
            onClick={() => navigate("/invoices")}
            className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors duration-200 cursor-pointer"
          >
            Overdue Invoices
          </button>
          <button
            onClick={() => navigate("/transactions")}
            className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 hover:bg-yellow-200 dark:hover:bg-yellow-900/50 transition-colors duration-200 cursor-pointer"
          >
            Pending Transactions
          </button>
          <button
            onClick={() => navigate("/reports")}
            className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors duration-200 cursor-pointer"
          >
            P&L Report
          </button>
          <button
            onClick={() => navigate("/reports")}
            className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 dark:bg-green-900/30 text-green-700 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors duration-200 cursor-pointer"
          >
            Balance Sheet
          </button>
          <button
            onClick={() => navigate("/reports")}
            className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors duration-200 cursor-pointer"
          >
            Cash Flow
          </button>
        </div>
      </div>
    </div>
  );
}
