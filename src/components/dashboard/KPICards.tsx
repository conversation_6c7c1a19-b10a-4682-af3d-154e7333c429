import React from "react";
import { dashboardService } from "../../services/dashboardService";

interface KPICardsProps {
  stats: any;
  currency?: string;
}

export default function KPICards({ stats, currency = "USD" }: KPICardsProps) {
  const kpis = [
    {
      id: "gross-profit-margin",
      label: "Gross Profit Margin",
      value: `${(stats?.grossProfitMargin || 0).toFixed(1)}%`,
      change: null,
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
            clipRule="evenodd"
          />
        </svg>
      ),
      color:
        (stats?.grossProfitMargin || 0) >= 20
          ? "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20"
          : "text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20",
      description: "Profitability after direct costs",
    },
    {
      id: "operating-cash-flow",
      label: "Operating Cash Flow",
      value: dashboardService.formatCurrency(
        stats?.operatingCashFlow || 0,
        currency
      ),
      change: null,
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
          <path
            fillRule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z"
            clipRule="evenodd"
          />
        </svg>
      ),
      color:
        (stats?.operatingCashFlow || 0) >= 0
          ? "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20"
          : "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20",
      description: "Cash generated from operations",
    },
    {
      id: "working-capital",
      label: "Working Capital",
      value: dashboardService.formatCurrency(
        stats?.workingCapital || 0,
        currency
      ),
      change: null,
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
            clipRule="evenodd"
          />
        </svg>
      ),
      color:
        (stats?.workingCapital || 0) >= 0
          ? "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20"
          : "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20",
      description: "Short-term financial health",
    },
    {
      id: "burn-rate",
      label: "Monthly Burn Rate",
      value: dashboardService.formatCurrency(stats?.burnRate || 0, currency),
      change: null,
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z"
            clipRule="evenodd"
          />
        </svg>
      ),
      color: "text-orange-600 bg-orange-100",
      description: "Average monthly expenses",
    },
    {
      id: "collection-period",
      label: "Avg Collection Period",
      value: `${Math.round(stats?.avgCollectionPeriod || 0)} days`,
      change: null,
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
            clipRule="evenodd"
          />
        </svg>
      ),
      color:
        (stats?.avgCollectionPeriod || 0) <= 30
          ? "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20"
          : (stats?.avgCollectionPeriod || 0) <= 45
          ? "text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20"
          : "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20",
      description: "Time to collect receivables",
    },
    {
      id: "payment-period",
      label: "Avg Payment Period",
      value: `${Math.round(stats?.avgPaymentPeriod || 0)} days`,
      change: null,
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
          <path
            fillRule="evenodd"
            d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"
            clipRule="evenodd"
          />
        </svg>
      ),
      color: "text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20",
      description: "Time to pay suppliers",
    },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 h-full">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white dark:text-white">
          Key Performance Indicators
        </h3>
        <div className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Current Period</div>
      </div>

      <div className="grid bg-white dark:bg-gray-800 grid-cols-2 gap-3">
        {kpis.map((kpi) => (
          <div
            key={kpi.id}
            className="flex flex-col p-3 rounded-lg border border-gray-100 dark:border-gray-600 hover:border-blue-200 dark:hover:border-blue-500 hover:bg-blue-50 dark:bg-blue-900/20 dark:hover:bg-gray-700 transition-all duration-200"
          >
            <div className="flex items-center mb-2">
              <div
                className={`w-6 h-6 rounded-md flex items-center justify-center ${kpi.color} shadow-sm flex-shrink-0`}
              >
                {React.cloneElement(kpi.icon as React.ReactElement, {
                  className: "w-3 h-3",
                } as any)}
              </div>
              <h4 className="text-xs font-medium text-gray-900 dark:text-white dark:text-white truncate ml-2">
                {kpi.label}
              </h4>
            </div>
            <div className="flex items-baseline justify-between">
              <div className="text-sm font-bold text-gray-900 dark:text-white dark:text-white">{kpi.value}</div>
              {kpi.change && (
                <div
                  className={`flex items-center text-xs font-medium ${
                    kpi.change >= 0 ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
                  }`}
                >
                  {kpi.change >= 0 ? (
                    <svg
                      className="w-3 h-3 mr-1"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="w-3 h-3 mr-1"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                  {Math.abs(kpi.change).toFixed(1)}%
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Growth Indicators */}
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 dark:border-gray-600">
        <h4 className="text-sm font-semibold text-gray-900 dark:text-white dark:text-white mb-3">
          Growth Metrics
        </h4>

        <div className="grid bg-white dark:bg-gray-800 grid-cols-2 gap-3">
          <div className="flex flex-col p-3 rounded-lg border border-gray-100 dark:border-gray-600 hover:border-blue-200 dark:hover:border-blue-500 hover:bg-blue-50 dark:bg-blue-900/20 dark:hover:bg-gray-700 transition-all duration-200">
            <div className="flex items-center mb-2">
              <div
                className={`w-6 h-6 rounded-md flex items-center justify-center ${
                  (stats?.revenueGrowth || 0) >= 0
                    ? "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20"
                    : "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20"
                } shadow-sm flex-shrink-0`}
              >
                <svg
                  className="w-3 h-3"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <h4 className="text-xs font-medium text-gray-900 dark:text-white dark:text-white truncate ml-2">
                Revenue Growth
              </h4>
            </div>
            <div className="flex items-baseline justify-between">
              <div
                className={`text-sm font-bold ${
                  (stats?.revenueGrowth || 0) >= 0
                    ? "text-green-600 dark:text-green-400"
                    : "text-red-600 dark:text-red-400"
                }`}
              >
                {(stats?.revenueGrowth || 0) >= 0 ? "+" : ""}
                {(stats?.revenueGrowth || 0).toFixed(1)}%
              </div>
            </div>
          </div>

          <div className="flex flex-col p-3 rounded-lg border border-gray-100 dark:border-gray-600 hover:border-blue-200 dark:hover:border-blue-500 hover:bg-blue-50 dark:bg-blue-900/20 dark:hover:bg-gray-700 transition-all duration-200">
            <div className="flex items-center mb-2">
              <div
                className={`w-6 h-6 rounded-md flex items-center justify-center ${
                  (stats?.expenseGrowth || 0) <= 5
                    ? "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20"
                    : "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20"
                } shadow-sm flex-shrink-0`}
              >
                <svg
                  className="w-3 h-3"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <h4 className="text-xs font-medium text-gray-900 dark:text-white dark:text-white truncate ml-2">
                Expense Growth
              </h4>
            </div>
            <div className="flex items-baseline justify-between">
              <div
                className={`text-sm font-bold ${
                  (stats?.expenseGrowth || 0) <= 5
                    ? "text-green-600 dark:text-green-400"
                    : "text-red-600 dark:text-red-400"
                }`}
              >
                {(stats?.expenseGrowth || 0) >= 0 ? "+" : ""}
                {(stats?.expenseGrowth || 0).toFixed(1)}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
