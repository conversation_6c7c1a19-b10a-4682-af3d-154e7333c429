import { useState } from "react";
import {
  DocumentTextIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CalculatorIcon,
  UserGroupIcon,
  HeartIcon,
  ShieldCheckIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";

interface StatutoryCalculationResult {
  grossSalary: number;
  basicSalary: number;
  nssf: {
    employee: number;
    employer: number;
    rate: number;
    cap: number;
  };
  wcf: {
    amount: number;
    rate: number;
  };
  nhif: {
    amount: number;
    band: string;
  };
  totalEmployeeDeductions: number;
  totalEmployerContributions: number;
  netSalary: number;
}

export default function StatutoryContributions() {
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();
  const [grossSalary, setGrossSalary] = useState<string>("");
  const [basicSalary, setBasicSalary] = useState<string>("");
  const [result, setResult] = useState<StatutoryCalculationResult | null>(null);
  const [loading, setLoading] = useState(false);

  const handleCalculate = async () => {
    if (!currentCompany || !grossSalary || !basicSalary) {
      addNotification("error", "Validation Error", "Please enter both gross and basic salary");
      return;
    }

    const gross = parseFloat(grossSalary);
    const basic = parseFloat(basicSalary);

    if (gross <= 0 || basic <= 0 || basic > gross) {
      addNotification("error", "Validation Error", "Please enter valid salary amounts (basic ≤ gross)");
      return;
    }

    setLoading(true);
    try {
      // Calculate NSSF (10% of basic salary, max 20,000 TZS)
      const nssfEmployee = Math.min(basic * 0.10, 20000);
      const nssfEmployer = nssfEmployee; // Same amount for employer

      // Calculate WCF (0.5% of gross salary)
      const wcfAmount = gross * 0.005;

      // Calculate NHIF (band-based on gross salary)
      let nhifAmount = 1700; // Default for high salaries
      let nhifBand = "Above 100K";

      if (gross <= 15000) {
        nhifAmount = 150;
        nhifBand = "Up to 15K";
      } else if (gross <= 20000) {
        nhifAmount = 300;
        nhifBand = "15K - 20K";
      } else if (gross <= 25000) {
        nhifAmount = 400;
        nhifBand = "20K - 25K";
      } else if (gross <= 30000) {
        nhifAmount = 500;
        nhifBand = "25K - 30K";
      } else if (gross <= 35000) {
        nhifAmount = 600;
        nhifBand = "30K - 35K";
      } else if (gross <= 40000) {
        nhifAmount = 750;
        nhifBand = "35K - 40K";
      } else if (gross <= 45000) {
        nhifAmount = 900;
        nhifBand = "40K - 45K";
      } else if (gross <= 50000) {
        nhifAmount = 1000;
        nhifBand = "45K - 50K";
      } else if (gross <= 60000) {
        nhifAmount = 1200;
        nhifBand = "50K - 60K";
      } else if (gross <= 70000) {
        nhifAmount = 1300;
        nhifBand = "60K - 70K";
      } else if (gross <= 80000) {
        nhifAmount = 1400;
        nhifBand = "70K - 80K";
      } else if (gross <= 90000) {
        nhifAmount = 1500;
        nhifBand = "80K - 90K";
      } else if (gross <= 100000) {
        nhifAmount = 1600;
        nhifBand = "90K - 100K";
      }

      const totalEmployeeDeductions = nssfEmployee + nhifAmount;
      const totalEmployerContributions = nssfEmployer + wcfAmount;
      const netSalary = gross - totalEmployeeDeductions;

      const calculationResult: StatutoryCalculationResult = {
        grossSalary: gross,
        basicSalary: basic,
        nssf: {
          employee: Math.round(nssfEmployee * 100) / 100,
          employer: Math.round(nssfEmployer * 100) / 100,
          rate: 10,
          cap: 20000
        },
        wcf: {
          amount: Math.round(wcfAmount * 100) / 100,
          rate: 0.5
        },
        nhif: {
          amount: nhifAmount,
          band: nhifBand
        },
        totalEmployeeDeductions: Math.round(totalEmployeeDeductions * 100) / 100,
        totalEmployerContributions: Math.round(totalEmployerContributions * 100) / 100,
        netSalary: Math.round(netSalary * 100) / 100
      };

      setResult(calculationResult);
      addNotification("success", "Success", "Statutory contributions calculated successfully");
    } catch (error) {
      console.error("Error calculating statutory contributions:", error);
      addNotification("error", "Calculation Error", "Failed to calculate statutory contributions");
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setGrossSalary("");
    setBasicSalary("");
    setResult(null);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-TZ", {
      style: "currency",
      currency: "TZS",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <DocumentTextIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Statutory Contributions Calculator
          </h3>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
          <InformationCircleIcon className="h-4 w-4" />
          <span>Calculate NSSF, WCF, and NHIF contributions</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <div className="space-y-6">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
              Salary Information
            </h4>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Gross Monthly Salary (TZS)
                </label>
                <input
                  type="number"
                  value={grossSalary}
                  onChange={(e) => setGrossSalary(e.target.value)}
                  placeholder="Enter gross monthly salary"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                  min="0"
                  step="1000"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Basic Monthly Salary (TZS)
                </label>
                <input
                  type="number"
                  value={basicSalary}
                  onChange={(e) => setBasicSalary(e.target.value)}
                  placeholder="Enter basic monthly salary"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                  min="0"
                  step="1000"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Basic salary is used for NSSF calculation (excluding allowances)
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 mt-6">
              <button
                onClick={handleCalculate}
                disabled={loading || !grossSalary || !basicSalary}
                className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                <CalculatorIcon className="h-4 w-4 mr-2" />
                {loading ? "Calculating..." : "Calculate Contributions"}
              </button>
              <button
                onClick={handleClear}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
              >
                Clear
              </button>
            </div>
          </div>

          {/* Statutory Contributions Information */}
          <div className="space-y-4">
            {/* NSSF Info */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <UserGroupIcon className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                <h5 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  NSSF - National Social Security Fund
                </h5>
              </div>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <li>• 10% of basic salary (employee contribution)</li>
                <li>• 10% of basic salary (employer contribution)</li>
                <li>• Maximum: 20,000 TZS per month each</li>
                <li>• Retirement and disability benefits</li>
              </ul>
            </div>

            {/* WCF Info */}
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <ShieldCheckIcon className="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
                <h5 className="text-sm font-medium text-green-900 dark:text-green-100">
                  WCF - Workers Compensation Fund
                </h5>
              </div>
              <ul className="text-sm text-green-800 dark:text-green-200 space-y-1">
                <li>• 0.5% of gross salary (employer only)</li>
                <li>• Workplace injury compensation</li>
                <li>• Occupational disease coverage</li>
                <li>• Mandatory for all employers</li>
              </ul>
            </div>

            {/* NHIF Info */}
            <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <HeartIcon className="h-5 w-5 text-purple-600 dark:text-purple-400 mr-2" />
                <h5 className="text-sm font-medium text-purple-900 dark:text-purple-100">
                  NHIF - National Health Insurance Fund
                </h5>
              </div>
              <ul className="text-sm text-purple-800 dark:text-purple-200 space-y-1">
                <li>• Band-based rates (150 - 1,700 TZS)</li>
                <li>• Based on gross salary level</li>
                <li>• Employee contribution only</li>
                <li>• Healthcare coverage and benefits</li>
              </ul>
            </div>
          </div>

          {/* Quick Examples */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              Quick Examples
            </h4>
            <div className="space-y-2">
              <button
                onClick={() => {
                  setGrossSalary("500000");
                  setBasicSalary("400000");
                }}
                className="w-full text-left px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                Entry Level: 500K gross, 400K basic
              </button>
              <button
                onClick={() => {
                  setGrossSalary("800000");
                  setBasicSalary("600000");
                }}
                className="w-full text-left px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                Mid Level: 800K gross, 600K basic
              </button>
              <button
                onClick={() => {
                  setGrossSalary("1200000");
                  setBasicSalary("800000");
                }}
                className="w-full text-left px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                Senior Level: 1.2M gross, 800K basic
              </button>
              <button
                onClick={() => {
                  setGrossSalary("2000000");
                  setBasicSalary("1000000");
                }}
                className="w-full text-left px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                Executive Level: 2M gross, 1M basic
              </button>
            </div>
          </div>
        </div>

        {/* Results Section */}
        <div className="space-y-6">
          {result ? (
            <div className="space-y-6">
              {/* Summary Card */}
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-medium text-green-900 dark:text-green-100">
                    Statutory Contributions Result
                  </h4>
                  <CheckCircleIcon className="h-8 w-8 text-green-500" />
                </div>

                <div className="space-y-4">
                  {/* Salary Summary */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg">
                      <p className="text-sm text-gray-600 dark:text-gray-400">Gross Salary</p>
                      <p className="text-lg font-bold text-gray-900 dark:text-white">
                        {formatCurrency(result.grossSalary)}
                      </p>
                    </div>
                    <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg">
                      <p className="text-sm text-gray-600 dark:text-gray-400">Basic Salary</p>
                      <p className="text-lg font-bold text-gray-900 dark:text-white">
                        {formatCurrency(result.basicSalary)}
                      </p>
                    </div>
                  </div>

                  {/* Contributions Breakdown */}
                  <div className="space-y-3">
                    {/* NSSF */}
                    <div className="bg-blue-100 dark:bg-blue-900/30 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <UserGroupIcon className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                          <span className="font-medium text-blue-900 dark:text-blue-100">NSSF</span>
                        </div>
                        <span className="text-sm text-blue-700 dark:text-blue-300">
                          {result.nssf.rate}% (max {formatCurrency(result.nssf.cap)})
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-blue-700 dark:text-blue-300">Employee:</p>
                          <p className="font-bold text-blue-900 dark:text-blue-100">
                            {formatCurrency(result.nssf.employee)}
                          </p>
                        </div>
                        <div>
                          <p className="text-blue-700 dark:text-blue-300">Employer:</p>
                          <p className="font-bold text-blue-900 dark:text-blue-100">
                            {formatCurrency(result.nssf.employer)}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* WCF */}
                    <div className="bg-green-100 dark:bg-green-900/30 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <ShieldCheckIcon className="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
                          <span className="font-medium text-green-900 dark:text-green-100">WCF</span>
                        </div>
                        <span className="text-sm text-green-700 dark:text-green-300">
                          {result.wcf.rate}% (employer only)
                        </span>
                      </div>
                      <div className="text-center">
                        <p className="text-green-700 dark:text-green-300 text-sm">Employer Contribution:</p>
                        <p className="font-bold text-green-900 dark:text-green-100 text-lg">
                          {formatCurrency(result.wcf.amount)}
                        </p>
                      </div>
                    </div>

                    {/* NHIF */}
                    <div className="bg-purple-100 dark:bg-purple-900/30 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <HeartIcon className="h-5 w-5 text-purple-600 dark:text-purple-400 mr-2" />
                          <span className="font-medium text-purple-900 dark:text-purple-100">NHIF</span>
                        </div>
                        <span className="text-sm text-purple-700 dark:text-purple-300">
                          Band: {result.nhif.band}
                        </span>
                      </div>
                      <div className="text-center">
                        <p className="text-purple-700 dark:text-purple-300 text-sm">Employee Contribution:</p>
                        <p className="font-bold text-purple-900 dark:text-purple-100 text-lg">
                          {formatCurrency(result.nhif.amount)}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Totals */}
                  <div className="grid grid-cols-2 gap-4 pt-4 border-t border-green-200 dark:border-green-700">
                    <div className="text-center p-3 bg-red-100 dark:bg-red-900/30 rounded-lg">
                      <p className="text-sm text-red-700 dark:text-red-300">Employee Deductions</p>
                      <p className="text-lg font-bold text-red-900 dark:text-red-100">
                        {formatCurrency(result.totalEmployeeDeductions)}
                      </p>
                    </div>
                    <div className="text-center p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                      <p className="text-sm text-orange-700 dark:text-orange-300">Employer Contributions</p>
                      <p className="text-lg font-bold text-orange-900 dark:text-orange-100">
                        {formatCurrency(result.totalEmployerContributions)}
                      </p>
                    </div>
                  </div>

                  {/* Net Salary */}
                  <div className="text-center p-4 bg-green-200 dark:bg-green-800/30 rounded-lg">
                    <p className="text-sm text-green-700 dark:text-green-300">Net Salary (after statutory deductions)</p>
                    <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                      {formatCurrency(result.netSalary)}
                    </p>
                  </div>
                </div>
              </div>

              {/* Important Notes */}
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div className="flex items-start">
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2" />
                  <div>
                    <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                      Important Notes
                    </h5>
                    <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                      <li>• This calculation excludes PAYE tax - use PAYE Calculator for complete payroll</li>
                      <li>• NSSF contributions are capped at 20,000 TZS monthly per person</li>
                      <li>• WCF is paid entirely by the employer</li>
                      <li>• NHIF rates are based on current Tanzania government schedules</li>
                      <li>• All contributions must be remitted to respective authorities monthly</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 text-center">
              <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Ready to Calculate
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Enter gross and basic salary to calculate statutory contributions (NSSF, WCF, NHIF).
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
