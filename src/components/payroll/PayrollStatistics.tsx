import { useState, useEffect } from "react";
import {
  ChartBarIcon,
  ArrowDownTrayIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CalendarIcon,
  DocumentChartBarIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { apiService } from "../../services/api";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

interface PayrollTrend {
  month: string;
  grossPay: number;
  paye: number;
  sdl: number;
  nssf: number;
  nhif: number;
  wcf: number;
  netPay: number;
  employees: number;
}

interface EmployeeTaxBurden {
  employeeId: string;
  employeeName: string;
  grossPay: number;
  totalTax: number;
  paye: number;
  sdl: number;
  nssf: number;
  nhif: number;
  wcf: number;
  taxRate: number;
  netPay: number;
}

interface ComplianceMetrics {
  payeCompliance: number;
  sdlCompliance: number;
  nssfCompliance: number;
  nhifCompliance: number;
  wcfCompliance: number;
  overallScore: number;
  lastUpdated: string;
}

interface PayrollBreakdown {
  category: string;
  amount: number;
  percentage: number;
  color: string;
}

export default function PayrollStatistics() {
  const [loading, setLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('12months');
  const [selectedView, setSelectedView] = useState('overview');
  const [payrollTrends, setPayrollTrends] = useState<PayrollTrend[]>([]);
  const [employeeTaxBurdens, setEmployeeTaxBurdens] = useState<EmployeeTaxBurden[]>([]);
  const [complianceMetrics, setComplianceMetrics] = useState<ComplianceMetrics | null>(null);
  const [payrollBreakdown, setPayrollBreakdown] = useState<PayrollBreakdown[]>([]);

  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  useEffect(() => {
    if (currentCompany) {
      fetchPayrollStatistics();
    }
  }, [currentCompany, selectedPeriod]);

  const fetchPayrollStatistics = async () => {
    setLoading(true);
    try {
      // Mock data for demonstration
      const mockTrends: PayrollTrend[] = [
        {
          month: 'Jan 2024',
          grossPay: 58000000,
          paye: 8120000,
          sdl: 3480000,
          nssf: 5800000,
          nhif: 1160000,
          wcf: 290000,
          netPay: 39150000,
          employees: 23,
        },
        {
          month: 'Feb 2024',
          grossPay: 59500000,
          paye: 8330000,
          sdl: 3570000,
          nssf: 5950000,
          nhif: 1190000,
          wcf: 297500,
          netPay: 40162500,
          employees: 24,
        },
        {
          month: 'Mar 2024',
          grossPay: 61000000,
          paye: 8540000,
          sdl: 3660000,
          nssf: 6100000,
          nhif: 1220000,
          wcf: 305000,
          netPay: 41175000,
          employees: 24,
        },
        {
          month: 'Apr 2024',
          grossPay: 62000000,
          paye: 8680000,
          sdl: 3720000,
          nssf: 6200000,
          nhif: 1240000,
          wcf: 310000,
          netPay: 41850000,
          employees: 25,
        },
        {
          month: 'May 2024',
          grossPay: 62500000,
          paye: 8750000,
          sdl: 3750000,
          nssf: 6250000,
          nhif: 1250000,
          wcf: 312500,
          netPay: 42187500,
          employees: 25,
        },
        {
          month: 'Jun 2024',
          grossPay: 63000000,
          paye: 8820000,
          sdl: 3780000,
          nssf: 6300000,
          nhif: 1260000,
          wcf: 315000,
          netPay: 42525000,
          employees: 25,
        },
      ];

      const mockEmployeeTaxBurdens: EmployeeTaxBurden[] = [
        {
          employeeId: '1',
          employeeName: 'John Doe',
          grossPay: 3600000,
          totalTax: 1080000,
          paye: 540000,
          sdl: 216000,
          nssf: 360000,
          nhif: 36000,
          wcf: 18000,
          taxRate: 30,
          netPay: 2520000,
        },
        {
          employeeId: '2',
          employeeName: 'Jane Smith',
          grossPay: 2800000,
          totalTax: 700000,
          paye: 350000,
          sdl: 168000,
          nssf: 280000,
          nhif: 28000,
          wcf: 14000,
          taxRate: 25,
          netPay: 2100000,
        },
        {
          employeeId: '3',
          employeeName: 'Michael Johnson',
          grossPay: 2200000,
          totalTax: 462000,
          paye: 220000,
          sdl: 132000,
          nssf: 220000,
          nhif: 22000,
          wcf: 11000,
          taxRate: 21,
          netPay: 1738000,
        },
      ];

      const mockCompliance: ComplianceMetrics = {
        payeCompliance: 98,
        sdlCompliance: 100,
        nssfCompliance: 95,
        nhifCompliance: 97,
        wcfCompliance: 100,
        overallScore: 98,
        lastUpdated: new Date().toISOString(),
      };

      const mockBreakdown: PayrollBreakdown[] = [
        { category: 'Net Pay', amount: 42525000, percentage: 67.5, color: '#10B981' },
        { category: 'PAYE Tax', amount: 8820000, percentage: 14.0, color: '#EF4444' },
        { category: 'NSSF', amount: 6300000, percentage: 10.0, color: '#3B82F6' },
        { category: 'SDL', amount: 3780000, percentage: 6.0, color: '#8B5CF6' },
        { category: 'NHIF', amount: 1260000, percentage: 2.0, color: '#F59E0B' },
        { category: 'WCF', amount: 315000, percentage: 0.5, color: '#6B7280' },
      ];

      setPayrollTrends(mockTrends);
      setEmployeeTaxBurdens(mockEmployeeTaxBurdens);
      setComplianceMetrics(mockCompliance);
      setPayrollBreakdown(mockBreakdown);

      addNotification("success", "Success", "Payroll statistics loaded successfully");
    } catch (error: any) {
      console.error("Error fetching payroll statistics:", error);
      addNotification("error", "Error", "Failed to load payroll statistics");
    } finally {
      setLoading(false);
    }
  };

  const exportReport = async () => {
    try {
      addNotification("success", "Export Started", "Payroll statistics report is being generated");
      // In a real implementation, this would trigger a file download
      console.log("Exporting payroll statistics report");
    } catch (error: any) {
      console.error("Error exporting report:", error);
      addNotification("error", "Export Error", "Failed to export report");
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getComplianceColor = (score: number) => {
    if (score >= 95) return 'text-green-600 dark:text-green-400';
    if (score >= 85) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getComplianceIcon = (score: number) => {
    if (score >= 95) return <CheckCircleIcon className="h-5 w-5" />;
    return <ExclamationTriangleIcon className="h-5 w-5" />;
  };

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) {
      return <ArrowTrendingUpIcon className="h-4 w-4 text-green-500" />;
    } else if (current < previous) {
      return <ArrowTrendingDownIcon className="h-4 w-4 text-red-500" />;
    }
    return null;
  };

  const calculateTrendPercentage = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  const currentMonth = payrollTrends[payrollTrends.length - 1];
  const previousMonth = payrollTrends[payrollTrends.length - 2];

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <ChartBarIcon className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Payroll Statistics & Analytics
          </h3>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="6months">Last 6 Months</option>
            <option value="12months">Last 12 Months</option>
            <option value="24months">Last 24 Months</option>
          </select>
          <button
            onClick={exportReport}
            className="flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors duration-200"
          >
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            Export Report
          </button>
        </div>
      </div>

      {/* Payroll Statistics Information */}
      <div className="bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <InformationCircleIcon className="h-5 w-5 text-indigo-600 dark:text-indigo-400 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-indigo-900 dark:text-indigo-100 mb-1">
              Payroll Analytics & Insights - Tanzania
            </h4>
            <p className="text-sm text-indigo-800 dark:text-indigo-200">
              Comprehensive payroll analytics including PAYE and SDL trends, employee tax burden analysis,
              compliance tracking, and detailed cost breakdowns for informed decision-making.
            </p>
          </div>
        </div>
      </div>

      {/* View Selector */}
      <div className="mb-6">
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          {[
            { id: 'overview', label: 'Overview', icon: ChartBarIcon },
            { id: 'trends', label: 'Trends', icon: ArrowTrendingUpIcon },
            { id: 'employees', label: 'Employee Analysis', icon: UserGroupIcon },
            { id: 'compliance', label: 'Compliance', icon: CheckCircleIcon },
          ].map((view) => (
            <button
              key={view.id}
              onClick={() => setSelectedView(view.id)}
              className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                selectedView === view.id
                  ? 'bg-white dark:bg-gray-700 text-indigo-600 dark:text-indigo-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              <view.icon className="h-4 w-4 mr-2" />
              {view.label}
            </button>
          ))}
        </div>
      </div>

      {loading ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading statistics...</p>
        </div>
      ) : (
        <>
          {/* Overview Section */}
          {selectedView === 'overview' && (
            <div className="space-y-6">
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <CurrencyDollarIcon className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Gross Pay</p>
                      <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                        {currentMonth ? formatCurrency(currentMonth.grossPay) : 'N/A'}
                      </p>
                      {currentMonth && previousMonth && (
                        <div className="flex items-center mt-1">
                          {getTrendIcon(currentMonth.grossPay, previousMonth.grossPay)}
                          <span className={`text-sm ml-1 ${
                            currentMonth.grossPay > previousMonth.grossPay
                              ? 'text-green-600 dark:text-green-400'
                              : 'text-red-600 dark:text-red-400'
                          }`}>
                            {formatPercentage(Math.abs(calculateTrendPercentage(currentMonth.grossPay, previousMonth.grossPay)))}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <UserGroupIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Employees</p>
                      <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                        {currentMonth ? currentMonth.employees : 'N/A'}
                      </p>
                      {currentMonth && previousMonth && (
                        <div className="flex items-center mt-1">
                          {getTrendIcon(currentMonth.employees, previousMonth.employees)}
                          <span className={`text-sm ml-1 ${
                            currentMonth.employees > previousMonth.employees
                              ? 'text-green-600 dark:text-green-400'
                              : 'text-red-600 dark:text-red-400'
                          }`}>
                            {Math.abs(currentMonth.employees - previousMonth.employees)} employees
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <DocumentChartBarIcon className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Taxes</p>
                      <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                        {currentMonth ? formatCurrency(currentMonth.paye + currentMonth.sdl) : 'N/A'}
                      </p>
                      {currentMonth && previousMonth && (
                        <div className="flex items-center mt-1">
                          {getTrendIcon(
                            currentMonth.paye + currentMonth.sdl,
                            previousMonth.paye + previousMonth.sdl
                          )}
                          <span className={`text-sm ml-1 ${
                            (currentMonth.paye + currentMonth.sdl) > (previousMonth.paye + previousMonth.sdl)
                              ? 'text-green-600 dark:text-green-400'
                              : 'text-red-600 dark:text-red-400'
                          }`}>
                            {formatPercentage(Math.abs(calculateTrendPercentage(
                              currentMonth.paye + currentMonth.sdl,
                              previousMonth.paye + previousMonth.sdl
                            )))}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                        complianceMetrics && complianceMetrics.overallScore >= 95
                          ? 'bg-green-100 dark:bg-green-900/20'
                          : 'bg-yellow-100 dark:bg-yellow-900/20'
                      }`}>
                        {complianceMetrics && getComplianceIcon(complianceMetrics.overallScore)}
                      </div>
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Compliance Score</p>
                      <p className={`text-2xl font-semibold ${
                        complianceMetrics ? getComplianceColor(complianceMetrics.overallScore) : 'text-gray-900 dark:text-white'
                      }`}>
                        {complianceMetrics ? `${complianceMetrics.overallScore}%` : 'N/A'}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {complianceMetrics && complianceMetrics.overallScore >= 95 ? 'Excellent' : 'Needs Attention'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payroll Breakdown Chart */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Current Month Payroll Breakdown
                </h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={payrollBreakdown}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ category, percentage }) => `${category}: ${percentage}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="amount"
                        >
                          {payrollBreakdown.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value: number) => formatCurrency(value)} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="space-y-3">
                    {payrollBreakdown.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div
                            className="w-4 h-4 rounded mr-3"
                            style={{ backgroundColor: item.color }}
                          ></div>
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {item.category}
                          </span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-semibold text-gray-900 dark:text-white">
                            {formatCurrency(item.amount)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {formatPercentage(item.percentage)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Trends Section */}
          {selectedView === 'trends' && (
            <div className="space-y-6">
              {/* Payroll Trends Chart */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Payroll Trends Over Time
                </h4>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={payrollTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis tickFormatter={(value) => `${(value / 1000000).toFixed(0)}M`} />
                      <Tooltip formatter={(value: number) => formatCurrency(value)} />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="grossPay"
                        stackId="1"
                        stroke="#3B82F6"
                        fill="#3B82F6"
                        fillOpacity={0.6}
                        name="Gross Pay"
                      />
                      <Area
                        type="monotone"
                        dataKey="netPay"
                        stackId="2"
                        stroke="#10B981"
                        fill="#10B981"
                        fillOpacity={0.6}
                        name="Net Pay"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Tax Trends Chart */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Tax & Contribution Trends
                </h4>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={payrollTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis tickFormatter={(value) => `${(value / 1000000).toFixed(0)}M`} />
                      <Tooltip formatter={(value: number) => formatCurrency(value)} />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="paye"
                        stroke="#EF4444"
                        strokeWidth={2}
                        name="PAYE Tax"
                      />
                      <Line
                        type="monotone"
                        dataKey="sdl"
                        stroke="#8B5CF6"
                        strokeWidth={2}
                        name="SDL"
                      />
                      <Line
                        type="monotone"
                        dataKey="nssf"
                        stroke="#3B82F6"
                        strokeWidth={2}
                        name="NSSF"
                      />
                      <Line
                        type="monotone"
                        dataKey="nhif"
                        stroke="#F59E0B"
                        strokeWidth={2}
                        name="NHIF"
                      />
                      <Line
                        type="monotone"
                        dataKey="wcf"
                        stroke="#6B7280"
                        strokeWidth={2}
                        name="WCF"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Employee Count Trend */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Employee Count Trend
                </h4>
                <div className="h-60">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={payrollTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="employees" fill="#10B981" name="Employees" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
          )}

          {/* Employee Analysis Section */}
          {selectedView === 'employees' && (
            <div className="space-y-6">
              {/* Employee Tax Burden Analysis */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Employee Tax Burden Analysis
                </h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Employee
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Gross Pay
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Total Tax
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Tax Rate
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Net Pay
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Breakdown
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {employeeTaxBurdens.map((employee) => (
                        <tr key={employee.employeeId} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {employee.employeeName}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 dark:text-white">
                              {formatCurrency(employee.grossPay)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 dark:text-white">
                              {formatCurrency(employee.totalTax)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              employee.taxRate >= 25
                                ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                                : employee.taxRate >= 15
                                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                                : 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                            }`}>
                              {formatPercentage(employee.taxRate)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 dark:text-white">
                              {formatCurrency(employee.netPay)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                              <div>PAYE: {formatCurrency(employee.paye)}</div>
                              <div>SDL: {formatCurrency(employee.sdl)}</div>
                              <div>NSSF: {formatCurrency(employee.nssf)}</div>
                              <div>NHIF: {formatCurrency(employee.nhif)}</div>
                              <div>WCF: {formatCurrency(employee.wcf)}</div>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Tax Burden Distribution Chart */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Tax Burden Distribution by Employee
                </h4>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={employeeTaxBurdens}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="employeeName" />
                      <YAxis tickFormatter={(value) => `${(value / 1000000).toFixed(1)}M`} />
                      <Tooltip formatter={(value: number) => formatCurrency(value)} />
                      <Legend />
                      <Bar dataKey="paye" stackId="a" fill="#EF4444" name="PAYE" />
                      <Bar dataKey="sdl" stackId="a" fill="#8B5CF6" name="SDL" />
                      <Bar dataKey="nssf" stackId="a" fill="#3B82F6" name="NSSF" />
                      <Bar dataKey="nhif" stackId="a" fill="#F59E0B" name="NHIF" />
                      <Bar dataKey="wcf" stackId="a" fill="#6B7280" name="WCF" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
          )}

          {/* Compliance Section */}
          {selectedView === 'compliance' && complianceMetrics && (
            <div className="space-y-6">
              {/* Compliance Overview */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  { name: 'PAYE Compliance', score: complianceMetrics.payeCompliance, color: 'indigo' },
                  { name: 'SDL Compliance', score: complianceMetrics.sdlCompliance, color: 'purple' },
                  { name: 'NSSF Compliance', score: complianceMetrics.nssfCompliance, color: 'blue' },
                  { name: 'NHIF Compliance', score: complianceMetrics.nhifCompliance, color: 'yellow' },
                  { name: 'WCF Compliance', score: complianceMetrics.wcfCompliance, color: 'gray' },
                  { name: 'Overall Score', score: complianceMetrics.overallScore, color: 'green' },
                ].map((metric, index) => (
                  <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {metric.name}
                        </p>
                        <p className={`text-2xl font-semibold ${getComplianceColor(metric.score)}`}>
                          {metric.score}%
                        </p>
                      </div>
                      <div className={`${getComplianceColor(metric.score)}`}>
                        {getComplianceIcon(metric.score)}
                      </div>
                    </div>
                    <div className="mt-4">
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            metric.score >= 95 ? 'bg-green-500' :
                            metric.score >= 85 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${metric.score}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Compliance Details */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Compliance Details & Recommendations
                </h4>
                <div className="space-y-4">
                  {[
                    {
                      category: 'PAYE Tax',
                      score: complianceMetrics.payeCompliance,
                      status: complianceMetrics.payeCompliance >= 95 ? 'Excellent' : 'Needs Attention',
                      recommendation: complianceMetrics.payeCompliance >= 95
                        ? 'PAYE calculations and submissions are on track.'
                        : 'Review PAYE calculations and ensure timely submissions to TRA.',
                    },
                    {
                      category: 'Skills Development Levy',
                      score: complianceMetrics.sdlCompliance,
                      status: complianceMetrics.sdlCompliance >= 95 ? 'Excellent' : 'Needs Attention',
                      recommendation: complianceMetrics.sdlCompliance >= 95
                        ? 'SDL payments are up to date and compliant.'
                        : 'Ensure SDL payments are made on time to avoid penalties.',
                    },
                    {
                      category: 'NSSF Contributions',
                      score: complianceMetrics.nssfCompliance,
                      status: complianceMetrics.nssfCompliance >= 95 ? 'Excellent' : 'Needs Attention',
                      recommendation: complianceMetrics.nssfCompliance >= 95
                        ? 'NSSF contributions are being processed correctly.'
                        : 'Review NSSF contribution calculations and payment schedules.',
                    },
                    {
                      category: 'NHIF Contributions',
                      score: complianceMetrics.nhifCompliance,
                      status: complianceMetrics.nhifCompliance >= 95 ? 'Excellent' : 'Needs Attention',
                      recommendation: complianceMetrics.nhifCompliance >= 95
                        ? 'NHIF contributions are compliant with regulations.'
                        : 'Verify NHIF contribution bands and ensure accurate deductions.',
                    },
                    {
                      category: 'WCF Contributions',
                      score: complianceMetrics.wcfCompliance,
                      status: complianceMetrics.wcfCompliance >= 95 ? 'Excellent' : 'Needs Attention',
                      recommendation: complianceMetrics.wcfCompliance >= 95
                        ? 'WCF contributions are being handled properly.'
                        : 'Review WCF calculation methodology and payment timing.',
                    },
                  ].map((item, index) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h5 className="text-sm font-medium text-gray-900 dark:text-white">
                          {item.category}
                        </h5>
                        <div className="flex items-center space-x-2">
                          <span className={`text-sm font-medium ${getComplianceColor(item.score)}`}>
                            {item.score}%
                          </span>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            item.score >= 95
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                              : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                          }`}>
                            {item.status}
                          </span>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {item.recommendation}
                      </p>
                    </div>
                  ))}
                </div>
                <div className="mt-4 p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
                  <p className="text-sm text-indigo-800 dark:text-indigo-200">
                    <strong>Last Updated:</strong> {new Date(complianceMetrics.lastUpdated).toLocaleDateString('en-TZ', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </p>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
