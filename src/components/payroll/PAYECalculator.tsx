import { useState } from "react";
import {
  CalculatorIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { apiService } from "../../services/api";

interface PAYEResult {
  annualSalary: number;
  taxRelief: number;
  taxableIncome: number;
  totalTax: number;
  netSalary: number;
  monthlyGross: number;
  monthlyTax: number;
  monthlyNet: number;
  effectiveRate: number;
  bands: Array<{
    band: number;
    rate: number;
    amount: number;
    tax: number;
  }>;
}

export default function PAYECalculator() {
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();
  const [annualSalary, setAnnualSalary] = useState<string>("");
  const [result, setResult] = useState<PAYEResult | null>(null);
  const [loading, setLoading] = useState(false);

  const handleCalculate = async () => {
    if (!currentCompany || !annualSalary || parseFloat(annualSalary) <= 0) {
      addNotification("error", "Validation Error", "Please enter a valid annual salary");
      return;
    }

    setLoading(true);
    try {
      const data = await apiService.post<any>(`/payroll/${currentCompany.id}/paye/quick-calculate`, {
        annualSalary: parseFloat(annualSalary),
      });

      setResult(data.data);
      addNotification("success", "Success", "PAYE calculated successfully");
    } catch (error: any) {
      console.error("Error calculating PAYE:", error);
      const errorMessage = error.response?.data?.error || "Failed to calculate PAYE";
      addNotification("error", "Calculation Error", errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setAnnualSalary("");
    setResult(null);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-TZ", {
      style: "currency",
      currency: "TZS",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getBandColor = (band: number) => {
    const colors = [
      'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200',
      'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200',
      'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200',
      'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200',
      'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200'
    ];
    return colors[band - 1] || colors[0];
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <CalculatorIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            PAYE Calculator
          </h3>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
          <InformationCircleIcon className="h-4 w-4" />
          <span>Calculate Pay As You Earn tax for Tanzania</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <div className="space-y-6">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
              Salary Information
            </h4>

            {/* Annual Salary Input */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Annual Salary (TZS)
                </label>
                <input
                  type="number"
                  value={annualSalary}
                  onChange={(e) => setAnnualSalary(e.target.value)}
                  placeholder="Enter annual salary in TZS"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                  min="0"
                  step="1000"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Enter the gross annual salary before any deductions
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 mt-6">
              <button
                onClick={handleCalculate}
                disabled={loading || !annualSalary}
                className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                <CalculatorIcon className="h-4 w-4 mr-2" />
                {loading ? "Calculating..." : "Calculate PAYE"}
              </button>
              <button
                onClick={handleClear}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
              >
                Clear
              </button>
            </div>
          </div>

          {/* PAYE Information */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
              Tanzania PAYE Tax Bands (2024)
            </h4>
            <div className="space-y-2 text-sm text-blue-800 dark:text-blue-200">
              <div className="flex justify-between">
                <span>0 - 270,000 TZS:</span>
                <span className="font-medium">0%</span>
              </div>
              <div className="flex justify-between">
                <span>270,001 - 520,000 TZS:</span>
                <span className="font-medium">9%</span>
              </div>
              <div className="flex justify-between">
                <span>520,001 - 760,000 TZS:</span>
                <span className="font-medium">20%</span>
              </div>
              <div className="flex justify-between">
                <span>760,001 - 1,000,000 TZS:</span>
                <span className="font-medium">25%</span>
              </div>
              <div className="flex justify-between">
                <span>Above 1,000,000 TZS:</span>
                <span className="font-medium">30%</span>
              </div>
              <div className="mt-3 pt-2 border-t border-blue-300 dark:border-blue-700">
                <div className="flex justify-between">
                  <span>Personal Relief:</span>
                  <span className="font-medium">220,000 TZS annually</span>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Examples */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              Quick Examples
            </h4>
            <div className="space-y-2">
              <button
                onClick={() => setAnnualSalary("3600000")}
                className="w-full text-left px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                3.6M TZS - Entry level salary
              </button>
              <button
                onClick={() => setAnnualSalary("6000000")}
                className="w-full text-left px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                6M TZS - Mid-level salary
              </button>
              <button
                onClick={() => setAnnualSalary("12000000")}
                className="w-full text-left px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                12M TZS - Senior level salary
              </button>
              <button
                onClick={() => setAnnualSalary("24000000")}
                className="w-full text-left px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                24M TZS - Executive salary
              </button>
            </div>
          </div>
        </div>

        {/* Results Section */}
        <div className="space-y-6">
          {result ? (
            <div className="space-y-6">
              {/* Summary Card */}
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-medium text-green-900 dark:text-green-100">
                    PAYE Calculation Result
                  </h4>
                  <CheckCircleIcon className="h-8 w-8 text-green-500" />
                </div>

                <div className="space-y-4">
                  {/* Annual Summary */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg">
                      <p className="text-sm text-gray-600 dark:text-gray-400">Annual Gross</p>
                      <p className="text-lg font-bold text-gray-900 dark:text-white">
                        {formatCurrency(result.annualSalary)}
                      </p>
                    </div>
                    <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg">
                      <p className="text-sm text-gray-600 dark:text-gray-400">Annual Tax</p>
                      <p className="text-lg font-bold text-red-600 dark:text-red-400">
                        {formatCurrency(result.totalTax)}
                      </p>
                    </div>
                  </div>

                  {/* Monthly Summary */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                      <p className="text-sm text-blue-700 dark:text-blue-300">Monthly Gross</p>
                      <p className="text-lg font-bold text-blue-900 dark:text-blue-100">
                        {formatCurrency(result.monthlyGross)}
                      </p>
                    </div>
                    <div className="text-center p-3 bg-red-100 dark:bg-red-900/30 rounded-lg">
                      <p className="text-sm text-red-700 dark:text-red-300">Monthly PAYE</p>
                      <p className="text-lg font-bold text-red-900 dark:text-red-100">
                        {formatCurrency(result.monthlyTax)}
                      </p>
                    </div>
                    <div className="text-center p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                      <p className="text-sm text-green-700 dark:text-green-300">Monthly Net</p>
                      <p className="text-lg font-bold text-green-900 dark:text-green-100">
                        {formatCurrency(result.monthlyNet)}
                      </p>
                    </div>
                  </div>

                  {/* Tax Details */}
                  <div className="space-y-2 pt-4 border-t border-green-200 dark:border-green-700">
                    <div className="flex justify-between text-sm">
                      <span className="text-green-700 dark:text-green-300">Taxable Income:</span>
                      <span className="font-medium text-green-900 dark:text-green-100">
                        {formatCurrency(result.taxableIncome)}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-green-700 dark:text-green-300">Tax Relief:</span>
                      <span className="font-medium text-green-900 dark:text-green-100">
                        {formatCurrency(result.taxRelief)}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-green-700 dark:text-green-300">Effective Rate:</span>
                      <span className="font-medium text-green-900 dark:text-green-100">
                        {result.effectiveRate}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tax Bands Breakdown */}
              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Tax Bands Breakdown
                </h4>
                <div className="space-y-3">
                  {result.bands.map((band, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg ${getBandColor(band.band)}`}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">Band {band.band} ({band.rate}%)</p>
                          <p className="text-sm opacity-75">
                            Taxable: {formatCurrency(band.amount)}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold">{formatCurrency(band.tax)}</p>
                          <p className="text-sm opacity-75">tax</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Additional Information */}
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div className="flex items-start">
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2" />
                  <div>
                    <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                      Important Notes
                    </h5>
                    <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                      <li>• This calculation excludes other statutory deductions (NSSF, WCF, NHIF)</li>
                      <li>• PAYE must be deducted monthly and remitted to TRA by 15th of following month</li>
                      <li>• Personal relief of 220,000 TZS is applied annually</li>
                      <li>• Rates are based on current Tanzania tax legislation</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 text-center">
              <CalculatorIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Ready to Calculate
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Enter an annual salary to calculate PAYE tax using Tanzania tax bands.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
