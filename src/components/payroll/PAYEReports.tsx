import { useState, useEffect } from "react";
import {
  DocumentTextIcon,
  PlusIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  CalendarIcon,
  UserGroupIcon,
  DocumentArrowDownIcon,
  InformationCircleIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { apiService } from "../../services/api";

interface PAYEReport {
  id: string;
  reportType: 'monthly' | 'annual' | 'certificate' | 'summary';
  title: string;
  period: string;
  employeeId?: string;
  employeeName?: string;
  totalEmployees: number;
  totalGrossPay: number;
  totalPAYE: number;
  status: 'draft' | 'generated' | 'submitted';
  generatedDate: string;
  submittedDate?: string;
  fileUrl?: string;
}

interface ReportFilters {
  reportType: string;
  period: string;
  status: string;
  employeeId: string;
}

export default function PAYEReports() {
  const [loading, setLoading] = useState(false);
  const [reports, setReports] = useState<PAYEReport[]>([]);
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<ReportFilters>({
    reportType: '',
    period: '',
    status: '',
    employeeId: '',
  });
  const [newReport, setNewReport] = useState({
    reportType: 'monthly' as 'monthly' | 'annual' | 'certificate' | 'summary',
    period: '',
    employeeId: '',
  });

  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  useEffect(() => {
    if (currentCompany) {
      fetchReports();
    }
  }, [currentCompany]);

  const fetchReports = async () => {
    setLoading(true);
    try {
      // For now, use mock data since we don't have the backend endpoint yet
      const mockReports: PAYEReport[] = [
        {
          id: '1',
          reportType: 'monthly',
          title: 'Monthly PAYE Report - December 2024',
          period: '2024-12',
          totalEmployees: 25,
          totalGrossPay: 62500000,
          totalPAYE: 8750000,
          status: 'submitted',
          generatedDate: '2024-12-31T10:00:00Z',
          submittedDate: '2024-12-31T14:30:00Z',
          fileUrl: '/reports/paye-monthly-2024-12.pdf',
        },
        {
          id: '2',
          reportType: 'monthly',
          title: 'Monthly PAYE Report - November 2024',
          period: '2024-11',
          totalEmployees: 24,
          totalGrossPay: 60000000,
          totalPAYE: 8400000,
          status: 'generated',
          generatedDate: '2024-11-30T10:00:00Z',
          fileUrl: '/reports/paye-monthly-2024-11.pdf',
        },
        {
          id: '3',
          reportType: 'certificate',
          title: 'PAYE Certificate - John Doe',
          period: '2024',
          employeeId: '1',
          employeeName: 'John Doe',
          totalEmployees: 1,
          totalGrossPay: 36000000,
          totalPAYE: 5400000,
          status: 'generated',
          generatedDate: '2024-12-15T09:00:00Z',
          fileUrl: '/reports/paye-certificate-john-doe-2024.pdf',
        },
        {
          id: '4',
          reportType: 'annual',
          title: 'Annual PAYE Reconciliation - 2023',
          period: '2023',
          totalEmployees: 22,
          totalGrossPay: 660000000,
          totalPAYE: 92400000,
          status: 'submitted',
          generatedDate: '2024-01-15T10:00:00Z',
          submittedDate: '2024-01-20T16:00:00Z',
          fileUrl: '/reports/paye-annual-2023.pdf',
        },
        {
          id: '5',
          reportType: 'summary',
          title: 'PAYE Summary for TRA - Q4 2024',
          period: '2024-Q4',
          totalEmployees: 25,
          totalGrossPay: 187500000,
          totalPAYE: 26250000,
          status: 'draft',
          generatedDate: '2024-12-28T08:00:00Z',
        },
      ];

      setReports(mockReports);
      addNotification("success", "Success", "PAYE reports loaded successfully");
    } catch (error: any) {
      console.error("Error fetching PAYE reports:", error);
      addNotification("error", "Error", "Failed to load PAYE reports");
    } finally {
      setLoading(false);
    }
  };

  const generateReport = async () => {
    if (!newReport.reportType || !newReport.period) {
      addNotification("error", "Validation Error", "Please select report type and period");
      return;
    }

    if (newReport.reportType === 'certificate' && !newReport.employeeId) {
      addNotification("error", "Validation Error", "Please select an employee for certificate");
      return;
    }

    setLoading(true);
    try {
      // Mock report generation
      const reportId = Date.now().toString();
      const reportTitle = getReportTitle(newReport.reportType, newReport.period, newReport.employeeId);

      const mockReport: PAYEReport = {
        id: reportId,
        reportType: newReport.reportType,
        title: reportTitle,
        period: newReport.period,
        employeeId: newReport.employeeId || undefined,
        employeeName: newReport.employeeId ? 'John Doe' : undefined,
        totalEmployees: newReport.reportType === 'certificate' ? 1 : 25,
        totalGrossPay: newReport.reportType === 'certificate' ? 3000000 : 62500000,
        totalPAYE: newReport.reportType === 'certificate' ? 450000 : 8750000,
        status: 'generated',
        generatedDate: new Date().toISOString(),
        fileUrl: `/reports/${reportId}.pdf`,
      };

      setReports(prev => [mockReport, ...prev]);
      setShowGenerateModal(false);
      setNewReport({ reportType: 'monthly', period: '', employeeId: '' });
      addNotification("success", "Success", "PAYE report generated successfully");
    } catch (error: any) {
      console.error("Error generating PAYE report:", error);
      addNotification("error", "Error", "Failed to generate PAYE report");
    } finally {
      setLoading(false);
    }
  };

  const getReportTitle = (type: string, period: string, employeeId?: string) => {
    switch (type) {
      case 'monthly':
        return `Monthly PAYE Report - ${period}`;
      case 'annual':
        return `Annual PAYE Reconciliation - ${period}`;
      case 'certificate':
        return `PAYE Certificate - ${employeeId ? 'Employee' : 'Unknown'}`;
      case 'summary':
        return `PAYE Summary for TRA - ${period}`;
      default:
        return `PAYE Report - ${period}`;
    }
  };

  const downloadReport = async (report: PAYEReport) => {
    try {
      // Mock download functionality
      addNotification("success", "Download Started", `Downloading ${report.title}`);
      // In a real implementation, this would trigger a file download
      console.log(`Downloading report: ${report.fileUrl}`);
    } catch (error: any) {
      console.error("Error downloading report:", error);
      addNotification("error", "Download Error", "Failed to download report");
    }
  };

  const submitReport = async (report: PAYEReport) => {
    if (report.status === 'submitted') {
      addNotification("warning", "Already Submitted", "This report has already been submitted");
      return;
    }

    try {
      // Mock submission
      setReports(prev => prev.map(r =>
        r.id === report.id
          ? { ...r, status: 'submitted' as const, submittedDate: new Date().toISOString() }
          : r
      ));
      addNotification("success", "Success", "Report submitted to TRA successfully");
    } catch (error: any) {
      console.error("Error submitting report:", error);
      addNotification("error", "Submission Error", "Failed to submit report");
    }
  };

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.period.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (report.employeeName && report.employeeName.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesType = !filters.reportType || report.reportType === filters.reportType;
    const matchesPeriod = !filters.period || report.period.includes(filters.period);
    const matchesStatus = !filters.status || report.status === filters.status;
    const matchesEmployee = !filters.employeeId || report.employeeId === filters.employeeId;

    return matchesSearch && matchesType && matchesPeriod && matchesStatus && matchesEmployee;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'generated':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getReportTypeIcon = (type: string) => {
    switch (type) {
      case 'monthly':
        return <CalendarIcon className="h-4 w-4" />;
      case 'annual':
        return <DocumentTextIcon className="h-4 w-4" />;
      case 'certificate':
        return <UserGroupIcon className="h-4 w-4" />;
      case 'summary':
        return <DocumentArrowDownIcon className="h-4 w-4" />;
      default:
        return <DocumentTextIcon className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-TZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <DocumentTextIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            PAYE Reports
          </h3>
        </div>
        <button
          onClick={() => setShowGenerateModal(true)}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Generate Report
        </button>
      </div>

      {/* PAYE Reports Information */}
      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <InformationCircleIcon className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-1">
              PAYE Reports & Submissions - Tanzania
            </h4>
            <p className="text-sm text-green-800 dark:text-green-200">
              Generate monthly PAYE reports, employee certificates, and annual reconciliations for TRA compliance.
              All reports can be exported to PDF and Excel formats.
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search reports by title, period, or employee..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <select
              value={filters.reportType}
              onChange={(e) => setFilters(prev => ({ ...prev, reportType: e.target.value }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Types</option>
              <option value="monthly">Monthly</option>
              <option value="annual">Annual</option>
              <option value="certificate">Certificate</option>
              <option value="summary">Summary</option>
            </select>
            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Status</option>
              <option value="draft">Draft</option>
              <option value="generated">Generated</option>
              <option value="submitted">Submitted</option>
            </select>
          </div>
        </div>
      </div>

      {/* Report Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DocumentTextIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Reports</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{reports.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                <div className="h-3 w-3 bg-green-600 dark:bg-green-400 rounded-full"></div>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Submitted</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {reports.filter(r => r.status === 'submitted').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <div className="h-3 w-3 bg-blue-600 dark:bg-blue-400 rounded-full"></div>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Generated</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {reports.filter(r => r.status === 'generated').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">TZS</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total PAYE</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {formatCurrency(reports.reduce((sum, r) => sum + r.totalPAYE, 0))}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Reports Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white">
            PAYE Reports ({filteredReports.length})
          </h4>
        </div>

        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading reports...</p>
          </div>
        ) : filteredReports.length === 0 ? (
          <div className="p-8 text-center">
            <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm || Object.values(filters).some(f => f)
                ? 'No reports found matching your criteria.'
                : 'No PAYE reports found. Generate your first report to get started.'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Report
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Period
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Employees
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    PAYE Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Generated
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredReports.map((report) => (
                  <tr key={report.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 rounded-lg bg-green-100 dark:bg-green-900/20 flex items-center justify-center">
                            <div className="text-green-600 dark:text-green-400">
                              {getReportTypeIcon(report.reportType)}
                            </div>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {report.title}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {report.reportType.charAt(0).toUpperCase() + report.reportType.slice(1)} Report
                            {report.employeeName && ` • ${report.employeeName}`}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">{report.period}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">{report.totalEmployees}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {formatCurrency(report.totalPAYE)}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Gross: {formatCurrency(report.totalGrossPay)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(report.status)}`}>
                        {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                      </span>
                      {report.submittedDate && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Submitted: {formatDate(report.submittedDate)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(report.generatedDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => downloadReport(report)}
                          className="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300"
                          title="Download Report"
                        >
                          <ArrowDownTrayIcon className="h-4 w-4" />
                        </button>
                        {report.status !== 'submitted' && (
                          <button
                            onClick={() => submitReport(report)}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                            title="Submit to TRA"
                          >
                            <DocumentArrowDownIcon className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300"
                          title="View Report"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Generate Report Modal */}
      {showGenerateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Generate PAYE Report
              </h3>
              <button
                onClick={() => setShowGenerateModal(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Report Type *
                  </label>
                  <select
                    value={newReport.reportType}
                    onChange={(e) => setNewReport(prev => ({
                      ...prev,
                      reportType: e.target.value as 'monthly' | 'annual' | 'certificate' | 'summary',
                      employeeId: e.target.value === 'certificate' ? prev.employeeId : ''
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="monthly">Monthly PAYE Report</option>
                    <option value="annual">Annual PAYE Reconciliation</option>
                    <option value="certificate">Employee PAYE Certificate</option>
                    <option value="summary">PAYE Summary for TRA</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Period *
                  </label>
                  {newReport.reportType === 'monthly' ? (
                    <input
                      type="month"
                      value={newReport.period}
                      onChange={(e) => setNewReport(prev => ({ ...prev, period: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                    />
                  ) : newReport.reportType === 'annual' || newReport.reportType === 'certificate' ? (
                    <input
                      type="number"
                      placeholder="e.g., 2024"
                      value={newReport.period}
                      onChange={(e) => setNewReport(prev => ({ ...prev, period: e.target.value }))}
                      min="2020"
                      max="2030"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                    />
                  ) : (
                    <select
                      value={newReport.period}
                      onChange={(e) => setNewReport(prev => ({ ...prev, period: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Select Quarter</option>
                      <option value="2024-Q1">Q1 2024</option>
                      <option value="2024-Q2">Q2 2024</option>
                      <option value="2024-Q3">Q3 2024</option>
                      <option value="2024-Q4">Q4 2024</option>
                    </select>
                  )}
                </div>

                {newReport.reportType === 'certificate' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Employee *
                    </label>
                    <select
                      value={newReport.employeeId}
                      onChange={(e) => setNewReport(prev => ({ ...prev, employeeId: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Select Employee</option>
                      <option value="1">John Doe (EMP001)</option>
                      <option value="2">Jane Smith (EMP002)</option>
                      <option value="3">Michael Johnson (EMP003)</option>
                    </select>
                  </div>
                )}

                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                  <h5 className="text-sm font-medium text-green-900 dark:text-green-100 mb-2">
                    Report Information
                  </h5>
                  <div className="space-y-1 text-sm text-green-800 dark:text-green-200">
                    {newReport.reportType === 'monthly' && (
                      <>
                        <p>• Monthly PAYE calculations for all employees</p>
                        <p>• Detailed breakdown by employee</p>
                        <p>• Ready for TRA submission</p>
                      </>
                    )}
                    {newReport.reportType === 'annual' && (
                      <>
                        <p>• Annual PAYE reconciliation</p>
                        <p>• Year-end tax calculations</p>
                        <p>• Employee annual summaries</p>
                      </>
                    )}
                    {newReport.reportType === 'certificate' && (
                      <>
                        <p>• Individual employee PAYE certificate</p>
                        <p>• Annual tax deductions summary</p>
                        <p>• Official document for employee records</p>
                      </>
                    )}
                    {newReport.reportType === 'summary' && (
                      <>
                        <p>• Quarterly PAYE summary</p>
                        <p>• Consolidated report for TRA</p>
                        <p>• Tax authority submission format</p>
                      </>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-4 mt-6">
                <button
                  onClick={() => setShowGenerateModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                >
                  Cancel
                </button>
                <button
                  onClick={generateReport}
                  disabled={loading}
                  className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Generating...
                    </div>
                  ) : (
                    'Generate Report'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
