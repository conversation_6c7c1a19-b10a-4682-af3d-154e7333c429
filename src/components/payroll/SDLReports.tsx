import { useState, useEffect } from "react";
import {
  DocumentTextIcon,
  PlusIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  DocumentArrowDownIcon,
  InformationCircleIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { apiService } from "../../services/api";

interface SDLReport {
  id: string;
  reportType: 'monthly' | 'quarterly' | 'annual' | 'payment_tracking';
  title: string;
  period: string;
  totalEmployees: number;
  totalGrossPay: number;
  totalSDL: number;
  sdlRate: number;
  paymentStatus: 'pending' | 'paid' | 'overdue';
  status: 'draft' | 'generated' | 'submitted';
  generatedDate: string;
  submittedDate?: string;
  dueDate: string;
  paidDate?: string;
  fileUrl?: string;
}

interface ReportFilters {
  reportType: string;
  period: string;
  status: string;
  paymentStatus: string;
}

export default function SDLReports() {
  const [loading, setLoading] = useState(false);
  const [reports, setReports] = useState<SDLReport[]>([]);
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<ReportFilters>({
    reportType: '',
    period: '',
    status: '',
    paymentStatus: '',
  });
  const [newReport, setNewReport] = useState({
    reportType: 'monthly' as 'monthly' | 'quarterly' | 'annual' | 'payment_tracking',
    period: '',
  });

  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  useEffect(() => {
    if (currentCompany) {
      fetchReports();
    }
  }, [currentCompany]);

  const fetchReports = async () => {
    setLoading(true);
    try {
      // For now, use mock data since we don't have the backend endpoint yet
      const mockReports: SDLReport[] = [
        {
          id: '1',
          reportType: 'monthly',
          title: 'Monthly SDL Report - December 2024',
          period: '2024-12',
          totalEmployees: 25,
          totalGrossPay: 62500000,
          totalSDL: 3750000, // 6% of gross pay
          sdlRate: 6,
          paymentStatus: 'paid',
          status: 'submitted',
          generatedDate: '2024-12-31T10:00:00Z',
          submittedDate: '2024-12-31T14:30:00Z',
          dueDate: '2025-01-15T23:59:59Z',
          paidDate: '2025-01-10T16:00:00Z',
          fileUrl: '/reports/sdl-monthly-2024-12.pdf',
        },
        {
          id: '2',
          reportType: 'monthly',
          title: 'Monthly SDL Report - November 2024',
          period: '2024-11',
          totalEmployees: 24,
          totalGrossPay: 60000000,
          totalSDL: 3600000,
          sdlRate: 6,
          paymentStatus: 'paid',
          status: 'submitted',
          generatedDate: '2024-11-30T10:00:00Z',
          submittedDate: '2024-11-30T15:00:00Z',
          dueDate: '2024-12-15T23:59:59Z',
          paidDate: '2024-12-12T14:30:00Z',
          fileUrl: '/reports/sdl-monthly-2024-11.pdf',
        },
        {
          id: '3',
          reportType: 'quarterly',
          title: 'Quarterly SDL Report - Q4 2024',
          period: '2024-Q4',
          totalEmployees: 25,
          totalGrossPay: 187500000,
          totalSDL: 11250000,
          sdlRate: 6,
          paymentStatus: 'pending',
          status: 'generated',
          generatedDate: '2024-12-28T08:00:00Z',
          dueDate: '2025-01-31T23:59:59Z',
          fileUrl: '/reports/sdl-quarterly-2024-q4.pdf',
        },
        {
          id: '4',
          reportType: 'annual',
          title: 'Annual SDL Report - 2023',
          period: '2023',
          totalEmployees: 22,
          totalGrossPay: 660000000,
          totalSDL: 39600000,
          sdlRate: 6,
          paymentStatus: 'paid',
          status: 'submitted',
          generatedDate: '2024-01-15T10:00:00Z',
          submittedDate: '2024-01-20T16:00:00Z',
          dueDate: '2024-03-31T23:59:59Z',
          paidDate: '2024-03-15T10:00:00Z',
          fileUrl: '/reports/sdl-annual-2023.pdf',
        },
        {
          id: '5',
          reportType: 'payment_tracking',
          title: 'SDL Payment Tracking - January 2025',
          period: '2025-01',
          totalEmployees: 25,
          totalGrossPay: 62500000,
          totalSDL: 3750000,
          sdlRate: 6,
          paymentStatus: 'overdue',
          status: 'generated',
          generatedDate: '2025-01-31T10:00:00Z',
          dueDate: '2025-02-15T23:59:59Z',
          fileUrl: '/reports/sdl-payment-tracking-2025-01.pdf',
        },
      ];

      setReports(mockReports);
      addNotification("success", "Success", "SDL reports loaded successfully");
    } catch (error: any) {
      console.error("Error fetching SDL reports:", error);
      addNotification("error", "Error", "Failed to load SDL reports");
    } finally {
      setLoading(false);
    }
  };

  const generateReport = async () => {
    if (!newReport.reportType || !newReport.period) {
      addNotification("error", "Validation Error", "Please select report type and period");
      return;
    }

    setLoading(true);
    try {
      // Mock report generation
      const reportId = Date.now().toString();
      const reportTitle = getReportTitle(newReport.reportType, newReport.period);

      const mockReport: SDLReport = {
        id: reportId,
        reportType: newReport.reportType,
        title: reportTitle,
        period: newReport.period,
        totalEmployees: 25,
        totalGrossPay: 62500000,
        totalSDL: 3750000,
        sdlRate: 6,
        paymentStatus: 'pending',
        status: 'generated',
        generatedDate: new Date().toISOString(),
        dueDate: getDueDate(newReport.reportType, newReport.period),
        fileUrl: `/reports/${reportId}.pdf`,
      };

      setReports(prev => [mockReport, ...prev]);
      setShowGenerateModal(false);
      setNewReport({ reportType: 'monthly', period: '' });
      addNotification("success", "Success", "SDL report generated successfully");
    } catch (error: any) {
      console.error("Error generating SDL report:", error);
      addNotification("error", "Error", "Failed to generate SDL report");
    } finally {
      setLoading(false);
    }
  };

  const getReportTitle = (type: string, period: string) => {
    switch (type) {
      case 'monthly':
        return `Monthly SDL Report - ${period}`;
      case 'quarterly':
        return `Quarterly SDL Report - ${period}`;
      case 'annual':
        return `Annual SDL Report - ${period}`;
      case 'payment_tracking':
        return `SDL Payment Tracking - ${period}`;
      default:
        return `SDL Report - ${period}`;
    }
  };

  const getDueDate = (type: string, period: string) => {
    const now = new Date();
    switch (type) {
      case 'monthly':
        return new Date(now.getFullYear(), now.getMonth() + 1, 15).toISOString();
      case 'quarterly':
        return new Date(now.getFullYear(), now.getMonth() + 2, 31).toISOString();
      case 'annual':
        return new Date(now.getFullYear() + 1, 2, 31).toISOString();
      default:
        return new Date(now.getFullYear(), now.getMonth() + 1, 15).toISOString();
    }
  };

  const downloadReport = async (report: SDLReport) => {
    try {
      // Mock download functionality
      addNotification("success", "Download Started", `Downloading ${report.title}`);
      // In a real implementation, this would trigger a file download
      console.log(`Downloading report: ${report.fileUrl}`);
    } catch (error: any) {
      console.error("Error downloading report:", error);
      addNotification("error", "Download Error", "Failed to download report");
    }
  };

  const submitReport = async (report: SDLReport) => {
    if (report.status === 'submitted') {
      addNotification("warning", "Already Submitted", "This report has already been submitted");
      return;
    }

    try {
      // Mock submission
      setReports(prev => prev.map(r =>
        r.id === report.id
          ? { ...r, status: 'submitted' as const, submittedDate: new Date().toISOString() }
          : r
      ));
      addNotification("success", "Success", "SDL report submitted to TRA successfully");
    } catch (error: any) {
      console.error("Error submitting report:", error);
      addNotification("error", "Submission Error", "Failed to submit report");
    }
  };

  const markAsPaid = async (report: SDLReport) => {
    if (report.paymentStatus === 'paid') {
      addNotification("warning", "Already Paid", "This SDL payment has already been marked as paid");
      return;
    }

    try {
      // Mock payment marking
      setReports(prev => prev.map(r =>
        r.id === report.id
          ? { ...r, paymentStatus: 'paid' as const, paidDate: new Date().toISOString() }
          : r
      ));
      addNotification("success", "Success", "SDL payment marked as paid successfully");
    } catch (error: any) {
      console.error("Error marking payment:", error);
      addNotification("error", "Payment Error", "Failed to mark payment as paid");
    }
  };

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.period.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = !filters.reportType || report.reportType === filters.reportType;
    const matchesPeriod = !filters.period || report.period.includes(filters.period);
    const matchesStatus = !filters.status || report.status === filters.status;
    const matchesPaymentStatus = !filters.paymentStatus || report.paymentStatus === filters.paymentStatus;

    return matchesSearch && matchesType && matchesPeriod && matchesStatus && matchesPaymentStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'generated':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'overdue':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'pending':
        return <ClockIcon className="h-4 w-4" />;
      case 'overdue':
        return <ExclamationTriangleIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const getReportTypeIcon = (type: string) => {
    switch (type) {
      case 'monthly':
        return <CalendarIcon className="h-4 w-4" />;
      case 'quarterly':
        return <DocumentTextIcon className="h-4 w-4" />;
      case 'annual':
        return <DocumentArrowDownIcon className="h-4 w-4" />;
      case 'payment_tracking':
        return <CurrencyDollarIcon className="h-4 w-4" />;
      default:
        return <DocumentTextIcon className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-TZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const isOverdue = (dueDate: string, paymentStatus: string) => {
    return new Date(dueDate) < new Date() && paymentStatus !== 'paid';
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <DocumentTextIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            SDL Reports
          </h3>
        </div>
        <button
          onClick={() => setShowGenerateModal(true)}
          className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors duration-200"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Generate Report
        </button>
      </div>

      {/* SDL Reports Information */}
      <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <InformationCircleIcon className="h-5 w-5 text-purple-600 dark:text-purple-400 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-1">
              SDL Reports & Payment Tracking - Tanzania
            </h4>
            <p className="text-sm text-purple-800 dark:text-purple-200">
              Generate SDL reports, track payments, and manage compliance with Tanzania's Skills Development Levy (6% of gross payroll).
              All reports can be exported to PDF and Excel formats.
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search reports by title or period..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <select
              value={filters.reportType}
              onChange={(e) => setFilters(prev => ({ ...prev, reportType: e.target.value }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Types</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
              <option value="annual">Annual</option>
              <option value="payment_tracking">Payment Tracking</option>
            </select>
            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Status</option>
              <option value="draft">Draft</option>
              <option value="generated">Generated</option>
              <option value="submitted">Submitted</option>
            </select>
            <select
              value={filters.paymentStatus}
              onChange={(e) => setFilters(prev => ({ ...prev, paymentStatus: e.target.value }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Payments</option>
              <option value="pending">Pending</option>
              <option value="paid">Paid</option>
              <option value="overdue">Overdue</option>
            </select>
          </div>
        </div>
      </div>

      {/* Report Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DocumentTextIcon className="h-8 w-8 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Reports</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{reports.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                <CheckCircleIcon className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Paid</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {reports.filter(r => r.paymentStatus === 'paid').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-600 dark:text-red-400" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Overdue</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {reports.filter(r => r.paymentStatus === 'overdue' || isOverdue(r.dueDate, r.paymentStatus)).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">TZS</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total SDL</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {formatCurrency(reports.reduce((sum, r) => sum + r.totalSDL, 0))}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Reports Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white">
            SDL Reports ({filteredReports.length})
          </h4>
        </div>

        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading reports...</p>
          </div>
        ) : filteredReports.length === 0 ? (
          <div className="p-8 text-center">
            <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm || Object.values(filters).some(f => f)
                ? 'No reports found matching your criteria.'
                : 'No SDL reports found. Generate your first report to get started.'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Report
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Period
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Employees
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    SDL Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Payment Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Report Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Due Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredReports.map((report) => (
                  <tr key={report.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 rounded-lg bg-purple-100 dark:bg-purple-900/20 flex items-center justify-center">
                            <div className="text-purple-600 dark:text-purple-400">
                              {getReportTypeIcon(report.reportType)}
                            </div>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {report.title}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {report.reportType.charAt(0).toUpperCase() + report.reportType.slice(1).replace('_', ' ')} Report
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">{report.period}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">{report.totalEmployees}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {formatCurrency(report.totalSDL)}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {report.sdlRate}% of {formatCurrency(report.totalGrossPay)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getPaymentStatusColor(report.paymentStatus)}`}>
                          <div className="mr-1">
                            {getPaymentStatusIcon(report.paymentStatus)}
                          </div>
                          {report.paymentStatus.charAt(0).toUpperCase() + report.paymentStatus.slice(1)}
                        </span>
                      </div>
                      {report.paidDate && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Paid: {formatDate(report.paidDate)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(report.status)}`}>
                        {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                      </span>
                      {report.submittedDate && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Submitted: {formatDate(report.submittedDate)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm ${isOverdue(report.dueDate, report.paymentStatus) ? 'text-red-600 dark:text-red-400 font-medium' : 'text-gray-900 dark:text-white'}`}>
                        {formatDate(report.dueDate)}
                      </div>
                      {isOverdue(report.dueDate, report.paymentStatus) && (
                        <div className="text-xs text-red-500 dark:text-red-400">
                          Overdue
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => downloadReport(report)}
                          className="text-purple-600 dark:text-purple-400 hover:text-purple-900 dark:hover:text-purple-300"
                          title="Download Report"
                        >
                          <ArrowDownTrayIcon className="h-4 w-4" />
                        </button>
                        {report.status !== 'submitted' && (
                          <button
                            onClick={() => submitReport(report)}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                            title="Submit to TRA"
                          >
                            <DocumentArrowDownIcon className="h-4 w-4" />
                          </button>
                        )}
                        {report.paymentStatus !== 'paid' && (
                          <button
                            onClick={() => markAsPaid(report)}
                            className="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300"
                            title="Mark as Paid"
                          >
                            <CheckCircleIcon className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300"
                          title="View Report"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Generate Report Modal */}
      {showGenerateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Generate SDL Report
              </h3>
              <button
                onClick={() => setShowGenerateModal(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Report Type *
                  </label>
                  <select
                    value={newReport.reportType}
                    onChange={(e) => setNewReport(prev => ({
                      ...prev,
                      reportType: e.target.value as 'monthly' | 'quarterly' | 'annual' | 'payment_tracking'
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="monthly">Monthly SDL Report</option>
                    <option value="quarterly">Quarterly SDL Report</option>
                    <option value="annual">Annual SDL Report</option>
                    <option value="payment_tracking">SDL Payment Tracking</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Period *
                  </label>
                  {newReport.reportType === 'monthly' || newReport.reportType === 'payment_tracking' ? (
                    <input
                      type="month"
                      value={newReport.period}
                      onChange={(e) => setNewReport(prev => ({ ...prev, period: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
                    />
                  ) : newReport.reportType === 'annual' ? (
                    <input
                      type="number"
                      placeholder="e.g., 2024"
                      value={newReport.period}
                      onChange={(e) => setNewReport(prev => ({ ...prev, period: e.target.value }))}
                      min="2020"
                      max="2030"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
                    />
                  ) : (
                    <select
                      value={newReport.period}
                      onChange={(e) => setNewReport(prev => ({ ...prev, period: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Select Quarter</option>
                      <option value="2024-Q1">Q1 2024</option>
                      <option value="2024-Q2">Q2 2024</option>
                      <option value="2024-Q3">Q3 2024</option>
                      <option value="2024-Q4">Q4 2024</option>
                    </select>
                  )}
                </div>

                <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
                  <h5 className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-2">
                    Report Information
                  </h5>
                  <div className="space-y-1 text-sm text-purple-800 dark:text-purple-200">
                    {newReport.reportType === 'monthly' && (
                      <>
                        <p>• Monthly SDL calculations (6% of gross payroll)</p>
                        <p>• Employee payroll breakdown</p>
                        <p>• Ready for TRA submission</p>
                      </>
                    )}
                    {newReport.reportType === 'quarterly' && (
                      <>
                        <p>• Quarterly SDL summary report</p>
                        <p>• Consolidated payroll data</p>
                        <p>• Compliance tracking</p>
                      </>
                    )}
                    {newReport.reportType === 'annual' && (
                      <>
                        <p>• Annual SDL reconciliation</p>
                        <p>• Year-end compliance report</p>
                        <p>• Complete payroll summary</p>
                      </>
                    )}
                    {newReport.reportType === 'payment_tracking' && (
                      <>
                        <p>• SDL payment status tracking</p>
                        <p>• Due date monitoring</p>
                        <p>• Compliance dashboard</p>
                      </>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-4 mt-6">
                <button
                  onClick={() => setShowGenerateModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                >
                  Cancel
                </button>
                <button
                  onClick={generateReport}
                  disabled={loading}
                  className="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md shadow-sm hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Generating...
                    </div>
                  ) : (
                    'Generate Report'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
