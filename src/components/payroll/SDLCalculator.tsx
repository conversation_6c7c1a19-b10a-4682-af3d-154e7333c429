import { useState } from "react";
import {
  CalculatorIcon,
  ClipboardDocumentIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { apiService } from "../../services/api";

interface SDLCalculation {
  grossPayroll: number;
  sdlRate: number;
  sdlAmount: number;
  exemptAmount: number;
  netSDLAmount: number;
  employeeCount: number;
  averageSalary: number;
  calculationDate: string;
}

export default function SDLCalculator() {
  const [loading, setLoading] = useState(false);
  const [grossPayroll, setGrossPayroll] = useState("");
  const [employeeCount, setEmployeeCount] = useState("");
  const [exemptAmount, setExemptAmount] = useState("");
  const [result, setResult] = useState<SDLCalculation | null>(null);

  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  const calculateSDL = async () => {
    if (!grossPayroll || !employeeCount) {
      addNotification("error", "Validation Error", "Please enter gross payroll and employee count");
      return;
    }

    if (parseFloat(grossPayroll) <= 0 || parseInt(employeeCount) <= 0) {
      addNotification("error", "Validation Error", "Please enter valid positive amounts");
      return;
    }

    setLoading(true);
    try {
      const data = await apiService.post<any>(`/payroll/${currentCompany?.id}/sdl/calculate`, {
        taxYear: new Date().getFullYear(),
        taxMonth: new Date().getMonth() + 1,
        payrollData: {
          grossPayroll: parseFloat(grossPayroll),
          employeeCount: parseInt(employeeCount),
          exemptAmount: parseFloat(exemptAmount) || 0,
        }
      });

      setResult(data.data);
      addNotification("success", "Success", "SDL calculated successfully");
    } catch (error: any) {
      console.error("Error calculating SDL:", error);
      const errorMessage = error.response?.data?.error || "Failed to calculate SDL";
      addNotification("error", "Calculation Error", errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    addNotification("success", "Copied to clipboard", "Value copied to clipboard");
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <CalculatorIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            SDL Calculator
          </h3>
        </div>
      </div>

      {/* SDL Information */}
      <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <InformationCircleIcon className="h-5 w-5 text-purple-600 dark:text-purple-400 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-1">
              Skills Development Levy (SDL) - Tanzania
            </h4>
            <p className="text-sm text-purple-800 dark:text-purple-200">
              SDL is calculated at 6% of gross payroll for companies with annual payroll exceeding TZS 4 million.
              This levy supports vocational training and skills development programs in Tanzania.
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Form */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Calculate SDL
          </h4>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Monthly Gross Payroll (TZS)
              </label>
              <input
                type="number"
                value={grossPayroll}
                onChange={(e) => setGrossPayroll(e.target.value)}
                placeholder="e.g., 12000000"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Number of Employees
              </label>
              <input
                type="number"
                value={employeeCount}
                onChange={(e) => setEmployeeCount(e.target.value)}
                placeholder="e.g., 25"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Exempt Amount (Optional)
              </label>
              <input
                type="number"
                value={exemptAmount}
                onChange={(e) => setExemptAmount(e.target.value)}
                placeholder="e.g., 0"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Amount exempt from SDL calculation (if applicable)
              </p>
            </div>

            <button
              onClick={calculateSDL}
              disabled={loading}
              className="w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <CalculatorIcon className="h-4 w-4 mr-2" />
              )}
              Calculate SDL
            </button>
          </div>
        </div>

        {/* Results */}
        {result && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              SDL Calculation Results
            </h4>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Gross Payroll</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {(result.grossPayroll / 1000000).toFixed(1)}M TZS
                  </p>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  <p className="text-sm text-gray-600 dark:text-gray-400">SDL Rate</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {result.sdlRate}%
                  </p>
                </div>
              </div>

              <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-purple-600 dark:text-purple-400">SDL Amount</p>
                    <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                      {(result.netSDLAmount / 1000).toFixed(0)}K TZS
                    </p>
                  </div>
                  <button
                    onClick={() => copyToClipboard(result.netSDLAmount.toString())}
                    className="p-2 text-purple-600 dark:text-purple-400 hover:bg-purple-100 dark:hover:bg-purple-800 rounded-md transition-colors duration-200"
                  >
                    <ClipboardDocumentIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Employee Count</p>
                  <p className="font-medium text-gray-900 dark:text-white">{result.employeeCount}</p>
                </div>

                <div>
                  <p className="text-gray-600 dark:text-gray-400">Average Salary</p>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {(result.averageSalary / 1000).toFixed(0)}K TZS
                  </p>
                </div>
              </div>

              {result.exemptAmount > 0 && (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg border border-yellow-200 dark:border-yellow-800">
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    Exempt Amount: {(result.exemptAmount / 1000).toFixed(0)}K TZS
                  </p>
                </div>
              )}

              <div className="text-xs text-gray-500 dark:text-gray-400">
                Calculated on: {new Date(result.calculationDate).toLocaleDateString()}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
