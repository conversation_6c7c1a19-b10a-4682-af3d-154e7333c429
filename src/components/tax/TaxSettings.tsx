import { useState, useEffect } from "react";
import { CogIcon } from "@heroicons/react/24/outline";
import type {
  TaxSettings,
  TaxRate,
  TaxCalculationMethod,
  TaxRoundingMethod,
} from "../../types/tax";
import { taxService } from "../../services/taxService";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";

interface TaxSettingsProps {
  taxRates: TaxRate[];
}

const CALCULATION_METHODS: {
  value: TaxCalculationMethod;
  label: string;
  description: string;
}[] = [
  {
    value: "exclusive",
    label: "Tax Exclusive",
    description: "Tax is calculated on top of the base amount",
  },
  {
    value: "inclusive",
    label: "Tax Inclusive",
    description: "Tax is included in the total amount",
  },
  {
    value: "compound",
    label: "Compound Tax",
    description: "Multiple taxes are compounded on each other",
  },
];

const ROUNDING_METHODS: {
  value: TaxRoundingMethod;
  label: string;
  description: string;
}[] = [
  {
    value: "round",
    label: "Standard Rounding",
    description: "Round to nearest decimal place",
  },
  {
    value: "round_up",
    label: "Round Up",
    description: "Always round up",
  },
  {
    value: "round_down",
    label: "Round Down",
    description: "Always round down",
  },
  {
    value: "truncate",
    label: "Truncate",
    description: "Cut off decimal places",
  },
];

export default function TaxSettings({ taxRates }: TaxSettingsProps) {
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  const [settings, setSettings] = useState<TaxSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (currentCompany) {
      loadSettings();
    }
  }, [currentCompany]);

  const loadSettings = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      const data = await taxService.getTaxSettings(currentCompany.id);
      setSettings(data);
    } catch (error) {
      console.error("Failed to load tax settings:", error);
      showNotification({
        type: "error",
        title: "Error",
        message: "Failed to load tax settings"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!currentCompany || !settings) return;

    try {
      setSaving(true);
      const updatedSettings = await taxService.updateTaxSettings(
        currentCompany.id,
        settings
      );
      setSettings(updatedSettings);
      showNotification({
        type: "success",
        title: "Success",
        message: "Tax settings saved successfully"
      });
    } catch (error) {
      console.error("Failed to save tax settings:", error);
      showNotification({
        type: "error",
        title: "Error",
        message: "Failed to save tax settings"
      });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof TaxSettings, value: any) => {
    if (!settings) return;
    setSettings((prev) => (prev ? { ...prev, [field]: value } : null));
  };

  const activeTaxRates = taxRates.filter((rate) => rate.isActive);

  if (loading) {
    return (
      <div className="text-center bg-white dark:bg-gray-800 py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Loading tax settings...</p>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="text-center bg-white dark:bg-gray-800 py-12">
        <CogIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          No settings found
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
          Failed to load tax settings.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Tax Settings</h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
          Configure global tax calculation and compliance settings
        </p>
      </div>

      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 space-y-6">
        {/* Default Tax Rate */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Default Tax Rate
          </label>
          <select
            value={settings.defaultTaxRateId || ""}
            onChange={(e) =>
              handleInputChange("defaultTaxRateId", e.target.value || undefined)
            }
            className="w-full max-w-md px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">No default tax rate</option>
            {activeTaxRates.map((rate) => (
              <option key={rate.id} value={rate.id}>
                {rate.name} ({typeof rate.rate === 'string' ? parseFloat(rate.rate).toFixed(3) : rate.rate.toFixed(3)}%) - {rate.jurisdiction}
              </option>
            ))}
          </select>
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            This tax rate will be used as the default for new transactions
          </p>
        </div>

        {/* Tax Calculation Method */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Tax Calculation Method
          </label>
          <div className="space-y-2">
            {CALCULATION_METHODS.map((method) => (
              <div key={method.value} className="flex items-start">
                <input
                  type="radio"
                  id={`calc-${method.value}`}
                  name="calculationMethod"
                  value={method.value}
                  checked={settings.taxCalculationMethod === method.value}
                  onChange={(e) =>
                    handleInputChange(
                      "taxCalculationMethod",
                      e.target.value as TaxCalculationMethod
                    )
                  }
                  className="mt-1 h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600"
                />
                <div className="ml-3">
                  <label
                    htmlFor={`calc-${method.value}`}
                    className="text-sm font-medium text-gray-900 dark:text-white cursor-pointer"
                  >
                    {method.label}
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{method.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Rounding Settings */}
        <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Rounding Method
            </label>
            <select
              value={settings.roundingMethod}
              onChange={(e) =>
                handleInputChange(
                  "roundingMethod",
                  e.target.value as TaxRoundingMethod
                )
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {ROUNDING_METHODS.map((method) => (
                <option key={method.value} value={method.value}>
                  {method.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Rounding Precision (Decimal Places)
            </label>
            <input
              type="number"
              min="0"
              max="6"
              value={settings.roundingPrecision}
              onChange={(e) =>
                handleInputChange("roundingPrecision", parseInt(e.target.value))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Tax Behavior Settings */}
        <div className="space-y-4 bg-white dark:bg-gray-800">
          <h4 className="text-md font-medium text-gray-900 dark:text-white">Tax Behavior</h4>

          <div className="space-y-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="includeTaxInPrice"
                checked={settings.includeTaxInPrice}
                onChange={(e) =>
                  handleInputChange("includeTaxInPrice", e.target.checked)
                }
                className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
              <label
                htmlFor="includeTaxInPrice"
                className="ml-2 block text-sm text-gray-900 dark:text-white"
              >
                Include tax in displayed prices
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="autoCalculateTax"
                checked={settings.autoCalculateTax}
                onChange={(e) =>
                  handleInputChange("autoCalculateTax", e.target.checked)
                }
                className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
              <label
                htmlFor="autoCalculateTax"
                className="ml-2 block text-sm text-gray-900 dark:text-white"
              >
                Automatically calculate tax on transactions
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="enableTaxExemptions"
                checked={settings.enableTaxExemptions}
                onChange={(e) =>
                  handleInputChange("enableTaxExemptions", e.target.checked)
                }
                className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
              <label
                htmlFor="enableTaxExemptions"
                className="ml-2 block text-sm text-gray-900 dark:text-white"
              >
                Enable tax exemptions
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="requireTaxCertificates"
                checked={settings.requireTaxCertificates}
                onChange={(e) =>
                  handleInputChange("requireTaxCertificates", e.target.checked)
                }
                className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
              <label
                htmlFor="requireTaxCertificates"
                className="ml-2 block text-sm text-gray-900 dark:text-white"
              >
                Require tax exemption certificates
              </label>
            </div>
          </div>
        </div>

        {/* Tax Reporting Currency */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Tax Reporting Currency
          </label>
          <select
            value={settings.taxReportingCurrency}
            onChange={(e) =>
              handleInputChange("taxReportingCurrency", e.target.value)
            }
            className="w-full max-w-xs px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="USD">USD - US Dollar</option>
            <option value="EUR">EUR - Euro</option>
            <option value="GBP">GBP - British Pound</option>
            <option value="CAD">CAD - Canadian Dollar</option>
            <option value="AUD">AUD - Australian Dollar</option>
          </select>
        </div>

        {/* Save Button */}
        <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleSave}
            disabled={saving}
            className="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 cursor-pointer"
          >
            {saving ? "Saving..." : "Save Settings"}
          </button>
        </div>
      </div>
    </div>
  );
}
