import { useState, useEffect } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import type { TaxRate, TaxType, CreateTaxRateRequest, UpdateTaxRateRequest } from "../../types/tax";
import { useCompany } from "../../contexts/CompanyContext";

interface TaxRateFormProps {
  taxRate?: TaxRate | null;
  onSave: (data: CreateTaxRateRequest | UpdateTaxRateRequest) => Promise<void>;
  onCancel: () => void;
}

const TAX_TYPES: { value: TaxType; label: string }[] = [
  { value: "sales_tax", label: "Sales Tax" },
  { value: "vat", label: "VAT" },
  { value: "gst", label: "GST" },
  { value: "income_tax", label: "Income Tax" },
  { value: "property_tax", label: "Property Tax" },
  { value: "excise_tax", label: "Excise Tax" },
  { value: "customs_duty", label: "Customs Duty" },
  { value: "other", label: "Other" },
];

export default function TaxRateForm({ taxRate, onSave, onCancel }: TaxRateFormProps) {
  const { currentCompany } = useCompany();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    rate: "",
    type: "sales_tax" as TaxType,
    jurisdiction: "",
    jurisdictionCode: "",
    isActive: true,
    isDefault: false,
    effectiveDate: new Date().toISOString().split('T')[0],
    expiryDate: "",
    applicableToProducts: true,
    applicableToServices: true,
  });

  useEffect(() => {
    if (taxRate) {
      setFormData({
        name: taxRate.name,
        description: taxRate.description || "",
        rate: taxRate.rate.toString(),
        type: taxRate.type,
        jurisdiction: taxRate.jurisdiction,
        jurisdictionCode: taxRate.jurisdictionCode || "",
        isActive: taxRate.isActive,
        isDefault: taxRate.isDefault,
        effectiveDate: taxRate.effectiveDate,
        expiryDate: taxRate.expiryDate || "",
        applicableToProducts: taxRate.applicableToProducts,
        applicableToServices: taxRate.applicableToServices,
      });
    }
  }, [taxRate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentCompany) return;

    setLoading(true);
    try {
      const data = {
        companyId: currentCompany.id,
        name: formData.name,
        description: formData.description || undefined,
        rate: parseFloat(formData.rate),
        type: formData.type,
        jurisdiction: formData.jurisdiction,
        jurisdictionCode: formData.jurisdictionCode || undefined,
        isActive: formData.isActive,
        isDefault: formData.isDefault,
        effectiveDate: formData.effectiveDate,
        expiryDate: formData.expiryDate || undefined,
        applicableToProducts: formData.applicableToProducts,
        applicableToServices: formData.applicableToServices,
      };

      if (taxRate) {
        await onSave({ id: taxRate.id, ...data } as UpdateTaxRateRequest);
      } else {
        await onSave(data as CreateTaxRateRequest);
      }
    } catch (error) {
      console.error("Failed to save tax rate:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {taxRate ? "Edit Tax Rate" : "Create Tax Rate"}
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-pointer"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Tax Rate (%) *
              </label>
              <input
                type="number"
                step="0.001"
                min="0"
                max="100"
                value={formData.rate}
                onChange={(e) => handleInputChange("rate", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Tax Type *
              </label>
              <select
                value={formData.type}
                onChange={(e) => handleInputChange("type", e.target.value as TaxType)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                {TAX_TYPES.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Jurisdiction *
              </label>
              <input
                type="text"
                value={formData.jurisdiction}
                onChange={(e) => handleInputChange("jurisdiction", e.target.value)}
                placeholder="e.g., Federal, California, Los Angeles County"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Jurisdiction Code
              </label>
              <input
                type="text"
                value={formData.jurisdictionCode}
                onChange={(e) => handleInputChange("jurisdictionCode", e.target.value)}
                placeholder="e.g., US, CA, LAC"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Effective Date *
              </label>
              <input
                type="date"
                value={formData.effectiveDate}
                onChange={(e) => handleInputChange("effectiveDate", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Expiry Date
              </label>
              <input
                type="date"
                value={formData.expiryDate}
                onChange={(e) => handleInputChange("expiryDate", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => handleInputChange("isActive", e.target.checked)}
                  className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                />
                <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900 dark:text-white">
                  Active
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isDefault"
                  checked={formData.isDefault}
                  onChange={(e) => handleInputChange("isDefault", e.target.checked)}
                  className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                />
                <label htmlFor="isDefault" className="ml-2 block text-sm text-gray-900 dark:text-white">
                  Default Tax Rate
                </label>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="applicableToProducts"
                  checked={formData.applicableToProducts}
                  onChange={(e) => handleInputChange("applicableToProducts", e.target.checked)}
                  className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                />
                <label htmlFor="applicableToProducts" className="ml-2 block text-sm text-gray-900 dark:text-white">
                  Applicable to Products
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="applicableToServices"
                  checked={formData.applicableToServices}
                  onChange={(e) => handleInputChange("applicableToServices", e.target.checked)}
                  className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                />
                <label htmlFor="applicableToServices" className="ml-2 block text-sm text-gray-900 dark:text-white">
                  Applicable to Services
                </label>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 cursor-pointer"
            >
              {loading ? "Saving..." : taxRate ? "Update" : "Create"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
