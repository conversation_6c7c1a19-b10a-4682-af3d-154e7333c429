import { useState } from "react";
import { PlusIcon, PencilIcon, TrashIcon } from "@heroicons/react/24/outline";
import type {
  TaxCategory,
  TaxRate,
  CreateTaxCategoryRequest,
  UpdateTaxCategoryRequest,
} from "../../types/tax";
import { useConfirmDialog } from "../../hooks/useConfirmDialog";
import ConfirmDialog from "../ui/ConfirmDialog";
import TaxCategoryForm from "./TaxCategoryForm";

interface TaxCategoryManagementProps {
  categories: TaxCategory[];
  taxRates: TaxRate[];
  onUpdate: () => void;
}

export default function TaxCategoryManagement({
  categories,
  taxRates,
  onUpdate,
}: TaxCategoryManagementProps) {
  const [showForm, setShowForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<TaxCategory | null>(
    null
  );
  const [searchTerm, setSearchTerm] = useState("");
  const { dialogProps, showConfirmDialog } = useConfirmDialog();

  const filteredCategories = categories.filter(
    (category) =>
      category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (category.description &&
        category.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleCreate = () => {
    setEditingCategory(null);
    setShowForm(true);
  };

  const handleEdit = (category: TaxCategory) => {
    setEditingCategory(category);
    setShowForm(true);
  };

  const handleDelete = (category: TaxCategory) => {
    showConfirmDialog({
      title: "Delete Tax Category",
      message: `Are you sure you want to delete the "${category.name}" tax category? This action cannot be undone.`,
      confirmLabel: "Delete",
      type: "danger",
      onConfirm: async () => {
        try {
          // Delete logic would be implemented here
          console.log("Deleting category:", category.id);
          onUpdate();
        } catch (error) {
          console.error("Failed to delete tax category:", error);
        }
      },
    });
  };

  const handleFormSave = async (
    data: CreateTaxCategoryRequest | UpdateTaxCategoryRequest
  ) => {
    try {
      // Save logic would be implemented here
      console.log("Saving category:", data);
      setShowForm(false);
      setEditingCategory(null);
      onUpdate();
    } catch (error) {
      console.error("Failed to save tax category:", error);
    }
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setEditingCategory(null);
  };

  const getTaxRateName = (taxRateId: string | undefined) => {
    if (!taxRateId) return "None";
    const taxRate = taxRates.find((rate) => rate.id === taxRateId);
    return taxRate ? `${taxRate.name} (${taxRate.rate}%)` : "Unknown";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Tax Categories</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Configure product and service tax categories with default tax rates
          </p>
        </div>
        <button
          onClick={handleCreate}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Category
        </button>
      </div>

      {/* Search */}
      <div className="max-w-md">
        <input
          type="text"
          placeholder="Search categories..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Categories List */}
      <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
        {filteredCategories.length === 0 ? (
          <div className="text-center bg-white dark:bg-gray-800 py-12">
            <p className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
              {searchTerm
                ? "No categories match your search."
                : "No tax categories found."}
            </p>
            {!searchTerm && (
              <button
                onClick={handleCreate}
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create your first tax category
              </button>
            )}
          </div>
        ) : (
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredCategories.map((category) => (
              <li key={category.id} className="hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 group">
                <div className="px-6 py-4 flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {category.name}
                          </p>
                          {!category.isActive && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/20 text-red-800">
                              Inactive
                            </span>
                          )}
                        </div>
                        {category.description && (
                          <p className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">
                            {category.description}
                          </p>
                        )}
                        <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <span>
                            Default Tax Rate:{" "}
                            {getTaxRateName(category.defaultTaxRateId)}
                          </span>
                          <span>
                            Created:{" "}
                            {new Date(category.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={() => handleEdit(category)}
                      className="p-2 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:text-blue-400 cursor-pointer"
                      title="Edit category"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(category)}
                      className="p-2 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-red-600 dark:text-red-400 cursor-pointer"
                      title="Delete category"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Form Modal */}
      {showForm && (
        <TaxCategoryForm
          category={editingCategory}
          taxRates={taxRates}
          onSave={handleFormSave}
          onCancel={handleFormCancel}
        />
      )}

      {/* Confirm Dialog */}
      <ConfirmDialog {...dialogProps} />
    </div>
  );
}
