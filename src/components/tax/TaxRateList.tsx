import { useState } from "react";
import {
  PencilIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  XCircleIcon,
  StarIcon,
} from "@heroicons/react/24/outline";
import type { TaxRate } from "../../types/tax";
import { useConfirmDialog } from "../../hooks/useConfirmDialog";
import ConfirmDialog from "../ui/ConfirmDialog";

interface TaxRateListProps {
  taxRates: TaxRate[];
  onEdit: (taxRate: TaxRate) => void;
  onDelete: (taxRateId: string) => void;
}

export default function TaxRateList({
  taxRates,
  onEdit,
  onDelete,
}: TaxRateListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<
    "all" | "active" | "inactive"
  >("all");
  const { dialogProps, showConfirmDialog } = useConfirmDialog();

  const filteredTaxRates = (taxRates || []).filter((rate) => {
    const matchesSearch =
      rate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rate.jurisdiction.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rate.type.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = typeFilter === "all" || rate.type === typeFilter;

    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "active" && rate.isActive) ||
      (statusFilter === "inactive" && !rate.isActive);

    return matchesSearch && matchesType && matchesStatus;
  });

  const handleDeleteClick = (taxRate: TaxRate) => {
    showConfirmDialog({
      title: "Delete Tax Rate",
      message: `Are you sure you want to delete the "${taxRate.name}" tax rate? This action cannot be undone.`,
      confirmLabel: "Delete",
      type: "danger",
      onConfirm: () => onDelete(taxRate.id),
    });
  };

  const formatRate = (rate: number | string) => {
    const numericRate = typeof rate === 'string' ? parseFloat(rate) : rate;
    return `${numericRate.toFixed(3)}%`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getTaxTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      sales_tax: "Sales Tax",
      vat: "VAT",
      gst: "GST",
      income_tax: "Income Tax",
      property_tax: "Property Tax",
      excise_tax: "Excise Tax",
      customs_duty: "Customs Duty",
      other: "Other",
    };
    return labels[type] || type;
  };

  const uniqueTypes = [...new Set((taxRates || []).map((rate) => rate.type))];

  return (
    <div className="space-y-4 bg-white dark:bg-gray-800">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
          </div>
          <input
            type="text"
            placeholder="Search tax rates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 placeholder-gray-500 dark:placeholder-gray-400 dark:placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 dark:placeholder-gray-500 dark:placeholder-gray-400 dark:placeholder-gray-500 dark:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <select
          value={typeFilter}
          onChange={(e) => setTypeFilter(e.target.value)}
          className="block w-full sm:w-auto px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 cursor-pointer"
        >
          <option value="all">All Types</option>
          {uniqueTypes.map((type) => (
            <option key={type} value={type}>
              {getTaxTypeLabel(type)}
            </option>
          ))}
        </select>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value as any)}
          className="block w-full sm:w-auto px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 cursor-pointer"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>

      {/* Tax Rates Table */}
      <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {filteredTaxRates.length === 0 ? (
            <li className="px-6 py-8 text-center">
              <div className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                No tax rates found
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                {searchTerm
                  ? "Try adjusting your search criteria."
                  : "Get started by adding a new tax rate."}
              </p>
            </li>
          ) : (
            filteredTaxRates.map((taxRate) => (
              <li key={taxRate.id} className="hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 group">
                <div className="px-6 py-4 flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {taxRate.name}
                        </p>
                        {taxRate.isDefault && (
                          <StarIcon
                            className="h-4 w-4 text-yellow-500 dark:text-yellow-400"
                            title="Default tax rate"
                          />
                        )}
                        {taxRate.isActive ? (
                          <CheckCircleIcon className="h-4 w-4 text-green-500 dark:text-green-400" />
                        ) : (
                          <XCircleIcon className="h-4 w-4 text-red-500 dark:text-red-400" />
                        )}
                      </div>
                      <div className="flex items-center space-x-4 mt-1">
                        <span className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                          {formatRate(taxRate.rate)}
                        </span>
                        <span className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          {getTaxTypeLabel(taxRate.type)}
                        </span>
                        <span className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          {taxRate.jurisdiction}
                        </span>
                      </div>
                      {taxRate.description && (
                        <p className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">
                          {taxRate.description}
                        </p>
                      )}
                      <div className="flex items-center space-x-4 mt-1 text-xs text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        <span>
                          Effective: {formatDate(taxRate.effectiveDate)}
                        </span>
                        {taxRate.expiryDate && (
                          <span>Expires: {formatDate(taxRate.expiryDate)}</span>
                        )}
                        <span>
                          Applies to:{" "}
                          {[
                            taxRate.applicableToProducts && "Products",
                            taxRate.applicableToServices && "Services",
                          ]
                            .filter(Boolean)
                            .join(", ") || "None"}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={() => onEdit(taxRate)}
                      className="p-2 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:text-blue-400 rounded-full hover:bg-blue-50 dark:bg-blue-900/20 cursor-pointer"
                      title="Edit tax rate"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteClick(taxRate)}
                      className="p-2 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-red-600 dark:text-red-400 rounded-full hover:bg-red-50 dark:bg-red-900/20 cursor-pointer"
                      title="Delete tax rate"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </li>
            ))
          )}
        </ul>
      </div>

      <ConfirmDialog {...dialogProps} />
    </div>
  );
}
