import { useState, useEffect } from "react";
import {
  DocumentTextIcon,
  CalendarIcon,
  ArrowDownTrayIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import type { TaxReport } from "../../types/tax";
import { taxService } from "../../services/taxService";
import { useNotification } from "../../contexts/NotificationContext";

interface TaxReportsProps {
  companyId: string;
}

interface LocalTaxReportFilters {
  reportType?: string;
  periodType?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
  jurisdiction?: string;
}

const REPORT_TYPES = [
  { value: "sales_tax_return", label: "Sales Tax Report" },
  { value: "vat_return", label: "VAT Return" },
  { value: "gst_return", label: "GST Return" },
  { value: "tax_summary", label: "Tax Summary" },
];

const PERIOD_TYPES = [
  { value: "MONTHLY", label: "Monthly" },
  { value: "QUARTERLY", label: "Quarterly" },
  { value: "YEARLY", label: "Yearly" },
];

export default function TaxReports({ companyId }: TaxReportsProps) {
  const { showNotification } = useNotification();
  const [reports, setReports] = useState<TaxReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [filters, setFilters] = useState<LocalTaxReportFilters>({
    reportType: "sales_tax_return",
    periodType: "MONTHLY",
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
  });

  useEffect(() => {
    loadReports();
  }, [companyId]);

  const loadReports = async () => {
    try {
      setLoading(true);
      const data = await taxService.getTaxReports(companyId, filters as any);
      setReports(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("Failed to load tax reports:", error);
      showNotification({
        type: "error",
        title: "Error",
        message: "Failed to load tax reports"
      });
      setReports([]); // Ensure reports is always an array
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateReport = async () => {
    try {
      setGenerating(true);
      const report = await taxService.generateTaxReport(companyId, filters as any);
      setReports((prev) => [report, ...prev]);
      showNotification({
        type: "success",
        title: "Success",
        message: "Tax report generated successfully"
      });
    } catch (error) {
      console.error("Failed to generate tax report:", error);
      showNotification({
        type: "error",
        title: "Error",
        message: "Failed to generate tax report"
      });
    } finally {
      setGenerating(false);
    }
  };

  const handleDownloadReport = async (reportId: string) => {
    try {
      await taxService.downloadTaxReport(reportId);
      showNotification({
        type: "success",
        title: "Success",
        message: "Report downloaded successfully"
      });
    } catch (error) {
      console.error("Failed to download report:", error);
      showNotification({
        type: "error",
        title: "Error",
        message: "Failed to download report"
      });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200";
      case "GENERATED":
        return "bg-blue-100 dark:bg-blue-900/20 text-blue-800";
      case "FILED":
        return "bg-green-100 dark:bg-green-900/20 text-green-800";
      case "PAID":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Tax Reports</h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
          Generate and manage tax compliance reports
        </p>
      </div>

      {/* Report Generation Form */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
          Generate New Report
        </h4>

        <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Report Type
            </label>
            <select
              value={filters.reportType}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, reportType: e.target.value as any }))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {REPORT_TYPES.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Period Type
            </label>
            <select
              value={filters.periodType}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, periodType: e.target.value }))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {PERIOD_TYPES.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Start Date
            </label>
            <input
              type="date"
              value={filters.startDate}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, startDate: e.target.value }))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              End Date
            </label>
            <input
              type="date"
              value={filters.endDate}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, endDate: e.target.value }))
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <button
          onClick={handleGenerateReport}
          disabled={generating}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 cursor-pointer"
        >
          <ChartBarIcon className="h-4 w-4 mr-2" />
          {generating ? "Generating..." : "Generate Report"}
        </button>
      </div>

      {/* Reports List */}
      <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
          <h4 className="text-md font-medium text-gray-900 dark:text-white">Recent Reports</h4>
        </div>

        {loading ? (
          <div className="text-center bg-white dark:bg-gray-800 py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Loading reports...</p>
          </div>
        ) : !reports || reports.length === 0 ? (
          <div className="text-center bg-white dark:bg-gray-800 py-12">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              No reports
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
              Generate your first tax report to get started.
            </p>
          </div>
        ) : (
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {reports.map((report) => (
              <li key={report.id} className="hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700">
                <div className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3">
                        <DocumentTextIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {REPORT_TYPES.find(
                              (t) => t.value === report.reportType
                            )?.label || report.reportType}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                            {formatDate(report.periodStart)} -{" "}
                            {formatDate(report.periodEnd)}
                          </p>
                        </div>
                      </div>

                      <div className="mt-2 grid bg-white dark:bg-gray-800 grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Total Sales:</span>
                          <span className="ml-1 font-medium">
                            {formatCurrency(report.totalSales)}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Taxable Sales:</span>
                          <span className="ml-1 font-medium">
                            {formatCurrency((report as any).taxableSales || 0)}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Tax Collected:</span>
                          <span className="ml-1 font-medium">
                            {formatCurrency((report as any).taxCollected || 0)}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Tax Due:</span>
                          <span className="ml-1 font-medium">
                            {formatCurrency((report as any).taxDue || 0)}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                          report.status
                        )}`}
                      >
                        {report.status}
                      </span>

                      {(report as any).dueDate &&
                        new Date((report as any).dueDate) < new Date() &&
                        report.status !== "filed" && (
                          <ExclamationTriangleIcon
                            className="h-5 w-5 text-red-500 dark:text-red-400"
                            title="Overdue"
                          />
                        )}

                      <button
                        onClick={() => handleDownloadReport(report.id)}
                        className="p-2 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:text-blue-400 cursor-pointer"
                        title="Download report"
                      >
                        <ArrowDownTrayIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  {(report as any).dueDate && (
                    <div className="mt-2 flex items-center text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      <CalendarIcon className="h-4 w-4 mr-1" />
                      Due: {formatDate((report as any).dueDate)}
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
