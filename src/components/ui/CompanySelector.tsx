import { Fragment } from "react";
import { Listbox, Transition } from "@headlessui/react";
import {
  CheckIcon,
  ChevronUpDownIcon,
  BuildingOfficeIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
// import type { Company } from "../../types";

export default function CompanySelector() {
  const { currentCompany, companies, setCurrentCompany, isLoading } =
    useCompany();

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2">
        <div className="animate-pulse bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 h-4 w-32 rounded-md"></div>
      </div>
    );
  }

  if (companies.length <= 1) {
    return (
      <div className="flex items-center space-x-2">
        <span className="text-sm font-semibold text-gray-900 dark:text-white truncate">
          {currentCompany?.name || "No Company"}
        </span>
      </div>
    );
  }

  return (
    <Listbox value={currentCompany} onChange={setCurrentCompany}>
      <div className="relative">
        <Listbox.Button className="relative w-full cursor-pointer py-1 pr-6 text-left border-0 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 sm:text-sm transition-colors duration-200 group">
          <span className="block truncate font-semibold text-gray-900 dark:text-white group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors duration-200">
            {currentCompany?.name || "Select Company"}
          </span>
          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center">
            <ChevronUpDownIcon
              className="h-4 w-4 text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200"
              aria-hidden="true"
            />
          </span>
        </Listbox.Button>
        <Transition
          as={Fragment}
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <Listbox.Options className="absolute z-10 mt-2 max-h-60 w-full overflow-auto rounded-lg bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 dark:ring-gray-600 focus:outline-none sm:text-sm border border-gray-200 dark:border-gray-700 dark:border-gray-600">
            {companies.map((company) => (
              <Listbox.Option
                key={company.id}
                className={({ active }) =>
                  `relative cursor-pointer select-none py-2 pl-10 pr-4 transition-colors duration-150 ${
                    active ? "bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100" : "text-gray-900 dark:text-gray-100"
                  }`
                }
                value={company}
              >
                {({ selected }) => (
                  <>
                    <span
                      className={`block truncate ${
                        selected ? "font-semibold" : "font-medium"
                      }`}
                    >
                      {company.name}
                    </span>
                    {selected ? (
                      <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-blue-600 dark:text-blue-400">
                        <CheckIcon className="h-5 w-5" aria-hidden="true" />
                      </span>
                    ) : null}
                  </>
                )}
              </Listbox.Option>
            ))}
          </Listbox.Options>
        </Transition>
      </div>
    </Listbox>
  );
}
