import React from 'react';

interface SeparatorProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation?: 'horizontal' | 'vertical';
  decorative?: boolean;
}

export const Separator: React.FC<SeparatorProps> = ({
  orientation = 'horizontal',
  decorative = true,
  className = '',
  ...props
}) => {
  const classes = orientation === 'horizontal' 
    ? `shrink-0 bg-border h-[1px] w-full ${className}`
    : `shrink-0 bg-border w-[1px] h-full ${className}`;
  
  return (
    <div
      role={decorative ? 'none' : 'separator'}
      aria-orientation={orientation}
      className={classes}
      {...props}
    />
  );
};
