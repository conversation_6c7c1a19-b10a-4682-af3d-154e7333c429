import { XMarkIcon, PencilIcon, PhoneIcon, EnvelopeIcon, GlobeAltIcon } from "@heroicons/react/24/outline";
import type { Contact } from "../../types";
import { contactService } from "../../services/contactService";

interface ContactViewProps {
  contact: Contact;
  onEdit?: () => void;
  onClose: () => void;
}

export default function ContactView({ contact, onEdit, onClose }: ContactViewProps) {
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }).format(date);
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'Invalid Date';
    }
  };

  const getContactTypeBadge = () => {
    const colorClass = contactService.getContactTypeColor(contact.contactType);
    const displayName = contactService.getContactTypeDisplayName(contact.contactType);

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
        {displayName}
      </span>
    );
  };

  const getStatusBadge = () => {
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          contact.isActive
            ? "bg-green-100 dark:bg-green-900/20 text-green-800"
            : "bg-red-100 dark:bg-red-900/20 text-red-800"
        }`}
      >
        {contact.isActive ? "Active" : "Inactive"}
      </span>
    );
  };

  const billingAddress = contactService.getFullAddress(contact, 'billing');
  const shippingAddress = contactService.getFullAddress(contact, 'shipping');

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0 h-16 w-16">
              <div className="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center">
                <span className="text-xl font-medium text-gray-700 dark:text-gray-300">
                  {contactService.getDisplayName(contact).charAt(0).toUpperCase()}
                </span>
              </div>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {contactService.getDisplayName(contact)}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">
                {contact.contactNumber}
              </p>
              <div className="flex items-center space-x-2 mt-2">
                {getContactTypeBadge()}
                {getStatusBadge()}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {onEdit && (
              <button
                onClick={onEdit}
                className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <PencilIcon className="h-4 w-4 mr-2" />
                Edit
              </button>
            )}
            <button
              onClick={onClose}
              className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
        </div>

        <div className="grid bg-white dark:bg-gray-800 grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Contact Information */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Contact Information</h3>
              <div className="space-y-3">
                {contact.email && (
                  <div className="flex items-center">
                    <EnvelopeIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">Email</p>
                      <a
                        href={`mailto:${contact.email}`}
                        className="text-sm text-primary-600 hover:text-primary-500"
                      >
                        {contact.email}
                      </a>
                    </div>
                  </div>
                )}

                {contact.phone && (
                  <div className="flex items-center">
                    <PhoneIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">Phone</p>
                      <a
                        href={`tel:${contact.phone}`}
                        className="text-sm text-primary-600 hover:text-primary-500"
                      >
                        {contact.phone}
                      </a>
                    </div>
                  </div>
                )}

                {contact.mobile && (
                  <div className="flex items-center">
                    <PhoneIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">Mobile</p>
                      <a
                        href={`tel:${contact.mobile}`}
                        className="text-sm text-primary-600 hover:text-primary-500"
                      >
                        {contact.mobile}
                      </a>
                    </div>
                  </div>
                )}

                {contact.website && (
                  <div className="flex items-center">
                    <GlobeAltIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">Website</p>
                      <a
                        href={contact.website.startsWith('http') ? contact.website : `https://${contact.website}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-primary-600 hover:text-primary-500"
                      >
                        {contact.website}
                      </a>
                    </div>
                  </div>
                )}

                {contact.taxId && (
                  <div className="flex items-start">
                    <div className="w-5 h-5 mr-3 mt-0.5"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">Tax ID</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{contact.taxId}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Financial Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Financial Information</h3>
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">Currency</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{contact.currency}</p>
                </div>

                {contact.paymentTerms && (
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">Payment Terms</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      {contactService.getPaymentTermsOptions().find(
                        option => option.value === contact.paymentTerms
                      )?.label || contact.paymentTerms}
                    </p>
                  </div>
                )}

                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">Credit Limit</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    {contactService.formatCurrency(contact.creditLimit, contact.currency)}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Addresses */}
          <div className="space-y-6">
            {/* Billing Address */}
            {billingAddress && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Billing Address</h3>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 whitespace-pre-line">
                  {billingAddress}
                </div>
              </div>
            )}

            {/* Shipping Address */}
            {shippingAddress && shippingAddress !== billingAddress && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Shipping Address</h3>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 whitespace-pre-line">
                  {shippingAddress}
                </div>
              </div>
            )}

            {/* Notes */}
            {contact.notes && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Notes</h3>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 whitespace-pre-line">
                  {contact.notes}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Metadata */}
        <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            <div>
              <span className="font-medium">Created:</span> {formatDate(contact.createdAt)}
            </div>
            <div>
              <span className="font-medium">Last Updated:</span> {formatDate(contact.updatedAt)}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
