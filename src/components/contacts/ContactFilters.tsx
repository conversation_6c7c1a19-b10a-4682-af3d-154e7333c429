import { useState } from "react";
import { MagnifyingGlassIcon, FunnelIcon, XMarkIcon } from "@heroicons/react/24/outline";
import type { ContactType } from "../../types";
import { contactService } from "../../services/contactService";

interface ContactFiltersProps {
  onFiltersChange: (filters: {
    contactType?: ContactType;
    isActive?: boolean;
    search?: string;
  }) => void;
}

export default function ContactFilters({ onFiltersChange }: ContactFiltersProps) {
  const [search, setSearch] = useState("");
  const [contactType, setContactType] = useState<ContactType | "">("");
  const [isActive, setIsActive] = useState<boolean | "">("");
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleSearchChange = (value: string) => {
    setSearch(value);
    applyFilters({ search: value });
  };

  const handleContactTypeChange = (value: ContactType | "") => {
    setContactType(value);
    applyFilters({ contactType: value || undefined });
  };

  const handleStatusChange = (value: boolean | "") => {
    setIsActive(value);
    applyFilters({ isActive: value === "" ? undefined : value });
  };

  const applyFilters = (newFilters: Partial<{
    contactType?: ContactType;
    isActive?: boolean;
    search?: string;
  }> = {}) => {
    const filters = {
      search: newFilters.search !== undefined ? newFilters.search : search,
      contactType: newFilters.contactType !== undefined ? newFilters.contactType : (contactType || undefined),
      isActive: newFilters.isActive !== undefined ? newFilters.isActive : (isActive === "" ? undefined : isActive),
    };

    // Remove empty values
    Object.keys(filters).forEach(key => {
      if (filters[key as keyof typeof filters] === undefined || filters[key as keyof typeof filters] === "") {
        delete filters[key as keyof typeof filters];
      }
    });

    onFiltersChange(filters);
  };

  const clearFilters = () => {
    setSearch("");
    setContactType("");
    setIsActive("");
    setShowAdvanced(false);
    onFiltersChange({});
  };

  const hasActiveFilters = search || contactType || isActive !== "";

  const contactTypeOptions: Array<{ value: ContactType; label: string }> = [
    { value: "CUSTOMER", label: "Customer" },
    { value: "VENDOR", label: "Vendor" },
    { value: "EMPLOYEE", label: "Employee" },
    { value: "OTHER", label: "Other" },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="flex flex-col bg-white dark:bg-gray-800 space-y-4">
        {/* Search Bar */}
        <div className="flex-1">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
            </div>
            <input
              type="text"
              placeholder="Search contacts by name, email, phone..."
              value={search}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 placeholder-gray-500 dark:placeholder-gray-400 dark:placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 dark:placeholder-gray-500 dark:placeholder-gray-400 dark:placeholder-gray-500 dark:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
        </div>

        {/* Filter Toggle and Clear */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Advanced Filters
            {hasActiveFilters && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                Active
              </span>
            )}
          </button>

          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="inline-flex items-center px-3 py-2 text-sm leading-4 font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:text-white"
            >
              <XMarkIcon className="h-4 w-4 mr-1" />
              Clear Filters
            </button>
          )}
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            {/* Contact Type Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Contact Type
              </label>
              <select
                value={contactType}
                onChange={(e) => handleContactTypeChange(e.target.value as ContactType | "")}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">All Types</option>
                {contactTypeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Status
              </label>
              <select
                value={isActive === "" ? "" : isActive.toString()}
                onChange={(e) => {
                  const value = e.target.value;
                  handleStatusChange(value === "" ? "" : value === "true");
                }}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">All Statuses</option>
                <option value="true">Active</option>
                <option value="false">Inactive</option>
              </select>
            </div>
          </div>
        )}

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 pt-2 border-t border-gray-200 dark:border-gray-700">
            {search && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800">
                Search: "{search}"
                <button
                  onClick={() => handleSearchChange("")}
                  className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:text-blue-600 dark:text-blue-400"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            {contactType && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800">
                Type: {contactService.getContactTypeDisplayName(contactType)}
                <button
                  onClick={() => handleContactTypeChange("")}
                  className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-green-400 hover:text-green-600 dark:text-green-400"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            {isActive !== "" && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Status: {isActive ? "Active" : "Inactive"}
                <button
                  onClick={() => handleStatusChange("")}
                  className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-purple-400 hover:text-purple-600"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
