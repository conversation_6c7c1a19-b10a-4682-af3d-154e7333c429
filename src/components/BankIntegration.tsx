import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';
import { Alert, AlertDescription } from './ui/alert';
import { 
  CreditCard, 
  Building2, 
  RefreshCw, 
  Plus, 
  Trash2, 
  CheckCircle, 
  AlertCircle,
  DollarSign,
  Calendar,
  Filter
} from 'lucide-react';
import { useToast } from '../hooks/use-toast';
import { apiService } from '@/services/api';

interface BankAccount {
  id: string;
  bankName: string;
  accountName: string;
  accountNumber: string;
  accountType: 'CHECKING' | 'SAVINGS' | 'CREDIT_CARD' | 'INVESTMENT' | 'LOAN';
  balance: number;
  currency: string;
  isActive: boolean;
  lastSyncAt?: string;
}

interface BankTransaction {
  id: string;
  transactionId: string;
  amount: number;
  description: string;
  category: string;
  date: string;
  pending: boolean;
  merchantName?: string;
  isReconciled: boolean;
  reconciledAt?: string;
}

interface BankIntegrationProps {
  companyId: string;
}

export const BankIntegration: React.FC<BankIntegrationProps> = ({ companyId }) => {
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([]);
  const [transactions, setTransactions] = useState<BankTransaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<string | null>(null);
  const [showTransactions, setShowTransactions] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadBankAccounts();
  }, [companyId]);

  const loadBankAccounts = async () => {
    try {
      setLoading(true);
      const accounts = await apiService.get(`/banks/${companyId}/accounts`);
      setBankAccounts(accounts as BankAccount[]);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load bank accounts',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadTransactions = async (accountId?: string) => {
    try {
      setLoading(true);
      const params = accountId ? { bankAccountId: accountId } : {};
      const response = await apiService.get(`/banks/${companyId}/transactions`, { params });
      setTransactions((response as any).transactions || []);
      setShowTransactions(true);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load transactions',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const connectBankAccount = async () => {
    try {
      setLoading(true);
      
      // Create link token
      await apiService.post(`/banks/${companyId}/link-token`, {
        products: ['transactions', 'accounts']
      });
      
      // In a real implementation, you would use Plaid Link here
      // For now, we'll simulate the connection
      toast({
        title: 'Bank Connection',
        description: 'Bank connection feature requires Plaid Link integration',
      });
      
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to connect bank account',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const syncTransactions = async (accountId?: string) => {
    try {
      setSyncing(true);
      const response = await apiService.post(`/banks/${companyId}/sync`, {
        bankAccountId: accountId
      });
      
      toast({
        title: 'Sync Complete',
        description: `${(response as any).newTransactions || 0} new transactions, ${(response as any).updatedTransactions || 0} updated`,
      });
      
      await loadBankAccounts();
      if (showTransactions) {
        await loadTransactions(selectedAccount || undefined);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to sync transactions',
        variant: 'destructive',
      });
    } finally {
      setSyncing(false);
    }
  };

  const disconnectAccount = async (accountId: string) => {
    try {
      await apiService.delete(`/banks/${companyId}/accounts/${accountId}`);
      toast({
        title: 'Success',
        description: 'Bank account disconnected successfully',
      });
      await loadBankAccounts();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to disconnect bank account',
        variant: 'destructive',
      });
    }
  };

  const reconcileTransaction = async (transactionId: string) => {
    try {
      await apiService.put(`/banks/${companyId}/transactions/${transactionId}/reconcile`, {});
      toast({
        title: 'Success',
        description: 'Transaction reconciled successfully',
      });
      await loadTransactions(selectedAccount || undefined);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to reconcile transaction',
        variant: 'destructive',
      });
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getAccountTypeIcon = (type: string) => {
    switch (type) {
      case 'CHECKING':
      case 'SAVINGS':
        return <Building2 className="h-4 w-4" />;
      case 'CREDIT_CARD':
        return <CreditCard className="h-4 w-4" />;
      default:
        return <DollarSign className="h-4 w-4" />;
    }
  };

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'CHECKING':
        return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800';
      case 'SAVINGS':
        return 'bg-green-100 dark:bg-green-900/20 text-green-800';
      case 'CREDIT_CARD':
        return 'bg-purple-100 text-purple-800';
      case 'INVESTMENT':
        return 'bg-orange-100 text-orange-800';
      case 'LOAN':
        return 'bg-red-100 dark:bg-red-900/20 text-red-800';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Bank Integration</h2>
          <p className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Connect and manage your bank accounts</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => syncTransactions()}
            disabled={syncing || bankAccounts.length === 0}
            variant="outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${syncing ? 'animate-spin' : ''}`} />
            Sync All
          </Button>
          <Button onClick={connectBankAccount} disabled={loading}>
            <Plus className="h-4 w-4 mr-2" />
            Connect Bank
          </Button>
        </div>
      </div>

      {/* Bank Accounts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Connected Accounts
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading && bankAccounts.length === 0 ? (
            <div className="text-center py-8">Loading bank accounts...</div>
          ) : bankAccounts.length === 0 ? (
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 mx-auto text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-4" />
              <h3 className="text-lg font-medium mb-2">No Bank Accounts Connected</h3>
              <p className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-4">
                Connect your bank accounts to automatically sync transactions
              </p>
              <Button onClick={connectBankAccount}>
                <Plus className="h-4 w-4 mr-2" />
                Connect Your First Bank
              </Button>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {bankAccounts.map((account) => (
                <Card key={account.id} className="relative">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center gap-2">
                        {getAccountTypeIcon(account.accountType)}
                        <div>
                          <h4 className="font-medium">{account.accountName}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{account.bankName}</p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => disconnectAccount(account.id)}
                        className="text-red-600 dark:text-red-400 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Balance</span>
                        <span className="font-medium">
                          {formatCurrency(account.balance, account.currency)}
                        </span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Type</span>
                        <Badge className={getAccountTypeColor(account.accountType)}>
                          {account.accountType.replace('_', ' ')}
                        </Badge>
                      </div>
                      
                      {account.lastSyncAt && (
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Last Sync</span>
                          <span className="text-sm">{formatDate(account.lastSyncAt)}</span>
                        </div>
                      )}
                    </div>
                    
                    <Separator className="my-3" />
                    
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedAccount(account.id);
                          loadTransactions(account.id);
                        }}
                        className="flex-1"
                      >
                        View Transactions
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => syncTransactions(account.id)}
                        disabled={syncing}
                      >
                        <RefreshCw className={`h-4 w-4 ${syncing ? 'animate-spin' : ''}`} />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Transactions */}
      {showTransactions && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Bank Transactions
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadTransactions()}
                className="ml-auto"
              >
                <Filter className="h-4 w-4 mr-2" />
                All Accounts
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">Loading transactions...</div>
            ) : transactions.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 mx-auto text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-4" />
                <h3 className="text-lg font-medium mb-2">No Transactions Found</h3>
                <p className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Sync your bank accounts to see transactions here
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {transactions.map((transaction) => (
                  <div
                    key={transaction.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{transaction.description}</span>
                        {transaction.pending && (
                          <Badge variant="outline" className="text-xs">
                            Pending
                          </Badge>
                        )}
                        {transaction.isReconciled && (
                          <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                        )}
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        <span>{formatDate(transaction.date)}</span>
                        <span>{transaction.category}</span>
                        {transaction.merchantName && (
                          <span>{transaction.merchantName}</span>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <span className={`font-medium ${
                        transaction.amount >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                      }`}>
                        {formatCurrency(Math.abs(transaction.amount))}
                      </span>
                      
                      {!transaction.isReconciled && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => reconcileTransaction(transaction.id)}
                        >
                          Reconcile
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Status Alert */}
      {bankAccounts.length > 0 && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Bank transactions are automatically synced daily. You can manually sync anytime using the sync buttons.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};
