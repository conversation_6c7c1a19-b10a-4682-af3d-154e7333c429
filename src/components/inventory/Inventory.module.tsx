import React, { useState } from "react";
import InventoryDashboard from "./InventoryDashboard";
import InventoryItemList from "./InventoryItemList";
import InventoryItemDetail from "./InventoryItemDetail";
import InventoryItemForm from "./InventoryItemForm";
import StockAdjustmentDialog from "./StockAdjustmentDialog";
import StockTransferDialog from "./StockTransferDialog";

// Main Inventory module page with simple routing/state
const InventoryModule: React.FC = () => {
  const [view, setView] = useState<"dashboard" | "list" | "detail" | "form">("dashboard");
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [showAdjust, setShowAdjust] = useState(false);
  const [showTransfer, setShowTransfer] = useState(false);
  const [locations, setLocations] = useState<Array<{ id: string; name: string }>>([]);
  const [refreshKey, setRefreshKey] = useState(0);

  // Handlers for navigation/actions
  const handleShowList = () => setView("list");
  const handleShowDashboard = () => setView("dashboard");
  const handleShowDetail = (id: string) => {
    setSelectedItemId(id);
    setView("detail");
  };
  const handleAddItem = () => setView("form");
  const handleEditItem = (id: string) => {
    setSelectedItemId(id);
    setView("form");
  };
  const handleFormSubmit = () => {
    setView("list");
    setRefreshKey(k => k + 1);
  };
  const handleCloseDetail = () => setView("list");
  const handleShowAdjust = (itemId: string, locs: Array<{ id: string; name: string }>) => {
    setSelectedItemId(itemId);
    setLocations(locs);
    setShowAdjust(true);
  };
  const handleShowTransfer = (itemId: string, locs: Array<{ id: string; name: string }>) => {
    setSelectedItemId(itemId);
    setLocations(locs);
    setShowTransfer(true);
  };

  return (
    <div className="p-6">
      {view === "dashboard" && <InventoryDashboard />}
      {view === "list" && (
        <InventoryItemList key={refreshKey}
          onAddItem={handleAddItem}
          onShowDetail={handleShowDetail}
        />
      )}
      {view === "detail" && selectedItemId && (
        <InventoryItemDetail
          itemId={selectedItemId}
          onClose={handleCloseDetail}
          onEdit={() => handleEditItem(selectedItemId)}
          onAdjustStock={(locs) => handleShowAdjust(selectedItemId, locs)}
          onTransferStock={(locs) => handleShowTransfer(selectedItemId, locs)}
        />
      )}
      {view === "form" && (
        <InventoryItemForm
          item={selectedItemId}
          onSubmit={handleFormSubmit}
          onCancel={handleShowList}
        />
      )}
      {showAdjust && selectedItemId && (
        <StockAdjustmentDialog
          itemId={selectedItemId}
          locations={locations}
          onClose={() => setShowAdjust(false)}
          onAdjusted={() => setRefreshKey(k => k + 1)}
        />
      )}
      {showTransfer && selectedItemId && (
        <StockTransferDialog
          itemId={selectedItemId}
          locations={locations}
          onClose={() => setShowTransfer(false)}
          onTransferred={() => setRefreshKey(k => k + 1)}
        />
      )}
    </div>
  );
};

export default InventoryModule;
