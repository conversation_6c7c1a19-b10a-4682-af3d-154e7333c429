import React, { useState } from "react";
import { Button } from "../ui/button";

interface StockTransferDialogProps {
  itemId: string;
  locations: Array<{ id: string; name: string }>;
  onClose: () => void;
  onTransferred: () => void;
}

const StockTransferDialog: React.FC<StockTransferDialogProps> = ({ itemId, locations, onClose, onTransferred }) => {
  const [form, setForm] = useState({
    from_location_id: locations[0]?.id || "",
    to_location_id: locations[1]?.id || "",
    quantity: 0,
    reference: "",
  });
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  function handleChange(e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setSaving(true);
    setError(null);
    try {
      const res = await fetch(`/api/inventory/${window.currentCompanyId}/transfer`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ...form, item_id: itemId }),
      });
      if (!res.ok) throw new Error("Failed to transfer stock");
      onTransferred();
      onClose();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex justify-center items-center z-50">
      <div className="bg-white dark:bg-gray-900 rounded shadow-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-bold mb-4">Transfer Stock</h2>
        <form className="space-y-4" onSubmit={handleSubmit}>
          <div>
            <label className="block font-semibold mb-1">From Location</label>
            <select name="from_location_id" value={form.from_location_id} onChange={handleChange} className="input input-bordered w-full">
              {locations.map((loc) => (
                <option key={loc.id} value={loc.id}>{loc.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block font-semibold mb-1">To Location</label>
            <select name="to_location_id" value={form.to_location_id} onChange={handleChange} className="input input-bordered w-full">
              {locations.map((loc) => (
                <option key={loc.id} value={loc.id}>{loc.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block font-semibold mb-1">Quantity</label>
            <input name="quantity" type="number" value={form.quantity} onChange={handleChange} className="input input-bordered w-full" required />
          </div>
          <div>
            <label className="block font-semibold mb-1">Reference</label>
            <input name="reference" value={form.reference} onChange={handleChange} className="input input-bordered w-full" />
          </div>
          {error && <div className="text-red-600">{error}</div>}
          <div className="flex space-x-2 mt-4">
            <Button type="submit" disabled={saving}>{saving ? "Transferring..." : "Transfer"}</Button>
            <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StockTransferDialog;
