import React, { useEffect, useState } from "react";
import { Button } from "../ui/button";

interface ItemDetailProps {
  itemId: string;
  onClose: () => void;
  onEdit: () => void;
  onAdjustStock: (locations: Array<{ id: string; name: string }>) => void;
  onTransferStock: (locations: Array<{ id: string; name: string }>) => void;
}

interface InventoryItemDetail {
  id: string;
  sku: string;
  name: string;
  description?: string;
  category?: string;
  vendor?: string;
  unit_of_measure?: string;
  purchase_price?: number;
  sales_price?: number;
  is_active: boolean;
  photo_url?: string;
  stockByLocation: Array<{
    location_id: string;
    location_name: string;
    quantity_on_hand: number;
    reorder_point: number;
    min_level: number;
    max_level: number;
  }>;
  transactionHistory: Array<{
    id: string;
    date: string;
    type: string;
    quantity: number;
    location_name: string;
    reference?: string;
  }>;
}

const InventoryItemDetail: React.FC<ItemDetailProps> = ({ itemId, onClose }) => {
  const [item, setItem] = useState<InventoryItemDetail | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(true);
    Promise.all([
      fetch(`/api/inventory/${window.currentCompanyId}/items/${itemId}`).then((res) => res.json()),
      fetch(`/api/inventory/${window.currentCompanyId}/items/${itemId}/stock`).then((res) => res.json()),
      fetch(`/api/inventory/${window.currentCompanyId}/reports/activity?item_id=${itemId}`).then((res) => res.json()),
    ]).then(([itemData, stockData, historyData]) => {
      setItem({
        ...itemData,
        stockByLocation: stockData,
        transactionHistory: historyData.transactions || [],
      });
    }).finally(() => setLoading(false));
  }, [itemId]);

  if (loading || !item) return <div className="p-6">Loading...</div>;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex justify-end z-50">
      <div className="w-full max-w-xl bg-white dark:bg-gray-900 h-full shadow-lg overflow-y-auto p-6 relative">
        <button onClick={onClose} className="absolute top-4 right-4 text-gray-400 hover:text-gray-700">&times;</button>
        <div className="flex items-center space-x-4 mb-4">
          {item.photo_url ? (
            <img src={item.photo_url} alt={item.name} className="w-20 h-20 rounded object-cover" />
          ) : (
            <div className="w-20 h-20 bg-gray-200 rounded flex items-center justify-center text-gray-400">No Image</div>
          )}
          <div>
            <h2 className="text-2xl font-bold">{item.name}</h2>
            <div className="text-gray-500">SKU: {item.sku}</div>
            <div className="text-gray-500">{item.category}</div>
            <div className="text-gray-500">Vendor: {item.vendor}</div>
            <div className="text-gray-500">Unit: {item.unit_of_measure}</div>
            <div className="text-gray-500">Status: {item.is_active ? "Active" : "Inactive"}</div>
          </div>
        </div>
        <div className="mb-6">
          <div className="font-semibold mb-2">Description</div>
          <div className="text-gray-700 dark:text-gray-300">{item.description || "-"}</div>
        </div>
        <div className="mb-6">
          <div className="font-semibold mb-2">Stock by Location</div>
          <table className="min-w-full table-auto border border-gray-200 dark:border-gray-700">
            <thead className="bg-gray-100 dark:bg-gray-800">
              <tr>
                <th className="px-2 py-1 text-left">Location</th>
                <th className="px-2 py-1 text-right">Qty On Hand</th>
                <th className="px-2 py-1 text-right">Reorder Point</th>
                <th className="px-2 py-1 text-right">Min</th>
                <th className="px-2 py-1 text-right">Max</th>
              </tr>
            </thead>
            <tbody>
              {item.stockByLocation.map((loc) => (
                <tr key={loc.location_id}>
                  <td className="px-2 py-1">{loc.location_name}</td>
                  <td className="px-2 py-1 text-right">{loc.quantity_on_hand}</td>
                  <td className="px-2 py-1 text-right">{loc.reorder_point}</td>
                  <td className="px-2 py-1 text-right">{loc.min_level}</td>
                  <td className="px-2 py-1 text-right">{loc.max_level}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="mb-6">
          <div className="font-semibold mb-2">Transaction History</div>
          <div className="overflow-x-auto">
            <table className="min-w-full table-auto border border-gray-200 dark:border-gray-700">
              <thead className="bg-gray-100 dark:bg-gray-800">
                <tr>
                  <th className="px-2 py-1 text-left">Date</th>
                  <th className="px-2 py-1 text-left">Type</th>
                  <th className="px-2 py-1 text-left">Location</th>
                  <th className="px-2 py-1 text-right">Qty</th>
                  <th className="px-2 py-1 text-left">Reference</th>
                </tr>
              </thead>
              <tbody>
                {item.transactionHistory.length ? (
                  item.transactionHistory.map((txn) => (
                    <tr key={txn.id}>
                      <td className="px-2 py-1">{txn.date}</td>
                      <td className="px-2 py-1">{txn.type}</td>
                      <td className="px-2 py-1">{txn.location_name}</td>
                      <td className="px-2 py-1 text-right">{txn.quantity}</td>
                      <td className="px-2 py-1">{txn.reference ?? "-"}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="px-2 py-4 text-center text-gray-500">
                      No transactions found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={onEdit}>Edit</Button>
          <Button variant="outline" onClick={() => onAdjustStock(item.stockByLocation.map(loc => ({ id: loc.location_id, name: loc.location_name })))}>Adjust Stock</Button>
          <Button variant="outline" onClick={() => onTransferStock(item.stockByLocation.map(loc => ({ id: loc.location_id, name: loc.location_name })))}>Transfer Stock</Button>
        </div>
      </div>
    </div>
  );
};

export default InventoryItemDetail;
