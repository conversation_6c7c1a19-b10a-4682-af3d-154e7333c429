import React, { useEffect, useState } from "react";
import { Button } from "../ui/button";

interface InventoryCategory {
  id: string;
  name: string;
}
interface ContactVendor {
  id: string;
  name: string;
}

interface ItemFormProps {
  item?: any;
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

const InventoryItemForm: React.FC<ItemFormProps> = ({ item, onSubmit, onCancel }) => {
  const [form, setForm] = useState({
    sku: item?.sku || "",
    name: item?.name || "",
    description: item?.description || "",
    category_id: item?.category_id || "",
    preferred_vendor_id: item?.preferred_vendor_id || "",
    unit_of_measure: item?.unit_of_measure || "",
    purchase_price: item?.purchase_price || "",
    sales_price: item?.sales_price || "",
    cost_method: item?.cost_method || "FIFO",
    is_active: item?.is_active ?? true,
    photo: undefined as File | undefined,
  });
  const [categories, setCategories] = useState<InventoryCategory[]>([]);
  const [vendors, setVendors] = useState<ContactVendor[]>([]);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetch(`/api/inventory/${window.currentCompanyId}/categories`).then(r => r.json()).then(setCategories);
    fetch(`/api/contacts?vendor_only=true`).then(r => r.json()).then(setVendors);
  }, []);

  function handleChange(e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) {
    const { name, value, type, checked } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  }

  function handleFileChange(e: React.ChangeEvent<HTMLInputElement>) {
    setForm((prev) => ({ ...prev, photo: e.target.files?.[0] }));
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setSaving(true);
    const payload = { ...form };
    // Handle photo upload if present (stub)
    if (form.photo) {
      // TODO: Implement photo upload API
    }
    await onSubmit(payload);
    setSaving(false);
  }

  function handleCancel() {
    setForm({
      sku: item?.sku || "",
      name: item?.name || "",
      description: item?.description || "",
      category_id: item?.category_id || "",
      preferred_vendor_id: item?.preferred_vendor_id || "",
      unit_of_measure: item?.unit_of_measure || "",
      purchase_price: item?.purchase_price || "",
      sales_price: item?.sales_price || "",
      cost_method: item?.cost_method || "FIFO",
      is_active: item?.is_active ?? true,
      photo: undefined,
    });
    onCancel();
  }

  return (
    <form className="space-y-4 p-4" onSubmit={handleSubmit}>
      <div className="flex space-x-4">
        <div className="flex-1">
          <label className="block font-semibold mb-1">SKU</label>
          <input name="sku" value={form.sku} onChange={handleChange} required className="input input-bordered w-full" />
        </div>
        <div className="flex-1">
          <label className="block font-semibold mb-1">Name</label>
          <input name="name" value={form.name} onChange={handleChange} required className="input input-bordered w-full" />
        </div>
      </div>
      <div>
        <label className="block font-semibold mb-1">Description</label>
        <textarea name="description" value={form.description} onChange={handleChange} className="input input-bordered w-full" />
      </div>
      <div className="flex space-x-4">
        <div className="flex-1">
          <label className="block font-semibold mb-1">Category</label>
          <select name="category_id" value={form.category_id} onChange={handleChange} className="input input-bordered w-full">
            <option value="">-- Select --</option>
            {categories.map((cat) => (
              <option key={cat.id} value={cat.id}>{cat.name}</option>
            ))}
          </select>
        </div>
        <div className="flex-1">
          <label className="block font-semibold mb-1">Vendor</label>
          <select name="preferred_vendor_id" value={form.preferred_vendor_id} onChange={handleChange} className="input input-bordered w-full">
            <option value="">-- Select --</option>
            {vendors.map((v) => (
              <option key={v.id} value={v.id}>{v.name}</option>
            ))}
          </select>
        </div>
      </div>
      <div className="flex space-x-4">
        <div className="flex-1">
          <label className="block font-semibold mb-1">Unit of Measure</label>
          <input name="unit_of_measure" value={form.unit_of_measure} onChange={handleChange} className="input input-bordered w-full" />
        </div>
        <div className="flex-1">
          <label className="block font-semibold mb-1">Purchase Price</label>
          <input name="purchase_price" type="number" value={form.purchase_price} onChange={handleChange} className="input input-bordered w-full" />
        </div>
        <div className="flex-1">
          <label className="block font-semibold mb-1">Sales Price</label>
          <input name="sales_price" type="number" value={form.sales_price} onChange={handleChange} className="input input-bordered w-full" />
        </div>
      </div>
      <div className="flex space-x-4">
        <div className="flex-1">
          <label className="block font-semibold mb-1">Cost Method</label>
          <select name="cost_method" value={form.cost_method} onChange={handleChange} className="input input-bordered w-full">
            <option value="FIFO">FIFO</option>
            <option value="AVG">Average</option>
          </select>
        </div>
        <div className="flex-1 flex items-center mt-8">
          <label className="mr-2 font-semibold">Active</label>
          <input name="is_active" type="checkbox" checked={form.is_active} onChange={handleChange} />
        </div>
      </div>
      <div>
        <label className="block font-semibold mb-1">Photo</label>
        <input name="photo" type="file" accept="image/*" onChange={handleFileChange} className="file-input" />
      </div>
      <div className="flex space-x-2 mt-4">
        <Button type="submit" disabled={saving}>{saving ? "Saving..." : "Save"}</Button>
        <Button type="button" variant="outline" onClick={handleCancel}>Cancel</Button>
      </div>
    </form>
  );
};

export default InventoryItemForm;
