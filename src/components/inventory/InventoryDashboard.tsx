import React, { useEffect, useState } from "react";
import { Button } from "../ui/button";

interface InventorySummary {
  totalItems: number;
  totalValue: number;
  lowStockCount: number;
  recentActivity: Array<{
    id: string;
    description: string;
    date: string;
  }>;
}

const InventoryDashboard: React.FC = () => {
  const [summary, setSummary] = useState<InventorySummary | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(true);
    fetch("/api/inventory/" + window.currentCompanyId + "/reports/valuation")
      .then((res) => res.json())
      .then((data) => {
        setSummary({
          totalItems: data.totalItems ?? 0,
          totalValue: data.totalValue ?? 0,
          lowStockCount: data.lowStockCount ?? 0,
          recentActivity: data.recentActivity ?? [],
        });
      })
      .finally(() => setLoading(false));
  }, []);

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Inventory Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded shadow p-4">
          <div className="text-sm text-gray-500">Total Items</div>
          <div className="text-2xl font-semibold">{summary?.totalItems ?? "-"}</div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded shadow p-4">
          <div className="text-sm text-gray-500">Total Value</div>
          <div className="text-2xl font-semibold">{summary?.totalValue?.toLocaleString() ?? "-"}</div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded shadow p-4">
          <div className="text-sm text-gray-500">Low Stock Alerts</div>
          <div className="text-2xl font-semibold text-red-600">{summary?.lowStockCount ?? "-"}</div>
        </div>
      </div>
      <div className="flex space-x-4 mt-4">
        <Button>Add Item</Button>
        <Button variant="outline">Adjust Stock</Button>
        <Button variant="outline">Transfer Stock</Button>
      </div>
      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-2">Recent Activity</h2>
        <div className="bg-white dark:bg-gray-800 rounded shadow p-4">
          {summary?.recentActivity?.length ? (
            <ul className="divide-y divide-gray-200">
              {summary.recentActivity.map((activity) => (
                <li key={activity.id} className="py-2 flex justify-between">
                  <span>{activity.description}</span>
                  <span className="text-xs text-gray-400">{activity.date}</span>
                </li>
              ))}
            </ul>
          ) : (
            <div className="text-gray-500">No recent activity.</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default InventoryDashboard;
