import React, { useEffect, useState } from "react";
import { Button } from "../ui/button";

interface InventoryItem {
  id: string;
  sku: string;
  name: string;
  category?: string;
  quantity_on_hand?: number;
  sales_price?: number;
  purchase_price?: number;
  is_active: boolean;
}

interface InventoryItemListProps {
  onAddItem: () => void;
  onShowDetail: (id: string) => void;
}

const InventoryItemList: React.FC<InventoryItemListProps> = ({ onAddItem, onShowDetail }) => {
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");

  useEffect(() => {
    setLoading(true);
    fetch(`/api/inventory/${window.currentCompanyId}/items`)
      .then((res) => res.json())
      .then((data) => setItems(data))
      .finally(() => setLoading(false));
  }, []);

  const filteredItems = items.filter(
    (item) =>
      item.name.toLowerCase().includes(search.toLowerCase()) ||
      item.sku.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Inventory Items</h1>
        <Button onClick={onAddItem}>Add Item</Button>
      </div>
      <input
        type="text"
        className="input input-bordered w-full mb-4"
        placeholder="Search by name or SKU..."
        value={search}
        onChange={(e) => setSearch(e.target.value)}
      />
      <div className="overflow-x-auto">
        <table className="min-w-full table-auto border border-gray-200 dark:border-gray-700">
          <thead className="bg-gray-100 dark:bg-gray-800">
            <tr>
              <th className="px-4 py-2 text-left">SKU</th>
              <th className="px-4 py-2 text-left">Name</th>
              <th className="px-4 py-2 text-left">Category</th>
              <th className="px-4 py-2 text-right">Qty On Hand</th>
              <th className="px-4 py-2 text-right">Sales Price</th>
              <th className="px-4 py-2 text-right">Purchase Price</th>
              <th className="px-4 py-2">Status</th>
              <th className="px-4 py-2">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredItems.length ? (
              filteredItems.map((item) => (
                <tr key={item.id} className="border-t border-gray-200 dark:border-gray-700">
                  <td className="px-4 py-2">{item.sku}</td>
                  <td className="px-4 py-2">{item.name}</td>
                  <td className="px-4 py-2">{item.category ?? "-"}</td>
                  <td className="px-4 py-2 text-right">{item.quantity_on_hand ?? "-"}</td>
                  <td className="px-4 py-2 text-right">{item.sales_price ?? "-"}</td>
                  <td className="px-4 py-2 text-right">{item.purchase_price ?? "-"}</td>
                  <td className="px-4 py-2">
                    {item.is_active ? (
                      <span className="text-green-600">Active</span>
                    ) : (
                      <span className="text-gray-400">Inactive</span>
                    )}
                  </td>
                  <td className="px-4 py-2">
                    <Button size="sm" variant="outline" onClick={() => onShowDetail(item.id)}>Details</Button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="px-4 py-8 text-center text-gray-500">
                  {loading ? "Loading..." : "No inventory items found."}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default InventoryItemList;
