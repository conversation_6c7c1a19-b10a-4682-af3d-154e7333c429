import { useState, useEffect } from "react";
import {
  PrinterIcon,
  ArrowDownTrayIcon,
  DocumentArrowDownIcon,
  TableCellsIcon,
} from "@heroicons/react/24/outline";
import { reportService, type CashFlowData } from "../../services/reportService";
import { useCompany } from "../../contexts/CompanyContext";
import {
  exportService,
  type TableData,
  type ExportOptions,
} from "../../services/exportService";
import ReportFilters from "./ReportFilters";

export default function CashFlowReport() {
  const { currentCompany } = useCompany();
  const [reportData, setReportData] = useState<CashFlowData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
  });

  useEffect(() => {
    loadReport();
  }, [currentCompany, filters]);

  const loadReport = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      setError(null);
      const data = await reportService.getCashFlow(currentCompany.id, filters);
      setReportData(data);
    } catch (err) {
      console.error("Failed to load cash flow statement:", err);
      setError("Failed to load cash flow statement. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleFiltersChange = (newFilters: any) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
  };

  const handlePrint = () => {
    reportService.printReport();
  };

  const handleExport = () => {
    if (!reportData || !reportData.data) return;

    const exportData = [];

    // Operating Activities
    exportData.push({
      Category: "Cash Flows from Operating Activities",
      Amount: "",
    });
    exportData.push({
      Category: "Net Income",
      Amount: reportData.data.operatingActivities.netIncome,
    });
    exportData.push({
      Category:
        "Adjustments to reconcile net income to net cash provided by operating activities:",
      Amount: "",
    });
    exportData.push({
      Category: "Depreciation and Amortization",
      Amount: reportData.data.operatingActivities.adjustments.depreciation,
    });
    exportData.push({ Category: "Changes in Working Capital:", Amount: "" });
    exportData.push({
      Category: "Accounts Receivable",
      Amount:
        reportData.data.operatingActivities.adjustments?.changes
          ?.accountsReceivable ?? 0,
    });
    exportData.push({
      Category: "Accounts Payable",
      Amount:
        reportData.data.operatingActivities.adjustments?.changes
          ?.accountsPayable ?? 0,
    });
    exportData.push({
      Category: "Inventory",
      Amount:
        reportData.data.operatingActivities.adjustments?.changes?.inventory ??
        0,
    });
    exportData.push({
      Category: "Other Current Assets",
      Amount:
        reportData.data.operatingActivities.adjustments?.changes
          ?.otherCurrentAssets ?? 0,
    });
    exportData.push({
      Category: "Other Current Liabilities",
      Amount:
        reportData.data.operatingActivities.adjustments?.changes
          ?.otherCurrentLiabilities ?? 0,
    });
    exportData.push({
      Category: "Net Cash Provided by Operating Activities",
      Amount: reportData.data.operatingActivities.netCash,
    });

    // Investing Activities
    exportData.push({
      Category: "Cash Flows from Investing Activities",
      Amount: "",
    });
    exportData.push({
      Category: "Purchases of Property and Equipment",
      Amount: reportData.data.investingActivities.purchases,
    });
    exportData.push({
      Category: "Proceeds from Sale of Assets",
      Amount: reportData.data.investingActivities.proceeds,
    });
    exportData.push({
      Category: "Net Cash Used in Investing Activities",
      Amount: reportData.data.investingActivities.netCash,
    });

    // Financing Activities
    exportData.push({
      Category: "Cash Flows from Financing Activities",
      Amount: "",
    });
    exportData.push({
      Category: "Proceeds from Issuance of Stock",
      Amount: reportData.data.financingActivities.stockIssuance,
    });
    exportData.push({
      Category: "Dividends Paid",
      Amount: reportData.data.financingActivities.dividends,
    });
    exportData.push({
      Category: "Proceeds from Borrowing",
      Amount: reportData.data.financingActivities.borrowing,
    });
    exportData.push({
      Category: "Repayment of Debt",
      Amount: reportData.data.financingActivities.debtRepayment,
    });
    exportData.push({
      Category: "Net Cash Provided by Financing Activities",
      Amount: reportData.data.financingActivities.netCash,
    });

    // Summary
    exportData.push({
      Category: "Net Increase (Decrease) in Cash",
      Amount: reportData.data.netChange,
    });
    exportData.push({
      Category: "Cash at Beginning of Period",
      Amount: reportData.data.beginningCash,
    });
    exportData.push({
      Category: "Cash at End of Period",
      Amount: reportData.data.endingCash,
    });

    reportService.exportToCSV(
      exportData,
      `cash-flow-statement-${filters.startDate}-to-${filters.endDate}`
    );
  };

  const prepareTableData = (): TableData => {
    if (!reportData || !reportData.data)
      throw new Error("No report data available");

    const rows = [];

    // Operating Activities
    rows.push({
      category: "Cash Flows from Operating Activities",
      amount: "",
      isHeader: true,
    });
    rows.push({
      category: "Net Income",
      amount: reportData.data.operatingActivities.netIncome,
      isHeader: false,
    });
    rows.push({
      category:
        "Adjustments to reconcile net income to net cash provided by operating activities:",
      amount: "",
      isHeader: false,
    });
    rows.push({
      category: "Depreciation and Amortization",
      amount: reportData.data.operatingActivities.adjustments.depreciation,
      isHeader: false,
    });
    rows.push({
      category: "Changes in Working Capital:",
      amount: "",
      isHeader: false,
    });
    rows.push({
      category: "Accounts Receivable",
      amount:
        reportData.data.operatingActivities.adjustments?.changes
          ?.accountsReceivable ?? 0,
      isHeader: false,
    });
    rows.push({
      category: "Accounts Payable",
      amount:
        reportData.data.operatingActivities.adjustments?.changes
          ?.accountsPayable ?? 0,
      isHeader: false,
    });
    rows.push({
      category: "Inventory",
      amount:
        reportData.data.operatingActivities.adjustments?.changes?.inventory ??
        0,
      isHeader: false,
    });
    rows.push({
      category: "Other Current Assets",
      amount:
        reportData.data.operatingActivities.adjustments?.changes
          ?.otherCurrentAssets ?? 0,
      isHeader: false,
    });
    rows.push({
      category: "Other Current Liabilities",
      amount:
        reportData.data.operatingActivities.adjustments?.changes
          ?.otherCurrentLiabilities ?? 0,
      isHeader: false,
    });
    rows.push({
      category: "Net Cash Provided by Operating Activities",
      amount: reportData.data.operatingActivities.netCash,
      isHeader: false,
    });

    // Investing Activities
    rows.push({
      category: "Cash Flows from Investing Activities",
      amount: "",
      isHeader: true,
    });
    rows.push({
      category: "Purchases of Property and Equipment",
      amount: reportData.data.investingActivities.purchases,
      isHeader: false,
    });
    rows.push({
      category: "Proceeds from Sale of Assets",
      amount: reportData.data.investingActivities.proceeds,
      isHeader: false,
    });
    rows.push({
      category: "Net Cash Used in Investing Activities",
      amount: reportData.data.investingActivities.netCash,
      isHeader: false,
    });

    // Financing Activities
    rows.push({
      category: "Cash Flows from Financing Activities",
      amount: "",
      isHeader: true,
    });
    rows.push({
      category: "Proceeds from Issuance of Stock",
      amount: reportData.data.financingActivities.stockIssuance,
      isHeader: false,
    });
    rows.push({
      category: "Dividends Paid",
      amount: reportData.data.financingActivities.dividends,
      isHeader: false,
    });
    rows.push({
      category: "Proceeds from Borrowing",
      amount: reportData.data.financingActivities.borrowing,
      isHeader: false,
    });
    rows.push({
      category: "Repayment of Debt",
      amount: reportData.data.financingActivities.debtRepayment,
      isHeader: false,
    });
    rows.push({
      category: "Net Cash Provided by Financing Activities",
      amount: reportData.data.financingActivities.netCash,
      isHeader: false,
    });

    // Summary
    rows.push({
      category: "Net Increase (Decrease) in Cash",
      amount: reportData.data.netChange,
      isHeader: false,
    });
    rows.push({
      category: "Cash at Beginning of Period",
      amount: reportData.data.beginningCash,
      isHeader: false,
    });
    rows.push({
      category: "Cash at End of Period",
      amount: reportData.data.endingCash,
      isHeader: false,
    });

    return {
      columns: [
        { header: "Category", dataKey: "category", width: 50, align: "left" },
        {
          header: "Amount",
          dataKey: "amount",
          width: 20,
          align: "right",
          format: "currency",
        },
      ],
      rows: rows,
      totals: {
        operating: reportData.data.operatingActivities.netCash,
        investing: reportData.data.investingActivities.netCash,
        financing: reportData.data.financingActivities.netCash,
        netChange: reportData.data.netChange,
        beginningCash: reportData.data.beginningCash,
        endingCash: reportData.data.endingCash,
      },
    };
  };

  const handleExportPDF = async () => {
    if (!reportData || !currentCompany) return;

    const tableData = prepareTableData();
    const options: ExportOptions = {
      filename: `cash-flow-statement-${filters.startDate}-to-${filters.endDate}.pdf`,
      title: "Statement of Cash Flows",
      subtitle: `From ${reportService.formatDate(
        filters.startDate
      )} to ${reportService.formatDate(filters.endDate)}`,
      companyName: currentCompany.name,
      companyAddress: currentCompany.address || "",
      reportDate: new Date().toLocaleDateString(),
      orientation: "portrait",
    };

    await exportService.exportToPDF(tableData, options);
  };

  const handleExportExcel = async () => {
    if (!reportData || !currentCompany) return;

    const tableData = prepareTableData();
    const options: ExportOptions = {
      filename: `cash-flow-statement-${filters.startDate}-to-${filters.endDate}.xlsx`,
      title: "Statement of Cash Flows",
      subtitle: `From ${reportService.formatDate(
        filters.startDate
      )} to ${reportService.formatDate(filters.endDate)}`,
      companyName: currentCompany.name,
      companyAddress: currentCompany.address || "",
      reportDate: new Date().toLocaleDateString(),
    };

    await exportService.exportToExcel(tableData, options);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center bg-white dark:bg-gray-800 py-12">
        <div className="text-red-600 dark:text-red-400 mb-4">{error}</div>
        <button
          onClick={loadReport}
          className="text-primary-600 hover:text-primary-500"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Cash Flow Statement
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Track cash inflows and outflows from{" "}
            {reportService.formatDate(filters.startDate)} to{" "}
            {reportService.formatDate(filters.endDate)}
          </p>
        </div>
        <div className="flex space-x-3">
          <div className="relative inline-block text-left">
            <div className="flex space-x-2">
              <button
                onClick={handleExport}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                CSV
              </button>
              <button
                onClick={handleExportPDF}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                PDF
              </button>
              <button
                onClick={handleExportExcel}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <TableCellsIcon className="h-4 w-4 mr-2" />
                Excel
              </button>
            </div>
          </div>
          <button
            onClick={handlePrint}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
          >
            <PrinterIcon className="h-4 w-4 mr-2" />
            Print
          </button>
        </div>
      </div>

      {/* Filters */}
      <ReportFilters
        reportType="cash-flow"
        onFiltersChange={handleFiltersChange}
      />

      {/* Report Content */}
      {reportData && reportData.data && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {currentCompany?.name}
              </h2>
              <div className="text-right">
                <div className="text-lg font-medium text-gray-900 dark:text-white">
                  Statement of Cash Flows
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  From {reportService.formatDate(reportData.startDate)} to{" "}
                  {reportService.formatDate(reportData.endDate)}
                </div>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto bg-white dark:bg-gray-800">
            <table className="min-w-full bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr className="bg-white dark:bg-gray-800">
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider"
                    style={{ width: "70%" }}
                  >
                    Category
                  </th>
                  <th
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider"
                    style={{ width: "30%" }}
                  >
                    Amount
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 dark:divide-gray-700">
                {/* Operating Activities */}
                <tr className="bg-gray-100 dark:bg-gray-700 font-bold">
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 dark:text-white">
                    Cash Flows from Operating Activities
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white text-right"></td>
                </tr>
                <tr>
                  <td className="px-8 py-3 text-sm text-gray-900 dark:text-white">
                    Net Income
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.operatingActivities.netIncome
                    )}
                  </td>
                </tr>
                <tr>
                  <td className="px-8 py-3 text-sm text-gray-900 dark:text-white">
                    Adjustments to reconcile net income to net cash provided by
                    operating activities:
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right"></td>
                </tr>
                <tr>
                  <td className="px-10 py-3 text-sm text-gray-900 dark:text-white">
                    Depreciation and Amortization
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.operatingActivities.adjustments
                        .depreciation
                    )}
                  </td>
                </tr>
                <tr>
                  <td className="px-10 py-3 text-sm text-gray-900 dark:text-white">
                    Changes in Working Capital:
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right"></td>
                </tr>
                <tr>
                  <td className="px-12 py-3 text-sm text-gray-900 dark:text-white">
                    Accounts Receivable
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.operatingActivities.adjustments?.changes
                        ?.accountsReceivable ?? 0
                    )}
                  </td>
                </tr>
                <tr>
                  <td className="px-12 py-3 text-sm text-gray-900 dark:text-white">
                    Accounts Payable
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.operatingActivities.adjustments?.changes
                        ?.accountsPayable ?? 0
                    )}
                  </td>
                </tr>
                <tr>
                  <td className="px-12 py-3 text-sm text-gray-900 dark:text-white">
                    Inventory
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.operatingActivities.adjustments?.changes
                        ?.inventory ?? 0
                    )}
                  </td>
                </tr>
                <tr>
                  <td className="px-12 py-3 text-sm text-gray-900 dark:text-white">
                    Other Current Assets
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.operatingActivities.adjustments?.changes
                        ?.otherCurrentAssets ?? 0
                    )}
                  </td>
                </tr>
                <tr>
                  <td className="px-12 py-3 text-sm text-gray-900 dark:text-white">
                    Other Current Liabilities
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.operatingActivities.adjustments?.changes
                        ?.otherCurrentLiabilities ?? 0
                    )}
                  </td>
                </tr>
                <tr className="border-t border-gray-300 dark:border-gray-600 font-bold">
                  <td className="px-8 py-4 text-sm font-bold text-gray-900 dark:text-white">
                    Net Cash Provided by Operating Activities
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.operatingActivities.netCash
                    )}
                  </td>
                </tr>

                {/* Investing Activities */}
                <tr className="bg-gray-100 dark:bg-gray-700 font-bold">
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 dark:text-white">
                    Cash Flows from Investing Activities
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white text-right"></td>
                </tr>
                <tr>
                  <td className="px-8 py-3 text-sm text-gray-900 dark:text-white">
                    Purchases of Property and Equipment
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.investingActivities.purchases
                    )}
                  </td>
                </tr>
                <tr>
                  <td className="px-8 py-3 text-sm text-gray-900 dark:text-white">
                    Proceeds from Sale of Assets
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.investingActivities.proceeds
                    )}
                  </td>
                </tr>
                <tr className="border-t border-gray-300 dark:border-gray-600 font-bold">
                  <td className="px-8 py-4 text-sm font-bold text-gray-900 dark:text-white">
                    Net Cash Used in Investing Activities
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.investingActivities.netCash
                    )}
                  </td>
                </tr>

                {/* Financing Activities */}
                <tr className="bg-gray-100 dark:bg-gray-700 font-bold">
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 dark:text-white">
                    Cash Flows from Financing Activities
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white text-right"></td>
                </tr>
                <tr>
                  <td className="px-8 py-3 text-sm text-gray-900 dark:text-white">
                    Proceeds from Issuance of Stock
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.financingActivities.stockIssuance
                    )}
                  </td>
                </tr>
                <tr>
                  <td className="px-8 py-3 text-sm text-gray-900 dark:text-white">
                    Dividends Paid
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.financingActivities.dividends
                    )}
                  </td>
                </tr>
                <tr>
                  <td className="px-8 py-3 text-sm text-gray-900 dark:text-white">
                    Proceeds from Borrowing
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.financingActivities.borrowing
                    )}
                  </td>
                </tr>
                <tr>
                  <td className="px-8 py-3 text-sm text-gray-900 dark:text-white">
                    Repayment of Debt
                  </td>
                  <td className="px-6 py-3 text-sm text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.financingActivities.debtRepayment
                    )}
                  </td>
                </tr>
                <tr className="border-t border-gray-300 dark:border-gray-600 font-bold">
                  <td className="px-8 py-4 text-sm font-bold text-gray-900 dark:text-white">
                    Net Cash Provided by Financing Activities
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.financingActivities.netCash
                    )}
                  </td>
                </tr>

                {/* Summary */}
                <tr className="border-t-2 border-gray-300 dark:border-gray-600 font-bold">
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 dark:text-white">
                    Net Increase (Decrease) in Cash
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 dark:text-white text-right">
                    <span
                      className={
                        reportData.data.netChange >= 0
                          ? "text-green-600 dark:text-green-400"
                          : "text-red-600 dark:text-red-400"
                      }
                    >
                      {reportService.formatCurrency(reportData.data.netChange)}
                    </span>
                  </td>
                </tr>
                <tr className="font-bold">
                  <td className="px-6 py-3 text-sm font-bold text-gray-900 dark:text-white">
                    Cash at Beginning of Period
                  </td>
                  <td className="px-6 py-3 text-sm font-bold text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.data.beginningCash
                    )}
                  </td>
                </tr>
                <tr className="border-t border-gray-300 dark:border-gray-600 font-bold">
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 dark:text-white">
                    Cash at End of Period
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(reportData.data.endingCash)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          {/* Summary Statistics */}
          <div className="px-6 py-4 bg-blue-50 dark:bg-blue-900/20 border-t border-gray-200 dark:border-gray-700">
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
              Summary
            </h4>
            <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="text-gray-600 dark:text-gray-400">
                  Operating Cash Flow
                </div>
                <div className="text-lg font-semibold text-gray-900 dark:text-white">
                  {reportService.formatCurrency(
                    reportData.data.operatingActivities.netCash
                  )}
                </div>
              </div>
              <div className="text-center">
                <div className="text-gray-600 dark:text-gray-400">
                  Investing Cash Flow
                </div>
                <div className="text-lg font-semibold text-gray-900 dark:text-white">
                  {reportService.formatCurrency(
                    reportData.data.investingActivities.netCash
                  )}
                </div>
              </div>
              <div className="text-center">
                <div className="text-gray-600 dark:text-gray-400">
                  Financing Cash Flow
                </div>
                <div className="text-lg font-semibold text-gray-900 dark:text-white">
                  {reportService.formatCurrency(
                    reportData.data.financingActivities.netCash
                  )}
                </div>
              </div>
              <div className="text-center">
                <div className="text-gray-600 dark:text-gray-400">
                  Net Cash Change
                </div>
                <div
                  className={`text-lg font-semibold ${
                    reportData.data.netChange >= 0
                      ? "text-green-600 dark:text-green-400"
                      : "text-red-600 dark:text-red-400"
                  }`}
                >
                  {reportService.formatCurrency(reportData.data.netChange)}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
