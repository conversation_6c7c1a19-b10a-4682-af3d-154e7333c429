import { useState, useEffect, useRef } from "react";
import { PrinterIcon, ArrowDownTrayIcon, DocumentArrowDownIcon, TableCellsIcon } from "@heroicons/react/24/outline";
import {
  reportService,
  type BalanceSheetData,
} from "../../services/reportService";
import { useCompany } from "../../contexts/CompanyContext";
import { exportService, type TableData, type ExportOptions } from "../../services/exportService";
import ReportFilters from "./ReportFilters";

export default function BalanceSheetReport() {
  const { currentCompany } = useCompany();
  const [reportData, setReportData] = useState<BalanceSheetData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    asOfDate: new Date().toISOString().split("T")[0],
  });
  const reportRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadReport();
  }, [currentCompany, filters]);

  const loadReport = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      setError(null);
      const data = await reportService.getBalanceSheet(
        currentCompany.id,
        filters
      );
      setReportData(data);
    } catch (err) {
      console.error("Failed to load balance sheet:", err);
      setError("Failed to load balance sheet. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleFiltersChange = (newFilters: any) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
  };

  const handlePrint = () => {
    reportService.printReport();
  };

  const handleExport = () => {
    if (!reportData) return;

    const exportData: any[] = [];

    // Add assets
    Object.entries(reportData.data.assets.accounts).forEach(
      ([subtype, accounts]) => {
        exportData.push({
          Section: "ASSETS",
          Subtype: reportService.getAccountSubtypeDisplayName(subtype),
          AccountCode: "",
          AccountName: "",
          Amount: "",
        });

        accounts.forEach((account: any) => {
          exportData.push({
            Section: "",
            Subtype: "",
            AccountCode: account.code,
            AccountName: account.name,
            Amount: account.current_balance,
          });
        });
      }
    );

    // Add liabilities
    Object.entries(reportData.data.liabilities.accounts).forEach(
      ([subtype, accounts]) => {
        exportData.push({
          Section: "LIABILITIES",
          Subtype: reportService.getAccountSubtypeDisplayName(subtype),
          AccountCode: "",
          AccountName: "",
          Amount: "",
        });

        accounts.forEach((account: any) => {
          exportData.push({
            Section: "",
            Subtype: "",
            AccountCode: account.code,
            AccountName: account.name,
            Amount: account.current_balance,
          });
        });
      }
    );

    // Add equity
    Object.entries(reportData.data.equity.accounts).forEach(
      ([subtype, accounts]) => {
        exportData.push({
          Section: "EQUITY",
          Subtype: reportService.getAccountSubtypeDisplayName(subtype),
          AccountCode: "",
          AccountName: "",
          Amount: "",
        });

        accounts.forEach((account: any) => {
          exportData.push({
            Section: "",
            Subtype: "",
            AccountCode: account.code,
            AccountName: account.name,
            Amount: account.current_balance,
          });
        });
      }
    );

    reportService.exportToCSV(exportData, `balance-sheet-${filters.asOfDate}`);
  };

  const prepareTableData = (): TableData => {
    if (!reportData) throw new Error('No report data available');

    const rows: any[] = [];

    // Add assets section
    Object.entries(reportData.data.assets.accounts).forEach(([subtype, accounts]) => {
      rows.push({
        section: "ASSETS",
        subtype: reportService.getAccountSubtypeDisplayName(subtype),
        accountCode: "",
        accountName: "",
        amount: "",
        isHeader: true,
      });

      accounts.forEach((account: any) => {
        rows.push({
          section: "",
          subtype: "",
          accountCode: account.code,
          accountName: account.name,
          amount: account.current_balance,
          isHeader: false,
        });
      });
    });

    rows.push({
      section: "TOTAL ASSETS",
      subtype: "",
      accountCode: "",
      accountName: "",
      amount: reportData.data.totals.totalAssets,
      isTotal: true,
    });

    // Add liabilities section
    Object.entries(reportData.data.liabilities.accounts).forEach(([subtype, accounts]) => {
      rows.push({
        section: "LIABILITIES",
        subtype: reportService.getAccountSubtypeDisplayName(subtype),
        accountCode: "",
        accountName: "",
        amount: "",
        isHeader: true,
      });

      accounts.forEach((account: any) => {
        rows.push({
          section: "",
          subtype: "",
          accountCode: account.code,
          accountName: account.name,
          amount: account.current_balance,
          isHeader: false,
        });
      });
    });

    rows.push({
      section: "TOTAL LIABILITIES",
      subtype: "",
      accountCode: "",
      accountName: "",
      amount: reportData.data.totals.totalLiabilities,
      isTotal: true,
    });

    // Add equity section
    Object.entries(reportData.data.equity.accounts).forEach(([subtype, accounts]) => {
      rows.push({
        section: "EQUITY",
        subtype: reportService.getAccountSubtypeDisplayName(subtype),
        accountCode: "",
        accountName: "",
        amount: "",
        isHeader: true,
      });

      accounts.forEach((account: any) => {
        rows.push({
          section: "",
          subtype: "",
          accountCode: account.code,
          accountName: account.name,
          amount: account.current_balance,
          isHeader: false,
        });
      });
    });

    rows.push({
      section: "TOTAL EQUITY",
      subtype: "",
      accountCode: "",
      accountName: "",
      amount: reportData.data.totals.totalEquity,
      isTotal: true,
    });

    return {
      columns: [
        { header: "Section", dataKey: "section", width: 25, align: "left" },
        { header: "Subtype", dataKey: "subtype", width: 25, align: "left" },
        { header: "Account Code", dataKey: "accountCode", width: 15, align: "left" },
        { header: "Account Name", dataKey: "accountName", width: 35, align: "left" },
        { header: "Amount", dataKey: "amount", width: 20, align: "right", format: "currency" },
      ],
      rows: rows,
      totals: {
        totalAssets: reportData.data.totals.totalAssets,
        totalLiabilities: reportData.data.totals.totalLiabilities,
        totalEquity: reportData.data.totals.totalEquity,
        totalLiabilitiesAndEquity: reportData.data.totals.totalLiabilitiesAndEquity,
      },
    };
  };

  const handleExportPDF = async () => {
    if (!reportData || !currentCompany) return;

    const tableData = prepareTableData();
    const options: ExportOptions = {
      filename: `balance-sheet-${filters.asOfDate}.pdf`,
      title: "Balance Sheet",
      subtitle: `As of ${reportService.formatDate(filters.asOfDate)}`,
      companyName: currentCompany.name,
      companyAddress: currentCompany.address,
      reportDate: new Date().toLocaleDateString(),
      orientation: 'portrait',
    };

    await exportService.exportToPDF(tableData, options);
  };

  const handleExportExcel = async () => {
    if (!reportData || !currentCompany) return;

    const tableData = prepareTableData();
    const options: ExportOptions = {
      filename: `balance-sheet-${filters.asOfDate}.xlsx`,
      title: "Balance Sheet",
      subtitle: `As of ${reportService.formatDate(filters.asOfDate)}`,
      companyName: currentCompany.name,
      companyAddress: currentCompany.address,
      reportDate: new Date().toLocaleDateString(),
    };

    await exportService.exportToExcel(tableData, options);
  };

  const renderAccountSection = (
    title: string,
    accounts: Record<string, any[]>,
    total: number
  ) => (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
        {title}
      </h3>
      {Object.entries(accounts).map(([subtype, accountList]) => (
        <div key={subtype} className="mb-4">
          <h4 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-2">
            {reportService.getAccountSubtypeDisplayName(subtype)}
          </h4>
          <div className="ml-4">
            {accountList.map((account: any) => (
              <div key={account.id} className="flex justify-between py-1">
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  {account.code} - {account.name}
                </span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {reportService.formatCurrency(account.current_balance)}
                </span>
              </div>
            ))}
          </div>
        </div>
      ))}
      <div className="border-t border-gray-300 dark:border-gray-600 pt-2 mt-4">
        <div className="flex justify-between font-semibold text-gray-900 dark:text-white">
          <span>Total {title}</span>
          <span>{reportService.formatCurrency(total)}</span>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center bg-white dark:bg-gray-800 py-12">
        <div className="text-red-600 dark:text-red-400 mb-4">{error}</div>
        <button
          onClick={loadReport}
          className="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:text-blue-400 cursor-pointer"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Balance Sheet
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Assets, liabilities, and equity as of{" "}
            {reportService.formatDate(filters.asOfDate)}
          </p>
        </div>
        <div className="flex space-x-3">
          <div className="relative inline-block text-left">
            <div className="flex space-x-2">
              <button
                onClick={handleExport}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                CSV
              </button>
              <button
                onClick={handleExportPDF}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                PDF
              </button>
              <button
                onClick={handleExportExcel}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <TableCellsIcon className="h-4 w-4 mr-2" />
                Excel
              </button>
            </div>
          </div>
          <button
            onClick={handlePrint}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
          >
            <PrinterIcon className="h-4 w-4 mr-2" />
            Print
          </button>
        </div>
      </div>

      {/* Filters */}
      <ReportFilters
        reportType="balance-sheet"
        onFiltersChange={handleFiltersChange}
      />

      {/* Report Content */}
      {reportData && (
        <div ref={reportRef} className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {currentCompany?.name}
              </h2>
              <div className="text-right">
                <div className="text-lg font-medium text-gray-900 dark:text-white">
                  Balance Sheet
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  As of {reportService.formatDate(reportData.asOfDate)}
                </div>
              </div>
            </div>
          </div>

          <div className="p-6">
            <div className="grid bg-white dark:bg-gray-800 grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Assets */}
              <div>
                {renderAccountSection(
                  "Assets",
                  reportData.data.assets.accounts,
                  reportData.data.assets.total
                )}
              </div>

              {/* Right Column - Liabilities and Equity */}
              <div>
                {renderAccountSection(
                  "Liabilities",
                  reportData.data.liabilities.accounts,
                  reportData.data.liabilities.total
                )}

                {renderAccountSection(
                  "Equity",
                  reportData.data.equity.accounts,
                  reportData.data.equity.total
                )}

                {/* Total Liabilities and Equity */}
                <div className="border-t-2 border-gray-400 dark:border-gray-500 pt-4 mt-6">
                  <div className="flex justify-between font-bold text-lg text-gray-900 dark:text-white">
                    <span>Total Liabilities and Equity</span>
                    <span>
                      {reportService.formatCurrency(
                        reportData.data.totals.totalLiabilitiesAndEquity
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Balance Check */}
            <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Total Assets</div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    {reportService.formatCurrency(
                      reportData.data.totals.totalAssets
                    )}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    Total Liabilities & Equity
                  </div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    {reportService.formatCurrency(
                      reportData.data.totals.totalLiabilitiesAndEquity
                    )}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Balance Check</div>
                  <div
                    className={`text-lg font-semibold ${
                      reportData.data.totals.isBalanced
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {reportData.data.totals.isBalanced
                      ? "✓ Balanced"
                      : "✗ Not Balanced"}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
