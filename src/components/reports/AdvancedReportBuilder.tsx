import { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  TableCellsIcon,
  FunnelIcon,
  CalendarIcon,
  DocumentArrowDownIcon,
  EyeIcon,
  Cog6ToothIcon,
} from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';
import { useNotification } from '../../contexts/NotificationContext';

interface ReportField {
  id: string;
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  table: string;
  aggregation?: 'sum' | 'count' | 'avg' | 'min' | 'max';
}

interface ReportFilter {
  id: string;
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'between';
  value: any;
  value2?: any; // For between operator
}

interface ReportSort {
  field: string;
  direction: 'asc' | 'desc';
}

interface ReportConfig {
  name: string;
  description: string;
  type: 'table' | 'chart' | 'summary';
  fields: string[];
  filters: ReportFilter[];
  groupBy: string[];
  sortBy: ReportSort[];
  chartType?: 'bar' | 'line' | 'pie' | 'area';
  dateRange?: {
    type: 'custom' | 'last_30_days' | 'last_90_days' | 'this_month' | 'this_year';
    startDate?: string;
    endDate?: string;
  };
}

interface AdvancedReportBuilderProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (report: ReportConfig) => void;
  report?: ReportConfig;
}

export default function AdvancedReportBuilder({
  isOpen,
  onClose,
  onSave,
  report,
}: AdvancedReportBuilderProps) {
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    name: '',
    description: '',
    type: 'table',
    fields: [],
    filters: [],
    groupBy: [],
    sortBy: [],
    dateRange: { type: 'last_30_days' },
  });

  const [availableFields, setAvailableFields] = useState<ReportField[]>([]);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'fields' | 'filters' | 'grouping' | 'preview'>('fields');

  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();

  useEffect(() => {
    if (isOpen) {
      loadAvailableFields();
      if (report) {
        setReportConfig(report);
      } else {
        resetReport();
      }
    }
  }, [isOpen, report]);

  const loadAvailableFields = () => {
    // Mock available fields - in real app, this would come from API
    const fields: ReportField[] = [
      { id: 'transaction_date', name: 'Transaction Date', type: 'date', table: 'transactions' },
      { id: 'description', name: 'Description', type: 'string', table: 'transactions' },
      { id: 'reference', name: 'Reference', type: 'string', table: 'transactions' },
      { id: 'total_amount', name: 'Total Amount', type: 'number', table: 'transactions' },
      { id: 'status', name: 'Status', type: 'string', table: 'transactions' },
      { id: 'account_code', name: 'Account Code', type: 'string', table: 'accounts' },
      { id: 'account_name', name: 'Account Name', type: 'string', table: 'accounts' },
      { id: 'account_type', name: 'Account Type', type: 'string', table: 'accounts' },
      { id: 'debit_amount', name: 'Debit Amount', type: 'number', table: 'transaction_entries' },
      { id: 'credit_amount', name: 'Credit Amount', type: 'number', table: 'transaction_entries' },
      { id: 'created_by', name: 'Created By', type: 'string', table: 'transactions' },
      { id: 'created_at', name: 'Created At', type: 'date', table: 'transactions' },
    ];
    setAvailableFields(fields);
  };

  const resetReport = () => {
    setReportConfig({
      name: '',
      description: '',
      type: 'table',
      fields: [],
      filters: [],
      groupBy: [],
      sortBy: [],
      dateRange: { type: 'last_30_days' },
    });
  };

  const addField = (fieldId: string) => {
    if (!reportConfig.fields.includes(fieldId)) {
      setReportConfig(prev => ({
        ...prev,
        fields: [...prev.fields, fieldId],
      }));
    }
  };

  const removeField = (fieldId: string) => {
    setReportConfig(prev => ({
      ...prev,
      fields: prev.fields.filter(f => f !== fieldId),
    }));
  };

  const addFilter = () => {
    const newFilter: ReportFilter = {
      id: `filter-${Date.now()}`,
      field: availableFields[0]?.id || '',
      operator: 'equals',
      value: '',
    };
    setReportConfig(prev => ({
      ...prev,
      filters: [...prev.filters, newFilter],
    }));
  };

  const updateFilter = (filterId: string, updates: Partial<ReportFilter>) => {
    setReportConfig(prev => ({
      ...prev,
      filters: prev.filters.map(f => f.id === filterId ? { ...f, ...updates } : f),
    }));
  };

  const removeFilter = (filterId: string) => {
    setReportConfig(prev => ({
      ...prev,
      filters: prev.filters.filter(f => f.id !== filterId),
    }));
  };

  const addGroupBy = (fieldId: string) => {
    if (!reportConfig.groupBy.includes(fieldId)) {
      setReportConfig(prev => ({
        ...prev,
        groupBy: [...prev.groupBy, fieldId],
      }));
    }
  };

  const removeGroupBy = (fieldId: string) => {
    setReportConfig(prev => ({
      ...prev,
      groupBy: prev.groupBy.filter(f => f !== fieldId),
    }));
  };

  const addSort = (fieldId: string, direction: 'asc' | 'desc') => {
    setReportConfig(prev => ({
      ...prev,
      sortBy: [...prev.sortBy.filter(s => s.field !== fieldId), { field: fieldId, direction }],
    }));
  };

  const removeSort = (fieldId: string) => {
    setReportConfig(prev => ({
      ...prev,
      sortBy: prev.sortBy.filter(s => s.field !== fieldId),
    }));
  };

  const generatePreview = async () => {
    if (!currentCompany || reportConfig.fields.length === 0) return;

    setLoading(true);
    try {
      // Mock preview data - in real app, this would call the reporting API
      const mockData = [
        {
          transaction_date: '2024-01-15',
          description: 'Office Supplies',
          total_amount: 250.00,
          status: 'POSTED',
          account_name: 'Office Expenses',
        },
        {
          transaction_date: '2024-01-16',
          description: 'Client Payment',
          total_amount: 1500.00,
          status: 'POSTED',
          account_name: 'Accounts Receivable',
        },
        {
          transaction_date: '2024-01-17',
          description: 'Rent Payment',
          total_amount: 2000.00,
          status: 'POSTED',
          account_name: 'Rent Expense',
        },
      ];

      setPreviewData(mockData);
      setActiveTab('preview');
    } catch (error) {
      console.error('Failed to generate preview:', error);
      showNotification({
        type: 'error',
        title: 'Preview failed',
        message: 'Could not generate report preview',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = () => {
    if (!reportConfig.name.trim()) {
      showNotification({
        type: 'error',
        title: 'Report name is required',
        message: 'Please enter a report name',
      });
      return;
    }

    if (reportConfig.fields.length === 0) {
      showNotification({
        type: 'error',
        title: 'At least one field is required',
        message: 'Please select at least one field for the report',
      });
      return;
    }

    onSave(reportConfig);
    showNotification({
      type: 'success',
      title: 'Report saved',
      message: 'Report configuration has been saved successfully',
    });
  };

  const getFieldName = (fieldId: string) => {
    return availableFields.find(f => f.id === fieldId)?.name || fieldId;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <ChartBarIcon className="h-6 w-6 text-primary-600 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Advanced Report Builder
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            ×
          </button>
        </div>

        <div className="flex h-[calc(90vh-140px)]">
          {/* Sidebar */}
          <div className="w-80 border-r border-gray-200 dark:border-gray-700 p-6 overflow-y-auto">
            <div className="space-y-6">
              {/* Basic Info */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Report Details
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Name *
                    </label>
                    <input
                      type="text"
                      value={reportConfig.name}
                      onChange={(e) => setReportConfig(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Enter report name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Description
                    </label>
                    <textarea
                      value={reportConfig.description}
                      onChange={(e) => setReportConfig(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Enter report description"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Report Type
                    </label>
                    <select
                      value={reportConfig.type}
                      onChange={(e) => setReportConfig(prev => ({ ...prev, type: e.target.value as any }))}
                      className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="table">Table</option>
                      <option value="chart">Chart</option>
                      <option value="summary">Summary</option>
                    </select>
                  </div>

                  {reportConfig.type === 'chart' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Chart Type
                      </label>
                      <select
                        value={reportConfig.chartType || 'bar'}
                        onChange={(e) => setReportConfig(prev => ({ ...prev, chartType: e.target.value as any }))}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="bar">Bar Chart</option>
                        <option value="line">Line Chart</option>
                        <option value="pie">Pie Chart</option>
                        <option value="area">Area Chart</option>
                      </select>
                    </div>
                  )}
                </div>
              </div>

              {/* Date Range */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Date Range
                </h3>
                <div className="space-y-4">
                  <select
                    value={reportConfig.dateRange?.type || 'last_30_days'}
                    onChange={(e) => setReportConfig(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange, type: e.target.value as any }
                    }))}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="last_30_days">Last 30 Days</option>
                    <option value="last_90_days">Last 90 Days</option>
                    <option value="this_month">This Month</option>
                    <option value="this_year">This Year</option>
                    <option value="custom">Custom Range</option>
                  </select>

                  {reportConfig.dateRange?.type === 'custom' && (
                    <div className="grid grid-cols-1 gap-2">
                      <input
                        type="date"
                        value={reportConfig.dateRange.startDate || ''}
                        onChange={(e) => setReportConfig(prev => ({
                          ...prev,
                          dateRange: { ...prev.dateRange, startDate: e.target.value }
                        }))}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                      <input
                        type="date"
                        value={reportConfig.dateRange.endDate || ''}
                        onChange={(e) => setReportConfig(prev => ({
                          ...prev,
                          dateRange: { ...prev.dateRange, endDate: e.target.value }
                        }))}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Tabs */}
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="flex space-x-8 px-6">
                {[
                  { id: 'fields', name: 'Fields', icon: TableCellsIcon },
                  { id: 'filters', name: 'Filters', icon: FunnelIcon },
                  { id: 'grouping', name: 'Grouping', icon: Cog6ToothIcon },
                  { id: 'preview', name: 'Preview', icon: EyeIcon },
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                        : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                    }`}
                  >
                    <tab.icon className="h-5 w-5 mr-2" />
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="flex-1 p-6 overflow-y-auto">
              {/* Fields Tab */}
              {activeTab === 'fields' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Select Fields
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Available Fields */}
                      <div>
                        <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">
                          Available Fields
                        </h4>
                        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 max-h-80 overflow-y-auto">
                          {availableFields.map((field) => (
                            <div
                              key={field.id}
                              className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded cursor-pointer"
                              onClick={() => addField(field.id)}
                            >
                              <div>
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {field.name}
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  {field.table} • {field.type}
                                </div>
                              </div>
                              {!reportConfig.fields.includes(field.id) && (
                                <button className="text-primary-600 hover:text-primary-700">
                                  Add
                                </button>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Selected Fields */}
                      <div>
                        <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">
                          Selected Fields ({reportConfig.fields.length})
                        </h4>
                        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 max-h-80 overflow-y-auto">
                          {reportConfig.fields.length === 0 ? (
                            <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                              No fields selected
                            </div>
                          ) : (
                            reportConfig.fields.map((fieldId) => (
                              <div
                                key={fieldId}
                                className="flex items-center justify-between p-2 bg-primary-50 dark:bg-primary-900/20 rounded mb-2"
                              >
                                <span className="text-sm font-medium text-gray-900 dark:text-white">
                                  {getFieldName(fieldId)}
                                </span>
                                <button
                                  onClick={() => removeField(fieldId)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  Remove
                                </button>
                              </div>
                            ))
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Additional tabs content will be added in next chunk */}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={generatePreview}
            disabled={loading || reportConfig.fields.length === 0}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
          >
            <EyeIcon className="h-4 w-4 mr-2" />
            {loading ? 'Generating...' : 'Preview'}
          </button>

          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              Save Report
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
