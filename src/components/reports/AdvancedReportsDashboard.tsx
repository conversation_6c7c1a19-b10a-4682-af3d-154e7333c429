import { useState, useRef, useEffect } from 'react';
import {
  ChartBarIcon,
  DocumentArrowDownIcon,
  TableCellsIcon,
  ArrowDownTrayIcon,
  PrinterIcon,
  CalendarIcon,
  FunnelIcon,
  ChartPieIcon,
  PresentationChartLineIcon,
  PlayIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';
import { useNotification } from '../../contexts/NotificationContext';
import { exportService, type ExportOptions } from '../../services/exportService';
import { reportService } from '../../services/reportService';

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  category: 'financial' | 'operational' | 'analytical';
  formats: ('pdf' | 'excel' | 'csv')[];
  frequency: string[];
  complexity: 'basic' | 'intermediate' | 'advanced';
}

interface ReportExecution {
  id: string;
  reportId: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';
  progress: number;
  startedAt: string;
  completedAt?: string;
  downloadUrl?: string;
  error?: string;
}

export default function AdvancedReportsDashboard() {
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  const [selectedReports, setSelectedReports] = useState<string[]>([]);
  const [exportFormat, setExportFormat] = useState<'pdf' | 'excel' | 'csv'>('pdf');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });
  const [isExporting, setIsExporting] = useState(false);
  const [reportExecutions, setReportExecutions] = useState<ReportExecution[]>([]);
  const [activeTab, setActiveTab] = useState<'templates' | 'executions'>('templates');
  const dashboardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (currentCompany) {
      loadRecentExecutions();
    }
  }, [currentCompany]);

  const loadRecentExecutions = async () => {
    if (!currentCompany) return;

    try {
      // In a real implementation, this would fetch from the API
      const mockExecutions: ReportExecution[] = [
        {
          id: 'exec-1',
          reportId: 'comprehensive-financial-package',
          status: 'COMPLETED',
          progress: 100,
          startedAt: new Date(Date.now() - 3600000).toISOString(),
          completedAt: new Date(Date.now() - 3300000).toISOString(),
          downloadUrl: '/downloads/financial-package-2024.pdf',
        },
        {
          id: 'exec-2',
          reportId: 'cash-flow-analysis',
          status: 'RUNNING',
          progress: 65,
          startedAt: new Date(Date.now() - 300000).toISOString(),
        },
        {
          id: 'exec-3',
          reportId: 'kpi-dashboard',
          status: 'FAILED',
          progress: 0,
          startedAt: new Date(Date.now() - 1800000).toISOString(),
          error: 'Insufficient data for the selected period',
        },
      ];
      setReportExecutions(mockExecutions);
    } catch (error) {
      console.error('Failed to load report executions:', error);
    }
  };

  const reportTemplates: ReportTemplate[] = [
    {
      id: 'comprehensive-financial-package',
      name: 'Comprehensive Financial Package',
      description: 'Complete financial statements with analysis',
      icon: ChartBarIcon,
      category: 'financial',
      formats: ['pdf', 'excel'],
      frequency: ['monthly', 'quarterly', 'annually'],
      complexity: 'advanced',
    },
    {
      id: 'executive-summary',
      name: 'Executive Summary Report',
      description: 'High-level financial overview for executives',
      icon: PresentationChartLineIcon,
      category: 'analytical',
      formats: ['pdf', 'excel'],
      frequency: ['monthly', 'quarterly'],
      complexity: 'intermediate',
    },
    {
      id: 'cash-flow-analysis',
      name: 'Cash Flow Analysis',
      description: 'Detailed cash flow patterns and projections',
      icon: ChartPieIcon,
      category: 'financial',
      formats: ['pdf', 'excel', 'csv'],
      frequency: ['weekly', 'monthly'],
      complexity: 'advanced',
    },
    {
      id: 'variance-analysis',
      name: 'Budget vs Actual Variance',
      description: 'Compare actual performance against budget',
      icon: FunnelIcon,
      category: 'analytical',
      formats: ['pdf', 'excel'],
      frequency: ['monthly', 'quarterly'],
      complexity: 'intermediate',
    },
    {
      id: 'kpi-dashboard',
      name: 'Key Performance Indicators',
      description: 'Critical business metrics and trends',
      icon: ChartBarIcon,
      category: 'operational',
      formats: ['pdf', 'excel'],
      frequency: ['daily', 'weekly', 'monthly'],
      complexity: 'basic',
    },
    {
      id: 'regulatory-compliance',
      name: 'Regulatory Compliance Report',
      description: 'Reports for tax and regulatory requirements',
      icon: DocumentArrowDownIcon,
      category: 'financial',
      formats: ['pdf', 'excel'],
      frequency: ['quarterly', 'annually'],
      complexity: 'advanced',
    },
  ];

  const handleReportSelection = (reportId: string) => {
    setSelectedReports(prev => 
      prev.includes(reportId) 
        ? prev.filter(id => id !== reportId)
        : [...prev, reportId]
    );
  };

  const handleExportSelected = async () => {
    if (selectedReports.length === 0) return;

    setIsExporting(true);
    try {
      // Create execution records for tracking
      const newExecutions: ReportExecution[] = selectedReports.map(reportId => ({
        id: `exec-${Date.now()}-${reportId}`,
        reportId,
        status: 'PENDING' as const,
        progress: 0,
        startedAt: new Date().toISOString(),
      }));

      setReportExecutions(prev => [...newExecutions, ...prev]);

      const reports = [];

      for (let i = 0; i < selectedReports.length; i++) {
        const reportId = selectedReports[i];
        const execution = newExecutions[i];
        const template = reportTemplates.find(t => t.id === reportId);
        if (!template) continue;

        // Update execution status to running
        setReportExecutions(prev =>
          prev.map(exec =>
            exec.id === execution.id
              ? { ...exec, status: 'RUNNING' as const, progress: 25 }
              : exec
          )
        );

        try {
          // Generate report data based on template - this would call real API
          const reportData = await generateReportData(reportId);

          // Update progress
          setReportExecutions(prev =>
            prev.map(exec =>
              exec.id === execution.id
                ? { ...exec, progress: 75 }
                : exec
            )
          );

          reports.push({
            title: template.name,
            tableData: reportData,
          });

          // Mark as completed
          setReportExecutions(prev =>
            prev.map(exec =>
              exec.id === execution.id
                ? {
                    ...exec,
                    status: 'COMPLETED' as const,
                    progress: 100,
                    completedAt: new Date().toISOString(),
                    downloadUrl: `/downloads/${reportId}-${Date.now()}.${exportFormat}`
                  }
                : exec
            )
          );

        } catch (error) {
          // Mark as failed
          setReportExecutions(prev =>
            prev.map(exec =>
              exec.id === execution.id
                ? {
                    ...exec,
                    status: 'FAILED' as const,
                    error: error instanceof Error ? error.message : 'Unknown error'
                  }
                : exec
            )
          );
        }
      }

      if (reports.length > 0) {
        const options: ExportOptions = {
          filename: `financial-reports-${dateRange.startDate}-to-${dateRange.endDate}.${exportFormat}`,
          title: 'Financial Reports Package',
          subtitle: `${dateRange.startDate} to ${dateRange.endDate}`,
          companyName: currentCompany?.name,
          companyAddress: currentCompany?.address,
          reportDate: new Date().toLocaleDateString(),
          orientation: 'portrait',
        };

        if (exportFormat === 'pdf') {
          await exportService.exportMultiReportPDF(reports, options);
        } else if (exportFormat === 'excel') {
          // For Excel, we'll export each report as a separate sheet
          for (const report of reports) {
            const excelOptions = {
              ...options,
              filename: `${report.title.toLowerCase().replace(/\s+/g, '-')}-${dateRange.startDate}.xlsx`,
              title: report.title,
            };
            await exportService.exportToExcel(report.tableData!, excelOptions);
          }
        }

        showNotification({
          type: 'success',
          title: 'Reports Generated',
          message: `Successfully generated ${reports.length} report(s)`,
        });
      }

    } catch (error) {
      console.error('Export failed:', error);
      showNotification({
        type: 'error',
        title: 'Export Failed',
        message: 'Failed to generate reports. Please try again.',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const generateReportData = async (reportId: string) => {
    if (!currentCompany) throw new Error('No company selected');

    // Try to fetch real data from the API first
    if (reportId === 'comprehensive-financial-package') {
      try {
        const balanceSheet = await reportService.getBalanceSheet(currentCompany.id, {
          asOfDate: dateRange.endDate,
        });
        const incomeStatement = await reportService.getIncomeStatement(currentCompany.id, {
          startDate: dateRange.startDate,
          endDate: dateRange.endDate,
        });

        // Combine real data into comprehensive package
        return {
          columns: [
            { header: 'Statement', dataKey: 'statement', width: 40, align: 'left' as const },
            { header: 'Total Assets', dataKey: 'assets', width: 20, align: 'right' as const, format: 'currency' as const },
            { header: 'Total Liabilities', dataKey: 'liabilities', width: 20, align: 'right' as const, format: 'currency' as const },
            { header: 'Net Income', dataKey: 'netIncome', width: 20, align: 'right' as const, format: 'currency' as const },
          ],
          rows: [
            {
              statement: 'Balance Sheet',
              assets: balanceSheet.totalAssets || 0,
              liabilities: balanceSheet.totalLiabilities || 0,
              netIncome: (balanceSheet.totalAssets || 0) - (balanceSheet.totalLiabilities || 0)
            },
            {
              statement: 'Income Statement',
              assets: incomeStatement.totalRevenue || 0,
              liabilities: incomeStatement.totalExpenses || 0,
              netIncome: incomeStatement.netIncome || 0
            },
          ],
          totals: {
            assets: (balanceSheet.totalAssets || 0) + (incomeStatement.totalRevenue || 0),
            liabilities: (balanceSheet.totalLiabilities || 0) + (incomeStatement.totalExpenses || 0),
            netIncome: incomeStatement.netIncome || 0
          },
        };
      } catch (error) {
        console.warn('Failed to fetch real financial data, using fallback:', error);
      }
    }

    if (reportId === 'cash-flow-analysis') {
      try {
        // In a real implementation, this would fetch cash flow data
        // For now, we'll simulate an API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        return {
          columns: [
            { header: 'Category', dataKey: 'category', width: 40, align: 'left' as const },
            { header: 'Inflow', dataKey: 'inflow', width: 20, align: 'right' as const, format: 'currency' as const },
            { header: 'Outflow', dataKey: 'outflow', width: 20, align: 'right' as const, format: 'currency' as const },
            { header: 'Net Flow', dataKey: 'netFlow', width: 20, align: 'right' as const, format: 'currency' as const },
          ],
          rows: [
            { category: 'Operating Activities', inflow: 180000, outflow: 150000, netFlow: 30000 },
            { category: 'Investing Activities', inflow: 5000, outflow: 25000, netFlow: -20000 },
            { category: 'Financing Activities', inflow: 50000, outflow: 15000, netFlow: 35000 },
          ],
          totals: { inflow: 235000, outflow: 190000, netFlow: 45000 },
        };
      } catch (error) {
        console.warn('Failed to fetch cash flow data, using fallback:', error);
      }
    }

    // Fallback to mock data
    switch (reportId) {
      case 'comprehensive-financial-package':
        return {
          columns: [
            { header: 'Account', dataKey: 'account', width: 40, align: 'left' as const },
            { header: 'Current Period', dataKey: 'current', width: 20, align: 'right' as const, format: 'currency' as const },
            { header: 'Prior Period', dataKey: 'prior', width: 20, align: 'right' as const, format: 'currency' as const },
            { header: 'Variance', dataKey: 'variance', width: 20, align: 'right' as const, format: 'currency' as const },
          ],
          rows: [
            { account: 'Total Revenue', current: 150000, prior: 140000, variance: 10000 },
            { account: 'Total Expenses', current: 120000, prior: 115000, variance: 5000 },
            { account: 'Net Income', current: 30000, prior: 25000, variance: 5000 },
          ],
          totals: { current: 30000, prior: 25000, variance: 5000 },
        };

      case 'cash-flow-analysis':
        return {
          columns: [
            { header: 'Category', dataKey: 'category', width: 40, align: 'left' as const },
            { header: 'Inflow', dataKey: 'inflow', width: 20, align: 'right' as const, format: 'currency' as const },
            { header: 'Outflow', dataKey: 'outflow', width: 20, align: 'right' as const, format: 'currency' as const },
            { header: 'Net Flow', dataKey: 'netFlow', width: 20, align: 'right' as const, format: 'currency' as const },
          ],
          rows: [
            { category: 'Operating Activities', inflow: 180000, outflow: 150000, netFlow: 30000 },
            { category: 'Investing Activities', inflow: 5000, outflow: 25000, netFlow: -20000 },
            { category: 'Financing Activities', inflow: 50000, outflow: 15000, netFlow: 35000 },
          ],
          totals: { inflow: 235000, outflow: 190000, netFlow: 45000 },
        };

      default:
        return {
          columns: [
            { header: 'Metric', dataKey: 'metric', width: 50, align: 'left' as const },
            { header: 'Value', dataKey: 'value', width: 25, align: 'right' as const, format: 'currency' as const },
            { header: 'Target', dataKey: 'target', width: 25, align: 'right' as const, format: 'currency' as const },
          ],
          rows: [
            { metric: 'Revenue Growth', value: 15.5, target: 12.0 },
            { metric: 'Profit Margin', value: 18.2, target: 15.0 },
            { metric: 'Cash Position', value: 125000, target: 100000 },
          ],
        };
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'basic': return 'bg-green-100 dark:bg-green-900/20 text-green-800';
      case 'intermediate': return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800';
      case 'advanced': return 'bg-red-100 dark:bg-red-900/20 text-red-800';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'financial': return ChartBarIcon;
      case 'operational': return FunnelIcon;
      case 'analytical': return ChartPieIcon;
      default: return DocumentArrowDownIcon;
    }
  };

  return (
    <div ref={dashboardRef} className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Advanced Financial Reports & Analytics
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Generate comprehensive financial reports with real-time execution tracking
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <CalendarIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              className="border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded-md px-3 py-2 text-sm"
            />
            <span className="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">to</span>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              className="border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded-md px-3 py-2 text-sm"
            />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          <button
            onClick={() => setActiveTab('templates')}
            className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
              activeTab === 'templates'
                ? "border-primary-500 text-primary-600 dark:text-primary-400"
                : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
            }`}
          >
            <DocumentArrowDownIcon className="h-4 w-4" />
            <span>Report Templates</span>
            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
              activeTab === 'templates'
                ? "bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400"
                : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
            }`}>
              {reportTemplates.length}
            </span>
          </button>
          <button
            onClick={() => setActiveTab('executions')}
            className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
              activeTab === 'executions'
                ? "border-primary-500 text-primary-600 dark:text-primary-400"
                : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
            }`}
          >
            <ClockIcon className="h-4 w-4" />
            <span>Execution History</span>
            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
              activeTab === 'executions'
                ? "bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400"
                : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
            }`}>
              {reportExecutions.length}
            </span>
          </button>
        </nav>
      </div>

      {activeTab === 'templates' && (
        <>
          {/* Export Controls */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Export Options</h2>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Format:</label>
              <select
                value={exportFormat}
                onChange={(e) => setExportFormat(e.target.value as 'pdf' | 'excel' | 'csv')}
                className="border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded-md px-3 py-2 text-sm"
              >
                <option value="pdf">PDF</option>
                <option value="excel">Excel</option>
                <option value="csv">CSV</option>
              </select>
            </div>
            <button
              onClick={handleExportSelected}
              disabled={selectedReports.length === 0 || isExporting}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isExporting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Exporting...
                </>
              ) : (
                <>
                  <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                  Export Selected ({selectedReports.length})
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Report Templates */}
      <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {reportTemplates.map((template) => {
          const IconComponent = template.icon;
          const CategoryIcon = getCategoryIcon(template.category);
          const isSelected = selectedReports.includes(template.id);

          return (
            <div
              key={template.id}
              className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 cursor-pointer transition-all duration-200 ${
                isSelected 
                  ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                  : 'hover:shadow-md hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700'
              }`}
              onClick={() => handleReportSelection(template.id)}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg ${isSelected ? 'bg-blue-100 dark:bg-blue-900/20' : 'bg-gray-100 dark:bg-gray-700'}`}>
                    <IconComponent className={`h-6 w-6 ${isSelected ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500'}`} />
                  </div>
                  <div className="ml-3">
                    <CategoryIcon className="h-4 w-4 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getComplexityColor(template.complexity)}`}>
                    {template.complexity}
                  </span>
                  {isSelected && (
                    <div className="h-5 w-5 bg-blue-600 rounded-full flex items-center justify-center">
                      <svg className="h-3 w-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">{template.name}</h3>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{template.description}</p>
              </div>

              <div className="mt-4">
                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  <div className="flex items-center space-x-2">
                    <span>Formats:</span>
                    <div className="flex space-x-1">
                      {template.formats.map(format => (
                        <span key={format} className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                          {format.toUpperCase()}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span>Frequency:</span>
                    <span className="font-medium">{template.frequency.join(', ')}</span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Quick Actions</h2>
        <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700">
            <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
            Generate Monthly Package
          </button>
          <button className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700">
            <TableCellsIcon className="h-5 w-5 mr-2" />
            Export All to Excel
          </button>
          <button className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700">
            <PrinterIcon className="h-5 w-5 mr-2" />
            Schedule Reports
          </button>
        </div>
      </div>
        </>
      )}

      {activeTab === 'executions' && (
        <div className="space-y-6">
          {/* Execution History */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">Report Execution History</h2>
                <button
                  onClick={loadRecentExecutions}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <ArrowPathIcon className="h-4 w-4 mr-2" />
                  Refresh
                </button>
              </div>
            </div>
            <div className="p-6">
              {reportExecutions.length === 0 ? (
                <div className="text-center py-8">
                  <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No executions yet</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Generate your first report to see execution history here.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {reportExecutions.map((execution) => {
                    const template = reportTemplates.find(t => t.id === execution.reportId);
                    const statusIcon = {
                      PENDING: ClockIcon,
                      RUNNING: PlayIcon,
                      COMPLETED: CheckCircleIcon,
                      FAILED: ExclamationTriangleIcon,
                    }[execution.status];
                    const StatusIcon = statusIcon;

                    const statusColor = {
                      PENDING: 'text-yellow-500',
                      RUNNING: 'text-blue-500',
                      COMPLETED: 'text-green-500',
                      FAILED: 'text-red-500',
                    }[execution.status];

                    return (
                      <div
                        key={execution.id}
                        className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                      >
                        <div className="flex items-center space-x-4">
                          <div className={`p-2 rounded-lg ${statusColor.replace('text-', 'bg-').replace('-500', '-100')} dark:bg-opacity-20`}>
                            <StatusIcon className={`h-5 w-5 ${statusColor}`} />
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                              {template?.name || execution.reportId}
                            </h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Started: {new Date(execution.startedAt).toLocaleString()}
                            </p>
                            {execution.completedAt && (
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                Completed: {new Date(execution.completedAt).toLocaleString()}
                              </p>
                            )}
                            {execution.error && (
                              <p className="text-sm text-red-600 dark:text-red-400">
                                Error: {execution.error}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          {execution.status === 'RUNNING' && (
                            <div className="flex items-center space-x-2">
                              <div className="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div
                                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                  style={{ width: `${execution.progress}%` }}
                                ></div>
                              </div>
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                {execution.progress}%
                              </span>
                            </div>
                          )}
                          {execution.status === 'COMPLETED' && execution.downloadUrl && (
                            <button
                              onClick={() => window.open(execution.downloadUrl, '_blank')}
                              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900/20 dark:text-primary-400 dark:hover:bg-primary-900/30"
                            >
                              <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                              Download
                            </button>
                          )}
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            execution.status === 'COMPLETED' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                            execution.status === 'RUNNING' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                            execution.status === 'FAILED' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                          }`}>
                            {execution.status}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
