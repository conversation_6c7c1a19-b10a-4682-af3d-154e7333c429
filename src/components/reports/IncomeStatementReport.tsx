import { useState, useEffect } from "react";
import {
  PrinterIcon,
  ArrowDownTrayIcon,
  DocumentArrowDownIcon,
  TableCellsIcon,
} from "@heroicons/react/24/outline";
import {
  reportService,
  type IncomeStatementData,
} from "../../services/reportService";
import { useCompany } from "../../contexts/CompanyContext";
import {
  exportService,
  type TableData,
  type ExportOptions,
} from "../../services/exportService";
import ReportFilters from "./ReportFilters";

export default function IncomeStatementReport() {
  const { currentCompany } = useCompany();
  const [reportData, setReportData] = useState<IncomeStatementData | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
  });

  useEffect(() => {
    loadReport();
  }, [currentCompany, filters]);

  const loadReport = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      setError(null);
      const data = await reportService.getIncomeStatement(
        currentCompany.id,
        filters
      );
      setReportData(data);
    } catch (err) {
      console.error("Failed to load income statement:", err);
      setError("Failed to load income statement. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleFiltersChange = (newFilters: any) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
  };

  const handlePrint = () => {
    reportService.printReport();
  };

  const handleExport = () => {
    if (!reportData || !reportData.data || !reportData.totals) return;

    const exportData: any[] = [];

    // Add revenue accounts
    exportData.push({
      Section: "REVENUE",
      AccountCode: "",
      AccountName: "",
      Amount: "",
    });

    Object.values(reportData.data.revenue.accounts).forEach((account: any) => {
      exportData.push({
        Section: "",
        AccountCode: account.accountCode,
        AccountName: account.accountName,
        Amount: account.total,
      });
    });

    exportData.push({
      Section: "TOTAL REVENUE",
      AccountCode: "",
      AccountName: "",
      Amount: reportData.totals.totalRevenue,
    });

    // Add expense accounts
    exportData.push({
      Section: "EXPENSES",
      AccountCode: "",
      AccountName: "",
      Amount: "",
    });

    Object.values(reportData.data.expenses.accounts).forEach((account: any) => {
      exportData.push({
        Section: "",
        AccountCode: account.accountCode,
        AccountName: account.accountName,
        Amount: account.total,
      });
    });

    exportData.push({
      Section: "TOTAL EXPENSES",
      AccountCode: "",
      AccountName: "",
      Amount: reportData.totals.totalExpenses,
    });

    exportData.push({
      Section: "NET INCOME",
      AccountCode: "",
      AccountName: "",
      Amount: reportData.totals.netIncome,
    });

    reportService.exportToCSV(
      exportData,
      `income-statement-${filters.startDate}-to-${filters.endDate}`
    );
  };

  const prepareTableData = (): TableData => {
    if (!reportData || !reportData.data || !reportData.totals) {
      throw new Error("No report data available");
    }

    const rows: any[] = [];

    // Add revenue section
    Object.values(reportData.data.revenue.accounts).forEach((account: any) => {
      rows.push({
        section: "REVENUE",
        accountCode: account.accountCode,
        accountName: account.accountName,
        amount: account.total,
        isHeader: false,
      });
    });

    rows.push({
      section: "TOTAL REVENUE",
      accountCode: "",
      accountName: "",
      amount: reportData.totals.totalRevenue,
      isTotal: true,
    });

    // Add expenses section
    Object.values(reportData.data.expenses.accounts).forEach((account: any) => {
      rows.push({
        section: "EXPENSES",
        accountCode: account.accountCode,
        accountName: account.accountName,
        amount: account.total,
        isHeader: false,
      });
    });

    rows.push({
      section: "TOTAL EXPENSES",
      accountCode: "",
      accountName: "",
      amount: reportData.totals.totalExpenses,
      isTotal: true,
    });

    rows.push({
      section: "NET INCOME",
      accountCode: "",
      accountName: "",
      amount: reportData.totals.netIncome,
      isTotal: true,
    });

    return {
      columns: [
        { header: "Section", dataKey: "section", width: 25, align: "left" },
        {
          header: "Account Code",
          dataKey: "accountCode",
          width: 15,
          align: "left",
        },
        {
          header: "Account Name",
          dataKey: "accountName",
          width: 35,
          align: "left",
        },
        {
          header: "Amount",
          dataKey: "amount",
          width: 20,
          align: "right",
          format: "currency",
        },
      ],
      rows: rows,
      totals: {
        totalRevenue: reportData.totals.totalRevenue,
        totalExpenses: reportData.totals.totalExpenses,
        netIncome: reportData.totals.netIncome,
      },
    };
  };

  const handleExportPDF = async () => {
    if (!reportData || !currentCompany) return;

    const tableData = prepareTableData();
    const options: ExportOptions = {
      filename: `income-statement-${filters.startDate}-to-${filters.endDate}.pdf`,
      title: "Income Statement",
      subtitle: `Period: ${reportService.getReportPeriodDisplay(
        filters.startDate,
        filters.endDate
      )}`,
      companyName: currentCompany.name,
      companyAddress: currentCompany.address,
      reportDate: new Date().toLocaleDateString(),
      orientation: "portrait",
    };

    await exportService.exportToPDF(tableData, options);
  };

  const handleExportExcel = async () => {
    if (!reportData || !currentCompany) return;

    const tableData = prepareTableData();
    const options: ExportOptions = {
      filename: `income-statement-${filters.startDate}-to-${filters.endDate}.xlsx`,
      title: "Income Statement",
      subtitle: `Period: ${reportService.getReportPeriodDisplay(
        filters.startDate,
        filters.endDate
      )}`,
      companyName: currentCompany.name,
      companyAddress: currentCompany.address,
      reportDate: new Date().toLocaleDateString(),
    };

    await exportService.exportToExcel(tableData, options);
  };

  const renderAccountSection = (
    title: string,
    accounts: Record<string, any>,
    total: number,
    isExpense: boolean = false
  ) => (
    <div className="mb-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
        {title}
      </h3>
      <div className="space-y-1">
        {Object.values(accounts).map((account: any) => (
          <div key={account.accountId} className="flex justify-between py-1">
            <span className="text-sm text-gray-700 dark:text-gray-300">
              {account.accountCode} - {account.accountName}
            </span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {reportService.formatCurrency(account.total)}
            </span>
          </div>
        ))}
      </div>
      <div className="border-t border-gray-300 dark:border-gray-600 pt-2 mt-4">
        <div className="flex justify-between font-semibold text-gray-900 dark:text-white">
          <span>Total {title}</span>
          <span
            className={
              isExpense
                ? "text-red-600 dark:text-red-400"
                : "text-green-600 dark:text-green-400"
            }
          >
            {reportService.formatCurrency(total)}
          </span>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center bg-white dark:bg-gray-800 py-12">
        <div className="text-red-600 dark:text-red-400 mb-4">{error}</div>
        <button
          onClick={loadReport}
          className="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:text-blue-400 cursor-pointer"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Income Statement
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Revenue and expenses for{" "}
            {reportService.getReportPeriodDisplay(
              filters.startDate,
              filters.endDate
            )}
          </p>
        </div>
        <div className="flex space-x-3">
          <div className="relative inline-block text-left">
            <div className="flex space-x-2">
              <button
                onClick={handleExport}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                CSV
              </button>
              <button
                onClick={handleExportPDF}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                PDF
              </button>
              <button
                onClick={handleExportExcel}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <TableCellsIcon className="h-4 w-4 mr-2" />
                Excel
              </button>
            </div>
          </div>
          <button
            onClick={handlePrint}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
          >
            <PrinterIcon className="h-4 w-4 mr-2" />
            Print
          </button>
        </div>
      </div>

      {/* Filters */}
      <ReportFilters
        reportType="income-statement"
        onFiltersChange={handleFiltersChange}
      />

      {/* Report Content */}
      {reportData && reportData.data && reportData.totals && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {currentCompany?.name}
              </h2>
              <div className="text-right">
                <div className="text-lg font-medium text-gray-900 dark:text-white">
                  Income Statement
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  {reportService.getReportPeriodDisplay(
                    reportData.startDate,
                    reportData.endDate
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="p-6">
            {/* Revenue Section */}
            {renderAccountSection(
              "Revenue",
              reportData.data.revenue.accounts,
              reportData.data.revenue.total
            )}

            {/* Expenses Section */}
            {renderAccountSection(
              "Expenses",
              reportData.data.expenses.accounts,
              reportData.data.expenses.total,
              true
            )}

            {/* Net Income */}
            <div className="border-t-2 border-gray-400 dark:border-gray-500 pt-4 mt-6">
              <div className="flex justify-between font-bold text-xl text-gray-900 dark:text-white">
                <span>Net Income</span>
                <span
                  className={
                    reportData.totals.netIncome >= 0
                      ? "text-green-600 dark:text-green-400"
                      : "text-red-600 dark:text-red-400"
                  }
                >
                  {reportService.formatCurrency(reportData.totals.netIncome)}
                </span>
              </div>
            </div>

            {/* Summary Metrics */}
            <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    Total Revenue
                  </div>
                  <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                    {reportService.formatCurrency(
                      reportData.totals.totalRevenue
                    )}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    Total Expenses
                  </div>
                  <div className="text-lg font-semibold text-red-600 dark:text-red-400">
                    {reportService.formatCurrency(
                      reportData.totals.totalExpenses
                    )}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    Net Income
                  </div>
                  <div
                    className={`text-lg font-semibold ${
                      reportData.totals.netIncome >= 0
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {reportService.formatCurrency(reportData.totals.netIncome)}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    Net Margin
                  </div>
                  <div
                    className={`text-lg font-semibold ${
                      reportData.totals.netIncome >= 0
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {reportService.formatPercentage(
                      reportData.totals.totalRevenue > 0
                        ? (reportData.totals.netIncome /
                            reportData.totals.totalRevenue) *
                            100
                        : 0
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Performance Indicators */}
            {reportData.totals.totalRevenue > 0 && (
              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                  Performance Indicators
                </h4>
                <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      Expense Ratio:
                    </span>
                    <span className="font-medium">
                      {reportService.formatPercentage(
                        reportService.calculatePercentage(
                          reportData.totals.totalExpenses,
                          reportData.totals.totalRevenue
                        )
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      Profit Margin:
                    </span>
                    <span className="font-medium">
                      {reportService.formatPercentage(
                        reportData.totals.totalRevenue > 0
                          ? (reportData.totals.netIncome /
                              reportData.totals.totalRevenue) *
                              100
                          : 0
                      )}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
