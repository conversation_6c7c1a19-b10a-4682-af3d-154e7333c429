import { useState } from "react";
import {
  CalendarIcon,
  FunnelIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { reportService } from "../../services/reportService";

interface ReportFiltersProps {
  reportType:
    | "balance-sheet"
    | "income-statement"
    | "trial-balance"
    | "aging-report"
    | "cash-flow";
  onFiltersChange: (filters: {
    startDate?: string;
    endDate?: string;
    asOfDate?: string;
    reportType?: string;
  }) => void;
}

export default function ReportFilters({
  reportType,
  onFiltersChange,
}: ReportFiltersProps) {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [asOfDate, setAsOfDate] = useState(
    new Date().toISOString().split("T")[0]
  );
  const [agingReportType, setAgingReportType] = useState("receivables");
  const [showAdvanced, setShowAdvanced] = useState(false);

  const dateRangeOptions = reportService.getDateRangeOptions();

  const handleStartDateChange = (value: string) => {
    setStartDate(value);
    applyFilters({ startDate: value });
  };

  const handleEndDateChange = (value: string) => {
    setEndDate(value);
    applyFilters({ endDate: value });
  };

  const handleAsOfDateChange = (value: string) => {
    setAsOfDate(value);
    applyFilters({ asOfDate: value });
  };

  const handleAgingReportTypeChange = (value: string) => {
    setAgingReportType(value);
    applyFilters({ reportType: value });
  };

  const applyFilters = (
    newFilters: Partial<{
      startDate?: string;
      endDate?: string;
      asOfDate?: string;
      reportType?: string;
    }> = {}
  ) => {
    const filters = {
      startDate:
        newFilters.startDate !== undefined ? newFilters.startDate : startDate,
      endDate: newFilters.endDate !== undefined ? newFilters.endDate : endDate,
      asOfDate:
        newFilters.asOfDate !== undefined ? newFilters.asOfDate : asOfDate,
      reportType:
        newFilters.reportType !== undefined
          ? newFilters.reportType
          : agingReportType,
    };

    // Remove empty values
    Object.keys(filters).forEach((key) => {
      if (
        filters[key as keyof typeof filters] === undefined ||
        filters[key as keyof typeof filters] === ""
      ) {
        delete filters[key as keyof typeof filters];
      }
    });

    onFiltersChange(filters);
  };

  const setQuickDateRange = (option: any) => {
    setStartDate(option.startDate);
    setEndDate(option.endDate);
    applyFilters({ startDate: option.startDate, endDate: option.endDate });
  };

  const clearFilters = () => {
    setStartDate("");
    setEndDate("");
    setAsOfDate(new Date().toISOString().split("T")[0]);
    setAgingReportType("receivables");
    setShowAdvanced(false);
    onFiltersChange({});
  };

  const hasActiveFilters =
    startDate ||
    endDate ||
    asOfDate !== new Date().toISOString().split("T")[0] ||
    (reportType === "aging-report" && agingReportType !== "receivables");

  const requiresDateRange = ["income-statement", "cash-flow"].includes(reportType);
  const requiresAsOfDate = ["balance-sheet", "trial-balance"].includes(
    reportType
  );
  const requiresReportType = ["aging-report"].includes(reportType);

  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="flex flex-col bg-white dark:bg-gray-800 space-y-4">
        {/* Filter Toggle and Clear */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Report Filters
            {hasActiveFilters && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800">
                Active
              </span>
            )}
          </button>

          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="inline-flex items-center px-3 py-2 text-sm leading-4 font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:text-white"
            >
              <XMarkIcon className="h-4 w-4 mr-1" />
              Clear Filters
            </button>
          )}
        </div>

        {/* Filters */}
        {showAdvanced && (
          <div className="space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            {/* Date Range Filters for Income Statement */}
            {requiresDateRange && (
              <div className="space-y-4 bg-white dark:bg-gray-800">
                <div className="flex items-center space-x-2">
                  <CalendarIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Report Period
                  </span>
                </div>

                <div className="flex flex-wrap gap-2 mb-4">
                  {dateRangeOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => setQuickDateRange(option)}
                      className="px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700"
                    >
                      {option.label}
                    </button>
                  ))}
                </div>

                <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Start Date
                    </label>
                    <input
                      type="date"
                      value={startDate}
                      onChange={(e) => handleStartDateChange(e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      End Date
                    </label>
                    <input
                      type="date"
                      value={endDate}
                      onChange={(e) => handleEndDateChange(e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* As Of Date for Balance Sheet and Trial Balance */}
            {requiresAsOfDate && (
              <div className="space-y-4 bg-white dark:bg-gray-800">
                <div className="flex items-center space-x-2">
                  <CalendarIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    As Of Date
                  </span>
                </div>

                <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      As Of Date
                    </label>
                    <input
                      type="date"
                      value={asOfDate}
                      onChange={(e) => handleAsOfDateChange(e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Report Type for Aging Report */}
            {requiresReportType && (
              <div className="space-y-4 bg-white dark:bg-gray-800">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Report Type
                  </span>
                </div>

                <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Aging Report Type
                    </label>
                    <select
                      value={agingReportType}
                      onChange={(e) =>
                        handleAgingReportTypeChange(e.target.value)
                      }
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    >
                      <option value="receivables">Accounts Receivable</option>
                      <option value="payables">Accounts Payable</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      As Of Date
                    </label>
                    <input
                      type="date"
                      value={asOfDate}
                      onChange={(e) => handleAsOfDateChange(e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 pt-2 border-t border-gray-200 dark:border-gray-700">
            {startDate && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800">
                Start: {reportService.formatDate(startDate)}
                <button
                  onClick={() => handleStartDateChange("")}
                  className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:text-blue-600 dark:text-blue-400"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            {endDate && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800">
                End: {reportService.formatDate(endDate)}
                <button
                  onClick={() => handleEndDateChange("")}
                  className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:text-blue-600 dark:text-blue-400"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            {asOfDate !== new Date().toISOString().split("T")[0] && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800">
                As Of: {reportService.formatDate(asOfDate)}
                <button
                  onClick={() =>
                    handleAsOfDateChange(new Date().toISOString().split("T")[0])
                  }
                  className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-green-400 hover:text-green-600 dark:text-green-400"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            {reportType === "aging-report" &&
              agingReportType !== "receivables" && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  Type:{" "}
                  {agingReportType === "payables"
                    ? "Accounts Payable"
                    : "Accounts Receivable"}
                  <button
                    onClick={() => handleAgingReportTypeChange("receivables")}
                    className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-purple-400 hover:text-purple-600"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </span>
              )}
          </div>
        )}
      </div>
    </div>
  );
}
