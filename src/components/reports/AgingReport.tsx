import { useState, useEffect } from "react";
import {
  PrinterIcon,
  ArrowDownTrayIcon,
  DocumentArrowDownIcon,
  TableCellsIcon,
} from "@heroicons/react/24/outline";
import {
  reportService,
  type AgingReportData,
} from "../../services/reportService";
import { useCompany } from "../../contexts/CompanyContext";
import {
  exportService,
  type TableData,
  type ExportOptions,
} from "../../services/exportService";
import ReportFilters from "./ReportFilters";

export default function AgingReport() {
  const { currentCompany } = useCompany();
  const [reportData, setReportData] = useState<AgingReportData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    asOfDate: new Date().toISOString().split("T")[0],
    reportType: "receivables",
  });

  useEffect(() => {
    loadReport();
  }, [currentCompany, filters]);

  const loadReport = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      setError(null);
      const data = await reportService.getAgingReport(
        currentCompany.id,
        filters
      );
      setReportData(data);
    } catch (err) {
      console.error("Failed to load aging report:", err);
      setError("Failed to load aging report. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleFiltersChange = (newFilters: any) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
  };

  const handlePrint = () => {
    reportService.printReport();
  };

  const handleExport = () => {
    if (!reportData || !reportData.data || !reportData.totals) return;

    const exportData: any[] = [];

    // Add data for each account
    reportData.data.accounts.forEach((account: any) => {
      exportData.push({
        AccountCode: account.accountCode,
        AccountName: account.accountName,
        Current: account.current,
        "1-30 Days": account["1-30"],
        "31-60 Days": account["31-60"],
        "61-90 Days": account["61-90"],
        "Over 90 Days": account.over90,
        Total: account.total,
      });
    });

    // Add totals row
    exportData.push({
      AccountCode: "",
      AccountName: "TOTALS",
      Current: reportData.totals.current,
      "1-30 Days": reportData.totals["1-30"],
      "31-60 Days": reportData.totals["31-60"],
      "61-90 Days": reportData.totals["61-90"],
      "Over 90 Days": reportData.totals.over90,
      Total: reportData.totals.total,
    });

    reportService.exportToCSV(
      exportData,
      `aging-report-${filters.reportType}-${filters.asOfDate}`
    );
  };

  const prepareTableData = (): TableData => {
    if (!reportData || !reportData.data || !reportData.totals) {
      throw new Error("No report data available");
    }

    const rows: any[] = [];

    // Add data for each aging bucket
    Object.entries(reportData.data.buckets).forEach(([bucket, invoices]) => {
      rows.push({
        agingBucket: reportService.getAgingBucketDisplayName(bucket),
        invoiceNumber: "",
        contactName: "",
        invoiceDate: "",
        dueDate: "",
        daysPastDue: "",
        balanceDue: "",
        isHeader: true,
      });

      invoices.forEach((invoice: any) => {
        rows.push({
          agingBucket: "",
          invoiceNumber: invoice.invoice_number,
          contactName: invoice.contactDisplayName,
          invoiceDate: reportService.formatDate(invoice.invoice_date),
          dueDate: reportService.formatDate(invoice.due_date),
          daysPastDue:
            invoice.daysPastDue > 0 ? invoice.daysPastDue : "Current",
          balanceDue: invoice.balance_due,
          isHeader: false,
        });
      });

      // Add subtotal for this bucket
      const bucketTotal =
        reportData.data.totals[bucket as keyof typeof reportData.data.totals];
      rows.push({
        agingBucket: `Total ${reportService.getAgingBucketDisplayName(bucket)}`,
        invoiceNumber: "",
        contactName: "",
        invoiceDate: "",
        dueDate: "",
        daysPastDue: "",
        balanceDue: bucketTotal,
        isHeader: false,
      });
    });

    // Add grand total
    rows.push({
      agingBucket: "GRAND TOTAL",
      invoiceNumber: "",
      contactName: "",
      invoiceDate: "",
      dueDate: "",
      daysPastDue: "",
      balanceDue: reportData.data.totals.grandTotal,
      isHeader: false,
    });

    return {
      columns: [
        {
          header: "Aging Bucket",
          dataKey: "agingBucket",
          width: 15,
          align: "left",
        },
        {
          header: "Invoice #",
          dataKey: "invoiceNumber",
          width: 10,
          align: "left",
        },
        { header: "Contact", dataKey: "contactName", width: 20, align: "left" },
        {
          header: "Invoice Date",
          dataKey: "invoiceDate",
          width: 10,
          align: "left",
        },
        { header: "Due Date", dataKey: "dueDate", width: 10, align: "left" },
        {
          header: "Days Past Due",
          dataKey: "daysPastDue",
          width: 10,
          align: "center",
        },
        {
          header: "Balance Due",
          dataKey: "balanceDue",
          width: 15,
          align: "right",
          format: "currency",
        },
      ],
      rows: rows,
      totals: {
        current: reportData.data.totals.current,
        "1-30": reportData.data.totals["1-30"],
        "31-60": reportData.data.totals["31-60"],
        "61-90": reportData.data.totals["61-90"],
        over90: reportData.data.totals.over90,
        grandTotal: reportData.data.totals.grandTotal,
      },
    };
  };

  const handleExportPDF = async () => {
    if (!reportData || !currentCompany) return;

    const tableData = prepareTableData();
    const options: ExportOptions = {
      filename: `aging-report-${filters.reportType}-${filters.asOfDate}.pdf`,
      title:
        filters.reportType === "receivables"
          ? "Accounts Receivable Aging"
          : "Accounts Payable Aging",
      subtitle: `As of ${reportService.formatDate(filters.asOfDate)}`,
      companyName: currentCompany.name,
      companyAddress: currentCompany.address,
      reportDate: new Date().toLocaleDateString(),
      orientation: "landscape",
    };

    await exportService.exportToPDF(tableData, options);
  };

  const handleExportExcel = async () => {
    if (!reportData || !currentCompany) return;

    const tableData = prepareTableData();
    const options: ExportOptions = {
      filename: `aging-report-${filters.reportType}-${filters.asOfDate}.xlsx`,
      title:
        filters.reportType === "receivables"
          ? "Accounts Receivable Aging"
          : "Accounts Payable Aging",
      subtitle: `As of ${reportService.formatDate(filters.asOfDate)}`,
      companyName: currentCompany.name,
      companyAddress: currentCompany.address,
      reportDate: new Date().toLocaleDateString(),
    };

    await exportService.exportToExcel(tableData, options);
  };

  const renderAgingBucket = (
    bucketKey: string,
    invoices: any[],
    total: number
  ) => (
    <div key={bucketKey} className="mb-6">
      <div className="flex justify-between items-center mb-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <h4 className="text-md font-semibold text-gray-900 dark:text-white">
          {reportService.getAgingBucketDisplayName(bucketKey)}
        </h4>
        <span
          className={`text-lg font-bold ${reportService.getAgingBucketColor(
            bucketKey
          )}`}
        >
          {reportService.formatCurrency(total)}
        </span>
      </div>

      {invoices.length > 0 ? (
        <div className="overflow-x-auto bg-white dark:bg-gray-800">
          <table className="min-w-full bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr className="bg-white dark:bg-gray-800">
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase">
                  Invoice #
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase">
                  Contact
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase">
                  Invoice Date
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase">
                  Due Date
                </th>
                <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase">
                  Days Past Due
                </th>
                <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase">
                  Balance Due
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 dark:divide-gray-700">
              {invoices.map((invoice: any) => (
                <tr
                  key={invoice.id}
                  className="hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700"
                >
                  <td className="px-4 py-2 text-sm font-medium text-gray-900 dark:text-white">
                    {invoice.invoice_number}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">
                    {invoice.contactDisplayName}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">
                    {reportService.formatDate(invoice.invoice_date)}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">
                    {reportService.formatDate(invoice.due_date)}
                  </td>
                  <td className="px-4 py-2 text-sm text-center">
                    <span
                      className={`font-medium ${
                        invoice.daysPastDue > 0
                          ? "text-red-600 dark:text-red-400"
                          : "text-green-600 dark:text-green-400"
                      }`}
                    >
                      {invoice.daysPastDue > 0
                        ? invoice.daysPastDue
                        : "Current"}
                    </span>
                  </td>
                  <td className="px-4 py-2 text-sm font-medium text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(invoice.balance_due)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-4 text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-sm">
          No invoices in this aging bucket
        </div>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center bg-white dark:bg-gray-800 py-12">
        <div className="text-red-600 dark:text-red-400 mb-4">{error}</div>
        <button
          onClick={loadReport}
          className="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:text-blue-400 cursor-pointer"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Aging Report
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            {filters.reportType === "receivables"
              ? "Accounts Receivable"
              : "Accounts Payable"}{" "}
            aging as of {reportService.formatDate(filters.asOfDate)}
          </p>
        </div>
        <div className="flex space-x-3">
          <div className="relative inline-block text-left">
            <div className="flex space-x-2">
              <button
                onClick={handleExport}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                CSV
              </button>
              <button
                onClick={handleExportPDF}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                PDF
              </button>
              <button
                onClick={handleExportExcel}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <TableCellsIcon className="h-4 w-4 mr-2" />
                Excel
              </button>
            </div>
          </div>
          <button
            onClick={handlePrint}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
          >
            <PrinterIcon className="h-4 w-4 mr-2" />
            Print
          </button>
        </div>
      </div>

      {/* Filters */}
      <ReportFilters
        reportType="aging-report"
        onFiltersChange={handleFiltersChange}
      />

      {/* Report Content */}
      {reportData && reportData.data && reportData.totals && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {currentCompany?.name}
              </h2>
              <div className="text-right">
                <div className="text-lg font-medium text-gray-900 dark:text-white">
                  {filters.reportType === "receivables"
                    ? "Accounts Receivable"
                    : "Accounts Payable"}{" "}
                  Aging
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  As of {reportService.formatDate(reportData.asOfDate)}
                </div>
              </div>
            </div>
          </div>

          {/* Summary Totals */}
          <div className="px-6 py-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <div className="grid bg-white dark:bg-gray-800 grid-cols-2 md:grid-cols-6 gap-4 text-center">
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Current
                </div>
                <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                  {reportService.formatCurrency(reportData.totals.current)}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  1-30 Days
                </div>
                <div className="text-lg font-semibold text-yellow-600 dark:text-yellow-400">
                  {reportService.formatCurrency(reportData.totals["1-30"])}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  31-60 Days
                </div>
                <div className="text-lg font-semibold text-orange-600">
                  {reportService.formatCurrency(reportData.totals["31-60"])}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  61-90 Days
                </div>
                <div className="text-lg font-semibold text-red-600 dark:text-red-400">
                  {reportService.formatCurrency(reportData.totals["61-90"])}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Over 90 Days
                </div>
                <div className="text-lg font-semibold text-red-800">
                  {reportService.formatCurrency(reportData.totals.over90)}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Total
                </div>
                <div className="text-xl font-bold text-gray-900 dark:text-white">
                  {reportService.formatCurrency(reportData.totals.total)}
                </div>
              </div>
            </div>
          </div>

          <div className="p-6">
            {/* Aging Accounts Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                      Account Code
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                      Account Name
                    </th>
                    <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                      Current
                    </th>
                    <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                      1-30 Days
                    </th>
                    <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                      31-60 Days
                    </th>
                    <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                      61-90 Days
                    </th>
                    <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                      Over 90 Days
                    </th>
                    <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                      Total
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {reportData.data.accounts.map((account: any) => (
                    <tr
                      key={account.accountId}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700"
                    >
                      <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">
                        {account.accountCode}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">
                        {account.accountName}
                      </td>
                      <td className="px-4 py-2 text-sm text-right text-gray-900 dark:text-white">
                        {reportService.formatCurrency(account.current)}
                      </td>
                      <td className="px-4 py-2 text-sm text-right text-gray-900 dark:text-white">
                        {reportService.formatCurrency(account["1-30"])}
                      </td>
                      <td className="px-4 py-2 text-sm text-right text-gray-900 dark:text-white">
                        {reportService.formatCurrency(account["31-60"])}
                      </td>
                      <td className="px-4 py-2 text-sm text-right text-gray-900 dark:text-white">
                        {reportService.formatCurrency(account["61-90"])}
                      </td>
                      <td className="px-4 py-2 text-sm text-right text-gray-900 dark:text-white">
                        {reportService.formatCurrency(account.over90)}
                      </td>
                      <td className="px-4 py-2 text-sm text-right font-semibold text-gray-900 dark:text-white">
                        {reportService.formatCurrency(account.total)}
                      </td>
                    </tr>
                  ))}
                  {/* Totals Row */}
                  <tr className="bg-gray-50 dark:bg-gray-700 font-semibold">
                    <td
                      className="px-4 py-2 text-sm text-gray-900 dark:text-white"
                      colSpan={2}
                    >
                      TOTALS
                    </td>
                    <td className="px-4 py-2 text-sm text-right text-gray-900 dark:text-white">
                      {reportService.formatCurrency(reportData.totals.current)}
                    </td>
                    <td className="px-4 py-2 text-sm text-right text-gray-900 dark:text-white">
                      {reportService.formatCurrency(reportData.totals["1-30"])}
                    </td>
                    <td className="px-4 py-2 text-sm text-right text-gray-900 dark:text-white">
                      {reportService.formatCurrency(reportData.totals["31-60"])}
                    </td>
                    <td className="px-4 py-2 text-sm text-right text-gray-900 dark:text-white">
                      {reportService.formatCurrency(reportData.totals["61-90"])}
                    </td>
                    <td className="px-4 py-2 text-sm text-right text-gray-900 dark:text-white">
                      {reportService.formatCurrency(reportData.totals.over90)}
                    </td>
                    <td className="px-4 py-2 text-sm text-right font-bold text-gray-900 dark:text-white">
                      {reportService.formatCurrency(reportData.totals.total)}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            {/* Summary Statistics */}
            <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                Summary Statistics
              </h4>
              <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    Total Accounts
                  </div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    {reportData.summary.totalAccounts}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    Total Amount
                  </div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    {reportService.formatCurrency(
                      reportData.summary.totalOutstanding
                    )}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    Overdue Percentage
                  </div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    {Math.round(reportData.summary.overduePercentage)}%
                  </div>
                </div>
              </div>
            </div>

            {/* Collection Priority */}
            {reportData.totals.over90 > 0 && (
              <div className="mt-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200">
                <h4 className="text-md font-medium text-red-800 mb-2">
                  ⚠️ Collection Priority
                </h4>
                <div className="text-sm text-red-700 dark:text-red-400">
                  You have{" "}
                  {reportService.formatCurrency(reportData.totals.over90)} in
                  invoices over 90 days past due. Consider prioritizing
                  collection efforts for these accounts.
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
