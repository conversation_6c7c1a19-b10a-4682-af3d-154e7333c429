import { useState, useEffect } from "react";
import {
  PrinterIcon,
  ArrowDownTrayIcon,
  DocumentArrowDownIcon,
  TableCellsIcon,
} from "@heroicons/react/24/outline";
import {
  reportService,
  type TrialBalanceData,
} from "../../services/reportService";
import { useCompany } from "../../contexts/CompanyContext";
import {
  exportService,
  type TableData,
  type ExportOptions,
} from "../../services/exportService";
import ReportFilters from "./ReportFilters";

export default function TrialBalanceReport() {
  const { currentCompany } = useCompany();
  const [reportData, setReportData] = useState<TrialBalanceData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    asOfDate: new Date().toISOString().split("T")[0],
  });

  useEffect(() => {
    loadReport();
  }, [currentCompany, filters]);

  const loadReport = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      setError(null);
      const data = await reportService.getTrialBalance(
        currentCompany.id,
        filters
      );
      setReportData(data);
    } catch (err) {
      console.error("Failed to load trial balance:", err);
      setError("Failed to load trial balance. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleFiltersChange = (newFilters: any) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
  };

  const handlePrint = () => {
    reportService.printReport();
  };

  const handleExport = () => {
    if (!reportData || !reportData.data || !reportData.totals) return;

    const exportData = reportData.data.accounts.map((account) => ({
      AccountCode: account.accountCode,
      AccountName: account.accountName,
      AccountType: account.accountType,
      DebitBalance: account.debitBalance,
      CreditBalance: account.creditBalance,
      NetBalance: account.netBalance,
    }));

    // Add totals row
    exportData.push({
      AccountCode: "",
      AccountName: "TOTALS",
      AccountType: "",
      DebitBalance: reportData.totals.totalDebits,
      CreditBalance: reportData.totals.totalCredits,
      NetBalance: reportData.totals.difference,
    });

    reportService.exportToCSV(exportData, `trial-balance-${filters.asOfDate}`);
  };

  const prepareTableData = (): TableData => {
    if (!reportData || !reportData.data || !reportData.totals) {
      throw new Error("No report data available");
    }

    const rows = reportData.data.accounts.map((account) => ({
      accountCode: account.accountCode,
      accountName: account.accountName,
      accountType: reportService.getAccountTypeDisplayName(account.accountType),
      debitBalance: account.debitBalance > 0 ? account.debitBalance : "-",
      creditBalance: account.creditBalance > 0 ? account.creditBalance : "-",
      netBalance: account.netBalance,
      isHeader: false,
    }));

    rows.push({
      accountCode: "",
      accountName: "TOTALS",
      accountType: "",
      debitBalance: reportData.totals.totalDebits,
      creditBalance: reportData.totals.totalCredits,
      netBalance: reportData.totals.difference,
      isHeader: false,
      // Note: Using isTotal for styling in export if supported
    });

    return {
      columns: [
        {
          header: "Account Code",
          dataKey: "accountCode",
          width: 15,
          align: "left",
        },
        {
          header: "Account Name",
          dataKey: "accountName",
          width: 25,
          align: "left",
        },
        { header: "Type", dataKey: "accountType", width: 15, align: "left" },
        {
          header: "Debit Balance",
          dataKey: "debitBalance",
          width: 15,
          align: "right",
          format: "currency",
        },
        {
          header: "Credit Balance",
          dataKey: "creditBalance",
          width: 15,
          align: "right",
          format: "currency",
        },
        {
          header: "Net Balance",
          dataKey: "netBalance",
          width: 15,
          align: "right",
          format: "currency",
        },
      ],
      rows: rows,
      totals: {
        totalDebits: reportData.totals.totalDebits,
        totalCredits: reportData.totals.totalCredits,
        difference: reportData.totals.difference,
      },
    };
  };

  const handleExportPDF = async () => {
    if (!reportData || !currentCompany) return;

    const tableData = prepareTableData();
    const options: ExportOptions = {
      filename: `trial-balance-${filters.asOfDate}.pdf`,
      title: "Trial Balance",
      subtitle: `As of ${reportService.formatDate(filters.asOfDate)}`,
      companyName: currentCompany.name,
      companyAddress: currentCompany.address,
      reportDate: new Date().toLocaleDateString(),
      orientation: "landscape",
    };

    await exportService.exportToPDF(tableData, options);
  };

  const handleExportExcel = async () => {
    if (!reportData || !currentCompany) return;

    const tableData = prepareTableData();
    const options: ExportOptions = {
      filename: `trial-balance-${filters.asOfDate}.xlsx`,
      title: "Trial Balance",
      subtitle: `As of ${reportService.formatDate(filters.asOfDate)}`,
      companyName: currentCompany.name,
      companyAddress: currentCompany.address,
      reportDate: new Date().toLocaleDateString(),
    };

    await exportService.exportToExcel(tableData, options);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center bg-white dark:bg-gray-800 py-12">
        <div className="text-red-600 dark:text-red-400 mb-4">{error}</div>
        <button
          onClick={loadReport}
          className="text-primary-600 hover:text-primary-500"
        >
          Try again
        </button>
      </div>
    );
  }

  // Check if data structure is complete
  if (reportData && (!reportData.data || !reportData.totals)) {
    return (
      <div className="text-center bg-white dark:bg-gray-800 py-12">
        <div className="text-yellow-600 dark:text-yellow-400 mb-4">
          Report data is incomplete. Please try refreshing.
        </div>
        <button
          onClick={loadReport}
          className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
        >
          Refresh
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Trial Balance
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Verify that debits equal credits as of{" "}
            {reportService.formatDate(filters.asOfDate)}
          </p>
        </div>
        <div className="flex space-x-3">
          <div className="relative inline-block text-left">
            <div className="flex space-x-2">
              <button
                onClick={handleExport}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                CSV
              </button>
              <button
                onClick={handleExportPDF}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                PDF
              </button>
              <button
                onClick={handleExportExcel}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <TableCellsIcon className="h-4 w-4 mr-2" />
                Excel
              </button>
            </div>
          </div>
          <button
            onClick={handlePrint}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
          >
            <PrinterIcon className="h-4 w-4 mr-2" />
            Print
          </button>
        </div>
      </div>

      {/* Filters */}
      <ReportFilters
        reportType="trial-balance"
        onFiltersChange={handleFiltersChange}
      />

      {/* Report Content */}
      {reportData && reportData.data && reportData.totals && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {currentCompany?.name}
              </h2>
              <div className="text-right">
                <div className="text-lg font-medium text-gray-900 dark:text-white">
                  Trial Balance
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  As of {reportService.formatDate(reportData.asOfDate)}
                </div>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto bg-white dark:bg-gray-800">
            <table className="min-w-full bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr className="bg-white dark:bg-gray-800">
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                    Account Code
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                    Account Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                    Debit Balance
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                    Credit Balance
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                    Net Balance
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 dark:divide-gray-700">
                {reportData.data.accounts.map((account) => (
                  <tr
                    key={account.accountId}
                    className="hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {account.accountCode}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {account.accountName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200">
                        {reportService.getAccountTypeDisplayName(
                          account.accountType
                        )}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white text-right">
                      {account.debitBalance > 0
                        ? reportService.formatCurrency(account.debitBalance)
                        : "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white text-right">
                      {account.creditBalance > 0
                        ? reportService.formatCurrency(account.creditBalance)
                        : "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white text-right font-medium">
                      <span
                        className={
                          account.netBalance >= 0
                            ? "text-green-600 dark:text-green-400"
                            : "text-red-600 dark:text-red-400"
                        }
                      >
                        {reportService.formatCurrency(account.netBalance)}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot className="bg-gray-50 dark:bg-gray-800">
                <tr className="border-t-2 border-gray-300 dark:border-gray-600">
                  <td
                    colSpan={3}
                    className="px-6 py-4 text-sm font-bold text-gray-900 dark:text-white"
                  >
                    TOTALS
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.totals.totalDebits
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 dark:text-white text-right">
                    {reportService.formatCurrency(
                      reportData.totals.totalCredits
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 dark:text-white text-right">
                    <span
                      className={
                        reportData.totals.difference === 0
                          ? "text-green-600 dark:text-green-400"
                          : "text-red-600 dark:text-red-400"
                      }
                    >
                      {reportService.formatCurrency(
                        reportData.totals.difference
                      )}
                    </span>
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>

          {/* Balance Check */}
          <div className="px-6 py-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div className="flex justify-center">
              <div
                className={`inline-flex items-center px-4 py-2 rounded-lg ${
                  reportData.totals.isBalanced
                    ? "bg-green-100 dark:bg-green-900/20 text-green-800"
                    : "bg-red-100 dark:bg-red-900/20 text-red-800"
                }`}
              >
                <span className="text-lg font-semibold">
                  {reportData.totals.isBalanced
                    ? "✓ Trial Balance is Balanced"
                    : "✗ Trial Balance is NOT Balanced"}
                </span>
              </div>
            </div>

            {!reportData.totals.isBalanced && (
              <div className="mt-4 text-center">
                <div className="text-sm text-red-600 dark:text-red-400">
                  Difference:{" "}
                  {reportService.formatCurrency(
                    Math.abs(reportData.totals.difference)
                  )}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">
                  Please review your journal entries for errors
                </div>
              </div>
            )}
          </div>

          {/* Summary Statistics */}
          <div className="px-6 py-4 bg-blue-50 dark:bg-blue-900/20 border-t border-gray-200 dark:border-gray-700">
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
              Summary
            </h4>
            <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Total Accounts
                </div>
                <div className="text-lg font-semibold text-gray-900 dark:text-white">
                  {reportData.data.accounts.length}
                </div>
              </div>
              <div className="text-center">
                <div className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Total Debits
                </div>
                <div className="text-lg font-semibold text-gray-900 dark:text-white">
                  {reportService.formatCurrency(reportData.totals.totalDebits)}
                </div>
              </div>
              <div className="text-center">
                <div className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Total Credits
                </div>
                <div className="text-lg font-semibold text-gray-900 dark:text-white">
                  {reportService.formatCurrency(reportData.totals.totalCredits)}
                </div>
              </div>
              <div className="text-center">
                <div className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Balance Status
                </div>
                <div
                  className={`text-lg font-semibold ${
                    reportData.totals.isBalanced
                      ? "text-green-600 dark:text-green-400"
                      : "text-red-600 dark:text-red-400"
                  }`}
                >
                  {reportData.totals.isBalanced ? "Balanced" : "Unbalanced"}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
