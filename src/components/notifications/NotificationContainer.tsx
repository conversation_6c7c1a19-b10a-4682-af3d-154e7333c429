import { useNotification } from "../../contexts/NotificationContext";
import NotificationItem from "./NotificationItem";

export default function NotificationContainer() {
  const { notifications } = useNotification();

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-6 right-6 z-50 space-y-3 max-w-md">
      {notifications.map((notification) => (
        <NotificationItem key={notification.id} notification={notification} />
      ))}
    </div>
  );
}
