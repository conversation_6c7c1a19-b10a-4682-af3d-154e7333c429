import { useState, useEffect } from "react";
import { XMarkIcon, PlusIcon, TrashIcon } from "@heroicons/react/24/outline";
import type { Invoice, InvoiceType, Contact, Account } from "../../types";
import { invoiceService } from "../../services/invoiceService";
import { contactService } from "../../services/contactService";
import { accountService } from "../../services/accountService";
import { useCompany } from "../../contexts/CompanyContext";

interface InvoiceFormProps {
  invoice?: Invoice;
  onSave: (invoice: Invoice) => void;
  onCancel: () => void;
}

interface LineItem {
  id?: string;
  itemCode?: string;
  description: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  discountPercent: number;
  accountId: string;
  lineTotal: number;
}

export default function InvoiceForm({
  invoice,
  onSave,
  onCancel,
}: InvoiceFormProps) {
  const { currentCompany } = useCompany();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);

  const [formData, setFormData] = useState({
    contactId: "",
    invoiceType: "SALES" as InvoiceType,
    invoiceDate: new Date().toISOString().split("T")[0],
    dueDate: "",
    currency: "USD",
    exchangeRate: 1,
    termsAndConditions: "",
    notes: "",
  });

  const [lineItems, setLineItems] = useState<LineItem[]>([
    {
      description: "",
      quantity: 1,
      unit: "EA",
      unitPrice: 0,
      discountPercent: 0,
      accountId: "",
      lineTotal: 0,
    },
  ]);

  useEffect(() => {
    loadContacts();
    loadAccounts();

    if (invoice) {
      setFormData({
        contactId: invoice.contactId,
        invoiceType: invoice.invoiceType,
        invoiceDate: invoice.invoiceDate,
        dueDate: invoice.dueDate,
        currency: invoice.currency,
        exchangeRate: invoice.exchangeRate,
        termsAndConditions: invoice.termsAndConditions || "",
        notes: invoice.notes || "",
      });

      if (invoice.lineItems && invoice.lineItems.length > 0) {
        setLineItems(
          invoice.lineItems.map((item) => ({
            id: item.id,
            itemCode: item.itemCode,
            description: item.description,
            quantity: item.quantity,
            unit: item.unit,
            unitPrice: item.unitPrice,
            discountPercent: item.discountPercent,
            accountId: item.accountId,
            lineTotal: item.lineTotal,
          }))
        );
      }
    }
  }, [invoice]);

  const loadContacts = async () => {
    if (!currentCompany) return;

    try {
      const response = await contactService.getContacts(currentCompany.id, {
        limit: 1000,
      });
      setContacts(response.data);
    } catch (error) {
      console.error("Failed to load contacts:", error);
    }
  };

  const loadAccounts = async () => {
    if (!currentCompany) return;

    try {
      const accounts = await accountService.getAccounts(currentCompany.id);
      setAccounts(accounts);
    } catch (error) {
      console.error("Failed to load accounts:", error);
    }
  };

  const calculateDueDate = (
    invoiceDate: string,
    paymentTerms: string = "NET_30"
  ) => {
    const date = new Date(invoiceDate);
    const daysToAdd =
      paymentTerms === "NET_15"
        ? 15
        : paymentTerms === "NET_45"
        ? 45
        : paymentTerms === "NET_60"
        ? 60
        : paymentTerms === "NET_90"
        ? 90
        : 30;

    date.setDate(date.getDate() + daysToAdd);
    return date.toISOString().split("T")[0];
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => {
      const newData = { ...prev, [field]: value };

      // Auto-calculate due date when invoice date changes
      if (field === "invoiceDate" && value) {
        newData.dueDate = calculateDueDate(value);
      }

      return newData;
    });

    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleLineItemChange = (index: number, field: string, value: any) => {
    setLineItems((prev) => {
      const newItems = [...prev];
      newItems[index] = { ...newItems[index], [field]: value };

      // Recalculate line total
      const item = newItems[index];
      const subtotal = item.quantity * item.unitPrice;
      const discountAmount = (subtotal * item.discountPercent) / 100;
      newItems[index].lineTotal = subtotal - discountAmount;

      return newItems;
    });
  };

  const addLineItem = () => {
    setLineItems((prev) => [
      ...prev,
      {
        description: "",
        quantity: 1,
        unit: "EA",
        unitPrice: 0,
        discountPercent: 0,
        accountId: "",
        lineTotal: 0,
      },
    ]);
  };

  const removeLineItem = (index: number) => {
    if (lineItems.length > 1) {
      setLineItems((prev) => prev.filter((_, i) => i !== index));
    }
  };

  const calculateTotals = () => {
    const subtotal = lineItems.reduce((sum, item) => sum + item.lineTotal, 0);
    const discountAmount = lineItems.reduce((sum, item) => {
      const itemSubtotal = item.quantity * item.unitPrice;
      const itemDiscount = (itemSubtotal * item.discountPercent) / 100;
      return sum + itemDiscount;
    }, 0);
    const taxAmount = 0; // Tax calculation would be implemented here
    const totalAmount = subtotal + taxAmount;

    return {
      subtotal,
      discountAmount,
      taxAmount,
      totalAmount,
    };
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.contactId) {
      newErrors.contactId = "Contact is required";
    }

    if (!formData.invoiceDate) {
      newErrors.invoiceDate = "Invoice date is required";
    }

    if (!formData.dueDate) {
      newErrors.dueDate = "Due date is required";
    }

    lineItems.forEach((item, index) => {
      if (!item.description) {
        newErrors[`lineItem_${index}_description`] = "Description is required";
      }
      if (!item.quantity || item.quantity <= 0) {
        newErrors[`lineItem_${index}_quantity`] =
          "Quantity must be greater than 0";
      }
      if (item.unitPrice < 0) {
        newErrors[`lineItem_${index}_unitPrice`] =
          "Unit price cannot be negative";
      }
      if (!item.accountId) {
        newErrors[`lineItem_${index}_accountId`] = "Account is required";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentCompany || !validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const totals = calculateTotals();

      const invoiceData = {
        companyId: currentCompany.id,
        ...formData,
        lineItems: lineItems.map((item) => ({
          itemCode: item.itemCode,
          description: item.description,
          quantity: item.quantity,
          unit: item.unit,
          unitPrice: item.unitPrice,
          discountPercent: item.discountPercent,
          accountId: item.accountId,
          taxDetails: {},
        })),
        ...totals,
      };

      let savedInvoice: Invoice;

      if (invoice) {
        savedInvoice = await invoiceService.updateInvoice(
          currentCompany.id,
          invoice.id,
          invoiceData
        );
      } else {
        savedInvoice = await invoiceService.createInvoice(invoiceData);
      }

      onSave(savedInvoice);
    } catch (err) {
      console.error("Failed to save invoice:", err);
      setErrors({ submit: "Failed to save invoice. Please try again." });
    } finally {
      setLoading(false);
    }
  };

  const totals = calculateTotals();
  const unitOptions = invoiceService.getUnitOptions();

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div
          className="fixed inset-0"
          style={{ backgroundColor: 'rgba(0,0,0,0.6)' }}
          onClick={onCancel}
        />

        <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-11/12 max-w-6xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {invoice ? "Edit Invoice" : "Create New Invoice"}
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-pointer"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {errors.submit && (
          <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 rounded-md">
            <p className="text-sm text-red-600 dark:text-red-400">{errors.submit}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Header Information */}
          <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Invoice Type *
              </label>
              <select
                value={formData.invoiceType}
                onChange={(e) =>
                  handleInputChange("invoiceType", e.target.value)
                }
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                required
              >
                <option value="SALES">Sales Invoice</option>
                <option value="PURCHASE">Purchase Invoice</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Contact *
              </label>
              <select
                value={formData.contactId}
                onChange={(e) => handleInputChange("contactId", e.target.value)}
                className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                  errors.contactId ? "border-red-300" : "border-gray-300 dark:border-gray-600"
                }`}
                required
              >
                <option value="">Select a contact</option>
                {contacts.map((contact) => (
                  <option key={contact.id} value={contact.id}>
                    {contactService.getDisplayName(contact)}
                  </option>
                ))}
              </select>
              {errors.contactId && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.contactId}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Currency
              </label>
              <select
                value={formData.currency}
                onChange={(e) => handleInputChange("currency", e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="USD">USD - US Dollar</option>
                <option value="EUR">EUR - Euro</option>
                <option value="GBP">GBP - British Pound</option>
                <option value="CAD">CAD - Canadian Dollar</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Invoice Date *
              </label>
              <input
                type="date"
                value={formData.invoiceDate}
                onChange={(e) =>
                  handleInputChange("invoiceDate", e.target.value)
                }
                className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                  errors.invoiceDate ? "border-red-300" : "border-gray-300 dark:border-gray-600"
                }`}
                required
              />
              {errors.invoiceDate && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.invoiceDate}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Due Date *
              </label>
              <input
                type="date"
                value={formData.dueDate}
                onChange={(e) => handleInputChange("dueDate", e.target.value)}
                className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                  errors.dueDate ? "border-red-300" : "border-gray-300 dark:border-gray-600"
                }`}
                required
              />
              {errors.dueDate && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.dueDate}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Exchange Rate
              </label>
              <input
                type="number"
                step="0.000001"
                value={formData.exchangeRate}
                onChange={(e) =>
                  handleInputChange(
                    "exchangeRate",
                    parseFloat(e.target.value) || 1
                  )
                }
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          {/* Line Items */}
          <div>
            <div className="flex justify-between items-center mb-4">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">Line Items</h4>
              <button
                type="button"
                onClick={addLineItem}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 cursor-pointer"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Line Item
              </button>
            </div>

            <div className="overflow-x-auto bg-white dark:bg-gray-800">
              <table className="min-w-full bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr className="bg-white dark:bg-gray-800">
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                      Description *
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                      Qty *
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                      Unit
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                      Unit Price *
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                      Discount %
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                      Account *
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 dark:divide-gray-700">
                  {lineItems.map((item, index) => (
                    <tr key={index}>
                      <td className="px-3 py-4">
                        <input
                          type="text"
                          value={item.description}
                          onChange={(e) =>
                            handleLineItemChange(
                              index,
                              "description",
                              e.target.value
                            )
                          }
                          className={`block w-full px-2 py-1 border rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 ${
                            errors[`lineItem_${index}_description`]
                              ? "border-red-300"
                              : "border-gray-300 dark:border-gray-600"
                          }`}
                          placeholder="Item description"
                        />
                        {errors[`lineItem_${index}_description`] && (
                          <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                            {errors[`lineItem_${index}_description`]}
                          </p>
                        )}
                      </td>
                      <td className="px-3 py-4">
                        <input
                          type="number"
                          step="0.001"
                          value={item.quantity}
                          onChange={(e) =>
                            handleLineItemChange(
                              index,
                              "quantity",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          className={`block w-20 px-2 py-1 border rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 ${
                            errors[`lineItem_${index}_quantity`]
                              ? "border-red-300"
                              : "border-gray-300 dark:border-gray-600"
                          }`}
                        />
                        {errors[`lineItem_${index}_quantity`] && (
                          <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                            {errors[`lineItem_${index}_quantity`]}
                          </p>
                        )}
                      </td>
                      <td className="px-3 py-4">
                        <select
                          value={item.unit}
                          onChange={(e) =>
                            handleLineItemChange(index, "unit", e.target.value)
                          }
                          className="block w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                        >
                          {unitOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </td>
                      <td className="px-3 py-4">
                        <input
                          type="number"
                          step="0.01"
                          value={item.unitPrice}
                          onChange={(e) =>
                            handleLineItemChange(
                              index,
                              "unitPrice",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          className={`block w-24 px-2 py-1 border rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 ${
                            errors[`lineItem_${index}_unitPrice`]
                              ? "border-red-300"
                              : "border-gray-300 dark:border-gray-600"
                          }`}
                        />
                        {errors[`lineItem_${index}_unitPrice`] && (
                          <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                            {errors[`lineItem_${index}_unitPrice`]}
                          </p>
                        )}
                      </td>
                      <td className="px-3 py-4">
                        <input
                          type="number"
                          step="0.01"
                          min="0"
                          max="100"
                          value={item.discountPercent}
                          onChange={(e) =>
                            handleLineItemChange(
                              index,
                              "discountPercent",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          className="block w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                        />
                      </td>
                      <td className="px-3 py-4">
                        <select
                          value={item.accountId}
                          onChange={(e) =>
                            handleLineItemChange(
                              index,
                              "accountId",
                              e.target.value
                            )
                          }
                          className={`block w-40 px-2 py-1 border rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 ${
                            errors[`lineItem_${index}_accountId`]
                              ? "border-red-300"
                              : "border-gray-300 dark:border-gray-600"
                          }`}
                        >
                          <option value="">Select account</option>
                          {accounts.map((account) => (
                            <option key={account.id} value={account.id}>
                              {account.code} - {account.name}
                            </option>
                          ))}
                        </select>
                        {errors[`lineItem_${index}_accountId`] && (
                          <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                            {errors[`lineItem_${index}_accountId`]}
                          </p>
                        )}
                      </td>
                      <td className="px-3 py-4 text-sm font-medium text-gray-900 dark:text-white">
                        {invoiceService.formatCurrency(
                          item.lineTotal,
                          formData.currency
                        )}
                      </td>
                      <td className="px-3 py-4">
                        {lineItems.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeLineItem(index)}
                            className="text-red-600 dark:text-red-400 hover:text-red-900 cursor-pointer"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Totals */}
          <div className="flex justify-end">
            <div className="w-64 space-y-2">
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>
                  {invoiceService.formatCurrency(
                    totals.subtotal,
                    formData.currency
                  )}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Discount:</span>
                <span>
                  -
                  {invoiceService.formatCurrency(
                    totals.discountAmount,
                    formData.currency
                  )}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Tax:</span>
                <span>
                  {invoiceService.formatCurrency(
                    totals.taxAmount,
                    formData.currency
                  )}
                </span>
              </div>
              <div className="flex justify-between text-lg font-semibold border-t pt-2">
                <span>Total:</span>
                <span>
                  {invoiceService.formatCurrency(
                    totals.totalAmount,
                    formData.currency
                  )}
                </span>
              </div>
            </div>
          </div>

          {/* Notes and Terms */}
          <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Terms and Conditions
              </label>
              <textarea
                value={formData.termsAndConditions}
                onChange={(e) =>
                  handleInputChange("termsAndConditions", e.target.value)
                }
                rows={4}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Payment terms, conditions, etc."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notes
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => handleInputChange("notes", e.target.value)}
                rows={4}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Internal notes, special instructions, etc."
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 cursor-pointer"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 disabled:opacity-50 cursor-pointer"
            >
              {loading
                ? "Saving..."
                : invoice
                ? "Update Invoice"
                : "Create Invoice"}
            </button>
          </div>
        </form>
        </div>
      </div>
    </div>
  );
}
