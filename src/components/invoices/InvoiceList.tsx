import { useState, useEffect } from "react";
import {
  PencilIcon,
  TrashIcon,
  EyeIcon,
  PaperAirplaneIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";
import type { Invoice, InvoiceType, InvoiceStatus } from "../../types";
import { invoiceService } from "../../services/invoiceService";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { useConfirmDialog } from "../../hooks/useConfirmDialog";
import ConfirmDialog from "../ui/ConfirmDialog";
import humps from "humps";

interface InvoiceListProps {
  filters?: {
    invoiceType?: InvoiceType;
    status?: InvoiceStatus;
    contactId?: string;
    search?: string;
    dateFrom?: string;
    dateTo?: string;
  };
  onView?: (invoice: Invoice) => void;
  onEdit?: (invoice: Invoice) => void;
  onDelete?: (invoice: Invoice) => void;
  refreshTrigger?: number;
}

export default function InvoiceList({
  filters,
  onView,
  onEdit,
  refreshTrigger,
}: InvoiceListProps) {
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();
  const { confirm, dialogProps } = useConfirmDialog();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
  });

  const loadInvoices = async (page: number = 1) => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      setError(null);

      const response = await invoiceService.getInvoices(currentCompany.id, {
        ...filters,
        page,
        limit: pagination.limit,
      });

      const camelizedInvoices = humps.camelizeKeys(response.data) as Invoice[];

      setInvoices(camelizedInvoices);
      setPagination(response.pagination);
    } catch (err) {
      console.error("Failed to load invoices:", err);
      setError("Failed to load invoices. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInvoices();
  }, [currentCompany, filters, refreshTrigger]);

  const handleDelete = async (invoice: Invoice) => {
    if (!currentCompany) return;

    if (invoice.status !== "DRAFT") {
      addNotification(
        "warning",
        "Cannot Delete Invoice",
        "Only draft invoices can be deleted."
      );
      return;
    }

    const confirmed = await confirm({
      title: "Delete Invoice",
      message: `Are you sure you want to delete invoice ${invoice.invoiceNumber}? This action cannot be undone.`,
      confirmLabel: "Delete",
      cancelLabel: "Cancel",
      type: "danger",
    });

    if (confirmed) {
      try {
        await invoiceService.deleteInvoice(currentCompany.id, invoice.id);
        loadInvoices(pagination.page);
        addNotification(
          "success",
          "Invoice Deleted",
          `Invoice ${invoice.invoiceNumber} has been deleted successfully.`
        );
      } catch (err) {
        console.error("Failed to delete invoice:", err);
        addNotification(
          "error",
          "Delete Failed",
          "Failed to delete invoice. Please try again."
        );
      }
    }
  };

  const handleSendInvoice = async (invoice: Invoice) => {
    if (!currentCompany) return;

    try {
      await invoiceService.sendInvoice(currentCompany.id, invoice.id);
      loadInvoices(pagination.page);
      addNotification(
        "success",
        "Invoice Sent",
        `Invoice ${invoice.invoiceNumber} has been sent successfully.`
      );
    } catch (err) {
      console.error("Failed to send invoice:", err);
      addNotification(
        "error",
        "Send Failed",
        "Failed to send invoice. Please try again."
      );
    }
  };

  const handleMarkPaid = async (invoice: Invoice) => {
    if (!currentCompany) return;

    const confirmed = await confirm({
      title: "Mark Invoice as Paid",
      message: `Are you sure you want to mark invoice ${
        invoice.invoiceNumber
      } as paid for ${invoiceService.formatCurrency(
        invoice.balanceDue,
        invoice.currency
      )}?`,
      confirmLabel: "Mark as Paid",
      cancelLabel: "Cancel",
      type: "success",
    });

    if (confirmed) {
      try {
        await invoiceService.markInvoicePaid(currentCompany.id, invoice.id);
        loadInvoices(pagination.page);
        addNotification(
          "success",
          "Invoice Marked as Paid",
          `Invoice ${invoice.invoiceNumber} has been marked as paid successfully.`
        );
      } catch (err) {
        console.error("Failed to mark invoice as paid:", err);
        addNotification(
          "error",
          "Update Failed",
          "Failed to mark invoice as paid. Please check the invoice details and try again."
        );
      }
    }
  };

  const getInvoiceTypeBadge = (type: InvoiceType) => {
    const colorClass = invoiceService.getInvoiceTypeColor(type);
    const displayName = invoiceService.getInvoiceTypeDisplayName(type);

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}
      >
        {displayName}
      </span>
    );
  };

  const getStatusBadge = (status: InvoiceStatus) => {
    const colorClass = invoiceService.getInvoiceStatusColor(status);
    const displayName = invoiceService.getInvoiceStatusDisplayName(status);

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}
      >
        {displayName}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center bg-white dark:bg-gray-800 py-12">
        <div className="text-red-600 dark:text-red-400 mb-4">{error}</div>
        <button
          onClick={() => loadInvoices()}
          className="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:text-blue-400 cursor-pointer"
        >
          Try again
        </button>
      </div>
    );
  }

  if (invoices.length === 0) {
    return (
      <div className="text-center bg-white dark:bg-gray-800 py-12">
        <svg
          className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          No invoices found
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
          {filters?.search
            ? "Try adjusting your search criteria."
            : "Get started by creating your first invoice."}
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
      <div className="overflow-x-auto bg-white dark:bg-gray-800">
        <table className="min-w-full bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr className="bg-white dark:bg-gray-800">
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                Invoice
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                Amount
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 dark:divide-gray-700">
            {invoices.map((invoice) => (
              <tr
                key={invoice.id}
                className="hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 group cursor-pointer"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {invoice.invoiceNumber}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    Due: {invoiceService.formatDate(invoice.dueDate)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {invoice.contactDisplayName ||
                      invoice.contactName ||
                      "No Contact"}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    {invoice.contactNumber || ""}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getInvoiceTypeBadge(invoice.invoiceType)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {invoiceService.formatDate(invoice.invoiceDate)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {invoiceService.formatCurrency(
                      invoice.totalAmount,
                      invoice.currency
                    )}
                  </div>
                  {invoice.balanceDue > 0 && (
                    <div className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      Balance:{" "}
                      {invoiceService.formatCurrency(
                        invoice.balanceDue,
                        invoice.currency
                      )}
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(invoice.status)}
                  {invoiceService.isOverdue(invoice) && (
                    <div className="text-xs text-red-600 dark:text-red-400 mt-1">
                      {invoiceService.getDaysOverdue(invoice)} days overdue
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={() => onView?.(invoice)}
                      className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:bg-blue-900/20 rounded"
                      title="View invoice"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </button>
                    {invoice.status === "DRAFT" && (
                      <>
                        <button
                          onClick={() => onEdit?.(invoice)}
                          className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:bg-blue-900/20 rounded"
                          title="Edit invoice"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleSendInvoice(invoice)}
                          className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:bg-blue-900/20 rounded"
                          title="Send invoice"
                        >
                          <PaperAirplaneIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(invoice)}
                          className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-red-600 dark:text-red-400 hover:bg-red-50 dark:bg-red-900/20 rounded"
                          title="Delete invoice"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </>
                    )}
                    {(invoice.status === "SENT" ||
                      invoice.status === "OVERDUE") &&
                      invoice.balanceDue > 0 && (
                        <button
                          onClick={() => handleMarkPaid(invoice)}
                          className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-green-600 dark:text-green-400 hover:bg-green-50 dark:bg-green-900/20 rounded"
                          title="Mark as paid"
                        >
                          <CheckCircleIcon className="h-4 w-4" />
                        </button>
                      )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 sm:px-6">
          <div className="flex flex-1 justify-between sm:hidden">
            <button
              onClick={() => loadInvoices(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className="relative inline-flex items-center rounded-md border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 disabled:opacity-50 cursor-pointer"
            >
              Previous
            </button>
            <button
              onClick={() => loadInvoices(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages}
              className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 disabled:opacity-50 cursor-pointer"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Showing{" "}
                <span className="font-medium">
                  {(pagination.page - 1) * pagination.limit + 1}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(
                    pagination.page * pagination.limit,
                    pagination.total
                  )}
                </span>{" "}
                of <span className="font-medium">{pagination.total}</span>{" "}
                results
              </p>
            </div>
            <div>
              <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm">
                <button
                  onClick={() => loadInvoices(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0 disabled:opacity-50 cursor-pointer"
                >
                  Previous
                </button>
                <button
                  onClick={() => loadInvoices(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages}
                  className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700 focus:z-20 focus:outline-offset-0 disabled:opacity-50 cursor-pointer"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Dialog */}
      <ConfirmDialog {...dialogProps} />
    </div>
  );
}
