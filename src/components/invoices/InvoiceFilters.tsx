import { useState, useEffect } from "react";
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
  CalendarIcon,
} from "@heroicons/react/24/outline";
import type { InvoiceType, InvoiceStatus, Contact } from "../../types";
import { invoiceService } from "../../services/invoiceService";
import { contactService } from "../../services/contactService";
import { useCompany } from "../../contexts/CompanyContext";

interface InvoiceFiltersProps {
  onFiltersChange: (filters: {
    invoiceType?: InvoiceType;
    status?: InvoiceStatus;
    contactId?: string;
    search?: string;
    dateFrom?: string;
    dateTo?: string;
  }) => void;
}

export default function InvoiceFilters({
  onFiltersChange,
}: InvoiceFiltersProps) {
  const { currentCompany } = useCompany();
  const [search, setSearch] = useState("");
  const [invoiceType, setInvoiceType] = useState<InvoiceType | "">("");
  const [status, setStatus] = useState<InvoiceStatus | "">("");
  const [contactId, setContactId] = useState("");
  const [dateFrom, setDateFrom] = useState("");
  const [dateTo, setDateTo] = useState("");
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [contacts, setContacts] = useState<Contact[]>([]);

  useEffect(() => {
    loadContacts();
  }, [currentCompany]);

  const loadContacts = async () => {
    if (!currentCompany) return;

    try {
      const response = await contactService.getContacts(currentCompany.id, {
        limit: 1000, // Get all contacts for dropdown
      });
      setContacts(response.data);
    } catch (error) {
      console.error("Failed to load contacts:", error);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearch(value);
    applyFilters({ search: value });
  };

  const handleInvoiceTypeChange = (value: InvoiceType | "") => {
    setInvoiceType(value);
    applyFilters({ invoiceType: value || undefined });
  };

  const handleStatusChange = (value: InvoiceStatus | "") => {
    setStatus(value);
    applyFilters({ status: value || undefined });
  };

  const handleContactChange = (value: string) => {
    setContactId(value);
    applyFilters({ contactId: value || undefined });
  };

  const handleDateFromChange = (value: string) => {
    setDateFrom(value);
    applyFilters({ dateFrom: value || undefined });
  };

  const handleDateToChange = (value: string) => {
    setDateTo(value);
    applyFilters({ dateTo: value || undefined });
  };

  const applyFilters = (
    newFilters: Partial<{
      invoiceType?: InvoiceType;
      status?: InvoiceStatus;
      contactId?: string;
      search?: string;
      dateFrom?: string;
      dateTo?: string;
    }> = {}
  ) => {
    const filters = {
      search: newFilters.search !== undefined ? newFilters.search : search,
      invoiceType:
        newFilters.invoiceType !== undefined
          ? newFilters.invoiceType
          : invoiceType || undefined,
      status:
        newFilters.status !== undefined
          ? newFilters.status
          : status || undefined,
      contactId:
        newFilters.contactId !== undefined
          ? newFilters.contactId
          : contactId || undefined,
      dateFrom:
        newFilters.dateFrom !== undefined
          ? newFilters.dateFrom
          : dateFrom || undefined,
      dateTo:
        newFilters.dateTo !== undefined
          ? newFilters.dateTo
          : dateTo || undefined,
    };

    // Remove empty values
    Object.keys(filters).forEach((key) => {
      if (
        filters[key as keyof typeof filters] === undefined ||
        filters[key as keyof typeof filters] === ""
      ) {
        delete filters[key as keyof typeof filters];
      }
    });

    onFiltersChange(filters);
  };

  const clearFilters = () => {
    setSearch("");
    setInvoiceType("");
    setStatus("");
    setContactId("");
    setDateFrom("");
    setDateTo("");
    setShowAdvanced(false);
    onFiltersChange({});
  };

  const setQuickDateRange = (days: number) => {
    const today = new Date();
    const fromDate = new Date(today);
    fromDate.setDate(today.getDate() - days);

    const fromStr = fromDate.toISOString().split("T")[0];
    const toStr = today.toISOString().split("T")[0];

    setDateFrom(fromStr);
    setDateTo(toStr);
    applyFilters({ dateFrom: fromStr, dateTo: toStr });
  };

  const hasActiveFilters =
    search || invoiceType || status || contactId || dateFrom || dateTo;

  const invoiceTypeOptions: Array<{ value: InvoiceType; label: string }> = [
    { value: "SALES", label: "Sales Invoice" },
    { value: "PURCHASE", label: "Purchase Invoice" },
  ];

  const statusOptions: Array<{ value: InvoiceStatus; label: string }> = [
    { value: "DRAFT", label: "Draft" },
    { value: "SENT", label: "Sent" },
    { value: "PAID", label: "Paid" },
    { value: "OVERDUE", label: "Overdue" },
    { value: "CANCELLED", label: "Cancelled" },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="flex flex-col bg-white dark:bg-gray-800 space-y-4">
        {/* Search Bar */}
        <div className="flex-1">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
            </div>
            <input
              type="text"
              placeholder="Search invoices by number, contact, notes..."
              value={search}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 placeholder-gray-500 dark:placeholder-gray-400 dark:placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 dark:placeholder-gray-500 dark:placeholder-gray-400 dark:placeholder-gray-500 dark:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Filter Toggle and Clear */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Advanced Filters
            {hasActiveFilters && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                Active
              </span>
            )}
          </button>

          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="inline-flex items-center px-3 py-2 text-sm leading-4 font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:text-white"
            >
              <XMarkIcon className="h-4 w-4 mr-1" />
              Clear Filters
            </button>
          )}
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Invoice Type Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Invoice Type
                </label>
                <select
                  value={invoiceType}
                  onChange={(e) =>
                    handleInvoiceTypeChange(e.target.value as InvoiceType | "")
                  }
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">All Types</option>
                  {invoiceTypeOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Status Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <select
                  value={status}
                  onChange={(e) =>
                    handleStatusChange(e.target.value as InvoiceStatus | "")
                  }
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">All Statuses</option>
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Contact Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Contact
                </label>
                <select
                  value={contactId}
                  onChange={(e) => handleContactChange(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">All Contacts</option>
                  {contacts.map((contact) => (
                    <option key={contact.id} value={contact.id}>
                      {contactService.getDisplayName(contact)}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Date Range Filters */}
            <div className="space-y-4 bg-white dark:bg-gray-800">
              <div className="flex items-center space-x-2">
                <CalendarIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Date Range
                </span>
              </div>

              <div className="flex flex-wrap gap-2 mb-4">
                <button
                  onClick={() => setQuickDateRange(7)}
                  className="px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700"
                >
                  Last 7 days
                </button>
                <button
                  onClick={() => setQuickDateRange(30)}
                  className="px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700"
                >
                  Last 30 days
                </button>
                <button
                  onClick={() => setQuickDateRange(90)}
                  className="px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700"
                >
                  Last 90 days
                </button>
                <button
                  onClick={() => setQuickDateRange(365)}
                  className="px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-700"
                >
                  Last year
                </button>
              </div>

              <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    From Date
                  </label>
                  <input
                    type="date"
                    value={dateFrom}
                    onChange={(e) => handleDateFromChange(e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    To Date
                  </label>
                  <input
                    type="date"
                    value={dateTo}
                    onChange={(e) => handleDateToChange(e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 pt-2 border-t border-gray-200 dark:border-gray-700">
            {search && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800">
                Search: "{search}"
                <button
                  onClick={() => handleSearchChange("")}
                  className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:text-blue-600 dark:text-blue-400"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            {invoiceType && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800">
                Type: {invoiceService.getInvoiceTypeDisplayName(invoiceType)}
                <button
                  onClick={() => handleInvoiceTypeChange("")}
                  className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-green-400 hover:text-green-600 dark:text-green-400"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            {status && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Status: {invoiceService.getInvoiceStatusDisplayName(status)}
                <button
                  onClick={() => handleStatusChange("")}
                  className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-purple-400 hover:text-purple-600"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            {contactId && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800">
                Contact:{" "}
                {contacts.find((c) => c.id === contactId)?.name || "Unknown"}
                <button
                  onClick={() => handleContactChange("")}
                  className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-yellow-400 hover:text-yellow-600 dark:text-yellow-400"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            {(dateFrom || dateTo) && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                Date: {dateFrom || "..."} to {dateTo || "..."}
                <button
                  onClick={() => {
                    handleDateFromChange("");
                    handleDateToChange("");
                  }}
                  className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-indigo-400 hover:text-indigo-600"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
