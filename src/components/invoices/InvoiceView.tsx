import {
  XMarkIcon,
  PrinterIcon,
  PaperAirplaneIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";
import type { Invoice } from "../../types";
import { invoiceService } from "../../services/invoiceService";
// import { contactService } from "../../services/contactService";
import { useCompany } from "../../contexts/CompanyContext";

interface InvoiceViewProps {
  invoice: Invoice;
  onClose: () => void;
  onEdit?: () => void;
  onSend?: () => void;
  onMarkPaid?: () => void;
}

export default function InvoiceView({
  invoice,
  onClose,
  onEdit,
  onSend,
  onMarkPaid,
}: InvoiceViewProps) {
  const { currentCompany } = useCompany();

  const handlePrint = () => {
    window.print();
  };

  const getStatusBadge = (status: string) => {
    const colorClass = invoiceService.getInvoiceStatusColor(status as any);
    const displayName = invoiceService.getInvoiceStatusDisplayName(
      status as any
    );

    return (
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${colorClass}`}
      >
        {displayName}
      </span>
    );
  };

  const getTypeBadge = (type: string) => {
    const colorClass = invoiceService.getInvoiceTypeColor(type as any);
    const displayName = invoiceService.getInvoiceTypeDisplayName(type as any);

    return (
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${colorClass}`}
      >
        {displayName}
      </span>
    );
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div
          className="fixed inset-0"
          style={{ backgroundColor: "rgba(0,0,0,0.6)" }}
          onClick={onClose}
        />

        <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-11/12 max-w-4xl max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex justify-between items-center mb-6 print:hidden">
            <div className="flex items-center space-x-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Invoice Details
              </h3>
              {getStatusBadge(invoice.status)}
              {getTypeBadge(invoice.invoiceType)}
            </div>
            <div className="flex items-center space-x-2">
              {invoice.status === "DRAFT" && onEdit && (
                <button
                  onClick={onEdit}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
                >
                  Edit
                </button>
              )}
              {invoice.status === "DRAFT" && onSend && (
                <button
                  onClick={onSend}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                  Send
                </button>
              )}
              {(invoice.status === "SENT" || invoice.status === "OVERDUE") &&
                invoice.balanceDue > 0 &&
                onMarkPaid && (
                  <button
                    onClick={onMarkPaid}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircleIcon className="h-4 w-4 mr-2" />
                    Mark Paid
                  </button>
                )}
              <button
                onClick={handlePrint}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
              >
                <PrinterIcon className="h-4 w-4 mr-2" />
                Print
              </button>
              <button
                onClick={onClose}
                className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Invoice Content */}
          <div className="bg-white dark:bg-gray-800">
            {/* Company Header */}
            <div className="border-b border-gray-200 dark:border-gray-700 pb-6 mb-6">
              <div className="flex justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    {invoice.invoiceType === "SALES" ? "INVOICE" : "BILL"}
                  </h1>
                  <p className="text-lg text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">
                    #{invoice.invoiceNumber}
                  </p>
                </div>
                <div className="text-right">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {currentCompany?.name}
                  </h2>
                  {currentCompany?.address && (
                    <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-2">
                      <p>{currentCompany.address}</p>
                      <p>
                        {currentCompany.city && `${currentCompany.city}, `}
                        {currentCompany.state} {currentCompany.postalCode}
                      </p>
                      {currentCompany.country && (
                        <p>{currentCompany.country}</p>
                      )}
                      {currentCompany.phone && (
                        <p>Phone: {currentCompany.phone}</p>
                      )}
                      {currentCompany.email && (
                        <p>Email: {currentCompany.email}</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Invoice Details */}
            <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {/* Bill To */}
              <div>
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wide mb-3">
                  {invoice.invoiceType === "SALES" ? "Bill To" : "Bill From"}
                </h3>
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  <p className="font-medium text-gray-900 dark:text-white">
                    {(invoice as any).contactDisplayName ||
                      (invoice as any).contactName ||
                      (invoice as any).contact_display_name ||
                      (invoice as any).contact_name ||
                      "Contact Name Not Available"}
                  </p>
                  {((invoice as any).contactEmail ||
                    (invoice as any).contact_email) && (
                    <p>
                      {(invoice as any).contactEmail ||
                        (invoice as any).contact_email}
                    </p>
                  )}
                  {((invoice as any).billingAddress ||
                    (invoice as any).billing_address) && (
                    <div className="mt-2">
                      <p>
                        {(invoice as any).billingAddress ||
                          (invoice as any).billing_address}
                      </p>
                      <p>
                        {((invoice as any).billingCity ||
                          (invoice as any).billing_city) &&
                          `${
                            (invoice as any).billingCity ||
                            (invoice as any).billing_city
                          }, `}
                        {(invoice as any).billingState ||
                          (invoice as any).billing_state}{" "}
                        {(invoice as any).billingPostalCode ||
                          (invoice as any).billing_postal_code}
                      </p>
                      {((invoice as any).billingCountry ||
                        (invoice as any).billing_country) && (
                        <p>
                          {(invoice as any).billingCountry ||
                            (invoice as any).billing_country}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Invoice Info */}
              <div>
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wide mb-3">
                  Invoice Information
                </h3>
                <div className="text-sm space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      Invoice Date:
                    </span>
                    <span className="text-gray-900 dark:text-white">
                      {invoiceService.formatDate(invoice.invoiceDate)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      Due Date:
                    </span>
                    <span className="text-gray-900 dark:text-white">
                      {invoiceService.formatDate(invoice.dueDate)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      Currency:
                    </span>
                    <span className="text-gray-900 dark:text-white">
                      {invoice.currency}
                    </span>
                  </div>
                  {invoice.exchangeRate !== 1 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        Exchange Rate:
                      </span>
                      <span className="text-gray-900 dark:text-white">
                        {invoice.exchangeRate}
                      </span>
                    </div>
                  )}
                  {invoiceService.isOverdue(invoice) && (
                    <div className="flex justify-between">
                      <span className="text-red-600 dark:text-red-400">
                        Days Overdue:
                      </span>
                      <span className="text-red-900 font-medium">
                        {invoiceService.getDaysOverdue(invoice)} days
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Line Items */}
            <div className="mb-8">
              <div className="overflow-x-auto bg-white dark:bg-gray-800">
                <table className="min-w-full bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr className="bg-white dark:bg-gray-800">
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                        Qty
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                        Unit
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                        Unit Price
                      </th>
                      {invoice.lineItems?.some(
                        (item) => item.discountPercent > 0
                      ) && (
                        <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                          Discount
                        </th>
                      )}
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 dark:divide-gray-700">
                    {invoice.lineItems && invoice.lineItems.length > 0 ? (
                      invoice.lineItems.map((item, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4">
                            <div className="text-sm text-gray-900 dark:text-white">
                              {item.description}
                            </div>
                            {item.itemCode && (
                              <div className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                Code: {item.itemCode}
                              </div>
                            )}
                          </td>
                          <td className="px-6 py-4 text-center text-sm text-gray-900 dark:text-white">
                            {item.quantity}
                          </td>
                          <td className="px-6 py-4 text-center text-sm text-gray-900 dark:text-white">
                            {item.unit}
                          </td>
                          <td className="px-6 py-4 text-right text-sm text-gray-900 dark:text-white">
                            {invoiceService.formatCurrency(
                              item.unitPrice,
                              invoice.currency
                            )}
                          </td>
                          {invoice.lineItems?.some(
                            (item) => item.discountPercent > 0
                          ) && (
                            <td className="px-6 py-4 text-center text-sm text-gray-900 dark:text-white">
                              {item.discountPercent > 0
                                ? `${item.discountPercent}%`
                                : "-"}
                            </td>
                          )}
                          <td className="px-6 py-4 text-right text-sm text-gray-900 dark:text-white font-medium">
                            {invoiceService.formatCurrency(
                              item.lineTotal,
                              invoice.currency
                            )}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr className="bg-white dark:bg-gray-800">
                        <td
                          colSpan={6}
                          className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"
                        >
                          No line items found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Totals */}
            <div className="flex justify-end mb-8">
              <div className="w-64">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      Subtotal:
                    </span>
                    <span className="text-gray-900 dark:text-white">
                      {invoiceService.formatCurrency(
                        invoice.subtotal,
                        invoice.currency
                      )}
                    </span>
                  </div>
                  {invoice.discountAmount > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        Discount:
                      </span>
                      <span className="text-gray-900 dark:text-white">
                        -
                        {invoiceService.formatCurrency(
                          invoice.discountAmount,
                          invoice.currency
                        )}
                      </span>
                    </div>
                  )}
                  {invoice.taxAmount > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        Tax:
                      </span>
                      <span className="text-gray-900 dark:text-white">
                        {invoiceService.formatCurrency(
                          invoice.taxAmount,
                          invoice.currency
                        )}
                      </span>
                    </div>
                  )}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
                    <div className="flex justify-between text-lg font-semibold">
                      <span className="text-gray-900 dark:text-white">
                        Total:
                      </span>
                      <span className="text-gray-900 dark:text-white">
                        {invoiceService.formatCurrency(
                          invoice.totalAmount,
                          invoice.currency
                        )}
                      </span>
                    </div>
                  </div>
                  {invoice.paidAmount > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        Paid:
                      </span>
                      <span className="text-green-600 dark:text-green-400">
                        -
                        {invoiceService.formatCurrency(
                          invoice.paidAmount,
                          invoice.currency
                        )}
                      </span>
                    </div>
                  )}
                  {invoice.balanceDue > 0 && (
                    <div className="flex justify-between text-lg font-semibold">
                      <span className="text-gray-900 dark:text-white">
                        Balance Due:
                      </span>
                      <span
                        className={`${
                          invoice.balanceDue > 0 &&
                          invoiceService.isOverdue(invoice)
                            ? "text-red-600 dark:text-red-400"
                            : "text-gray-900 dark:text-white"
                        }`}
                      >
                        {invoiceService.formatCurrency(
                          invoice.balanceDue,
                          invoice.currency
                        )}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Terms and Notes */}
            {(invoice.termsAndConditions || invoice.notes) && (
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-6">
                  {invoice.termsAndConditions && (
                    <div>
                      <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                        Terms and Conditions
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 whitespace-pre-wrap">
                        {invoice.termsAndConditions}
                      </p>
                    </div>
                  )}
                  {invoice.notes && (
                    <div>
                      <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                        Notes
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 whitespace-pre-wrap">
                        {invoice.notes}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Footer */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6 mt-8 text-center text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
              <p>Thank you for your business!</p>
              <p className="mt-1">
                Generated on {new Date().toLocaleDateString()} at{" "}
                {new Date().toLocaleTimeString()}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
