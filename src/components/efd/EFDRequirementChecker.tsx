import { useState } from "react";
import {
  CpuChipIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";

interface EFDRequirement {
  requiresEFD: boolean;
  amount: number;
  threshold: number;
  message: string;
}

export default function EFDRequirementChecker() {
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();
  const [amount, setAmount] = useState<string>("");
  const [requirement, setRequirement] = useState<EFDRequirement | null>(null);
  const [loading, setLoading] = useState(false);

  const handleCheck = async () => {
    if (!currentCompany || !amount || parseFloat(amount) <= 0) {
      addNotification("Please enter a valid amount", "error");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/efd/${currentCompany.id}/check-requirement`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount: parseFloat(amount),
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setRequirement(data.data);
        addNotification("EFD requirement checked successfully", "success");
      } else {
        const error = await response.json();
        addNotification(error.error || "Failed to check EFD requirement", "error");
      }
    } catch (error) {
      console.error("Error checking EFD requirement:", error);
      addNotification("Failed to check EFD requirement", "error");
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setAmount("");
    setRequirement(null);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-TZ", {
      style: "currency",
      currency: "TZS",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getRequirementIcon = (requiresEFD: boolean) => {
    if (requiresEFD) {
      return <CheckCircleIcon className="h-8 w-8 text-red-500" />;
    }
    return <XCircleIcon className="h-8 w-8 text-green-500" />;
  };

  const getRequirementColor = (requiresEFD: boolean) => {
    if (requiresEFD) {
      return {
        bg: 'bg-red-50 dark:bg-red-900/20',
        border: 'border-red-200 dark:border-red-800',
        text: 'text-red-900 dark:text-red-100',
        subtext: 'text-red-700 dark:text-red-300'
      };
    }
    return {
      bg: 'bg-green-50 dark:bg-green-900/20',
      border: 'border-green-200 dark:border-green-800',
      text: 'text-green-900 dark:text-green-100',
      subtext: 'text-green-700 dark:text-green-300'
    };
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <CpuChipIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            EFD Requirement Checker
          </h3>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
          <InformationCircleIcon className="h-4 w-4" />
          <span>Check if transaction requires Electronic Fiscal Device</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <div className="space-y-6">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
              Transaction Amount
            </h4>

            {/* Amount Input */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Transaction Amount (TZS)
                </label>
                <input
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="Enter transaction amount in TZS"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                  min="0"
                  step="1"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Enter the gross transaction amount to check EFD requirement
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 mt-6">
              <button
                onClick={handleCheck}
                disabled={loading || !amount}
                className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                <CpuChipIcon className="h-4 w-4 mr-2" />
                {loading ? "Checking..." : "Check EFD Requirement"}
              </button>
              <button
                onClick={handleClear}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
              >
                Clear
              </button>
            </div>
          </div>

          {/* EFD Information */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
              Tanzania EFD Requirements
            </h4>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• <strong>Threshold:</strong> 5,000 TZS per transaction</li>
              <li>• <strong>Scope:</strong> All sales transactions</li>
              <li>• <strong>Receipt:</strong> Must be issued immediately</li>
              <li>• <strong>QR Code:</strong> Required for verification</li>
              <li>• <strong>Real-time:</strong> Transaction must be recorded instantly</li>
              <li>• <strong>Backup:</strong> Data must be backed up daily</li>
            </ul>
          </div>

          {/* Quick Examples */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              Quick Examples
            </h4>
            <div className="space-y-2">
              <button
                onClick={() => setAmount("3000")}
                className="w-full text-left px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                3,000 TZS - Small purchase (No EFD required)
              </button>
              <button
                onClick={() => setAmount("5000")}
                className="w-full text-left px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                5,000 TZS - At threshold (EFD required)
              </button>
              <button
                onClick={() => setAmount("25000")}
                className="w-full text-left px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                25,000 TZS - Regular sale (EFD required)
              </button>
              <button
                onClick={() => setAmount("100000")}
                className="w-full text-left px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                100,000 TZS - Large sale (EFD required)
              </button>
            </div>
          </div>
        </div>

        {/* Results Section */}
        <div className="space-y-6">
          {requirement ? (
            <div className={`rounded-lg p-6 border ${getRequirementColor(requirement.requiresEFD).bg} ${getRequirementColor(requirement.requiresEFD).border}`}>
              <div className="flex items-center justify-between mb-4">
                <h4 className={`text-lg font-medium ${getRequirementColor(requirement.requiresEFD).text}`}>
                  EFD Requirement Result
                </h4>
                {getRequirementIcon(requirement.requiresEFD)}
              </div>

              <div className="space-y-4">
                {/* Transaction Amount */}
                <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600">
                  <span className={`text-sm ${getRequirementColor(requirement.requiresEFD).subtext}`}>
                    Transaction Amount:
                  </span>
                  <span className={`font-medium ${getRequirementColor(requirement.requiresEFD).text}`}>
                    {formatCurrency(requirement.amount)}
                  </span>
                </div>

                {/* Threshold */}
                <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600">
                  <span className={`text-sm ${getRequirementColor(requirement.requiresEFD).subtext}`}>
                    EFD Threshold:
                  </span>
                  <span className={`font-medium ${getRequirementColor(requirement.requiresEFD).text}`}>
                    {formatCurrency(requirement.threshold)}
                  </span>
                </div>

                {/* Requirement Status */}
                <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600">
                  <span className={`text-sm ${getRequirementColor(requirement.requiresEFD).subtext}`}>
                    EFD Required:
                  </span>
                  <span className={`font-bold text-lg ${requirement.requiresEFD ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                    {requirement.requiresEFD ? 'YES' : 'NO'}
                  </span>
                </div>
              </div>

              {/* Message */}
              <div className="mt-6 p-4 bg-white dark:bg-gray-800 rounded-lg">
                <p className={`text-sm ${getRequirementColor(requirement.requiresEFD).text}`}>
                  <strong>Result:</strong> {requirement.message}
                </p>
              </div>

              {/* Action Required */}
              {requirement.requiresEFD && (
                <div className="mt-4 p-4 bg-red-100 dark:bg-red-900/30 rounded-lg">
                  <div className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 mr-2" />
                    <div>
                      <h5 className="font-medium text-red-800 dark:text-red-200 mb-2">
                        Action Required
                      </h5>
                      <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
                        <li>• Transaction must be processed through EFD</li>
                        <li>• Receipt with QR code must be issued</li>
                        <li>• Transaction must be recorded in real-time</li>
                        <li>• Daily Z-Report must include this transaction</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {/* Compliance Note */}
              {!requirement.requiresEFD && (
                <div className="mt-4 p-4 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <div className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5 mr-2" />
                    <div>
                      <h5 className="font-medium text-green-800 dark:text-green-200 mb-2">
                        No EFD Required
                      </h5>
                      <p className="text-sm text-green-700 dark:text-green-300">
                        This transaction is below the 5,000 TZS threshold and does not require Electronic Fiscal Device processing. However, you may still choose to use EFD for consistency and better record keeping.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 text-center">
              <CpuChipIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Ready to Check
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Enter a transaction amount to check if Electronic Fiscal Device is required.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
