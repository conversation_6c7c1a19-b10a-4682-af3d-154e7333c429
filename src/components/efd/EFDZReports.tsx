import { useState, useEffect } from "react";
import {
  DocumentTextIcon,
  PlusIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  PrinterIcon,
  InformationCircleIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ChartBarIcon,
  ComputerDesktopIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { apiService } from "../../services/api";

interface EFDZReport {
  id: string;
  companyId: string;
  efdDeviceId: string;
  zReportNumber: string;
  reportDate: string;
  reportTimestamp: string;
  fiscalDayIdentifier: string;
  totalTransactions: number;
  totalGrossSales: number;
  totalVatAmount: number;
  totalNetSales: number;
  totalDiscounts: number;
  totalRefunds: number;
  cashTotal: number;
  cardTotal: number;
  mobileMoney: number;
  otherPaymentTotal: number;
  salesCount: number;
  refundCount: number;
  voidCount: number;
  trainingCount: number;
  reportStatus: 'GENERATED' | 'SUBMITTED' | 'ACCEPTED' | 'REJECTED';
  submittedToTra?: string;
  traResponse?: any;
  rejectionReason?: string;
  detailedBreakdown?: any;
  efdSignature?: string;
  createdAt: string;
  updatedAt: string;
}

interface EFDDevice {
  id: string;
  deviceSerial: string;
  deviceModel: string;
  locationName?: string;
  status: string;
}

export default function EFDZReports() {
  const [loading, setLoading] = useState(false);
  const [zReports, setZReports] = useState<EFDZReport[]>([]);
  const [devices, setDevices] = useState<EFDDevice[]>([]);
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState<EFDZReport | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [deviceFilter, setDeviceFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');

  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  useEffect(() => {
    if (currentCompany) {
      fetchZReports();
      fetchDevices();
    }
  }, [currentCompany]);

  const fetchZReports = async () => {
    setLoading(true);
    try {
      // Mock data for demonstration
      const mockZReports: EFDZReport[] = [
        {
          id: '1',
          companyId: currentCompany?.id || '',
          efdDeviceId: '1',
          zReportNumber: 'Z-001234-20240612-0001',
          reportDate: '2024-06-12',
          reportTimestamp: new Date().toISOString(),
          fiscalDayIdentifier: '1-2024-06-12',
          totalTransactions: 45,
          totalGrossSales: 2250000,
          totalVatAmount: 405000,
          totalNetSales: 1845000,
          totalDiscounts: 25000,
          totalRefunds: 15000,
          cashTotal: 1200000,
          cardTotal: 850000,
          mobileMoney: 200000,
          otherPaymentTotal: 0,
          salesCount: 42,
          refundCount: 2,
          voidCount: 1,
          trainingCount: 0,
          reportStatus: 'ACCEPTED',
          submittedToTra: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          traResponse: { status: 'SUCCESS', reportId: 'TRA-Z-001234' },
          efdSignature: 'EFD-SIG-001234567890ABCDEF',
          createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: '2',
          companyId: currentCompany?.id || '',
          efdDeviceId: '1',
          zReportNumber: 'Z-001234-20240611-0001',
          reportDate: '2024-06-11',
          reportTimestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          fiscalDayIdentifier: '1-2024-06-11',
          totalTransactions: 38,
          totalGrossSales: 1890000,
          totalVatAmount: 340200,
          totalNetSales: 1549800,
          totalDiscounts: 15000,
          totalRefunds: 0,
          cashTotal: 950000,
          cardTotal: 740000,
          mobileMoney: 200000,
          otherPaymentTotal: 0,
          salesCount: 38,
          refundCount: 0,
          voidCount: 0,
          trainingCount: 0,
          reportStatus: 'SUBMITTED',
          submittedToTra: new Date(Date.now() - 20 * 60 * 60 * 1000).toISOString(),
          efdSignature: 'EFD-SIG-001234567890ABCDEG',
          createdAt: new Date(Date.now() - 28 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 20 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: '3',
          companyId: currentCompany?.id || '',
          efdDeviceId: '2',
          zReportNumber: 'Z-001235-20240612-0001',
          reportDate: '2024-06-12',
          reportTimestamp: new Date().toISOString(),
          fiscalDayIdentifier: '2-2024-06-12',
          totalTransactions: 28,
          totalGrossSales: 1420000,
          totalVatAmount: 255600,
          totalNetSales: 1164400,
          totalDiscounts: 8000,
          totalRefunds: 5000,
          cashTotal: 800000,
          cardTotal: 520000,
          mobileMoney: 100000,
          otherPaymentTotal: 0,
          salesCount: 26,
          refundCount: 1,
          voidCount: 1,
          trainingCount: 0,
          reportStatus: 'GENERATED',
          efdSignature: 'EFD-SIG-001234567890ABCDEH',
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        },
      ];

      setZReports(mockZReports);
      addNotification("success", "Success", "Z-Reports loaded successfully");
    } catch (error: any) {
      console.error("Error fetching Z-Reports:", error);
      addNotification("error", "Error", "Failed to load Z-Reports");
    } finally {
      setLoading(false);
    }
  };

  const fetchDevices = async () => {
    try {
      // Mock devices data
      const mockDevices: EFDDevice[] = [
        {
          id: '1',
          deviceSerial: 'EFD001234567',
          deviceModel: 'TREMOL FP-2000',
          locationName: 'Main Store',
          status: 'ACTIVE',
        },
        {
          id: '2',
          deviceSerial: 'EFD001234568',
          deviceModel: 'DATECS FP-800',
          locationName: 'Branch Store',
          status: 'ACTIVE',
        },
      ];

      setDevices(mockDevices);
    } catch (error: any) {
      console.error("Error fetching EFD devices:", error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACCEPTED':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'SUBMITTED':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'GENERATED':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'REJECTED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACCEPTED':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'SUBMITTED':
        return <ClockIcon className="h-4 w-4" />;
      case 'GENERATED':
        return <ExclamationTriangleIcon className="h-4 w-4" />;
      case 'REJECTED':
        return <XCircleIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-TZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-TZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDeviceName = (deviceId: string) => {
    const device = devices.find(d => d.id === deviceId);
    return device ? `${device.deviceModel} (${device.locationName})` : 'Unknown Device';
  };

  const handleGenerateZReport = async (deviceId: string, reportDate: string) => {
    try {
      addNotification("info", "Generating", "Generating Z-Report...");
      // In a real implementation, this would call the API
      console.log("Generating Z-Report for device:", deviceId, "date:", reportDate);
      await fetchZReports(); // Refresh the list
      addNotification("success", "Success", "Z-Report generated successfully");
      setShowGenerateModal(false);
    } catch (error: any) {
      console.error("Error generating Z-Report:", error);
      addNotification("error", "Error", "Failed to generate Z-Report");
    }
  };

  const handleSubmitToTRA = async (report: EFDZReport) => {
    try {
      addNotification("info", "Submitting", "Submitting Z-Report to TRA...");
      // In a real implementation, this would call the API
      console.log("Submitting Z-Report to TRA:", report.id);
      await fetchZReports(); // Refresh the list
      addNotification("success", "Success", "Z-Report submitted to TRA successfully");
    } catch (error: any) {
      console.error("Error submitting to TRA:", error);
      addNotification("error", "Error", "Failed to submit Z-Report to TRA");
    }
  };

  const handleDownloadPDF = (report: EFDZReport) => {
    try {
      addNotification("info", "Downloading", "Generating Z-Report PDF...");
      // In a real implementation, this would trigger a PDF download
      console.log("Downloading PDF for Z-Report:", report.id);
      addNotification("success", "Success", "Z-Report PDF downloaded successfully");
    } catch (error: any) {
      console.error("Error downloading PDF:", error);
      addNotification("error", "Error", "Failed to download Z-Report PDF");
    }
  };

  const handlePrintReport = (report: EFDZReport) => {
    try {
      addNotification("info", "Printing", "Sending Z-Report to printer...");
      // In a real implementation, this would trigger printing
      console.log("Printing Z-Report:", report.id);
      addNotification("success", "Success", "Z-Report sent to printer");
    } catch (error: any) {
      console.error("Error printing report:", error);
      addNotification("error", "Error", "Failed to print Z-Report");
    }
  };

  const filteredZReports = zReports.filter(report => {
    const matchesSearch = report.zReportNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.fiscalDayIdentifier.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         getDeviceName(report.efdDeviceId).toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || report.reportStatus === statusFilter;
    const matchesDevice = deviceFilter === 'all' || report.efdDeviceId === deviceFilter;

    let matchesDate = true;
    if (dateFilter !== 'all') {
      const reportDate = new Date(report.reportDate);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const lastWeek = new Date(today);
      lastWeek.setDate(lastWeek.getDate() - 7);

      switch (dateFilter) {
        case 'today':
          matchesDate = reportDate.toDateString() === today.toDateString();
          break;
        case 'yesterday':
          matchesDate = reportDate.toDateString() === yesterday.toDateString();
          break;
        case 'last-week':
          matchesDate = reportDate >= lastWeek;
          break;
      }
    }

    return matchesSearch && matchesStatus && matchesDevice && matchesDate;
  });

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <DocumentTextIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            EFD Z-Reports Management
          </h3>
        </div>
        <button
          onClick={() => setShowGenerateModal(true)}
          className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors duration-200"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Generate Z-Report
        </button>
      </div>

      {/* Z-Reports Information */}
      <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <InformationCircleIcon className="h-5 w-5 text-purple-600 dark:text-purple-400 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-1">
              EFD Z-Reports Management - Tanzania
            </h4>
            <p className="text-sm text-purple-800 dark:text-purple-200">
              Generate, view, and manage daily Z-Reports for Electronic Fiscal Device compliance.
              Submit reports to TRA, track submission status, and download PDF reports.
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div className="lg:col-span-2">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search by Z-Report number, fiscal ID, or device..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
        <div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="all">All Status</option>
            <option value="GENERATED">Generated</option>
            <option value="SUBMITTED">Submitted</option>
            <option value="ACCEPTED">Accepted</option>
            <option value="REJECTED">Rejected</option>
          </select>
        </div>
        <div>
          <select
            value={deviceFilter}
            onChange={(e) => setDeviceFilter(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="all">All Devices</option>
            {devices.map((device) => (
              <option key={device.id} value={device.id}>
                {device.deviceModel} ({device.locationName})
              </option>
            ))}
          </select>
        </div>
        <div>
          <select
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="all">All Dates</option>
            <option value="today">Today</option>
            <option value="yesterday">Yesterday</option>
            <option value="last-week">Last Week</option>
          </select>
        </div>
      </div>

      {loading ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading Z-Reports...</p>
        </div>
      ) : (
        <>
          {/* Z-Reports Table */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Z-Report
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Transactions
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Sales Summary
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Device
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Report Date
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredZReports.map((report) => (
                    <tr key={report.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <DocumentTextIcon className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {report.zReportNumber}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {report.fiscalDayIdentifier}
                            </div>
                            {report.efdSignature && (
                              <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                Signature: {report.efdSignature.substring(0, 16)}...
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col space-y-1">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(report.reportStatus)}`}>
                            {getStatusIcon(report.reportStatus)}
                            <span className="ml-1">{report.reportStatus}</span>
                          </span>
                          {report.submittedToTra && (
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Submitted: {formatDateTime(report.submittedToTra)}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          <div className="font-medium">
                            Total: {report.totalTransactions}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 space-y-0.5">
                            <div>Sales: {report.salesCount}</div>
                            <div>Refunds: {report.refundCount}</div>
                            <div>Voids: {report.voidCount}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          <div className="font-medium">
                            {formatCurrency(report.totalGrossSales)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 space-y-0.5">
                            <div>VAT: {formatCurrency(report.totalVatAmount)}</div>
                            <div>Net: {formatCurrency(report.totalNetSales)}</div>
                            <div>Discounts: {formatCurrency(report.totalDiscounts)}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {getDeviceName(report.efdDeviceId)}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 space-y-0.5 mt-1">
                          <div>Cash: {formatCurrency(report.cashTotal)}</div>
                          <div>Card: {formatCurrency(report.cardTotal)}</div>
                          <div>Mobile: {formatCurrency(report.mobileMoney)}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {formatDate(report.reportDate)}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {formatDateTime(report.reportTimestamp)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => {
                              setSelectedReport(report);
                              setShowViewModal(true);
                            }}
                            className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"
                            title="View Details"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDownloadPDF(report)}
                            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                            title="Download PDF"
                          >
                            <ArrowDownTrayIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handlePrintReport(report)}
                            className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                            title="Print Report"
                          >
                            <PrinterIcon className="h-4 w-4" />
                          </button>
                          {report.reportStatus === 'GENERATED' && (
                            <button
                              onClick={() => handleSubmitToTRA(report)}
                              className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                              title="Submit to TRA"
                            >
                              <ArrowPathIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredZReports.length === 0 && (
              <div className="p-8 text-center">
                <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No Z-Reports found
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  {searchTerm || statusFilter !== 'all' || deviceFilter !== 'all' || dateFilter !== 'all'
                    ? 'No Z-Reports match your search criteria.'
                    : 'Get started by generating your first Z-Report.'}
                </p>
                {!searchTerm && statusFilter === 'all' && deviceFilter === 'all' && dateFilter === 'all' && (
                  <button
                    onClick={() => setShowGenerateModal(true)}
                    className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors duration-200"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Generate Z-Report
                  </button>
                )}
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
