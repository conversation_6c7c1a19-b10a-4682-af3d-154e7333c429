import { useState, useEffect } from "react";
import {
  ChartBarIcon,
  ArrowDownTrayIcon,
  ComputerDesktopIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ClockIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  ChartPieIcon,
  PresentationChartLineIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { apiService } from "../../services/api";
import { TrendingDownIcon, TrendingUpIcon } from "lucide-react";

interface EFDStatistics {
  devices: {
    total: number;
    active: number;
    inactive: number;
    expired: number;
    performance: {
      deviceId: string;
      deviceName: string;
      transactionCount: number;
      uptime: number;
      lastSync: string;
    }[];
  };
  transactions: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    totalAmount: number;
    trends: {
      date: string;
      count: number;
      amount: number;
    }[];
    byPaymentMethod: {
      method: string;
      count: number;
      amount: number;
    }[];
    byType: {
      type: string;
      count: number;
      amount: number;
    }[];
  };
  zReports: {
    generated: number;
    submitted: number;
    pending: number;
    accepted: number;
    rejected: number;
    submissionRate: number;
    trends: {
      date: string;
      generated: number;
      submitted: number;
    }[];
  };
  compliance: {
    score: number;
    issues: number;
    lastZReport: string;
    monthlyScores: {
      month: string;
      score: number;
    }[];
    issueBreakdown: {
      category: string;
      count: number;
    }[];
  };
}

export default function EFDStatistics() {
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<EFDStatistics | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<string>('current-month');
  const [refreshing, setRefreshing] = useState(false);

  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  useEffect(() => {
    if (currentCompany) {
      fetchStatistics();
    }
  }, [currentCompany, selectedPeriod]);

  const fetchStatistics = async () => {
    setLoading(true);
    try {
      // Mock comprehensive statistics data
      const mockStatistics: EFDStatistics = {
        devices: {
          total: 2,
          active: 2,
          inactive: 0,
          expired: 0,
          performance: [
            {
              deviceId: '1',
              deviceName: 'TREMOL FP-2000 (Main Store)',
              transactionCount: 1247,
              uptime: 99.2,
              lastSync: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            },
            {
              deviceId: '2',
              deviceName: 'DATECS FP-800 (Branch Store)',
              transactionCount: 892,
              uptime: 98.7,
              lastSync: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
            },
          ],
        },
        transactions: {
          today: 45,
          thisWeek: 312,
          thisMonth: 1247,
          totalAmount: 15750000,
          trends: [
            { date: '2024-06-06', count: 38, amount: 1890000 },
            { date: '2024-06-07', count: 42, amount: 2100000 },
            { date: '2024-06-08', count: 35, amount: 1750000 },
            { date: '2024-06-09', count: 48, amount: 2400000 },
            { date: '2024-06-10', count: 41, amount: 2050000 },
            { date: '2024-06-11', count: 38, amount: 1890000 },
            { date: '2024-06-12', count: 45, amount: 2250000 },
          ],
          byPaymentMethod: [
            { method: 'Cash', count: 687, amount: 8925000 },
            { method: 'Card', count: 423, amount: 5512500 },
            { method: 'Mobile Money', count: 137, amount: 1312500 },
          ],
          byType: [
            { type: 'Sale', count: 1205, amount: 15187500 },
            { type: 'Refund', count: 35, amount: -437500 },
            { type: 'Void', count: 7, amount: 0 },
          ],
        },
        zReports: {
          generated: 30,
          submitted: 28,
          pending: 2,
          accepted: 26,
          rejected: 2,
          submissionRate: 93.3,
          trends: [
            { date: '2024-06-06', generated: 2, submitted: 2 },
            { date: '2024-06-07', generated: 2, submitted: 2 },
            { date: '2024-06-08', generated: 2, submitted: 2 },
            { date: '2024-06-09', generated: 2, submitted: 2 },
            { date: '2024-06-10', generated: 2, submitted: 2 },
            { date: '2024-06-11', generated: 2, submitted: 2 },
            { date: '2024-06-12', generated: 2, submitted: 0 },
          ],
        },
        compliance: {
          score: 98,
          issues: 1,
          lastZReport: new Date().toISOString(),
          monthlyScores: [
            { month: 'Jan', score: 95 },
            { month: 'Feb', score: 97 },
            { month: 'Mar', score: 96 },
            { month: 'Apr', score: 98 },
            { month: 'May', score: 99 },
            { month: 'Jun', score: 98 },
          ],
          issueBreakdown: [
            { category: 'Pending Z-Reports', count: 2 },
            { category: 'Certificate Expiry', count: 0 },
            { category: 'Device Offline', count: 0 },
            { category: 'Failed Submissions', count: 1 },
          ],
        },
      };

      setStatistics(mockStatistics);
      addNotification("success", "Success", "EFD statistics loaded successfully");
    } catch (error: any) {
      console.error("Error fetching EFD statistics:", error);
      addNotification("error", "Error", "Failed to load EFD statistics");
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchStatistics();
    setRefreshing(false);
  };

  const handleExportReport = async () => {
    try {
      addNotification("info", "Exporting", "Generating EFD compliance report...");
      // In a real implementation, this would trigger a report download
      console.log("Exporting EFD compliance report for period:", selectedPeriod);
      addNotification("success", "Success", "EFD compliance report exported successfully");
    } catch (error: any) {
      console.error("Error exporting report:", error);
      addNotification("error", "Error", "Failed to export compliance report");
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-TZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getComplianceScoreColor = (score: number) => {
    if (score >= 95) return 'text-green-600 dark:text-green-400';
    if (score >= 85) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getComplianceScoreBg = (score: number) => {
    if (score >= 95) return 'bg-green-100 dark:bg-green-900/20';
    if (score >= 85) return 'bg-yellow-100 dark:bg-yellow-900/20';
    return 'bg-red-100 dark:bg-red-900/20';
  };

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) {
      return <TrendingUpIcon className="h-4 w-4 text-green-500" />;
    } else if (current < previous) {
      return <TrendingDownIcon className="h-4 w-4 text-red-500" />;
    }
    return <div className="h-4 w-4" />;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <ChartBarIcon className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              EFD Compliance Statistics
            </h3>
          </div>
        </div>
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading EFD statistics...</p>
        </div>
      </div>
    );
  }

  if (!statistics) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <ChartBarIcon className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              EFD Compliance Statistics
            </h3>
          </div>
        </div>
        <div className="p-8 text-center">
          <ExclamationTriangleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No statistics available
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            Unable to load EFD compliance statistics.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <ChartBarIcon className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            EFD Compliance Statistics
          </h3>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="current-month">Current Month</option>
            <option value="last-month">Last Month</option>
            <option value="current-quarter">Current Quarter</option>
            <option value="current-year">Current Year</option>
          </select>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button
            onClick={handleExportReport}
            className="flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors duration-200"
          >
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            Export Report
          </button>
        </div>
      </div>

      {/* EFD Statistics Information */}
      <div className="bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <InformationCircleIcon className="h-5 w-5 text-indigo-600 dark:text-indigo-400 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-indigo-900 dark:text-indigo-100 mb-1">
              EFD Compliance Analytics - Tanzania
            </h4>
            <p className="text-sm text-indigo-800 dark:text-indigo-200">
              Comprehensive analytics and statistics for Electronic Fiscal Device compliance.
              Monitor transaction trends, device performance, and compliance scores.
            </p>
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Compliance Score Card */}
        <div className={`rounded-lg p-6 border ${getComplianceScoreBg(statistics.compliance.score)} border-opacity-20`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Compliance Score</p>
              <p className={`text-3xl font-bold ${getComplianceScoreColor(statistics.compliance.score)}`}>
                {statistics.compliance.score}%
              </p>
            </div>
            <CheckCircleIcon className={`h-8 w-8 ${getComplianceScoreColor(statistics.compliance.score)}`} />
          </div>
          <div className="mt-4">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {statistics.compliance.issues} active issues
            </p>
          </div>
        </div>

        {/* Total Transactions Card */}
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Transactions</p>
              <p className="text-3xl font-bold text-blue-900 dark:text-blue-100">
                {statistics.transactions.thisMonth.toLocaleString()}
              </p>
            </div>
            <DocumentTextIcon className="h-8 w-8 text-blue-500" />
          </div>
          <div className="mt-4 flex items-center">
            {getTrendIcon(statistics.transactions.thisWeek, 280)}
            <p className="text-xs text-blue-700 dark:text-blue-300 ml-1">
              {statistics.transactions.thisWeek} this week
            </p>
          </div>
        </div>

        {/* Total Value Card */}
        <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-6 border border-green-200 dark:border-green-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600 dark:text-green-400">Total Value</p>
              <p className="text-3xl font-bold text-green-900 dark:text-green-100">
                {formatCurrency(statistics.transactions.totalAmount)}
              </p>
            </div>
            <CurrencyDollarIcon className="h-8 w-8 text-green-500" />
          </div>
          <div className="mt-4 flex items-center">
            {getTrendIcon(statistics.transactions.totalAmount, 14200000)}
            <p className="text-xs text-green-700 dark:text-green-300 ml-1">
              vs last month
            </p>
          </div>
        </div>

        {/* Active Devices Card */}
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-6 border border-purple-200 dark:border-purple-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Active Devices</p>
              <p className="text-3xl font-bold text-purple-900 dark:text-purple-100">
                {statistics.devices.active}/{statistics.devices.total}
              </p>
            </div>
            <ComputerDesktopIcon className="h-8 w-8 text-purple-500" />
          </div>
          <div className="mt-4">
            <p className="text-xs text-purple-700 dark:text-purple-300">
              {formatPercentage((statistics.devices.active / statistics.devices.total) * 100)} uptime
            </p>
          </div>
        </div>
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Transaction Trends Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">Transaction Trends</h4>
            <PresentationChartLineIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {statistics.transactions.trends.map((trend, index) => (
              <div key={trend.date} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {new Date(trend.date).toLocaleDateString('en-TZ', { month: 'short', day: 'numeric' })}
                  </span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {trend.count} transactions
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {formatCurrency(trend.amount)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Payment Methods Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">Payment Methods</h4>
            <ChartPieIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {statistics.transactions.byPaymentMethod.map((method, index) => {
              const colors = ['bg-blue-500', 'bg-green-500', 'bg-yellow-500'];
              const percentage = (method.count / statistics.transactions.thisMonth) * 100;
              return (
                <div key={method.method} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 ${colors[index]} rounded-full`}></div>
                      <span className="text-sm text-gray-600 dark:text-gray-400">{method.method}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {method.count} ({formatPercentage(percentage)})
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {formatCurrency(method.amount)}
                      </div>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className={`${colors[index]} h-2 rounded-full transition-all duration-300`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Device Performance and Z-Reports */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Device Performance */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">Device Performance</h4>
            <ComputerDesktopIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {statistics.devices.performance.map((device) => (
              <div key={device.deviceId} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h5 className="text-sm font-medium text-gray-900 dark:text-white">
                    {device.deviceName}
                  </h5>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    device.uptime >= 99
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : device.uptime >= 95
                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                  }`}>
                    {formatPercentage(device.uptime)} uptime
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500 dark:text-gray-400">Transactions</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {device.transactionCount.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-500 dark:text-gray-400">Last Sync</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {formatDateTime(device.lastSync)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Z-Reports Analytics */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">Z-Reports Analytics</h4>
            <DocumentTextIcon className="h-5 w-5 text-gray-400" />
          </div>

          {/* Z-Report Summary */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {statistics.zReports.generated}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">Generated</p>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                {formatPercentage(statistics.zReports.submissionRate)}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">Submission Rate</p>
            </div>
          </div>

          {/* Z-Report Status Breakdown */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Accepted</span>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {statistics.zReports.accepted}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Submitted</span>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {statistics.zReports.submitted}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Pending</span>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {statistics.zReports.pending}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Rejected</span>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {statistics.zReports.rejected}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Compliance Tracking */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-6">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white">Compliance Tracking</h4>
          <ChartBarIcon className="h-5 w-5 text-gray-400" />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Monthly Compliance Scores */}
          <div>
            <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-4">Monthly Compliance Scores</h5>
            <div className="space-y-3">
              {statistics.compliance.monthlyScores.map((score) => (
                <div key={score.month} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">{score.month}</span>
                  <div className="flex items-center space-x-3">
                    <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          score.score >= 95 ? 'bg-green-500' : score.score >= 85 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${score.score}%` }}
                      ></div>
                    </div>
                    <span className={`text-sm font-medium ${getComplianceScoreColor(score.score)}`}>
                      {score.score}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Issue Breakdown */}
          <div>
            <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-4">Issue Breakdown</h5>
            <div className="space-y-3">
              {statistics.compliance.issueBreakdown.map((issue, index) => (
                <div key={issue.category} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">{issue.category}</span>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    issue.count === 0
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : issue.count <= 2
                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                  }`}>
                    {issue.count}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Last Z-Report Info */}
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">Last Z-Report Generated</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {formatDateTime(statistics.compliance.lastZReport)}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <ClockIcon className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {Math.floor((new Date().getTime() - new Date(statistics.compliance.lastZReport).getTime()) / (1000 * 60 * 60))} hours ago
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
