import { useState, useEffect } from "react";
import {
  ComputerDesktopIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  WifiIcon,
  SignalIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  Cog6ToothIcon,
  DocumentTextIcon,
  InformationCircleIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { apiService } from "../../services/api";

interface EFDDevice {
  id: string;
  companyId: string;
  deviceSerial: string;
  deviceModel: string;
  manufacturer: string;
  firmwareVersion?: string;
  traDeviceId: string;
  traCertificateNumber?: string;
  registrationDate: string;
  certificateExpiry?: string;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'EXPIRED';
  connectionType: 'USB' | 'ETHERNET' | 'WIFI' | 'BLUETOOTH';
  ipAddress?: string;
  port?: number;
  locationName?: string;
  physicalAddress?: string;
  latitude?: number;
  longitude?: number;
  deviceSettings?: any;
  lastSync?: string;
  lastHeartbeat?: string;
}

export default function EFDDevices() {
  const [loading, setLoading] = useState(false);
  const [devices, setDevices] = useState<EFDDevice[]>([]);
  const [showRegisterModal, setShowRegisterModal] = useState(false);
  const [showConfigModal, setShowConfigModal] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<EFDDevice | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  useEffect(() => {
    if (currentCompany) {
      fetchDevices();
    }
  }, [currentCompany]);

  const fetchDevices = async () => {
    setLoading(true);
    try {
      // Mock data for demonstration
      const mockDevices: EFDDevice[] = [
        {
          id: '1',
          companyId: currentCompany?.id || '',
          deviceSerial: 'EFD001234567',
          deviceModel: 'TREMOL FP-2000',
          manufacturer: 'TREMOL',
          firmwareVersion: '1.2.3',
          traDeviceId: 'TRA-EFD-001234',
          traCertificateNumber: 'CERT-2024-001234',
          registrationDate: '2024-01-15',
          certificateExpiry: '2025-01-15',
          status: 'ACTIVE',
          connectionType: 'ETHERNET',
          ipAddress: '*************',
          port: 8080,
          locationName: 'Main Store',
          physicalAddress: 'Dar es Salaam, Tanzania',
          latitude: -6.7924,
          longitude: 39.2083,
          lastSync: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          lastHeartbeat: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
        },
        {
          id: '2',
          companyId: currentCompany?.id || '',
          deviceSerial: 'EFD001234568',
          deviceModel: 'DATECS FP-800',
          manufacturer: 'DATECS',
          firmwareVersion: '2.1.0',
          traDeviceId: 'TRA-EFD-001235',
          traCertificateNumber: 'CERT-2024-001235',
          registrationDate: '2024-02-01',
          certificateExpiry: '2025-02-01',
          status: 'ACTIVE',
          connectionType: 'USB',
          locationName: 'Branch Store',
          physicalAddress: 'Arusha, Tanzania',
          latitude: -3.3869,
          longitude: 36.6830,
          lastSync: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          lastHeartbeat: new Date(Date.now() - 1 * 60 * 1000).toISOString(),
        },
      ];

      setDevices(mockDevices);
      addNotification("success", "Success", "EFD devices loaded successfully");
    } catch (error: any) {
      console.error("Error fetching EFD devices:", error);
      addNotification("error", "Error", "Failed to load EFD devices");
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'INACTIVE':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      case 'SUSPENDED':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'EXPIRED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'INACTIVE':
        return <ClockIcon className="h-4 w-4" />;
      case 'SUSPENDED':
      case 'EXPIRED':
        return <ExclamationTriangleIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const getConnectionIcon = (connectionType: string) => {
    switch (connectionType) {
      case 'ETHERNET':
      case 'WIFI':
        return <WifiIcon className="h-4 w-4" />;
      case 'USB':
      case 'BLUETOOTH':
        return <SignalIcon className="h-4 w-4" />;
      default:
        return <SignalIcon className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-TZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-TZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getLastHeartbeatStatus = (lastHeartbeat?: string) => {
    if (!lastHeartbeat) return { status: 'unknown', text: 'Unknown', color: 'text-gray-500' };

    const now = new Date();
    const heartbeat = new Date(lastHeartbeat);
    const diffMinutes = Math.floor((now.getTime() - heartbeat.getTime()) / (1000 * 60));

    if (diffMinutes < 5) {
      return { status: 'online', text: 'Online', color: 'text-green-600 dark:text-green-400' };
    } else if (diffMinutes < 15) {
      return { status: 'warning', text: 'Warning', color: 'text-yellow-600 dark:text-yellow-400' };
    } else {
      return { status: 'offline', text: 'Offline', color: 'text-red-600 dark:text-red-400' };
    }
  };

  const filteredDevices = devices.filter(device => {
    const matchesSearch = device.deviceSerial.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         device.deviceModel.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         device.locationName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         device.traDeviceId.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || device.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <ComputerDesktopIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            EFD Device Management
          </h3>
        </div>
        <button
          onClick={() => setShowRegisterModal(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Register EFD Device
        </button>
      </div>

      {/* EFD Information */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <InformationCircleIcon className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
              Electronic Fiscal Device (EFD) Management - Tanzania
            </h4>
            <p className="text-sm text-blue-800 dark:text-blue-200">
              Register, configure, and monitor your EFD devices for TRA compliance.
              Manage device certificates, monitor health status, and track transaction history.
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search devices by serial, model, location, or TRA ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
        <div className="sm:w-48">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="all">All Status</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
            <option value="SUSPENDED">Suspended</option>
            <option value="EXPIRED">Expired</option>
          </select>
        </div>
      </div>

      {loading ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading EFD devices...</p>
        </div>
      ) : (
        <>
          {/* Devices Table */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Device
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Connection
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Certificate
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Last Activity
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredDevices.map((device) => {
                    const heartbeatStatus = getLastHeartbeatStatus(device.lastHeartbeat);
                    const isExpiringSoon = device.certificateExpiry &&
                      new Date(device.certificateExpiry).getTime() - new Date().getTime() < 30 * 24 * 60 * 60 * 1000;

                    return (
                      <tr key={device.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0">
                              <ComputerDesktopIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {device.deviceModel}
                              </div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                {device.deviceSerial}
                              </div>
                              <div className="text-xs text-gray-400 dark:text-gray-500">
                                {device.manufacturer} • v{device.firmwareVersion}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex flex-col space-y-1">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(device.status)}`}>
                              {getStatusIcon(device.status)}
                              <span className="ml-1">{device.status}</span>
                            </span>
                            <span className={`text-xs ${heartbeatStatus.color}`}>
                              {heartbeatStatus.text}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {getConnectionIcon(device.connectionType)}
                            <div className="ml-2">
                              <div className="text-sm text-gray-900 dark:text-white">
                                {device.connectionType}
                              </div>
                              {device.ipAddress && (
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  {device.ipAddress}:{device.port}
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">
                            {device.locationName}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {device.physicalAddress}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">
                            {device.traCertificateNumber}
                          </div>
                          {device.certificateExpiry && (
                            <div className={`text-xs ${isExpiringSoon ? 'text-red-600 dark:text-red-400' : 'text-gray-500 dark:text-gray-400'}`}>
                              Expires: {formatDate(device.certificateExpiry)}
                              {isExpiringSoon && ' (Soon)'}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">
                            {device.lastSync ? formatDateTime(device.lastSync) : 'Never'}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Last sync
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center justify-end space-x-2">
                            <button
                              onClick={() => {
                                setSelectedDevice(device);
                                setShowConfigModal(true);
                              }}
                              className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                              title="Configure Device"
                            >
                              <Cog6ToothIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => setSelectedDevice(device)}
                              className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                              title="View Details"
                            >
                              <EyeIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => setSelectedDevice(device)}
                              className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                              title="View Transactions"
                            >
                              <DocumentTextIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {filteredDevices.length === 0 && (
              <div className="p-8 text-center">
                <ComputerDesktopIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No EFD devices found
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  {searchTerm || statusFilter !== 'all'
                    ? 'No devices match your search criteria.'
                    : 'Get started by registering your first EFD device.'}
                </p>
                {!searchTerm && statusFilter === 'all' && (
                  <button
                    onClick={() => setShowRegisterModal(true)}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Register EFD Device
                  </button>
                )}
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
