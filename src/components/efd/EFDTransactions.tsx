import { useState, useEffect } from "react";
import {
  DocumentTextIcon,
  PlusIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  QrCodeIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  PrinterIcon,
  InformationCircleIcon,
  CurrencyDollarIcon,
  ReceiptRefundIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { apiService } from "../../services/api";

interface EFDTransaction {
  id: string;
  companyId: string;
  efdDeviceId: string;
  transactionId?: string;
  invoiceId?: string;
  efdReceiptNumber: string;
  efdInternalNumber?: string;
  fiscalCode: string;
  qrCode?: string;
  transactionType: 'SALE' | 'REFUND' | 'VOID' | 'TRAINING' | 'COPY';
  grossAmount: number;
  vatAmount: number;
  netAmount: number;
  discountAmount: number;
  paymentMethod: 'CASH' | 'CARD' | 'MOBILE_MONEY' | 'BANK_TRANSFER' | 'CREDIT' | 'MIXED';
  cashAmount: number;
  cardAmount: number;
  mobileMoney: number;
  otherAmount: number;
  customerName?: string;
  customerTin?: string;
  customerVrn?: string;
  customerMobile?: string;
  efdTimestamp: string;
  submissionStatus: 'PENDING' | 'SUBMITTED' | 'ACCEPTED' | 'REJECTED';
  submittedToTra?: string;
  traResponse?: any;
  rejectionReason?: string;
  zReportNumber?: string;
  zReportDate?: string;
  createdAt: string;
  updatedAt: string;
}

interface EFDDevice {
  id: string;
  deviceSerial: string;
  deviceModel: string;
  locationName?: string;
  status: string;
}

export default function EFDTransactions() {
  const [loading, setLoading] = useState(false);
  const [transactions, setTransactions] = useState<EFDTransaction[]>([]);
  const [devices, setDevices] = useState<EFDDevice[]>([]);
  const [showRecordModal, setShowRecordModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<EFDTransaction | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [deviceFilter, setDeviceFilter] = useState<string>('all');

  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  useEffect(() => {
    if (currentCompany) {
      fetchTransactions();
      fetchDevices();
    }
  }, [currentCompany]);

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      // Mock data for demonstration
      const mockTransactions: EFDTransaction[] = [
        {
          id: '1',
          companyId: currentCompany?.id || '',
          efdDeviceId: '1',
          efdReceiptNumber: 'EFD-2024-001234',
          efdInternalNumber: 'INT-001234',
          fiscalCode: 'FC-20240612-001234',
          qrCode: 'https://verify.tra.go.tz/receipt/FC-20240612-001234',
          transactionType: 'SALE',
          grossAmount: 125000,
          vatAmount: 22500,
          netAmount: 102500,
          discountAmount: 0,
          paymentMethod: 'CASH',
          cashAmount: 125000,
          cardAmount: 0,
          mobileMoney: 0,
          otherAmount: 0,
          customerName: 'John Doe',
          customerTin: '***********',
          customerMobile: '+255712345678',
          efdTimestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          submissionStatus: 'ACCEPTED',
          submittedToTra: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          traResponse: { status: 'SUCCESS', receiptId: 'TRA-001234' },
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: '2',
          companyId: currentCompany?.id || '',
          efdDeviceId: '1',
          efdReceiptNumber: 'EFD-2024-001235',
          efdInternalNumber: 'INT-001235',
          fiscalCode: 'FC-20240612-001235',
          qrCode: 'https://verify.tra.go.tz/receipt/FC-20240612-001235',
          transactionType: 'SALE',
          grossAmount: 85000,
          vatAmount: 15300,
          netAmount: 69700,
          discountAmount: 5000,
          paymentMethod: 'CARD',
          cashAmount: 0,
          cardAmount: 85000,
          mobileMoney: 0,
          otherAmount: 0,
          customerName: 'Jane Smith',
          customerVrn: 'VRN-*********',
          customerMobile: '+255723456789',
          efdTimestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          submissionStatus: 'SUBMITTED',
          submittedToTra: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: '3',
          companyId: currentCompany?.id || '',
          efdDeviceId: '2',
          efdReceiptNumber: 'EFD-2024-001236',
          efdInternalNumber: 'INT-001236',
          fiscalCode: 'FC-20240612-001236',
          transactionType: 'REFUND',
          grossAmount: -25000,
          vatAmount: -4500,
          netAmount: -20500,
          discountAmount: 0,
          paymentMethod: 'CASH',
          cashAmount: -25000,
          cardAmount: 0,
          mobileMoney: 0,
          otherAmount: 0,
          customerName: 'Michael Johnson',
          efdTimestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          submissionStatus: 'PENDING',
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        },
      ];

      setTransactions(mockTransactions);
      addNotification("success", "Success", "EFD transactions loaded successfully");
    } catch (error: any) {
      console.error("Error fetching EFD transactions:", error);
      addNotification("error", "Error", "Failed to load EFD transactions");
    } finally {
      setLoading(false);
    }
  };

  const fetchDevices = async () => {
    try {
      // Mock devices data
      const mockDevices: EFDDevice[] = [
        {
          id: '1',
          deviceSerial: 'EFD001234567',
          deviceModel: 'TREMOL FP-2000',
          locationName: 'Main Store',
          status: 'ACTIVE',
        },
        {
          id: '2',
          deviceSerial: 'EFD001234568',
          deviceModel: 'DATECS FP-800',
          locationName: 'Branch Store',
          status: 'ACTIVE',
        },
      ];

      setDevices(mockDevices);
    } catch (error: any) {
      console.error("Error fetching EFD devices:", error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACCEPTED':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'SUBMITTED':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'REJECTED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACCEPTED':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'SUBMITTED':
        return <ClockIcon className="h-4 w-4" />;
      case 'PENDING':
        return <ExclamationTriangleIcon className="h-4 w-4" />;
      case 'REJECTED':
        return <XCircleIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'SALE':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'REFUND':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'VOID':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      case 'TRAINING':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'COPY':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'SALE':
        return <CurrencyDollarIcon className="h-4 w-4" />;
      case 'REFUND':
        return <ReceiptRefundIcon className="h-4 w-4" />;
      case 'VOID':
        return <TrashIcon className="h-4 w-4" />;
      case 'TRAINING':
        return <InformationCircleIcon className="h-4 w-4" />;
      case 'COPY':
        return <DocumentTextIcon className="h-4 w-4" />;
      default:
        return <DocumentTextIcon className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-TZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDeviceName = (deviceId: string) => {
    const device = devices.find(d => d.id === deviceId);
    return device ? `${device.deviceModel} (${device.locationName})` : 'Unknown Device';
  };

  const handleSubmitToTRA = async (transaction: EFDTransaction) => {
    try {
      addNotification("info", "Submitting", "Submitting transaction to TRA...");
      // In a real implementation, this would call the API
      console.log("Submitting transaction to TRA:", transaction.id);
      await fetchTransactions(); // Refresh the list
      addNotification("success", "Success", "Transaction submitted to TRA successfully");
    } catch (error: any) {
      console.error("Error submitting to TRA:", error);
      addNotification("error", "Error", "Failed to submit transaction to TRA");
    }
  };

  const handlePrintReceipt = (transaction: EFDTransaction) => {
    try {
      addNotification("info", "Printing", "Generating fiscal receipt...");
      // In a real implementation, this would trigger receipt printing
      console.log("Printing receipt for transaction:", transaction.id);
      addNotification("success", "Success", "Receipt sent to printer");
    } catch (error: any) {
      console.error("Error printing receipt:", error);
      addNotification("error", "Error", "Failed to print receipt");
    }
  };

  const handleExportData = async () => {
    try {
      addNotification("info", "Exporting", "Generating transaction export...");
      // In a real implementation, this would trigger a file download
      console.log("Exporting transaction data");
      addNotification("success", "Success", "Transaction data exported successfully");
    } catch (error: any) {
      console.error("Error exporting data:", error);
      addNotification("error", "Error", "Failed to export transaction data");
    }
  };

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = transaction.efdReceiptNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.fiscalCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.customerTin?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || transaction.submissionStatus === statusFilter;
    const matchesType = typeFilter === 'all' || transaction.transactionType === typeFilter;
    const matchesDevice = deviceFilter === 'all' || transaction.efdDeviceId === deviceFilter;

    return matchesSearch && matchesStatus && matchesType && matchesDevice;
  });

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <DocumentTextIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            EFD Transaction Management
          </h3>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleExportData}
            className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
          >
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            Export Data
          </button>
          <button
            onClick={() => setShowRecordModal(true)}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Record EFD Transaction
          </button>
        </div>
      </div>

      {/* EFD Transaction Information */}
      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <InformationCircleIcon className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-1">
              EFD Transaction Management - Tanzania
            </h4>
            <p className="text-sm text-green-800 dark:text-green-200">
              Record, view, and manage Electronic Fiscal Device transactions.
              Generate fiscal receipts with QR codes, track submission status, and handle refunds and voids.
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div className="lg:col-span-2">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search by receipt number, fiscal code, or customer..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
        <div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="all">All Status</option>
            <option value="PENDING">Pending</option>
            <option value="SUBMITTED">Submitted</option>
            <option value="ACCEPTED">Accepted</option>
            <option value="REJECTED">Rejected</option>
          </select>
        </div>
        <div>
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="all">All Types</option>
            <option value="SALE">Sale</option>
            <option value="REFUND">Refund</option>
            <option value="VOID">Void</option>
            <option value="TRAINING">Training</option>
            <option value="COPY">Copy</option>
          </select>
        </div>
        <div>
          <select
            value={deviceFilter}
            onChange={(e) => setDeviceFilter(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="all">All Devices</option>
            {devices.map((device) => (
              <option key={device.id} value={device.id}>
                {device.deviceModel} ({device.locationName})
              </option>
            ))}
          </select>
        </div>
      </div>

      {loading ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading EFD transactions...</p>
        </div>
      ) : (
        <>
          {/* Transactions Table */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Transaction
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Type & Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Device
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Date & Time
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredTransactions.map((transaction) => (
                    <tr key={transaction.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <DocumentTextIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {transaction.efdReceiptNumber}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {transaction.fiscalCode}
                            </div>
                            {transaction.qrCode && (
                              <div className="flex items-center mt-1">
                                <QrCodeIcon className="h-3 w-3 text-gray-400 mr-1" />
                                <span className="text-xs text-gray-400">QR Available</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col space-y-1">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(transaction.transactionType)}`}>
                            {getTypeIcon(transaction.transactionType)}
                            <span className="ml-1">{transaction.transactionType}</span>
                          </span>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(transaction.submissionStatus)}`}>
                            {getStatusIcon(transaction.submissionStatus)}
                            <span className="ml-1">{transaction.submissionStatus}</span>
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          <div className="font-medium">
                            {formatCurrency(transaction.grossAmount)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            VAT: {formatCurrency(transaction.vatAmount)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Net: {formatCurrency(transaction.netAmount)}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {transaction.customerName || 'Walk-in Customer'}
                        </div>
                        {transaction.customerTin && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            TIN: {transaction.customerTin}
                          </div>
                        )}
                        {transaction.customerVrn && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            VRN: {transaction.customerVrn}
                          </div>
                        )}
                        {transaction.customerMobile && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {transaction.customerMobile}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {getDeviceName(transaction.efdDeviceId)}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {transaction.paymentMethod}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {formatDateTime(transaction.efdTimestamp)}
                        </div>
                        {transaction.submittedToTra && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Submitted: {formatDateTime(transaction.submittedToTra)}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => {
                              setSelectedTransaction(transaction);
                              setShowViewModal(true);
                            }}
                            className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                            title="View Details"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          {transaction.qrCode && (
                            <button
                              onClick={() => window.open(transaction.qrCode, '_blank')}
                              className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                              title="View QR Code"
                            >
                              <QrCodeIcon className="h-4 w-4" />
                            </button>
                          )}
                          <button
                            onClick={() => handlePrintReceipt(transaction)}
                            className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"
                            title="Print Receipt"
                          >
                            <PrinterIcon className="h-4 w-4" />
                          </button>
                          {transaction.submissionStatus === 'PENDING' && (
                            <button
                              onClick={() => handleSubmitToTRA(transaction)}
                              className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                              title="Submit to TRA"
                            >
                              <ArrowPathIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredTransactions.length === 0 && (
              <div className="p-8 text-center">
                <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No EFD transactions found
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  {searchTerm || statusFilter !== 'all' || typeFilter !== 'all' || deviceFilter !== 'all'
                    ? 'No transactions match your search criteria.'
                    : 'Get started by recording your first EFD transaction.'}
                </p>
                {!searchTerm && statusFilter === 'all' && typeFilter === 'all' && deviceFilter === 'all' && (
                  <button
                    onClick={() => setShowRecordModal(true)}
                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Record EFD Transaction
                  </button>
                )}
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
