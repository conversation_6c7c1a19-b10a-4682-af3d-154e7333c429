import * as React from "react";
import { TrendingUp } from "lucide-react";
import { Label, Pie, Pie<PERSON><PERSON> } from "recharts";

import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

interface PieChartData {
  label: string;
  value: number;
  color: string;
}

interface ShadcnPieChartProps {
  title: string;
  data: PieChartData[];
  currency?: string;
  description?: string;
  trend?: {
    value: number;
    label: string;
  };
}

const ShadcnPieChart: React.FC<ShadcnPieChartProps> = ({
  title,
  data,
  currency = "USD",
  description,
  trend,
}) => {
  // Transform data for Recharts and create chart config
  const chartData = React.useMemo(() => {
    return data.map((item) => ({
      category: item.label,
      amount: item.value,
      fill: item.color,
    }));
  }, [data]);

  const chartConfig = React.useMemo(() => {
    const config: ChartConfig = {};
    data.forEach((item) => {
      config[item.label.toLowerCase().replace(/\s+/g, "_")] = {
        label: item.label,
        color: item.color,
      };
    });
    return config;
  }, [data]) satisfies ChartConfig;

  // Calculate total for center display
  const totalAmount = React.useMemo(() => {
    return data.reduce((acc, item) => acc + item.value, 0);
  }, [data]);

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  if (data.length === 0) {
    return (
      <div className="flex flex-col">
        <div className="flex items-center pb-2">
          <h3 className="text-lg font-semibold leading-none tracking-tight">
            {title}
          </h3>
        </div>
        {description && (
          <p className="text-sm text-muted-foreground pb-4">{description}</p>
        )}
        <div className="flex flex-1 items-center justify-center">
          <div className="flex flex-col items-center gap-2 text-center">
            <div className="text-muted-foreground">
              <svg
                className="h-12 w-12"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
            </div>
            <div className="text-sm font-medium">No data available</div>
            <div className="text-xs text-muted-foreground">
              Expense data will appear here
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      <div className="flex items-center pb-2">
        <h3 className="text-lg font-semibold leading-none tracking-tight">
          {title}
        </h3>
      </div>
      {description && (
        <p className="text-sm text-muted-foreground pb-4">{description}</p>
      )}
      <ChartContainer
        config={chartConfig}
        className="mx-auto aspect-square max-h-[300px]"
      >
        <PieChart>
          <ChartTooltip
            cursor={false}
            content={
              <ChartTooltipContent
                hideLabel
                formatter={(value, name) => [
                  formatCurrency(value as number),
                  name,
                ]}
              />
            }
          />
          <Pie
            data={chartData}
            dataKey="amount"
            nameKey="category"
            innerRadius={60}
            strokeWidth={5}
          >
            <Label
              content={({ viewBox }) => {
                if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                  return (
                    <text
                      x={viewBox.cx}
                      y={viewBox.cy}
                      textAnchor="middle"
                      dominantBaseline="middle"
                    >
                      <tspan
                        x={viewBox.cx}
                        y={viewBox.cy}
                        className="fill-foreground text-3xl font-bold"
                      >
                        {formatCurrency(totalAmount)}
                      </tspan>
                      <tspan
                        x={viewBox.cx}
                        y={(viewBox.cy || 0) + 24}
                        className="fill-muted-foreground"
                      >
                        Total
                      </tspan>
                    </text>
                  );
                }
              }}
            />
          </Pie>
        </PieChart>
      </ChartContainer>

      {/* Legend */}
      <div className="flex flex-wrap justify-center gap-4 pt-4">
        {data.map((item) => (
          <div key={item.label} className="flex items-center gap-2">
            <div
              className="h-3 w-3 rounded-sm"
              style={{ backgroundColor: item.color }}
            />
            <span className="text-sm text-muted-foreground">{item.label}</span>
            <span className="text-sm font-medium">
              {formatCurrency(item.value)}
            </span>
          </div>
        ))}
      </div>

      {/* Trend indicator */}
      {trend && (
        <div className="flex items-center gap-2 pt-4 text-sm">
          <TrendingUp className="h-4 w-4" />
          <span className="text-muted-foreground">{trend.label}</span>
        </div>
      )}
    </div>
  );
};

export default ShadcnPieChart;
