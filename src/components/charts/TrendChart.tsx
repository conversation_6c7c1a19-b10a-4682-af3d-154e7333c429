// import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
} from "recharts";
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { TrendingUp, TrendingDown } from "lucide-react";

interface TrendChartProps {
  title: string;
  data: Array<{
    period: string;
    value: number;
    label?: string;
  }>;
  color?: string;
  height?: number;
  showGrowth?: boolean;
  currency?: string;
  description?: string;
}

export default function TrendChart({
  title,
  data,
  color = "#3B82F6", // Blue color
  showGrowth = false,
  currency = "USD",
  description,
}: TrendChartProps) {
  // Chart configuration for shadcn
  const chartConfig = {
    value: {
      label: "Value",
      color: color,
    },
  } satisfies ChartConfig;

  // Format currency for tooltips
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  if (!data || data.length === 0) {
    return (
      <Card className="bg-white dark:bg-gray-800 border-gray-100 dark:border-gray-700">
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-muted-foreground mb-2">
                <svg
                  className="mx-auto h-12 w-12"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                  />
                </svg>
              </div>
              <p className="text-muted-foreground font-medium">
                No data available
              </p>
              <p className="text-muted-foreground text-sm">
                Trend data will appear here
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate growth percentage
  const growth = (() => {
    if (data.length < 2) return 0;

    const firstValue = data[0].value;
    const lastValue = data[data.length - 1].value;

    // Handle zero or negative starting values
    if (firstValue === 0) {
      return lastValue > 0 ? 100 : 0; // If we go from 0 to positive, show 100% growth
    }

    // Normal growth calculation
    return ((lastValue - firstValue) / Math.abs(firstValue)) * 100;
  })();

  // Calculate summary statistics
  const maxValue = Math.max(...data.map((d) => d.value));
  const currentValue = data[data.length - 1]?.value || 0;
  const averageValue = data.reduce((sum, d) => sum + d.value, 0) / data.length;

  return (
    <Card className="bg-white dark:bg-gray-800 border-gray-100 dark:border-gray-700">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          {showGrowth && !isNaN(growth) && isFinite(growth) && (
            <div
              className={`flex items-center text-sm font-medium ${
                growth >= 0 ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
              }`}
            >
              {growth >= 0 ? (
                <TrendingUp className="w-4 h-4 mr-1" />
              ) : (
                <TrendingDown className="w-4 h-4 mr-1" />
              )}
              {Math.abs(growth).toFixed(1)}%
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="w-full h-80">
          <LineChart
            data={data}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="period"
              tick={{ fontSize: 12 }}
              className="fill-muted-foreground"
              tickLine={{ stroke: 'hsl(var(--muted))' }}
              axisLine={{ stroke: 'hsl(var(--muted))' }}
            />
            <YAxis
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => formatCurrency(value)}
              className="fill-muted-foreground"
              tickLine={{ stroke: 'hsl(var(--muted))' }}
              axisLine={{ stroke: 'hsl(var(--muted))' }}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  formatter={(value, name) => [
                    formatCurrency(value as number),
                    name,
                  ]}
                />
              }
            />
            <Line
              type="monotone"
              dataKey="value"
              stroke={color}
              strokeWidth={3}
              dot={{ fill: color, strokeWidth: 2, r: 5 }}
              activeDot={{ r: 8, fill: color, stroke: "hsl(var(--background))", strokeWidth: 2 }}
              connectNulls={true}
            />
          </LineChart>
        </ChartContainer>

        {/* Summary stats */}
        <div className="mt-6 grid grid-cols-1 sm:grid-cols-3 gap-4 pt-4 border-t">
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Current</p>
            <p className="text-lg font-semibold">
              {formatCurrency(currentValue)}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Average</p>
            <p className="text-lg font-semibold">
              {formatCurrency(averageValue)}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Peak</p>
            <p className="text-lg font-semibold">{formatCurrency(maxValue)}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
