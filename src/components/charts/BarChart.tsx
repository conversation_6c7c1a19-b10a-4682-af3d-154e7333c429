import React from "react";
import {
  <PERSON><PERSON><PERSON> as Re<PERSON><PERSON>Bar<PERSON>hart,
  Bar,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
} from "recharts";
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface BarChartData {
  period: string;
  revenue: number;
  expenses: number;
}

interface BarChartProps {
  title: string;
  data: BarChartData[];
  currency?: string;
  height?: number;
  description?: string;
}

const BarChart: React.FC<BarChartProps> = ({
  title,
  data,
  currency = "USD",
  description,
}) => {
  // Chart configuration for shadcn
  const chartConfig = {
    revenue: {
      label: "Revenue",
      color: "#3B82F6", // Blue
    },
    expenses: {
      label: "Expenses",
      color: "#EF4444", // Red for expenses
    },
  } satisfies ChartConfig;

  // Format currency for tooltips
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  if (data.length === 0) {
    return (
      <Card className="h-[520px] flex flex-col bg-white dark:bg-gray-800 border-gray-100 dark:border-gray-700">
        <CardHeader className="flex-shrink-0">
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent className="flex-1 flex flex-col">
          <div className="flex items-center justify-center flex-1">
            <div className="text-center">
              <div className="text-muted-foreground mb-2">
                <svg
                  className="mx-auto h-12 w-12"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
              <p className="text-muted-foreground font-medium">
                No data available
              </p>
              <p className="text-muted-foreground text-sm">
                Revenue and expense data will appear here
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate summary statistics
  const totalRevenue = data.reduce((sum, d) => sum + d.revenue, 0);
  const totalExpenses = data.reduce((sum, d) => sum + d.expenses, 0);
  const netIncome = totalRevenue - totalExpenses;

  return (
    <Card className="h-[520px] flex flex-col bg-white dark:bg-gray-800 border-gray-100 dark:border-gray-700">
      <CardHeader className="flex-shrink-0">
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="flex-1 flex flex-col">
        <ChartContainer
          config={chartConfig}
          className="w-full h-64 flex-shrink-0"
        >
          <RechartsBarChart
            data={data}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 5,
            }}
            barCategoryGap="20%"
          >
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="period"
              tick={{ fontSize: 12 }}
              className="fill-muted-foreground"
              tickLine={{ stroke: 'hsl(var(--muted))' }}
              axisLine={{ stroke: 'hsl(var(--muted))' }}
            />
            <YAxis
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => formatCurrency(value)}
              className="fill-muted-foreground"
              tickLine={{ stroke: 'hsl(var(--muted))' }}
              axisLine={{ stroke: 'hsl(var(--muted))' }}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  formatter={(value, name) => [
                    formatCurrency(value as number),
                    name,
                  ]}
                />
              }
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Bar
              dataKey="revenue"
              name="Revenue"
              fill="#3B82F6"
              radius={[2, 2, 0, 0]}
            />
            <Bar
              dataKey="expenses"
              name="Expenses"
              fill="#EF4444"
              radius={[2, 2, 0, 0]}
            />
          </RechartsBarChart>
        </ChartContainer>

        {/* Summary Statistics */}
        <div className="mt-4 grid grid-cols-1 sm:grid-cols-3 gap-4 pt-4 border-t flex-1">
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Total Revenue</p>
            <p className="text-lg font-semibold text-green-600 dark:text-green-400">
              {formatCurrency(totalRevenue)}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Total Expenses</p>
            <p className="text-lg font-semibold text-red-600 dark:text-red-400">
              {formatCurrency(totalExpenses)}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Net Income</p>
            <p
              className={`text-lg font-semibold ${
                netIncome >= 0 ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
              }`}
            >
              {formatCurrency(netIncome)}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BarChart;
