import { useAuth } from "../../contexts/AuthContext";
import { useTheme } from "../../contexts/ThemeContext";
import CompanySelector from "../ui/CompanySelector";
import {
  BellIcon,
  Cog6ToothIcon,
  UserCircleIcon,
  ChevronDownIcon,
  BuildingOffice2Icon,
  SunIcon,
  MoonIcon,
} from "@heroicons/react/24/outline";
import { Fragment } from "react";
import { Menu, Transition } from "@headlessui/react";

export default function Header() {
  const { user, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();

  const userInitials =
    user?.firstName && user?.lastName
      ? `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase()
      : "U";

  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 relative z-10 transition-colors duration-300">
      <div className="mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left Section - Company */}
          <div className="flex items-center space-x-8">
            {/* Company Selector */}
            <div className="hidden md:block">
              <div className="flex items-center space-x-3">
                <span className="text-xs font-semibold text-gray-600 dark:text-gray-400 dark:text-gray-300 uppercase tracking-wider">
                  Company
                </span>
                <div className="w-px h-5 bg-gray-300 dark:bg-gray-600"></div>
                <div>
                  <CompanySelector />
                </div>
              </div>
            </div>
          </div>

          {/* Right Section - Actions & User */}
          <div className="flex items-center space-x-3">
            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className="p-2.5 text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-all duration-200 cursor-pointer group"
              title={theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}
            >
              {theme === 'light' ? (
                <MoonIcon className="w-5 h-5 group-hover:scale-110 transition-transform duration-200" />
              ) : (
                <SunIcon className="w-5 h-5 group-hover:scale-110 transition-transform duration-200" />
              )}
            </button>

            {/* Notifications */}
            <button className="relative p-2.5 text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-all duration-200 cursor-pointer group">
              <BellIcon className="w-5 h-5 group-hover:scale-110 transition-transform duration-200" />
              <span className="absolute top-1.5 right-1.5 w-2 h-2 bg-red-500 rounded-full ring-2 ring-white dark:ring-gray-800"></span>
            </button>

            {/* Settings */}
            <button className="p-2.5 text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-all duration-200 cursor-pointer group">
              <Cog6ToothIcon className="w-5 h-5 group-hover:scale-110 transition-transform duration-200" />
            </button>

            {/* User Menu */}
            <Menu as="div" className="relative ml-3">
              <Menu.Button className="flex items-center space-x-3 p-2 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 group cursor-pointer border border-transparent hover:border-gray-200 dark:hover:border-gray-600">
                <div className="flex items-center space-x-3">
                  <div className="w-9 h-9 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg ring-2 ring-white dark:ring-gray-800">
                    <span className="text-sm font-bold text-white">
                      {userInitials}
                    </span>
                  </div>
                  <div className="hidden lg:block text-left">
                    <p className="text-sm font-semibold text-gray-900 dark:text-white">
                      {user?.firstName} {user?.lastName}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{user?.email}</p>
                  </div>
                </div>
                <ChevronDownIcon className="w-4 h-4 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" />
              </Menu.Button>

              <Transition
                as={Fragment}
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <Menu.Items className="absolute right-0 mt-2 w-64 origin-top-right bg-white dark:bg-gray-800 rounded-xl shadow-xl ring-1 ring-black ring-opacity-5 dark:ring-gray-700 focus:outline-none border border-gray-100 dark:border-gray-700">
                  <div className="p-2">
                    <div className="px-3 py-2 border-b border-gray-100 dark:border-gray-700 mb-2">
                      <p className="text-sm font-semibold text-gray-900 dark:text-white">
                        {user?.firstName} {user?.lastName}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{user?.email}</p>
                    </div>

                    <Menu.Item>
                      {({ active }) => (
                        <button
                          className={`${
                            active ? "bg-gray-50 dark:bg-gray-700" : ""
                          } group flex w-full items-center rounded-lg px-3 py-2 text-sm text-gray-700 dark:text-gray-300 transition-colors cursor-pointer`}
                        >
                          <UserCircleIcon className="w-4 h-4 mr-3 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                          Profile Settings
                        </button>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          className={`${
                            active ? "bg-gray-50 dark:bg-gray-700" : ""
                          } group flex w-full items-center rounded-lg px-3 py-2 text-sm text-gray-700 dark:text-gray-300 transition-colors cursor-pointer`}
                        >
                          <Cog6ToothIcon className="w-4 h-4 mr-3 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                          Account Settings
                        </button>
                      )}
                    </Menu.Item>
                    <div className="my-2 h-px bg-gray-100 dark:bg-gray-800"></div>
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          onClick={logout}
                          className={`${
                            active ? "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400" : "text-gray-700 dark:text-gray-300"
                          } group flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors cursor-pointer`}
                        >
                          <svg
                            className="w-4 h-4 mr-3 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                            />
                          </svg>
                          Sign Out
                        </button>
                      )}
                    </Menu.Item>
                  </div>
                </Menu.Items>
              </Transition>
            </Menu>
          </div>
        </div>
      </div>

      {/* Mobile Company Selector */}
      <div className="md:hidden px-4 pb-3 border-t border-gray-200 dark:border-gray-700 dark:border-gray-600">
        <div className="flex items-center space-x-3 pt-3">
          <span className="text-xs font-semibold text-gray-600 dark:text-gray-400 dark:text-gray-300 uppercase tracking-wider">
            Company
          </span>
          <div className="w-px h-4 bg-gray-300 dark:bg-gray-600"></div>
          <div className="flex-1">
            <CompanySelector />
          </div>
        </div>
      </div>
    </header>
  );
}
