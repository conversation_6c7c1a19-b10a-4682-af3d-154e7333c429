import { Outlet } from "react-router-dom";
import Sidebar from "./Sidebar";
import Header from "./Header";
import NotificationContainer from "../notifications/NotificationContainer";

export default function Layout() {
  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-800 dark:bg-gray-900 transition-colors duration-300">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-gray-800 dark:bg-gray-900 p-6 transition-colors duration-300">
          <Outlet />
        </main>
      </div>
      <NotificationContainer />
    </div>
  );
}
