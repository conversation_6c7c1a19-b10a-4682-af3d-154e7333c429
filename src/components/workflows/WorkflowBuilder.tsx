import { useState, useEffect } from 'react';
import {
  PlusIcon,
  TrashIcon,
  ArrowRightIcon,
  CogIcon,
  UserGroupIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';
import { useNotification } from '../../contexts/NotificationContext';

interface WorkflowStep {
  id: string;
  type: 'APPROVAL' | 'NOTIFICATION' | 'CONDITION' | 'ACTION';
  name: string;
  config: any;
  position: { x: number; y: number };
}

interface WorkflowConnection {
  from: string;
  to: string;
  condition?: string;
}

interface WorkflowBuilderProps {
  workflow?: any;
  isOpen: boolean;
  onClose: () => void;
  onSave: (workflow: any) => void;
}

export default function WorkflowBuilder({
  workflow,
  isOpen,
  onClose,
  onSave,
}: WorkflowBuilderProps) {
  const [workflowData, setWorkflowData] = useState({
    name: '',
    description: '',
    trigger: {
      type: 'EVENT',
      config: { event: 'transaction.created' },
    },
    isActive: true,
  });

  const [steps, setSteps] = useState<WorkflowStep[]>([]);
  const [connections, setConnections] = useState<WorkflowConnection[]>([]);
  const [selectedStep, setSelectedStep] = useState<WorkflowStep | null>(null);
  const [showStepConfig, setShowStepConfig] = useState(false);
  const [saving, setSaving] = useState(false);

  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();

  useEffect(() => {
    if (isOpen) {
      if (workflow) {
        populateWorkflow();
      } else {
        resetWorkflow();
      }
    }
  }, [isOpen, workflow]);

  const populateWorkflow = () => {
    if (!workflow) return;

    setWorkflowData({
      name: workflow.name || '',
      description: workflow.description || '',
      trigger: workflow.trigger || { type: 'EVENT', config: { event: 'transaction.created' } },
      isActive: workflow.isActive ?? true,
    });

    // Convert workflow actions to visual steps
    const workflowSteps: WorkflowStep[] = [];
    if (workflow.actions) {
      workflow.actions.forEach((action: any, index: number) => {
        workflowSteps.push({
          id: `step-${index}`,
          type: action.type === 'APPROVAL_REQUEST' ? 'APPROVAL' : 'ACTION',
          name: action.type,
          config: action.config,
          position: { x: 100 + index * 200, y: 100 },
        });
      });
    }

    setSteps(workflowSteps);
  };

  const resetWorkflow = () => {
    setWorkflowData({
      name: '',
      description: '',
      trigger: {
        type: 'EVENT',
        config: { event: 'transaction.created' },
      },
      isActive: true,
    });
    setSteps([]);
    setConnections([]);
    setSelectedStep(null);
  };

  const addStep = (type: WorkflowStep['type']) => {
    const newStep: WorkflowStep = {
      id: `step-${Date.now()}`,
      type,
      name: getStepTypeName(type),
      config: getDefaultStepConfig(type),
      position: { x: 100 + steps.length * 200, y: 100 },
    };

    setSteps(prev => [...prev, newStep]);
  };

  const getStepTypeName = (type: WorkflowStep['type']): string => {
    switch (type) {
      case 'APPROVAL':
        return 'Approval Required';
      case 'NOTIFICATION':
        return 'Send Notification';
      case 'CONDITION':
        return 'Check Condition';
      case 'ACTION':
        return 'Perform Action';
      default:
        return 'Unknown Step';
    }
  };

  const getDefaultStepConfig = (type: WorkflowStep['type']): any => {
    switch (type) {
      case 'APPROVAL':
        return {
          approvers: [],
          requiredApprovals: 1,
          title: 'Approval Required',
          description: 'Please review and approve this request',
        };
      case 'NOTIFICATION':
        return {
          to: [],
          subject: 'Workflow Notification',
          message: 'A workflow action has been triggered',
        };
      case 'CONDITION':
        return {
          field: 'amount',
          operator: 'greater_than',
          value: 1000,
        };
      case 'ACTION':
        return {
          type: 'UPDATE_RECORD',
          table: 'transactions',
          updates: {},
        };
      default:
        return {};
    }
  };

  const removeStep = (stepId: string) => {
    setSteps(prev => prev.filter(step => step.id !== stepId));
    setConnections(prev => prev.filter(conn => conn.from !== stepId && conn.to !== stepId));
  };

  const updateStep = (stepId: string, updates: Partial<WorkflowStep>) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, ...updates } : step
    ));
  };

  const handleStepClick = (step: WorkflowStep) => {
    setSelectedStep(step);
    setShowStepConfig(true);
  };

  const handleStepConfigSave = (config: any) => {
    if (selectedStep) {
      updateStep(selectedStep.id, { config });
      setShowStepConfig(false);
      setSelectedStep(null);
    }
  };

  const handleSave = async () => {
    if (!currentCompany) return;

    if (!workflowData.name.trim()) {
      showNotification({
        type: 'error',
        title: 'Workflow name is required',
        message: 'Please enter a workflow name',
      });
      return;
    }

    if (steps.length === 0) {
      showNotification({
        type: 'error',
        title: 'At least one step is required',
        message: 'Please add at least one workflow step',
      });
      return;
    }

    setSaving(true);
    try {
      // Convert visual workflow to API format
      const actions = steps.map(step => ({
        type: step.type === 'APPROVAL' ? 'APPROVAL_REQUEST' : step.type,
        config: step.config,
      }));

      const workflowPayload = {
        ...workflowData,
        actions,
        conditions: [], // Add conditions if needed
      };

      // Here you would call the workflow service to save
      // await workflowService.createWorkflow(currentCompany.id, workflowPayload);

      showNotification({
        type: 'success',
        title: 'Workflow saved',
        message: 'Workflow has been saved successfully',
      });

      onSave(workflowPayload);
      onClose();
    } catch (error) {
      console.error('Failed to save workflow:', error);
      showNotification({
        type: 'error',
        title: 'Failed to save workflow',
        message: 'Could not save the workflow',
      });
    } finally {
      setSaving(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-7xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {workflow ? 'Edit Workflow' : 'Create Workflow'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XCircleIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-140px)]">
          {/* Sidebar */}
          <div className="w-80 border-r border-gray-200 dark:border-gray-700 p-6 overflow-y-auto">
            <div className="space-y-6">
              {/* Basic Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Workflow Details
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Name *
                    </label>
                    <input
                      type="text"
                      value={workflowData.name}
                      onChange={(e) => setWorkflowData(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Enter workflow name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Description
                    </label>
                    <textarea
                      value={workflowData.description}
                      onChange={(e) => setWorkflowData(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Enter workflow description"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Trigger Event
                    </label>
                    <select
                      value={workflowData.trigger.config.event}
                      onChange={(e) => setWorkflowData(prev => ({
                        ...prev,
                        trigger: {
                          ...prev.trigger,
                          config: { ...prev.trigger.config, event: e.target.value }
                        }
                      }))}
                      className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="transaction.created">Transaction Created</option>
                      <option value="transaction.submitted">Transaction Submitted</option>
                      <option value="transaction.approved">Transaction Approved</option>
                      <option value="invoice.created">Invoice Created</option>
                      <option value="payment.received">Payment Received</option>
                    </select>
                  </div>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={workflowData.isActive}
                      onChange={(e) => setWorkflowData(prev => ({ ...prev, isActive: e.target.checked }))}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                      Active
                    </span>
                  </label>
                </div>
              </div>

              {/* Step Types */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Add Steps
                </h3>
                <div className="space-y-2">
                  {[
                    { type: 'APPROVAL' as const, icon: UserGroupIcon, label: 'Approval', color: 'blue' },
                    { type: 'NOTIFICATION' as const, icon: ClockIcon, label: 'Notification', color: 'green' },
                    { type: 'CONDITION' as const, icon: CogIcon, label: 'Condition', color: 'yellow' },
                    { type: 'ACTION' as const, icon: CheckCircleIcon, label: 'Action', color: 'purple' },
                  ].map(({ type, icon: Icon, label, color }) => (
                    <button
                      key={type}
                      onClick={() => addStep(type)}
                      className={`w-full flex items-center p-3 border-2 border-dashed border-${color}-300 dark:border-${color}-600 rounded-lg hover:border-${color}-500 dark:hover:border-${color}-400 transition-colors`}
                    >
                      <Icon className={`h-5 w-5 text-${color}-500 mr-3`} />
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        Add {label}
                      </span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Canvas */}
          <div className="flex-1 p-6 bg-gray-50 dark:bg-gray-900 relative overflow-auto">
            <div className="min-h-full min-w-full relative">
              {/* Workflow Steps */}
              {steps.map((step) => (
                <div
                  key={step.id}
                  className="absolute bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-700 rounded-lg p-4 cursor-pointer hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
                  style={{
                    left: step.position.x,
                    top: step.position.y,
                    width: 180,
                  }}
                  onClick={() => handleStepClick(step)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      {step.type === 'APPROVAL' && <UserGroupIcon className="h-5 w-5 text-blue-500 mr-2" />}
                      {step.type === 'NOTIFICATION' && <ClockIcon className="h-5 w-5 text-green-500 mr-2" />}
                      {step.type === 'CONDITION' && <CogIcon className="h-5 w-5 text-yellow-500 mr-2" />}
                      {step.type === 'ACTION' && <CheckCircleIcon className="h-5 w-5 text-purple-500 mr-2" />}
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {step.type}
                      </span>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeStep(step.id);
                      }}
                      className="text-red-400 hover:text-red-600"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {step.name}
                  </div>
                </div>
              ))}

              {/* Connections */}
              {connections.map((connection, index) => {
                const fromStep = steps.find(s => s.id === connection.from);
                const toStep = steps.find(s => s.id === connection.to);
                if (!fromStep || !toStep) return null;

                return (
                  <svg
                    key={index}
                    className="absolute pointer-events-none"
                    style={{
                      left: 0,
                      top: 0,
                      width: '100%',
                      height: '100%',
                    }}
                  >
                    <line
                      x1={fromStep.position.x + 180}
                      y1={fromStep.position.y + 40}
                      x2={toStep.position.x}
                      y2={toStep.position.y + 40}
                      stroke="#6B7280"
                      strokeWidth="2"
                      markerEnd="url(#arrowhead)"
                    />
                    <defs>
                      <marker
                        id="arrowhead"
                        markerWidth="10"
                        markerHeight="7"
                        refX="9"
                        refY="3.5"
                        orient="auto"
                      >
                        <polygon
                          points="0 0, 10 3.5, 0 7"
                          fill="#6B7280"
                        />
                      </marker>
                    </defs>
                  </svg>
                );
              })}

              {/* Empty State */}
              {steps.length === 0 && (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <CogIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      No workflow steps
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Add steps from the sidebar to build your workflow
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={saving}
            className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
          >
            {saving ? 'Saving...' : workflow ? 'Update Workflow' : 'Create Workflow'}
          </button>
        </div>
      </div>

      {/* Step Configuration Modal */}
      {showStepConfig && selectedStep && (
        <StepConfigModal
          step={selectedStep}
          onSave={handleStepConfigSave}
          onClose={() => setShowStepConfig(false)}
        />
      )}
    </div>
  );
}

// Step Configuration Modal Component (simplified)
function StepConfigModal({ step, onSave, onClose }: {
  step: WorkflowStep;
  onSave: (config: any) => void;
  onClose: () => void;
}) {
  const [config, setConfig] = useState(step.config);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md">
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Configure {step.type}
          </h3>
          
          {/* Step-specific configuration would go here */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Configuration
              </label>
              <textarea
                value={JSON.stringify(config, null, 2)}
                onChange={(e) => {
                  try {
                    setConfig(JSON.parse(e.target.value));
                  } catch {
                    // Invalid JSON, ignore
                  }
                }}
                rows={8}
                className="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              onClick={() => onSave(config)}
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
