import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import { accountService } from "../../services/accountService";
import type { Account, AccountType, AccountSubtype } from "../../types";

interface AccountFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: AccountFormData) => Promise<void>;
  account?: Account | null;
  companyId: string;
  parentAccounts?: Account[];
}

export interface AccountFormData {
  code: string;
  name: string;
  description: string;
  accountType: AccountType;
  accountSubtype: AccountSubtype | "";
  parentAccountId: string;
  openingBalance: number;
  currency: string;
}

const ACCOUNT_TYPES: { value: AccountType; label: string }[] = [
  { value: "ASSET", label: "Asset" },
  { value: "LIABILITY", label: "Liability" },
  { value: "EQUITY", label: "Equity" },
  { value: "REVENUE", label: "Revenue" },
  { value: "EXPENSE", label: "Expense" },
];

const CURRENCIES = [
  { value: "USD", label: "US Dollar (USD)" },
  { value: "EUR", label: "Euro (EUR)" },
  { value: "GBP", label: "British Pound (GBP)" },
  { value: "CAD", label: "Canadian Dollar (CAD)" },
  { value: "AUD", label: "Australian Dollar (AUD)" },
];

export default function AccountForm({
  isOpen,
  onClose,
  onSubmit,
  account,
  companyId,
  parentAccounts = [],
}: AccountFormProps) {
  const [formData, setFormData] = useState<AccountFormData>({
    code: "",
    name: "",
    description: "",
    accountType: "ASSET",
    accountSubtype: "",
    parentAccountId: "",
    openingBalance: 0,
    currency: "USD",
  });

  const [validSubtypes, setValidSubtypes] = useState<AccountSubtype[]>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<
    Partial<Record<keyof AccountFormData, string>>
  >({});

  // Initialize form data when account changes
  useEffect(() => {
    if (account) {
      setFormData({
        code: account.code,
        name: account.name,
        description: account.description || "",
        accountType: account.accountType,
        accountSubtype: account.accountSubtype || "",
        parentAccountId: account.parentAccountId || "",
        openingBalance: account.openingBalance,
        currency: account.currency,
      });
    } else {
      setFormData({
        code: "",
        name: "",
        description: "",
        accountType: "ASSET",
        accountSubtype: "",
        parentAccountId: "",
        openingBalance: 0,
        currency: "USD",
      });
    }
  }, [account]);

  // Update valid subtypes when account type changes
  useEffect(() => {
    const subtypes = accountService.getValidSubtypes(formData.accountType);
    setValidSubtypes(subtypes);

    // Reset subtype if it's not valid for the new account type
    if (
      formData.accountSubtype &&
      !subtypes.includes(formData.accountSubtype as AccountSubtype)
    ) {
      setFormData((prev) => ({ ...prev, accountSubtype: "" }));
    }
  }, [formData.accountType, formData.accountSubtype]);

  // Generate account code when account type or subtype changes
  useEffect(() => {
    if (!account && formData.accountType) {
      generateAccountCode();
    }
  }, [formData.accountType, formData.accountSubtype, account]);

  const generateAccountCode = async () => {
    try {
      const code = await accountService.generateAccountCode(
        companyId,
        formData.accountType,
        formData.accountSubtype as AccountSubtype
      );
      setFormData((prev) => ({ ...prev, code }));
    } catch (error) {
      console.error("Failed to generate account code:", error);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof AccountFormData, string>> = {};

    // Validate code
    const codeValidation = accountService.validateAccountCode(formData.code);
    if (!codeValidation.isValid) {
      newErrors.code = codeValidation.error;
    }

    // Validate name
    if (!formData.name.trim()) {
      newErrors.name = "Account name is required";
    }

    // Validate account type
    if (!formData.accountType) {
      newErrors.accountType = "Account type is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error("Form submission error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (
    field: keyof AccountFormData,
    value: string | number
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  // Filter parent accounts to exclude the current account and its descendants
  const availableParentAccounts = parentAccounts.filter((parentAccount) => {
    if (account && parentAccount.id === account.id) {
      return false;
    }
    // TODO: Add logic to exclude descendants
    return parentAccount.accountType === formData.accountType;
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div
          className="fixed inset-0"
          style={{ backgroundColor: 'rgba(0,0,0,0.6)' }}
          onClick={onClose}
        />

        <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {account ? "Edit Account" : "Create Account"}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-pointer"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            {/* Account Code */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Account Code *
              </label>
              <input
                type="text"
                value={formData.code}
                onChange={(e) => handleInputChange("code", e.target.value)}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.code ? "border-red-300" : "border-gray-300 dark:border-gray-600"
                }`}
                placeholder="e.g., 1001"
              />
              {errors.code && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.code}</p>
              )}
            </div>

            {/* Account Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Account Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.name ? "border-red-300" : "border-gray-300 dark:border-gray-600"
                }`}
                placeholder="e.g., Cash"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name}</p>
              )}
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Optional description"
              />
            </div>

            {/* Account Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Account Type *
              </label>
              <select
                value={formData.accountType}
                onChange={(e) =>
                  handleInputChange("accountType", e.target.value)
                }
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.accountType ? "border-red-300" : "border-gray-300 dark:border-gray-600"
                }`}
              >
                {ACCOUNT_TYPES.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
              {errors.accountType && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.accountType}
                </p>
              )}
            </div>

            {/* Account Subtype */}
            {validSubtypes.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Account Subtype
                </label>
                <select
                  value={formData.accountSubtype}
                  onChange={(e) =>
                    handleInputChange("accountSubtype", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Select subtype (optional)</option>
                  {validSubtypes.map((subtype) => (
                    <option key={subtype} value={subtype}>
                      {accountService.getAccountSubtypeDisplayName(subtype)}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Parent Account */}
            {availableParentAccounts.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Parent Account
                </label>
                <select
                  value={formData.parentAccountId}
                  onChange={(e) =>
                    handleInputChange("parentAccountId", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">No parent account</option>
                  {availableParentAccounts.map((parentAccount) => (
                    <option key={parentAccount.id} value={parentAccount.id}>
                      {parentAccount.code} - {parentAccount.name}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Opening Balance */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Opening Balance
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.openingBalance}
                onChange={(e) =>
                  handleInputChange(
                    "openingBalance",
                    parseFloat(e.target.value) || 0
                  )
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                placeholder="0.00"
              />
            </div>

            {/* Currency */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Currency
              </label>
              <select
                value={formData.currency}
                onChange={(e) => handleInputChange("currency", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              >
                {CURRENCIES.map((currency) => (
                  <option key={currency.value} value={currency.value}>
                    {currency.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 cursor-pointer"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
              >
                {loading
                  ? "Saving..."
                  : account
                  ? "Update Account"
                  : "Create Account"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
