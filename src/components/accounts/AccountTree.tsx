import { useState } from "react";
import { ChevronRight, ChevronDown, Edit, Trash2, Plus } from "lucide-react";
import { accountService } from "../../services/accountService";
import type { Account } from "../../types";

interface AccountTreeProps {
  accounts: Account[];
  onEditAccount: (account: Account) => void;
  onDeleteAccount: (account: Account) => void;
  onCreateSubAccount: (parentAccount: Account) => void;
  loading?: boolean;
}

interface AccountNodeProps {
  account: Account;
  level: number;
  onEditAccount: (account: Account) => void;
  onDeleteAccount: (account: Account) => void;
  onCreateSubAccount: (parentAccount: Account) => void;
}

function AccountNode({
  account,
  level,
  onEditAccount,
  onDeleteAccount,
  onCreateSubAccount,
}: AccountNodeProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const hasChildren = account.children && account.children.length > 0;

  const formatBalance = (balance: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: account.currency || "USD",
      minimumFractionDigits: 2,
    }).format(balance);
  };

  const getBalanceColor = (balance: number, accountType: string) => {
    if (balance === 0) return "text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500";

    // For assets and expenses, positive is good (green)
    // For liabilities, equity, and revenue, positive is normal (blue)
    if (["ASSET", "EXPENSE"].includes(accountType)) {
      return balance > 0 ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400";
    } else {
      return balance > 0 ? "text-blue-600 dark:text-blue-400" : "text-red-600 dark:text-red-400";
    }
  };

  return (
    <div className="select-none">
      <div
        className={`flex items-center py-2 px-3 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 rounded-md group ${
          level > 0 ? "ml-6" : ""
        }`}
        style={{ paddingLeft: `${level * 24 + 12}px` }}
      >
        {/* Expand/Collapse Button */}
        <div className="w-6 h-6 flex items-center justify-center">
          {hasChildren ? (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 p-1"
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </button>
          ) : (
            <div className="w-4 h-4" />
          )}
        </div>

        {/* Account Code */}
        <div className="w-20 text-sm font-mono text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 flex-shrink-0">
          {account.code}
        </div>

        {/* Account Name */}
        <div className="flex-1 min-w-0 px-3">
          <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
            {account.name}
          </div>
          {account.description && (
            <div className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">
              {account.description}
            </div>
          )}
        </div>

        {/* Account Type */}
        <div className="w-24 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 flex-shrink-0">
          {accountService.getAccountTypeDisplayName(account.accountType)}
        </div>

        {/* Current Balance */}
        <div
          className={`w-32 text-sm text-right flex-shrink-0 ${getBalanceColor(
            account.openingBalance || 0,
            account.accountType
          )}`}
        >
          {formatBalance(account.openingBalance || 0)}
        </div>

        {/* Actions */}
        <div className="w-24 flex items-center justify-end space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={() => onCreateSubAccount(account)}
            className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:bg-blue-900/20 rounded"
            title="Add sub-account"
          >
            <Plus className="h-4 w-4" />
          </button>
          <button
            onClick={() => onEditAccount(account)}
            className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:bg-blue-900/20 rounded"
            title="Edit account"
          >
            <Edit className="h-4 w-4" />
          </button>
          {!account.isSystem && (
            <button
              onClick={() => onDeleteAccount(account)}
              className="p-1 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-red-600 dark:text-red-400 hover:bg-red-50 dark:bg-red-900/20 rounded"
              title="Delete account"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div>
          {account.children!.map((child) => (
            <AccountNode
              key={child.id}
              account={child}
              level={level + 1}
              onEditAccount={onEditAccount}
              onDeleteAccount={onDeleteAccount}
              onCreateSubAccount={onCreateSubAccount}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export default function AccountTree({
  accounts,
  onEditAccount,
  onDeleteAccount,
  onCreateSubAccount,
  loading = false,
}: AccountTreeProps) {
  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="p-6 bg-white dark:bg-gray-800">
          <div className="animate-pulse space-y-4 bg-white dark:bg-gray-800">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="w-6 h-6 bg-gray-200 dark:bg-gray-600 rounded"></div>
                <div className="w-20 h-4 bg-gray-200 dark:bg-gray-600 rounded"></div>
                <div className="flex-1 h-4 bg-gray-200 dark:bg-gray-600 rounded"></div>
                <div className="w-24 h-4 bg-gray-200 dark:bg-gray-600 rounded"></div>
                <div className="w-32 h-4 bg-gray-200 dark:bg-gray-600 rounded"></div>
                <div className="w-24 h-4 bg-gray-200 dark:bg-gray-600 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (accounts.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="text-center bg-white dark:bg-gray-800 py-12">
            <svg
              className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              No accounts found
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
              Get started by creating your first account.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gray-50 dark:bg-gray-700 px-6 py-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
          <div className="w-6"></div>
          <div className="w-20">Code</div>
          <div className="flex-1 px-3">Account Name</div>
          <div className="w-24">Type</div>
          <div className="w-32 text-right">Balance</div>
          <div className="w-24 text-center">Actions</div>
        </div>
      </div>

      {/* Account Tree */}
      <div className="divide-y divide-gray-100 dark:divide-gray-700">
        {accounts.map((account) => (
          <AccountNode
            key={account.id}
            account={account}
            level={0}
            onEditAccount={onEditAccount}
            onDeleteAccount={onDeleteAccount}
            onCreateSubAccount={onCreateSubAccount}
          />
        ))}
      </div>
    </div>
  );
}
