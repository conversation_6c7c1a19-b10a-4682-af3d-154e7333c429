import { useState } from "react";
import { PaperAirplaneIcon, UserPlusIcon } from "@heroicons/react/24/outline";
import type { Role } from "../../types";
import { userService } from "../../services/userService";
import { useNotification } from "../../contexts/NotificationContext";
import { useCompany } from "../../contexts/CompanyContext";

interface UserInvitationProps {
  roles: Role[];
  onInviteSent: () => void;
}

export default function UserInvitation({ roles, onInviteSent }: UserInvitationProps) {
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  const [invitations, setInvitations] = useState([
    { email: "", roleId: "", firstName: "", lastName: "" }
  ]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<number, Record<string, string>>>({});

  const addInvitation = () => {
    setInvitations(prev => [
      ...prev,
      { email: "", roleId: "", firstName: "", lastName: "" }
    ]);
  };

  const removeInvitation = (index: number) => {
    if (invitations.length > 1) {
      setInvitations(prev => prev.filter((_, i) => i !== index));
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[index];
        return newErrors;
      });
    }
  };

  const updateInvitation = (index: number, field: string, value: string) => {
    setInvitations(prev => prev.map((inv, i) => 
      i === index ? { ...inv, [field]: value } : inv
    ));
    
    // Clear error for this field
    if (errors[index]?.[field]) {
      setErrors(prev => ({
        ...prev,
        [index]: {
          ...prev[index],
          [field]: ""
        }
      }));
    }
  };

  const validateInvitations = () => {
    const newErrors: Record<number, Record<string, string>> = {};
    let hasErrors = false;

    invitations.forEach((invitation, index) => {
      const invErrors: Record<string, string> = {};

      if (!invitation.email.trim()) {
        invErrors.email = "Email is required";
        hasErrors = true;
      } else if (!/\S+@\S+\.\S+/.test(invitation.email)) {
        invErrors.email = "Email is invalid";
        hasErrors = true;
      }

      if (!invitation.firstName.trim()) {
        invErrors.firstName = "First name is required";
        hasErrors = true;
      }

      if (!invitation.lastName.trim()) {
        invErrors.lastName = "Last name is required";
        hasErrors = true;
      }

      if (!invitation.roleId) {
        invErrors.roleId = "Role is required";
        hasErrors = true;
      }

      if (Object.keys(invErrors).length > 0) {
        newErrors[index] = invErrors;
      }
    });

    // Check for duplicate emails
    const emails = invitations.map(inv => inv.email.toLowerCase().trim()).filter(Boolean);
    const duplicates = emails.filter((email, index) => emails.indexOf(email) !== index);
    
    if (duplicates.length > 0) {
      invitations.forEach((invitation, index) => {
        if (duplicates.includes(invitation.email.toLowerCase().trim())) {
          if (!newErrors[index]) newErrors[index] = {};
          newErrors[index].email = "Duplicate email address";
          hasErrors = true;
        }
      });
    }

    setErrors(newErrors);
    return !hasErrors;
  };

  const handleSendInvitations = async () => {
    if (!validateInvitations()) {
      return;
    }

    if (!currentCompany) {
      showNotification({
        type: "error",
        title: "No Company Selected",
        message: "Please select a company before sending invitations",
      });
      return;
    }

    setLoading(true);
    try {
      const validInvitations = invitations.filter(inv => 
        inv.email.trim() && inv.firstName.trim() && inv.lastName.trim() && inv.roleId
      );

      await Promise.all(
        validInvitations.map(invitation =>
          userService.inviteUser({
            email: invitation.email.trim(),
            firstName: invitation.firstName.trim(),
            lastName: invitation.lastName.trim(),
            roleId: invitation.roleId,
            companyId: currentCompany.id,
          })
        )
      );

      showNotification({
        type: "success",
        title: "Invitations Sent",
        message: `${validInvitations.length} invitation${validInvitations.length !== 1 ? 's' : ''} sent successfully`,
      }
      
      );

      // Reset form
      setInvitations([{ email: "", roleId: "", firstName: "", lastName: "" }]);
      setErrors({});
      onInviteSent();
    } catch (error: any) {
      console.error("Failed to send invitations:", error);
      showNotification(
{
          type: "error",
          title: "Failed to send invitations",
          message: error.response?.data?.error || "Failed to send invitations",
        }
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Invite Users</h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
          Send email invitations to new users to join {currentCompany?.name}
        </p>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="space-y-6">
            {invitations.map((invitation, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                    Invitation {index + 1}
                  </h4>
                  {invitations.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeInvitation(index)}
                      className="text-red-600 dark:text-red-400 hover:text-red-800 text-sm cursor-pointer"
                    >
                      Remove
                    </button>
                  )}
                </div>

                <div className="grid bg-white dark:bg-gray-800 grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      First Name *
                    </label>
                    <input
                      type="text"
                      value={invitation.firstName}
                      onChange={(e) => updateInvitation(index, "firstName", e.target.value)}
                      className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors[index]?.firstName ? "border-red-300" : "border-gray-300 dark:border-gray-600"
                      }`}
                      placeholder="John"
                    />
                    {errors[index]?.firstName && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors[index].firstName}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      value={invitation.lastName}
                      onChange={(e) => updateInvitation(index, "lastName", e.target.value)}
                      className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors[index]?.lastName ? "border-red-300" : "border-gray-300 dark:border-gray-600"
                      }`}
                      placeholder="Doe"
                    />
                    {errors[index]?.lastName && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors[index].lastName}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      value={invitation.email}
                      onChange={(e) => updateInvitation(index, "email", e.target.value)}
                      className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors[index]?.email ? "border-red-300" : "border-gray-300 dark:border-gray-600"
                      }`}
                      placeholder="<EMAIL>"
                    />
                    {errors[index]?.email && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors[index].email}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Role *
                    </label>
                    <select
                      value={invitation.roleId}
                      onChange={(e) => updateInvitation(index, "roleId", e.target.value)}
                      className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer ${
                        errors[index]?.roleId ? "border-red-300" : "border-gray-300 dark:border-gray-600"
                      }`}
                    >
                      <option value="">Select a role</option>
                      {roles.filter(role => role.isActive).map((role) => (
                        <option key={role.id} value={role.id}>
                          {role.name}
                        </option>
                      ))}
                    </select>
                    {errors[index]?.roleId && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors[index].roleId}</p>
                    )}
                  </div>
                </div>
              </div>
            ))}

            <div className="flex items-center justify-between">
              <button
                type="button"
                onClick={addInvitation}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
              >
                <UserPlusIcon className="h-4 w-4 mr-2" />
                Add Another Invitation
              </button>

              <button
                type="button"
                onClick={handleSendInvitations}
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 cursor-pointer"
              >
                <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                {loading ? "Sending..." : `Send ${invitations.length} Invitation${invitations.length !== 1 ? 's' : ''}`}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Information Box */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 rounded-md p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              How invitations work
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Invited users will receive an email with a link to set up their account</li>
                <li>They'll be able to set their own password during the setup process</li>
                <li>Once they complete setup, they'll have access based on their assigned role</li>
                <li>Invitations expire after 7 days and can be resent if needed</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
