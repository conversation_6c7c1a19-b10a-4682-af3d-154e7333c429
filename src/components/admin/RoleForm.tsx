import { useState, useEffect } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import type { Role } from "../../types";

interface PermissionCategory {
  category: string;
  permissions: Array<{
    key: string;
    name: string;
    description: string;
  }>;
}

interface RoleFormProps {
  role?: Role | null;
  availablePermissions: PermissionCategory[];
  onSave: (roleData: any) => void;
  onCancel: () => void;
}

export default function RoleForm({ role, availablePermissions, onSave, onCancel }: RoleFormProps) {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    permissions: [] as string[],
    isActive: true,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (role) {
      setFormData({
        name: role.name,
        description: role.description || "",
        permissions: [...role.permissions],
        isActive: role.isActive,
      });
    }
    // Expand all categories by default
    setExpandedCategories(new Set(availablePermissions.map(cat => cat.category)));
  }, [role, availablePermissions]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Role name is required";
    }

    if (formData.permissions.length === 0) {
      newErrors.permissions = "At least one permission must be selected";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const roleData = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        permissions: formData.permissions,
        isActive: formData.isActive,
      };

      await onSave(roleData);
    } catch (error) {
      console.error("Failed to save role:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const handlePermissionToggle = (permissionKey: string) => {
    const newPermissions = formData.permissions.includes(permissionKey)
      ? formData.permissions.filter(p => p !== permissionKey)
      : [...formData.permissions, permissionKey];
    
    handleInputChange("permissions", newPermissions);
  };

  const handleCategoryToggle = (category: string) => {
    const categoryPermissions = availablePermissions
      .find(cat => cat.category === category)
      ?.permissions.map(p => p.key) || [];
    
    const allSelected = categoryPermissions.every(p => formData.permissions.includes(p));
    
    if (allSelected) {
      // Deselect all in category
      const newPermissions = formData.permissions.filter(p => !categoryPermissions.includes(p));
      handleInputChange("permissions", newPermissions);
    } else {
      // Select all in category
      const newPermissions = [...new Set([...formData.permissions, ...categoryPermissions])];
      handleInputChange("permissions", newPermissions);
    }
  };

  const toggleCategoryExpansion = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  const isCategoryFullySelected = (category: string) => {
    const categoryPermissions = availablePermissions
      .find(cat => cat.category === category)
      ?.permissions.map(p => p.key) || [];
    return categoryPermissions.length > 0 && categoryPermissions.every(p => formData.permissions.includes(p));
  };

  const isCategoryPartiallySelected = (category: string) => {
    const categoryPermissions = availablePermissions
      .find(cat => cat.category === category)
      ?.permissions.map(p => p.key) || [];
    return categoryPermissions.some(p => formData.permissions.includes(p)) && 
           !categoryPermissions.every(p => formData.permissions.includes(p));
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {role ? "Edit Role" : "Create New Role"}
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-pointer"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid bg-white dark:bg-gray-800 grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Role Name *
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.name ? "border-red-300" : "border-gray-300 dark:border-gray-600"
                }`}
                placeholder="e.g., Accountant, Manager, Viewer"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name}</p>
              )}
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Description
              </label>
              <input
                type="text"
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Brief description of this role"
              />
            </div>
          </div>

          {/* Active Status */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isActive"
              checked={formData.isActive}
              onChange={(e) => handleInputChange("isActive", e.target.checked)}
              className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded cursor-pointer"
            />
            <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900 dark:text-white cursor-pointer">
              Active role
            </label>
          </div>

          {/* Permissions */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Permissions *
            </label>
            {errors.permissions && (
              <p className="mb-3 text-sm text-red-600 dark:text-red-400">{errors.permissions}</p>
            )}
            
            <div className="border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded-md max-h-96 overflow-y-auto">
              {availablePermissions.map((category) => {
                const isExpanded = expandedCategories.has(category.category);
                const isFullySelected = isCategoryFullySelected(category.category);
                const isPartiallySelected = isCategoryPartiallySelected(category.category);
                
                return (
                  <div key={category.category} className="border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                    <div className="p-3 bg-gray-50 dark:bg-gray-800 flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={isFullySelected}
                          ref={(el) => {
                            if (el) el.indeterminate = isPartiallySelected && !isFullySelected;
                          }}
                          onChange={() => handleCategoryToggle(category.category)}
                          className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded cursor-pointer"
                        />
                        <button
                          type="button"
                          onClick={() => toggleCategoryExpansion(category.category)}
                          className="text-sm font-medium text-gray-900 dark:text-white hover:text-gray-700 dark:text-gray-300 cursor-pointer"
                        >
                          {category.category}
                        </button>
                      </div>
                      <button
                        type="button"
                        onClick={() => toggleCategoryExpansion(category.category)}
                        className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-pointer"
                      >
                        {isExpanded ? "−" : "+"}
                      </button>
                    </div>
                    
                    {isExpanded && (
                      <div className="p-3 space-y-2">
                        {category.permissions.map((permission) => (
                          <div key={permission.key} className="flex items-start space-x-3">
                            <input
                              type="checkbox"
                              id={permission.key}
                              checked={formData.permissions.includes(permission.key)}
                              onChange={() => handlePermissionToggle(permission.key)}
                              className="mt-1 h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded cursor-pointer"
                            />
                            <div className="flex-1">
                              <label
                                htmlFor={permission.key}
                                className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer"
                              >
                                {permission.name}
                              </label>
                              <p className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{permission.description}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800 cursor-pointer"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 cursor-pointer"
            >
              {loading ? "Saving..." : role ? "Update Role" : "Create Role"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
