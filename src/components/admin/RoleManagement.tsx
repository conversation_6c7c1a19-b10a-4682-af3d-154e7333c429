import { useState } from "react";
import {
  PlusIcon,
  TrashIcon,
  ShieldCheckIcon,
  ChevronRightIcon,
  ChevronDownIcon,
} from "@heroicons/react/24/outline";
import type { Role } from "../../types";
import { userService } from "../../services/userService";
import { useNotification } from "../../contexts/NotificationContext";
import { useConfirmDialog } from "../../hooks/useConfirmDialog";
import ConfirmDialog from "../ui/ConfirmDialog";
import RoleForm from "./RoleForm";
import { Edit } from "lucide-react";

interface RoleManagementProps {
  roles: Role[];
  onUpdate: () => void;
}

const AVAILABLE_PERMISSIONS = [
  // User Management
  {
    category: "User Management",
    permissions: [
      {
        key: "users:read",
        name: "View Users",
        description: "View user list and details",
      },
      {
        key: "users:create",
        name: "Create Users",
        description: "Create new user accounts",
      },
      {
        key: "users:update",
        name: "Update Users",
        description: "Edit user information",
      },
      {
        key: "users:delete",
        name: "Delete Users",
        description: "Delete user accounts",
      },
    ],
  },

  // Company Management
  {
    category: "Company Management",
    permissions: [
      {
        key: "companies:read",
        name: "View Companies",
        description: "View company information",
      },
      {
        key: "companies:create",
        name: "Create Companies",
        description: "Create new companies",
      },
      {
        key: "companies:update",
        name: "Update Companies",
        description: "Edit company settings",
      },
      {
        key: "companies:delete",
        name: "Delete Companies",
        description: "Delete companies",
      },
    ],
  },

  // Accounting
  {
    category: "Accounting",
    permissions: [
      {
        key: "accounts:read",
        name: "View Accounts",
        description: "View chart of accounts",
      },
      {
        key: "accounts:create",
        name: "Create Accounts",
        description: "Create new accounts",
      },
      {
        key: "accounts:update",
        name: "Update Accounts",
        description: "Edit account information",
      },
      {
        key: "accounts:delete",
        name: "Delete Accounts",
        description: "Delete accounts",
      },
      {
        key: "transactions:read",
        name: "View Transactions",
        description: "View transaction records",
      },
      {
        key: "transactions:create",
        name: "Create Transactions",
        description: "Create new transactions",
      },
      {
        key: "transactions:update",
        name: "Update Transactions",
        description: "Edit transactions",
      },
      {
        key: "transactions:delete",
        name: "Delete Transactions",
        description: "Delete transactions",
      },
      {
        key: "transactions:approve",
        name: "Approve Transactions",
        description: "Approve pending transactions",
      },
    ],
  },

  // Invoicing
  {
    category: "Invoicing",
    permissions: [
      {
        key: "invoices:read",
        name: "View Invoices",
        description: "View invoice records",
      },
      {
        key: "invoices:create",
        name: "Create Invoices",
        description: "Create new invoices",
      },
      {
        key: "invoices:update",
        name: "Update Invoices",
        description: "Edit invoice information",
      },
      {
        key: "invoices:delete",
        name: "Delete Invoices",
        description: "Delete invoices",
      },
      {
        key: "invoices:send",
        name: "Send Invoices",
        description: "Send invoices to customers",
      },
    ],
  },

  // Contacts
  {
    category: "Contacts",
    permissions: [
      {
        key: "contacts:read",
        name: "View Contacts",
        description: "View contact records",
      },
      {
        key: "contacts:create",
        name: "Create Contacts",
        description: "Create new contacts",
      },
      {
        key: "contacts:update",
        name: "Update Contacts",
        description: "Edit contact information",
      },
      {
        key: "contacts:delete",
        name: "Delete Contacts",
        description: "Delete contacts",
      },
    ],
  },

  // Reports
  {
    category: "Reports",
    permissions: [
      {
        key: "reports:read",
        name: "View Reports",
        description: "Access financial reports",
      },
      {
        key: "reports:export",
        name: "Export Reports",
        description: "Export reports to various formats",
      },
    ],
  },

  // Settings
  {
    category: "Settings",
    permissions: [
      {
        key: "settings:read",
        name: "View Settings",
        description: "View system settings",
      },
      {
        key: "settings:update",
        name: "Update Settings",
        description: "Modify system settings",
      },
    ],
  },

  // Audit
  {
    category: "Audit",
    permissions: [
      {
        key: "audit:read",
        name: "View Audit Logs",
        description: "Access audit trail and logs",
      },
    ],
  },
];

export default function RoleManagement({
  roles = [],
  onUpdate,
}: RoleManagementProps) {
  const { showNotification } = useNotification();
  const { confirmDialog, showConfirmDialog } = useConfirmDialog();
  const [showRoleForm, setShowRoleForm] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [expandedRoles, setExpandedRoles] = useState<Set<string>>(new Set());

  const handleCreateRole = () => {
    setEditingRole(null);
    setShowRoleForm(true);
  };

  const handleEditRole = (role: Role) => {
    setEditingRole(role);
    setShowRoleForm(true);
  };

  const handleDeleteRole = async (roleId: string) => {
    try {
      await userService.deleteRole(roleId);
      showNotification({
        type: "success",
        title: "Role Deleted",
        message: "Role deleted successfully",
      });
      onUpdate();
    } catch (error: any) {
      console.error("Failed to delete role:", error);
      showNotification({
        type: "error",
        title: "Failed to delete role",
        message: error.response?.data?.error || "Failed to delete role",
      });
    }
  };

  const handleDeleteClick = (role: Role) => {
    showConfirmDialog({
      title: "Delete Role",
      message: `Are you sure you want to delete the "${role.name}" role? This action cannot be undone.`,
      confirmLabel: "Delete",
      type: "danger",
      onConfirm: () => handleDeleteRole(role.id),
    });
  };

  const handleRoleSave = async (roleData: any) => {
    try {
      if (editingRole) {
        await userService.updateRole(editingRole.id, roleData);
        showNotification({
          type: "success",
          title: "Role Updated",
          message: "Role updated successfully",
        });
      } else {
        await userService.createRole(roleData);
        showNotification({
          type: "success",
          title: "Role Created",
          message: "Role created successfully",
        });
      }
      setShowRoleForm(false);
      setEditingRole(null);
      onUpdate();
    } catch (error: any) {
      console.error("Failed to save role:", error);
      showNotification({
        type: "error",
        title: "Failed to save role",
        message: error.response?.data?.error || "Failed to save role",
      });
    }
  };

  const toggleRoleExpansion = (roleId: string) => {
    const newExpanded = new Set(expandedRoles);
    if (newExpanded.has(roleId)) {
      newExpanded.delete(roleId);
    } else {
      newExpanded.add(roleId);
    }
    setExpandedRoles(newExpanded);
  };

  const getPermissionDetails = (permissionKey: string) => {
    for (const category of AVAILABLE_PERMISSIONS) {
      const permission = category.permissions.find(
        (p) => p.key === permissionKey
      );
      if (permission) {
        return permission;
      }
    }
    return { key: permissionKey, name: permissionKey, description: "" };
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Roles & Permissions
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Manage user roles and their associated permissions
          </p>
        </div>
        <button
          onClick={handleCreateRole}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Create Role
        </button>
      </div>

      {/* Roles List */}
      <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {!roles || roles.length === 0 ? (
            <li className="px-6 py-8 text-center">
              <ShieldCheckIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                No roles found
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                Get started by creating your first role.
              </p>
            </li>
          ) : (
            roles.map((role) => {
              const isExpanded = expandedRoles.has(role.id);
              return (
                <li key={role.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800">
                  <div className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => toggleRoleExpansion(role.id)}
                          className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-pointer"
                        >
                          {isExpanded ? (
                            <ChevronDownIcon className="h-5 w-5" />
                          ) : (
                            <ChevronRightIcon className="h-5 w-5" />
                          )}
                        </button>
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                            {role.name}
                          </h4>
                          {role.description && (
                            <p className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                              {role.description}
                            </p>
                          )}
                          <p className="text-xs text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">
                            {role.permissions.length} permission
                            {role.permissions.length !== 1 ? "s" : ""}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEditRole(role)}
                          className="p-2 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:text-blue-400 rounded-full hover:bg-blue-50 dark:bg-blue-900/20 cursor-pointer"
                          title="Edit role"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteClick(role)}
                          className="p-2 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-red-600 dark:text-red-400 rounded-full hover:bg-red-50 dark:bg-red-900/20 cursor-pointer"
                          title="Delete role"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>

                    {/* Expanded Permissions */}
                    {isExpanded && (
                      <div className="mt-4 ml-8 space-y-2">
                        <h5 className="text-xs font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wide">
                          Permissions
                        </h5>
                        <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                          {role.permissions.map((permission) => {
                            const details = getPermissionDetails(permission);
                            return (
                              <div
                                key={permission}
                                className="flex items-center space-x-2 text-xs"
                              >
                                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                                <span
                                  className="text-gray-700 dark:text-gray-300"
                                  title={details.description}
                                >
                                  {details.name}
                                </span>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                </li>
              );
            })
          )}
        </ul>
      </div>

      {/* Role Form Modal */}
      {showRoleForm && (
        <RoleForm
          role={editingRole}
          availablePermissions={AVAILABLE_PERMISSIONS}
          onSave={handleRoleSave}
          onCancel={() => {
            setShowRoleForm(false);
            setEditingRole(null);
          }}
        />
      )}

      <ConfirmDialog {...confirmDialog} />
    </div>
  );
}
