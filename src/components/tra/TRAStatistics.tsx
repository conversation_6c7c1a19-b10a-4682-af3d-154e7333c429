import { useState } from "react";
import {
  ChartBarIcon,
  ArrowDownTrayIcon,
} from "@heroicons/react/24/outline";

export default function TRAStatistics() {
  const [loading, setLoading] = useState(false);

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <ChartBarIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            TRA Compliance Statistics
          </h3>
        </div>
        <button
          onClick={() => setLoading(!loading)}
          className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors duration-200"
        >
          <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
          Export Report
        </button>
      </div>

      <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6 text-center">
        <ChartBarIcon className="h-12 w-12 text-purple-400 mx-auto mb-4" />
        <h4 className="text-lg font-medium text-purple-900 dark:text-purple-100 mb-2">
          TRA Statistics Coming Soon
        </h4>
        <p className="text-sm text-purple-800 dark:text-purple-200 mb-4">
          This section will provide detailed analytics and statistics for TRA compliance.
        </p>
        <div className="space-y-2 text-sm text-purple-700 dark:text-purple-300">
          <p>• VAT collection and payment trends</p>
          <p>• WHT deduction analytics</p>
          <p>• Compliance score tracking</p>
          <p>• Monthly and annual reports</p>
        </div>
      </div>
    </div>
  );
}
