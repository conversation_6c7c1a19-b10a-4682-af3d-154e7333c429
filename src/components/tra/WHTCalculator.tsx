import { useState } from "react";
import {
  CalculatorIcon,
  InformationCircleIcon,
  ClipboardDocumentIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { apiService } from "../../services/api";

interface WHTCalculation {
  whtAmount: number;
  netAmount: number;
  whtRate: number;
}

export default function WHTCalculator() {
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();
  const [grossAmount, setGrossAmount] = useState<string>("");
  const [whtCategory, setWHTCategory] = useState<string>("PROFESSIONAL_SERVICES");
  const [calculation, setCalculation] = useState<WHTCalculation | null>(null);
  const [loading, setLoading] = useState(false);

  const whtCategories = [
    { value: "PROFESSIONAL_SERVICES", label: "Professional Services (5%)", description: "Legal, accounting, consulting services" },
    { value: "CONSULTANCY", label: "Consultancy (5%)", description: "Management and technical consultancy" },
    { value: "MANAGEMENT_FEES", label: "Management Fees (5%)", description: "Management and administration fees" },
    { value: "TECHNICAL_SERVICES", label: "Technical Services (5%)", description: "Technical and engineering services" },
    { value: "RENT", label: "Rent (10%)", description: "Property rental payments" },
    { value: "INTEREST", label: "Interest (10%)", description: "Interest on loans and deposits" },
    { value: "DIVIDENDS", label: "Dividends (10%)", description: "Dividend payments to shareholders" },
    { value: "ROYALTIES", label: "Royalties (15%)", description: "Intellectual property royalties" },
    { value: "COMMISSIONS", label: "Commissions (5%)", description: "Sales and service commissions" },
    { value: "TRANSPORT", label: "Transport (5%)", description: "Transportation services" },
    { value: "CONSTRUCTION", label: "Construction (5%)", description: "Construction and building services" },
    { value: "SECURITY_SERVICES", label: "Security Services (5%)", description: "Security and protection services" },
    { value: "CLEANING_SERVICES", label: "Cleaning Services (5%)", description: "Cleaning and maintenance services" },
    { value: "CATERING", label: "Catering (5%)", description: "Catering and food services" },
  ];

  const handleCalculate = async () => {
    if (!currentCompany || !grossAmount || parseFloat(grossAmount) <= 0) {
      addNotification("error", "Validation Error", "Please enter a valid gross amount");
      return;
    }

    setLoading(true);
    try {
      const data = await apiService.post<any>(`/tra/${currentCompany.id}/wht/calculate`, {
        grossAmount: parseFloat(grossAmount),
        whtCategory,
      });

      setCalculation(data.data);
      addNotification("success", "Success", "WHT calculated successfully");
    } catch (error: any) {
      console.error("Error calculating WHT:", error);
      const errorMessage = error.response?.data?.error || "Failed to calculate WHT";
      addNotification("error", "Calculation Error", errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setGrossAmount("");
    setCalculation(null);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    addNotification("Copied to clipboard", "success");
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-TZ", {
      style: "currency",
      currency: "TZS",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const getWHTThreshold = () => {
    return 30000; // 30K TZS threshold for most WHT categories
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <CalculatorIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Withholding Tax Calculator
          </h3>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
          <InformationCircleIcon className="h-4 w-4" />
          <span>Tanzania WHT Calculation Tool</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <div className="space-y-6">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
              Calculation Parameters
            </h4>

            {/* Gross Amount Input */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Gross Amount (TZS)
                </label>
                <input
                  type="number"
                  value={grossAmount}
                  onChange={(e) => setGrossAmount(e.target.value)}
                  placeholder="Enter gross amount in TZS"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-800 dark:text-white"
                  min="0"
                  step="0.01"
                />
              </div>

              {/* WHT Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Service Category
                </label>
                <select
                  value={whtCategory}
                  onChange={(e) => setWHTCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-800 dark:text-white"
                >
                  {whtCategories.map((cat) => (
                    <option key={cat.value} value={cat.value}>
                      {cat.label}
                    </option>
                  ))}
                </select>
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  {whtCategories.find((cat) => cat.value === whtCategory)?.description}
                </p>
              </div>

              {/* Threshold Information */}
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                <div className="flex items-center">
                  <InformationCircleIcon className="h-4 w-4 text-yellow-600 dark:text-yellow-400 mr-2" />
                  <span className="text-sm text-yellow-800 dark:text-yellow-200">
                    WHT applies to payments above {formatCurrency(getWHTThreshold())}
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 mt-6">
              <button
                onClick={handleCalculate}
                disabled={loading || !grossAmount}
                className="flex-1 flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                <CalculatorIcon className="h-4 w-4 mr-2" />
                {loading ? "Calculating..." : "Calculate WHT"}
              </button>
              <button
                onClick={handleClear}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200"
              >
                Clear
              </button>
            </div>
          </div>

          {/* WHT Information */}
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-2">
              Tanzania WHT Information
            </h4>
            <ul className="text-sm text-green-800 dark:text-green-200 space-y-1">
              <li>• Minimum threshold: 30,000 TZS per payment</li>
              <li>• Professional services: 5% WHT rate</li>
              <li>• Rent and interest: 10% WHT rate</li>
              <li>• Royalties: 15% WHT rate</li>
              <li>• WHT certificates must be issued within 30 days</li>
              <li>• Monthly submission to TRA required</li>
            </ul>
          </div>
        </div>

        {/* Results Section */}
        <div className="space-y-6">
          {calculation ? (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
              <h4 className="text-lg font-medium text-green-900 dark:text-green-100 mb-4">
                WHT Calculation Results
              </h4>

              <div className="space-y-4">
                {/* Gross Amount */}
                <div className="flex justify-between items-center py-2 border-b border-green-200 dark:border-green-700">
                  <span className="text-sm text-green-700 dark:text-green-300">Gross Amount:</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-green-900 dark:text-green-100">
                      {formatCurrency(parseFloat(grossAmount))}
                    </span>
                    <button
                      onClick={() => copyToClipboard(grossAmount)}
                      className="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200"
                    >
                      <ClipboardDocumentIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* WHT Rate */}
                <div className="flex justify-between items-center py-2 border-b border-green-200 dark:border-green-700">
                  <span className="text-sm text-green-700 dark:text-green-300">WHT Rate:</span>
                  <span className="font-medium text-green-900 dark:text-green-100">
                    {calculation.whtRate}%
                  </span>
                </div>

                {/* WHT Amount */}
                <div className="flex justify-between items-center py-2 border-b border-green-200 dark:border-green-700">
                  <span className="text-sm text-green-700 dark:text-green-300">WHT Amount:</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-green-900 dark:text-green-100">
                      {formatCurrency(calculation.whtAmount)}
                    </span>
                    <button
                      onClick={() => copyToClipboard(calculation.whtAmount.toString())}
                      className="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200"
                    >
                      <ClipboardDocumentIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* Net Amount */}
                <div className="flex justify-between items-center py-2 border-b border-green-200 dark:border-green-700">
                  <span className="text-sm text-green-700 dark:text-green-300">Net Amount (After WHT):</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-green-900 dark:text-green-100">
                      {formatCurrency(calculation.netAmount)}
                    </span>
                    <button
                      onClick={() => copyToClipboard(calculation.netAmount.toString())}
                      className="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200"
                    >
                      <ClipboardDocumentIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Summary */}
              <div className="mt-6 p-4 bg-green-100 dark:bg-green-800/30 rounded-lg">
                <h5 className="text-sm font-medium text-green-900 dark:text-green-100 mb-2">
                  Calculation Summary
                </h5>
                <p className="text-sm text-green-800 dark:text-green-200">
                  {calculation.whtAmount > 0
                    ? `From a gross payment of ${formatCurrency(parseFloat(grossAmount))}, withholding tax of ${formatCurrency(calculation.whtAmount)} (${calculation.whtRate}%) is deducted, leaving a net payment of ${formatCurrency(calculation.netAmount)}.`
                    : `The gross amount of ${formatCurrency(parseFloat(grossAmount))} is below the WHT threshold of ${formatCurrency(getWHTThreshold())}, so no withholding tax applies.`
                  }
                </p>
              </div>

              {/* Certificate Note */}
              {calculation.whtAmount > 0 && (
                <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>Note:</strong> A WHT certificate must be issued to the payee within 30 days of payment.
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 text-center">
              <CalculatorIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Ready to Calculate
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Enter a gross amount and select a service category to calculate withholding tax.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
