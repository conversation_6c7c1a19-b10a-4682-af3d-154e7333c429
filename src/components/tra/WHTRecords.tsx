import { useState } from "react";
import {
  DocumentTextIcon,
  PlusIcon,
  EyeIcon,
  ArrowDownTrayIcon,
} from "@heroicons/react/24/outline";

export default function WHTRecords() {
  const [loading, setLoading] = useState(false);

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <DocumentTextIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Withholding Tax Records
          </h3>
        </div>
        <button
          onClick={() => setLoading(!loading)}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Record WHT Transaction
        </button>
      </div>

      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6 text-center">
        <DocumentTextIcon className="h-12 w-12 text-green-400 mx-auto mb-4" />
        <h4 className="text-lg font-medium text-green-900 dark:text-green-100 mb-2">
          WHT Records Coming Soon
        </h4>
        <p className="text-sm text-green-800 dark:text-green-200 mb-4">
          This section will allow you to record, track, and manage withholding tax transactions.
        </p>
        <div className="space-y-2 text-sm text-green-700 dark:text-green-300">
          <p>• Record WHT transactions automatically</p>
          <p>• Generate WHT certificates</p>
          <p>• Submit WHT returns to TRA</p>
          <p>• Track certificate issuance</p>
        </div>
      </div>
    </div>
  );
}
