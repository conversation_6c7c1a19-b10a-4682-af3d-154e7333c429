import { useState } from "react";
import {
  DocumentTextIcon,
  PlusIcon,
  EyeIcon,
  ArrowDownTrayIcon,
} from "@heroicons/react/24/outline";

export default function VATReturns() {
  const [loading, setLoading] = useState(false);

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <DocumentTextIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            VAT Returns Management
          </h3>
        </div>
        <button
          onClick={() => setLoading(!loading)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Generate VAT Return
        </button>
      </div>

      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 text-center">
        <DocumentTextIcon className="h-12 w-12 text-blue-400 mx-auto mb-4" />
        <h4 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
          VAT Returns Coming Soon
        </h4>
        <p className="text-sm text-blue-800 dark:text-blue-200 mb-4">
          This section will allow you to generate, submit, and manage VAT returns for TRA compliance.
        </p>
        <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
          <p>• Generate monthly VAT returns automatically</p>
          <p>• Submit returns directly to TRA</p>
          <p>• Track submission status and responses</p>
          <p>• Download VAT certificates</p>
        </div>
      </div>
    </div>
  );
}
