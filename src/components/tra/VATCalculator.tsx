import { useState } from "react";
import {
  CalculatorIcon,
  InformationCircleIcon,
  ClipboardDocumentIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { apiService } from "../../services/api";

interface VATCalculation {
  vatAmount: number;
  netAmount: number;
  vatRate: number;
}

export default function VATCalculator() {
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();
  const [amount, setAmount] = useState<string>("");
  const [isVATInclusive, setIsVATInclusive] = useState<boolean>(false);
  const [category, setCategory] = useState<string>("STANDARD");
  const [calculation, setCalculation] = useState<VATCalculation | null>(null);
  const [loading, setLoading] = useState(false);

  const vatCategories = [
    { value: "STANDARD", label: "Standard Rate (18%)", description: "Most goods and services" },
    { value: "ZERO_RATED", label: "Zero-Rated (0%)", description: "Exports, basic food items" },
    { value: "EXEMPT", label: "Exempt", description: "Financial services, education" },
  ];

  const handleCalculate = async () => {
    if (!currentCompany || !amount || parseFloat(amount) <= 0) {
      addNotification("error", "Validation Error", "Please enter a valid amount");
      return;
    }

    setLoading(true);
    try {
      const data = await apiService.post<any>(`/tra/${currentCompany.id}/vat/calculate`, {
        amount: parseFloat(amount),
        isVATInclusive,
        category,
      });

      setCalculation(data.data);
      addNotification("success", "Success", "VAT calculated successfully");
    } catch (error: any) {
      console.error("Error calculating VAT:", error);
      const errorMessage = error.response?.data?.error || "Failed to calculate VAT";
      addNotification("error", "Calculation Error", errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setAmount("");
    setCalculation(null);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    addNotification("success", "Copied to clipboard", "Value copied to clipboard");
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-TZ", {
      style: "currency",
      currency: "TZS",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <CalculatorIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            VAT Calculator
          </h3>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
          <InformationCircleIcon className="h-4 w-4" />
          <span>Tanzania VAT Calculation Tool</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <div className="space-y-6">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
              Calculation Parameters
            </h4>

            {/* Amount Input */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Amount (TZS)
                </label>
                <input
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="Enter amount in TZS"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                  min="0"
                  step="0.01"
                />
              </div>

              {/* VAT Inclusive Toggle */}
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="vatInclusive"
                  checked={isVATInclusive}
                  onChange={(e) => setIsVATInclusive(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="vatInclusive" className="text-sm text-gray-700 dark:text-gray-300">
                  Amount includes VAT
                </label>
              </div>

              {/* VAT Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  VAT Category
                </label>
                <select
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                >
                  {vatCategories.map((cat) => (
                    <option key={cat.value} value={cat.value}>
                      {cat.label}
                    </option>
                  ))}
                </select>
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  {vatCategories.find((cat) => cat.value === category)?.description}
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 mt-6">
              <button
                onClick={handleCalculate}
                disabled={loading || !amount}
                className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                <CalculatorIcon className="h-4 w-4 mr-2" />
                {loading ? "Calculating..." : "Calculate VAT"}
              </button>
              <button
                onClick={handleClear}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
              >
                Clear
              </button>
            </div>
          </div>

          {/* VAT Information */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
              Tanzania VAT Information
            </h4>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• Standard VAT rate: 18%</li>
              <li>• VAT registration threshold: 100M TZS annual turnover</li>
              <li>• VAT returns due: 20th of following month</li>
              <li>• Zero-rated: Exports, basic food items</li>
              <li>• Exempt: Financial services, education, healthcare</li>
            </ul>
          </div>
        </div>

        {/* Results Section */}
        <div className="space-y-6">
          {calculation ? (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
              <h4 className="text-lg font-medium text-green-900 dark:text-green-100 mb-4">
                VAT Calculation Results
              </h4>

              <div className="space-y-4">
                {/* Original Amount */}
                <div className="flex justify-between items-center py-2 border-b border-green-200 dark:border-green-700">
                  <span className="text-sm text-green-700 dark:text-green-300">
                    {isVATInclusive ? "Gross Amount (VAT Inclusive)" : "Net Amount (VAT Exclusive)"}:
                  </span>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-green-900 dark:text-green-100">
                      {formatCurrency(parseFloat(amount))}
                    </span>
                    <button
                      onClick={() => copyToClipboard(amount)}
                      className="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200"
                    >
                      <ClipboardDocumentIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* VAT Rate */}
                <div className="flex justify-between items-center py-2 border-b border-green-200 dark:border-green-700">
                  <span className="text-sm text-green-700 dark:text-green-300">VAT Rate:</span>
                  <span className="font-medium text-green-900 dark:text-green-100">
                    {calculation.vatRate}%
                  </span>
                </div>

                {/* VAT Amount */}
                <div className="flex justify-between items-center py-2 border-b border-green-200 dark:border-green-700">
                  <span className="text-sm text-green-700 dark:text-green-300">VAT Amount:</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-green-900 dark:text-green-100">
                      {formatCurrency(calculation.vatAmount)}
                    </span>
                    <button
                      onClick={() => copyToClipboard(calculation.vatAmount.toString())}
                      className="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200"
                    >
                      <ClipboardDocumentIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* Net Amount */}
                <div className="flex justify-between items-center py-2 border-b border-green-200 dark:border-green-700">
                  <span className="text-sm text-green-700 dark:text-green-300">
                    {isVATInclusive ? "Net Amount (VAT Exclusive)" : "Gross Amount (VAT Inclusive)"}:
                  </span>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-green-900 dark:text-green-100">
                      {formatCurrency(isVATInclusive ? calculation.netAmount : calculation.netAmount + calculation.vatAmount)}
                    </span>
                    <button
                      onClick={() => copyToClipboard((isVATInclusive ? calculation.netAmount : calculation.netAmount + calculation.vatAmount).toString())}
                      className="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200"
                    >
                      <ClipboardDocumentIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Summary */}
              <div className="mt-6 p-4 bg-green-100 dark:bg-green-800/30 rounded-lg">
                <h5 className="text-sm font-medium text-green-900 dark:text-green-100 mb-2">
                  Calculation Summary
                </h5>
                <p className="text-sm text-green-800 dark:text-green-200">
                  {isVATInclusive
                    ? `From a VAT-inclusive amount of ${formatCurrency(parseFloat(amount))}, the VAT component is ${formatCurrency(calculation.vatAmount)} and the net amount is ${formatCurrency(calculation.netAmount)}.`
                    : `On a net amount of ${formatCurrency(parseFloat(amount))}, VAT of ${formatCurrency(calculation.vatAmount)} applies, making the total ${formatCurrency(calculation.netAmount + calculation.vatAmount)}.`
                  }
                </p>
              </div>
            </div>
          ) : (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 text-center">
              <CalculatorIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Ready to Calculate
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Enter an amount and click "Calculate VAT" to see the breakdown.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
