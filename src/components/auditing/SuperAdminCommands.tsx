import { useState } from "react";
import {
  CommandLineIcon,
  PlayIcon,
  DocumentArrowDownIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  ChartBarIcon,
} from "@heroicons/react/24/outline";

interface CommandResult {
  command: string;
  output: string;
  status: 'SUCCESS' | 'ERROR' | 'WARNING';
  timestamp: string;
  executionTime: number;
}

interface AuditCommand {
  id: string;
  name: string;
  description: string;
  command: string;
  category: 'REPORTING' | 'COMPLIANCE' | 'INVESTIGATION' | 'MAINTENANCE';
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  icon: any;
}

export default function SuperAdminCommands() {
  const [selectedCommand, setSelectedCommand] = useState<string>('');
  const [customCommand, setCustomCommand] = useState<string>('');
  const [commandHistory, setCommandHistory] = useState<CommandResult[]>([]);
  const [executing, setExecuting] = useState(false);

  const auditCommands: AuditCommand[] = [
    {
      id: 'generate-tra-report',
      name: 'Generate TRA Monthly Report',
      description: 'Generate comprehensive TRA compliance report for the current month',
      command: 'audit:generate-tra-report --month=current --format=pdf',
      category: 'REPORTING',
      riskLevel: 'LOW',
      icon: DocumentArrowDownIcon,
    },
    {
      id: 'generate-bot-report',
      name: 'Generate BOT Quarterly Report',
      description: 'Generate Bank of Tanzania quarterly compliance report',
      command: 'audit:generate-bot-report --quarter=current --format=pdf',
      category: 'REPORTING',
      riskLevel: 'LOW',
      icon: DocumentArrowDownIcon,
    },
    {
      id: 'suspicious-activity-scan',
      name: 'Suspicious Activity Scan',
      description: 'Scan for suspicious activities in the last 30 days',
      command: 'audit:scan-suspicious --days=30 --threshold=high',
      category: 'INVESTIGATION',
      riskLevel: 'MEDIUM',
      icon: ExclamationTriangleIcon,
    },
    {
      id: 'compliance-check',
      name: 'Full Compliance Check',
      description: 'Run comprehensive compliance check against all regulations',
      command: 'audit:compliance-check --regulations=all --detailed',
      category: 'COMPLIANCE',
      riskLevel: 'MEDIUM',
      icon: ShieldCheckIcon,
    },
    {
      id: 'audit-log-cleanup',
      name: 'Audit Log Cleanup',
      description: 'Clean up old audit logs based on retention policies',
      command: 'audit:cleanup --dry-run --retention-policy=default',
      category: 'MAINTENANCE',
      riskLevel: 'HIGH',
      icon: ClockIcon,
    },
    {
      id: 'risk-assessment',
      name: 'Risk Assessment Report',
      description: 'Generate comprehensive risk assessment for all activities',
      command: 'audit:risk-assessment --period=30days --include-recommendations',
      category: 'INVESTIGATION',
      riskLevel: 'LOW',
      icon: ChartBarIcon,
    },
  ];

  const executeCommand = async (command: string) => {
    if (!command.trim()) return;

    setExecuting(true);
    const startTime = Date.now();

    try {
      // Mock command execution
      await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));

      const executionTime = Date.now() - startTime;
      const mockOutput = generateMockOutput(command);
      const status = Math.random() > 0.1 ? 'SUCCESS' : 'WARNING';

      const result: CommandResult = {
        command,
        output: mockOutput,
        status,
        timestamp: new Date().toISOString(),
        executionTime,
      };

      setCommandHistory(prev => [result, ...prev.slice(0, 9)]); // Keep last 10 results
      setSelectedCommand('');
      setCustomCommand('');
    } catch (error) {
      const result: CommandResult = {
        command,
        output: `Error executing command: ${error}`,
        status: 'ERROR',
        timestamp: new Date().toISOString(),
        executionTime: Date.now() - startTime,
      };
      setCommandHistory(prev => [result, ...prev.slice(0, 9)]);
    } finally {
      setExecuting(false);
    }
  };

  const generateMockOutput = (command: string): string => {
    if (command.includes('tra-report')) {
      return `TRA Monthly Report Generated Successfully
=====================================
Report Period: June 2025
Total Transactions: 1,247
VAT Transactions: 89
Total VAT Amount: 15,750,000 TZS
Withholding Tax Transactions: 156
Total Withholding Tax: 2,340,000 TZS
EFD Transactions: 1,198
Compliance Status: COMPLIANT
Report saved to: /reports/tra_monthly_june_2025.pdf`;
    }

    if (command.includes('bot-report')) {
      return `BOT Quarterly Report Generated Successfully
==========================================
Report Period: Q2 2025
Forex Transactions: 23
Total Forex Amount: $125,000 USD
Large Cash Transactions: 5
Total Large Cash: 45,000,000 TZS
Reportable Transactions: 8
Compliance Status: COMPLIANT
Report saved to: /reports/bot_quarterly_q2_2025.pdf`;
    }

    if (command.includes('suspicious')) {
      return `Suspicious Activity Scan Completed
===================================
Scan Period: Last 30 days
High-Risk Activities Found: 5
- Large cash transactions: 2
- High-frequency trading: 1
- Unusual access patterns: 2
Critical Issues: 0
Recommendations: Review flagged transactions
Detailed report saved to: /reports/suspicious_activity_scan.json`;
    }

    if (command.includes('compliance-check')) {
      return `Comprehensive Compliance Check Completed
========================================
TRA Compliance: PASS (98.5%)
BOT Compliance: PASS (96.2%)
AML Compliance: PASS (99.1%)
Overall Score: 97.9%
Issues Found: 3 minor
- Missing EFD receipts: 2
- Late VAT filing: 1
Recommendations available in detailed report`;
    }

    if (command.includes('cleanup')) {
      return `Audit Log Cleanup Analysis (DRY RUN)
====================================
Total Audit Logs: 125,847
Logs to be archived: 45,230
Logs to be deleted: 12,156
Estimated space savings: 2.3 GB
Retention policy: 7 years
Run with --execute flag to perform cleanup`;
    }

    if (command.includes('risk-assessment')) {
      return `Risk Assessment Report Generated
=================================
Assessment Period: Last 30 days
Overall Risk Level: MEDIUM
High-Risk Users: 3
High-Risk Transactions: 12
Risk Factors Identified:
- Unusual transaction patterns: 5
- Failed access attempts: 8
- Large cash transactions: 4
Recommendations: Enhanced monitoring for flagged users`;
    }

    return `Command executed successfully
Output: ${command}
Status: Completed
Timestamp: ${new Date().toLocaleString()}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUCCESS': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'WARNING': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'ERROR': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500';
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'HIGH': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      case 'MEDIUM': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      default: return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
    }
  };

  return (
    <div className="space-y-6">
      {/* Command Categories */}
      <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {['REPORTING', 'COMPLIANCE', 'INVESTIGATION', 'MAINTENANCE'].map((category) => {
          const categoryCommands = auditCommands.filter(cmd => cmd.category === category);
          return (
            <div key={category} className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow border border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                {category.replace('_', ' ')}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-3">
                {categoryCommands.length} commands available
              </p>
              <div className="space-y-1">
                {categoryCommands.slice(0, 2).map((cmd) => (
                  <button
                    key={cmd.id}
                    onClick={() => setSelectedCommand(cmd.command)}
                    className="w-full text-left text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 truncate"
                  >
                    {cmd.name}
                  </button>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* Command Execution */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Command Execution
        </h3>

        {/* Predefined Commands */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Select Predefined Command
          </label>
          <select
            value={selectedCommand}
            onChange={(e) => setSelectedCommand(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="">Choose a command...</option>
            {auditCommands.map((cmd) => (
              <option key={cmd.id} value={cmd.command}>
                {cmd.name} - {cmd.description}
              </option>
            ))}
          </select>
        </div>

        {/* Custom Command */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Or Enter Custom Command
          </label>
          <div className="flex space-x-2">
            <input
              type="text"
              value={customCommand}
              onChange={(e) => setCustomCommand(e.target.value)}
              placeholder="audit:command --options"
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white font-mono text-sm"
            />
            <button
              onClick={() => executeCommand(customCommand)}
              disabled={executing || !customCommand.trim()}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <PlayIcon className="h-4 w-4 mr-2" />
              Execute
            </button>
          </div>
        </div>

        {/* Execute Selected Command */}
        {selectedCommand && (
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Selected Command:</span>
              <button
                onClick={() => executeCommand(selectedCommand)}
                disabled={executing}
                className="flex items-center px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                <PlayIcon className="h-4 w-4 mr-1" />
                {executing ? 'Executing...' : 'Execute'}
              </button>
            </div>
            <code className="text-sm text-gray-800 dark:text-gray-200 font-mono">
              {selectedCommand}
            </code>
          </div>
        )}
      </div>

      {/* Command History */}
      {commandHistory.length > 0 && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Command History
            </h3>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
              Recent command executions and their results
            </p>
          </div>
          <div className="p-6">
            <div className="space-y-4 bg-white dark:bg-gray-800">
              {commandHistory.map((result, index) => (
                <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <CommandLineIcon className="h-4 w-4 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                        <code className="text-sm font-mono text-gray-800 dark:text-gray-200">
                          {result.command}
                        </code>
                      </div>
                      <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        <span>{new Date(result.timestamp).toLocaleString()}</span>
                        <span>Execution time: {result.executionTime}ms</span>
                      </div>
                    </div>
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(result.status)}`}>
                      {result.status}
                    </span>
                  </div>
                  <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                    <pre className="text-xs text-gray-700 dark:text-gray-300 whitespace-pre-wrap font-mono">
                      {result.output}
                    </pre>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Command Reference */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <CommandLineIcon className="h-5 w-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-300">
              Super Admin Command Reference
            </h3>
            <div className="mt-2 text-sm text-blue-700 dark:text-blue-400">
              <ul className="list-disc list-inside space-y-1">
                <li><code>audit:generate-tra-report</code> - Generate TRA compliance reports</li>
                <li><code>audit:generate-bot-report</code> - Generate BOT compliance reports</li>
                <li><code>audit:scan-suspicious</code> - Scan for suspicious activities</li>
                <li><code>audit:compliance-check</code> - Run comprehensive compliance checks</li>
                <li><code>audit:cleanup</code> - Manage audit log retention and cleanup</li>
                <li><code>audit:risk-assessment</code> - Generate risk assessment reports</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
