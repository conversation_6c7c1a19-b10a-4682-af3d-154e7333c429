import { useState } from "react";
import {
  MagnifyingGlassIcon,
  UserIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  ChartBarIcon,
} from "@heroicons/react/24/outline";

interface InvestigationQuery {
  type: 'USER' | 'TRANSACTION' | 'IP_ADDRESS' | 'TIME_RANGE' | 'PATTERN';
  value: string;
  startDate?: string;
  endDate?: string;
}

interface InvestigationResult {
  id: string;
  type: string;
  description: string;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  timestamp: string;
  details: any;
}

export default function InvestigationTools() {
  const [query, setQuery] = useState<InvestigationQuery>({
    type: 'USER',
    value: '',
  });
  const [results, setResults] = useState<InvestigationResult[]>([]);
  const [loading, setLoading] = useState(false);

  const investigationTypes = [
    { value: 'USER', label: 'User Activity', icon: UserIcon },
    { value: 'TRANSACTION', label: 'Transaction Analysis', icon: DocumentTextIcon },
    { value: 'IP_ADDRESS', label: 'IP Address Tracking', icon: MagnifyingGlassIcon },
    { value: 'TIME_RANGE', label: 'Time-based Analysis', icon: ClockIcon },
    { value: 'PATTERN', label: 'Pattern Detection', icon: ChartBarIcon },
  ];

  const handleInvestigation = async () => {
    if (!query.value.trim()) return;

    setLoading(true);
    try {
      // Mock investigation results
      const mockResults: InvestigationResult[] = [
        {
          id: '1',
          type: 'Suspicious Login',
          description: `Multiple failed login attempts for ${query.value}`,
          riskLevel: 'HIGH',
          timestamp: new Date().toISOString(),
          details: {
            attempts: 15,
            ipAddresses: ['*************', '*********'],
            timeSpan: '2 hours'
          }
        },
        {
          id: '2',
          type: 'High-Value Transaction',
          description: `Large transaction initiated by ${query.value}`,
          riskLevel: 'MEDIUM',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          details: {
            amount: ********,
            currency: 'TZS',
            recipient: 'External Account'
          }
        }
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      setResults(mockResults);
    } catch (error) {
      console.error('Investigation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'CRITICAL': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      case 'HIGH': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400';
      case 'MEDIUM': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      default: return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
    }
  };

  return (
    <div className="space-y-6">
      {/* Investigation Query */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Investigation Query
        </h3>
        
        <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Investigation Type
            </label>
            <select
              value={query.type}
              onChange={(e) => setQuery({ ...query, type: e.target.value as any })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            >
              {investigationTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search Value
            </label>
            <input
              type="text"
              value={query.value}
              onChange={(e) => setQuery({ ...query, value: e.target.value })}
              placeholder="Enter user email, transaction ID, IP address, etc."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        {query.type === 'TIME_RANGE' && (
          <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Start Date
              </label>
              <input
                type="datetime-local"
                value={query.startDate || ''}
                onChange={(e) => setQuery({ ...query, startDate: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                End Date
              </label>
              <input
                type="datetime-local"
                value={query.endDate || ''}
                onChange={(e) => setQuery({ ...query, endDate: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
        )}

        <button
          onClick={handleInvestigation}
          disabled={loading || !query.value.trim()}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          <MagnifyingGlassIcon className="h-5 w-5 mr-2" />
          {loading ? 'Investigating...' : 'Start Investigation'}
        </button>
      </div>

      {/* Investigation Results */}
      {(loading || results.length > 0) && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Investigation Results
            </h3>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
              {loading ? 'Analyzing data...' : `Found ${results.length} potential issues`}
            </p>
          </div>

          <div className="p-6">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Running forensic analysis...
                </p>
              </div>
            ) : (
              <div className="space-y-4 bg-white dark:bg-gray-800">
                {results.map((result) => (
                  <div key={result.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <ExclamationTriangleIcon className={`h-6 w-6 mt-0.5 ${
                          result.riskLevel === 'CRITICAL' ? 'text-red-600 dark:text-red-400' :
                          result.riskLevel === 'HIGH' ? 'text-orange-600' :
                          result.riskLevel === 'MEDIUM' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-green-600 dark:text-green-400'
                        }`} />
                        <div className="flex-1">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                            {result.type}
                          </h4>
                          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                            {result.description}
                          </p>
                          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                            {new Date(result.timestamp).toLocaleString()}
                          </div>
                        </div>
                      </div>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getRiskLevelColor(result.riskLevel)}`}>
                        {result.riskLevel}
                      </span>
                    </div>

                    {/* Result Details */}
                    <div className="mt-4 pl-9">
                      <div className="bg-gray-50 dark:bg-gray-800 rounded-md p-3">
                        <h5 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Investigation Details:
                        </h5>
                        <div className="space-y-1 text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          {Object.entries(result.details).map(([key, value]) => (
                            <div key={key} className="flex justify-between">
                              <span className="capitalize">{key.replace(/([A-Z])/g, ' $1').toLowerCase()}:</span>
                              <span className="font-medium">
                                {Array.isArray(value) ? value.join(', ') : String(value)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Investigation Tips */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <MagnifyingGlassIcon className="h-5 w-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-300">
              Investigation Tips
            </h3>
            <div className="mt-2 text-sm text-blue-700 dark:text-blue-400">
              <ul className="list-disc list-inside space-y-1">
                <li>Use specific user emails or transaction IDs for targeted investigations</li>
                <li>IP address tracking helps identify suspicious access patterns</li>
                <li>Time-range analysis reveals activity spikes and unusual patterns</li>
                <li>Pattern detection automatically identifies anomalies in user behavior</li>
                <li>Cross-reference multiple investigation types for comprehensive analysis</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
