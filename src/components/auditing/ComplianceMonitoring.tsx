import { useState, useEffect } from "react";
import {
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  DocumentArrowDownIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  InformationCircleIcon,
  CurrencyDollarIcon,
  BanknotesIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";

interface ComplianceStatus {
  authority: 'TRA' | 'BOT' | 'AML';
  status: 'COMPLIANT' | 'WARNING' | 'NON_COMPLIANT' | 'UNDER_REVIEW';
  score: number;
  lastChecked: string;
  issues: ComplianceIssue[];
  nextReportDue: string;
}

interface ComplianceIssue {
  id: string;
  type: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  recommendation: string;
  dueDate?: string;
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED';
}

interface ComplianceMetric {
  name: string;
  value: number;
  threshold: number;
  unit: string;
  status: 'SAFE' | 'WARNING' | 'CRITICAL';
  trend: 'UP' | 'DOWN' | 'STABLE';
}

interface ComplianceAlert {
  id: string;
  type: 'THRESHOLD_EXCEEDED' | 'REPORT_DUE' | 'SUSPICIOUS_ACTIVITY' | 'REGULATORY_UPDATE';
  authority: 'TRA' | 'BOT' | 'AML' | 'SYSTEM';
  message: string;
  severity: 'INFO' | 'WARNING' | 'CRITICAL';
  timestamp: string;
  actionRequired: boolean;
}

export default function ComplianceMonitoring() {
  const [complianceStatuses, setComplianceStatuses] = useState<ComplianceStatus[]>([]);
  const [complianceMetrics, setComplianceMetrics] = useState<ComplianceMetric[]>([]);
  const [complianceAlerts, setComplianceAlerts] = useState<ComplianceAlert[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedAuthority, setSelectedAuthority] = useState<'ALL' | 'TRA' | 'BOT' | 'AML'>('ALL');

  useEffect(() => {
    loadComplianceData();
  }, []);

  const loadComplianceData = async () => {
    setLoading(true);
    try {
      // Mock data - replace with actual API calls
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setComplianceStatuses([
        {
          authority: 'TRA',
          status: 'COMPLIANT',
          score: 96.5,
          lastChecked: new Date().toISOString(),
          nextReportDue: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          issues: [
            {
              id: '1',
              type: 'Missing EFD Receipt',
              severity: 'LOW',
              description: '2 transactions missing EFD receipts',
              recommendation: 'Generate missing EFD receipts for transactions above 5K TZS',
              status: 'OPEN'
            }
          ]
        },
        {
          authority: 'BOT',
          status: 'WARNING',
          score: 88.2,
          lastChecked: new Date().toISOString(),
          nextReportDue: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
          issues: [
            {
              id: '2',
              type: 'Large Cash Transaction',
              severity: 'MEDIUM',
              description: '3 transactions exceed 5M TZS without proper documentation',
              recommendation: 'Submit large cash transaction reports to BOT',
              dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
              status: 'IN_PROGRESS'
            }
          ]
        },
        {
          authority: 'AML',
          status: 'COMPLIANT',
          score: 94.8,
          lastChecked: new Date().toISOString(),
          nextReportDue: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          issues: []
        }
      ]);

      setComplianceMetrics([
        {
          name: 'VAT Transactions',
          value: 89,
          threshold: 100,
          unit: 'transactions',
          status: 'SAFE',
          trend: 'UP'
        },
        {
          name: 'Forex Transactions',
          value: 12,
          threshold: 10,
          unit: 'transactions',
          status: 'WARNING',
          trend: 'UP'
        },
        {
          name: 'Large Cash Transactions',
          value: 3,
          threshold: 5,
          unit: 'transactions',
          status: 'SAFE',
          trend: 'STABLE'
        },
        {
          name: 'PEP Customers',
          value: 2,
          threshold: 5,
          unit: 'customers',
          status: 'SAFE',
          trend: 'STABLE'
        },
        {
          name: 'Failed Access Attempts',
          value: 18,
          threshold: 20,
          unit: 'attempts',
          status: 'WARNING',
          trend: 'UP'
        }
      ]);

      setComplianceAlerts([
        {
          id: '1',
          type: 'THRESHOLD_EXCEEDED',
          authority: 'BOT',
          message: 'Forex transaction threshold exceeded: 12 transactions this month (limit: 10)',
          severity: 'WARNING',
          timestamp: new Date().toISOString(),
          actionRequired: true
        },
        {
          id: '2',
          type: 'REPORT_DUE',
          authority: 'TRA',
          message: 'TRA monthly report due in 7 days',
          severity: 'INFO',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          actionRequired: true
        },
        {
          id: '3',
          type: 'SUSPICIOUS_ACTIVITY',
          authority: 'AML',
          message: 'High-frequency transactions detected for customer ID: CUST-001',
          severity: 'CRITICAL',
          timestamp: new Date(Date.now() - 7200000).toISOString(),
          actionRequired: true
        }
      ]);
    } catch (error) {
      console.error('Failed to load compliance data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLIANT': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'WARNING': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'NON_COMPLIANT': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      case 'UNDER_REVIEW': return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400';
      default: return 'text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      case 'HIGH': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400';
      case 'MEDIUM': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'LOW': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      default: return 'text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500';
    }
  };

  const getMetricStatusColor = (status: string) => {
    switch (status) {
      case 'SAFE': return 'text-green-600 dark:text-green-400';
      case 'WARNING': return 'text-yellow-600 dark:text-yellow-400';
      case 'CRITICAL': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500';
    }
  };

  const getAlertSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'border-red-500 bg-red-50 dark:bg-red-900/20';
      case 'WARNING': return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      case 'INFO': return 'border-blue-500 bg-blue-50 dark:bg-blue-900/20';
      default: return 'border-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const filteredStatuses = selectedAuthority === 'ALL' 
    ? complianceStatuses 
    : complianceStatuses.filter(status => status.authority === selectedAuthority);

  const filteredAlerts = selectedAuthority === 'ALL'
    ? complianceAlerts
    : complianceAlerts.filter(alert => alert.authority === selectedAuthority);

  return (
    <div className="space-y-6">
      {/* Header with Authority Filter */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Tanzania Regulatory Compliance Monitoring
          </h3>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Real-time compliance tracking for TRA, BOT, and AML regulations
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={selectedAuthority}
            onChange={(e) => setSelectedAuthority(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm"
          >
            <option value="ALL">All Authorities</option>
            <option value="TRA">TRA Only</option>
            <option value="BOT">BOT Only</option>
            <option value="AML">AML Only</option>
          </select>
          <button
            onClick={loadComplianceData}
            disabled={loading}
            className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 text-sm transition-colors duration-200"
          >
            <ClockIcon className="h-4 w-4 mr-2" />
            {loading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* Compliance Status Overview */}
      <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-3 gap-6">
        {filteredStatuses.map((status) => (
          <div key={status.authority} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  {status.authority === 'TRA' && <DocumentArrowDownIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />}
                  {status.authority === 'BOT' && <BanknotesIcon className="h-8 w-8 text-green-600 dark:text-green-400" />}
                  {status.authority === 'AML' && <ShieldCheckIcon className="h-8 w-8 text-purple-600" />}
                </div>
                <div className="ml-3">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                    {status.authority} Compliance
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    Score: {status.score}%
                  </p>
                </div>
              </div>
              <span className={`px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(status.status)}`}>
                {status.status.replace('_', ' ')}
              </span>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Last Checked:</span>
                <span className="text-gray-900 dark:text-white">
                  {new Date(status.lastChecked).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Next Report Due:</span>
                <span className="text-gray-900 dark:text-white">
                  {new Date(status.nextReportDue).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Open Issues:</span>
                <span className={`font-medium ${status.issues.length > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                  {status.issues.length}
                </span>
              </div>
            </div>

            {status.issues.length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Recent Issues:</h5>
                <div className="space-y-2">
                  {status.issues.slice(0, 2).map((issue) => (
                    <div key={issue.id} className="text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-900 dark:text-white">{issue.type}</span>
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(issue.severity)}`}>
                          {issue.severity}
                        </span>
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">{issue.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Compliance Metrics */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Compliance Metrics</h3>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Key metrics tracked for regulatory compliance
          </p>
        </div>
        <div className="p-6">
          <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {complianceMetrics.map((metric, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">{metric.name}</h4>
                  <span className={`text-sm font-semibold ${getMetricStatusColor(metric.status)}`}>
                    {metric.status}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-2xl font-bold text-gray-900 dark:text-white">{metric.value}</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 ml-1">/ {metric.threshold}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{metric.unit}</div>
                    <div className={`text-xs ${
                      metric.trend === 'UP' ? 'text-red-600 dark:text-red-400' : 
                      metric.trend === 'DOWN' ? 'text-green-600 dark:text-green-400' : 
                      'text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500'
                    }`}>
                      {metric.trend === 'UP' ? '↗' : metric.trend === 'DOWN' ? '↘' : '→'} {metric.trend}
                    </div>
                  </div>
                </div>
                <div className="mt-2">
                  <div className="w-full bg-gray-200 dark:bg-gray-800 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        metric.status === 'SAFE' ? 'bg-green-600' :
                        metric.status === 'WARNING' ? 'bg-yellow-600' :
                        'bg-red-600'
                      }`}
                      style={{ width: `${Math.min((metric.value / metric.threshold) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Compliance Alerts */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Compliance Alerts</h3>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Recent compliance alerts requiring attention
          </p>
        </div>
        <div className="p-6">
          {filteredAlerts.length > 0 ? (
            <div className="space-y-4 bg-white dark:bg-gray-800">
              {filteredAlerts.map((alert) => (
                <div key={alert.id} className={`border-l-4 p-4 rounded-r-lg ${getAlertSeverityColor(alert.severity)}`}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        {alert.severity === 'CRITICAL' && <ExclamationTriangleIcon className="h-6 w-6 text-red-600 dark:text-red-400" />}
                        {alert.severity === 'WARNING' && <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />}
                        {alert.severity === 'INFO' && <InformationCircleIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {alert.authority} - {alert.type.replace(/_/g, ' ')}
                          </span>
                          {alert.actionRequired && (
                            <span className="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 dark:bg-red-900/20 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                              Action Required
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-700 dark:text-gray-300">{alert.message}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">
                          {new Date(alert.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                        View Details
                      </button>
                      <button className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300">
                        Dismiss
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CheckCircleIcon className="mx-auto h-12 w-12 text-green-600 dark:text-green-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No Active Alerts</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                All compliance requirements are currently being met.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Quick Compliance Actions</h3>
        <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
            <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
            Generate TRA Report
          </button>
          <button className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
            <BanknotesIcon className="h-5 w-5 mr-2" />
            Submit BOT Report
          </button>
          <button className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
            <ShieldCheckIcon className="h-5 w-5 mr-2" />
            Run AML Scan
          </button>
          <button className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
            <ChartBarIcon className="h-5 w-5 mr-2" />
            View Analytics
          </button>
        </div>
      </div>
    </div>
  );
}
