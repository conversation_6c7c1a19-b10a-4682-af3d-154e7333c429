import { useState, useEffect } from "react";
import {
  DocumentArrowDownIcon,
  ChartBarIcon,
  CalendarIcon,
  FunnelIcon,
  EyeIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  TableCellsIcon,
  PresentationChartLineIcon,
  ArrowDownTrayIcon,
  XMarkIcon,
  PlusIcon,
  PlayIcon,
  ArrowPathIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: 'COMPLIANCE' | 'AUDIT' | 'FINANCIAL' | 'SECURITY' | 'ANALYTICS';
  authority?: 'TRA' | 'BOT' | 'AML';
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'ANNUAL' | 'ON_DEMAND';
  format: 'PDF' | 'EXCEL' | 'CSV' | 'JSON';
  estimatedTime: string;
  lastGenerated?: string;
  icon: any;
}

interface GeneratedReport {
  id: string;
  templateId: string;
  name: string;
  category: string;
  authority?: string;
  generatedAt: string;
  generatedBy: string;
  status: 'GENERATING' | 'COMPLETED' | 'FAILED' | 'SCHEDULED';
  format: string;
  size: string;
  downloadUrl?: string;
}

interface ReportFilters {
  category: string;
  authority: string;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  format: string;
  includeCharts: boolean;
  includeDetails: boolean;
}

export default function AdvancedReports() {
  const [reportTemplates, setReportTemplates] = useState<ReportTemplate[]>([]);
  const [generatedReports, setGeneratedReports] = useState<GeneratedReport[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [filters, setFilters] = useState<ReportFilters>({
    category: 'ALL',
    authority: 'ALL',
    dateRange: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
    },
    format: 'PDF',
    includeCharts: true,
    includeDetails: true,
  });
  const [loading, setLoading] = useState(false);
  const [generatingTemplates, setGeneratingTemplates] = useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = useState<'templates' | 'generated' | 'scheduled'>('templates');

  // Modal states
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [showCustomReportModal, setShowCustomReportModal] = useState(false);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [viewingTemplate, setViewingTemplate] = useState<ReportTemplate | null>(null);
  const [confirmationMessage, setConfirmationMessage] = useState('');
  const [confirmationType, setConfirmationType] = useState<'success' | 'error' | 'info'>('success');

  // Custom report state
  const [customReport, setCustomReport] = useState({
    name: '',
    description: '',
    category: 'CUSTOM',
    format: 'PDF',
    sections: [] as string[],
    dateRange: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
    },
    includeCharts: true,
    includeDetails: true,
  });

  // Schedule state
  const [scheduleData, setScheduleData] = useState({
    templateId: '',
    name: '',
    frequency: 'MONTHLY',
    dayOfMonth: 1,
    time: '09:00',
    recipients: [''],
    format: 'PDF',
    enabled: true,
  });

  useEffect(() => {
    loadReportTemplates();
    loadGeneratedReports();
  }, []);

  const loadReportTemplates = () => {
    const templates: ReportTemplate[] = [
      {
        id: 'tra-monthly',
        name: 'TRA Monthly Compliance Report',
        description: 'Comprehensive monthly report for Tanzania Revenue Authority including VAT, withholding tax, and EFD compliance',
        category: 'COMPLIANCE',
        authority: 'TRA',
        frequency: 'MONTHLY',
        format: 'PDF',
        estimatedTime: '2-3 minutes',
        lastGenerated: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        icon: DocumentTextIcon,
      },
      {
        id: 'bot-quarterly',
        name: 'BOT Quarterly Report',
        description: 'Bank of Tanzania quarterly compliance report covering forex transactions and large cash movements',
        category: 'COMPLIANCE',
        authority: 'BOT',
        frequency: 'QUARTERLY',
        format: 'PDF',
        estimatedTime: '3-5 minutes',
        lastGenerated: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        icon: DocumentTextIcon,
      },
      {
        id: 'aml-suspicious',
        name: 'AML Suspicious Activity Report',
        description: 'Anti-Money Laundering report highlighting suspicious transactions and high-risk customers',
        category: 'COMPLIANCE',
        authority: 'AML',
        frequency: 'ON_DEMAND',
        format: 'PDF',
        estimatedTime: '1-2 minutes',
        icon: ExclamationTriangleIcon,
      },
      {
        id: 'audit-comprehensive',
        name: 'Comprehensive Audit Trail',
        description: 'Complete audit trail report with all user activities, system changes, and risk assessments',
        category: 'AUDIT',
        frequency: 'WEEKLY',
        format: 'EXCEL',
        estimatedTime: '5-7 minutes',
        lastGenerated: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        icon: ChartBarIcon,
      },
      {
        id: 'financial-summary',
        name: 'Financial Audit Summary',
        description: 'Summary of all financial transactions with compliance flags and risk indicators',
        category: 'FINANCIAL',
        frequency: 'MONTHLY',
        format: 'PDF',
        estimatedTime: '3-4 minutes',
        icon: DocumentArrowDownIcon,
      },
      {
        id: 'security-analysis',
        name: 'Security Analysis Report',
        description: 'Security incident analysis including failed access attempts, IP tracking, and threat assessment',
        category: 'SECURITY',
        frequency: 'WEEKLY',
        format: 'PDF',
        estimatedTime: '2-3 minutes',
        icon: EyeIcon,
      },
      {
        id: 'compliance-analytics',
        name: 'Compliance Analytics Dashboard',
        description: 'Advanced analytics report with trends, predictions, and compliance score analysis',
        category: 'ANALYTICS',
        frequency: 'MONTHLY',
        format: 'EXCEL',
        estimatedTime: '4-6 minutes',
        icon: PresentationChartLineIcon,
      },
      {
        id: 'transaction-analysis',
        name: 'Transaction Pattern Analysis',
        description: 'Detailed analysis of transaction patterns, anomalies, and risk assessment',
        category: 'ANALYTICS',
        frequency: 'ON_DEMAND',
        format: 'EXCEL',
        estimatedTime: '3-5 minutes',
        icon: TableCellsIcon,
      },
    ];
    setReportTemplates(templates);
  };

  const loadGeneratedReports = () => {
    const reports: GeneratedReport[] = [
      {
        id: '1',
        templateId: 'tra-monthly',
        name: 'TRA Monthly Compliance Report - June 2025',
        category: 'COMPLIANCE',
        authority: 'TRA',
        generatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        generatedBy: '<EMAIL>',
        status: 'COMPLETED',
        format: 'PDF',
        size: '2.4 MB',
        downloadUrl: '/reports/tra-monthly-june-2025.pdf',
      },
      {
        id: '2',
        templateId: 'audit-comprehensive',
        name: 'Comprehensive Audit Trail - Week 25',
        category: 'AUDIT',
        generatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        generatedBy: '<EMAIL>',
        status: 'COMPLETED',
        format: 'EXCEL',
        size: '5.7 MB',
        downloadUrl: '/reports/audit-trail-week-25.xlsx',
      },
      {
        id: '3',
        templateId: 'aml-suspicious',
        name: 'AML Suspicious Activity Report - June 2025',
        category: 'COMPLIANCE',
        authority: 'AML',
        generatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        generatedBy: '<EMAIL>',
        status: 'GENERATING',
        format: 'PDF',
        size: 'Calculating...',
      },
      {
        id: '4',
        templateId: 'security-analysis',
        name: 'Security Analysis Report - Week 25',
        category: 'SECURITY',
        generatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        generatedBy: '<EMAIL>',
        status: 'FAILED',
        format: 'PDF',
        size: 'N/A',
      },
    ];
    setGeneratedReports(reports);
  };

  const generateReport = async (template: ReportTemplate, customFilters?: any) => {
    // Add template to generating set
    setGeneratingTemplates(prev => new Set(prev).add(template.id));

    try {
      // Mock report generation
      const newReport: GeneratedReport = {
        id: Date.now().toString(),
        templateId: template.id,
        name: `${template.name} - ${new Date().toLocaleDateString()}`,
        category: template.category,
        authority: template.authority,
        generatedAt: new Date().toISOString(),
        generatedBy: '<EMAIL>',
        status: 'GENERATING',
        format: customFilters?.format || filters.format,
        size: 'Calculating...',
      };

      setGeneratedReports(prev => [newReport, ...prev]);
      setShowTemplateModal(false);

      // Simulate generation time
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Update status to completed
      setGeneratedReports(prev =>
        prev.map(report =>
          report.id === newReport.id
            ? {
                ...report,
                status: 'COMPLETED' as const,
                size: `${(Math.random() * 5 + 1).toFixed(1)} MB`,
                downloadUrl: `/reports/${template.id}-${Date.now()}.${(customFilters?.format || filters.format).toLowerCase()}`
              }
            : report
        )
      );

    } catch (error) {
      console.error('Failed to generate report:', error);
    } finally {
      // Remove template from generating set
      setGeneratingTemplates(prev => {
        const newSet = new Set(prev);
        newSet.delete(template.id);
        return newSet;
      });
    }
  };

  const generateCustomReport = async () => {
    if (!customReport.name.trim()) return;

    setLoading(true);
    try {
      const newReport: GeneratedReport = {
        id: Date.now().toString(),
        templateId: 'custom',
        name: customReport.name,
        category: 'CUSTOM',
        generatedAt: new Date().toISOString(),
        generatedBy: '<EMAIL>',
        status: 'GENERATING',
        format: customReport.format,
        size: 'Calculating...',
      };

      setGeneratedReports(prev => [newReport, ...prev]);
      setShowCustomReportModal(false);

      // Reset custom report form
      setCustomReport({
        name: '',
        description: '',
        category: 'CUSTOM',
        format: 'PDF',
        sections: [],
        dateRange: {
          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          endDate: new Date().toISOString().split('T')[0],
        },
        includeCharts: true,
        includeDetails: true,
      });

      // Simulate generation time
      await new Promise(resolve => setTimeout(resolve, 4000));

      // Update status to completed
      setGeneratedReports(prev =>
        prev.map(report =>
          report.id === newReport.id
            ? {
                ...report,
                status: 'COMPLETED' as const,
                size: `${(Math.random() * 8 + 2).toFixed(1)} MB`,
                downloadUrl: `/reports/custom-${Date.now()}.${customReport.format.toLowerCase()}`
              }
            : report
        )
      );

    } catch (error) {
      console.error('Failed to generate custom report:', error);
    } finally {
      setLoading(false);
    }
  };

  const downloadReport = (report: GeneratedReport) => {
    if (report.downloadUrl) {
      // Simulate download
      const link = document.createElement('a');
      link.href = report.downloadUrl;
      link.download = `${report.name}.${report.format.toLowerCase()}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Show success message in modal
      setConfirmationMessage(`Successfully downloading "${report.name}" (${report.format}, ${report.size})`);
      setConfirmationType('success');
      setShowConfirmationModal(true);
    }
  };

  const retryReport = async (report: GeneratedReport) => {
    setGeneratedReports(prev =>
      prev.map(r =>
        r.id === report.id
          ? { ...r, status: 'GENERATING' as const, size: 'Calculating...' }
          : r
      )
    );

    // Simulate retry
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Update to completed or failed randomly
    const success = Math.random() > 0.2; // 80% success rate
    setGeneratedReports(prev =>
      prev.map(r =>
        r.id === report.id
          ? {
              ...r,
              status: success ? 'COMPLETED' as const : 'FAILED' as const,
              size: success ? `${(Math.random() * 5 + 1).toFixed(1)} MB` : 'N/A',
              downloadUrl: success ? `/reports/retry-${Date.now()}.${report.format.toLowerCase()}` : undefined
            }
          : r
      )
    );
  };

  const createSchedule = async () => {
    if (!scheduleData.templateId || !scheduleData.name.trim()) return;

    try {
      // Mock schedule creation
      const recipientCount = scheduleData.recipients.filter(r => r.trim()).length;
      setConfirmationMessage(`Schedule "${scheduleData.name}" created successfully! Reports will be generated ${scheduleData.frequency.toLowerCase()} and sent to ${recipientCount} recipient${recipientCount !== 1 ? 's' : ''}.`);
      setConfirmationType('success');
      setShowConfirmationModal(true);

      setShowScheduleModal(false);
      setScheduleData({
        templateId: '',
        name: '',
        frequency: 'MONTHLY',
        dayOfMonth: 1,
        time: '09:00',
        recipients: [''],
        format: 'PDF',
        enabled: true,
      });
    } catch (error) {
      console.error('Failed to create schedule:', error);
      setConfirmationMessage('Failed to create schedule. Please try again.');
      setConfirmationType('error');
      setShowConfirmationModal(true);
    }
  };

  const viewTemplate = (template: ReportTemplate) => {
    setViewingTemplate(template);
    setShowTemplateModal(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'GENERATING': return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400';
      case 'FAILED': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      case 'SCHEDULED': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      default: return 'text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'COMPLIANCE': return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400';
      case 'AUDIT': return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20 dark:text-purple-400';
      case 'FINANCIAL': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'SECURITY': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      case 'ANALYTICS': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400';
      default: return 'text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500';
    }
  };

  const filteredTemplates = reportTemplates.filter(template => {
    if (filters.category !== 'ALL' && template.category !== filters.category) return false;
    if (filters.authority !== 'ALL' && template.authority !== filters.authority) return false;
    return true;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Advanced Reports & Analytics
          </h3>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Comprehensive audit and compliance reporting with advanced analytics
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowScheduleModal(true)}
            className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 text-sm transition-colors duration-200"
          >
            <CalendarIcon className="h-4 w-4 mr-2" />
            Schedule Report
          </button>
          <button
            onClick={() => setShowCustomReportModal(true)}
            className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm transition-colors duration-200"
          >
            <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
            Custom Report
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'templates', name: 'Report Templates', icon: DocumentTextIcon },
            { id: 'generated', name: 'Generated Reports', icon: CheckCircleIcon },
            { id: 'scheduled', name: 'Scheduled Reports', icon: ClockIcon },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <div className="flex items-center space-x-2">
                <tab.icon className="h-4 w-4" />
                <span>{tab.name}</span>
              </div>
            </button>
          ))}
        </nav>
      </div>

      {/* Report Templates Tab */}
      {activeTab === 'templates' && (
        <div className="space-y-6">
          {/* Filters */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">Report Filters</h4>
            <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Category
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => setFilters({ ...filters, category: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                >
                  <option value="ALL">All Categories</option>
                  <option value="COMPLIANCE">Compliance</option>
                  <option value="AUDIT">Audit</option>
                  <option value="FINANCIAL">Financial</option>
                  <option value="SECURITY">Security</option>
                  <option value="ANALYTICS">Analytics</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Authority
                </label>
                <select
                  value={filters.authority}
                  onChange={(e) => setFilters({ ...filters, authority: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                >
                  <option value="ALL">All Authorities</option>
                  <option value="TRA">TRA</option>
                  <option value="BOT">BOT</option>
                  <option value="AML">AML</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Format
                </label>
                <select
                  value={filters.format}
                  onChange={(e) => setFilters({ ...filters, format: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                >
                  <option value="PDF">PDF</option>
                  <option value="EXCEL">Excel</option>
                  <option value="CSV">CSV</option>
                  <option value="JSON">JSON</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Date Range
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm">
                  <option value="30">Last 30 days</option>
                  <option value="90">Last 90 days</option>
                  <option value="365">Last year</option>
                  <option value="custom">Custom range</option>
                </select>
              </div>
            </div>

            <div className="mt-4 flex items-center space-x-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.includeCharts}
                  onChange={(e) => setFilters({ ...filters, includeCharts: e.target.checked })}
                  className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Include Charts</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.includeDetails}
                  onChange={(e) => setFilters({ ...filters, includeDetails: e.target.checked })}
                  className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Include Details</span>
              </label>
            </div>
          </div>

          {/* Report Templates Grid */}
          <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map((template) => (
              <div key={template.id} className="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow duration-200">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <template.icon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {template.name}
                        </h4>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(template.category)}`}>
                            {template.category}
                          </span>
                          {template.authority && (
                            <span className="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                              {template.authority}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-4">
                    {template.description}
                  </p>

                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Frequency:</span>
                      <span className="text-gray-900 dark:text-white">{template.frequency}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Format:</span>
                      <span className="text-gray-900 dark:text-white">{template.format}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Est. Time:</span>
                      <span className="text-gray-900 dark:text-white">{template.estimatedTime}</span>
                    </div>
                    {template.lastGenerated && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Last Generated:</span>
                        <span className="text-gray-900 dark:text-white">
                          {new Date(template.lastGenerated).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="mt-6 flex space-x-3">
                    <button
                      onClick={() => generateReport(template)}
                      disabled={generatingTemplates.has(template.id)}
                      className="flex-1 flex items-center justify-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 text-sm transition-colors duration-200 cursor-pointer"
                    >
                      {generatingTemplates.has(template.id) ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Generating...
                        </>
                      ) : (
                        <>
                          <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                          Generate
                        </>
                      )}
                    </button>
                    <button
                      onClick={() => viewTemplate(template)}
                      className="flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm transition-colors duration-200 cursor-pointer"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Generated Reports Tab */}
      {activeTab === 'generated' && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h4 className="text-md font-medium text-gray-900 dark:text-white">Generated Reports</h4>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
              Recently generated reports and their download status
            </p>
          </div>
          <div className="p-6">
            {generatedReports.length > 0 ? (
              <div className="space-y-4 bg-white dark:bg-gray-800">
                {generatedReports.map((report) => (
                  <div key={report.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <DocumentArrowDownIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <h5 className="text-sm font-medium text-gray-900 dark:text-white">{report.name}</h5>
                        <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <span>Generated by {report.generatedBy}</span>
                          <span>{new Date(report.generatedAt).toLocaleString()}</span>
                          <span>{report.format} • {report.size}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(report.status)}`}>
                        {report.status}
                      </span>
                      {report.status === 'COMPLETED' && report.downloadUrl && (
                        <button
                          onClick={() => downloadReport(report)}
                          className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 text-sm transition-colors duration-200 cursor-pointer"
                        >
                          <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                          Download
                        </button>
                      )}
                      {report.status === 'FAILED' && (
                        <button
                          onClick={() => retryReport(report)}
                          className="flex items-center px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 text-sm transition-colors duration-200 cursor-pointer"
                        >
                          <ArrowPathIcon className="h-4 w-4 mr-2" />
                          Retry
                        </button>
                      )}
                      {report.status === 'GENERATING' && (
                        <div className="flex items-center px-3 py-2 bg-blue-100 dark:bg-blue-900/20 text-blue-800 rounded-md text-sm">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                          Generating...
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <DocumentArrowDownIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No Reports Generated</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Generate your first report from the templates tab.
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Scheduled Reports Tab */}
      {activeTab === 'scheduled' && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <div className="text-center py-8">
            <ClockIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">Scheduled Reports</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
              Schedule automatic report generation for regular compliance reporting.
            </p>
            <div className="mt-6">
              <button
                onClick={() => setShowScheduleModal(true)}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm transition-colors duration-200 mx-auto cursor-pointer"
              >
                <CalendarIcon className="h-4 w-4 mr-2" />
                Create Schedule
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Template View Modal */}
      {showTemplateModal && viewingTemplate && (
        <div className="fixed inset-0 flex items-center justify-center p-4 z-50" style={{ backgroundColor: 'rgba(0,0,0,0.6)' }}>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {viewingTemplate.name}
              </h3>
              <button
                onClick={() => {
                  setShowTemplateModal(false);
                  setViewingTemplate(null);
                }}
                className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6">
              <div className="space-y-4 bg-white dark:bg-gray-800">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Description</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{viewingTemplate.description}</p>
                </div>

                <div className="grid bg-white dark:bg-gray-800 grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Category</h4>
                    <span className={`px-3 py-1 text-sm font-semibold rounded-full ${getCategoryColor(viewingTemplate.category)}`}>
                      {viewingTemplate.category}
                    </span>
                  </div>

                  {viewingTemplate.authority && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Authority</h4>
                      <span className="px-3 py-1 text-sm font-semibold rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                        {viewingTemplate.authority}
                      </span>
                    </div>
                  )}
                </div>

                <div className="grid bg-white dark:bg-gray-800 grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Frequency</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{viewingTemplate.frequency}</p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Estimated Time</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{viewingTemplate.estimatedTime}</p>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Report Sections</h4>
                  <div className="space-y-2">
                    {viewingTemplate.authority === 'TRA' && (
                      <>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          VAT Summary and Analysis
                        </div>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          Withholding Tax Calculations
                        </div>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          EFD Transaction Records
                        </div>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          Compliance Score Assessment
                        </div>
                      </>
                    )}
                    {viewingTemplate.authority === 'BOT' && (
                      <>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          Forex Transaction Analysis
                        </div>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          Large Cash Movement Tracking
                        </div>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          Cross-border Transfer Reports
                        </div>
                      </>
                    )}
                    {viewingTemplate.authority === 'AML' && (
                      <>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          Suspicious Transaction Detection
                        </div>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          PEP Customer Analysis
                        </div>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          High-Risk Activity Assessment
                        </div>
                      </>
                    )}
                    {!viewingTemplate.authority && (
                      <>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          Comprehensive Data Analysis
                        </div>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          Statistical Summaries
                        </div>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          Trend Analysis and Insights
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              <div className="mt-6 flex space-x-3">
                <button
                  onClick={() => generateReport(viewingTemplate)}
                  disabled={generatingTemplates.has(viewingTemplate.id)}
                  className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 transition-colors duration-200 cursor-pointer"
                >
                  {generatingTemplates.has(viewingTemplate.id) ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Generating...
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-4 w-4 mr-2" />
                      Generate Report
                    </>
                  )}
                </button>
                <button
                  onClick={() => {
                    setShowTemplateModal(false);
                    setViewingTemplate(null);
                  }}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 cursor-pointer"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Custom Report Modal */}
      {showCustomReportModal && (
        <div className="fixed inset-0 flex items-center justify-center p-4 z-50" style={{ backgroundColor: 'rgba(0,0,0,0.6)' }}>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Create Custom Report
              </h3>
              <button
                onClick={() => setShowCustomReportModal(false)}
                className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6">
              <div className="space-y-6">
                <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Report Name *
                    </label>
                    <input
                      type="text"
                      value={customReport.name}
                      onChange={(e) => setCustomReport({ ...customReport, name: e.target.value })}
                      placeholder="Enter report name"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Format
                    </label>
                    <select
                      value={customReport.format}
                      onChange={(e) => setCustomReport({ ...customReport, format: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="PDF">PDF</option>
                      <option value="EXCEL">Excel</option>
                      <option value="CSV">CSV</option>
                      <option value="JSON">JSON</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Description
                  </label>
                  <textarea
                    value={customReport.description}
                    onChange={(e) => setCustomReport({ ...customReport, description: e.target.value })}
                    placeholder="Describe what this report should include"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Report Sections
                  </label>
                  <div className="grid bg-white dark:bg-gray-800 grid-cols-2 gap-2">
                    {[
                      'Transaction Summary',
                      'Compliance Analysis',
                      'Risk Assessment',
                      'User Activity',
                      'Financial Metrics',
                      'Security Events',
                      'Audit Trail',
                      'Performance Data'
                    ].map((section) => (
                      <label key={section} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={customReport.sections.includes(section)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setCustomReport({
                                ...customReport,
                                sections: [...customReport.sections, section]
                              });
                            } else {
                              setCustomReport({
                                ...customReport,
                                sections: customReport.sections.filter(s => s !== section)
                              });
                            }
                          }}
                          className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{section}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Start Date
                    </label>
                    <input
                      type="date"
                      value={customReport.dateRange.startDate}
                      onChange={(e) => setCustomReport({
                        ...customReport,
                        dateRange: { ...customReport.dateRange, startDate: e.target.value }
                      })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      End Date
                    </label>
                    <input
                      type="date"
                      value={customReport.dateRange.endDate}
                      onChange={(e) => setCustomReport({
                        ...customReport,
                        dateRange: { ...customReport.dateRange, endDate: e.target.value }
                      })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-6">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={customReport.includeCharts}
                      onChange={(e) => setCustomReport({ ...customReport, includeCharts: e.target.checked })}
                      className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Include Charts & Graphs</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={customReport.includeDetails}
                      onChange={(e) => setCustomReport({ ...customReport, includeDetails: e.target.checked })}
                      className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Include Detailed Data</span>
                  </label>
                </div>
              </div>

              <div className="mt-6 flex space-x-3">
                <button
                  onClick={generateCustomReport}
                  disabled={loading || !customReport.name.trim()}
                  className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 transition-colors duration-200 cursor-pointer"
                >
                  <PlayIcon className="h-4 w-4 mr-2" />
                  {loading ? 'Generating...' : 'Generate Custom Report'}
                </button>
                <button
                  onClick={() => setShowCustomReportModal(false)}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 cursor-pointer"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Schedule Report Modal */}
      {showScheduleModal && (
        <div className="fixed inset-0 flex items-center justify-center p-4 z-50" style={{ backgroundColor: 'rgba(0,0,0,0.6)' }}>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Schedule Report Generation
              </h3>
              <button
                onClick={() => setShowScheduleModal(false)}
                className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6">
              <div className="space-y-6">
                <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Report Template *
                    </label>
                    <select
                      value={scheduleData.templateId}
                      onChange={(e) => setScheduleData({ ...scheduleData, templateId: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Select a template</option>
                      {reportTemplates.map((template) => (
                        <option key={template.id} value={template.id}>
                          {template.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Schedule Name *
                    </label>
                    <input
                      type="text"
                      value={scheduleData.name}
                      onChange={(e) => setScheduleData({ ...scheduleData, name: e.target.value })}
                      placeholder="Enter schedule name"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>

                <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Frequency
                    </label>
                    <select
                      value={scheduleData.frequency}
                      onChange={(e) => setScheduleData({ ...scheduleData, frequency: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="DAILY">Daily</option>
                      <option value="WEEKLY">Weekly</option>
                      <option value="MONTHLY">Monthly</option>
                      <option value="QUARTERLY">Quarterly</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Day of Month
                    </label>
                    <select
                      value={scheduleData.dayOfMonth}
                      onChange={(e) => setScheduleData({ ...scheduleData, dayOfMonth: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      {Array.from({ length: 28 }, (_, i) => i + 1).map(day => (
                        <option key={day} value={day}>{day}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Time
                    </label>
                    <input
                      type="time"
                      value={scheduleData.time}
                      onChange={(e) => setScheduleData({ ...scheduleData, time: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email Recipients
                  </label>
                  <div className="space-y-2">
                    {scheduleData.recipients.map((recipient, index) => (
                      <div key={index} className="flex space-x-2">
                        <input
                          type="email"
                          value={recipient}
                          onChange={(e) => {
                            const newRecipients = [...scheduleData.recipients];
                            newRecipients[index] = e.target.value;
                            setScheduleData({ ...scheduleData, recipients: newRecipients });
                          }}
                          placeholder="Enter email address"
                          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                        {scheduleData.recipients.length > 1 && (
                          <button
                            onClick={() => {
                              const newRecipients = scheduleData.recipients.filter((_, i) => i !== index);
                              setScheduleData({ ...scheduleData, recipients: newRecipients });
                            }}
                            className="px-3 py-2 text-red-600 dark:text-red-400 hover:text-red-800 cursor-pointer"
                          >
                            <XMarkIcon className="h-5 w-5" />
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      onClick={() => setScheduleData({
                        ...scheduleData,
                        recipients: [...scheduleData.recipients, '']
                      })}
                      className="flex items-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 cursor-pointer"
                    >
                      <PlusIcon className="h-4 w-4 mr-1" />
                      Add Recipient
                    </button>
                  </div>
                </div>

                <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Format
                    </label>
                    <select
                      value={scheduleData.format}
                      onChange={(e) => setScheduleData({ ...scheduleData, format: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="PDF">PDF</option>
                      <option value="EXCEL">Excel</option>
                      <option value="CSV">CSV</option>
                      <option value="JSON">JSON</option>
                    </select>
                  </div>

                  <div className="flex items-center">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={scheduleData.enabled}
                        onChange={(e) => setScheduleData({ ...scheduleData, enabled: e.target.checked })}
                        className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Enable Schedule</span>
                    </label>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex space-x-3">
                <button
                  onClick={createSchedule}
                  disabled={!scheduleData.templateId || !scheduleData.name.trim()}
                  className="flex-1 flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 transition-colors duration-200 cursor-pointer"
                >
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  Create Schedule
                </button>
                <button
                  onClick={() => setShowScheduleModal(false)}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 cursor-pointer"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmationModal && (
        <div className="fixed inset-0 flex items-center justify-center p-4 z-50" style={{ backgroundColor: 'rgba(0,0,0,0.6)' }}>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  {confirmationType === 'success' && (
                    <CheckCircleIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
                  )}
                  {confirmationType === 'error' && (
                    <XMarkIcon className="h-8 w-8 text-red-600 dark:text-red-400" />
                  )}
                  {confirmationType === 'info' && (
                    <InformationCircleIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                  )}
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {confirmationType === 'success' && 'Success'}
                    {confirmationType === 'error' && 'Error'}
                    {confirmationType === 'info' && 'Information'}
                  </h3>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  {confirmationMessage}
                </p>
              </div>

              <div className="flex justify-end">
                <button
                  onClick={() => setShowConfirmationModal(false)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 cursor-pointer"
                >
                  OK
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
