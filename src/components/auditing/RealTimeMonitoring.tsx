import { useState, useEffect, useRef } from "react";
import {
  BellI<PERSON>,
  EyeIcon,
  ClockIcon,
  UserIcon,
  ComputerDesktopIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlayIcon,
  PauseIcon,
  ArrowPathIcon,
  ChartBarIcon,
  CpuChipIcon,
  ServerIcon,
  WifiIcon,
  LockClosedIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  UsersIcon,
  GlobeAltIcon,
  FireIcon,
  BoltIcon,
  SignalIcon,
} from "@heroicons/react/24/outline";

interface ActivityEvent {
  id: string;
  timestamp: string;
  type: 'USER_LOGIN' | 'TRANSACTION' | 'SYSTEM' | 'SECURITY' | 'COMPLIANCE' | 'ERROR' | 'WARNING';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  user?: string;
  action: string;
  details: string;
  ipAddress?: string;
  location?: string;
  status: 'SUCCESS' | 'FAILED' | 'PENDING' | 'WARNING';
}

interface SystemMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  threshold: number;
  status: 'NORMAL' | 'WARNING' | 'CRITICAL';
  trend: 'UP' | 'DOWN' | 'STABLE';
  icon: any;
}

interface Alert {
  id: string;
  timestamp: string;
  type: 'SECURITY' | 'PERFORMANCE' | 'COMPLIANCE' | 'SYSTEM' | 'USER';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  message: string;
  acknowledged: boolean;
  source: string;
}

export default function RealTimeMonitoring() {
  const [isMonitoring, setIsMonitoring] = useState(true);
  const [activityEvents, setActivityEvents] = useState<ActivityEvent[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetric[]>([]);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<'ALL' | 'USER_LOGIN' | 'TRANSACTION' | 'SYSTEM' | 'SECURITY' | 'COMPLIANCE'>('ALL');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5); // seconds
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    loadInitialData();
    if (autoRefresh && isMonitoring) {
      startAutoRefresh();
    }
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoRefresh, isMonitoring, refreshInterval]);

  const loadInitialData = () => {
    loadSystemMetrics();
    loadActivityEvents();
    loadAlerts();
  };

  const startAutoRefresh = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    intervalRef.current = setInterval(() => {
      if (isMonitoring) {
        generateNewActivity();
        updateSystemMetrics();
        checkForAlerts();
      }
    }, refreshInterval * 1000);
  };

  const loadSystemMetrics = () => {
    const metrics: SystemMetric[] = [
      {
        id: 'cpu',
        name: 'CPU Usage',
        value: 45,
        unit: '%',
        threshold: 80,
        status: 'NORMAL',
        trend: 'STABLE',
        icon: CpuChipIcon,
      },
      {
        id: 'memory',
        name: 'Memory Usage',
        value: 67,
        unit: '%',
        threshold: 85,
        status: 'NORMAL',
        trend: 'UP',
        icon: ServerIcon,
      },
      {
        id: 'disk',
        name: 'Disk Usage',
        value: 34,
        unit: '%',
        threshold: 90,
        status: 'NORMAL',
        trend: 'STABLE',
        icon: ComputerDesktopIcon,
      },
      {
        id: 'network',
        name: 'Network I/O',
        value: 23,
        unit: 'MB/s',
        threshold: 100,
        status: 'NORMAL',
        trend: 'DOWN',
        icon: WifiIcon,
      },
      {
        id: 'active_users',
        name: 'Active Users',
        value: 12,
        unit: 'users',
        threshold: 50,
        status: 'NORMAL',
        trend: 'UP',
        icon: UsersIcon,
      },
      {
        id: 'transactions',
        name: 'Transactions/min',
        value: 34,
        unit: 'tps',
        threshold: 100,
        status: 'NORMAL',
        trend: 'STABLE',
        icon: CurrencyDollarIcon,
      },
    ];
    setSystemMetrics(metrics);
  };

  const loadActivityEvents = () => {
    const events: ActivityEvent[] = [
      {
        id: '1',
        timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
        type: 'USER_LOGIN',
        severity: 'LOW',
        user: '<EMAIL>',
        action: 'User Login',
        details: 'Successful login from desktop application',
        ipAddress: '************',
        location: 'Dar es Salaam, Tanzania',
        status: 'SUCCESS',
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        type: 'TRANSACTION',
        severity: 'MEDIUM',
        user: '<EMAIL>',
        action: 'Large Transaction',
        details: 'Transaction of 15M TZS processed',
        status: 'SUCCESS',
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 8 * 60 * 1000).toISOString(),
        type: 'SECURITY',
        severity: 'HIGH',
        action: 'Failed Login Attempt',
        details: 'Multiple failed login attempts detected',
        ipAddress: '************',
        location: 'Unknown',
        status: 'FAILED',
      },
      {
        id: '4',
        timestamp: new Date(Date.now() - 12 * 60 * 1000).toISOString(),
        type: 'COMPLIANCE',
        severity: 'MEDIUM',
        action: 'TRA Report Generated',
        details: 'Monthly compliance report generated successfully',
        user: '<EMAIL>',
        status: 'SUCCESS',
      },
      {
        id: '5',
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        type: 'SYSTEM',
        severity: 'LOW',
        action: 'Database Backup',
        details: 'Automated database backup completed',
        status: 'SUCCESS',
      },
    ];
    setActivityEvents(events);
  };

  const loadAlerts = () => {
    const alertsData: Alert[] = [
      {
        id: '1',
        timestamp: new Date(Date.now() - 3 * 60 * 1000).toISOString(),
        type: 'SECURITY',
        severity: 'HIGH',
        title: 'Suspicious Login Activity',
        message: 'Multiple failed login attempts from IP ************',
        acknowledged: false,
        source: 'Security Monitor',
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
        type: 'COMPLIANCE',
        severity: 'MEDIUM',
        title: 'Forex Threshold Exceeded',
        message: 'Monthly forex transaction limit approaching (85% of threshold)',
        acknowledged: false,
        source: 'Compliance Monitor',
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 20 * 60 * 1000).toISOString(),
        type: 'PERFORMANCE',
        severity: 'LOW',
        title: 'Database Query Optimization',
        message: 'Slow query detected in transaction processing',
        acknowledged: true,
        source: 'Performance Monitor',
      },
    ];
    setAlerts(alertsData);
  };

  const generateNewActivity = () => {
    const activityTypes = ['USER_LOGIN', 'TRANSACTION', 'SYSTEM', 'SECURITY', 'COMPLIANCE'] as const;
    const severities = ['LOW', 'MEDIUM', 'HIGH'] as const;
    const statuses = ['SUCCESS', 'FAILED', 'WARNING'] as const;
    
    const randomType = activityTypes[Math.floor(Math.random() * activityTypes.length)];
    const randomSeverity = severities[Math.floor(Math.random() * severities.length)];
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
    
    const newEvent: ActivityEvent = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      type: randomType,
      severity: randomSeverity,
      action: getRandomAction(randomType),
      details: getRandomDetails(randomType),
      user: randomType === 'USER_LOGIN' ? '<EMAIL>' : undefined,
      ipAddress: randomType === 'USER_LOGIN' || randomType === 'SECURITY' ? '192.168.1.' + Math.floor(Math.random() * 255) : undefined,
      status: randomStatus,
    };

    setActivityEvents(prev => [newEvent, ...prev.slice(0, 49)]); // Keep last 50 events
  };

  const getRandomAction = (type: string): string => {
    const actions = {
      USER_LOGIN: ['User Login', 'User Logout', 'Password Change', 'Profile Update'],
      TRANSACTION: ['Transaction Created', 'Transaction Approved', 'Large Transaction', 'Transaction Cancelled'],
      SYSTEM: ['Database Backup', 'System Update', 'Cache Clear', 'Log Rotation'],
      SECURITY: ['Failed Login', 'Permission Change', 'Security Scan', 'Access Denied'],
      COMPLIANCE: ['Report Generated', 'Threshold Check', 'Audit Log', 'Compliance Scan'],
    };
    const typeActions = actions[type as keyof typeof actions] || ['System Activity'];
    return typeActions[Math.floor(Math.random() * typeActions.length)];
  };

  const getRandomDetails = (type: string): string => {
    const details = {
      USER_LOGIN: ['Successful login from web browser', 'Mobile app authentication', 'Two-factor authentication completed'],
      TRANSACTION: ['Payment processed successfully', 'Invoice generated', 'Forex transaction recorded'],
      SYSTEM: ['Automated maintenance completed', 'System health check passed', 'Performance optimization applied'],
      SECURITY: ['Unauthorized access attempt blocked', 'Security policy updated', 'Firewall rule applied'],
      COMPLIANCE: ['Regulatory report submitted', 'Compliance check completed', 'Audit trail updated'],
    };
    const typeDetails = details[type as keyof typeof details] || ['System activity recorded'];
    return typeDetails[Math.floor(Math.random() * typeDetails.length)];
  };

  const updateSystemMetrics = () => {
    setSystemMetrics(prev => prev.map(metric => ({
      ...metric,
      value: Math.max(0, Math.min(100, metric.value + (Math.random() - 0.5) * 10)),
      trend: Math.random() > 0.5 ? 'UP' : Math.random() > 0.5 ? 'DOWN' : 'STABLE',
      status: metric.value > metric.threshold ? 'CRITICAL' : metric.value > metric.threshold * 0.8 ? 'WARNING' : 'NORMAL',
    })));
  };

  const checkForAlerts = () => {
    // Randomly generate new alerts
    if (Math.random() > 0.9) { // 10% chance of new alert
      const alertTypes = ['SECURITY', 'PERFORMANCE', 'COMPLIANCE', 'SYSTEM'] as const;
      const severities = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'] as const;
      
      const newAlert: Alert = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        type: alertTypes[Math.floor(Math.random() * alertTypes.length)],
        severity: severities[Math.floor(Math.random() * severities.length)],
        title: 'New Alert Detected',
        message: 'Real-time monitoring detected an event requiring attention',
        acknowledged: false,
        source: 'Real-time Monitor',
      };
      
      setAlerts(prev => [newAlert, ...prev.slice(0, 19)]); // Keep last 20 alerts
    }
  };

  const acknowledgeAlert = (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
  };

  const toggleMonitoring = () => {
    setIsMonitoring(!isMonitoring);
    if (!isMonitoring && autoRefresh) {
      startAutoRefresh();
    }
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'USER_LOGIN': return UserIcon;
      case 'TRANSACTION': return CurrencyDollarIcon;
      case 'SYSTEM': return ComputerDesktopIcon;
      case 'SECURITY': return ShieldCheckIcon;
      case 'COMPLIANCE': return DocumentTextIcon;
      default: return BellIcon;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUCCESS': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'FAILED': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      case 'WARNING': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'PENDING': return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400';
      default: return 'text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      case 'HIGH': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400';
      case 'MEDIUM': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'LOW': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      default: return 'text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500';
    }
  };

  const getMetricStatusColor = (status: string) => {
    switch (status) {
      case 'NORMAL': return 'text-green-600 dark:text-green-400';
      case 'WARNING': return 'text-yellow-600 dark:text-yellow-400';
      case 'CRITICAL': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500';
    }
  };

  const filteredEvents = selectedFilter === 'ALL' 
    ? activityEvents 
    : activityEvents.filter(event => event.type === selectedFilter);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Real-time Monitoring & Alerts
          </h3>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Live activity monitoring with real-time alerts and system metrics
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Auto-refresh:</span>
            <select
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(Number(e.target.value))}
              className="px-2 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value={1}>1s</option>
              <option value={5}>5s</option>
              <option value={10}>10s</option>
              <option value={30}>30s</option>
            </select>
          </div>
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`flex items-center px-3 py-2 rounded-md text-sm transition-colors duration-200 cursor-pointer ${
              autoRefresh 
                ? 'bg-green-600 text-white hover:bg-green-700' 
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            {autoRefresh ? <PauseIcon className="h-4 w-4 mr-2" /> : <PlayIcon className="h-4 w-4 mr-2" />}
            {autoRefresh ? 'Pause' : 'Resume'}
          </button>
          <button
            onClick={toggleMonitoring}
            className={`flex items-center px-3 py-2 rounded-md text-sm transition-colors duration-200 cursor-pointer ${
              isMonitoring 
                ? 'bg-blue-600 text-white hover:bg-blue-700' 
                : 'bg-red-600 text-white hover:bg-red-700'
            }`}
          >
            {isMonitoring ? <EyeIcon className="h-4 w-4 mr-2" /> : <XCircleIcon className="h-4 w-4 mr-2" />}
            {isMonitoring ? 'Monitoring' : 'Stopped'}
          </button>
        </div>
      </div>

      {/* System Metrics */}
      <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {systemMetrics.map((metric) => {
          const IconComponent = metric.icon;
          return (
            <div key={metric.id} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <IconComponent className={`h-8 w-8 ${getMetricStatusColor(metric.status)}`} />
                  </div>
                  <div className="ml-3">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      {metric.name}
                    </h4>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(metric.status)}`}>
                        {metric.status}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        {metric.trend === 'UP' && '↗️'}
                        {metric.trend === 'DOWN' && '↘️'}
                        {metric.trend === 'STABLE' && '➡️'}
                        {metric.trend}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-2xl font-bold text-gray-900 dark:text-white">
                    {metric.value.toFixed(1)}{metric.unit}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    / {metric.threshold}{metric.unit}
                  </span>
                </div>

                <div className="w-full bg-gray-200 dark:bg-gray-800 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      metric.status === 'CRITICAL' ? 'bg-red-600' :
                      metric.status === 'WARNING' ? 'bg-yellow-600' : 'bg-green-600'
                    }`}
                    style={{ width: `${Math.min(100, (metric.value / metric.threshold) * 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Alerts Section */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Active Alerts</h3>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                {alerts.filter(a => !a.acknowledged).length} unacknowledged
              </span>
              <div className={`w-3 h-3 rounded-full ${isMonitoring ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
            </div>
          </div>
        </div>
        <div className="p-6">
          {alerts.length > 0 ? (
            <div className="space-y-4 bg-white dark:bg-gray-800">
              {alerts.slice(0, 5).map((alert) => (
                <div key={alert.id} className={`border-l-4 p-4 rounded-r-lg ${
                  alert.severity === 'CRITICAL' ? 'border-red-500 bg-red-50 dark:bg-red-900/20' :
                  alert.severity === 'HIGH' ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20' :
                  alert.severity === 'MEDIUM' ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20' :
                  'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                } ${alert.acknowledged ? 'opacity-60' : ''}`}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        {alert.severity === 'CRITICAL' && <FireIcon className="h-5 w-5 text-red-600 dark:text-red-400" />}
                        {alert.severity === 'HIGH' && <ExclamationTriangleIcon className="h-5 w-5 text-orange-600" />}
                        {alert.severity === 'MEDIUM' && <BellIcon className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />}
                        {alert.severity === 'LOW' && <CheckCircleIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                            {alert.title}
                          </h4>
                          <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(alert.severity)}`}>
                            {alert.severity}
                          </span>
                          {alert.acknowledged && (
                            <span className="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                              ACKNOWLEDGED
                            </span>
                          )}
                        </div>
                        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          {alert.message}
                        </p>
                        <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                          <span>{new Date(alert.timestamp).toLocaleString()}</span>
                          <span>Source: {alert.source}</span>
                          <span>Type: {alert.type}</span>
                        </div>
                      </div>
                    </div>
                    {!alert.acknowledged && (
                      <button
                        onClick={() => acknowledgeAlert(alert.id)}
                        className="ml-4 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 cursor-pointer"
                      >
                        Acknowledge
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CheckCircleIcon className="mx-auto h-12 w-12 text-green-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No Active Alerts</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                All systems are operating normally.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Activity Feed */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Live Activity Feed</h3>
            <div className="flex items-center space-x-3">
              <select
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value as any)}
                className="px-3 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="ALL">All Activities</option>
                <option value="USER_LOGIN">User Login</option>
                <option value="TRANSACTION">Transactions</option>
                <option value="SYSTEM">System</option>
                <option value="SECURITY">Security</option>
                <option value="COMPLIANCE">Compliance</option>
              </select>
              <button
                onClick={loadInitialData}
                className="flex items-center px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200 cursor-pointer"
              >
                <ArrowPathIcon className="h-4 w-4 mr-1" />
                Refresh
              </button>
            </div>
          </div>
        </div>
        <div className="p-6">
          {filteredEvents.length > 0 ? (
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {filteredEvents.map((event) => {
                const IconComponent = getEventIcon(event.type);
                return (
                  <div key={event.id} className="flex items-start space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                    <div className="flex-shrink-0">
                      <IconComponent className="h-5 w-5 text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {event.action}
                        </h4>
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(event.status)}`}>
                          {event.status}
                        </span>
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(event.severity)}`}>
                          {event.severity}
                        </span>
                      </div>
                      <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        {event.details}
                      </p>
                      <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        <span>{new Date(event.timestamp).toLocaleString()}</span>
                        {event.user && <span>User: {event.user}</span>}
                        {event.ipAddress && <span>IP: {event.ipAddress}</span>}
                        {event.location && <span>Location: {event.location}</span>}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <ClockIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No Activity</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                No activities match the selected filter.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
