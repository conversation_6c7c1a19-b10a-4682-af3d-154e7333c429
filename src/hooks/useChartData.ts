import { useState, useEffect } from "react";
import { dashboardService } from "../services/dashboardService";
import type {
  HistoricalDataPoint,
  ExpenseBreakdown,
} from "../services/dashboardService";
import { useCompany } from "../contexts/CompanyContext";

export function useChartData(period: "month" | "quarter" | "year" = "month") {
  const { currentCompany } = useCompany();
  const [revenueTrend, setRevenueTrend] = useState<HistoricalDataPoint[]>([]);
  const [expenseTrend, setExpenseTrend] = useState<HistoricalDataPoint[]>([]);
  const [cashFlowTrend, setCashFlowTrend] = useState<HistoricalDataPoint[]>([]);
  const [expenseBreakdown, setExpenseBreakdown] = useState<ExpenseBreakdown[]>([]);
  const [revenueExpenseComparison, setRevenueExpenseComparison] = useState<{
    revenue: HistoricalDataPoint[];
    expenses: HistoricalDataPoint[];
  }>({ revenue: [], expenses: [] });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchChartData = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch all chart data in parallel
      const [
        revenueData,
        expenseData,
        cashFlowData,
        expenseBreakdownData,
        comparisonData,
      ] = await Promise.all([
        dashboardService.getRevenueTrend(currentCompany.id),
        dashboardService.getExpenseTrend(currentCompany.id),
        dashboardService.getCashFlowTrend(currentCompany.id),
        dashboardService.getExpenseBreakdown(currentCompany.id, period),
        dashboardService.getRevenueExpenseComparison(currentCompany.id),
      ]);

      setRevenueTrend(revenueData);
      setExpenseTrend(expenseData);
      setCashFlowTrend(cashFlowData);
      setExpenseBreakdown(expenseBreakdownData);
      setRevenueExpenseComparison(comparisonData);
    } catch (err) {
      console.error("Failed to fetch chart data:", err);
      setError("Failed to load chart data");
    } finally {
      setLoading(false);
    }
  };

  const refresh = () => {
    fetchChartData();
  };

  useEffect(() => {
    fetchChartData();
  }, [currentCompany, period]);

  return {
    revenueTrend,
    expenseTrend,
    cashFlowTrend,
    expenseBreakdown,
    revenueExpenseComparison,
    loading,
    error,
    refresh,
  };
}

export default useChartData;
