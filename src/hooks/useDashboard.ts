import { useState, useEffect } from "react";
import { dashboardService } from "../services/dashboardService";
import type {
  DashboardData,
  DashboardStats,
  RecentActivity,
} from "../services/dashboardService";
import { useCompany } from "../contexts/CompanyContext";

export function useDashboard(period: "month" | "quarter" | "year" = "month") {
  const { currentCompany } = useCompany();
  const [data, setData] = useState<DashboardData | null>(null);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      setError(null);

      const dashboardData = await dashboardService.getDashboardData(
        currentCompany.id
      );
      setData(dashboardData);
      setStats(dashboardData.stats);
      setRecentActivity(dashboardData.recentActivity);
    } catch (err) {
      console.error("Failed to fetch dashboard data:", err);
      setError("Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };

  const fetchFinancialSummary = async () => {
    if (!currentCompany) return;

    try {
      const financialStats = await dashboardService.getFinancialSummary(
        currentCompany.id,
        period
      );
      setStats(financialStats);
    } catch (err) {
      console.error("Failed to fetch financial summary:", err);
      setError("Failed to load financial summary");
    }
  };

  const fetchRecentActivity = async (limit: number = 10) => {
    if (!currentCompany) return;

    try {
      const activity = await dashboardService.getRecentActivity(
        currentCompany.id,
        limit
      );
      setRecentActivity(activity);
    } catch (err) {
      console.error("Failed to fetch recent activity:", err);
      setError("Failed to load recent activity");
    }
  };

  const refresh = () => {
    fetchDashboardData();
  };

  useEffect(() => {
    fetchDashboardData();
  }, [currentCompany, period]);

  return {
    data,
    stats,
    recentActivity,
    loading,
    error,
    refresh,
    fetchFinancialSummary,
    fetchRecentActivity,
  };
}

export default useDashboard;
