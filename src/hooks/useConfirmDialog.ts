import { useState, useCallback } from "react";

interface ConfirmDialogOptions {
  title: string;
  message: string;
  confirmLabel?: string;
  cancelLabel?: string;
  type?: "danger" | "warning" | "info" | "success";
  onConfirm?: () => void | Promise<void>;
}

interface SimpleConfirmOptions {
  title: string;
  message: string;
  confirmLabel?: string;
  cancelLabel?: string;
  type?: "danger" | "warning" | "info" | "success";
}

interface ConfirmDialogState {
  isOpen: boolean;
  loading: boolean;
  title: string;
  message: string;
  confirmLabel: string;
  cancelLabel: string;
  type: "danger" | "warning" | "info" | "success";
  onConfirm: () => void | Promise<void>;
  onCancel?: () => void;
}

export function useConfirmDialog() {
  const [state, setState] = useState<ConfirmDialogState>({
    isOpen: false,
    loading: false,
    title: "",
    message: "",
    confirmLabel: "Confirm",
    cancelLabel: "Cancel",
    type: "warning",
    onConfirm: () => {},
  });

  const showConfirmDialog = useCallback((options: ConfirmDialogOptions) => {
    setState({
      isOpen: true,
      loading: false,
      title: options.title,
      message: options.message,
      confirmLabel: options.confirmLabel || "Confirm",
      cancelLabel: options.cancelLabel || "Cancel",
      type: options.type || "warning",
      onConfirm: options.onConfirm || (() => {}),
    });
  }, []);

  const hideConfirmDialog = useCallback(() => {
    setState((prev) => {
      if (prev.onCancel) {
        prev.onCancel();
      }
      return { ...prev, isOpen: false, loading: false };
    });
  }, []);

  const handleConfirm = useCallback(async () => {
    if (state.loading) return;

    setState((prev) => ({ ...prev, loading: true }));
    try {
      await state.onConfirm();
      setState((prev) => ({ ...prev, isOpen: false, loading: false }));
    } catch (error) {
      setState((prev) => ({ ...prev, loading: false }));
      throw error;
    }
  }, [state.onConfirm, state.loading]);

  const confirmDialog = {
    isOpen: state.isOpen,
    title: state.title,
    message: state.message,
    confirmText: state.confirmLabel,
    cancelText: state.cancelLabel,
    type: state.type,
    loading: state.loading,
    onConfirm: handleConfirm,
    onClose: hideConfirmDialog,
  };

  // Simple confirm function that returns a Promise<boolean>
  const confirm = useCallback(
    (options: SimpleConfirmOptions): Promise<boolean> => {
      return new Promise((resolve) => {
        setState({
          isOpen: true,
          loading: false,
          title: options.title,
          message: options.message,
          confirmLabel: options.confirmLabel || "Confirm",
          cancelLabel: options.cancelLabel || "Cancel",
          type: options.type || "warning",
          onConfirm: async () => {
            resolve(true);
            setState((prev) => ({ ...prev, isOpen: false }));
          },
        });

        // Handle cancel by resolving with false when dialog closes
        const originalOnClose = () => {
          resolve(false);
          setState((prev) => ({ ...prev, isOpen: false }));
        };

        // Store the cancel handler
        setState((prev) => ({ ...prev, onCancel: originalOnClose }));
      });
    },
    []
  );

  const dialogProps = {
    isOpen: state.isOpen,
    title: state.title,
    message: state.message,
    confirmText: state.confirmLabel,
    cancelText: state.cancelLabel,
    type: state.type,
    loading: state.loading,
    onConfirm: handleConfirm,
    onClose: hideConfirmDialog,
  };

  return {
    // New interface
    confirmDialog,
    showConfirmDialog,
    hideConfirmDialog,
    // Legacy interface for backward compatibility
    confirm,
    dialogProps,
  };
}
