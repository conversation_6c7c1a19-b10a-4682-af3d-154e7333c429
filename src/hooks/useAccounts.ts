import { useState, useEffect, useCallback } from "react";
import {
  accountService,
  type CreateAccountData,
  type UpdateAccountData,
  type AccountFilters,
} from "../services/accountService";
import { useNotification } from "../contexts/NotificationContext";
import type { Account, AccountType } from "../types";
import humps from "humps";

export interface UseAccountsOptions {
  companyId: string;
  filters?: AccountFilters;
  autoFetch?: boolean;
}

export interface UseAccountsReturn {
  accounts: Account[];
  accountsTree: Account[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createAccount: (data: CreateAccountData) => Promise<Account | null>;
  updateAccount: (
    accountId: string,
    data: UpdateAccountData
  ) => Promise<Account | null>;
  deleteAccount: (accountId: string) => Promise<boolean>;
  generateAccountCode: (
    accountType: AccountType,
    accountSubtype?: string
  ) => Promise<string>;
}

export function useAccounts(options: UseAccountsOptions): UseAccountsReturn {
  const { companyId, filters, autoFetch = true } = options;
  const { showNotification } = useNotification();

  const [accounts, setAccounts] = useState<Account[]>([]);
  const [accountsTree, setAccountsTree] = useState<Account[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAccounts = useCallback(async () => {
    if (!companyId) return;

    setLoading(true);
    setError(null);

    try {
      const [flatAccounts, treeAccounts] = await Promise.all([
        accountService.getAccounts(companyId, filters),
        accountService.getAccountsTree(companyId, filters),
      ]);
      const camelizedFlatAccounts = humps.camelizeKeys(
        flatAccounts
      ) as Account[];
      const camelizedTreeAccounts = humps.camelizeKeys(
        treeAccounts
      ) as Account[];

      setAccounts(camelizedFlatAccounts);
      setAccountsTree(camelizedTreeAccounts);

      // setAccounts(flatAccounts);
      // setAccountsTree(treeAccounts);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch accounts";
      setError(errorMessage);
      showNotification({
        type: "error",
        title: "Error",
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  }, [companyId, filters, showNotification]);

  const createAccount = useCallback(
    async (data: CreateAccountData): Promise<Account | null> => {
      try {
        setLoading(true);
        const newAccount = await accountService.createAccount(data);

        showNotification({
          type: "success",
          title: "Success",
          message: "Account created successfully",
        });

        // Refresh the accounts list
        await fetchAccounts();

        return newAccount;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to create account";
        setError(errorMessage);
        showNotification({
          type: "error",
          title: "Error",
          message: errorMessage,
        });
        return null;
      } finally {
        setLoading(false);
      }
    },
    [fetchAccounts, showNotification]
  );

  const updateAccount = useCallback(
    async (
      accountId: string,
      data: UpdateAccountData
    ): Promise<Account | null> => {
      try {
        setLoading(true);
        const updatedAccount = await accountService.updateAccount(
          companyId,
          accountId,
          data
        );

        showNotification({
          type: "success",
          title: "Success",
          message: "Account updated successfully",
        });

        // Refresh the accounts list
        await fetchAccounts();

        return updatedAccount;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to update account";
        setError(errorMessage);
        showNotification({
          type: "error",
          title: "Error",
          message: errorMessage,
        });
        return null;
      } finally {
        setLoading(false);
      }
    },
    [companyId, fetchAccounts, showNotification]
  );

  const deleteAccount = useCallback(
    async (accountId: string): Promise<boolean> => {
      try {
        setLoading(true);
        await accountService.deleteAccount(companyId, accountId);

        showNotification({
          type: "success",
          title: "Success",
          message: "Account deleted successfully",
        });

        // Refresh the accounts list
        await fetchAccounts();

        return true;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to delete account";
        setError(errorMessage);
        showNotification({
          type: "error",
          title: "Error",
          message: errorMessage,
        });
        return false;
      } finally {
        setLoading(false);
      }
    },
    [companyId, fetchAccounts, showNotification]
  );

  const generateAccountCode = useCallback(
    async (
      accountType: AccountType,
      accountSubtype?: string
    ): Promise<string> => {
      try {
        return await accountService.generateAccountCode(
          companyId,
          accountType,
          accountSubtype as any
        );
      } catch (err) {
        console.error("Failed to generate account code:", err);
        return "";
      }
    },
    [companyId]
  );

  useEffect(() => {
    if (autoFetch) {
      fetchAccounts();
    }
  }, [autoFetch, fetchAccounts]);

  return {
    accounts,
    accountsTree,
    loading,
    error,
    refetch: fetchAccounts,
    createAccount,
    updateAccount,
    deleteAccount,
    generateAccountCode,
  };
}

export interface UseAccountOptions {
  companyId: string;
  accountId: string;
  autoFetch?: boolean;
}

export interface UseAccountReturn {
  account: Account | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useAccount(options: UseAccountOptions): UseAccountReturn {
  const { companyId, accountId, autoFetch = true } = options;
  const { showNotification } = useNotification();

  const [account, setAccount] = useState<Account | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAccount = useCallback(async () => {
    if (!companyId || !accountId) return;

    setLoading(true);
    setError(null);

    try {
      const accountData = await accountService.getAccount(companyId, accountId);
      setAccount(accountData);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch account";
      setError(errorMessage);
      showNotification({
        type: "error",
        title: "Error",
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  }, [companyId, accountId, showNotification]);

  useEffect(() => {
    if (autoFetch) {
      fetchAccount();
    }
  }, [autoFetch, fetchAccount]);

  return {
    account,
    loading,
    error,
    refetch: fetchAccount,
  };
}
