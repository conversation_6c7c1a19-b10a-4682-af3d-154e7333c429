import { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  PlusIcon,
  DocumentDuplicateIcon,
  ArrowUpTrayIcon,
  ArrowDownTrayIcon,
  Cog6ToothIcon,
  ChartBarIcon
} from "@heroicons/react/24/outline";
import type { Transaction, TransactionStatus } from "../../types";
import TransactionList from "../../components/transactions/TransactionList";
import TransactionForm from "../../components/transactions/TransactionForm";
import TransactionView from "../../components/transactions/TransactionView";
import TransactionFilters from "../../components/transactions/TransactionFilters";
import BatchOperations from "../../components/transactions/BatchOperations";
import { useNotification } from "../../contexts/NotificationContext";

export default function Transactions() {
  const [showForm, setShowForm] = useState(false);
  const [showView, setShowView] = useState(false);
  const [showBatchOperations, setShowBatchOperations] = useState(false);
  const [batchMode, setBatchMode] = useState<'import' | 'export'>('import');
  const [batchType, setBatchType] = useState<'transactions' | 'templates'>('transactions');
  const [selectedTransaction, setSelectedTransaction] = useState<
    Transaction | undefined
  >();
  const [filters, setFilters] = useState<{
    status?: TransactionStatus;
    dateFrom?: string;
    dateTo?: string;
    search?: string;
  }>({});
  const [refreshKey, setRefreshKey] = useState(0);

  const { showNotification } = useNotification();
  const navigate = useNavigate();

  const handleNewTransaction = () => {
    setSelectedTransaction(undefined);
    setShowForm(true);
  };

  const handleEditTransaction = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setShowForm(true);
  };

  const handleViewTransaction = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setShowView(true);
  };

  const handleFormClose = () => {
    setShowForm(false);
    setSelectedTransaction(undefined);
  };

  const handleViewClose = () => {
    setShowView(false);
    setSelectedTransaction(undefined);
  };

  const handleTransactionSave = () => {
    setRefreshKey((prev) => prev + 1);
    handleFormClose();
  };

  const handleTransactionUpdate = (updatedTransaction: Transaction) => {
    setSelectedTransaction(updatedTransaction);
    setRefreshKey((prev) => prev + 1);
  };

  const handleEditFromView = () => {
    setShowView(false);
    setShowForm(true);
  };

  const handleBatchImport = () => {
    setBatchMode('import');
    setBatchType('transactions');
    setShowBatchOperations(true);
  };

  const handleBatchExport = () => {
    setBatchMode('export');
    setBatchType('transactions');
    setShowBatchOperations(true);
  };

  const handleBatchComplete = () => {
    setShowBatchOperations(false);
    setRefreshKey(prev => prev + 1);
    showNotification({
      type: 'success',
      title: 'Batch operation completed',
      message: `${batchMode === 'import' ? 'Import' : 'Export'} operation completed successfully`,
    });
  };

  const handleTemplatesClick = () => {
    // Navigate to templates page
    navigate('/transactions/templates');
  };

  const handleReportsClick = () => {
    // Navigate to reports page
    navigate('/reports');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Transactions</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Record and manage your financial transactions with enterprise features
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleNewTransaction}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Transaction
          </button>
        </div>
      </div>

      {/* Enterprise Features Quick Actions */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Enterprise Features
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={handleTemplatesClick}
            className="flex items-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
          >
            <DocumentDuplicateIcon className="h-8 w-8 text-primary-600 mr-3" />
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                Transaction Templates
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Create reusable templates
              </div>
            </div>
          </button>

          <button
            onClick={handleBatchImport}
            className="flex items-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
          >
            <ArrowUpTrayIcon className="h-8 w-8 text-green-600 mr-3" />
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                Batch Import
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Import from CSV/Excel
              </div>
            </div>
          </button>

          <button
            onClick={handleBatchExport}
            className="flex items-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
          >
            <ArrowDownTrayIcon className="h-8 w-8 text-blue-600 mr-3" />
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                Batch Export
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Export to CSV/Excel/PDF
              </div>
            </div>
          </button>

          <button
            onClick={handleReportsClick}
            className="flex items-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
          >
            <ChartBarIcon className="h-8 w-8 text-purple-600 mr-3" />
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                Advanced Reports
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Custom analytics
              </div>
            </div>
          </button>
        </div>
      </div>

      {/* Filters */}
      <TransactionFilters onFiltersChange={setFilters} />

      {/* Transaction List */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <TransactionList
          key={refreshKey}
          filters={filters}
          onEdit={handleEditTransaction}
          onView={handleViewTransaction}
        />
      </div>

      {/* Transaction Form Modal */}
      <TransactionForm
        transaction={selectedTransaction}
        isOpen={showForm}
        onClose={handleFormClose}
        onSave={handleTransactionSave}
      />

      {/* Transaction View Modal */}
      {selectedTransaction && (
        <TransactionView
          transaction={selectedTransaction}
          isOpen={showView}
          onClose={handleViewClose}
          onEdit={handleEditFromView}
          onUpdate={handleTransactionUpdate}
        />
      )}

      {/* Batch Operations Modal */}
      <BatchOperations
        isOpen={showBatchOperations}
        onClose={() => setShowBatchOperations(false)}
        onComplete={handleBatchComplete}
        mode={batchMode}
        type={batchType}
      />
    </div>
  );
}
