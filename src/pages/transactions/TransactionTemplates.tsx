import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { PlusIcon, DocumentDuplicateIcon, PlayIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import TransactionTemplateList from '../../components/transactions/TransactionTemplateList';
import TransactionTemplateForm from '../../components/transactions/TransactionTemplateForm';
import TransactionForm from '../../components/transactions/TransactionForm';
import { transactionTemplateService, type TransactionTemplate } from '../../services/transactionTemplateService';
import { useCompany } from '../../contexts/CompanyContext';
import { useNotification } from '../../contexts/NotificationContext';

export default function TransactionTemplates() {
  const [selectedTemplate, setSelectedTemplate] = useState<TransactionTemplate | null>(null);
  const [showTemplateForm, setShowTemplateForm] = useState(false);
  const [showTransactionForm, setShowTransactionForm] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [applyingTemplate, setApplyingTemplate] = useState<TransactionTemplate | null>(null);

  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  const navigate = useNavigate();

  const handleCreateTemplate = () => {
    setSelectedTemplate(null);
    setShowTemplateForm(true);
  };

  const handleEditTemplate = (template: TransactionTemplate) => {
    setSelectedTemplate(template);
    setShowTemplateForm(true);
  };

  const handleApplyTemplate = async (template: TransactionTemplate) => {
    if (!currentCompany) return;

    try {
      setApplyingTemplate(template);
      
      // Apply template with default values
      const transactionData = await transactionTemplateService.applyTemplate(
        currentCompany.id,
        template.id,
        {
          transactionDate: new Date().toISOString().split('T')[0],
          description: template.name,
          variables: template.defaultValues,
        }
      );

      // Create a transaction object that matches the TransactionForm expectations
      const transactionForForm = {
        id: '', // New transaction
        companyId: currentCompany.id,
        transactionNumber: '',
        transactionDate: transactionData.transactionDate,
        description: transactionData.description,
        reference: transactionData.reference || '',
        status: 'DRAFT' as const,
        totalAmount: 0,
        currency: currentCompany.baseCurrency,
        exchangeRate: 1,
        createdBy: '',
        metadata: {},
        entries: transactionData.entries.map((entry, index) => ({
          id: '',
          transactionId: '',
          accountId: entry.accountId,
          description: entry.description,
          debitAmount: entry.debitAmount,
          creditAmount: entry.creditAmount,
          currency: currentCompany.baseCurrency,
          exchangeRate: 1,
          baseDebitAmount: entry.debitAmount,
          baseCreditAmount: entry.creditAmount,
          lineNumber: index + 1,
          metadata: {},
          createdAt: '',
          updatedAt: '',
        })),
        createdAt: '',
        updatedAt: '',
      };

      setSelectedTemplate(transactionForForm as any);
      setShowTransactionForm(true);

      showNotification({
        type: 'success',
        title: 'Template applied',
        message: `Template "${template.name}" has been applied to a new transaction`,
      });
    } catch (error) {
      console.error('Failed to apply template:', error);
      showNotification({
        type: 'error',
        title: 'Failed to apply template',
        message: 'Could not apply the selected template',
      });
    } finally {
      setApplyingTemplate(null);
    }
  };

  const handleDuplicateTemplate = async (template: TransactionTemplate) => {
    if (!currentCompany) return;

    try {
      const duplicatedTemplate = {
        ...template,
        name: `${template.name} (Copy)`,
        id: undefined,
        isSystemTemplate: false,
        usageCount: 0,
        createdAt: '',
        updatedAt: '',
      };

      await transactionTemplateService.createTemplate(currentCompany.id, {
        name: duplicatedTemplate.name,
        description: duplicatedTemplate.description,
        category: duplicatedTemplate.category,
        defaultValues: duplicatedTemplate.defaultValues,
        entries: duplicatedTemplate.entries.map((entry, index) => ({
          accountId: entry.accountId,
          description: entry.description,
          debitAmount: entry.debitAmount,
          creditAmount: entry.creditAmount,
          isVariableAmount: entry.isVariableAmount,
          amountFormula: entry.amountFormula,
          lineNumber: index + 1,
          metadata: entry.metadata,
        })),
      });

      setRefreshKey(prev => prev + 1);
      showNotification({
        type: 'success',
        title: 'Template duplicated',
        message: `Template "${template.name}" has been duplicated successfully`,
      });
    } catch (error) {
      console.error('Failed to duplicate template:', error);
      showNotification({
        type: 'error',
        title: 'Failed to duplicate template',
        message: 'Could not duplicate the selected template',
      });
    }
  };

  const handleTemplateFormClose = () => {
    setShowTemplateForm(false);
    setSelectedTemplate(null);
  };

  const handleTemplateSave = () => {
    setRefreshKey(prev => prev + 1);
    handleTemplateFormClose();
  };

  const handleTransactionFormClose = () => {
    setShowTransactionForm(false);
    setSelectedTemplate(null);
  };

  const handleTransactionSave = () => {
    handleTransactionFormClose();
    showNotification({
      type: 'success',
      title: 'Transaction created',
      message: 'Transaction created successfully from template',
    });
  };

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/transactions')}
          className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back to Transactions
        </button>
        <nav className="flex" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2">
            <li>
              <button
                onClick={() => navigate('/transactions')}
                className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              >
                Transactions
              </button>
            </li>
            <li>
              <div className="flex items-center">
                <svg
                  className="flex-shrink-0 h-4 w-4 text-gray-400"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                </svg>
                <span className="ml-2 text-sm font-medium text-gray-900 dark:text-white">
                  Templates
                </span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Transaction Templates
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Create and manage reusable transaction templates to streamline data entry
          </p>
        </div>
        <button
          onClick={handleCreateTemplate}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          New Template
        </button>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={handleCreateTemplate}
            className="flex items-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
          >
            <PlusIcon className="h-8 w-8 text-gray-400 mr-3" />
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                Create Template
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Build a new reusable template
              </div>
            </div>
          </button>

          <button
            onClick={() => {
              // This would open a dialog to select from system templates
              showNotification({
                type: 'info',
                title: 'Coming Soon',
                message: 'System template import feature is coming soon',
              });
            }}
            className="flex items-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
          >
            <DocumentDuplicateIcon className="h-8 w-8 text-gray-400 mr-3" />
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                Import System Template
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Use pre-built templates
              </div>
            </div>
          </button>

          <button
            onClick={() => {
              // This would open a dialog to import from file
              showNotification({
                type: 'info',
                title: 'Coming Soon',
                message: 'Template import from file feature is coming soon',
              });
            }}
            className="flex items-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
          >
            <PlayIcon className="h-8 w-8 text-gray-400 mr-3" />
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                Import from File
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Upload template file
              </div>
            </div>
          </button>
        </div>
      </div>

      {/* Template List */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <TransactionTemplateList
          key={refreshKey}
          onCreateTemplate={handleCreateTemplate}
          onEditTemplate={handleEditTemplate}
          onApplyTemplate={handleApplyTemplate}
          onDuplicateTemplate={handleDuplicateTemplate}
          applyingTemplate={applyingTemplate}
          refreshKey={refreshKey}
        />
      </div>

      {/* Template Form Modal */}
      <TransactionTemplateForm
        template={selectedTemplate}
        isOpen={showTemplateForm}
        onClose={handleTemplateFormClose}
        onSave={handleTemplateSave}
      />

      {/* Transaction Form Modal (for applying templates) */}
      <TransactionForm
        transaction={selectedTemplate}
        isOpen={showTransactionForm}
        onClose={handleTransactionFormClose}
        onSave={handleTransactionSave}
      />
    </div>
  );
}
