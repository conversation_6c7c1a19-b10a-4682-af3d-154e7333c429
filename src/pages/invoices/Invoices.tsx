import { useState, useEffect } from "react";
import { PlusIcon } from "@heroicons/react/24/outline";
import type { Invoice, InvoiceType, InvoiceStatus } from "../../types";
import { invoiceService } from "../../services/invoiceService";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { useConfirmDialog } from "../../hooks/useConfirmDialog";
import InvoiceList from "../../components/invoices/InvoiceList";
import InvoiceFilters from "../../components/invoices/InvoiceFilters";
import InvoiceForm from "../../components/invoices/InvoiceForm";
import InvoiceView from "../../components/invoices/InvoiceView";
import ConfirmDialog from "../../components/ui/ConfirmDialog";
import humps from "humps";

interface InvoiceFilters {
  invoiceType?: InvoiceType;
  status?: InvoiceStatus;
  contactId?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
}

export default function Invoices() {
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();
  const { confirm, dialogProps } = useConfirmDialog();
  const [showForm, setShowForm] = useState(false);
  const [showView, setShowView] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [editingInvoice, setEditingInvoice] = useState<Invoice | null>(null);
  const [filters, setFilters] = useState<InvoiceFilters>({});
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [stats, setStats] = useState({
    total: 0,
    draft: 0,
    sent: 0,
    paid: 0,
    overdue: 0,
    totalAmount: 0,
    paidAmount: 0,
    outstandingAmount: 0,
  });

  useEffect(() => {
    loadStats();
  }, [currentCompany, refreshTrigger]);

  const loadStats = async () => {
    if (!currentCompany) return;

    try {
      // Load basic stats - this would be optimized with a dedicated stats endpoint
      const response = await invoiceService.getInvoices(currentCompany.id, {
        limit: 1000, // Get all for stats calculation
      });

      const camelizedInvoices = humps.camelizeKeys(response.data) as Invoice[];

      const invoices = camelizedInvoices;
      const newStats = {
        total: invoices.length,
        draft: invoices.filter((i) => i.status === "DRAFT").length,
        sent: invoices.filter((i) => i.status === "SENT").length,
        paid: invoices.filter((i) => i.status === "PAID").length,
        overdue: invoices.filter((i) => invoiceService.isOverdue(i)).length,
        totalAmount: invoices.reduce((sum, i) => sum + i.totalAmount, 0),
        paidAmount: invoices.reduce((sum, i) => sum + i.paidAmount, 0),
        outstandingAmount: invoices.reduce((sum, i) => sum + i.balanceDue, 0),
      };

      setStats(newStats);
    } catch (error) {
      console.error("Failed to load invoice stats:", error);
    }
  };

  const handleCreateInvoice = () => {
    setEditingInvoice(null);
    setShowForm(true);
  };

  const handleEditInvoice = (invoice: Invoice) => {
    setEditingInvoice(invoice);
    setShowForm(true);
  };

  const handleViewInvoice = async (invoice: Invoice) => {
    if (!currentCompany) return;

    try {
      // Fetch the complete invoice data with line items
      const fullInvoice = await invoiceService.getInvoice(
        currentCompany.id,
        invoice.id
      );
      const camelizedInvoice = humps.camelizeKeys(fullInvoice) as Invoice;
      setSelectedInvoice(camelizedInvoice);
      setShowView(true);
    } catch (error) {
      console.error("Failed to fetch invoice details:", error);
      addNotification(
        "error",
        "Failed to Load Invoice",
        "There was an error loading the invoice details. Please try again."
      );
    }
  };

  const handleSaveInvoice = (_invoice: Invoice) => {
    setShowForm(false);
    setEditingInvoice(null);
    setRefreshTrigger((prev) => prev + 1);
  };

  const handleSendInvoice = async (invoice: Invoice) => {
    if (!currentCompany) return;

    try {
      await invoiceService.sendInvoice(currentCompany.id, invoice.id);
      setRefreshTrigger((prev) => prev + 1);
      if (selectedInvoice?.id === invoice.id) {
        // Refresh the viewed invoice
        const updatedInvoice = await invoiceService.getInvoice(
          currentCompany.id,
          invoice.id
        );
        const camelizedInvoice = humps.camelizeKeys(updatedInvoice) as Invoice;
        setSelectedInvoice(camelizedInvoice);
      }
    } catch (error) {
      console.error("Failed to send invoice:", error);
      addNotification(
        "error",
        "Failed to Send Invoice",
        "There was an error sending the invoice. Please try again."
      );
    }
  };

  const handleMarkPaid = async (invoice: Invoice) => {
    if (!currentCompany) return;

    const confirmed = await confirm({
      title: "Mark Invoice as Paid",
      message: `Are you sure you want to mark invoice ${
        invoice.invoiceNumber
      } as paid for ${invoiceService.formatCurrency(
        invoice.balanceDue,
        invoice.currency
      )}?`,
      confirmLabel: "Mark as Paid",
      cancelLabel: "Cancel",
      type: "success",
    });

    if (confirmed) {
      try {
        await invoiceService.markInvoicePaid(currentCompany.id, invoice.id);
        setRefreshTrigger((prev) => prev + 1);
        if (selectedInvoice?.id === invoice.id) {
          // Refresh the viewed invoice
          const updatedInvoice = await invoiceService.getInvoice(
            currentCompany.id,
            invoice.id
          );
          const camelizedInvoice = humps.camelizeKeys(
            updatedInvoice
          ) as Invoice;
          setSelectedInvoice(camelizedInvoice);
        }
        addNotification(
          "success",
          "Invoice Marked as Paid",
          `Invoice ${invoice.invoiceNumber} has been marked as paid successfully.`
        );
      } catch (error) {
        console.error("Failed to mark invoice as paid:", error);
        addNotification(
          "error",
          "Failed to Mark as Paid",
          "There was an error marking the invoice as paid. Please check the invoice details and try again."
        );
      }
    }
  };

  const handleFiltersChange = (newFilters: InvoiceFilters) => {
    setFilters(newFilters);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingInvoice(null);
  };

  const handleCloseView = () => {
    setShowView(false);
    setSelectedInvoice(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Invoices</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Create and manage sales and purchase invoices
          </p>
        </div>
        <button
          onClick={handleCreateInvoice}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Create Invoice
        </button>
      </div>

      {/* Optimized Stats Cards - All in One Row */}
      <div className="grid bg-white dark:bg-gray-800 grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-7 gap-3">
        {/* Total Invoices */}
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-3">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                    <path
                      fillRule="evenodd"
                      d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">
                  Total
                </p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {stats.total}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Paid */}
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-3">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">
                  Paid
                </p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {stats.paid}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Pending */}
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-3">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">
                  Pending
                </p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {stats.sent}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Overdue */}
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-3">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">
                  Overdue
                </p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {stats.overdue}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Total Amount */}
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-3">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">
                  Total
                </p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {invoiceService.formatCurrency(stats.totalAmount)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Paid Amount */}
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-3">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-600 rounded-md flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">
                  Paid
                </p>
                <p className="text-lg font-semibold text-green-600 dark:text-green-400">
                  {invoiceService.formatCurrency(stats.paidAmount)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Outstanding Amount */}
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-3">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-red-600 rounded-md flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">
                  Outstanding
                </p>
                <p className="text-lg font-semibold text-red-600 dark:text-red-400">
                  {invoiceService.formatCurrency(stats.outstandingAmount)}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <InvoiceFilters onFiltersChange={handleFiltersChange} />

      {/* Invoice List */}
      <InvoiceList
        filters={filters}
        onView={handleViewInvoice}
        onEdit={handleEditInvoice}
        refreshTrigger={refreshTrigger}
      />

      {/* Modals */}
      {showForm && (
        <InvoiceForm
          invoice={editingInvoice || undefined}
          onSave={handleSaveInvoice}
          onCancel={handleCloseForm}
        />
      )}

      {showView && selectedInvoice && (
        <InvoiceView
          invoice={selectedInvoice}
          onClose={handleCloseView}
          onEdit={() => {
            handleCloseView();
            handleEditInvoice(selectedInvoice);
          }}
          onSend={() => handleSendInvoice(selectedInvoice)}
          onMarkPaid={() => handleMarkPaid(selectedInvoice)}
        />
      )}

      {/* Confirmation Dialog */}
      <ConfirmDialog {...dialogProps} />
    </div>
  );
}
