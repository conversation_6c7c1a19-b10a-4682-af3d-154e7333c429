import { useState, useEffect } from "react";
import {
  EyeIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  DocumentArrowDownIcon,
  MagnifyingGlassIcon,
  ClockIcon,
  UserGroupIcon,
  CogIcon,
  CommandLineIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import InvestigationTools from "../../components/auditing/InvestigationTools";
import SuperAdminCommands from "../../components/auditing/SuperAdminCommands";
import ComplianceMonitoring from "../../components/auditing/ComplianceMonitoring";
import AdvancedReports from "../../components/auditing/AdvancedReports";
import RealTimeMonitoring from "../../components/auditing/RealTimeMonitoring";

// Auditing section types
type AuditingSection = 
  | "overview" 
  | "investigation" 
  | "compliance" 
  | "reports" 
  | "commands" 
  | "monitoring";

interface AuditOverview {
  totalLogs: number;
  highRiskActivities: number;
  suspiciousTransactions: number;
  complianceIssues: number;
  recentAlerts: number;
  systemHealth: 'HEALTHY' | 'WARNING' | 'CRITICAL';
}

interface SuspiciousActivity {
  id: string;
  type: 'HIGH_FREQUENCY' | 'LARGE_AMOUNT' | 'UNUSUAL_PATTERN' | 'FAILED_ACCESS';
  description: string;
  riskLevel: 'HIGH' | 'CRITICAL';
  timestamp: string;
  userId?: string;
  amount?: number;
  currency?: string;
}

export default function Auditing() {
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  const [activeSection, setActiveSection] = useState<AuditingSection>("overview");
  const [loading, setLoading] = useState(false);
  const [auditOverview, setAuditOverview] = useState<AuditOverview | null>(null);
  const [suspiciousActivities, setSuspiciousActivities] = useState<SuspiciousActivity[]>([]);

  const auditingSections = [
    {
      id: "overview" as const,
      name: "Overview",
      description: "System-wide audit summary and alerts",
      icon: ChartBarIcon,
    },
    {
      id: "investigation" as const,
      name: "Investigation",
      description: "Deep dive analysis and forensic tools",
      icon: MagnifyingGlassIcon,
    },
    {
      id: "compliance" as const,
      name: "Compliance Monitoring",
      description: "Tanzania regulatory compliance tracking",
      icon: ShieldCheckIcon,
    },
    {
      id: "reports" as const,
      name: "Advanced Reports",
      description: "Comprehensive audit and compliance reports",
      icon: DocumentArrowDownIcon,
    },
    {
      id: "commands" as const,
      name: "Super Admin Commands",
      description: "CLI tools and administrative functions",
      icon: CommandLineIcon,
    },
    {
      id: "monitoring" as const,
      name: "Real-time Monitoring",
      description: "Live activity monitoring and alerts",
      icon: ClockIcon,
    },
  ];

  useEffect(() => {
    if (currentCompany) {
      loadAuditOverview();
      loadSuspiciousActivities();
    }
  }, [currentCompany]);

  const loadAuditOverview = async () => {
    if (!currentCompany) return;

    setLoading(true);
    try {
      // Try to fetch from API first
      const response = await fetch(`/api/auditing/overview?companyId=${currentCompany.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAuditOverview(data.data);
      } else {
        // Use mock data if API is not available
        loadMockOverviewData();
      }
    } catch (error) {
      // Use mock data on error (API not implemented yet)
      console.log('API not available, using mock data');
      loadMockOverviewData();
    } finally {
      setLoading(false);
    }
  };

  const loadMockOverviewData = () => {
    setAuditOverview({
      totalLogs: 1247,
      highRiskActivities: 23,
      suspiciousTransactions: 5,
      complianceIssues: 2,
      recentAlerts: 8,
      systemHealth: 'WARNING'
    });
  };

  const loadSuspiciousActivities = async () => {
    if (!currentCompany) return;

    try {
      // Try to fetch from API first
      const response = await fetch(`/api/auditing/suspicious?companyId=${currentCompany.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSuspiciousActivities(data.data);
      } else {
        // Use mock data if API is not available
        loadMockSuspiciousData();
      }
    } catch (error) {
      // Use mock data on error (API not implemented yet)
      console.log('API not available, using mock suspicious activities');
      loadMockSuspiciousData();
    }
  };

  const loadMockSuspiciousData = () => {
    setSuspiciousActivities([
      {
        id: '1',
        type: 'LARGE_AMOUNT',
        description: 'Transaction exceeding 15M TZS threshold',
        riskLevel: 'HIGH',
        timestamp: new Date().toISOString(),
        amount: 18500000,
        currency: 'TZS'
      },
      {
        id: '2',
        type: 'HIGH_FREQUENCY',
        description: 'User performed 25 transactions in 1 hour',
        riskLevel: 'CRITICAL',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        userId: 'user123'
      },
      {
        id: '3',
        type: 'FAILED_ACCESS',
        description: 'Multiple failed login attempts from suspicious IP',
        riskLevel: 'HIGH',
        timestamp: new Date(Date.now() - 7200000).toISOString(),
      }
    ]);
  };

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'HEALTHY': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'WARNING': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'CRITICAL': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500';
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'HIGH': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20 dark:text-orange-400';
      case 'CRITICAL': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
      default: return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
    }
  };

  const activeAuditingSection = auditingSections.find(
    (section) => section.id === activeSection
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Auditing & Investigation
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Comprehensive audit monitoring, investigation tools, and compliance management
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {auditOverview && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">System Health:</span>
              <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getHealthStatusColor(auditOverview.systemHealth)}`}>
                {auditOverview.systemHealth}
              </span>
            </div>
          )}
        </div>
      </div>

      <div className="grid bg-white dark:bg-gray-800 grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Navigation */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            {auditingSections.map((section) => {
              const Icon = section.icon;
              const isActive = activeSection === section.id;

              return (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full text-left group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer ${
                    isActive
                      ? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-r-2 border-blue-500"
                      : "text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white"
                  }`}
                >
                  <Icon
                    className={`mr-3 h-5 w-5 ${
                      isActive
                        ? "text-blue-500 dark:text-blue-400"
                        : "text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-300"
                    }`}
                  />
                  <div className="flex-1">
                    <div className="font-medium">{section.name}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-0.5">
                      {section.description}
                    </div>
                  </div>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          {/* Overview Section */}
          {activeSection === "overview" && (
            <div className="space-y-6">
              {/* Key Metrics */}
              <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <EyeIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Total Audit Logs</p>
                      <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                        {loading ? '...' : auditOverview?.totalLogs?.toLocaleString() || '0'}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <ExclamationTriangleIcon className="h-8 w-8 text-orange-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">High Risk Activities</p>
                      <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                        {loading ? '...' : auditOverview?.highRiskActivities || '0'}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <ShieldCheckIcon className="h-8 w-8 text-red-600 dark:text-red-400" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Compliance Issues</p>
                      <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                        {loading ? '...' : auditOverview?.complianceIssues || '0'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Suspicious Activities */}
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Recent Suspicious Activities</h3>
                  <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    Activities flagged for investigation
                  </p>
                </div>
                <div className="p-6">
                  {suspiciousActivities.length > 0 ? (
                    <div className="space-y-4 bg-white dark:bg-gray-800">
                      {suspiciousActivities.map((activity) => (
                        <div key={activity.id} className="flex items-start space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                          <div className="flex-shrink-0">
                            <ExclamationTriangleIcon className={`h-6 w-6 ${
                              activity.riskLevel === 'CRITICAL' ? 'text-red-600 dark:text-red-400' : 'text-orange-600'
                            }`} />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium text-gray-900 dark:text-white">
                                {activity.description}
                              </p>
                              <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getRiskLevelColor(activity.riskLevel)}`}>
                                {activity.riskLevel}
                              </span>
                            </div>
                            <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                              <span>{new Date(activity.timestamp).toLocaleString()}</span>
                              {activity.amount && (
                                <span>{activity.amount.toLocaleString()} {activity.currency}</span>
                              )}
                              {activity.userId && (
                                <span>User: {activity.userId}</span>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">No suspicious activities detected.</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Investigation Section */}
          {activeSection === "investigation" && (
            <InvestigationTools />
          )}

          {/* Super Admin Commands Section */}
          {activeSection === "commands" && (
            <SuperAdminCommands />
          )}

          {/* Compliance Monitoring Section */}
          {activeSection === "compliance" && (
            <ComplianceMonitoring />
          )}

          {/* Advanced Reports Section */}
          {activeSection === "reports" && (
            <AdvancedReports />
          )}

          {/* Real-time Monitoring Section */}
          {activeSection === "monitoring" && (
            <RealTimeMonitoring />
          )}

          {/* Other sections placeholder */}
          {activeSection !== "overview" && activeSection !== "investigation" && activeSection !== "commands" && activeSection !== "compliance" && activeSection !== "reports" && activeSection !== "monitoring" && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <div className="text-center">
                <activeAuditingSection.icon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                  {activeAuditingSection?.name}
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  {activeAuditingSection?.description}
                </p>
                <div className="mt-6">
                  <p className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    This section is under development and will include advanced {activeSection} features.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
