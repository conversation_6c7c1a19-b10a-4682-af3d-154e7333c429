import { useState, useEffect } from "react";
import {
  DocumentTextIcon,
  CalculatorIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowDownTrayIcon,
  PlusIcon,
  EyeIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../contexts/CompanyContext";
import { useNotification } from "../contexts/NotificationContext";
import { apiService } from "../services/api";
import VATCalculator from "../components/tra/VATCalculator";
import WHTCalculator from "../components/tra/WHTCalculator";
import VATReturns from "../components/tra/VATReturns";
import WHTRecords from "../components/tra/WHTRecords";
import TRAStatistics from "../components/tra/TRAStatistics";

type TRASection = 'overview' | 'vat-calculator' | 'wht-calculator' | 'vat-returns' | 'wht-records' | 'statistics';

interface TRAOverview {
  vatReturns: {
    submitted: number;
    pending: number;
    overdue: number;
  };
  withholdingTax: {
    certificates: number;
    pendingSubmissions: number;
    totalDeducted: number;
  };
  compliance: {
    score: number;
    issues: number;
    lastSubmission: string;
  };
}

export default function TRACompliance() {
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();
  const [activeSection, setActiveSection] = useState<TRASection>('overview');
  const [overview, setOverview] = useState<TRAOverview | null>(null);
  const [loading, setLoading] = useState(false);

  const traSections = [
    {
      id: 'overview' as const,
      name: 'Overview',
      description: 'TRA compliance dashboard and statistics',
      icon: DocumentTextIcon,
    },
    {
      id: 'vat-calculator' as const,
      name: 'VAT Calculator',
      description: 'Calculate VAT for transactions',
      icon: CalculatorIcon,
    },
    {
      id: 'wht-calculator' as const,
      name: 'WHT Calculator',
      description: 'Calculate withholding tax',
      icon: CalculatorIcon,
    },
    {
      id: 'vat-returns' as const,
      name: 'VAT Returns',
      description: 'Manage VAT returns and submissions',
      icon: DocumentTextIcon,
    },
    {
      id: 'wht-records' as const,
      name: 'WHT Records',
      description: 'Withholding tax certificates and records',
      icon: DocumentTextIcon,
    },
    {
      id: 'statistics' as const,
      name: 'Statistics',
      description: 'Detailed TRA compliance analytics',
      icon: ClockIcon,
    },
  ];

  useEffect(() => {
    if (currentCompany && activeSection === 'overview') {
      loadOverview();
    }
  }, [currentCompany, activeSection]);

  const loadOverview = async () => {
    if (!currentCompany) return;

    setLoading(true);
    try {
      const data = await apiService.get(`/tra/${currentCompany.id}/statistics`);
      setOverview(data.data);
    } catch (error) {
      console.error('Error loading TRA overview:', error);
      addNotification('error', 'Load Error', 'Failed to load TRA overview');
    } finally {
      setLoading(false);
    }
  };

  const getComplianceScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 dark:text-green-400';
    if (score >= 70) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getComplianceScoreBg = (score: number) => {
    if (score >= 90) return 'bg-green-100 dark:bg-green-900/20';
    if (score >= 70) return 'bg-yellow-100 dark:bg-yellow-900/20';
    return 'bg-red-100 dark:bg-red-900/20';
  };

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">Please select a company to view TRA compliance.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            TRA Compliance
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Tanzania Revenue Authority compliance management for {currentCompany.name}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Last updated: {new Date().toLocaleString()}
          </span>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {traSections.map((section) => {
            const Icon = section.icon;
            return (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                  activeSection === section.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Icon className="h-5 w-5" />
                  <span>{section.name}</span>
                </div>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        {/* Overview Section */}
        {activeSection === 'overview' && (
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                TRA Compliance Overview
              </h3>
              <button
                onClick={loadOverview}
                disabled={loading}
                className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 text-sm transition-colors duration-200"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                {loading ? 'Refreshing...' : 'Refresh'}
              </button>
            </div>

            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : overview ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* VAT Returns Card */}
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-600 dark:text-blue-400">VAT Returns</p>
                      <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                        {overview.vatReturns.submitted}
                      </p>
                    </div>
                    <DocumentTextIcon className="h-8 w-8 text-blue-500" />
                  </div>
                  <div className="mt-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-blue-700 dark:text-blue-300">Pending:</span>
                      <span className="font-medium text-blue-900 dark:text-blue-100">
                        {overview.vatReturns.pending}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-blue-700 dark:text-blue-300">Overdue:</span>
                      <span className={`font-medium ${overview.vatReturns.overdue > 0 ? 'text-red-600 dark:text-red-400' : 'text-blue-900 dark:text-blue-100'}`}>
                        {overview.vatReturns.overdue}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Withholding Tax Card */}
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-6 border border-green-200 dark:border-green-800">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-600 dark:text-green-400">WHT Certificates</p>
                      <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                        {overview.withholdingTax.certificates}
                      </p>
                    </div>
                    <DocumentTextIcon className="h-8 w-8 text-green-500" />
                  </div>
                  <div className="mt-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-green-700 dark:text-green-300">Pending:</span>
                      <span className="font-medium text-green-900 dark:text-green-100">
                        {overview.withholdingTax.pendingSubmissions}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-green-700 dark:text-green-300">Total Deducted:</span>
                      <span className="font-medium text-green-900 dark:text-green-100">
                        {(overview.withholdingTax.totalDeducted / 1000000).toFixed(1)}M TZS
                      </span>
                    </div>
                  </div>
                </div>

                {/* Compliance Score Card */}
                <div className={`rounded-lg p-6 border ${getComplianceScoreBg(overview.compliance.score)}`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Compliance Score</p>
                      <p className={`text-2xl font-bold ${getComplianceScoreColor(overview.compliance.score)}`}>
                        {overview.compliance.score}%
                      </p>
                    </div>
                    {overview.compliance.score >= 90 ? (
                      <CheckCircleIcon className="h-8 w-8 text-green-500" />
                    ) : (
                      <ExclamationTriangleIcon className="h-8 w-8 text-yellow-500" />
                    )}
                  </div>
                  <div className="mt-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-700 dark:text-gray-300">Issues:</span>
                      <span className={`font-medium ${overview.compliance.issues > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                        {overview.compliance.issues}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-700 dark:text-gray-300">Last Submission:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {new Date(overview.compliance.lastSubmission).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">No TRA compliance data available.</p>
              </div>
            )}
          </div>
        )}

        {/* VAT Calculator Section */}
        {activeSection === 'vat-calculator' && <VATCalculator />}

        {/* WHT Calculator Section */}
        {activeSection === 'wht-calculator' && <WHTCalculator />}

        {/* VAT Returns Section */}
        {activeSection === 'vat-returns' && <VATReturns />}

        {/* WHT Records Section */}
        {activeSection === 'wht-records' && <WHTRecords />}

        {/* Statistics Section */}
        {activeSection === 'statistics' && <TRAStatistics />}
      </div>
    </div>
  );
}
