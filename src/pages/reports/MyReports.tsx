import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CalendarIcon,
  ChartBarIcon,
  TableCellsIcon,
  DocumentChartBarIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';
import { useNotification } from '../../contexts/NotificationContext';

interface CustomReport {
  id: string;
  name: string;
  description: string;
  type: 'TABLE' | 'CHART' | 'SUMMARY';
  dataSource: string;
  createdAt: string;
  lastRun?: string;
  status: 'ACTIVE' | 'DRAFT';
}

export default function MyReports() {
  const navigate = useNavigate();
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  
  const [reports, setReports] = useState<CustomReport[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (currentCompany) {
      loadReports();
    }
  }, [currentCompany]);

  const loadReports = async () => {
    if (!currentCompany) return;
    
    setLoading(true);
    try {
      // In a real implementation, this would fetch from the API
      // For now, we'll use mock data to show where created reports appear
      const mockReports: CustomReport[] = [
        {
          id: '1',
          name: 'Monthly Sales Analysis',
          description: 'Comprehensive sales report with transaction breakdown',
          type: 'TABLE',
          dataSource: 'transactions',
          createdAt: '2024-12-10T10:30:00Z',
          lastRun: '2024-12-11T09:15:00Z',
          status: 'ACTIVE',
        },
        {
          id: '2',
          name: 'Customer Balance Summary',
          description: 'Customer outstanding balances and payment history',
          type: 'SUMMARY',
          dataSource: 'contacts',
          createdAt: '2024-12-09T14:20:00Z',
          lastRun: '2024-12-11T08:45:00Z',
          status: 'ACTIVE',
        },
        {
          id: '3',
          name: 'Bank Transaction Trends',
          description: 'Visual analysis of bank transaction patterns',
          type: 'CHART',
          dataSource: 'bank_transactions',
          createdAt: '2024-12-08T16:45:00Z',
          status: 'DRAFT',
        },
      ];
      
      setReports(mockReports);
    } catch (error) {
      console.error('Failed to load reports:', error);
      showNotification({
        type: 'error',
        title: 'Failed to load reports',
        message: 'Could not load your custom reports. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleViewReport = (report: CustomReport) => {
    showNotification({
      type: 'info',
      title: 'Report Viewer',
      message: `Opening ${report.name}...`,
    });
    // In a real implementation, this would navigate to the report viewer
    // navigate(`/reports/view/${report.id}`);
  };

  const handleEditReport = (report: CustomReport) => {
    showNotification({
      type: 'info',
      title: 'Report Editor',
      message: `Editing ${report.name}...`,
    });
    // In a real implementation, this would navigate to the report editor
    // navigate(`/reports/edit/${report.id}`);
  };

  const handleDeleteReport = async (report: CustomReport) => {
    if (!confirm(`Are you sure you want to delete "${report.name}"?`)) {
      return;
    }

    try {
      // In a real implementation, this would call the API to delete the report
      setReports(prev => prev.filter(r => r.id !== report.id));
      
      showNotification({
        type: 'success',
        title: 'Report deleted',
        message: `${report.name} has been deleted successfully.`,
      });
    } catch (error) {
      showNotification({
        type: 'error',
        title: 'Failed to delete report',
        message: 'Could not delete the report. Please try again.',
      });
    }
  };

  const getReportTypeIcon = (type: string) => {
    switch (type) {
      case 'TABLE':
        return TableCellsIcon;
      case 'CHART':
        return ChartBarIcon;
      case 'SUMMARY':
        return DocumentChartBarIcon;
      default:
        return DocumentTextIcon;
    }
  };

  const getReportTypeColor = (type: string) => {
    switch (type) {
      case 'TABLE':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400';
      case 'CHART':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'SUMMARY':
        return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20 dark:text-purple-400';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/reports')}
            className="mr-4 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              My Custom Reports
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              View and manage your custom reports created with the report builder
            </p>
          </div>
        </div>
        <button
          onClick={() => navigate('/reports/builder')}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Create New Report
        </button>
      </div>

      {/* Reports List */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Your Custom Reports ({reports.length})
          </h3>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Reports you've created using the custom report builder
          </p>
        </div>

        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Loading reports...</p>
          </div>
        ) : reports.length === 0 ? (
          <div className="p-6 text-center">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No custom reports</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Get started by creating your first custom report.
            </p>
            <div className="mt-6">
              <button
                onClick={() => navigate('/reports/builder')}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create Custom Report
              </button>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {reports.map((report) => {
              const TypeIcon = getReportTypeIcon(report.type);
              
              return (
                <div key={report.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 rounded-lg ${getReportTypeColor(report.type)}`}>
                        <TypeIcon className="h-5 w-5" />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {report.name}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {report.description}
                        </p>
                        <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                          <span>Type: {report.type}</span>
                          <span>•</span>
                          <span>Source: {report.dataSource}</span>
                          <span>•</span>
                          <span>Created: {formatDate(report.createdAt)}</span>
                          {report.lastRun && (
                            <>
                              <span>•</span>
                              <span>Last run: {formatDate(report.lastRun)}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        report.status === 'ACTIVE' 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                      }`}>
                        {report.status}
                      </span>
                      
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => handleViewReport(report)}
                          className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                          title="View Report"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleEditReport(report)}
                          className="p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400"
                          title="Edit Report"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteReport(report)}
                          className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                          title="Delete Report"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
