import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeftIcon, PlayIcon,  ShareIcon } from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';
import { useNotification } from '../../contexts/NotificationContext';
import { reportService } from '../../services/reportService';
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from '../../components/ui/chart';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, PieChart, Pie, Cell, LineChart, Line } from 'recharts';
import { DownloadIcon } from 'lucide-react';

interface CustomReport {
  id: string;
  name: string;
  description: string;
  type: 'TABLE' | 'CHART' | 'SUMMARY';
  dataSource: string;
  createdAt: string;
  lastRun?: string;
  status: 'ACTIVE' | 'DRAFT';
}

export default function ReportViewer() {
  const { reportId } = useParams<{ reportId: string }>();
  const navigate = useNavigate();
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  
  const [report, setReport] = useState<CustomReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [running, setRunning] = useState(false);
  const [reportData, setReportData] = useState<any>(null);
  const [chartType, setChartType] = useState<'bar' | 'line' | 'pie'>('bar');

  useEffect(() => {
    if (currentCompany && reportId) {
      loadReport();
    }
  }, [currentCompany, reportId]);

  // Auto-run the report when it's loaded
  useEffect(() => {
    if (report && currentCompany && reportId && !reportData) {
      runReport();
    }
  }, [report, currentCompany, reportId]);

  const loadReport = async () => {
    if (!currentCompany || !reportId) return;

    try {
      const reportData = await reportService.getCustomReport(currentCompany.id, reportId);
      setReport(reportData);
    } catch (error) {
      console.error('Failed to load report:', error);
      showNotification({
        type: 'error',
        title: 'Failed to load report',
        message: 'Could not load the report. Please try again.',
      });
      navigate('/reports');
    } finally {
      setLoading(false);
    }
  };

  const runReport = async () => {
    if (!currentCompany || !reportId) return;

    setRunning(true);
    try {
      const result = await reportService.runCustomReport(currentCompany.id, reportId);
      setReportData(result);
      
      // Update the report's last run time
      if (report) {
        setReport({
          ...report,
          lastRun: new Date().toISOString(),
        });
      }

      showNotification({
        type: 'success',
        title: 'Report generated',
        message: 'Report has been generated successfully.',
      });
    } catch (error) {
      console.error('Failed to run report:', error);
      showNotification({
        type: 'error',
        title: 'Failed to run report',
        message: 'Could not generate the report. Please try again.',
      });
    } finally {
      setRunning(false);
    }
  };

  const handleEdit = () => {
    navigate(`/reports/edit/${reportId}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderReportData = () => {
    if (!reportData) {
      return (
        <div className="text-center py-8">
          <div className="text-gray-500 dark:text-gray-400">No data to display</div>
        </div>
      );
    }

    // Handle different report types
    if (report?.type === 'TABLE') {
      return renderTableReport();
    } else if (report?.type === 'SUMMARY') {
      return renderSummaryReport();
    } else if (report?.type === 'CHART') {
      return renderChartReport();
    } else {
      return renderRawData();
    }
  };

  const renderTableReport = () => {
    if (!reportData || !reportData.data || !Array.isArray(reportData.data)) {
      return renderRawData();
    }

    const data = reportData.data;
    if (data.length === 0) {
      return (
        <div className="text-center py-8">
          <div className="text-gray-500 dark:text-gray-400">No records found</div>
        </div>
      );
    }

    // Get column headers from the first row
    const headers = Object.keys(data[0]);

    return (
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              {headers.map((header) => (
                <th
                  key={header}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  {header.replace(/_/g, ' ')}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {data.map((row: any, index: number) => (
              <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                {headers.map((header) => (
                  <td
                    key={header}
                    className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
                  >
                    {formatCellValue(row[header])}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  const renderSummaryReport = () => {
    if (!reportData) return renderRawData();

    const summary = reportData.summary || reportData;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Object.entries(summary).map(([key, value]) => (
          <div key={key} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              {key.replace(/_/g, ' ')}
            </div>
            <div className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
              {formatCellValue(value)}
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderChartReport = () => {
    if (!reportData || !reportData.data) {
      return renderRawData();
    }

    const data = reportData.data;
    const chartData = prepareChartData(data);

    if (!chartData || chartData.length === 0) {
      return (
        <div className="text-center py-8">
          <div className="text-gray-500 dark:text-gray-400">No data available for chart visualization</div>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* Chart Type Selector */}
        <div className="flex items-center justify-between">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white">Chart Visualization</h4>
          <div className="flex space-x-2">
            <button
              onClick={() => setChartType('bar')}
              className={`px-3 py-1 text-sm rounded-md ${
                chartType === 'bar'
                  ? 'bg-primary-100 text-primary-700 dark:bg-primary-900/20 dark:text-primary-400'
                  : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
              }`}
            >
              Bar Chart
            </button>
            <button
              onClick={() => setChartType('line')}
              className={`px-3 py-1 text-sm rounded-md ${
                chartType === 'line'
                  ? 'bg-primary-100 text-primary-700 dark:bg-primary-900/20 dark:text-primary-400'
                  : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
              }`}
            >
              Line Chart
            </button>
            <button
              onClick={() => setChartType('pie')}
              className={`px-3 py-1 text-sm rounded-md ${
                chartType === 'pie'
                  ? 'bg-primary-100 text-primary-700 dark:bg-primary-900/20 dark:text-primary-400'
                  : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
              }`}
            >
              Pie Chart
            </button>
          </div>
        </div>

        {/* Chart Display */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          {renderChart(chartData)}
        </div>

        {/* Data Summary */}
        {reportData.summary && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Summary Statistics</h5>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(reportData.summary).map(([key, value]) => (
                <div key={key} className="text-center">
                  <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {key.replace(/_/g, ' ')}
                  </div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    {formatCellValue(value)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderRawData = () => {
    return (
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Raw Data:
        </div>
        <pre className="text-sm text-gray-900 dark:text-white whitespace-pre-wrap overflow-x-auto">
          {JSON.stringify(reportData, null, 2)}
        </pre>
      </div>
    );
  };

  const formatCellValue = (value: any): string => {
    if (value === null || value === undefined) {
      return '-';
    }

    if (typeof value === 'number') {
      // Format numbers with commas
      return value.toLocaleString();
    }

    if (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}/)) {
      // Format dates
      return new Date(value).toLocaleDateString();
    }

    return String(value);
  };

  const prepareChartData = (data: any[]) => {
    if (!Array.isArray(data) || data.length === 0) {
      return [];
    }

    // Try to find the best fields for charting
    const firstItem = data[0];
    const keys = Object.keys(firstItem);

    // Look for common chart-friendly fields
    const nameField = keys.find(key =>
      key.toLowerCase().includes('name') ||
      key.toLowerCase().includes('description') ||
      key.toLowerCase().includes('category') ||
      key.toLowerCase().includes('type')
    ) || keys[0];

    const valueField = keys.find(key =>
      key.toLowerCase().includes('amount') ||
      key.toLowerCase().includes('value') ||
      key.toLowerCase().includes('balance') ||
      key.toLowerCase().includes('total')
    ) || keys.find(key => typeof firstItem[key] === 'number');

    if (!valueField) {
      // If no numeric field found, create a count chart
      const counts = data.reduce((acc, item) => {
        const key = item[nameField] || 'Unknown';
        acc[key] = (acc[key] || 0) + 1;
        return acc;
      }, {});

      return Object.entries(counts).map(([name, count]) => ({
        name,
        value: count,
      }));
    }

    // Group data by name field and sum values
    const grouped = data.reduce((acc, item) => {
      const key = item[nameField] || 'Unknown';
      const value = parseFloat(item[valueField]) || 0;

      if (acc[key]) {
        acc[key] += value;
      } else {
        acc[key] = value;
      }
      return acc;
    }, {});

    return Object.entries(grouped).map(([name, value]) => ({
      name,
      value: Math.abs(value as number), // Use absolute value for better visualization
    }));
  };

  const renderChart = (chartData: any[]) => {
    const chartConfig = {
      value: {
        label: "Value",
        color: "hsl(var(--chart-1))",
      },
    };

    const colors = [
      'hsl(var(--chart-1))',
      'hsl(var(--chart-2))',
      'hsl(var(--chart-3))',
      'hsl(var(--chart-4))',
      'hsl(var(--chart-5))',
    ];

    switch (chartType) {
      case 'bar':
        return (
          <ChartContainer config={chartConfig} className="h-[400px]">
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="name"
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Bar dataKey="value" fill="var(--color-value)" radius={4} />
            </BarChart>
          </ChartContainer>
        );

      case 'line':
        return (
          <ChartContainer config={chartConfig} className="h-[400px]">
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="name"
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Line
                type="monotone"
                dataKey="value"
                stroke="var(--color-value)"
                strokeWidth={2}
                dot={{ fill: "var(--color-value)", strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ChartContainer>
        );

      case 'pie':
        return (
          <ChartContainer config={chartConfig} className="h-[400px]">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={120}
                fill="#8884d8"
                dataKey="value"
              >
                {chartData.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              <ChartTooltip content={<ChartTooltipContent />} />
              <ChartLegend content={<ChartLegendContent />} />
            </PieChart>
          </ChartContainer>
        );

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Report not found</h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            The report you're looking for doesn't exist.
          </p>
          <button
            onClick={() => navigate('/reports')}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Reports
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/reports')}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Reports
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              {report.name}
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              {report.description}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={handleEdit}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Edit Report
          </button>
          <button
            onClick={runReport}
            disabled={running}
            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {running ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <PlayIcon className="h-4 w-4 mr-2" />
            )}
            {running ? 'Running...' : 'Run Report'}
          </button>
        </div>
      </div>

      {/* Report Info */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Report Information</h3>
        </div>
        <div className="px-6 py-4">
          <dl className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Type</dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-white">{report.type}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Data Source</dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-white">{report.dataSource}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
              <dd className="mt-1">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  report.status === 'ACTIVE' 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                    : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                }`}>
                  {report.status}
                </span>
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-white">{formatDate(report.createdAt)}</dd>
            </div>
            {report.lastRun && (
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Run</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">{formatDate(report.lastRun)}</dd>
              </div>
            )}
          </dl>
        </div>
      </div>

      {/* Report Results */}
      {reportData ? (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Report Results</h3>
              <div className="flex items-center space-x-2">
                <button className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                  <DownloadIcon className="h-4 w-4 mr-2" />
                  Export
                </button>
                <button className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                  <ShareIcon className="h-4 w-4 mr-2" />
                  Share
                </button>
              </div>
            </div>
          </div>
          <div className="px-6 py-4">
            {renderReportData()}
          </div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-12 text-center">
            <PlayIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No report data</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Click "Run Report" to generate the latest data.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
