import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  PlusIcon,
  ChartBarIcon,
  TableCellsIcon,
  DocumentChartBarIcon,
  CalendarIcon,
  PlayIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';
import { useNotification } from '../../contexts/NotificationContext';
import { reportService, type CustomReport } from '../../services/advancedReportService';

export default function AdvancedReports() {
  const [reports, setReports] = useState<CustomReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedReport, setSelectedReport] = useState<CustomReport | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [filters, setFilters] = useState({
    type: 'all',
    category: 'all',
    search: '',
  });

  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  const navigate = useNavigate();

  useEffect(() => {
    if (currentCompany) {
      loadReports();
    }
  }, [currentCompany, filters]);

  const loadReports = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      const data = await reportService.getReports(currentCompany.id, {
        reportType: filters.type !== 'all' ? filters.type : undefined,
        search: filters.search || undefined,
      });
      setReports(data.reports);
    } catch (error) {
      console.error('Failed to load reports:', error);
      showNotification({
        type: 'error',
        title: 'Failed to load reports',
        message: 'Could not load reports. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateReport = () => {
    navigate('/reports/new');
  };

  const handleEditReport = (report: CustomReport) => {
    navigate(`/reports/${report.id}/edit`);
  };

  const handleViewReport = (report: CustomReport) => {
    navigate(`/reports/${report.id}`);
  };

  const handleRunReport = async (report: CustomReport) => {
    if (!currentCompany) return;

    try {
      showNotification({
        type: 'info',
        title: 'Running report',
        message: `Generating ${report.name}...`,
      });

      const execution = await reportService.executeReport(currentCompany.id, report.id, {});
      
      showNotification({
        type: 'success',
        title: 'Report generated',
        message: `${report.name} has been generated successfully.`,
      });

      // Navigate to report results
      navigate(`/reports/${report.id}/results/${execution.id}`);
    } catch (error) {
      showNotification({
        type: 'error',
        title: 'Failed to run report',
        message: 'Could not generate the report.',
      });
    }
  };

  const handleDeleteReport = async () => {
    if (!currentCompany || !selectedReport) return;

    try {
      await reportService.deleteReport(currentCompany.id, selectedReport.id);
      
      showNotification({
        type: 'success',
        title: 'Report deleted',
        message: `${selectedReport.name} has been deleted.`,
      });
      
      setShowDeleteConfirm(false);
      setSelectedReport(null);
      loadReports();
    } catch (error) {
      showNotification({
        type: 'error',
        title: 'Failed to delete report',
        message: 'Could not delete report.',
      });
    }
  };

  const getReportTypeIcon = (reportType: string) => {
    const icons: Record<string, any> = {
      TABLE: TableCellsIcon,
      CHART: ChartBarIcon,
      SUMMARY: DocumentChartBarIcon,
    };
    const IconComponent = icons[reportType] || DocumentChartBarIcon;
    return <IconComponent className="h-5 w-5" />;
  };

  const getReportTypeBadge = (reportType: string) => {
    const badges: Record<string, string> = {
      TABLE: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
      CHART: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
      SUMMARY: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
    };
    return badges[reportType] || badges.TABLE;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Advanced Reports
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Create custom reports and analytics for your business data
          </p>
        </div>
        <button
          onClick={handleCreateReport}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          New Report
        </button>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button
          onClick={() => navigate('/reports/builder')}
          className="flex items-center p-6 bg-white dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
        >
          <ChartBarIcon className="h-8 w-8 text-primary-600 mr-4" />
          <div className="text-left">
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              Report Builder
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Create custom reports with drag & drop
            </div>
          </div>
        </button>

        <button
          onClick={() => navigate('/reports/templates')}
          className="flex items-center p-6 bg-white dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
        >
          <DocumentChartBarIcon className="h-8 w-8 text-green-600 mr-4" />
          <div className="text-left">
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              Report Templates
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Use pre-built report templates
            </div>
          </div>
        </button>

        <button
          onClick={() => navigate('/reports/scheduled')}
          className="flex items-center p-6 bg-white dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
        >
          <CalendarIcon className="h-8 w-8 text-purple-600 mr-4" />
          <div className="text-left">
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              Scheduled Reports
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Manage automated report delivery
            </div>
          </div>
        </button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentChartBarIcon className="h-6 w-6 text-blue-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Total Reports
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {reports.length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CalendarIcon className="h-6 w-6 text-green-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Scheduled
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {reports.filter(r => r.isScheduled).length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <PlayIcon className="h-6 w-6 text-purple-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Executions Today
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    24
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-6 w-6 text-yellow-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Avg. Runtime
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    2.3s
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Report Type
            </label>
            <select
              value={filters.type}
              onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Types</option>
              <option value="TABLE">Table Reports</option>
              <option value="CHART">Chart Reports</option>
              <option value="SUMMARY">Summary Reports</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Category
            </label>
            <select
              value={filters.category}
              onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Categories</option>
              <option value="financial">Financial</option>
              <option value="operational">Operational</option>
              <option value="compliance">Compliance</option>
              <option value="custom">Custom</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search
            </label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              placeholder="Search reports..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>
        </div>
      </div>

      {/* Reports List */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        {reports.length === 0 ? (
          <div className="text-center py-12">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              No reports found
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Get started by creating your first custom report.
            </p>
            <div className="mt-6">
              <button
                onClick={handleCreateReport}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                New Report
              </button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
            {reports.map((report) => (
              <div
                key={report.id}
                className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    {getReportTypeIcon(report.reportType)}
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                        {report.name}
                      </h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {report.description}
                      </p>
                    </div>
                  </div>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getReportTypeBadge(report.reportType)}`}>
                    {report.reportType}
                  </span>
                </div>

                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-4">
                  <span>Updated {new Date(report.updatedAt).toLocaleDateString()}</span>
                  {report.isScheduled && (
                    <span className="inline-flex items-center">
                      <CalendarIcon className="h-3 w-3 mr-1" />
                      Scheduled
                    </span>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleViewReport(report)}
                      className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      title="View report"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleEditReport(report)}
                      className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                      title="Edit report"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => {
                        setSelectedReport(report);
                        setShowDeleteConfirm(true);
                      }}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      title="Delete report"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                  <button
                    onClick={() => handleRunReport(report)}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900/20 dark:text-primary-400 dark:hover:bg-primary-900/30"
                  >
                    <PlayIcon className="h-3 w-3 mr-1" />
                    Run
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && selectedReport && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center p-4">
            <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setShowDeleteConfirm(false)} />
            <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
              <div className="flex items-center mb-4">
                <TrashIcon className="h-6 w-6 text-red-600 mr-3" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Delete Report
                </h3>
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                Are you sure you want to delete "{selectedReport.name}"? This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteReport}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
