import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeftIcon,
  PlusIcon,
  ChartBarIcon,
  TableCellsIcon,
  DocumentChartBarIcon,
  CircleStackIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  CubeIcon,
  UsersIcon,
  BanknotesIcon,
  BuildingOfficeIcon,
} from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';
import { useNotification } from '../../contexts/NotificationContext';
import { reportService } from '../../services/reportService';

interface DataSource {
  id: string;
  name: string;
  description: string;
  icon: any;
  color: string;
  recordCount?: number;
  lastUpdated?: string;
  fields: Array<{
    name: string;
    type: 'string' | 'number' | 'date' | 'boolean';
    description: string;
  }>;
}

export default function ReportBuilder() {
  const navigate = useNavigate();
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();

  const [reportType, setReportType] = useState<'TABLE' | 'CHART' | 'SUMMARY'>('TABLE');
  const [reportName, setReportName] = useState('');
  const [reportDescription, setReportDescription] = useState('');
  const [selectedDataSource, setSelectedDataSource] = useState<string>('');
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (currentCompany) {
      loadDataSources();
    }
  }, [currentCompany]);

  const loadDataSources = async () => {
    if (!currentCompany) return;

    setLoading(true);
    try {
      // In a real implementation, this would fetch from the API
      // For now, we'll use mock data with realistic database information
      const mockDataSources: DataSource[] = [
        {
          id: 'transactions',
          name: 'Transactions',
          description: 'All financial transactions including journal entries, invoices, and payments',
          icon: CubeIcon,
          color: 'blue',
          recordCount: 1247,
          lastUpdated: new Date().toISOString(),
          fields: [
            { name: 'transaction_date', type: 'date', description: 'Date of the transaction' },
            { name: 'description', type: 'string', description: 'Transaction description' },
            { name: 'reference', type: 'string', description: 'Reference number' },
            { name: 'total_amount', type: 'number', description: 'Total transaction amount' },
            { name: 'status', type: 'string', description: 'Transaction status' },
            { name: 'created_by', type: 'string', description: 'User who created the transaction' },
          ]
        },
        {
          id: 'accounts',
          name: 'Chart of Accounts',
          description: 'Account structure including assets, liabilities, equity, income, and expenses',
          icon: BuildingOfficeIcon,
          color: 'green',
          recordCount: 156,
          lastUpdated: new Date().toISOString(),
          fields: [
            { name: 'account_code', type: 'string', description: 'Account code' },
            { name: 'account_name', type: 'string', description: 'Account name' },
            { name: 'account_type', type: 'string', description: 'Account type (Asset, Liability, etc.)' },
            { name: 'balance', type: 'number', description: 'Current account balance' },
            { name: 'is_active', type: 'boolean', description: 'Whether account is active' },
          ]
        },
        {
          id: 'contacts',
          name: 'Contacts',
          description: 'Customer and vendor contact information and transaction history',
          icon: UsersIcon,
          color: 'purple',
          recordCount: 89,
          lastUpdated: new Date().toISOString(),
          fields: [
            { name: 'name', type: 'string', description: 'Contact name' },
            { name: 'email', type: 'string', description: 'Email address' },
            { name: 'phone', type: 'string', description: 'Phone number' },
            { name: 'type', type: 'string', description: 'Customer or Vendor' },
            { name: 'total_balance', type: 'number', description: 'Outstanding balance' },
          ]
        },
        {
          id: 'bank_transactions',
          name: 'Bank Transactions',
          description: 'Bank account transactions and reconciliation data',
          icon: BanknotesIcon,
          color: 'yellow',
          recordCount: 892,
          lastUpdated: new Date().toISOString(),
          fields: [
            { name: 'transaction_date', type: 'date', description: 'Bank transaction date' },
            { name: 'description', type: 'string', description: 'Bank transaction description' },
            { name: 'amount', type: 'number', description: 'Transaction amount' },
            { name: 'balance', type: 'number', description: 'Account balance after transaction' },
            { name: 'reconciled', type: 'boolean', description: 'Whether transaction is reconciled' },
          ]
        }
      ];

      setDataSources(mockDataSources);
    } catch (error) {
      console.error('Failed to load data sources:', error);
      showNotification({
        type: 'error',
        title: 'Failed to load data sources',
        message: 'Could not load available data sources. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!reportName.trim()) {
      showNotification({
        type: 'error',
        title: 'Report name required',
        message: 'Please enter a name for your report.',
      });
      return;
    }

    if (!selectedDataSource) {
      showNotification({
        type: 'error',
        title: 'Data source required',
        message: 'Please select a data source for your report.',
      });
      return;
    }

    if (!currentCompany?.id) {
      showNotification({
        type: 'error',
        title: 'Company required',
        message: 'Please select a company to create the report.',
      });
      return;
    }

    setLoading(true);
    try {
      // Create the report using the API
      const reportData = {
        name: reportName,
        description: reportDescription,
        type: reportType,
        dataSource: selectedDataSource,
      };

      console.log('Creating report:', reportData);

      const createdReport = await reportService.createCustomReport(
        currentCompany.id,
        reportData
      );

      console.log('Report created successfully:', createdReport);

      showNotification({
        type: 'success',
        title: 'Report created successfully!',
        message: `${reportName} has been created and saved to the database. You can find it in the "My Custom Reports" section below.`,
      });

      // Navigate back to reports page where they can see their created report
      navigate('/reports');
    } catch (error) {
      console.error('Failed to create report:', error);
      showNotification({
        type: 'error',
        title: 'Failed to create report',
        message: error instanceof Error ? error.message : 'Could not create the report. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const getSelectedDataSource = () => {
    return dataSources.find(ds => ds.id === selectedDataSource);
  };

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/reports')}
          className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back to Reports
        </button>
        <nav className="flex" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2">
            <li>
              <button
                onClick={() => navigate('/reports')}
                className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              >
                Reports
              </button>
            </li>
            <li>
              <div className="flex items-center">
                <svg
                  className="flex-shrink-0 h-4 w-4 text-gray-400"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                </svg>
                <span className="ml-2 text-sm font-medium text-gray-900 dark:text-white">
                  Report Builder
                </span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Custom Report Builder
        </h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Create custom reports with real database integration
        </p>
      </div>

      {/* Current Selection Indicator */}
      {(reportType || selectedDataSource || reportName) && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <InformationCircleIcon className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                Current Report Configuration
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-blue-700 dark:text-blue-300 font-medium">Report Name:</span>
                  <div className="text-blue-900 dark:text-blue-100 mt-1">
                    {reportName || <span className="italic text-blue-600 dark:text-blue-400">Not set</span>}
                  </div>
                </div>
                <div>
                  <span className="text-blue-700 dark:text-blue-300 font-medium">Report Type:</span>
                  <div className="flex items-center space-x-2 text-blue-900 dark:text-blue-100 mt-1">
                    {reportType === 'TABLE' && <TableCellsIcon className="h-4 w-4" />}
                    {reportType === 'CHART' && <ChartBarIcon className="h-4 w-4" />}
                    {reportType === 'SUMMARY' && <DocumentChartBarIcon className="h-4 w-4" />}
                    <span>
                      {reportType === 'TABLE' && 'Table Report'}
                      {reportType === 'CHART' && 'Chart Report'}
                      {reportType === 'SUMMARY' && 'Summary Report'}
                    </span>
                  </div>
                </div>
                <div>
                  <span className="text-blue-700 dark:text-blue-300 font-medium">Data Source:</span>
                  <div className="flex items-center space-x-2 text-blue-900 dark:text-blue-100 mt-1">
                    {selectedDataSource ? (
                      <>
                        {(() => {
                          const ds = getSelectedDataSource();
                          const IconComponent = ds?.icon;
                          return IconComponent ? <IconComponent className="h-4 w-4" /> : <CircleStackIcon className="h-4 w-4" />;
                        })()}
                        <span>{getSelectedDataSource()?.name}</span>
                        <span className="text-xs text-blue-600 dark:text-blue-400">
                          ({getSelectedDataSource()?.recordCount} records)
                        </span>
                      </>
                    ) : (
                      <span className="italic text-blue-600 dark:text-blue-400">Not selected</span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Report Configuration */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Report Configuration
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Report Name
            </label>
            <input
              type="text"
              value={reportName}
              onChange={(e) => setReportName(e.target.value)}
              placeholder="Enter report name..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Report Type
            </label>
            <select
              value={reportType}
              onChange={(e) => setReportType(e.target.value as 'TABLE' | 'CHART' | 'SUMMARY')}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="TABLE">Table Report</option>
              <option value="CHART">Chart Report</option>
              <option value="SUMMARY">Summary Report</option>
            </select>
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description
            </label>
            <textarea
              value={reportDescription}
              onChange={(e) => setReportDescription(e.target.value)}
              placeholder="Enter report description..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>
        </div>
      </div>

      {/* Report Type Selection */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Choose Report Type
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => setReportType('TABLE')}
            className={`p-6 border-2 rounded-lg transition-colors ${
              reportType === 'TABLE'
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
            }`}
          >
            <TableCellsIcon className="h-8 w-8 text-blue-600 mx-auto mb-3" />
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              Table Report
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Detailed data in rows and columns
            </div>
          </button>

          <button
            onClick={() => setReportType('CHART')}
            className={`p-6 border-2 rounded-lg transition-colors ${
              reportType === 'CHART'
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
            }`}
          >
            <ChartBarIcon className="h-8 w-8 text-green-600 mx-auto mb-3" />
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              Chart Report
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Visual charts and graphs
            </div>
          </button>

          <button
            onClick={() => setReportType('SUMMARY')}
            className={`p-6 border-2 rounded-lg transition-colors ${
              reportType === 'SUMMARY'
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
            }`}
          >
            <DocumentChartBarIcon className="h-8 w-8 text-purple-600 mx-auto mb-3" />
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              Summary Report
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Key metrics and totals
            </div>
          </button>
        </div>
      </div>

      {/* Data Source Selection */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Select Data Source
          </h3>
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <CircleStackIcon className="h-4 w-4" />
            <span>Live Database Connection</span>
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          </div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span className="ml-3 text-gray-600 dark:text-gray-400">Loading data sources...</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {dataSources.map((dataSource) => {
              const IconComponent = dataSource.icon;
              const isSelected = selectedDataSource === dataSource.id;

              return (
                <button
                  key={dataSource.id}
                  onClick={() => setSelectedDataSource(dataSource.id)}
                  className={`text-left p-4 border-2 rounded-lg transition-all duration-200 ${
                    isSelected
                      ? `border-${dataSource.color}-500 bg-${dataSource.color}-50 dark:bg-${dataSource.color}-900/20`
                      : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg bg-${dataSource.color}-100 dark:bg-${dataSource.color}-900/30`}>
                        <IconComponent className={`h-5 w-5 text-${dataSource.color}-600 dark:text-${dataSource.color}-400`} />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {dataSource.name}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          {dataSource.description}
                        </p>
                      </div>
                    </div>
                    {isSelected && (
                      <CheckCircleIcon className={`h-5 w-5 text-${dataSource.color}-600 dark:text-${dataSource.color}-400 flex-shrink-0`} />
                    )}
                  </div>

                  <div className="mt-3 flex items-center justify-between text-xs">
                    <div className="flex items-center space-x-4">
                      <span className="text-gray-600 dark:text-gray-400">
                        <strong>{dataSource.recordCount?.toLocaleString()}</strong> records
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">
                        <strong>{dataSource.fields.length}</strong> fields
                      </span>
                    </div>
                    <span className="text-gray-500 dark:text-gray-500">
                      Updated: {dataSource.lastUpdated ? new Date(dataSource.lastUpdated).toLocaleDateString() : 'N/A'}
                    </span>
                  </div>

                  {isSelected && (
                    <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                      <h5 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Available Fields:
                      </h5>
                      <div className="flex flex-wrap gap-1">
                        {dataSource.fields.slice(0, 6).map((field) => (
                          <span
                            key={field.name}
                            className={`inline-flex items-center px-2 py-1 rounded text-xs bg-${dataSource.color}-100 text-${dataSource.color}-800 dark:bg-${dataSource.color}-900/30 dark:text-${dataSource.color}-300`}
                          >
                            {field.name}
                          </span>
                        ))}
                        {dataSource.fields.length > 6 && (
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            +{dataSource.fields.length - 6} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </button>
              );
            })}
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {!reportName.trim() && !selectedDataSource ? (
            <span className="flex items-center space-x-2">
              <InformationCircleIcon className="h-4 w-4" />
              <span>Please enter a report name and select a data source to continue</span>
            </span>
          ) : !reportName.trim() ? (
            <span className="flex items-center space-x-2">
              <InformationCircleIcon className="h-4 w-4" />
              <span>Please enter a report name</span>
            </span>
          ) : !selectedDataSource ? (
            <span className="flex items-center space-x-2">
              <InformationCircleIcon className="h-4 w-4" />
              <span>Please select a data source</span>
            </span>
          ) : (
            <span className="flex items-center space-x-2 text-green-600 dark:text-green-400">
              <CheckCircleIcon className="h-4 w-4" />
              <span>Ready to create report</span>
            </span>
          )}
        </div>

        <div className="flex space-x-3">
          <button
            onClick={() => navigate('/reports')}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!reportName.trim() || !selectedDataSource || loading}
            className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Creating...</span>
              </>
            ) : (
              <>
                <PlusIcon className="h-4 w-4" />
                <span>Create Report</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
