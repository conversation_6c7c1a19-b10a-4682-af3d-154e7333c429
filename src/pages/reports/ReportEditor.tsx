import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeftIcon, CheckIcon } from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';
import { useNotification } from '../../contexts/NotificationContext';
import { reportService } from '../../services/reportService';

interface CustomReport {
  id: string;
  name: string;
  description: string;
  type: 'TABLE' | 'CHART' | 'SUMMARY';
  dataSource: string;
  createdAt: string;
  lastRun?: string;
  status: 'ACTIVE' | 'DRAFT';
}

export default function ReportEditor() {
  const { reportId } = useParams<{ reportId: string }>();
  const navigate = useNavigate();
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  
  const [report, setReport] = useState<CustomReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  // Form state
  const [reportName, setReportName] = useState('');
  const [reportDescription, setReportDescription] = useState('');
  const [reportType, setReportType] = useState<'TABLE' | 'CHART' | 'SUMMARY'>('TABLE');
  const [selectedDataSource, setSelectedDataSource] = useState('');
  const [reportStatus, setReportStatus] = useState<'ACTIVE' | 'DRAFT'>('ACTIVE');

  const dataSources = [
    { id: 'transactions', name: 'Transactions', description: 'All transaction data', recordCount: 1250 },
    { id: 'accounts', name: 'Chart of Accounts', description: 'Account structure and balances', recordCount: 45 },
    { id: 'contacts', name: 'Contacts', description: 'Customers and vendors', recordCount: 89 },
    { id: 'invoices', name: 'Invoices', description: 'Sales and purchase invoices', recordCount: 234 },
    { id: 'bank_transactions', name: 'Bank Transactions', description: 'Bank statement data', recordCount: 567 },
  ];

  useEffect(() => {
    if (currentCompany && reportId) {
      loadReport();
    }
  }, [currentCompany, reportId]);

  const loadReport = async () => {
    if (!currentCompany || !reportId) return;

    try {
      const reportData = await reportService.getCustomReport(currentCompany.id, reportId);
      setReport(reportData);
      
      // Populate form fields
      setReportName(reportData.name);
      setReportDescription(reportData.description);
      setReportType(reportData.type);
      setSelectedDataSource(reportData.dataSource);
      setReportStatus(reportData.status);
    } catch (error) {
      console.error('Failed to load report:', error);
      showNotification({
        type: 'error',
        title: 'Failed to load report',
        message: 'Could not load the report. Please try again.',
      });
      navigate('/reports');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!reportName.trim()) {
      showNotification({
        type: 'error',
        title: 'Report name required',
        message: 'Please enter a name for your report.',
      });
      return;
    }

    if (!selectedDataSource) {
      showNotification({
        type: 'error',
        title: 'Data source required',
        message: 'Please select a data source for your report.',
      });
      return;
    }

    if (!currentCompany || !reportId) {
      showNotification({
        type: 'error',
        title: 'Invalid request',
        message: 'Missing company or report information.',
      });
      return;
    }

    setSaving(true);
    try {
      const updateData = {
        name: reportName,
        description: reportDescription,
        type: reportType,
        dataSource: selectedDataSource,
      };

      const updatedReport = await reportService.updateCustomReport(
        currentCompany.id,
        reportId,
        updateData
      );

      setReport(updatedReport);

      showNotification({
        type: 'success',
        title: 'Report updated successfully!',
        message: `${reportName} has been updated.`,
      });

      // Navigate back to reports page
      navigate('/reports');
    } catch (error) {
      console.error('Failed to update report:', error);
      showNotification({
        type: 'error',
        title: 'Failed to update report',
        message: error instanceof Error ? error.message : 'Could not update the report. Please try again.',
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Report not found</h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            The report you're trying to edit doesn't exist.
          </p>
          <button
            onClick={() => navigate('/reports')}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Reports
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/reports')}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Reports
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              Edit Report
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Modify your custom report configuration
            </p>
          </div>
        </div>
        
        <button
          onClick={handleSave}
          disabled={saving}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {saving ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          ) : (
            <CheckIcon className="h-4 w-4 mr-2" />
          )}
          {saving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>

      {/* Edit Form */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Report Configuration</h3>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Update the basic settings for your custom report
          </p>
        </div>
        
        <div className="px-6 py-4 space-y-6">
          {/* Report Name */}
          <div>
            <label htmlFor="reportName" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Report Name *
            </label>
            <input
              type="text"
              id="reportName"
              value={reportName}
              onChange={(e) => setReportName(e.target.value)}
              className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter report name"
            />
          </div>

          {/* Report Description */}
          <div>
            <label htmlFor="reportDescription" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Description
            </label>
            <textarea
              id="reportDescription"
              rows={3}
              value={reportDescription}
              onChange={(e) => setReportDescription(e.target.value)}
              className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              placeholder="Describe what this report shows"
            />
          </div>

          {/* Report Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Report Type *
            </label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[
                { value: 'TABLE', label: 'Table Report', description: 'Structured data in rows and columns' },
                { value: 'CHART', label: 'Chart Report', description: 'Visual charts and graphs' },
                { value: 'SUMMARY', label: 'Summary Report', description: 'Key metrics and totals' },
              ].map((type) => (
                <div
                  key={type.value}
                  className={`relative rounded-lg border p-4 cursor-pointer ${
                    reportType === type.value
                      ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                      : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                  }`}
                  onClick={() => setReportType(type.value as 'TABLE' | 'CHART' | 'SUMMARY')}
                >
                  <div className="flex items-center">
                    <input
                      type="radio"
                      name="reportType"
                      value={type.value}
                      checked={reportType === type.value}
                      onChange={() => setReportType(type.value as 'TABLE' | 'CHART' | 'SUMMARY')}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <div className="ml-3">
                      <label className="block text-sm font-medium text-gray-900 dark:text-white">
                        {type.label}
                      </label>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{type.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Data Source */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Data Source *
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {dataSources.map((source) => (
                <div
                  key={source.id}
                  className={`relative rounded-lg border p-4 cursor-pointer ${
                    selectedDataSource === source.id
                      ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                      : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                  }`}
                  onClick={() => setSelectedDataSource(source.id)}
                >
                  <div className="flex items-start">
                    <input
                      type="radio"
                      name="dataSource"
                      value={source.id}
                      checked={selectedDataSource === source.id}
                      onChange={() => setSelectedDataSource(source.id)}
                      className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <div className="ml-3 flex-1">
                      <label className="block text-sm font-medium text-gray-900 dark:text-white">
                        {source.name}
                      </label>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{source.description}</p>
                      <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                        {source.recordCount.toLocaleString()} records
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
