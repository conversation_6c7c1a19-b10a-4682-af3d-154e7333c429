import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ArrowLeftIcon, CalendarIcon, CheckCircleIcon } from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { reportService } from "../../services/reportService";
import type { CustomReport } from "../../services/reportService";

export default function ScheduleReport() {
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  const navigate = useNavigate();
  const { reportId } = useParams<{ reportId: string }>();

  const [report, setReport] = useState<CustomReport | null>(null);
  const [loadingReport, setLoadingReport] = useState<boolean>(true);
  const [frequency, setFrequency] = useState<string>("daily");
  const [deliveryMethod, setDeliveryMethod] = useState<string>("email");
  const [recipientEmail, setRecipientEmail] = useState<string>("");
  const [submitting, setSubmitting] = useState<boolean>(false);

  useEffect(() => {
    if (currentCompany && reportId) {
      loadReportDetails();
    }
  }, [currentCompany, reportId]);

  const loadReportDetails = async () => {
    if (!currentCompany || !reportId) return;

    setLoadingReport(true);
    try {
      const reportData = await reportService.getCustomReport(currentCompany.id, reportId);
      setReport(reportData);
      if (reportData.schedule_frequency) {
        setFrequency(reportData.schedule_frequency);
      }
      if (reportData.schedule_delivery) {
        setDeliveryMethod(reportData.schedule_delivery);
      }
      if (reportData.schedule_recipient) {
        setRecipientEmail(reportData.schedule_recipient);
      }
    } catch (error) {
      console.error("Failed to load report details:", error);
      showNotification({
        type: "error",
        title: "Error",
        message: "Failed to load report details. Please try again.",
      });
    } finally {
      setLoadingReport(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentCompany || !report) return;

    if (deliveryMethod === "email" && !recipientEmail) {
      showNotification({
        type: "error",
        title: "Error",
        message: "Please provide a recipient email address.",
      });
      return;
    }

    setSubmitting(true);
    try {
      await reportService.scheduleCustomReport(
        currentCompany.id,
        report.id,
        frequency,
        deliveryMethod,
        recipientEmail || undefined
      );
      showNotification({
        type: "success",
        title: "Success",
        message: `Report "${report.name}" has been scheduled successfully.`,
      });
      navigate(`/reports/view/${report.id}`);
    } catch (error) {
      console.error("Failed to schedule report:", error);
      showNotification({
        type: "error",
        title: "Error",
        message: "Failed to schedule report. Please try again.",
      });
    } finally {
      setSubmitting(false);
    }
  };

  if (loadingReport) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Report not found</h3>
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          The specified report could not be found or loaded.
        </p>
        <div className="mt-6">
          <button
            onClick={() => navigate('/reports/advanced')}
            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
          >
            Back to Reports
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Schedule Report: {report.name}
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Set up automated delivery for this custom report
          </p>
        </div>
        <button
          onClick={() => navigate(`/reports/view/${report.id}`)}
          className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back to Report
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Schedule Settings
          </h2>
        </div>
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="space-y-4">
            <div>
              <label htmlFor="frequency" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Frequency
              </label>
              <div className="mt-1">
                <select
                  id="frequency"
                  name="frequency"
                  value={frequency}
                  onChange={(e) => setFrequency(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md dark:bg-gray-800 dark:text-white"
                >
                  <option value="daily">Daily (Every day at 9 AM)</option>
                  <option value="weekly">Weekly (Every Monday at 9 AM)</option>
                  <option value="monthly">Monthly (First day of every month at 9 AM)</option>
                </select>
              </div>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Choose how often this report should be generated and delivered.
              </p>
            </div>

            <div>
              <label htmlFor="deliveryMethod" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Delivery Method
              </label>
              <div className="mt-1">
                <select
                  id="deliveryMethod"
                  name="deliveryMethod"
                  value={deliveryMethod}
                  onChange={(e) => setDeliveryMethod(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md dark:bg-gray-800 dark:text-white"
                >
                  <option value="email">Email</option>
                  <option value="in-app">In-App Notification</option>
                </select>
              </div>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Select how you want to receive the report results.
              </p>
            </div>

            {deliveryMethod === "email" && (
              <div>
                <label htmlFor="recipientEmail" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Recipient Email
                </label>
                <div className="mt-1">
                  <input
                    type="email"
                    id="recipientEmail"
                    name="recipientEmail"
                    value={recipientEmail}
                    onChange={(e) => setRecipientEmail(e.target.value)}
                    required
                    className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-800 dark:text-white"
                    placeholder="<EMAIL>"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Enter the email address where the report should be sent.
                </p>
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => navigate(`/reports/view/${report.id}`)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 focus:outline-none hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={submitting}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {submitting ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <CalendarIcon className="h-4 w-4 mr-2" />
              )}
              {submitting ? "Scheduling..." : "Schedule Report"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
