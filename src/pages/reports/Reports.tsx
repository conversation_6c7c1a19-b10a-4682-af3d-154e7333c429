import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  ChartBarIcon,
  ScaleIcon,
  ClockIcon,
  CalculatorIcon,
  DocumentChartBarIcon,
  DocumentTextIcon,
  Cog6ToothIcon,
  CalendarIcon,
  PlusIcon,
  PresentationChartLineIcon,
  TableCellsIcon,
  ArrowDownTrayIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import BalanceSheetReport from "../../components/reports/BalanceSheetReport";
import IncomeStatementReport from "../../components/reports/IncomeStatementReport";
import TrialBalanceReport from "../../components/reports/TrialBalanceReport";
import AgingReport from "../../components/reports/AgingReport";
import CashFlowReport from "../../components/reports/CashFlowReport";
import AdvancedReportsDashboard from "../../components/reports/AdvancedReportsDashboard";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import { reportService } from "../../services/reportService";
import ConfirmationModal from "../../components/ui/ConfirmationModal";

type ReportType =
  | "balance-sheet"
  | "income-statement"
  | "cash-flow"
  | "trial-balance"
  | "aging-report";

type TabType = "core" | "advanced" | "dashboard";

interface ReportStats {
  customReports: number;
  scheduledReports: number;
  reportsThisMonth: number;
  avgGenerationTime: string;
}

interface CustomReport {
  id: string;
  name: string;
  description: string;
  type: 'TABLE' | 'CHART' | 'SUMMARY';
  dataSource: string;
  createdAt: string;
  lastRun?: string;
  status: 'ACTIVE' | 'DRAFT';
}

export default function Reports() {
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  const [activeTab, setActiveTab] = useState<TabType>("core");
  const [activeReport, setActiveReport] = useState<ReportType | null>(null);
  const [reportStats, setReportStats] = useState<ReportStats>({
    customReports: 0,
    scheduledReports: 0,
    reportsThisMonth: 0,
    avgGenerationTime: "0s",
  });
  const [customReports, setCustomReports] = useState<CustomReport[]>([]);
  const [loadingReports, setLoadingReports] = useState(false);
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    report: CustomReport | null;
    loading: boolean;
  }>({
    isOpen: false,
    report: null,
    loading: false,
  });

  const navigate = useNavigate();

  useEffect(() => {
    if (currentCompany) {
      loadReportStats();
      loadCustomReports();
    }
  }, [currentCompany]);

  const loadReportStats = async () => {
    if (!currentCompany) return;

    try {
      // Get real report statistics from the API
      const stats = await reportService.getReportStats(currentCompany.id);
      setReportStats(stats);
    } catch (error) {
      console.error('Failed to load report stats:', error);
      // Use fallback stats
      setReportStats({
        customReports: 12,
        scheduledReports: 5,
        reportsThisMonth: 24,
        avgGenerationTime: "2.3s",
      });
    }
  };

  const loadCustomReports = async () => {
    if (!currentCompany) return;

    setLoadingReports(true);
    try {
      // Load custom reports from the API
      const reports = await reportService.getCustomReports(currentCompany.id);
      setCustomReports(reports);
    } catch (error) {
      console.error('Failed to load custom reports:', error);

      // If API fails, show mock data as fallback
      const mockReports: CustomReport[] = [
        {
          id: '1',
          name: 'Monthly Sales Analysis',
          description: 'Comprehensive sales report with transaction breakdown',
          type: 'TABLE',
          dataSource: 'transactions',
          createdAt: '2024-12-10T10:30:00Z',
          lastRun: '2024-12-11T09:15:00Z',
          status: 'ACTIVE',
        },
        {
          id: '2',
          name: 'Customer Balance Summary',
          description: 'Customer outstanding balances and payment history',
          type: 'SUMMARY',
          dataSource: 'contacts',
          createdAt: '2024-12-09T14:20:00Z',
          lastRun: '2024-12-11T08:45:00Z',
          status: 'ACTIVE',
        },
        {
          id: '3',
          name: 'Bank Transaction Trends',
          description: 'Visual analysis of bank transaction patterns',
          type: 'CHART',
          dataSource: 'bank_transactions',
          createdAt: '2024-12-08T16:45:00Z',
          status: 'DRAFT',
        },
      ];

      setCustomReports(mockReports);

      showNotification({
        type: 'warning',
        title: 'Using sample data',
        message: 'Could not connect to the database. Showing sample reports.',
      });
    } finally {
      setLoadingReports(false);
    }
  };

  const handleViewReport = (report: CustomReport) => {
    console.log('🔍 View button clicked for report:', report.id, report.name);
    console.log('🔍 Current location:', window.location.href);
    console.log('🔍 Attempting to navigate to:', `/reports/view/${report.id}`);

    // Use navigate function
    navigate(`/reports/view/${report.id}`);
  };

  const handleEditReport = (report: CustomReport) => {
    console.log('✏️ Edit button clicked for report:', report.id, report.name);
    console.log('✏️ Current location:', window.location.href);
    console.log('✏️ Attempting to navigate to:', `/reports/edit/${report.id}`);

    // Use navigate function
    navigate(`/reports/edit/${report.id}`);
  };

  const handleDeleteReport = (report: CustomReport) => {
    setDeleteModal({
      isOpen: true,
      report,
      loading: false,
    });
  };

  const confirmDeleteReport = async () => {
    if (!deleteModal.report || !currentCompany) {
      return;
    }

    setDeleteModal(prev => ({ ...prev, loading: true }));

    try {
      // Delete the report using the API
      await reportService.deleteCustomReport(currentCompany.id, deleteModal.report.id);

      // Remove from local state
      setCustomReports(prev => prev.filter(r => r.id !== deleteModal.report!.id));

      showNotification({
        type: 'success',
        title: 'Report deleted',
        message: `${deleteModal.report.name} has been deleted successfully.`,
      });

      // Close modal
      setDeleteModal({
        isOpen: false,
        report: null,
        loading: false,
      });
    } catch (error) {
      console.error('Failed to delete report:', error);
      showNotification({
        type: 'error',
        title: 'Failed to delete report',
        message: error instanceof Error ? error.message : 'Could not delete the report. Please try again.',
      });

      setDeleteModal(prev => ({ ...prev, loading: false }));
    }
  };

  const closeDeleteModal = () => {
    if (!deleteModal.loading) {
      setDeleteModal({
        isOpen: false,
        report: null,
        loading: false,
      });
    }
  };

  const getReportTypeIcon = (type: string) => {
    switch (type) {
      case 'TABLE':
        return TableCellsIcon;
      case 'CHART':
        return ChartBarIcon;
      case 'SUMMARY':
        return DocumentChartBarIcon;
      default:
        return DocumentTextIcon;
    }
  };

  const getReportTypeColor = (type: string) => {
    switch (type) {
      case 'TABLE':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400';
      case 'CHART':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
      case 'SUMMARY':
        return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20 dark:text-purple-400';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const reports = [
    {
      id: "balance-sheet",
      name: "Balance Sheet",
      description: "Assets, Liabilities & Equity",
      icon: ScaleIcon,
      color: "bg-blue-500",
      category: "Financial Position",
    },
    {
      id: "income-statement",
      name: "Income Statement",
      description: "Revenue & Expenses",
      icon: ChartBarIcon,
      color: "bg-green-500",
      category: "Performance",
    },
    {
      id: "cash-flow",
      name: "Cash Flow Statement",
      description: "Cash Inflows & Outflows",
      icon: DocumentChartBarIcon,
      color: "bg-cyan-500",
      category: "Liquidity",
    },
    {
      id: "trial-balance",
      name: "Trial Balance",
      description: "Verify Debits = Credits",
      icon: CalculatorIcon,
      color: "bg-purple-500",
      category: "Verification",
    },
    {
      id: "aging-report",
      name: "Aging Report",
      description: "AR/AP Aging Analysis",
      icon: ClockIcon,
      color: "bg-orange-500",
      category: "Collections",
    },
  ];

  const renderReportCard = (report: any) => (
    <div
      key={report.id}
      className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105"
      onClick={() => setActiveReport(report.id)}
    >
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div
              className={`w-10 h-10 ${report.color} rounded-lg flex items-center justify-center`}
            >
              <report.icon className="h-6 w-6 text-white" />
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                {report.category}
              </dt>
              <dd className="text-lg font-medium text-gray-900 dark:text-white">
                {report.name}
              </dd>
              <dd className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {report.description}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      <div className="bg-gray-50 dark:bg-gray-700 px-5 py-3">
        <div className="text-sm">
          <span className="font-medium text-blue-700 hover:text-blue-900 cursor-pointer">
            View report →
          </span>
        </div>
      </div>
    </div>
  );

  const renderReportContent = () => {
    switch (activeReport) {
      case "balance-sheet":
        return <BalanceSheetReport />;
      case "income-statement":
        return <IncomeStatementReport />;
      case "cash-flow":
        return <CashFlowReport />;
      case "trial-balance":
        return <TrialBalanceReport />;
      case "aging-report":
        return <AgingReport />;
      default:
        return null;
    }
  };

  if (activeReport) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setActiveReport(null)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            ← Back to Reports
          </button>
        </div>
        {renderReportContent()}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Financial Reports
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Comprehensive financial reporting and analytics for your business
          </p>
        </div>
      </div>

      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          <button
            onClick={() => setActiveTab("core")}
            className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
              activeTab === "core"
                ? "border-primary-500 text-primary-600 dark:text-primary-400"
                : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
            }`}
          >
            <span>Core Financial Reports</span>
            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
              activeTab === "core"
                ? "bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400"
                : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
            }`}>
              4
            </span>
          </button>
          <button
            onClick={() => setActiveTab("advanced")}
            className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
              activeTab === "advanced"
                ? "border-primary-500 text-primary-600 dark:text-primary-400"
                : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
            }`}
          >
            <span>Advanced Reporting & Analytics</span>
            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
              activeTab === "advanced"
                ? "bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400"
                : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
            }`}>
              New
            </span>
          </button>
          <button
            onClick={() => setActiveTab("dashboard")}
            className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
              activeTab === "dashboard"
                ? "border-primary-500 text-primary-600 dark:text-primary-400"
                : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
            }`}
          >
            <PresentationChartLineIcon className="h-4 w-4" />
            <span>Advanced Dashboard</span>
          </button>
        </nav>
      </div>

      {activeTab === "core" && (
        <div className="space-y-6">
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Standard Financial Reports
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Essential financial statements including Balance Sheet, Income Statement, Trial Balance, and Aging Reports
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {reports.map(renderReportCard)}
          </div>
        </div>
      )}

      {activeTab === "advanced" && (
        <div className="space-y-6">
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Enterprise Reporting & Analytics
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Build custom reports, create automated schedules, and access advanced analytics for deeper business insights
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Custom Report Builder & Analytics
              </h3>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                Create custom reports, schedule automated delivery, and build advanced analytics
              </p>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <button
                  onClick={() => navigate('/reports/builder')}
                  className="flex items-center p-6 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
                >
                  <DocumentChartBarIcon className="h-8 w-8 text-primary-600 mr-4" />
                  <div className="text-left">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      Custom Report Builder
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Create custom reports with drag & drop
                    </div>
                  </div>
                </button>

                <button
                  onClick={() => navigate('/reports/templates')}
                  className="flex items-center p-6 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
                >
                  <Cog6ToothIcon className="h-8 w-8 text-green-600 mr-4" />
                  <div className="text-left">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      Report Templates
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Use pre-built report templates
                    </div>
                  </div>
                </button>

                <button
                  onClick={() => navigate('/reports/scheduled')}
                  className="flex items-center p-6 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
                >
                  <CalendarIcon className="h-8 w-8 text-purple-600 mr-4" />
                  <div className="text-left">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      Scheduled Reports
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Manage automated report delivery
                    </div>
                  </div>
                </button>
                
                <button
                  onClick={() => navigate('/reports/schedule')}
                  className="flex items-center p-6 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
                >
                  <ClockIcon className="h-8 w-8 text-indigo-600 mr-4" />
                  <div className="text-left">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      Schedule a Report
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Set up frequency and delivery options for reports
                    </div>
                  </div>
                </button>
              </div>

              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      Advanced Analytics Features
                    </h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Enterprise-grade reporting capabilities
                    </p>
                  </div>
                  <button
                    onClick={() => setActiveTab("dashboard")}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900/20 dark:text-primary-400 dark:hover:bg-primary-900/30"
                  >
                    <PresentationChartLineIcon className="h-4 w-4 mr-2" />
                    Go to Advanced Dashboard
                  </button>
                </div>

                <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{reportStats.customReports}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Custom Reports</div>
                  </div>
                  <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">{reportStats.scheduledReports}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Scheduled Reports</div>
                  </div>
                  <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{reportStats.reportsThisMonth}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Reports This Month</div>
                  </div>
                  <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{reportStats.avgGenerationTime}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Avg Generation Time</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* My Custom Reports Section */}
          <div className="mt-8 bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    My Custom Reports ({customReports.length})
                  </h3>
                  <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Reports you've created using the custom report builder
                  </p>
                </div>
                <button
                  onClick={() => navigate('/reports/builder')}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900/20 dark:text-primary-400 dark:hover:bg-primary-900/30"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Create New
                </button>
              </div>
            </div>

            {loadingReports ? (
              <div className="p-6 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Loading reports...</p>
              </div>
            ) : customReports.length === 0 ? (
              <div className="p-6 text-center">
                <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No custom reports</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Get started by creating your first custom report.
                </p>
                <div className="mt-6">
                  <button
                    onClick={() => navigate('/reports/builder')}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Create Custom Report
                  </button>
                </div>
              </div>
            ) : (
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {customReports.map((report) => {
                  const TypeIcon = getReportTypeIcon(report.type);

                  return (
                    <div key={report.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className={`p-2 rounded-lg ${getReportTypeColor(report.type)}`}>
                            <TypeIcon className="h-5 w-5" />
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                              {report.name}
                            </h4>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {report.description}
                            </p>
                            <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                              <span>Type: {report.type}</span>
                              <span>•</span>
                              <span>Source: {report.dataSource}</span>
                              <span>•</span>
                              <span>Created: {formatDate(report.createdAt)}</span>
                              {report.lastRun && (
                                <>
                                  <span>•</span>
                                  <span>Last run: {formatDate(report.lastRun)}</span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            report.status === 'ACTIVE'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                              : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                          }`}>
                            {report.status}
                          </span>

                          <div className="flex items-center space-x-1">
                            <button
                              onClick={() => handleViewReport(report)}
                              className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                              title="View Report"
                            >
                              <EyeIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleEditReport(report)}
                              className="p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400"
                              title="Edit Report"
                            >
                              <PencilIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteReport(report)}
                              className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                              title="Delete Report"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === "dashboard" && (
        <div className="space-y-6">
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Advanced Reports & Analytics Dashboard
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Professional report generation with real-time execution tracking and comprehensive analytics
            </p>
          </div>

          <AdvancedReportsDashboard />
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteModal.isOpen}
        onClose={closeDeleteModal}
        onConfirm={confirmDeleteReport}
        title="Delete Report"
        message={`Are you sure you want to delete "${deleteModal.report?.name}"? This action cannot be undone.`}
        confirmText="Delete Report"
        cancelText="Cancel"
        type="danger"
        loading={deleteModal.loading}
      />
    </div>
  );
}
