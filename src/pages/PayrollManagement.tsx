import { useState, useEffect } from "react";
import {
  CalculatorIcon,
  UsersIcon,
  DocumentTextIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowDownTrayIcon,
  PlusIcon,
  CurrencyDollarIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../contexts/CompanyContext";
import { useNotification } from "../contexts/NotificationContext";
import { apiService } from "../services/api";
import PAYECalculator from "../components/payroll/PAYECalculator";
import SDLCalculator from "../components/payroll/SDLCalculator";
import EmployeeManagement from "../components/payroll/EmployeeManagement";
import PAYEReports from "../components/payroll/PAYEReports";
import SDLReports from "../components/payroll/SDLReports";
import PayrollStatistics from "../components/payroll/PayrollStatistics";
import StatutoryContributions from "../components/payroll/StatutoryContributions";

type PayrollSection = 'overview' | 'paye-calculator' | 'sdl-calculator' | 'statutory-contributions' | 'employees' | 'paye-reports' | 'sdl-reports' | 'statistics';

interface PayrollOverview {
  employees: {
    total: number;
    active: number;
    inactive: number;
    newHires: number;
  };
  paye: {
    thisMonth: number;
    lastMonth: number;
    yearToDate: number;
    averagePerEmployee: number;
  };
  sdl: {
    thisMonth: number;
    lastMonth: number;
    yearToDate: number;
    rate: number;
  };
  payroll: {
    totalGrossPayroll: number;
    totalDeductions: number;
    totalNetPayroll: number;
    averageSalary: number;
  };
  compliance: {
    payeSubmissions: number;
    sdlSubmissions: number;
    pendingSubmissions: number;
    complianceScore: number;
  };
  statutory: {
    nssf: {
      employee: number;
      employer: number;
    };
    wcf: number;
    nhif: number;
  };
}

export default function PayrollManagement() {
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();
  const [activeSection, setActiveSection] = useState<PayrollSection>('overview');
  const [overview, setOverview] = useState<PayrollOverview | null>(null);
  const [loading, setLoading] = useState(false);

  const payrollSections = [
    {
      id: 'overview' as const,
      name: 'Overview',
      description: 'Payroll management dashboard and statistics',
      icon: ChartBarIcon,
    },
    {
      id: 'paye-calculator' as const,
      name: 'PAYE Calculator',
      description: 'Calculate Pay As You Earn tax',
      icon: CalculatorIcon,
    },
    {
      id: 'sdl-calculator' as const,
      name: 'SDL Calculator',
      description: 'Calculate Skills Development Levy',
      icon: CurrencyDollarIcon,
    },
    {
      id: 'statutory-contributions' as const,
      name: 'Statutory Contributions',
      description: 'NSSF, WCF, and NHIF management',
      icon: DocumentTextIcon,
    },
    {
      id: 'employees' as const,
      name: 'Employees',
      description: 'Manage employee information',
      icon: UsersIcon,
    },
    {
      id: 'paye-reports' as const,
      name: 'PAYE Reports',
      description: 'PAYE calculations and submissions',
      icon: DocumentTextIcon,
    },
    {
      id: 'sdl-reports' as const,
      name: 'SDL Reports',
      description: 'SDL calculations and submissions',
      icon: DocumentTextIcon,
    },
    {
      id: 'statistics' as const,
      name: 'Statistics',
      description: 'Detailed payroll analytics',
      icon: ClockIcon,
    },
  ];

  useEffect(() => {
    if (currentCompany && activeSection === 'overview') {
      loadOverview();
    }
  }, [currentCompany, activeSection]);

  const loadOverview = async () => {
    if (!currentCompany) return;

    setLoading(true);
    try {
      const data = await apiService.get(`/payroll/${currentCompany.id}/statistics`);
      setOverview(data.data);
    } catch (error) {
      console.error('Error loading payroll overview:', error);
      addNotification('error', 'Load Error', 'Failed to load payroll overview');
    } finally {
      setLoading(false);
    }
  };

  const getComplianceScoreColor = (score: number) => {
    if (score >= 95) return 'text-green-600 dark:text-green-400';
    if (score >= 85) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getComplianceScoreBg = (score: number) => {
    if (score >= 95) return 'bg-green-100 dark:bg-green-900/20';
    if (score >= 85) return 'bg-yellow-100 dark:bg-yellow-900/20';
    return 'bg-red-100 dark:bg-red-900/20';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-TZ", {
      style: "currency",
      currency: "TZS",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">Please select a company to view payroll management.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Payroll Management
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Employee payroll, taxes, and statutory contributions for {currentCompany.name}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Last updated: {new Date().toLocaleString()}
          </span>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {payrollSections.map((section) => {
            const Icon = section.icon;
            return (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                  activeSection === section.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Icon className="h-5 w-5" />
                  <span>{section.name}</span>
                </div>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        {/* Overview Section */}
        {activeSection === 'overview' && (
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Payroll Management Overview
              </h3>
              <button
                onClick={loadOverview}
                disabled={loading}
                className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 text-sm transition-colors duration-200"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                {loading ? 'Refreshing...' : 'Refresh'}
              </button>
            </div>

            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : overview ? (
              <div className="space-y-8">
                {/* Top Row - Key Metrics */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {/* Employees Card */}
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Employees</p>
                        <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                          {overview.employees.total}
                        </p>
                      </div>
                      <UsersIcon className="h-8 w-8 text-blue-500" />
                    </div>
                    <div className="mt-4 space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-blue-700 dark:text-blue-300">Active:</span>
                        <span className="font-medium text-blue-900 dark:text-blue-100">
                          {overview.employees.active}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-blue-700 dark:text-blue-300">New Hires:</span>
                        <span className="font-medium text-blue-900 dark:text-blue-100">
                          {overview.employees.newHires}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* PAYE Card */}
                  <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-6 border border-green-200 dark:border-green-800">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-green-600 dark:text-green-400">PAYE This Month</p>
                        <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                          {formatCurrency(overview.paye.thisMonth)}
                        </p>
                      </div>
                      <CalculatorIcon className="h-8 w-8 text-green-500" />
                    </div>
                    <div className="mt-4 space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-green-700 dark:text-green-300">YTD:</span>
                        <span className="font-medium text-green-900 dark:text-green-100">
                          {formatCurrency(overview.paye.yearToDate)}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-green-700 dark:text-green-300">Avg/Employee:</span>
                        <span className="font-medium text-green-900 dark:text-green-100">
                          {formatCurrency(overview.paye.averagePerEmployee)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* SDL Card */}
                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-6 border border-purple-200 dark:border-purple-800">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-purple-600 dark:text-purple-400">SDL This Month</p>
                        <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                          {formatCurrency(overview.sdl.thisMonth)}
                        </p>
                      </div>
                      <CurrencyDollarIcon className="h-8 w-8 text-purple-500" />
                    </div>
                    <div className="mt-4 space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-purple-700 dark:text-purple-300">Rate:</span>
                        <span className="font-medium text-purple-900 dark:text-purple-100">
                          {overview.sdl.rate}%
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-purple-700 dark:text-purple-300">YTD:</span>
                        <span className="font-medium text-purple-900 dark:text-purple-100">
                          {formatCurrency(overview.sdl.yearToDate)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Compliance Score Card */}
                  <div className={`rounded-lg p-6 border ${getComplianceScoreBg(overview.compliance.complianceScore)}`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Compliance Score</p>
                        <p className={`text-2xl font-bold ${getComplianceScoreColor(overview.compliance.complianceScore)}`}>
                          {overview.compliance.complianceScore}%
                        </p>
                      </div>
                      {overview.compliance.complianceScore >= 95 ? (
                        <CheckCircleIcon className="h-8 w-8 text-green-500" />
                      ) : (
                        <ExclamationTriangleIcon className="h-8 w-8 text-yellow-500" />
                      )}
                    </div>
                    <div className="mt-4 space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-700 dark:text-gray-300">PAYE Submissions:</span>
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          {overview.compliance.payeSubmissions}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-700 dark:text-gray-300">SDL Submissions:</span>
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          {overview.compliance.sdlSubmissions}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Payroll Summary */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Monthly Payroll Summary
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="text-center">
                      <p className="text-sm text-gray-600 dark:text-gray-400">Gross Payroll</p>
                      <p className="text-xl font-bold text-gray-900 dark:text-white">
                        {formatCurrency(overview.payroll.totalGrossPayroll)}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600 dark:text-gray-400">Total Deductions</p>
                      <p className="text-xl font-bold text-red-600 dark:text-red-400">
                        {formatCurrency(overview.payroll.totalDeductions)}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600 dark:text-gray-400">Net Payroll</p>
                      <p className="text-xl font-bold text-green-600 dark:text-green-400">
                        {formatCurrency(overview.payroll.totalNetPayroll)}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600 dark:text-gray-400">Average Salary</p>
                      <p className="text-xl font-bold text-blue-600 dark:text-blue-400">
                        {formatCurrency(overview.payroll.averageSalary)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">No payroll data available.</p>
              </div>
            )}

            {/* Tanzania Payroll Information */}
            <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
              <h4 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-4">
                Tanzania Payroll Requirements
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Taxes</h5>
                  <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li>• PAYE: Progressive rates 0%-30%</li>
                    <li>• SDL: 6% of gross payroll</li>
                    <li>• Personal relief: 220,000 TZS</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Social Security</h5>
                  <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li>• NSSF: 10% (max 20,000 TZS)</li>
                    <li>• WCF: 0.5% of gross salary</li>
                    <li>• Both employee & employer</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Health Insurance</h5>
                  <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li>• NHIF: Band-based rates</li>
                    <li>• 150 - 1,700 TZS monthly</li>
                    <li>• Based on salary level</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* PAYE Calculator Section */}
        {activeSection === 'paye-calculator' && <PAYECalculator />}

        {/* SDL Calculator Section */}
        {activeSection === 'sdl-calculator' && <SDLCalculator />}

        {/* Statutory Contributions Section */}
        {activeSection === 'statutory-contributions' && <StatutoryContributions />}

        {/* Employee Management Section */}
        {activeSection === 'employees' && <EmployeeManagement />}

        {/* PAYE Reports Section */}
        {activeSection === 'paye-reports' && <PAYEReports />}

        {/* SDL Reports Section */}
        {activeSection === 'sdl-reports' && <SDLReports />}

        {/* Statistics Section */}
        {activeSection === 'statistics' && <PayrollStatistics />}
      </div>
    </div>
  );
}
