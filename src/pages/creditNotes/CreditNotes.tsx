import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  PlusIcon,
  DocumentTextIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CheckIcon,
  XMarkIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';
import { useNotification } from '../../contexts/NotificationContext';

// Mock service for now - will be implemented with backend
const mockCreditNotes = [
  {
    id: '1',
    creditNoteNumber: 'CN-000001',
    customerName: 'Acme Corporation',
    creditDate: '2025-01-10',
    totalAmount: 1250.00,
    appliedAmount: 500.00,
    remainingAmount: 750.00,
    status: 'ISSUED',
    creditReason: 'RETURN',
    currency: 'USD',
  },
  {
    id: '2',
    creditNoteNumber: 'CN-000002',
    customerName: 'Tech Solutions Ltd',
    creditDate: '2025-01-08',
    totalAmount: 850.00,
    appliedAmount: 850.00,
    remainingAmount: 0.00,
    status: 'APPLIED',
    creditReason: 'DEFECTIVE',
    currency: 'USD',
  },
];

type CreditNoteStatus = 'DRAFT' | 'ISSUED' | 'APPLIED' | 'PARTIALLY_APPLIED' | 'REFUNDED' | 'CANCELLED';
type CreditReason = 'RETURN' | 'DEFECTIVE' | 'OVERCHARGE' | 'DISCOUNT' | 'GOODWILL' | 'ERROR' | 'OTHER';

const CreditNoteStatusLabels: Record<CreditNoteStatus, string> = {
  DRAFT: 'Draft',
  ISSUED: 'Issued',
  APPLIED: 'Applied',
  PARTIALLY_APPLIED: 'Partially Applied',
  REFUNDED: 'Refunded',
  CANCELLED: 'Cancelled',
};

const CreditNoteStatusColors: Record<CreditNoteStatus, string> = {
  DRAFT: 'gray',
  ISSUED: 'blue',
  APPLIED: 'green',
  PARTIALLY_APPLIED: 'orange',
  REFUNDED: 'green',
  CANCELLED: 'red',
};

const CreditReasonLabels: Record<CreditReason, string> = {
  RETURN: 'Product Return',
  DEFECTIVE: 'Defective Item',
  OVERCHARGE: 'Overcharge',
  DISCOUNT: 'Discount',
  GOODWILL: 'Goodwill',
  ERROR: 'Billing Error',
  OTHER: 'Other',
};

const CreditNotes = () => {
  const navigate = useNavigate();
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  const [creditNotes, setCreditNotes] = useState(mockCreditNotes);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    search: '',
    dateFrom: '',
    dateTo: '',
    page: 1,
    limit: 50,
  });

  const formatCurrency = (amount: number, currency: string = 'USD'): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (date: string): string => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: CreditNoteStatus) => {
    const color = CreditNoteStatusColors[status];
    const label = CreditNoteStatusLabels[status];
    
    const colorClasses = {
      gray: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
      blue: 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300',
      orange: 'bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-300',
      green: 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300',
      red: 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClasses[color as keyof typeof colorClasses]}`}>
        {label}
      </span>
    );
  };

  const getBalanceIndicator = (creditNote: any) => {
    if (creditNote.remainingAmount > 0) {
      return (
        <div className="flex items-center text-blue-600 dark:text-blue-400">
          <CurrencyDollarIcon className="h-4 w-4 mr-1" />
          <span className="text-xs font-medium">
            {formatCurrency(creditNote.remainingAmount, creditNote.currency)} available
          </span>
        </div>
      );
    }
    
    return (
      <div className="flex items-center text-green-600 dark:text-green-400">
        <CheckIcon className="h-4 w-4 mr-1" />
        <span className="text-xs">Fully applied</span>
      </div>
    );
  };

  const handleDelete = async (creditNoteId: string) => {
    if (!confirm('Are you sure you want to delete this credit note?')) {
      return;
    }

    try {
      // Mock delete - will be replaced with actual API call
      setCreditNotes(prev => prev.filter(cn => cn.id !== creditNoteId));
      
      addNotification({
        type: 'success',
        title: 'Success',
        message: 'Credit note deleted successfully',
      });
    } catch (error) {
      console.error('Failed to delete credit note:', error);
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to delete credit note',
      });
    }
  };

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">Please select a company</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Credit Notes
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Manage customer credit notes and refunds
          </p>
        </div>
        <button
          onClick={() => navigate('/credit-notes/new')}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          New Credit Note
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </label>
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value, page: 1 })}
              className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              <option value="">All Statuses</option>
              {Object.entries(CreditNoteStatusLabels).map(([value, label]) => (
                <option key={value} value={value}>{label}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Search
            </label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value, page: 1 })}
              placeholder="Search credit note, customer..."
              className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              From Date
            </label>
            <input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value, page: 1 })}
              className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              To Date
            </label>
            <input
              type="date"
              value={filters.dateTo}
              onChange={(e) => setFilters({ ...filters, dateTo: e.target.value, page: 1 })}
              className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* Credit Notes Table */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          </div>
        ) : creditNotes.length === 0 ? (
          <div className="text-center py-12">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              No credit notes
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Get started by creating a new credit note.
            </p>
            <div className="mt-6">
              <button
                onClick={() => navigate('/credit-notes/new')}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                New Credit Note
              </button>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Credit Note Number
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Credit Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Total Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Balance
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {creditNotes.map((creditNote) => (
                  <tr key={creditNote.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {creditNote.creditNoteNumber}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {creditNote.customerName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {formatDate(creditNote.creditDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(creditNote.status as CreditNoteStatus)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {formatCurrency(creditNote.totalAmount, creditNote.currency)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getBalanceIndicator(creditNote)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => navigate(`/credit-notes/${creditNote.id}`)}
                          className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                          title="View"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        {creditNote.status === 'DRAFT' && (
                          <>
                            <button
                              onClick={() => navigate(`/credit-notes/${creditNote.id}/edit`)}
                              className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                              title="Edit"
                            >
                              <PencilIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDelete(creditNote.id)}
                              className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                              title="Delete"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </>
                        )}
                        {creditNote.remainingAmount > 0 && creditNote.status === 'ISSUED' && (
                          <button
                            onClick={() => navigate(`/credit-notes/${creditNote.id}/apply`)}
                            className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                            title="Apply Credit"
                          >
                            <CurrencyDollarIcon className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentTextIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Total Credit Notes
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {creditNotes.length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Total Credit Amount
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {formatCurrency(creditNotes.reduce((sum, cn) => sum + cn.totalAmount, 0))}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckIcon className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Applied Credits
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {formatCurrency(creditNotes.reduce((sum, cn) => sum + cn.appliedAmount, 0))}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-6 w-6 text-orange-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Available Balance
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {formatCurrency(creditNotes.reduce((sum, cn) => sum + cn.remainingAmount, 0))}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreditNotes;
