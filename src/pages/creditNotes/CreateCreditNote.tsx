import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeftIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';
import { useNotification } from '../../contexts/NotificationContext';

// Mock data for now - will be replaced with actual services
const mockCustomers = [
  { id: '1', name: 'Acme Corporation', displayName: 'Acme Corporation' },
  { id: '2', name: 'Tech Solutions', displayName: 'Tech Solutions' },
];

const mockInvoices = [
  { id: '1', invoiceNumber: 'INV-000001', customerName: 'Acme Corporation', totalAmount: 2500.00 },
  { id: '2', invoiceNumber: 'INV-000002', customerName: 'Tech Solutions', totalAmount: 1800.00 },
];

const creditReasons = [
  { value: 'RETURN', label: 'Product Return' },
  { value: 'DEFECTIVE', label: 'Defective Item' },
  { value: 'OVERCHARGE', label: 'Overcharge' },
  { value: 'DISCOUNT', label: 'Discount' },
  { value: 'GOODWILL', label: 'Goodwill' },
  { value: 'ERROR', label: 'Billing Error' },
  { value: 'OTHER', label: 'Other' },
];

const CreateCreditNote = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState(mockCustomers);
  const [invoices, setInvoices] = useState(mockInvoices);

  const [formData, setFormData] = useState({
    companyId: currentCompany?.id || '',
    customerId: '',
    invoiceId: '',
    creditDate: new Date().toISOString().split('T')[0],
    currency: 'USD',
    exchangeRate: 1,
    creditReason: 'RETURN',
    reasonDescription: '',
    billingAddress: '',
    shippingAddress: '',
    notes: '',
    termsAndConditions: '',
    lineItems: [
      {
        description: '',
        unitOfMeasure: '',
        quantity: 1,
        unitPrice: 0,
        discountPercentage: 0,
        taxRate: 0,
        returnReason: 'DEFECTIVE',
        returnCondition: 'USED',
      }
    ],
  });

  const [errors, setErrors] = useState<string[]>([]);

  useEffect(() => {
    if (currentCompany) {
      setFormData(prev => ({ ...prev, companyId: currentCompany.id }));
      
      // Check if creating from invoice
      const invoiceId = searchParams.get('from_invoice');
      if (invoiceId) {
        setFormData(prev => ({ ...prev, invoiceId }));
        // Load invoice data and pre-fill form
      }
    }
  }, [currentCompany, searchParams]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCustomerChange = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId);
    if (customer) {
      setFormData(prev => ({
        ...prev,
        customerId,
        // Filter invoices for selected customer
      }));
    }
  };

  const handleLineItemChange = (index: number, field: string, value: any) => {
    const newLineItems = [...formData.lineItems];
    newLineItems[index] = { ...newLineItems[index], [field]: value };
    setFormData(prev => ({ ...prev, lineItems: newLineItems }));
  };

  const addLineItem = () => {
    setFormData(prev => ({
      ...prev,
      lineItems: [
        ...prev.lineItems,
        {
          description: '',
          unitOfMeasure: '',
          quantity: 1,
          unitPrice: 0,
          discountPercentage: 0,
          taxRate: 0,
          returnReason: 'DEFECTIVE',
          returnCondition: 'USED',
        }
      ]
    }));
  };

  const removeLineItem = (index: number) => {
    if (formData.lineItems.length > 1) {
      setFormData(prev => ({
        ...prev,
        lineItems: prev.lineItems.filter((_, i) => i !== index)
      }));
    }
  };

  const calculateLineTotal = (quantity: number, unitPrice: number, discountPercentage: number = 0): number => {
    const subtotal = quantity * unitPrice;
    const discountAmount = (subtotal * discountPercentage) / 100;
    return subtotal - discountAmount;
  };

  const calculateLineTax = (lineTotal: number, taxRate: number = 0): number => {
    return (lineTotal * taxRate) / 100;
  };

  const calculateTotals = () => {
    let subtotal = 0;
    let totalDiscount = 0;
    let totalTax = 0;

    formData.lineItems.forEach(item => {
      const lineSubtotal = item.quantity * item.unitPrice;
      const discountAmount = (lineSubtotal * item.discountPercentage) / 100;
      const lineTotal = lineSubtotal - discountAmount;
      const taxAmount = (lineTotal * item.taxRate) / 100;

      subtotal += lineSubtotal;
      totalDiscount += discountAmount;
      totalTax += taxAmount;
    });

    const total = subtotal - totalDiscount + totalTax;

    return {
      subtotal,
      totalDiscount,
      totalTax,
      total,
    };
  };

  const formatCurrency = (amount: number, currency: string = 'USD'): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentCompany) return;

    // Basic validation
    const validationErrors: string[] = [];
    if (!formData.customerId) validationErrors.push('Customer is required');
    if (!formData.creditDate) validationErrors.push('Credit date is required');
    if (formData.lineItems.length === 0) validationErrors.push('At least one line item is required');
    
    formData.lineItems.forEach((item, index) => {
      if (!item.description?.trim()) {
        validationErrors.push(`Line item ${index + 1}: Description is required`);
      }
      if (item.quantity <= 0) {
        validationErrors.push(`Line item ${index + 1}: Quantity must be greater than 0`);
      }
      if (item.unitPrice <= 0) {
        validationErrors.push(`Line item ${index + 1}: Unit price must be greater than 0`);
      }
    });

    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      setLoading(true);
      setErrors([]);
      
      // Mock API call - will be replaced with actual service
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      addNotification({
        type: 'success',
        title: 'Success',
        message: 'Credit note created successfully',
      });
      
      navigate('/credit-notes');
    } catch (error: any) {
      console.error('Failed to create credit note:', error);
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to create credit note',
      });
    } finally {
      setLoading(false);
    }
  };

  const totals = calculateTotals();

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">Please select a company</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/credit-notes')}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              Create Credit Note
            </h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Issue credit to customer for returns or adjustments
            </p>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Error Messages */}
        {errors.length > 0 && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
              Please fix the following errors:
            </h3>
            <ul className="mt-2 text-sm text-red-700 dark:text-red-300 list-disc list-inside">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Basic Information */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Credit Note Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Customer *
              </label>
              <select
                value={formData.customerId}
                onChange={(e) => handleCustomerChange(e.target.value)}
                required
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="">Select a customer</option>
                {customers.map(customer => (
                  <option key={customer.id} value={customer.id}>
                    {customer.displayName || customer.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Related Invoice (Optional)
              </label>
              <select
                value={formData.invoiceId}
                onChange={(e) => handleInputChange('invoiceId', e.target.value)}
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="">Select an invoice</option>
                {invoices.map(invoice => (
                  <option key={invoice.id} value={invoice.id}>
                    {invoice.invoiceNumber} - {formatCurrency(invoice.totalAmount)}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Credit Date *
              </label>
              <input
                type="date"
                value={formData.creditDate}
                onChange={(e) => handleInputChange('creditDate', e.target.value)}
                required
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Currency
              </label>
              <select
                value={formData.currency}
                onChange={(e) => handleInputChange('currency', e.target.value)}
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="USD">USD - US Dollar</option>
                <option value="EUR">EUR - Euro</option>
                <option value="GBP">GBP - British Pound</option>
                <option value="TZS">TZS - Tanzanian Shilling</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Credit Reason *
              </label>
              <select
                value={formData.creditReason}
                onChange={(e) => handleInputChange('creditReason', e.target.value)}
                required
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                {creditReasons.map(reason => (
                  <option key={reason.value} value={reason.value}>
                    {reason.label}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Reason Description
              </label>
              <input
                type="text"
                value={formData.reasonDescription}
                onChange={(e) => handleInputChange('reasonDescription', e.target.value)}
                placeholder="Describe the reason for credit"
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
          </div>
        </div>

        {/* Line Items */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Credit Items
            </h3>
            <button
              type="button"
              onClick={addLineItem}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-indigo-900 dark:text-indigo-300 dark:hover:bg-indigo-800"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              Add Item
            </button>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Description
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Qty
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Unit Price
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Discount %
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Tax %
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Total
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {formData.lineItems.map((item, index) => {
                  const lineTotal = calculateLineTotal(item.quantity, item.unitPrice, item.discountPercentage);
                  const taxAmount = calculateLineTax(lineTotal, item.taxRate);
                  const totalWithTax = lineTotal + taxAmount;

                  return (
                    <tr key={index}>
                      <td className="py-2">
                        <input
                          type="text"
                          value={item.description}
                          onChange={(e) => handleLineItemChange(index, 'description', e.target.value)}
                          placeholder="Item description"
                          className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                        />
                      </td>
                      <td className="py-2">
                        <input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => handleLineItemChange(index, 'quantity', parseFloat(e.target.value) || 0)}
                          min="0"
                          step="0.01"
                          className="block w-20 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                        />
                      </td>
                      <td className="py-2">
                        <input
                          type="number"
                          value={item.unitPrice}
                          onChange={(e) => handleLineItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                          min="0"
                          step="0.01"
                          className="block w-24 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                        />
                      </td>
                      <td className="py-2">
                        <input
                          type="number"
                          value={item.discountPercentage}
                          onChange={(e) => handleLineItemChange(index, 'discountPercentage', parseFloat(e.target.value) || 0)}
                          min="0"
                          max="100"
                          step="0.01"
                          className="block w-20 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                        />
                      </td>
                      <td className="py-2">
                        <input
                          type="number"
                          value={item.taxRate}
                          onChange={(e) => handleLineItemChange(index, 'taxRate', parseFloat(e.target.value) || 0)}
                          min="0"
                          max="100"
                          step="0.01"
                          className="block w-20 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                        />
                      </td>
                      <td className="py-2 text-sm text-gray-900 dark:text-white">
                        {formatCurrency(totalWithTax, formData.currency)}
                      </td>
                      <td className="py-2">
                        {formData.lineItems.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeLineItem(index)}
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        {/* Totals */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Credit Totals
          </h3>
          <div className="flex justify-end">
            <div className="w-64 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Subtotal:</span>
                <span className="text-gray-900 dark:text-white">
                  {formatCurrency(totals.subtotal, formData.currency)}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Discount:</span>
                <span className="text-gray-900 dark:text-white">
                  -{formatCurrency(totals.totalDiscount, formData.currency)}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Tax:</span>
                <span className="text-gray-900 dark:text-white">
                  {formatCurrency(totals.totalTax, formData.currency)}
                </span>
              </div>
              <div className="flex justify-between text-lg font-semibold border-t border-gray-200 dark:border-gray-700 pt-2">
                <span className="text-gray-900 dark:text-white">Credit Total:</span>
                <span className="text-gray-900 dark:text-white">
                  {formatCurrency(totals.total, formData.currency)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Additional Information
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Billing Address
              </label>
              <textarea
                value={formData.billingAddress}
                onChange={(e) => handleInputChange('billingAddress', e.target.value)}
                rows={3}
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notes
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={3}
                placeholder="Additional notes about this credit note"
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Terms and Conditions
              </label>
              <textarea
                value={formData.termsAndConditions}
                onChange={(e) => handleInputChange('termsAndConditions', e.target.value)}
                rows={4}
                placeholder="Terms and conditions for this credit note"
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate('/credit-notes')}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Creating...' : 'Create Credit Note'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateCreditNote;
