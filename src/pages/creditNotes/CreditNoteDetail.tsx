import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  CheckIcon,
  XMarkIcon,
  CurrencyDollarIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";

// Mock data for now - will be replaced with actual services
const mockCreditNote = {
  id: "1",
  creditNoteNumber: "CN-000001",
  customerId: "1",
  customerName: "Acme Corporation",
  customerEmail: "<EMAIL>",
  customerPhone: "******-0123",
  customerAddress: "123 Business St\nSuite 100\nNew York, NY 10001",
  invoiceId: "1",
  invoiceNumber: "INV-000001",
  creditDate: "2025-01-10",
  currency: "USD",
  exchangeRate: 1,
  creditReason: "RETURN",
  reasonDescription: "Customer returned defective items",
  totalAmount: 1250.0,
  appliedAmount: 500.0,
  remainingAmount: 750.0,
  status: "ISSUED",
  billingAddress: "123 Business St\nSuite 100\nNew York, NY 10001",
  notes: "Customer reported quality issues with delivered items",
  termsAndConditions: "Credit valid for 90 days from issue date",
  createdAt: "2025-01-10T10:00:00Z",
  updatedAt: "2025-01-10T10:00:00Z",
  lineItems: [
    {
      id: "1",
      description: "Premium Widget A",
      quantity: 2,
      unitPrice: 500.0,
      discountPercentage: 0,
      taxRate: 10,
      returnReason: "DEFECTIVE",
      returnCondition: "DAMAGED",
    },
    {
      id: "2",
      description: "Standard Widget B",
      quantity: 1,
      unitPrice: 300.0,
      discountPercentage: 10,
      taxRate: 10,
      returnReason: "RETURN",
      returnCondition: "GOOD",
    },
  ],
  history: [
    {
      id: "1",
      oldStatus: null,
      newStatus: "DRAFT",
      changedAt: "2025-01-10T09:00:00Z",
      changedByName: "John Doe",
      notes: "Credit note created",
    },
    {
      id: "2",
      oldStatus: "DRAFT",
      newStatus: "ISSUED",
      changedAt: "2025-01-10T10:00:00Z",
      changedByName: "John Doe",
      notes: "Credit note issued to customer",
    },
  ],
};

type CreditNoteStatus =
  | "DRAFT"
  | "ISSUED"
  | "APPLIED"
  | "PARTIALLY_APPLIED"
  | "REFUNDED"
  | "CANCELLED";

const CreditNoteStatusLabels: Record<CreditNoteStatus, string> = {
  DRAFT: "Draft",
  ISSUED: "Issued",
  APPLIED: "Applied",
  PARTIALLY_APPLIED: "Partially Applied",
  REFUNDED: "Refunded",
  CANCELLED: "Cancelled",
};

const CreditNoteStatusColors: Record<CreditNoteStatus, string> = {
  DRAFT: "gray",
  ISSUED: "blue",
  APPLIED: "green",
  PARTIALLY_APPLIED: "orange",
  REFUNDED: "green",
  CANCELLED: "red",
};

const CreditNoteDetail = () => {
  const { creditNoteId } = useParams<{ creditNoteId: string }>();
  const navigate = useNavigate();
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  const [creditNote] = useState(mockCreditNote);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    if (currentCompany && creditNoteId) {
      loadCreditNote();
    }
  }, [currentCompany, creditNoteId]);

  const loadCreditNote = async () => {
    if (!currentCompany || !creditNoteId) return;

    try {
      setLoading(true);
      // Mock API call - will be replaced with actual service
      await new Promise((resolve) => setTimeout(resolve, 500));
      // setCreditNote(data);
    } catch (error) {
      console.error("Failed to load credit note:", error);
      addNotification("error", "Error", "Failed to load credit note");
      navigate("/credit-notes");
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (_status: string, _notes?: string) => {
    if (!currentCompany || !creditNoteId) return;

    try {
      setActionLoading(true);
      // Mock API call - will be replaced with actual service
      await new Promise((resolve) => setTimeout(resolve, 1000));

      addNotification(
        "success",
        "Success",
        "Credit note status updated successfully"
      );

      loadCreditNote();
    } catch (error) {
      console.error("Failed to update status:", error);
      addNotification("error", "Error", "Failed to update credit note status");
    } finally {
      setActionLoading(false);
    }
  };

  const handleApplyCredit = async (_amount: number, _invoiceId?: string) => {
    if (!currentCompany || !creditNoteId) return;

    try {
      setActionLoading(true);
      // Mock API call - will be replaced with actual service
      await new Promise((resolve) => setTimeout(resolve, 1000));

      addNotification("success", "Success", "Credit applied successfully");

      loadCreditNote();
    } catch (error) {
      console.error("Failed to apply credit:", error);
      addNotification("error", "Error", "Failed to apply credit");
    } finally {
      setActionLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!currentCompany || !creditNoteId || !creditNote) return;

    if (!confirm("Are you sure you want to delete this credit note?")) {
      return;
    }

    try {
      setActionLoading(true);
      // Mock API call - will be replaced with actual service
      await new Promise((resolve) => setTimeout(resolve, 1000));

      addNotification("success", "Success", "Credit note deleted successfully");

      navigate("/credit-notes");
    } catch (error) {
      console.error("Failed to delete credit note:", error);
      addNotification("error", "Error", "Failed to delete credit note");
    } finally {
      setActionLoading(false);
    }
  };

  const formatCurrency = (amount: number, currency: string = "USD"): string => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (date: string): string => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusBadge = (status: CreditNoteStatus) => {
    const color = CreditNoteStatusColors[status];
    const label = CreditNoteStatusLabels[status];

    const colorClasses = {
      gray: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
      blue: "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300",
      orange:
        "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-300",
      green:
        "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300",
      red: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300",
    };

    return (
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          colorClasses[color as keyof typeof colorClasses]
        }`}
      >
        {label}
      </span>
    );
  };

  const calculateLineTotal = (
    quantity: number,
    unitPrice: number,
    discountPercentage: number = 0
  ): number => {
    const subtotal = quantity * unitPrice;
    const discountAmount = (subtotal * discountPercentage) / 100;
    return subtotal - discountAmount;
  };

  const calculateLineTax = (lineTotal: number, taxRate: number = 0): number => {
    return (lineTotal * taxRate) / 100;
  };

  const calculateTotals = () => {
    let subtotal = 0;
    let totalDiscount = 0;
    let totalTax = 0;

    creditNote.lineItems.forEach((item) => {
      const lineSubtotal = item.quantity * item.unitPrice;
      const discountAmount = (lineSubtotal * item.discountPercentage) / 100;
      const lineTotal = lineSubtotal - discountAmount;
      const taxAmount = (lineTotal * item.taxRate) / 100;

      subtotal += lineSubtotal;
      totalDiscount += discountAmount;
      totalTax += taxAmount;
    });

    const total = subtotal - totalDiscount + totalTax;

    return {
      subtotal,
      totalDiscount,
      totalTax,
      total,
    };
  };

  const canEdit = () => {
    return creditNote.status === "DRAFT";
  };

  const canDelete = () => {
    return creditNote.status === "DRAFT";
  };

  const canApply = () => {
    return creditNote.status === "ISSUED" && creditNote.remainingAmount > 0;
  };

  const canIssue = () => {
    return creditNote.status === "DRAFT";
  };

  const canCancel = () => {
    return ["DRAFT", "ISSUED"].includes(creditNote.status);
  };

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">
          Please select a company
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!creditNote) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">
          Credit note not found
        </p>
      </div>
    );
  }

  const totals = calculateTotals();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate("/credit-notes")}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              Credit Note {creditNote.creditNoteNumber}
            </h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Created on {formatDate(creditNote.createdAt)}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          {canEdit() && (
            <button
              onClick={() => navigate(`/credit-notes/${creditNote.id}/edit`)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
            >
              <PencilIcon className="h-4 w-4 mr-2" />
              Edit
            </button>
          )}
          {canDelete() && (
            <button
              onClick={handleDelete}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Delete
            </button>
          )}
        </div>
      </div>

      {/* Status and Actions */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Status
              </label>
              {getStatusBadge(creditNote.status as CreditNoteStatus)}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Total Amount
              </label>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {formatCurrency(creditNote.totalAmount, creditNote.currency)}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Applied Amount
              </label>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {formatCurrency(creditNote.appliedAmount, creditNote.currency)}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Remaining Balance
              </label>
              <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                {formatCurrency(
                  creditNote.remainingAmount,
                  creditNote.currency
                )}
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            {canIssue() && (
              <button
                onClick={() =>
                  handleStatusUpdate("ISSUED", "Credit note issued")
                }
                disabled={actionLoading}
                className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <CheckIcon className="h-4 w-4 mr-2" />
                Issue
              </button>
            )}

            {canApply() && (
              <button
                onClick={() => handleApplyCredit(creditNote.remainingAmount)}
                disabled={actionLoading}
                className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                Apply Credit
              </button>
            )}

            {canCancel() && (
              <button
                onClick={() =>
                  handleStatusUpdate("CANCELLED", "Credit note cancelled")
                }
                disabled={actionLoading}
                className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <XMarkIcon className="h-4 w-4 mr-2" />
                Cancel
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Credit Note Information */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Details */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Credit Note Details
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Credit Note Number
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {creditNote.creditNoteNumber}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Credit Date
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {formatDate(creditNote.creditDate)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Credit Reason
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {creditNote.creditReason}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Currency
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {creditNote.currency}
                </p>
              </div>
              {creditNote.invoiceNumber && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Related Invoice
                  </label>
                  <p className="mt-1 text-sm text-indigo-600 dark:text-indigo-400">
                    {creditNote.invoiceNumber}
                  </p>
                </div>
              )}
              {creditNote.reasonDescription && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Reason Description
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {creditNote.reasonDescription}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Line Items */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Credit Items
            </h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead>
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Qty
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Unit Price
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Discount
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Tax
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Total
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {creditNote.lineItems.map((item, index) => {
                    const lineTotal = calculateLineTotal(
                      item.quantity,
                      item.unitPrice,
                      item.discountPercentage
                    );
                    const taxAmount = calculateLineTax(lineTotal, item.taxRate);
                    const totalWithTax = lineTotal + taxAmount;

                    return (
                      <tr key={item.id || index}>
                        <td className="px-4 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {item.description}
                            </div>
                            {item.returnReason && (
                              <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                Return Reason: {item.returnReason}
                              </div>
                            )}
                            {item.returnCondition && (
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                Condition: {item.returnCondition}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                          {item.quantity}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                          {formatCurrency(item.unitPrice, creditNote.currency)}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                          {item.discountPercentage > 0 ? (
                            <div>
                              <span>{item.discountPercentage}%</span>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                -
                                {formatCurrency(
                                  (item.quantity *
                                    item.unitPrice *
                                    item.discountPercentage) /
                                    100,
                                  creditNote.currency
                                )}
                              </div>
                            </div>
                          ) : (
                            "-"
                          )}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                          {item.taxRate > 0 ? (
                            <div>
                              <span>{item.taxRate}%</span>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                {formatCurrency(taxAmount, creditNote.currency)}
                              </div>
                            </div>
                          ) : (
                            "-"
                          )}
                        </td>
                        <td className="px-4 py-4 text-sm font-medium text-gray-900 dark:text-white">
                          {formatCurrency(totalWithTax, creditNote.currency)}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* Totals */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Credit Totals
            </h3>
            <div className="flex justify-end">
              <div className="w-64 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    Subtotal:
                  </span>
                  <span className="text-gray-900 dark:text-white">
                    {formatCurrency(totals.subtotal, creditNote.currency)}
                  </span>
                </div>
                {totals.totalDiscount > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">
                      Discount:
                    </span>
                    <span className="text-gray-900 dark:text-white">
                      -
                      {formatCurrency(
                        totals.totalDiscount,
                        creditNote.currency
                      )}
                    </span>
                  </div>
                )}
                {totals.totalTax > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">
                      Tax:
                    </span>
                    <span className="text-gray-900 dark:text-white">
                      {formatCurrency(totals.totalTax, creditNote.currency)}
                    </span>
                  </div>
                )}
                <div className="flex justify-between text-lg font-semibold border-t border-gray-200 dark:border-gray-700 pt-2">
                  <span className="text-gray-900 dark:text-white">
                    Credit Total:
                  </span>
                  <span className="text-gray-900 dark:text-white">
                    {formatCurrency(totals.total, creditNote.currency)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Information */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Customer Information
            </h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Customer
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {creditNote.customerName}
                </p>
              </div>
              {creditNote.customerEmail && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Email
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {creditNote.customerEmail}
                  </p>
                </div>
              )}
              {creditNote.customerPhone && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Phone
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {creditNote.customerPhone}
                  </p>
                </div>
              )}
              {creditNote.customerAddress && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Address
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white whitespace-pre-line">
                    {creditNote.customerAddress}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Credit Summary */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Credit Summary
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  Total Items:
                </span>
                <span className="text-gray-900 dark:text-white">
                  {creditNote.lineItems.length}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  Total Quantity:
                </span>
                <span className="text-gray-900 dark:text-white">
                  {creditNote.lineItems.reduce(
                    (sum, item) => sum + item.quantity,
                    0
                  )}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  Credit Amount:
                </span>
                <span className="text-gray-900 dark:text-white">
                  {formatCurrency(creditNote.totalAmount, creditNote.currency)}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  Applied:
                </span>
                <span className="text-gray-900 dark:text-white">
                  {formatCurrency(
                    creditNote.appliedAmount,
                    creditNote.currency
                  )}
                </span>
              </div>
              <div className="flex justify-between text-sm font-semibold border-t border-gray-200 dark:border-gray-700 pt-2">
                <span className="text-gray-900 dark:text-white">
                  Available:
                </span>
                <span className="text-blue-600 dark:text-blue-400">
                  {formatCurrency(
                    creditNote.remainingAmount,
                    creditNote.currency
                  )}
                </span>
              </div>
            </div>
          </div>

          {/* Credit Balance Indicator */}
          {creditNote.remainingAmount > 0 && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start">
                <CurrencyDollarIcon className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-2" />
                <div>
                  <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    Credit Available
                  </h4>
                  <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                    {formatCurrency(
                      creditNote.remainingAmount,
                      creditNote.currency
                    )}{" "}
                    available for application to future invoices.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* History */}
          {creditNote.history && creditNote.history.length > 0 && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                History
              </h3>
              <div className="space-y-3">
                {creditNote.history.map((entry, index) => (
                  <div key={entry.id || index} className="text-sm">
                    <div className="flex justify-between items-start">
                      <div>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {entry.oldStatus
                            ? `${entry.oldStatus} → ${entry.newStatus}`
                            : entry.newStatus}
                        </span>
                        {entry.notes && (
                          <p className="text-gray-600 dark:text-gray-400 mt-1">
                            {entry.notes}
                          </p>
                        )}
                      </div>
                      <span className="text-gray-500 dark:text-gray-400 text-xs">
                        {formatDate(entry.changedAt)}
                      </span>
                    </div>
                    {entry.changedByName && (
                      <p className="text-gray-500 dark:text-gray-400 text-xs mt-1">
                        by {entry.changedByName}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreditNoteDetail;
