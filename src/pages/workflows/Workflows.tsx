import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  PlusIcon,
  PlayIcon,
  PauseIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  DocumentDuplicateIcon,
  ChartBarIcon,
  Cog6ToothIcon,
} from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';
import { useNotification } from '../../contexts/NotificationContext';
import { workflowService, type Workflow, type WorkflowStats, type WorkflowTemplate } from '../../services/workflowService';
import WorkflowBuilder from '../../components/workflows/WorkflowBuilder';

export default function Workflows() {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [stats, setStats] = useState<WorkflowStats | null>(null);
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  const [showWorkflowBuilder, setShowWorkflowBuilder] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    triggerType: 'all',
    search: '',
  });

  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  const navigate = useNavigate();

  useEffect(() => {
    if (currentCompany) {
      loadWorkflows();
      loadStats();
      loadTemplates();
    }
  }, [currentCompany, filters]);

  const loadWorkflows = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      const data = await workflowService.getWorkflows(currentCompany.id, {
        isActive: filters.status === 'active' ? true : filters.status === 'inactive' ? false : undefined,
        triggerType: filters.triggerType !== 'all' ? filters.triggerType : undefined,
        search: filters.search || undefined,
      });
      setWorkflows(data.workflows);
    } catch (error) {
      console.error('Failed to load workflows:', error);
      showNotification({
        type: 'error',
        title: 'Failed to load workflows',
        message: 'Could not load workflows. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    if (!currentCompany) return;

    try {
      const statsData = await workflowService.getWorkflowStats(currentCompany.id);
      setStats(statsData);
    } catch (error) {
      console.error('Failed to load workflow stats:', error);
    }
  };

  const loadTemplates = async () => {
    try {
      const templatesData = await workflowService.getWorkflowTemplates();
      setTemplates(templatesData);
    } catch (error) {
      console.error('Failed to load workflow templates:', error);
    }
  };

  const handleCreateWorkflow = () => {
    setShowWorkflowBuilder(true);
  };

  const handleSaveWorkflow = async (workflowData: any) => {
    if (!currentCompany) return;

    try {
      await workflowService.createWorkflow(currentCompany.id, workflowData);

      showNotification({
        type: 'success',
        title: 'Workflow created',
        message: `${workflowData.name} has been created successfully.`,
      });

      setShowWorkflowBuilder(false);
      loadWorkflows();
    } catch (error) {
      showNotification({
        type: 'error',
        title: 'Failed to create workflow',
        message: 'Could not create workflow. Please try again.',
      });
    }
  };

  const handleCreateFromTemplate = async (template: WorkflowTemplate) => {
    if (!currentCompany) return;

    try {
      const workflow = await workflowService.createFromTemplate(currentCompany.id, template.id, {
        name: `${template.name} - Copy`,
        description: template.description,
      });

      showNotification({
        type: 'success',
        title: 'Workflow created from template',
        message: `${workflow.name} has been created successfully.`,
      });

      setShowTemplates(false);
      loadWorkflows();
      navigate(`/workflows/${workflow.id}/edit`);
    } catch (error) {
      showNotification({
        type: 'error',
        title: 'Failed to create workflow',
        message: 'Could not create workflow from template.',
      });
    }
  };

  const handleEditWorkflow = (workflow: Workflow) => {
    navigate(`/workflows/${workflow.id}/edit`);
  };

  const handleViewWorkflow = (workflow: Workflow) => {
    navigate(`/workflows/${workflow.id}`);
  };

  const handleToggleWorkflow = async (workflow: Workflow) => {
    if (!currentCompany) return;

    try {
      await workflowService.updateWorkflow(currentCompany.id, workflow.id, {
        isActive: !workflow.isActive,
      });
      
      showNotification({
        type: 'success',
        title: `Workflow ${workflow.isActive ? 'deactivated' : 'activated'}`,
        message: `${workflow.name} has been ${workflow.isActive ? 'deactivated' : 'activated'}.`,
      });
      
      loadWorkflows();
    } catch (error) {
      showNotification({
        type: 'error',
        title: 'Failed to update workflow',
        message: 'Could not update workflow status.',
      });
    }
  };

  const handleDeleteWorkflow = async () => {
    if (!currentCompany || !selectedWorkflow) return;

    try {
      await workflowService.deleteWorkflow(currentCompany.id, selectedWorkflow.id);
      
      showNotification({
        type: 'success',
        title: 'Workflow deleted',
        message: `${selectedWorkflow.name} has been deleted.`,
      });
      
      setShowDeleteConfirm(false);
      setSelectedWorkflow(null);
      loadWorkflows();
    } catch (error) {
      showNotification({
        type: 'error',
        title: 'Failed to delete workflow',
        message: 'Could not delete workflow.',
      });
    }
  };

  const getStatusIcon = (workflow: Workflow) => {
    if (!workflow.isActive) {
      return <PauseIcon className="h-5 w-5 text-gray-400" />;
    }
    return <PlayIcon className="h-5 w-5 text-green-500" />;
  };

  const getTriggerTypeLabel = (triggerType: string) => {
    const types: Record<string, string> = {
      EVENT: 'Event Triggered',
      SCHEDULE: 'Scheduled',
      MANUAL: 'Manual',
    };
    return types[triggerType] || triggerType;
  };

  const getTriggerTypeBadge = (triggerType: string) => {
    const badges: Record<string, string> = {
      EVENT: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
      SCHEDULE: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
      MANUAL: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
    };
    return badges[triggerType] || badges.MANUAL;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Enhanced Approval Workflows
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Automate approval processes and business workflows with enterprise features
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowTemplates(true)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
            Templates
          </button>
          <button
            onClick={handleCreateWorkflow}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Workflow
          </button>
        </div>
      </div>

      {/* Enhanced Stats Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <PlayIcon className="h-6 w-6 text-green-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Active Workflows
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {stats?.activeWorkflows ?? workflows.filter(w => w.isActive).length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-6 w-6 text-yellow-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Pending Approvals
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {stats?.pendingApprovals ?? 12}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-6 w-6 text-green-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Completed Today
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {stats?.completedToday ?? 8}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Failed/Stalled
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {stats?.failedStalled ?? 2}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Enterprise Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-6 w-6 text-blue-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Total Runs
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-white">
                      {stats.totalRuns}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircleIcon className="h-6 w-6 text-green-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Success Rate
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-white">
                      {stats.successRate}%
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ExclamationTriangleIcon className="h-6 w-6 text-orange-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Needs Attention
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-white">
                      {stats.needsAttention}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Status
            </label>
            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Workflows</option>
              <option value="active">Active Only</option>
              <option value="inactive">Inactive Only</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Trigger Type
            </label>
            <select
              value={filters.triggerType}
              onChange={(e) => setFilters(prev => ({ ...prev, triggerType: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Types</option>
              <option value="EVENT">Event Triggered</option>
              <option value="SCHEDULE">Scheduled</option>
              <option value="MANUAL">Manual</option>
            </select>
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search
            </label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              placeholder="Search workflows..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>
        </div>
      </div>

      {/* Workflows List */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        {workflows.length === 0 ? (
          <div className="text-center py-12">
            <PlayIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              No workflows found
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Get started by creating your first approval workflow.
            </p>
            <div className="mt-6">
              <button
                onClick={handleCreateWorkflow}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                New Workflow
              </button>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Workflow
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Trigger Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Last Updated
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {workflows.map((workflow) => (
                  <tr key={workflow.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getStatusIcon(workflow)}
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {workflow.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {workflow.description}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTriggerTypeBadge(workflow.triggerType)}`}>
                        {getTriggerTypeLabel(workflow.triggerType)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        workflow.isActive
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                      }`}>
                        {workflow.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(workflow.updatedAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => handleViewWorkflow(workflow)}
                          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                          title="View workflow"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleEditWorkflow(workflow)}
                          className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                          title="Edit workflow"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleToggleWorkflow(workflow)}
                          className={`${
                            workflow.isActive
                              ? 'text-yellow-600 hover:text-yellow-900'
                              : 'text-green-600 hover:text-green-900'
                          }`}
                          title={workflow.isActive ? 'Deactivate workflow' : 'Activate workflow'}
                        >
                          {workflow.isActive ? (
                            <PauseIcon className="h-4 w-4" />
                          ) : (
                            <PlayIcon className="h-4 w-4" />
                          )}
                        </button>
                        <button
                          onClick={() => {
                            setSelectedWorkflow(workflow);
                            setShowDeleteConfirm(true);
                          }}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          title="Delete workflow"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Workflow Templates Modal */}
      {showTemplates && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center p-4">
            <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setShowTemplates(false)} />
            <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <DocumentDuplicateIcon className="h-6 w-6 text-primary-600 mr-3" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    Workflow Templates
                  </h3>
                </div>
                <button
                  onClick={() => setShowTemplates(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <XCircleIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                {templates.map((template) => (
                  <div
                    key={template.id}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                    onClick={() => handleCreateFromTemplate(template)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {template.name}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          {template.description}
                        </p>
                        <div className="flex items-center mt-2 space-x-2">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                            {template.category}
                          </span>
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400">
                            {template.triggerType}
                          </span>
                        </div>
                      </div>
                      <PlusIcon className="h-5 w-5 text-primary-600 ml-2" />
                    </div>
                  </div>
                ))}
              </div>

              {templates.length === 0 && (
                <div className="text-center py-8">
                  <DocumentDuplicateIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                    No templates available
                  </h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Templates will appear here when available.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && selectedWorkflow && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center p-4">
            <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setShowDeleteConfirm(false)} />
            <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
              <div className="flex items-center mb-4">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-600 mr-3" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Delete Workflow
                </h3>
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                Are you sure you want to delete "{selectedWorkflow.name}"? This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteWorkflow}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Workflow Builder Modal */}
      <WorkflowBuilder
        isOpen={showWorkflowBuilder}
        onClose={() => setShowWorkflowBuilder(false)}
        onSave={handleSaveWorkflow}
      />
    </div>
  );
}
