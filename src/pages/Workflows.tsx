import React, { useState, useEffect } from 'react';
import { useCompany } from '../contexts/CompanyContext';
import { apiService } from '../services/api';
import {
  PlayIcon,
  PauseIcon,
  PlusIcon,
  CogIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  TrashIcon,
  XMarkIcon,
  PencilIcon
} from '@heroicons/react/24/outline';

interface WorkflowTrigger {
  type: 'SCHEDULE' | 'EVENT' | 'MANUAL' | 'CONDITION';
  config: {
    schedule?: string;
    event?: string;
    condition?: any;
  };
}

interface Workflow {
  id: string;
  companyId: string;
  name: string;
  description?: string;
  isActive: boolean;
  trigger: WorkflowTrigger;
  conditions?: any[];
  actions: any[];
  metadata?: any;
  createdBy: string;
  lastRunAt?: string;
  nextRunAt?: string;
  runCount: number;
  createdAt: string;
  updatedAt: string;
}

interface WorkflowStats {
  activeWorkflows: number;
  totalRuns: number;
  successRate: number;
  needsAttention: number;
  recentExecutions: any[];
}

const getTriggerDisplayName = (trigger: WorkflowTrigger): string => {
  switch (trigger.type) {
    case 'SCHEDULE':
      return `Schedule: ${trigger.config.schedule || 'Custom'}`;
    case 'EVENT':
      return `Event: ${trigger.config.event || 'Unknown'}`;
    case 'MANUAL':
      return 'Manual Trigger';
    case 'CONDITION':
      return 'Condition-based';
    default:
      return 'Unknown';
  }
};

const getWorkflowCategory = (actions: any[]): string => {
  if (actions.some(action => action.type === 'APPROVAL_REQUEST')) return 'approval';
  if (actions.some(action => action.type === 'EMAIL' || action.type === 'NOTIFICATION')) return 'notification';
  if (actions.some(action => action.type === 'WEBHOOK')) return 'integration';
  return 'automation';
};

const categoryColors = {
  approval: 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400',
  automation: 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400',
  notification: 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400',
  integration: 'bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-400'
};

const statusIcons = {
  active: CheckCircleIcon,
  paused: PauseIcon,
  draft: ClockIcon
};

const statusColors = {
  active: 'text-green-600 dark:text-green-400',
  paused: 'text-yellow-600 dark:text-yellow-400',
  draft: 'text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500'
};

export default function Workflows() {
  const { currentCompany } = useCompany();
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [stats, setStats] = useState<WorkflowStats | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null);

  // Form states
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    triggerType: 'EVENT' as 'SCHEDULE' | 'EVENT' | 'MANUAL' | 'CONDITION',
    triggerConfig: {
      schedule: '',
      event: '',
      condition: {}
    },
    conditions: [] as any[],
    actions: [] as any[],
    isActive: true
  });

  // Settings states
  const [settings, setSettings] = useState({
    maxConcurrentExecutions: 10,
    defaultTimeout: 300,
    enableNotifications: true,
    enableLogging: true,
    retryAttempts: 3,
    retryDelay: 60
  });

  // Fetch workflows and stats
  useEffect(() => {
    if (currentCompany?.id) {
      fetchWorkflows();
      fetchStats();
    }
  }, [currentCompany?.id]);

  const fetchWorkflows = async () => {
    try {
      setLoading(true);

      const token = localStorage.getItem('token');
      const response = await fetch(`/api/workflows/${currentCompany?.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setWorkflows(data.workflows || []);
    } catch (err) {
      setError('Failed to load workflows');
      console.error('Error fetching workflows:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/workflows/${currentCompany?.id}/stats`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setStats(data);
    } catch (err) {
      console.error('Error fetching workflow stats:', err);
    }
  };

  const filteredWorkflows = workflows.filter(workflow => {
    const category = getWorkflowCategory(workflow.actions);
    const status = workflow.isActive ? 'active' : 'paused';
    const categoryMatch = selectedCategory === 'all' || category === selectedCategory;
    const statusMatch = selectedStatus === 'all' || status === selectedStatus;
    return categoryMatch && statusMatch;
  });

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const toggleWorkflowStatus = async (workflowId: string) => {
    try {
      const workflow = workflows.find(w => w.id === workflowId);
      if (!workflow) return;

      const token = localStorage.getItem('token');
      const response = await fetch(`/api/workflows/${currentCompany?.id}/${workflowId}/status`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive: !workflow.isActive }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Update local state
      setWorkflows(prev => prev.map(w =>
        w.id === workflowId ? { ...w, isActive: !w.isActive } : w
      ));

      // Refresh stats
      fetchStats();
    } catch (err) {
      console.error('Error updating workflow status:', err);
      setError('Failed to update workflow status');
    }
  };

  const handleDeleteWorkflow = (workflow: Workflow) => {
    setSelectedWorkflow(workflow);
    setShowDeleteModal(true);
  };

  const confirmDeleteWorkflow = async () => {
    if (!selectedWorkflow) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/workflows/${currentCompany?.id}/${selectedWorkflow.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Update local state
      setWorkflows(prev => prev.filter(w => w.id !== selectedWorkflow.id));

      // Refresh stats
      fetchStats();

      // Close modal
      setShowDeleteModal(false);
      setSelectedWorkflow(null);
    } catch (err) {
      console.error('Error deleting workflow:', err);
      setError('Failed to delete workflow');
    }
  };

  const handleEditWorkflow = (workflow: Workflow) => {
    setSelectedWorkflow(workflow);
    // Populate form with existing workflow data
    setFormData({
      name: workflow.name,
      description: workflow.description || '',
      triggerType: workflow.trigger.type,
      triggerConfig: workflow.trigger.config,
      conditions: workflow.conditions || [],
      actions: workflow.actions || [],
      isActive: workflow.isActive
    });
    setShowEditModal(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      triggerType: 'EVENT',
      triggerConfig: {
        schedule: '',
        event: '',
        condition: {}
      },
      conditions: [],
      actions: [],
      isActive: true
    });
  };

  const handleCreateWorkflow = async () => {
    // Validation
    if (!formData.name.trim()) {
      setError('Workflow name is required');
      return;
    }

    if (formData.actions.length === 0) {
      setError('At least one action is required');
      return;
    }

    // Validate trigger configuration
    if (formData.triggerType === 'SCHEDULE' && !formData.triggerConfig.schedule) {
      setError('Schedule is required for scheduled workflows');
      return;
    }

    if (formData.triggerType === 'EVENT' && !formData.triggerConfig.event) {
      setError('Event type is required for event-based workflows');
      return;
    }

    try {
      setError(null); // Clear any previous errors

      const token = localStorage.getItem('token');
      const response = await fetch(`/api/workflows/${currentCompany?.id}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim(),
          isActive: formData.isActive,
          trigger: {
            type: formData.triggerType,
            config: formData.triggerConfig
          },
          conditions: formData.conditions,
          actions: formData.actions
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Refresh workflows list
      await fetchWorkflows();
      await fetchStats();

      // Close modal and reset form
      setShowCreateModal(false);
      resetForm();

      // Show success message
      setSuccessMessage('Workflow created successfully!');
      setTimeout(() => setSuccessMessage(null), 5000);
    } catch (err: any) {
      console.error('Error creating workflow:', err);
      setError(err.message || 'Failed to create workflow');
    }
  };

  const handleUpdateWorkflow = async () => {
    if (!selectedWorkflow) return;

    // Validation
    if (!formData.name.trim()) {
      setError('Workflow name is required');
      return;
    }

    if (formData.actions.length === 0) {
      setError('At least one action is required');
      return;
    }

    // Validate trigger configuration
    if (formData.triggerType === 'SCHEDULE' && !formData.triggerConfig.schedule) {
      setError('Schedule is required for scheduled workflows');
      return;
    }

    if (formData.triggerType === 'EVENT' && !formData.triggerConfig.event) {
      setError('Event type is required for event-based workflows');
      return;
    }

    try {
      setError(null); // Clear any previous errors

      const token = localStorage.getItem('token');
      const response = await fetch(`/api/workflows/${currentCompany?.id}/${selectedWorkflow.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim(),
          isActive: formData.isActive,
          trigger: {
            type: formData.triggerType,
            config: formData.triggerConfig
          },
          conditions: formData.conditions,
          actions: formData.actions
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Refresh workflows list
      await fetchWorkflows();
      await fetchStats();

      // Close modal and reset form
      setShowEditModal(false);
      setSelectedWorkflow(null);
      resetForm();

      // Show success message
      setSuccessMessage('Workflow updated successfully!');
      setTimeout(() => setSuccessMessage(null), 5000);
    } catch (err: any) {
      console.error('Error updating workflow:', err);
      setError(err.message || 'Failed to update workflow');
    }
  };

  const handleSaveSettings = async () => {
    try {
      setError(null); // Clear any previous errors

      // For now, we'll just save to localStorage as a demo
      // In a real implementation, this would save to a settings API
      localStorage.setItem(`workflow_settings_${currentCompany?.id}`, JSON.stringify(settings));

      console.log('Settings saved:', settings);
      setShowSettingsModal(false);

      // Show success message (you could add a toast notification here)
      console.log('Workflow settings saved successfully');
    } catch (err: any) {
      console.error('Error saving settings:', err);
      setError(err.message || 'Failed to save settings');
    }
  };

  // Load settings on component mount
  useEffect(() => {
    if (currentCompany?.id) {
      const savedSettings = localStorage.getItem(`workflow_settings_${currentCompany.id}`);
      if (savedSettings) {
        try {
          const parsedSettings = JSON.parse(savedSettings);
          setSettings(parsedSettings);
        } catch (err) {
          console.error('Error loading saved settings:', err);
        }
      }
    }
  }, [currentCompany?.id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {successMessage && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 rounded-md p-4">
          <div className="flex">
            <CheckCircleIcon className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm text-green-800">{successMessage}</p>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white dark:text-white">Workflows</h1>
          <p className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">
            Automate your business processes and approvals
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowSettingsModal(true)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
          >
            <CogIcon className="h-4 w-4 mr-2" />
            Settings
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Create Workflow
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid bg-white dark:bg-gray-800 grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircleIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Active Workflows</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats?.activeWorkflows || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ArrowPathIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Total Runs</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats?.totalRuns || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircleIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Success Rate</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats?.successRate || 0}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Needs Attention</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats?.needsAttention || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex flex-wrap gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category</label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md cursor-pointer"
            >
              <option value="all">All Categories</option>
              <option value="approval">Approval</option>
              <option value="automation">Automation</option>
              <option value="notification">Notification</option>
              <option value="integration">Integration</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md cursor-pointer"
            >
              <option value="all">All Statuses</option>
              <option value="active">Active</option>
              <option value="paused">Paused</option>
              <option value="draft">Draft</option>
            </select>
          </div>
        </div>
      </div>

      {/* Workflows List */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Workflows ({filteredWorkflows.length})
          </h3>
        </div>
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {filteredWorkflows.length === 0 ? (
            <div className="p-12 text-center">
              <CogIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No workflows found</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                {workflows.length === 0
                  ? "Get started by creating your first workflow."
                  : "No workflows match your current filters."
                }
              </p>
              {workflows.length === 0 && (
                <div className="mt-6">
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 cursor-pointer"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Create Workflow
                  </button>
                </div>
              )}
            </div>
          ) : (
            filteredWorkflows.map((workflow) => {
              const category = getWorkflowCategory(workflow.actions);
              const status = workflow.isActive ? 'active' : 'paused';
              const StatusIcon = statusIcons[status];

              return (
                <div key={workflow.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <StatusIcon className={`h-5 w-5 ${statusColors[status]}`} />
                        <h4 className="text-lg font-medium text-gray-900 dark:text-white">{workflow.name}</h4>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${categoryColors[category]}`}>
                          {category}
                        </span>
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">{workflow.description || 'No description'}</p>
                      <div className="flex items-center space-x-6 mt-3 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        <span>Trigger: {getTriggerDisplayName(workflow.trigger)}</span>
                        <span>Runs: {workflow.runCount}</span>
                        <span>Created: {formatDate(workflow.createdAt)}</span>
                        {workflow.lastRunAt && (
                          <span>Last run: {formatDate(workflow.lastRunAt)}</span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => toggleWorkflowStatus(workflow.id)}
                        className={`inline-flex items-center px-3 py-1 rounded-md text-sm font-medium cursor-pointer ${
                          workflow.isActive
                            ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400 hover:bg-yellow-200 dark:hover:bg-yellow-900/30'
                            : 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/30'
                        }`}
                      >
                        {workflow.isActive ? (
                          <>
                            <PauseIcon className="h-4 w-4 mr-1" />
                            Pause
                          </>
                        ) : (
                          <>
                            <PlayIcon className="h-4 w-4 mr-1" />
                            Start
                          </>
                        )}
                      </button>
                      <button
                        onClick={() => handleEditWorkflow(workflow)}
                        className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:bg-gray-600 cursor-pointer"
                      >
                        <PencilIcon className="h-4 w-4 mr-1" />
                        Edit
                      </button>
                      <button
                        onClick={() => handleDeleteWorkflow(workflow)}
                        className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium text-red-700 bg-red-100 dark:bg-red-900/20 hover:bg-red-200 cursor-pointer"
                      >
                        <TrashIcon className="h-4 w-4 mr-1" />
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* Create Workflow Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 overflow-y-auto h-full w-full z-50" style={{ backgroundColor: 'rgba(0,0,0,0.6)' }}>
          <div className="relative top-10 mx-auto p-5 border max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Create New Workflow</h3>
                <button
                  onClick={() => {
                    setShowCreateModal(false);
                    resetForm();
                  }}
                  className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-pointer"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-4 max-h-96 overflow-y-auto">
                {/* Basic Information */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Workflow Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                      formData.name.trim() ? 'border-gray-300 dark:border-gray-600 focus:ring-blue-500' : 'border-red-300 focus:ring-red-500'
                    }`}
                    placeholder="Enter workflow name"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    placeholder="Describe what this workflow does"
                  />
                </div>

                {/* Trigger Configuration */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Trigger Type *
                  </label>
                  <select
                    value={formData.triggerType}
                    onChange={(e) => setFormData({...formData, triggerType: e.target.value as any})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
                  >
                    <option value="EVENT">Event-based</option>
                    <option value="SCHEDULE">Scheduled</option>
                    <option value="MANUAL">Manual</option>
                    <option value="CONDITION">Condition-based</option>
                  </select>
                </div>

                {/* Trigger Configuration Details */}
                {formData.triggerType === 'SCHEDULE' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Schedule (Cron Expression)
                    </label>
                    <input
                      type="text"
                      value={formData.triggerConfig.schedule}
                      onChange={(e) => setFormData({
                        ...formData,
                        triggerConfig: {...formData.triggerConfig, schedule: e.target.value}
                      })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="0 9 * * * (Daily at 9 AM)"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">
                      Examples: "0 9 * * *" (daily at 9 AM), "0 0 1 * *" (monthly)
                    </p>
                  </div>
                )}

                {formData.triggerType === 'EVENT' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Event Type
                    </label>
                    <select
                      value={formData.triggerConfig.event}
                      onChange={(e) => setFormData({
                        ...formData,
                        triggerConfig: {...formData.triggerConfig, event: e.target.value}
                      })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
                    >
                      <option value="">Select an event</option>
                      <option value="invoice_created">Invoice Created</option>
                      <option value="payment_received">Payment Received</option>
                      <option value="expense_submitted">Expense Submitted</option>
                      <option value="bank_transaction">Bank Transaction</option>
                      <option value="user_login">User Login</option>
                    </select>
                  </div>
                )}

                {/* Actions */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Actions *
                  </label>
                  <div className="space-y-2">
                    <button
                      type="button"
                      onClick={() => {
                        const newAction = {
                          type: 'EMAIL',
                          config: {
                            to: [''],
                            subject: '',
                            template: 'default'
                          }
                        };
                        setFormData({
                          ...formData,
                          actions: [...formData.actions, newAction]
                        });
                      }}
                      className="px-3 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 rounded-md text-sm hover:bg-blue-200 cursor-pointer"
                    >
                      + Add Email Action
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        const newAction = {
                          type: 'APPROVAL_REQUEST',
                          config: {
                            approvers: ['manager'],
                            message: '',
                            timeout: 48
                          }
                        };
                        setFormData({
                          ...formData,
                          actions: [...formData.actions, newAction]
                        });
                      }}
                      className="px-3 py-1 bg-green-100 dark:bg-green-900/20 text-green-800 rounded-md text-sm hover:bg-green-200 cursor-pointer ml-2"
                    >
                      + Add Approval Action
                    </button>
                  </div>

                  {formData.actions.map((action, index) => (
                    <div key={index} className="mt-2 p-3 border border-gray-200 dark:border-gray-700 rounded-md">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium">{action.type} Action</span>
                        <button
                          onClick={() => {
                            const newActions = formData.actions.filter((_, i) => i !== index);
                            setFormData({...formData, actions: newActions});
                          }}
                          className="text-red-500 dark:text-red-400 hover:text-red-700 cursor-pointer"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      </div>
                      {action.type === 'EMAIL' && (
                        <div className="space-y-2">
                          <input
                            type="text"
                            placeholder="Subject"
                            value={action.config.subject}
                            onChange={(e) => {
                              const newActions = [...formData.actions];
                              newActions[index].config.subject = e.target.value;
                              setFormData({...formData, actions: newActions});
                            }}
                            className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded text-sm"
                          />
                          <input
                            type="text"
                            placeholder="Recipients (comma-separated)"
                            value={action.config.to.join(', ')}
                            onChange={(e) => {
                              const newActions = [...formData.actions];
                              newActions[index].config.to = e.target.value.split(',').map(s => s.trim());
                              setFormData({...formData, actions: newActions});
                            }}
                            className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded text-sm"
                          />
                        </div>
                      )}
                      {action.type === 'APPROVAL_REQUEST' && (
                        <div className="space-y-2">
                          <input
                            type="text"
                            placeholder="Approval message"
                            value={action.config.message}
                            onChange={(e) => {
                              const newActions = [...formData.actions];
                              newActions[index].config.message = e.target.value;
                              setFormData({...formData, actions: newActions});
                            }}
                            className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded text-sm"
                          />
                          <input
                            type="number"
                            placeholder="Timeout (hours)"
                            value={action.config.timeout}
                            onChange={(e) => {
                              const newActions = [...formData.actions];
                              newActions[index].config.timeout = parseInt(e.target.value);
                              setFormData({...formData, actions: newActions});
                            }}
                            className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded text-sm"
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Status */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({...formData, isActive: e.target.checked})}
                    className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded cursor-pointer"
                  />
                  <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900 dark:text-white cursor-pointer">
                    Start workflow immediately
                  </label>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-3 mt-6 pt-4 border-t">
                <button
                  onClick={() => {
                    setShowCreateModal(false);
                    resetForm();
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 dark:text-gray-300 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:ring-gray-600 cursor-pointer"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateWorkflow}
                  disabled={!formData.name || formData.actions.length === 0}
                  className="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300 cursor-pointer disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  Create Workflow
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Settings Modal */}
      {showSettingsModal && (
        <div className="fixed inset-0 overflow-y-auto h-full w-full z-50" style={{ backgroundColor: 'rgba(0,0,0,0.6)' }}>
          <div className="relative top-20 mx-auto p-5 border max-w-lg shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Workflow Settings</h3>
                <button
                  onClick={() => setShowSettingsModal(false)}
                  className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-pointer"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-4 bg-white dark:bg-gray-800">
                {/* Execution Settings */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Execution Settings</h4>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                        Max Concurrent Executions
                      </label>
                      <input
                        type="number"
                        value={settings.maxConcurrentExecutions}
                        onChange={(e) => setSettings({
                          ...settings,
                          maxConcurrentExecutions: parseInt(e.target.value)
                        })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="1"
                        max="100"
                      />
                    </div>

                    <div>
                      <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                        Default Timeout (seconds)
                      </label>
                      <input
                        type="number"
                        value={settings.defaultTimeout}
                        onChange={(e) => setSettings({
                          ...settings,
                          defaultTimeout: parseInt(e.target.value)
                        })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="30"
                        max="3600"
                      />
                    </div>

                    <div>
                      <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                        Retry Attempts
                      </label>
                      <input
                        type="number"
                        value={settings.retryAttempts}
                        onChange={(e) => setSettings({
                          ...settings,
                          retryAttempts: parseInt(e.target.value)
                        })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="0"
                        max="10"
                      />
                    </div>

                    <div>
                      <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                        Retry Delay (seconds)
                      </label>
                      <input
                        type="number"
                        value={settings.retryDelay}
                        onChange={(e) => setSettings({
                          ...settings,
                          retryDelay: parseInt(e.target.value)
                        })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="10"
                        max="600"
                      />
                    </div>
                  </div>
                </div>

                {/* Notification Settings */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Notification Settings</h4>

                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="enableNotifications"
                        checked={settings.enableNotifications}
                        onChange={(e) => setSettings({
                          ...settings,
                          enableNotifications: e.target.checked
                        })}
                        className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded cursor-pointer"
                      />
                      <label htmlFor="enableNotifications" className="ml-2 block text-sm text-gray-900 dark:text-white cursor-pointer">
                        Enable workflow notifications
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="enableLogging"
                        checked={settings.enableLogging}
                        onChange={(e) => setSettings({
                          ...settings,
                          enableLogging: e.target.checked
                        })}
                        className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded cursor-pointer"
                      />
                      <label htmlFor="enableLogging" className="ml-2 block text-sm text-gray-900 dark:text-white cursor-pointer">
                        Enable detailed logging
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-3 mt-6 pt-4 border-t">
                <button
                  onClick={() => setShowSettingsModal(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 dark:text-gray-300 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:ring-gray-600 cursor-pointer"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveSettings}
                  className="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300 cursor-pointer"
                >
                  Save Settings
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Workflow Modal */}
      {showEditModal && selectedWorkflow && (
        <div className="fixed inset-0 overflow-y-auto h-full w-full z-50" style={{ backgroundColor: 'rgba(0,0,0,0.6)' }}>
          <div className="relative top-10 mx-auto p-5 border max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Edit Workflow</h3>
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    setSelectedWorkflow(null);
                    resetForm();
                  }}
                  className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-pointer"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-4 max-h-96 overflow-y-auto">
                {/* Basic Information */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Workflow Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter workflow name"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    placeholder="Describe what this workflow does"
                  />
                </div>

                {/* Trigger Configuration */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Trigger Type *
                  </label>
                  <select
                    value={formData.triggerType}
                    onChange={(e) => setFormData({...formData, triggerType: e.target.value as any})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
                  >
                    <option value="EVENT">Event-based</option>
                    <option value="SCHEDULE">Scheduled</option>
                    <option value="MANUAL">Manual</option>
                    <option value="CONDITION">Condition-based</option>
                  </select>
                </div>

                {/* Trigger Configuration Details */}
                {formData.triggerType === 'SCHEDULE' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Schedule (Cron Expression)
                    </label>
                    <input
                      type="text"
                      value={formData.triggerConfig.schedule}
                      onChange={(e) => setFormData({
                        ...formData,
                        triggerConfig: {...formData.triggerConfig, schedule: e.target.value}
                      })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="0 9 * * * (Daily at 9 AM)"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">
                      Examples: "0 9 * * *" (daily at 9 AM), "0 0 1 * *" (monthly)
                    </p>
                  </div>
                )}

                {formData.triggerType === 'EVENT' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Event Type
                    </label>
                    <select
                      value={formData.triggerConfig.event}
                      onChange={(e) => setFormData({
                        ...formData,
                        triggerConfig: {...formData.triggerConfig, event: e.target.value}
                      })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
                    >
                      <option value="">Select an event</option>
                      <option value="invoice_created">Invoice Created</option>
                      <option value="payment_received">Payment Received</option>
                      <option value="expense_submitted">Expense Submitted</option>
                      <option value="bank_transaction">Bank Transaction</option>
                      <option value="user_login">User Login</option>
                    </select>
                  </div>
                )}

                {/* Actions */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Actions *
                  </label>
                  <div className="space-y-2">
                    <button
                      type="button"
                      onClick={() => {
                        const newAction = {
                          type: 'EMAIL',
                          config: {
                            to: [''],
                            subject: '',
                            template: 'default'
                          }
                        };
                        setFormData({
                          ...formData,
                          actions: [...formData.actions, newAction]
                        });
                      }}
                      className="px-3 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 rounded-md text-sm hover:bg-blue-200 cursor-pointer"
                    >
                      + Add Email Action
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        const newAction = {
                          type: 'APPROVAL_REQUEST',
                          config: {
                            approvers: ['manager'],
                            message: '',
                            timeout: 48
                          }
                        };
                        setFormData({
                          ...formData,
                          actions: [...formData.actions, newAction]
                        });
                      }}
                      className="px-3 py-1 bg-green-100 dark:bg-green-900/20 text-green-800 rounded-md text-sm hover:bg-green-200 cursor-pointer ml-2"
                    >
                      + Add Approval Action
                    </button>
                  </div>

                  {formData.actions.map((action, index) => (
                    <div key={index} className="mt-2 p-3 border border-gray-200 dark:border-gray-700 rounded-md">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium">{action.type} Action</span>
                        <button
                          onClick={() => {
                            const newActions = formData.actions.filter((_, i) => i !== index);
                            setFormData({...formData, actions: newActions});
                          }}
                          className="text-red-500 dark:text-red-400 hover:text-red-700 cursor-pointer"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      </div>
                      {action.type === 'EMAIL' && (
                        <div className="space-y-2">
                          <input
                            type="text"
                            placeholder="Subject"
                            value={action.config.subject}
                            onChange={(e) => {
                              const newActions = [...formData.actions];
                              newActions[index].config.subject = e.target.value;
                              setFormData({...formData, actions: newActions});
                            }}
                            className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded text-sm"
                          />
                          <input
                            type="text"
                            placeholder="Recipients (comma-separated)"
                            value={action.config.to.join(', ')}
                            onChange={(e) => {
                              const newActions = [...formData.actions];
                              newActions[index].config.to = e.target.value.split(',').map(s => s.trim());
                              setFormData({...formData, actions: newActions});
                            }}
                            className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded text-sm"
                          />
                        </div>
                      )}
                      {action.type === 'APPROVAL_REQUEST' && (
                        <div className="space-y-2">
                          <input
                            type="text"
                            placeholder="Approval message"
                            value={action.config.message}
                            onChange={(e) => {
                              const newActions = [...formData.actions];
                              newActions[index].config.message = e.target.value;
                              setFormData({...formData, actions: newActions});
                            }}
                            className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded text-sm"
                          />
                          <input
                            type="number"
                            placeholder="Timeout (hours)"
                            value={action.config.timeout}
                            onChange={(e) => {
                              const newActions = [...formData.actions];
                              newActions[index].config.timeout = parseInt(e.target.value);
                              setFormData({...formData, actions: newActions});
                            }}
                            className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded text-sm"
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Status */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActiveEdit"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({...formData, isActive: e.target.checked})}
                    className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded cursor-pointer"
                  />
                  <label htmlFor="isActiveEdit" className="ml-2 block text-sm text-gray-900 dark:text-white cursor-pointer">
                    Workflow is active
                  </label>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-3 mt-6 pt-4 border-t">
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    setSelectedWorkflow(null);
                    resetForm();
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 dark:text-gray-300 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:ring-gray-600 cursor-pointer"
                >
                  Cancel
                </button>
                <button
                  onClick={handleUpdateWorkflow}
                  disabled={!formData.name || formData.actions.length === 0}
                  className="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300 cursor-pointer disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  Update Workflow
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && selectedWorkflow && (
        <div className="fixed inset-0 overflow-y-auto h-full w-full z-50" style={{ backgroundColor: 'rgba(0,0,0,0.6)' }}>
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Delete Workflow</h3>
                <button
                  onClick={() => {
                    setShowDeleteModal(false);
                    setSelectedWorkflow(null);
                  }}
                  className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-pointer"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-3">
                  Are you sure you want to delete the workflow <strong>"{selectedWorkflow.name}"</strong>?
                </p>
                <p className="text-sm text-red-600 dark:text-red-400">
                  This action cannot be undone. All workflow history and configurations will be permanently removed.
                </p>
              </div>
              <div className="flex items-center px-4 py-3 space-x-3">
                <button
                  onClick={() => {
                    setShowDeleteModal(false);
                    setSelectedWorkflow(null);
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 dark:text-gray-300 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:ring-gray-600 cursor-pointer flex-1"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDeleteWorkflow}
                  className="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300 cursor-pointer flex-1"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
