import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  ArrowLeftIcon,
  PencilIcon,
  DocumentArrowDownIcon,
  PrinterIcon,
  CheckIcon,
  XMarkIcon,
  PaperAirplaneIcon,
  ArchiveBoxIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import purchaseOrderService from "../../services/purchaseOrderService";
import type { PurchaseOrder } from "../../types/purchaseOrder";
import {
  PurchaseOrderStatusLabels,
  PurchaseOrderStatusColors,
  canEditPurchaseOrder,
  canApprovePurchaseOrder,
  canSendPurchaseOrder,
  canReceivePurchaseOrder,
  canCancelPurchaseOrder,
  getNextPossibleStatuses,
} from "../../types/purchaseOrder";

const PurchaseOrderDetail = () => {
  const { poId } = useParams<{ poId: string }>();
  const navigate = useNavigate();
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  const [purchaseOrder, setPurchaseOrder] = useState<PurchaseOrder | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    if (currentCompany && poId) {
      loadPurchaseOrder();
    }
  }, [currentCompany, poId]);

  const loadPurchaseOrder = async () => {
    if (!currentCompany || !poId) return;

    try {
      setLoading(true);
      const po = await purchaseOrderService.getPurchaseOrder(
        currentCompany.id,
        poId
      );
      setPurchaseOrder(po);
    } catch (error) {
      console.error("Failed to load purchase order:", error);
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to load purchase order",
      });
      navigate("/purchase-orders");
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (status: string, notes?: string) => {
    if (!currentCompany || !poId) return;

    try {
      setActionLoading(true);
      await purchaseOrderService.updatePurchaseOrderStatus(
        currentCompany.id,
        poId,
        {
          status: status as any,
          notes,
        }
      );

      addNotification({
        type: "success",
        title: "Success",
        message: "Purchase order status updated successfully",
      });

      loadPurchaseOrder();
    } catch (error) {
      console.error("Failed to update status:", error);
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to update purchase order status",
      });
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const color =
      PurchaseOrderStatusColors[
        status as keyof typeof PurchaseOrderStatusColors
      ] || "gray";
    const label =
      PurchaseOrderStatusLabels[
        status as keyof typeof PurchaseOrderStatusLabels
      ] || status;

    const colorClasses = {
      gray: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
      yellow:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-300",
      blue: "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300",
      purple:
        "bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-300",
      orange:
        "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-300",
      green:
        "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300",
      red: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300",
    };

    return (
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          colorClasses[color as keyof typeof colorClasses]
        }`}
      >
        {label}
      </span>
    );
  };

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">
          Please select a company
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!purchaseOrder) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">
          Purchase order not found
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate("/purchase-orders")}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Purchase Orders
          </button>
        </div>

        <div className="flex items-center space-x-3">
          {/* Status Actions */}
          {canApprovePurchaseOrder(purchaseOrder.status) && (
            <button
              onClick={() => handleStatusUpdate("APPROVED")}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              <CheckIcon className="h-4 w-4 mr-1" />
              Approve
            </button>
          )}

          {canSendPurchaseOrder(purchaseOrder.status) && (
            <button
              onClick={() => handleStatusUpdate("SENT")}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <PaperAirplaneIcon className="h-4 w-4 mr-1" />
              Send
            </button>
          )}

          {canReceivePurchaseOrder(purchaseOrder.status) && (
            <button
              onClick={() => handleStatusUpdate("FULLY_RECEIVED")}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
            >
              <ArchiveBoxIcon className="h-4 w-4 mr-1" />
              Mark Received
            </button>
          )}

          {canCancelPurchaseOrder(purchaseOrder.status) && (
            <button
              onClick={() => handleStatusUpdate("CANCELLED")}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              <XMarkIcon className="h-4 w-4 mr-1" />
              Cancel
            </button>
          )}

          {/* Edit Button */}
          {canEditPurchaseOrder(purchaseOrder.status) && (
            <button
              onClick={() => navigate(`/purchase-orders/${poId}/edit`)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <PencilIcon className="h-4 w-4 mr-1" />
              Edit
            </button>
          )}

          {/* Print/Export */}
          <button
            onClick={() => window.print()}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <PrinterIcon className="h-4 w-4 mr-1" />
            Print
          </button>
        </div>
      </div>

      {/* Purchase Order Header */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              Purchase Order {purchaseOrder.poNumber}
            </h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Created on{" "}
              {purchaseOrderService.formatDate(purchaseOrder.createdAt)}
            </p>
          </div>
          <div className="text-right">
            {getStatusBadge(purchaseOrder.status)}
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              Total:{" "}
              {purchaseOrderService.formatCurrency(
                purchaseOrder.totalAmount,
                purchaseOrder.currency
              )}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Vendor Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Vendor Information
            </h3>
            <div className="space-y-2">
              <p className="text-sm">
                <span className="font-medium text-gray-900 dark:text-white">
                  Name:
                </span>{" "}
                <span className="text-gray-600 dark:text-gray-400">
                  {purchaseOrder.vendorDisplayName || purchaseOrder.vendorName}
                </span>
              </p>
              {purchaseOrder.vendorEmail && (
                <p className="text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">
                    Email:
                  </span>{" "}
                  <span className="text-gray-600 dark:text-gray-400">
                    {purchaseOrder.vendorEmail}
                  </span>
                </p>
              )}
              {purchaseOrder.vendorPhone && (
                <p className="text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">
                    Phone:
                  </span>{" "}
                  <span className="text-gray-600 dark:text-gray-400">
                    {purchaseOrder.vendorPhone}
                  </span>
                </p>
              )}
              {purchaseOrder.vendorBillingAddress && (
                <p className="text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">
                    Address:
                  </span>{" "}
                  <span className="text-gray-600 dark:text-gray-400 whitespace-pre-line">
                    {purchaseOrder.vendorBillingAddress}
                  </span>
                </p>
              )}
            </div>
          </div>

          {/* Purchase Order Details */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Order Details
            </h3>
            <div className="space-y-2">
              <p className="text-sm">
                <span className="font-medium text-gray-900 dark:text-white">
                  PO Date:
                </span>{" "}
                <span className="text-gray-600 dark:text-gray-400">
                  {purchaseOrderService.formatDate(purchaseOrder.poDate)}
                </span>
              </p>
              {purchaseOrder.expectedDeliveryDate && (
                <p className="text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">
                    Expected Delivery:
                  </span>{" "}
                  <span className="text-gray-600 dark:text-gray-400">
                    {purchaseOrderService.formatDate(
                      purchaseOrder.expectedDeliveryDate
                    )}
                  </span>
                </p>
              )}
              {purchaseOrder.referenceNumber && (
                <p className="text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">
                    Reference:
                  </span>{" "}
                  <span className="text-gray-600 dark:text-gray-400">
                    {purchaseOrder.referenceNumber}
                  </span>
                </p>
              )}
              <p className="text-sm">
                <span className="font-medium text-gray-900 dark:text-white">
                  Currency:
                </span>{" "}
                <span className="text-gray-600 dark:text-gray-400">
                  {purchaseOrder.currency}
                </span>
              </p>
              {purchaseOrder.exchangeRate !== 1 && (
                <p className="text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">
                    Exchange Rate:
                  </span>{" "}
                  <span className="text-gray-600 dark:text-gray-400">
                    {purchaseOrder.exchangeRate}
                  </span>
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Line Items */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Line Items
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Unit
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Qty Ordered
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Qty Received
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Unit Cost
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Discount
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Tax
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Total
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {purchaseOrder.lineItems?.map((item) => (
                <tr key={item.id}>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    <div>
                      <div className="font-medium">{item.description}</div>
                      {item.itemCode && (
                        <div className="text-gray-500 dark:text-gray-400 text-xs">
                          Code: {item.itemCode}
                        </div>
                      )}
                      {item.notes && (
                        <div className="text-gray-500 dark:text-gray-400 text-xs mt-1">
                          {item.notes}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {item.unitOfMeasure || "-"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                    {item.quantityOrdered}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                    <span
                      className={
                        item.quantityReceived > 0
                          ? "text-green-600 dark:text-green-400"
                          : ""
                      }
                    >
                      {item.quantityReceived}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                    {purchaseOrderService.formatCurrency(
                      item.unitCost,
                      purchaseOrder.currency
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                    {item.discountPercentage > 0
                      ? `${item.discountPercentage}%`
                      : "-"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                    {item.taxRate > 0 ? `${item.taxRate}%` : "-"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900 dark:text-white">
                    {purchaseOrderService.formatCurrency(
                      item.lineTotal,
                      purchaseOrder.currency
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Totals */}
        <div className="bg-gray-50 dark:bg-gray-700 px-6 py-4">
          <div className="flex justify-end">
            <div className="w-64 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  Subtotal:
                </span>
                <span className="text-gray-900 dark:text-white">
                  {purchaseOrderService.formatCurrency(
                    purchaseOrder.subtotal,
                    purchaseOrder.currency
                  )}
                </span>
              </div>
              {purchaseOrder.discountAmount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    Discount:
                  </span>
                  <span className="text-gray-900 dark:text-white">
                    -
                    {purchaseOrderService.formatCurrency(
                      purchaseOrder.discountAmount,
                      purchaseOrder.currency
                    )}
                  </span>
                </div>
              )}
              {purchaseOrder.taxAmount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Tax:</span>
                  <span className="text-gray-900 dark:text-white">
                    {purchaseOrderService.formatCurrency(
                      purchaseOrder.taxAmount,
                      purchaseOrder.currency
                    )}
                  </span>
                </div>
              )}
              <div className="flex justify-between text-base font-medium border-t border-gray-200 dark:border-gray-600 pt-2">
                <span className="text-gray-900 dark:text-white">Total:</span>
                <span className="text-gray-900 dark:text-white">
                  {purchaseOrderService.formatCurrency(
                    purchaseOrder.totalAmount,
                    purchaseOrder.currency
                  )}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      {(purchaseOrder.billingAddress ||
        purchaseOrder.shippingAddress ||
        purchaseOrder.notes ||
        purchaseOrder.termsAndConditions) && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Additional Information
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {purchaseOrder.billingAddress && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Billing Address
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line">
                  {purchaseOrder.billingAddress}
                </p>
              </div>
            )}

            {purchaseOrder.shippingAddress && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Shipping Address
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line">
                  {purchaseOrder.shippingAddress}
                </p>
              </div>
            )}
          </div>

          {purchaseOrder.notes && (
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                Notes
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line">
                {purchaseOrder.notes}
              </p>
            </div>
          )}

          {purchaseOrder.termsAndConditions && (
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                Terms and Conditions
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line">
                {purchaseOrder.termsAndConditions}
              </p>
            </div>
          )}
        </div>
      )}

      {/* History */}
      {purchaseOrder.history && purchaseOrder.history.length > 0 && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Status History
          </h3>

          <div className="flow-root">
            <ul className="-mb-8">
              {purchaseOrder.history.map((entry, index) => (
                <li key={entry.id}>
                  <div className="relative pb-8">
                    {index !== purchaseOrder.history!.length - 1 && (
                      <span
                        className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-600"
                        aria-hidden="true"
                      />
                    )}
                    <div className="relative flex space-x-3">
                      <div>
                        <span className="h-8 w-8 rounded-full bg-gray-400 flex items-center justify-center ring-8 ring-white dark:ring-gray-800">
                          <span className="text-white text-xs font-medium">
                            {entry.newStatus.charAt(0)}
                          </span>
                        </span>
                      </div>
                      <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                        <div>
                          <p className="text-sm text-gray-900 dark:text-white">
                            Status changed to{" "}
                            <span className="font-medium">
                              {PurchaseOrderStatusLabels[entry.newStatus]}
                            </span>
                            {entry.oldStatus && (
                              <span className="text-gray-500 dark:text-gray-400">
                                {" "}
                                from{" "}
                                {PurchaseOrderStatusLabels[entry.oldStatus]}
                              </span>
                            )}
                          </p>
                          {entry.notes && (
                            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                              {entry.notes}
                            </p>
                          )}
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            by {entry.changedByName || "System"}
                          </p>
                        </div>
                        <div className="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                          {purchaseOrderService.formatDate(entry.changedAt)}
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default PurchaseOrderDetail;
