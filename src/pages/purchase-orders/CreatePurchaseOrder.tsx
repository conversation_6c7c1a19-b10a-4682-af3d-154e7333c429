import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  PlusIcon,
  TrashIcon,
  ArrowLeftIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import purchaseOrderService from "../../services/purchaseOrderService";
import { contactService } from "../../services/contactService";
import type {
  CreatePurchaseOrderRequest,
  PurchaseOrderLineItem,
} from "../../types/purchaseOrder";
import type { Contact } from "../../types";

const CreatePurchaseOrder = () => {
  const navigate = useNavigate();
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  const [loading, setLoading] = useState(false);
  const [vendors, setVendors] = useState<Contact[]>([]);
  const [formData, setFormData] = useState<CreatePurchaseOrderRequest>({
    companyId: currentCompany?.id || "",
    vendorId: "",
    poDate: new Date().toISOString().split("T")[0],
    expectedDeliveryDate: "",
    currency: "USD",
    exchangeRate: 1,
    billingAddress: "",
    shippingAddress: "",
    notes: "",
    termsAndConditions: "",
    referenceNumber: "",
    lineItems: [
      {
        description: "",
        unitOfMeasure: "",
        quantityOrdered: 1,
        unitCost: 0,
        discountPercentage: 0,
        taxRate: 0,
      },
    ],
  });

  const [errors, setErrors] = useState<string[]>([]);

  useEffect(() => {
    if (currentCompany) {
      setFormData((prev) => ({ ...prev, companyId: currentCompany.id }));
      loadVendors();
    }
  }, [currentCompany]);

  const loadVendors = async () => {
    if (!currentCompany) return;

    try {
      const response = await contactService.getContacts(currentCompany.id, {
        contactType: "VENDOR",
        isActive: true,
        limit: 1000,
      });
      setVendors(response.data);
    } catch (error) {
      console.error("Failed to load vendors:", error);
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to load vendors",
      });
    }
  };

  const handleInputChange = (
    field: keyof CreatePurchaseOrderRequest,
    value: any
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setErrors([]);
  };

  const handleLineItemChange = (
    index: number,
    field: keyof PurchaseOrderLineItem,
    value: any
  ) => {
    const updatedLineItems = [...formData.lineItems];
    updatedLineItems[index] = { ...updatedLineItems[index], [field]: value };
    setFormData((prev) => ({ ...prev, lineItems: updatedLineItems }));
    setErrors([]);
  };

  const addLineItem = () => {
    setFormData((prev) => ({
      ...prev,
      lineItems: [
        ...prev.lineItems,
        {
          description: "",
          unitOfMeasure: "",
          quantityOrdered: 1,
          unitCost: 0,
          discountPercentage: 0,
          taxRate: 0,
        },
      ],
    }));
  };

  const removeLineItem = (index: number) => {
    if (formData.lineItems.length > 1) {
      const updatedLineItems = formData.lineItems.filter((_, i) => i !== index);
      setFormData((prev) => ({ ...prev, lineItems: updatedLineItems }));
    }
  };

  const calculateLineTotal = (item: any) => {
    const subtotal = item.quantityOrdered * item.unitCost;
    const discountAmount = (subtotal * (item.discountPercentage || 0)) / 100;
    const lineTotal = subtotal - discountAmount;
    const taxAmount = (lineTotal * (item.taxRate || 0)) / 100;
    return lineTotal + taxAmount;
  };

  const calculateTotals = () => {
    return purchaseOrderService.calculateTotals(formData.lineItems);
  };

  const handleSubmit = async (e: React.FormEvent, asDraft = true) => {
    e.preventDefault();

    if (!currentCompany) return;

    // Validate form
    const validationErrors =
      purchaseOrderService.validatePurchaseOrder(formData);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      setLoading(true);

      const createdPO = await purchaseOrderService.createPurchaseOrder(
        formData
      );

      // If not saving as draft, submit for approval
      if (!asDraft) {
        await purchaseOrderService.submitForApproval(
          currentCompany.id,
          createdPO.id,
          "Purchase order submitted for approval"
        );
      }

      addNotification({
        type: "success",
        title: "Success",
        message: `Purchase order ${
          asDraft ? "created" : "submitted for approval"
        } successfully`,
      });

      navigate("/purchase-orders");
    } catch (error) {
      console.error("Failed to create purchase order:", error);
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to create purchase order",
      });
    } finally {
      setLoading(false);
    }
  };

  const totals = calculateTotals();

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">
          Please select a company
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate("/purchase-orders")}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Purchase Orders
          </button>
        </div>
      </div>

      <div>
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
          Create Purchase Order
        </h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Create a new purchase order for your vendor
        </p>
      </div>

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div className="text-sm text-red-700 dark:text-red-400">
            <ul className="list-disc list-inside space-y-1">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        </div>
      )}

      <form onSubmit={(e) => handleSubmit(e, true)} className="space-y-6">
        {/* Basic Information */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Basic Information
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Vendor *
              </label>
              <select
                value={formData.vendorId}
                onChange={(e) => handleInputChange("vendorId", e.target.value)}
                required
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">Select a vendor</option>
                {vendors.map((vendor) => (
                  <option key={vendor.id} value={vendor.id}>
                    {vendor.displayName || vendor.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                PO Date *
              </label>
              <input
                type="date"
                value={formData.poDate}
                onChange={(e) => handleInputChange("poDate", e.target.value)}
                required
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Expected Delivery Date
              </label>
              <input
                type="date"
                value={formData.expectedDeliveryDate}
                onChange={(e) =>
                  handleInputChange("expectedDeliveryDate", e.target.value)
                }
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Reference Number
              </label>
              <input
                type="text"
                value={formData.referenceNumber}
                onChange={(e) =>
                  handleInputChange("referenceNumber", e.target.value)
                }
                placeholder="Enter reference number"
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Currency
              </label>
              <select
                value={formData.currency}
                onChange={(e) => handleInputChange("currency", e.target.value)}
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="USD">USD - US Dollar</option>
                <option value="TZS">TZS - Tanzanian Shilling</option>
                <option value="EUR">EUR - Euro</option>
                <option value="GBP">GBP - British Pound</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Exchange Rate
              </label>
              <input
                type="number"
                step="0.00000001"
                value={formData.exchangeRate}
                onChange={(e) =>
                  handleInputChange(
                    "exchangeRate",
                    parseFloat(e.target.value) || 1
                  )
                }
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>
          </div>
        </div>

        {/* Addresses and Additional Information */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Additional Information
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Billing Address
              </label>
              <textarea
                value={formData.billingAddress}
                onChange={(e) =>
                  handleInputChange("billingAddress", e.target.value)
                }
                rows={3}
                placeholder="Enter billing address"
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Shipping Address
              </label>
              <textarea
                value={formData.shippingAddress}
                onChange={(e) =>
                  handleInputChange("shippingAddress", e.target.value)
                }
                rows={3}
                placeholder="Enter shipping address"
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>
          </div>

          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              rows={3}
              placeholder="Additional notes or instructions"
              className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>

          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Terms and Conditions
            </label>
            <textarea
              value={formData.termsAndConditions}
              onChange={(e) =>
                handleInputChange("termsAndConditions", e.target.value)
              }
              rows={4}
              placeholder="Enter terms and conditions"
              className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          <button
            type="button"
            onClick={() => navigate("/purchase-orders")}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
          >
            Cancel
          </button>

          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? "Creating..." : "Save as Draft"}
          </button>

          <button
            type="button"
            onClick={(e) => handleSubmit(e, false)}
            disabled={loading}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? "Submitting..." : "Submit for Approval"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreatePurchaseOrder;
<div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
  <div className="flex items-center justify-between mb-4">
    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
      Line Items
    </h3>
    <button
      type="button"
      onClick={addLineItem}
      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-indigo-900 dark:text-indigo-300 dark:hover:bg-indigo-800"
    >
      <PlusIcon className="h-4 w-4 mr-1" />
      Add Line Item
    </button>
  </div>

  <div className="space-y-4">
    {formData.lineItems.map((item, index) => (
      <div
        key={index}
        className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
      >
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
            Line Item {index + 1}
          </h4>
          {formData.lineItems.length > 1 && (
            <button
              type="button"
              onClick={() => removeLineItem(index)}
              className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description *
            </label>
            <input
              type="text"
              value={item.description}
              onChange={(e) =>
                handleLineItemChange(index, "description", e.target.value)
              }
              placeholder="Item description"
              required
              className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Unit
            </label>
            <input
              type="text"
              value={item.unitOfMeasure}
              onChange={(e) =>
                handleLineItemChange(index, "unitOfMeasure", e.target.value)
              }
              placeholder="e.g., pcs, kg"
              className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Quantity *
            </label>
            <input
              type="number"
              step="0.0001"
              value={item.quantityOrdered}
              onChange={(e) =>
                handleLineItemChange(
                  index,
                  "quantityOrdered",
                  parseFloat(e.target.value) || 0
                )
              }
              required
              min="0"
              className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Unit Cost *
            </label>
            <input
              type="number"
              step="0.01"
              value={item.unitCost}
              onChange={(e) =>
                handleLineItemChange(
                  index,
                  "unitCost",
                  parseFloat(e.target.value) || 0
                )
              }
              required
              min="0"
              className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Total
            </label>
            <div className="block w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-900 dark:text-white">
              {purchaseOrderService.formatCurrency(
                calculateLineTotal(item),
                formData.currency
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Discount %
            </label>
            <input
              type="number"
              step="0.01"
              value={item.discountPercentage}
              onChange={(e) =>
                handleLineItemChange(
                  index,
                  "discountPercentage",
                  parseFloat(e.target.value) || 0
                )
              }
              min="0"
              max="100"
              className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Tax Rate %
            </label>
            <input
              type="number"
              step="0.01"
              value={item.taxRate}
              onChange={(e) =>
                handleLineItemChange(
                  index,
                  "taxRate",
                  parseFloat(e.target.value) || 0
                )
              }
              min="0"
              max="100"
              className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Expected Delivery
            </label>
            <input
              type="date"
              value={item.expectedDeliveryDate}
              onChange={(e) =>
                handleLineItemChange(
                  index,
                  "expectedDeliveryDate",
                  e.target.value
                )
              }
              className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
        </div>

        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Notes
          </label>
          <textarea
            value={item.notes}
            onChange={(e) =>
              handleLineItemChange(index, "notes", e.target.value)
            }
            rows={2}
            placeholder="Additional notes for this line item"
            className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          />
        </div>
      </div>
    ))}
  </div>

  {/* Totals */}
  <div className="mt-6 border-t border-gray-200 dark:border-gray-700 pt-6">
    <div className="flex justify-end">
      <div className="w-64 space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Subtotal:</span>
          <span className="text-gray-900 dark:text-white">
            {purchaseOrderService.formatCurrency(
              totals.subtotal,
              formData.currency
            )}
          </span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Discount:</span>
          <span className="text-gray-900 dark:text-white">
            -
            {purchaseOrderService.formatCurrency(
              totals.totalDiscount,
              formData.currency
            )}
          </span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Tax:</span>
          <span className="text-gray-900 dark:text-white">
            {purchaseOrderService.formatCurrency(
              totals.totalTax,
              formData.currency
            )}
          </span>
        </div>
        <div className="flex justify-between text-base font-medium border-t border-gray-200 dark:border-gray-700 pt-2">
          <span className="text-gray-900 dark:text-white">Total:</span>
          <span className="text-gray-900 dark:text-white">
            {purchaseOrderService.formatCurrency(
              totals.total,
              formData.currency
            )}
          </span>
        </div>
      </div>
    </div>
  </div>
</div>;
