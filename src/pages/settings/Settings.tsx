import { useState } from "react";
import {
  UserIcon,
  BuildingOfficeIcon,
  ShieldCheckIcon,
  CogIcon,
  DocumentTextIcon,
  ChartBarIcon,
  BellIcon,
  ServerIcon,
} from "@heroicons/react/24/outline";
import UserProfileSettings from "../../components/settings/UserProfileSettings";
import CompanySettings from "../../components/settings/CompanySettings";
import SecuritySettings from "../../components/settings/SecuritySettings";
import AccountingSettings from "../../components/settings/AccountingSettings";
import IntegrationSettings from "../../components/settings/IntegrationSettings";
import AuditSettings from "../../components/settings/AuditSettings";
import NotificationSettings from "../../components/settings/NotificationSettings";
import SystemSettings from "../../components/settings/SystemSettings";
import WorkflowSettings from "../../components/settings/WorkflowSettings";

type SettingsSection =
  | "profile"
  | "company"
  | "security"
  | "accounting"
  | "integrations"
  | "workflows"
  | "audit"
  | "notifications"
  | "system";

export default function Settings() {
  const [activeSection, setActiveSection] =
    useState<SettingsSection>("profile");

  const settingsSections = [
    {
      id: "profile" as const,
      name: "User Profile",
      description: "Personal information and preferences",
      icon: UserIcon,
      component: UserProfileSettings,
    },
    {
      id: "company" as const,
      name: "Company",
      description: "Company information and configuration",
      icon: BuildingOfficeIcon,
      component: CompanySettings,
    },
    {
      id: "accounting" as const,
      name: "Accounting",
      description: "Fiscal year, currencies, and accounting rules",
      icon: DocumentTextIcon,
      component: AccountingSettings,
    },
    {
      id: "security" as const,
      name: "Security",
      description: "Password, access control, and authentication",
      icon: ShieldCheckIcon,
      component: SecuritySettings,
    },
    {
      id: "notifications" as const,
      name: "Notifications",
      description: "Email alerts and notification preferences",
      icon: BellIcon,
      component: NotificationSettings,
    },
    {
      id: "integrations" as const,
      name: "Integrations",
      description: "Third-party services and API connections",
      icon: CogIcon,
      component: IntegrationSettings,
    },
    {
      id: "workflows" as const,
      name: "Workflows",
      description: "Automation and business process workflows",
      icon: CogIcon,
      component: WorkflowSettings,
    },
    {
      id: "audit" as const,
      name: "Audit & Compliance",
      description: "Audit trails and compliance settings",
      icon: ChartBarIcon,
      component: AuditSettings,
    },
    {
      id: "system" as const,
      name: "System",
      description: "System information and advanced settings",
      icon: ServerIcon,
      component: SystemSettings,
    },
  ];

  const activeSettingsSection = settingsSections.find(
    (section) => section.id === activeSection
  );
  const ActiveComponent = activeSettingsSection?.component;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Settings</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Manage your account, company, and system settings
          </p>
        </div>
      </div>

      <div className="grid bg-white dark:bg-gray-800 grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Navigation */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            {settingsSections.map((section) => {
              const Icon = section.icon;
              const isActive = activeSection === section.id;

              return (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full text-left group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer ${
                    isActive
                      ? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 border-r-2 border-blue-500"
                      : "text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-300"
                  }`}
                >
                  <Icon
                    className={`mr-3 h-5 w-5 ${
                      isActive
                        ? "text-blue-500 dark:text-blue-400"
                        : "text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"
                    }`}
                  />
                  <div className="flex-1">
                    <div className="font-medium">{section.name}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-0.5">
                      {section.description}
                    </div>
                  </div>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          {ActiveComponent && <ActiveComponent />}
        </div>
      </div>
    </div>
  );
}
