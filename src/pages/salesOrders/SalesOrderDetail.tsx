import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  CheckIcon,
  XMarkIcon,
  TruckIcon,
  DocumentArrowDownIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  ChatBubbleLeftRightIcon,
  PaperClipIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import salesOrderService from "../../services/salesOrderService";
import type { SalesOrder } from "../../types/salesOrder";
import {
  SalesOrderStatusLabels,
  SalesOrderStatusColors,
  canEditSalesOrder,
  canDeleteSalesOrder,
  canConfirmSalesOrder,
  canStartProgress,
  canShipSalesOrder,
  canDeliverSalesOrder,
  canCompleteSalesOrder,
  canCancelSalesOrder,
  isOverdue,
  getDaysOverdue,
  getNextPossibleStatuses,
} from "../../types/salesOrder";

const SalesOrderDetail = () => {
  const { salesOrderId } = useParams<{ salesOrderId: string }>();
  const navigate = useNavigate();
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  const [salesOrder, setSalesOrder] = useState<SalesOrder | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    if (currentCompany && salesOrderId) {
      loadSalesOrder();
    }
  }, [currentCompany, salesOrderId]);

  const loadSalesOrder = async () => {
    if (!currentCompany || !salesOrderId) return;

    try {
      setLoading(true);
      const data = await salesOrderService.getSalesOrder(
        currentCompany.id,
        salesOrderId
      );
      setSalesOrder(data);
    } catch (error) {
      console.error("Failed to load sales order:", error);
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to load sales order",
      });
      navigate("/sales-orders");
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (status: string, notes?: string) => {
    if (!currentCompany || !salesOrderId) return;

    try {
      setActionLoading(true);
      await salesOrderService.updateSalesOrderStatus(
        currentCompany.id,
        salesOrderId,
        {
          status: status as any,
          notes,
        }
      );

      addNotification({
        type: "success",
        title: "Success",
        message: "Sales order status updated successfully",
      });

      loadSalesOrder();
    } catch (error) {
      console.error("Failed to update status:", error);
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to update sales order status",
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!currentCompany || !salesOrderId || !salesOrder) return;

    if (!confirm("Are you sure you want to delete this sales order?")) {
      return;
    }

    try {
      setActionLoading(true);
      await salesOrderService.deleteSalesOrder(currentCompany.id, salesOrderId);

      addNotification({
        type: "success",
        title: "Success",
        message: "Sales order deleted successfully",
      });

      navigate("/sales-orders");
    } catch (error) {
      console.error("Failed to delete sales order:", error);
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to delete sales order",
      });
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const color =
      SalesOrderStatusColors[status as keyof typeof SalesOrderStatusColors];
    const label =
      SalesOrderStatusLabels[status as keyof typeof SalesOrderStatusLabels];

    const colorClasses = {
      gray: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
      blue: "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300",
      yellow:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-300",
      orange:
        "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-300",
      green:
        "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300",
      red: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300",
    };

    return (
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          colorClasses[color as keyof typeof colorClasses]
        }`}
      >
        {label}
      </span>
    );
  };

  const getDeliveryStatus = () => {
    if (!salesOrder) return null;

    const delivery = salesOrderService.getDeliveryStatus(salesOrder);

    if (delivery.status === "overdue") {
      return (
        <div className="flex items-center text-red-600 dark:text-red-400">
          <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
          <span className="font-medium">{delivery.message}</span>
        </div>
      );
    }

    if (
      delivery.status === "on-time" &&
      delivery.daysRemaining &&
      delivery.daysRemaining <= 3
    ) {
      return (
        <div className="flex items-center text-orange-600 dark:text-orange-400">
          <ClockIcon className="h-5 w-5 mr-2" />
          <span className="font-medium">{delivery.message}</span>
        </div>
      );
    }

    if (delivery.status === "delivered") {
      return (
        <div className="flex items-center text-green-600 dark:text-green-400">
          <CheckIcon className="h-5 w-5 mr-2" />
          <span className="font-medium">{delivery.message}</span>
        </div>
      );
    }

    return (
      <div className="flex items-center text-gray-600 dark:text-gray-400">
        <ClockIcon className="h-5 w-5 mr-2" />
        <span>{delivery.message}</span>
      </div>
    );
  };

  const getFulfillmentPercentage = () => {
    if (!salesOrder) return 0;
    return salesOrderService.getFulfillmentPercentage(salesOrder);
  };

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">
          Please select a company
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!salesOrder) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">
          Sales order not found
        </p>
      </div>
    );
  }

  const fulfillmentPercentage = getFulfillmentPercentage();
  const nextStatuses = getNextPossibleStatuses(salesOrder.status as any);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate("/sales-orders")}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              Sales Order {salesOrder.salesOrderNumber}
            </h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Created on {salesOrderService.formatDate(salesOrder.createdAt)}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          {canEditSalesOrder(salesOrder.status as any) && (
            <button
              onClick={() => navigate(`/sales-orders/${salesOrder.id}/edit`)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
            >
              <PencilIcon className="h-4 w-4 mr-2" />
              Edit
            </button>
          )}
          {canDeleteSalesOrder(salesOrder.status as any) && (
            <button
              onClick={handleDelete}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Delete
            </button>
          )}
        </div>
      </div>

      {/* Status and Actions */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Status
              </label>
              {getStatusBadge(salesOrder.status)}
            </div>
            {salesOrder.deliveryDate && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Delivery Status
                </label>
                {getDeliveryStatus()}
              </div>
            )}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Fulfillment
              </label>
              <div className="flex items-center">
                <div className="w-24 bg-gray-200 rounded-full h-2 dark:bg-gray-700 mr-3">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${fulfillmentPercentage}%` }}
                  ></div>
                </div>
                <span className="text-sm text-gray-900 dark:text-white">
                  {Math.round(fulfillmentPercentage)}%
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            {nextStatuses.map((status) => {
              let buttonText = "";
              let buttonColor = "bg-indigo-600 hover:bg-indigo-700";
              let icon = null;

              switch (status) {
                case "CONFIRMED":
                  buttonText = "Confirm";
                  buttonColor = "bg-green-600 hover:bg-green-700";
                  icon = <CheckIcon className="h-4 w-4 mr-2" />;
                  break;
                case "IN_PROGRESS":
                  buttonText = "Start Progress";
                  buttonColor = "bg-blue-600 hover:bg-blue-700";
                  icon = <ClockIcon className="h-4 w-4 mr-2" />;
                  break;
                case "SHIPPED":
                  buttonText = "Ship";
                  buttonColor = "bg-orange-600 hover:bg-orange-700";
                  icon = <TruckIcon className="h-4 w-4 mr-2" />;
                  break;
                case "DELIVERED":
                  buttonText = "Mark Delivered";
                  buttonColor = "bg-green-600 hover:bg-green-700";
                  icon = <CheckIcon className="h-4 w-4 mr-2" />;
                  break;
                case "COMPLETED":
                  buttonText = "Complete";
                  buttonColor = "bg-green-600 hover:bg-green-700";
                  icon = <CheckIcon className="h-4 w-4 mr-2" />;
                  break;
                case "CANCELLED":
                  buttonText = "Cancel";
                  buttonColor = "bg-red-600 hover:bg-red-700";
                  icon = <XMarkIcon className="h-4 w-4 mr-2" />;
                  break;
              }

              return (
                <button
                  key={status}
                  onClick={() => handleStatusUpdate(status)}
                  disabled={actionLoading}
                  className={`inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white ${buttonColor} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {icon}
                  {buttonText}
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Sales Order Information */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Details */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Order Details
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Order Number
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {salesOrder.salesOrderNumber}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Order Date
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {salesOrderService.formatDate(salesOrder.orderDate)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Delivery Date
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {salesOrder.deliveryDate
                    ? salesOrderService.formatDate(salesOrder.deliveryDate)
                    : "Not set"}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Currency
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {salesOrder.currency}
                </p>
              </div>
              {salesOrder.estimateId && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Converted From
                  </label>
                  <p className="mt-1 text-sm text-indigo-600 dark:text-indigo-400">
                    Estimate (converted on{" "}
                    {salesOrder.convertedFromEstimateAt
                      ? salesOrderService.formatDate(
                          salesOrder.convertedFromEstimateAt
                        )
                      : "N/A"}
                    )
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Line Items */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Line Items
            </h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead>
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Item
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Qty Ordered
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Qty Shipped
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Unit Price
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Discount
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Tax
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Total
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {salesOrder.lineItems?.map((item, index) => {
                    const lineTotal = salesOrderService.calculateLineTotal(
                      item.quantityOrdered,
                      item.unitPrice,
                      item.discountPercentage
                    );
                    const taxAmount = salesOrderService.calculateLineTax(
                      lineTotal,
                      item.taxRate
                    );
                    const totalWithTax = lineTotal + taxAmount;

                    return (
                      <tr key={item.id || index}>
                        <td className="px-4 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {item.description}
                            </div>
                            {item.itemCode && (
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                Code: {item.itemCode}
                              </div>
                            )}
                            {item.unitOfMeasure && (
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                Unit: {item.unitOfMeasure}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                          {item.quantityOrdered}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                          <div className="flex items-center">
                            <span>{item.quantityShipped}</span>
                            {item.quantityShipped < item.quantityOrdered && (
                              <span className="ml-2 text-xs text-orange-600 dark:text-orange-400">
                                (Pending:{" "}
                                {item.quantityOrdered - item.quantityShipped})
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                          {salesOrderService.formatCurrency(
                            item.unitPrice,
                            salesOrder.currency
                          )}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                          {item.discountPercentage > 0 ? (
                            <div>
                              <div>{item.discountPercentage}%</div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                -
                                {salesOrderService.formatCurrency(
                                  item.discountAmount,
                                  salesOrder.currency
                                )}
                              </div>
                            </div>
                          ) : (
                            "-"
                          )}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                          {item.taxRate > 0 ? (
                            <div>
                              <div>{item.taxRate}%</div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                {salesOrderService.formatCurrency(
                                  taxAmount,
                                  salesOrder.currency
                                )}
                              </div>
                            </div>
                          ) : (
                            "-"
                          )}
                        </td>
                        <td className="px-4 py-4 text-sm font-medium text-gray-900 dark:text-white">
                          {salesOrderService.formatCurrency(
                            totalWithTax,
                            salesOrder.currency
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* Addresses */}
          {(salesOrder.billingAddress || salesOrder.shippingAddress) && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Addresses
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {salesOrder.billingAddress && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Billing Address
                    </label>
                    <div className="text-sm text-gray-900 dark:text-white whitespace-pre-line">
                      {salesOrder.billingAddress}
                    </div>
                  </div>
                )}
                {salesOrder.shippingAddress && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Shipping Address
                    </label>
                    <div className="text-sm text-gray-900 dark:text-white whitespace-pre-line">
                      {salesOrder.shippingAddress}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Notes and Instructions */}
          {(salesOrder.notes ||
            salesOrder.deliveryInstructions ||
            salesOrder.termsAndConditions) && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Additional Information
              </h3>
              <div className="space-y-4">
                {salesOrder.deliveryInstructions && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Delivery Instructions
                    </label>
                    <div className="text-sm text-gray-900 dark:text-white whitespace-pre-line">
                      {salesOrder.deliveryInstructions}
                    </div>
                  </div>
                )}
                {salesOrder.notes && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Notes
                    </label>
                    <div className="text-sm text-gray-900 dark:text-white whitespace-pre-line">
                      {salesOrder.notes}
                    </div>
                  </div>
                )}
                {salesOrder.termsAndConditions && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Terms and Conditions
                    </label>
                    <div className="text-sm text-gray-900 dark:text-white whitespace-pre-line">
                      {salesOrder.termsAndConditions}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Information */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Customer Information
            </h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Customer
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {salesOrder.customerDisplayName || salesOrder.customerName}
                </p>
              </div>
              {salesOrder.customerEmail && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Email
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {salesOrder.customerEmail}
                  </p>
                </div>
              )}
              {salesOrder.customerPhone && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Phone
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {salesOrder.customerPhone}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Order Totals */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Order Totals
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  Subtotal:
                </span>
                <span className="text-gray-900 dark:text-white">
                  {salesOrderService.formatCurrency(
                    salesOrder.subtotal,
                    salesOrder.currency
                  )}
                </span>
              </div>
              {salesOrder.discountAmount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    Discount:
                  </span>
                  <span className="text-gray-900 dark:text-white">
                    -
                    {salesOrderService.formatCurrency(
                      salesOrder.discountAmount,
                      salesOrder.currency
                    )}
                  </span>
                </div>
              )}
              {salesOrder.taxAmount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Tax:</span>
                  <span className="text-gray-900 dark:text-white">
                    {salesOrderService.formatCurrency(
                      salesOrder.taxAmount,
                      salesOrder.currency
                    )}
                  </span>
                </div>
              )}
              {salesOrder.shippingAmount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    Shipping:
                  </span>
                  <span className="text-gray-900 dark:text-white">
                    {salesOrderService.formatCurrency(
                      salesOrder.shippingAmount,
                      salesOrder.currency
                    )}
                  </span>
                </div>
              )}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
                <div className="flex justify-between text-lg font-semibold">
                  <span className="text-gray-900 dark:text-white">Total:</span>
                  <span className="text-gray-900 dark:text-white">
                    {salesOrderService.formatCurrency(
                      salesOrder.totalAmount,
                      salesOrder.currency
                    )}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Shipping Information */}
          {(salesOrder.trackingNumber ||
            salesOrder.shippingCarrier ||
            salesOrder.shippedAt) && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Shipping Information
              </h3>
              <div className="space-y-3">
                {salesOrder.shippedAt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Shipped Date
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {salesOrderService.formatDate(salesOrder.shippedAt)}
                    </p>
                  </div>
                )}
                {salesOrder.shippingCarrier && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Carrier
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {salesOrder.shippingCarrier}
                    </p>
                  </div>
                )}
                {salesOrder.trackingNumber && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Tracking Number
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {salesOrder.trackingNumber}
                    </p>
                  </div>
                )}
                {salesOrder.deliveredAt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Delivered Date
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {salesOrderService.formatDate(salesOrder.deliveredAt)}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* History */}
          {salesOrder.history && salesOrder.history.length > 0 && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                History
              </h3>
              <div className="space-y-3">
                {salesOrder.history.map((entry, index) => (
                  <div key={entry.id || index} className="text-sm">
                    <div className="flex justify-between items-start">
                      <div>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {entry.oldStatus
                            ? `${entry.oldStatus} → ${entry.newStatus}`
                            : entry.newStatus}
                        </span>
                        {entry.notes && (
                          <p className="text-gray-600 dark:text-gray-400 mt-1">
                            {entry.notes}
                          </p>
                        )}
                      </div>
                      <span className="text-gray-500 dark:text-gray-400 text-xs">
                        {salesOrderService.formatDate(entry.changedAt)}
                      </span>
                    </div>
                    {entry.changedByName && (
                      <p className="text-gray-500 dark:text-gray-400 text-xs mt-1">
                        by {entry.changedByName}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SalesOrderDetail;
