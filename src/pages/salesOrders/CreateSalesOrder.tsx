import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  ArrowLeftIcon,
  PlusIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import salesOrderService from "../../services/salesOrderService";
import { contactService } from "../../services/contactService";
import estimateService from "../../services/estimateService";
import type {
  CreateSalesOrderRequest,
  SalesOrderLineItem,
} from "../../types/salesOrder";
import type { Contact } from "../../types";
import type { Estimate } from "../../types/estimate";

const CreateSalesOrder = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<Contact[]>([]);
  const [sourceEstimate, setSourceEstimate] = useState<Estimate | null>(null);

  const [formData, setFormData] = useState<CreateSalesOrderRequest>({
    companyId: currentCompany?.id || "",
    customerId: "",
    orderDate: new Date().toISOString().split("T")[0],
    deliveryDate: "",
    currency: "USD",
    exchangeRate: 1,
    shippingAmount: 0,
    billingAddress: "",
    shippingAddress: "",
    deliveryInstructions: "",
    notes: "",
    termsAndConditions: salesOrderService.getDefaultTermsAndConditions(),
    lineItems: [
      {
        description: "",
        unitOfMeasure: "",
        quantityOrdered: 1,
        unitPrice: 0,
        discountPercentage: 0,
        taxRate: 0,
      },
    ],
  });

  const [errors, setErrors] = useState<string[]>([]);

  useEffect(() => {
    if (currentCompany) {
      setFormData((prev) => ({ ...prev, companyId: currentCompany.id }));
      loadCustomers();

      // Check if converting from estimate
      const estimateId = searchParams.get("from_estimate");
      if (estimateId) {
        loadEstimateForConversion(estimateId);
      }
    }
  }, [currentCompany, searchParams]);

  const loadCustomers = async () => {
    if (!currentCompany) return;

    try {
      const response = await contactService.getContacts(currentCompany.id, {
        limit: 1000,
      });
      setCustomers(response.data);
    } catch (error) {
      console.error("Failed to load customers:", error);
    }
  };

  const loadEstimateForConversion = async (estimateId: string) => {
    if (!currentCompany) return;

    try {
      const estimate = await estimateService.getEstimate(
        currentCompany.id,
        estimateId
      );
      setSourceEstimate(estimate);

      // Pre-fill form with estimate data
      setFormData((prev) => ({
        ...prev,
        customerId: estimate.customerId,
        currency: estimate.currency,
        exchangeRate: estimate.exchangeRate,
        billingAddress: estimate.billingAddress || "",
        shippingAddress: estimate.shippingAddress || "",
        notes: estimate.notes || "",
        termsAndConditions:
          estimate.termsAndConditions ||
          salesOrderService.getDefaultTermsAndConditions(),
        estimateId: estimateId,
        deliveryDate: salesOrderService.calculateDeliveryDate(
          prev.orderDate,
          7
        ),
        lineItems:
          estimate.lineItems?.map((item) => ({
            inventoryItemId: item.inventoryItemId,
            itemCode: item.itemCode,
            description: item.description,
            unitOfMeasure: item.unitOfMeasure,
            quantityOrdered: item.quantity,
            unitPrice: item.unitPrice,
            discountPercentage: item.discountPercentage,
            taxRate: item.taxRate,
            taxCode: item.taxCode,
            notes: item.notes,
          })) || [],
      }));
    } catch (error) {
      console.error("Failed to load estimate:", error);
      addNotification("error", "Error", "Failed to load estimate data");
    }
  };

  const handleInputChange = (
    field: keyof CreateSalesOrderRequest,
    value: any
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Auto-calculate delivery date when order date changes
    if (field === "orderDate" && value) {
      const deliveryDate = salesOrderService.calculateDeliveryDate(value, 7);
      setFormData((prev) => ({ ...prev, deliveryDate }));
    }
  };

  const handleCustomerChange = (customerId: string) => {
    const customer = customers.find((c) => c.id === customerId);
    if (customer) {
      setFormData((prev) => ({
        ...prev,
        customerId,
        billingAddress: customer.billingAddress || "",
        shippingAddress:
          customer.shippingAddress || customer.billingAddress || "",
      }));
    }
  };

  const handleLineItemChange = (
    index: number,
    field: keyof SalesOrderLineItem,
    value: any
  ) => {
    const newLineItems = [...formData.lineItems];
    newLineItems[index] = { ...newLineItems[index], [field]: value };

    // Auto-fill item details when inventory item is selected
    // Note: Inventory integration would go here

    setFormData((prev) => ({ ...prev, lineItems: newLineItems }));
  };

  const addLineItem = () => {
    setFormData((prev) => ({
      ...prev,
      lineItems: [
        ...prev.lineItems,
        {
          description: "",
          unitOfMeasure: "",
          quantityOrdered: 1,
          unitPrice: 0,
          discountPercentage: 0,
          taxRate: 0,
        },
      ],
    }));
  };

  const removeLineItem = (index: number) => {
    if (formData.lineItems.length > 1) {
      setFormData((prev) => ({
        ...prev,
        lineItems: prev.lineItems.filter((_, i) => i !== index),
      }));
    }
  };

  const calculateTotals = () => {
    return salesOrderService.calculateTotals(
      formData.lineItems,
      formData.shippingAmount
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentCompany) return;

    // Validate form
    const validationErrors = salesOrderService.validateSalesOrder(formData);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      setLoading(true);
      setErrors([]);

      const salesOrder = await salesOrderService.createSalesOrder(formData);

      addNotification("success", "Success", "Sales order created successfully");

      navigate(`/sales-orders/${salesOrder.id}`);
    } catch (error: any) {
      console.error("Failed to create sales order:", error);
      addNotification(
        "error",
        "Error",
        error.response?.data?.error || "Failed to create sales order"
      );
    } finally {
      setLoading(false);
    }
  };

  const totals = calculateTotals();

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">
          Please select a company
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate("/sales-orders")}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              {sourceEstimate
                ? "Convert Estimate to Sales Order"
                : "Create Sales Order"}
            </h1>
            {sourceEstimate && (
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Converting estimate {sourceEstimate.estimateNumber}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Error Messages */}
        {errors.length > 0 && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
              Please fix the following errors:
            </h3>
            <ul className="mt-2 text-sm text-red-700 dark:text-red-300 list-disc list-inside">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Basic Information */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Basic Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Customer *
              </label>
              <select
                value={formData.customerId}
                onChange={(e) => handleCustomerChange(e.target.value)}
                required
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="">Select a customer</option>
                {customers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.displayName || customer.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Order Date *
              </label>
              <input
                type="date"
                value={formData.orderDate}
                onChange={(e) => handleInputChange("orderDate", e.target.value)}
                required
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Delivery Date
              </label>
              <input
                type="date"
                value={formData.deliveryDate}
                onChange={(e) =>
                  handleInputChange("deliveryDate", e.target.value)
                }
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Currency
              </label>
              <select
                value={formData.currency}
                onChange={(e) => handleInputChange("currency", e.target.value)}
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="USD">USD - US Dollar</option>
                <option value="EUR">EUR - Euro</option>
                <option value="GBP">GBP - British Pound</option>
                <option value="TZS">TZS - Tanzanian Shilling</option>
              </select>
            </div>
          </div>
        </div>

        {/* Line Items */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Line Items
            </h3>
            <button
              type="button"
              onClick={addLineItem}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-indigo-900 dark:text-indigo-300 dark:hover:bg-indigo-800"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              Add Item
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Item
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Description
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Qty
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Unit Price
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Discount %
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Tax %
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Total
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {formData.lineItems.map((item, index) => {
                  const lineTotal = salesOrderService.calculateLineTotal(
                    item.quantityOrdered,
                    item.unitPrice,
                    item.discountPercentage
                  );
                  const taxAmount = salesOrderService.calculateLineTax(
                    lineTotal,
                    item.taxRate
                  );
                  const totalWithTax = lineTotal + taxAmount;

                  return (
                    <tr key={index}>
                      <td className="py-2">
                        <input
                          type="text"
                          value={item.itemCode || ""}
                          onChange={(e) =>
                            handleLineItemChange(
                              index,
                              "itemCode",
                              e.target.value
                            )
                          }
                          placeholder="Item code"
                          className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                        />
                      </td>
                      <td className="py-2">
                        <input
                          type="text"
                          value={item.description}
                          onChange={(e) =>
                            handleLineItemChange(
                              index,
                              "description",
                              e.target.value
                            )
                          }
                          placeholder="Item description"
                          className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                        />
                      </td>
                      <td className="py-2">
                        <input
                          type="number"
                          value={item.quantityOrdered}
                          onChange={(e) =>
                            handleLineItemChange(
                              index,
                              "quantityOrdered",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          min="0"
                          step="0.01"
                          className="block w-20 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                        />
                      </td>
                      <td className="py-2">
                        <input
                          type="number"
                          value={item.unitPrice}
                          onChange={(e) =>
                            handleLineItemChange(
                              index,
                              "unitPrice",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          min="0"
                          step="0.01"
                          className="block w-24 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                        />
                      </td>
                      <td className="py-2">
                        <input
                          type="number"
                          value={item.discountPercentage}
                          onChange={(e) =>
                            handleLineItemChange(
                              index,
                              "discountPercentage",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          min="0"
                          max="100"
                          step="0.01"
                          className="block w-20 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                        />
                      </td>
                      <td className="py-2">
                        <input
                          type="number"
                          value={item.taxRate}
                          onChange={(e) =>
                            handleLineItemChange(
                              index,
                              "taxRate",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          min="0"
                          max="100"
                          step="0.01"
                          className="block w-20 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                        />
                      </td>
                      <td className="py-2 text-sm text-gray-900 dark:text-white">
                        {salesOrderService.formatCurrency(
                          totalWithTax,
                          formData.currency
                        )}
                      </td>
                      <td className="py-2">
                        {formData.lineItems.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeLineItem(index)}
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        {/* Totals */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Totals
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Shipping Amount
              </label>
              <input
                type="number"
                value={formData.shippingAmount}
                onChange={(e) =>
                  handleInputChange(
                    "shippingAmount",
                    parseFloat(e.target.value) || 0
                  )
                }
                min="0"
                step="0.01"
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  Subtotal:
                </span>
                <span className="text-gray-900 dark:text-white">
                  {salesOrderService.formatCurrency(
                    totals.subtotal,
                    formData.currency
                  )}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  Discount:
                </span>
                <span className="text-gray-900 dark:text-white">
                  -
                  {salesOrderService.formatCurrency(
                    totals.totalDiscount,
                    formData.currency
                  )}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Tax:</span>
                <span className="text-gray-900 dark:text-white">
                  {salesOrderService.formatCurrency(
                    totals.totalTax,
                    formData.currency
                  )}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  Shipping:
                </span>
                <span className="text-gray-900 dark:text-white">
                  {salesOrderService.formatCurrency(
                    totals.shippingAmount,
                    formData.currency
                  )}
                </span>
              </div>
              <div className="flex justify-between text-lg font-semibold border-t border-gray-200 dark:border-gray-700 pt-2">
                <span className="text-gray-900 dark:text-white">Total:</span>
                <span className="text-gray-900 dark:text-white">
                  {salesOrderService.formatCurrency(
                    totals.total,
                    formData.currency
                  )}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Additional Information
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Billing Address
              </label>
              <textarea
                value={formData.billingAddress}
                onChange={(e) =>
                  handleInputChange("billingAddress", e.target.value)
                }
                rows={3}
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Shipping Address
              </label>
              <textarea
                value={formData.shippingAddress}
                onChange={(e) =>
                  handleInputChange("shippingAddress", e.target.value)
                }
                rows={3}
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Delivery Instructions
              </label>
              <textarea
                value={formData.deliveryInstructions}
                onChange={(e) =>
                  handleInputChange("deliveryInstructions", e.target.value)
                }
                rows={2}
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notes
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => handleInputChange("notes", e.target.value)}
                rows={3}
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Terms and Conditions
              </label>
              <textarea
                value={formData.termsAndConditions}
                onChange={(e) =>
                  handleInputChange("termsAndConditions", e.target.value)
                }
                rows={4}
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate("/sales-orders")}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? "Creating..." : "Create Sales Order"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateSalesOrder;
