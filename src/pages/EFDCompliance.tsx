import { useState, useEffect } from "react";
import {
  ComputerDesktopIcon,
  DocumentTextIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowDownTrayIcon,
  PlusIcon,
  CpuChipIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../contexts/CompanyContext";
import { useNotification } from "../contexts/NotificationContext";
import EFDDevices from "../components/efd/EFDDevices";
import EFDTransactions from "../components/efd/EFDTransactions";
import EFDZReports from "../components/efd/EFDZReports";
import EFDStatistics from "../components/efd/EFDStatistics";
import EFDRequirementChecker from "../components/efd/EFDRequirementChecker";

type EFDSection = 'overview' | 'devices' | 'transactions' | 'z-reports' | 'requirement-checker' | 'statistics';

interface EFDOverview {
  devices: {
    total: number;
    active: number;
    inactive: number;
    expired: number;
  };
  transactions: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    totalAmount: number;
  };
  zReports: {
    generated: number;
    submitted: number;
    pending: number;
  };
  compliance: {
    score: number;
    issues: number;
    lastZReport: string;
  };
}

export default function EFDCompliance() {
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();
  const [activeSection, setActiveSection] = useState<EFDSection>('overview');
  const [overview, setOverview] = useState<EFDOverview | null>(null);
  const [loading, setLoading] = useState(false);

  const efdSections = [
    {
      id: 'overview' as const,
      name: 'Overview',
      description: 'EFD compliance dashboard and statistics',
      icon: ChartBarIcon,
    },
    {
      id: 'devices' as const,
      name: 'EFD Devices',
      description: 'Manage Electronic Fiscal Devices',
      icon: ComputerDesktopIcon,
    },
    {
      id: 'transactions' as const,
      name: 'EFD Transactions',
      description: 'View and manage EFD transactions',
      icon: DocumentTextIcon,
    },
    {
      id: 'z-reports' as const,
      name: 'Z-Reports',
      description: 'Daily fiscal reports and submissions',
      icon: DocumentTextIcon,
    },
    {
      id: 'requirement-checker' as const,
      name: 'EFD Checker',
      description: 'Check if transaction requires EFD',
      icon: CpuChipIcon,
    },
    {
      id: 'statistics' as const,
      name: 'Statistics',
      description: 'Detailed EFD compliance analytics',
      icon: ClockIcon,
    },
  ];

  useEffect(() => {
    if (currentCompany && activeSection === 'overview') {
      loadOverview();
    }
  }, [currentCompany, activeSection]);

  const loadOverview = async () => {
    if (!currentCompany) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/efd/${currentCompany.id}/statistics`);
      if (response.ok) {
        const data = await response.json();
        setOverview(data.data);
      }
    } catch (error) {
      console.error('Error loading EFD overview:', error);
      addNotification('Failed to load EFD overview', 'error');
    } finally {
      setLoading(false);
    }
  };

  const getComplianceScoreColor = (score: number) => {
    if (score >= 95) return 'text-green-600 dark:text-green-400';
    if (score >= 85) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getComplianceScoreBg = (score: number) => {
    if (score >= 95) return 'bg-green-100 dark:bg-green-900/20';
    if (score >= 85) return 'bg-yellow-100 dark:bg-yellow-900/20';
    return 'bg-red-100 dark:bg-red-900/20';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-TZ", {
      style: "currency",
      currency: "TZS",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">Please select a company to view EFD compliance.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            EFD Compliance
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Electronic Fiscal Device compliance management for {currentCompany.name}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Last updated: {new Date().toLocaleString()}
          </span>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {efdSections.map((section) => {
            const Icon = section.icon;
            return (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                  activeSection === section.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Icon className="h-5 w-5" />
                  <span>{section.name}</span>
                </div>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        {/* Overview Section */}
        {activeSection === 'overview' && (
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                EFD Compliance Overview
              </h3>
              <button
                onClick={loadOverview}
                disabled={loading}
                className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 text-sm transition-colors duration-200"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                {loading ? 'Refreshing...' : 'Refresh'}
              </button>
            </div>

            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : overview ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* EFD Devices Card */}
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-600 dark:text-blue-400">EFD Devices</p>
                      <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                        {overview.devices.total}
                      </p>
                    </div>
                    <ComputerDesktopIcon className="h-8 w-8 text-blue-500" />
                  </div>
                  <div className="mt-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-blue-700 dark:text-blue-300">Active:</span>
                      <span className="font-medium text-blue-900 dark:text-blue-100">
                        {overview.devices.active}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-blue-700 dark:text-blue-300">Inactive:</span>
                      <span className={`font-medium ${overview.devices.inactive > 0 ? 'text-red-600 dark:text-red-400' : 'text-blue-900 dark:text-blue-100'}`}>
                        {overview.devices.inactive}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Transactions Card */}
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-6 border border-green-200 dark:border-green-800">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-600 dark:text-green-400">This Month</p>
                      <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                        {overview.transactions.thisMonth}
                      </p>
                    </div>
                    <DocumentTextIcon className="h-8 w-8 text-green-500" />
                  </div>
                  <div className="mt-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-green-700 dark:text-green-300">Today:</span>
                      <span className="font-medium text-green-900 dark:text-green-100">
                        {overview.transactions.today}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-green-700 dark:text-green-300">Amount:</span>
                      <span className="font-medium text-green-900 dark:text-green-100">
                        {formatCurrency(overview.transactions.totalAmount)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Z-Reports Card */}
                <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-6 border border-purple-200 dark:border-purple-800">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Z-Reports</p>
                      <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                        {overview.zReports.generated}
                      </p>
                    </div>
                    <DocumentTextIcon className="h-8 w-8 text-purple-500" />
                  </div>
                  <div className="mt-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-purple-700 dark:text-purple-300">Submitted:</span>
                      <span className="font-medium text-purple-900 dark:text-purple-100">
                        {overview.zReports.submitted}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-purple-700 dark:text-purple-300">Pending:</span>
                      <span className={`font-medium ${overview.zReports.pending > 0 ? 'text-red-600 dark:text-red-400' : 'text-purple-900 dark:text-purple-100'}`}>
                        {overview.zReports.pending}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Compliance Score Card */}
                <div className={`rounded-lg p-6 border ${getComplianceScoreBg(overview.compliance.score)}`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Compliance Score</p>
                      <p className={`text-2xl font-bold ${getComplianceScoreColor(overview.compliance.score)}`}>
                        {overview.compliance.score}%
                      </p>
                    </div>
                    {overview.compliance.score >= 95 ? (
                      <CheckCircleIcon className="h-8 w-8 text-green-500" />
                    ) : (
                      <ExclamationTriangleIcon className="h-8 w-8 text-yellow-500" />
                    )}
                  </div>
                  <div className="mt-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-700 dark:text-gray-300">Issues:</span>
                      <span className={`font-medium ${overview.compliance.issues > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                        {overview.compliance.issues}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-700 dark:text-gray-300">Last Z-Report:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {new Date(overview.compliance.lastZReport).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">No EFD compliance data available.</p>
              </div>
            )}

            {/* EFD Information */}
            <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
              <h4 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-4">
                Tanzania EFD Requirements
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Transaction Requirements</h5>
                  <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li>• EFD required for transactions ≥ 5,000 TZS</li>
                    <li>• All sales must be recorded in real-time</li>
                    <li>• Receipt must be issued for every transaction</li>
                    <li>• QR code verification required</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Reporting Requirements</h5>
                  <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li>• Daily Z-Reports must be generated</li>
                    <li>• Monthly submission to TRA required</li>
                    <li>• Device certificate must be valid</li>
                    <li>• Backup and data retention for 5 years</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* EFD Devices Section */}
        {activeSection === 'devices' && <EFDDevices />}

        {/* EFD Transactions Section */}
        {activeSection === 'transactions' && <EFDTransactions />}

        {/* Z-Reports Section */}
        {activeSection === 'z-reports' && <EFDZReports />}

        {/* EFD Requirement Checker Section */}
        {activeSection === 'requirement-checker' && <EFDRequirementChecker />}

        {/* Statistics Section */}
        {activeSection === 'statistics' && <EFDStatistics />}
      </div>
    </div>
  );
}
