import { useState } from "react";
import { useCompany } from "../contexts/CompanyContext";
import { useDashboard } from "../hooks/useDashboard";
import { useChartData } from "../hooks/useChartData";
import { dashboardService } from "../services/dashboardService";
import { ArrowPathIcon } from "@heroicons/react/24/outline";
import TrendChart from "../components/charts/TrendChart";
import PieChart from "../components/charts/PieChart";
import BarChart from "../components/charts/BarChart";
import AlertSystem, {
  generateAlerts,
} from "../components/dashboard/AlertSystem";
import QuickActions from "../components/dashboard/QuickActions";
import KPICards from "../components/dashboard/KPICards";

export default function Dashboard() {
  const { currentCompany } = useCompany();
  const [period, setPeriod] = useState<"month" | "quarter" | "year">("month");
  const { stats, recentActivity, loading, error, refresh } =
    useDashboard(period);
  const {
    revenueTrend,
    cashFlowTrend,
    expenseBreakdown,
    revenueExpenseComparison,
    loading: chartLoading,
    refresh: refreshCharts,
  } = useChartData(period);
  const [dismissedAlerts, setDismissedAlerts] = useState<string[]>([]);

  // Debug logging
  console.log("🔍 Dashboard - Expense breakdown data:", expenseBreakdown);
  console.log(
    "🔍 Dashboard - Expense breakdown length:",
    expenseBreakdown?.length
  );
  console.log("🔍 Dashboard - Chart loading state:", chartLoading);
  console.log("🔍 Dashboard - Component rendering successfully");

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center bg-white dark:bg-gray-800 py-12">
        <div className="text-red-600 dark:text-red-400 mb-4">{error}</div>
        <button
          onClick={refresh}
          className="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:text-blue-400 cursor-pointer"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Period Selector */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Welcome to your comprehensive accounting system
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="relative">
            <select
              value={period}
              onChange={(e) =>
                setPeriod(e.target.value as "month" | "quarter" | "year")
              }
              className="appearance-none bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:border-gray-600 rounded-lg px-4 py-2 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 hover:border-gray-400 dark:hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 shadow-sm min-w-[140px] cursor-pointer"
            >
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
              <option value="year">This Year</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <svg
                className="h-4 w-4 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </div>
          </div>
          <button
            onClick={() => {
              refresh();
              refreshCharts();
            }}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 cursor-pointer"
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Financial Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Financial Overview</h3>
          <div className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Current period summary</div>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
          {/* Total Revenue */}
          <div className="group relative flex flex-col items-center p-4 rounded-xl border border-gray-200 dark:border-gray-700 dark:border-gray-600 hover:border-green-300 dark:hover:border-green-500 hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 bg-gradient-to-br from-white to-gray-50 dark:from-gray-700 dark:to-gray-800 hover:from-green-50 hover:to-white dark:hover:from-gray-600 dark:hover:to-gray-700 cursor-pointer">
            {/* Icon */}
            <div className="flex items-center justify-center w-12 h-12 rounded-xl text-white bg-gradient-to-br from-green-500 to-green-600 transition-all duration-200 mb-3 shadow-sm group-hover:shadow-md group-hover:scale-105">
              <svg
                className="w-6 h-6 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
                  clipRule="evenodd"
                />
              </svg>
            </div>

            {/* Label */}
            <div className="text-sm font-medium text-gray-900 dark:text-white text-center mb-1">
              Revenue
            </div>

            {/* Value */}
            <div className="text-lg font-bold text-green-600 dark:text-green-400 text-center leading-tight">
              {dashboardService.formatCurrency(
                stats?.revenue || 0,
                currentCompany?.baseCurrency || "USD"
              )}
            </div>

            {/* Hover effect */}
            <div className="absolute inset-0 rounded-xl bg-green-50 dark:bg-green-900/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 -z-10" />
          </div>

          {/* Total Expenses */}
          <div className="group relative flex flex-col items-center p-4 rounded-xl border border-gray-200 dark:border-gray-700 dark:border-gray-600 hover:border-red-300 dark:hover:border-red-500 hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 bg-gradient-to-br from-white to-gray-50 dark:from-gray-700 dark:to-gray-800 hover:from-red-50 hover:to-white dark:hover:from-gray-600 dark:hover:to-gray-700 cursor-pointer">
            {/* Icon */}
            <div className="flex items-center justify-center w-12 h-12 rounded-xl text-white bg-gradient-to-br from-red-500 to-red-600 transition-all duration-200 mb-3 shadow-sm group-hover:shadow-md group-hover:scale-105">
              <svg
                className="w-6 h-6 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>

            {/* Label */}
            <div className="text-sm font-medium text-gray-900 dark:text-white text-center mb-1">
              Expenses
            </div>

            {/* Value */}
            <div className="text-lg font-bold text-red-600 dark:text-red-400 text-center leading-tight">
              {dashboardService.formatCurrency(
                stats?.expenses || 0,
                currentCompany?.baseCurrency || "USD"
              )}
            </div>

            {/* Hover effect */}
            <div className="absolute inset-0 rounded-xl bg-red-50 dark:bg-red-900/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 -z-10" />
          </div>

          {/* Net Income */}
          <div className={`group relative flex flex-col items-center p-4 rounded-xl border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-gradient-to-br from-white to-gray-50 dark:from-gray-700 dark:to-gray-800 cursor-pointer ${
            (stats?.netIncome || 0) >= 0
              ? "hover:border-green-300 dark:hover:border-green-500 focus:ring-green-500 hover:from-green-50 hover:to-white dark:hover:from-gray-600 dark:hover:to-gray-700"
              : "hover:border-red-300 dark:hover:border-red-500 focus:ring-red-500 hover:from-red-50 hover:to-white dark:hover:from-gray-600 dark:hover:to-gray-700"
          }`}>
            {/* Icon */}
            <div
              className={`flex items-center justify-center w-12 h-12 rounded-xl text-white transition-all duration-200 mb-3 shadow-sm group-hover:shadow-md group-hover:scale-105 ${
                (stats?.netIncome || 0) >= 0
                  ? "bg-gradient-to-br from-green-600 to-green-700"
                  : "bg-gradient-to-br from-red-600 to-red-700"
              }`}
            >
              <svg
                className="w-6 h-6 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
            </div>

            {/* Label */}
            <div className="text-sm font-medium text-gray-900 dark:text-white text-center mb-1">
              Net Income
            </div>

            {/* Value */}
            <div
              className={`text-lg font-bold text-center leading-tight ${
                (stats?.netIncome || 0) >= 0
                  ? "text-green-600 dark:text-green-400"
                  : "text-red-600 dark:text-red-400"
              }`}
            >
              {dashboardService.formatCurrency(
                stats?.netIncome || 0,
                currentCompany?.baseCurrency || "USD"
              )}
            </div>

            {/* Hover effect */}
            <div className={`absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 -z-10 ${
              (stats?.netIncome || 0) >= 0
                ? "bg-green-50 dark:bg-green-900/20"
                : "bg-red-50 dark:bg-red-900/20"
            }`} />
          </div>

          {/* Total Contacts */}
          <div className="group relative flex flex-col items-center p-4 rounded-xl border border-gray-200 dark:border-gray-700 dark:border-gray-600 hover:border-yellow-300 dark:hover:border-yellow-500 hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 bg-gradient-to-br from-white to-gray-50 dark:from-gray-700 dark:to-gray-800 hover:from-yellow-50 hover:to-white dark:hover:from-gray-600 dark:hover:to-gray-700 cursor-pointer">
            {/* Icon */}
            <div className="flex items-center justify-center w-12 h-12 rounded-xl text-white bg-gradient-to-br from-yellow-500 to-yellow-600 transition-all duration-200 mb-3 shadow-sm group-hover:shadow-md group-hover:scale-105">
              <svg
                className="w-6 h-6 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"
                  clipRule="evenodd"
                />
              </svg>
            </div>

            {/* Label */}
            <div className="text-sm font-medium text-gray-900 dark:text-white text-center mb-1">
              Contacts
            </div>

            {/* Value */}
            <div className="text-lg font-bold text-yellow-600 dark:text-yellow-400 text-center leading-tight">
              {stats?.totalContacts || 0}
            </div>

            {/* Hover effect */}
            <div className="absolute inset-0 rounded-xl bg-yellow-50 dark:bg-yellow-900/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 -z-10" />
          </div>

          {/* Accounts Receivable */}
          <div className="group relative flex flex-col items-center p-4 rounded-xl border border-gray-200 dark:border-gray-700 dark:border-gray-600 hover:border-indigo-300 dark:hover:border-indigo-500 hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 bg-gradient-to-br from-white to-gray-50 dark:from-gray-700 dark:to-gray-800 hover:from-indigo-50 hover:to-white dark:hover:from-gray-600 dark:hover:to-gray-700 cursor-pointer">
            {/* Icon */}
            <div className="flex items-center justify-center w-12 h-12 rounded-xl text-white bg-gradient-to-br from-indigo-500 to-indigo-600 transition-all duration-200 mb-3 shadow-sm group-hover:shadow-md group-hover:scale-105">
              <svg
                className="w-6 h-6 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z"
                  clipRule="evenodd"
                />
              </svg>
            </div>

            {/* Label */}
            <div className="text-sm font-medium text-gray-900 dark:text-white text-center mb-1">
              A/R
            </div>

            {/* Value */}
            <div className="text-lg font-bold text-indigo-600 dark:text-indigo-400 text-center leading-tight">
              {dashboardService.formatCurrency(
                stats?.accountsReceivable || 0,
                currentCompany?.baseCurrency || "USD"
              )}
            </div>

            {/* Hover effect */}
            <div className="absolute inset-0 rounded-xl bg-indigo-50 dark:bg-indigo-900/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 -z-10" />
          </div>

          {/* Cash Balance */}
          <div className="group relative flex flex-col items-center p-4 rounded-xl border border-gray-200 dark:border-gray-700 dark:border-gray-600 hover:border-emerald-300 dark:hover:border-emerald-500 hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 bg-gradient-to-br from-white to-gray-50 dark:from-gray-700 dark:to-gray-800 hover:from-emerald-50 hover:to-white dark:hover:from-gray-600 dark:hover:to-gray-700 cursor-pointer">
            {/* Icon */}
            <div className="flex items-center justify-center w-12 h-12 rounded-xl text-white bg-gradient-to-br from-emerald-500 to-emerald-600 transition-all duration-200 mb-3 shadow-sm group-hover:shadow-md group-hover:scale-105">
              <svg
                className="w-6 h-6 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
                  clipRule="evenodd"
                />
              </svg>
            </div>

            {/* Label */}
            <div className="text-sm font-medium text-gray-900 dark:text-white text-center mb-1">
              Cash
            </div>

            {/* Value */}
            <div className="text-lg font-bold text-emerald-600 dark:text-emerald-400 text-center leading-tight">
              {dashboardService.formatCurrency(
                stats?.cashBalance || 0,
                currentCompany?.baseCurrency || "USD"
              )}
            </div>

            {/* Hover effect */}
            <div className="absolute inset-0 rounded-xl bg-emerald-50 dark:bg-emerald-900/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 -z-10" />
          </div>
        </div>
      </div>

      {/* Alert System */}
      {stats && (
        <AlertSystem
          alerts={generateAlerts(stats).filter(
            (alert) => !dismissedAlerts.includes(alert.id)
          )}
          onDismiss={(alertId) =>
            setDismissedAlerts((prev) => [...prev, alertId])
          }
        />
      )}

      {/* Quick Actions */}
      <QuickActions />

      {/* Revenue Trend & KPI Cards Side by Side */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Revenue Trend - Takes 2/3 of the width */}
        <div className="lg:col-span-2">
          {chartLoading ? (
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow h-64 flex items-center justify-center">
              <div className="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Loading revenue trend...</div>
            </div>
          ) : (
            <TrendChart
              title="Revenue Trend"
              description="Track your revenue performance over the last 6 months"
              data={revenueTrend}
              color="#3B82F6"
              showGrowth={true}
              currency={currentCompany?.baseCurrency || "USD"}
            />
          )}
        </div>

        {/* KPI Cards - Takes 1/3 of the width */}
        <div className="lg:col-span-1">
          {stats && (
            <KPICards
              stats={stats}
              currency={currentCompany?.baseCurrency || "USD"}
            />
          )}
        </div>
      </div>

      {/* Revenue vs Expense Comparison & Pie Chart Side by Side */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Revenue vs Expense Comparison - Takes 2/3 of the width */}
        <div className="lg:col-span-2">
          {chartLoading ? (
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow h-80 flex items-center justify-center">
              <div className="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                Loading revenue vs expense comparison...
              </div>
            </div>
          ) : (
            <BarChart
              title="Revenue vs Expense Comparison"
              description="Compare your revenue and expenses over the last 6 months"
              data={
                // Transform the separate revenue and expense arrays into combined data
                // Create a map of expenses by period for easy lookup
                (() => {
                  const expenseMap = new Map(
                    revenueExpenseComparison.expenses.map((exp) => [
                      exp.period,
                      exp.value,
                    ])
                  );

                  return revenueExpenseComparison.revenue.map(
                    (revenuePoint) => ({
                      period: revenuePoint.period,
                      revenue: revenuePoint.value,
                      expenses: expenseMap.get(revenuePoint.period) || 0,
                    })
                  );
                })()
              }
              currency={currentCompany?.baseCurrency || "USD"}
              height={320}
            />
          )}
        </div>

        {/* Expense Breakdown Pie Chart - Takes 1/3 of the width */}
        <div className="lg:col-span-1">
          {chartLoading ? (
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow h-80 flex items-center justify-center">
              <div className="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Loading expense breakdown...</div>
            </div>
          ) : (
            <PieChart
              title="Expense Breakdown"
              description="Analyze your spending across different categories"
              data={
                expenseBreakdown.length > 0
                  ? expenseBreakdown
                  : [
                      // Fallback sample data when no expenses exist
                      {
                        label: "Operating Expenses",
                        value: (stats?.expenses || 1000) * 0.4,
                        color: "#3B82F6",
                      },
                      {
                        label: "Cost of Goods Sold",
                        value: (stats?.expenses || 1000) * 0.3,
                        color: "#60A5FA",
                      },
                      {
                        label: "Administrative",
                        value: (stats?.expenses || 1000) * 0.2,
                        color: "#93C5FD",
                      },
                      {
                        label: "Other",
                        value: (stats?.expenses || 1000) * 0.1,
                        color: "#DBEAFE",
                      },
                    ]
              }
              currency={currentCompany?.baseCurrency || "USD"}
            />
          )}
        </div>
      </div>

      {/* Cash Flow Trend & Recent Activity Side by Side */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Cash Flow Trend - Takes 2/3 of the width */}
        <div className="lg:col-span-2">
          {chartLoading ? (
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow h-80 flex items-center justify-center">
              <div className="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Loading cash flow trend...</div>
            </div>
          ) : (
            <TrendChart
              title="Cash Flow Trend"
              description="Monitor your cash flow performance over the last 6 months"
              data={cashFlowTrend}
              color="#1E40AF"
              showGrowth={true}
              currency={currentCompany?.baseCurrency || "USD"}
              height={320}
            />
          )}
        </div>

        {/* Recent Activity - Takes 1/3 of the width */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg h-[520px] flex flex-col">
            <div className="px-4 py-5 sm:p-6 flex flex-col h-full">
              <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                Recent Activity
              </h3>
              <div className="mt-5 flex-1 overflow-y-auto scrollbar-hide">
                {recentActivity && recentActivity.length > 0 ? (
                  <div className="flow-root">
                    <ul className="-mb-8">
                      {recentActivity.map((activity, index) => (
                        <li key={activity.id}>
                          <div className="relative pb-8">
                            {index !== recentActivity.length - 1 ? (
                              <span
                                className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-600"
                                aria-hidden="true"
                              />
                            ) : null}
                            <div className="relative flex space-x-3">
                              <div>
                                <span className="h-8 w-8 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center ring-8 ring-white dark:ring-gray-800">
                                  <span className="text-sm">
                                    {dashboardService.getActivityIcon(
                                      activity.type
                                    )}
                                  </span>
                                </span>
                              </div>
                              <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                <div>
                                  <p className="text-sm text-gray-900 dark:text-white">
                                    {activity.title}
                                  </p>
                                  <p className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                    {activity.description}
                                  </p>
                                  {activity.amount && (
                                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                                      {dashboardService.formatCurrency(
                                        activity.amount,
                                        activity.currency ||
                                          currentCompany?.baseCurrency ||
                                          "USD"
                                      )}
                                    </p>
                                  )}
                                </div>
                                <div className="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                  <div>
                                    {dashboardService.formatRelativeTime(
                                      activity.date
                                    )}
                                  </div>
                                  {activity.status && (
                                    <span
                                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${dashboardService.getStatusColor(
                                        activity.status
                                      )}`}
                                    >
                                      {activity.status}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                ) : (
                  <div className="text-center bg-white dark:bg-gray-800 py-12">
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                      />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                      No recent activity
                    </h3>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                      Get started by creating your first transaction or invoice.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
