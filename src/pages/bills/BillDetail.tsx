import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import {
  ArrowLeftIcon,
  PencilIcon,
  PrinterIcon,
  CheckIcon,
  XMarkIcon,
  CreditCardIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import billService from "../../services/billService";
import type { Bill } from "../../types/bill";
import {
  BillStatusLabels,
  BillStatusColors,
  canEditBill,
  canApproveBill,
  canPayBill,
  canCancelBill,
  isOverdue,
  getDaysOverdue,
} from "../../types/bill";

const BillDetail = () => {
  const { billId } = useParams<{ billId: string }>();
  const navigate = useNavigate();
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  const [bill, setBill] = useState<Bill | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    if (currentCompany && billId) {
      loadBill();
    }
  }, [currentCompany, billId]);

  const loadBill = async () => {
    if (!currentCompany || !billId) return;

    try {
      setLoading(true);
      const billData = await billService.getBill(currentCompany.id, billId);
      setBill(billData);
    } catch (error) {
      console.error("Failed to load bill:", error);
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to load bill",
      });
      navigate("/bills");
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (status: string, notes?: string) => {
    if (!currentCompany || !billId) return;

    try {
      setActionLoading(true);
      await billService.updateBillStatus(currentCompany.id, billId, {
        status: status as any,
        notes,
      });

      addNotification({
        type: "success",
        title: "Success",
        message: "Bill status updated successfully",
      });

      loadBill();
    } catch (error) {
      console.error("Failed to update status:", error);
      addNotification({
        type: "error",
        title: "Error",
        message: "Failed to update bill status",
      });
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (bill: Bill) => {
    let status = bill.status;
    let color = BillStatusColors[status];
    let label = BillStatusLabels[status];

    // Check if bill is overdue
    if (isOverdue(bill)) {
      status = "OVERDUE";
      color = "red";
      label = `Overdue (${getDaysOverdue(bill)} days)`;
    }

    const colorClasses = {
      gray: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
      yellow:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-300",
      blue: "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300",
      orange:
        "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-300",
      green:
        "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300",
      red: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300",
    };

    return (
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          colorClasses[color as keyof typeof colorClasses]
        }`}
      >
        {label}
      </span>
    );
  };

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">
          Please select a company
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!bill) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">Bill not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate("/bills")}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Bills
          </button>
        </div>

        <div className="flex items-center space-x-3">
          {/* Status Actions */}
          {canApproveBill(bill.status) && (
            <button
              onClick={() => handleStatusUpdate("APPROVED")}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              <CheckIcon className="h-4 w-4 mr-1" />
              Approve
            </button>
          )}

          {canPayBill(bill.status) && bill.balanceDue > 0 && (
            <button
              onClick={() => navigate(`/bills/${billId}/pay`)}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <CreditCardIcon className="h-4 w-4 mr-1" />
              Pay Bill
            </button>
          )}

          {canCancelBill(bill.status) && (
            <button
              onClick={() => handleStatusUpdate("CANCELLED")}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              <XMarkIcon className="h-4 w-4 mr-1" />
              Cancel
            </button>
          )}

          {/* Edit Button */}
          {canEditBill(bill.status) && (
            <button
              onClick={() => navigate(`/bills/${billId}/edit`)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <PencilIcon className="h-4 w-4 mr-1" />
              Edit
            </button>
          )}

          {/* Print */}
          <button
            onClick={() => window.print()}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <PrinterIcon className="h-4 w-4 mr-1" />
            Print
          </button>
        </div>
      </div>

      {/* Bill Header */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              Bill {bill.billNumber}
            </h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Created on {billService.formatDate(bill.createdAt)}
            </p>
            {bill.vendorInvoiceNumber && (
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Vendor Invoice: {bill.vendorInvoiceNumber}
              </p>
            )}
          </div>
          <div className="text-right">
            {getStatusBadge(bill)}
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              Total:{" "}
              {billService.formatCurrency(bill.totalAmount, bill.currency)}
            </p>
            {bill.balanceDue > 0 && (
              <p className="mt-1 text-sm font-medium text-red-600 dark:text-red-400">
                Balance Due:{" "}
                {billService.formatCurrency(bill.balanceDue, bill.currency)}
              </p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Vendor Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Vendor Information
            </h3>
            <div className="space-y-2">
              <p className="text-sm">
                <span className="font-medium text-gray-900 dark:text-white">
                  Name:
                </span>{" "}
                <span className="text-gray-600 dark:text-gray-400">
                  {bill.vendorDisplayName || bill.vendorName}
                </span>
              </p>
              {bill.vendorEmail && (
                <p className="text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">
                    Email:
                  </span>{" "}
                  <span className="text-gray-600 dark:text-gray-400">
                    {bill.vendorEmail}
                  </span>
                </p>
              )}
              {bill.vendorPhone && (
                <p className="text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">
                    Phone:
                  </span>{" "}
                  <span className="text-gray-600 dark:text-gray-400">
                    {bill.vendorPhone}
                  </span>
                </p>
              )}
              {bill.vendorBillingAddress && (
                <p className="text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">
                    Address:
                  </span>{" "}
                  <span className="text-gray-600 dark:text-gray-400 whitespace-pre-line">
                    {bill.vendorBillingAddress}
                  </span>
                </p>
              )}
            </div>
          </div>

          {/* Bill Details */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Bill Details
            </h3>
            <div className="space-y-2">
              <p className="text-sm">
                <span className="font-medium text-gray-900 dark:text-white">
                  Bill Date:
                </span>{" "}
                <span className="text-gray-600 dark:text-gray-400">
                  {billService.formatDate(bill.billDate)}
                </span>
              </p>
              {bill.dueDate && (
                <p className="text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">
                    Due Date:
                  </span>{" "}
                  <span
                    className={`text-gray-600 dark:text-gray-400 ${
                      isOverdue(bill)
                        ? "text-red-600 dark:text-red-400 font-medium"
                        : ""
                    }`}
                  >
                    {billService.formatDate(bill.dueDate)}
                    {isOverdue(bill) &&
                      ` (${getDaysOverdue(bill)} days overdue)`}
                  </span>
                </p>
              )}
              {bill.paymentTerms && (
                <p className="text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">
                    Payment Terms:
                  </span>{" "}
                  <span className="text-gray-600 dark:text-gray-400">
                    {bill.paymentTerms}
                  </span>
                </p>
              )}
              <p className="text-sm">
                <span className="font-medium text-gray-900 dark:text-white">
                  Currency:
                </span>{" "}
                <span className="text-gray-600 dark:text-gray-400">
                  {bill.currency}
                </span>
              </p>
              {bill.exchangeRate !== 1 && (
                <p className="text-sm">
                  <span className="font-medium text-gray-900 dark:text-white">
                    Exchange Rate:
                  </span>{" "}
                  <span className="text-gray-600 dark:text-gray-400">
                    {bill.exchangeRate}
                  </span>
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Line Items */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Line Items
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Unit
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Quantity
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Unit Cost
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Discount
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Tax
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Total
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {bill.lineItems?.map((item) => (
                <tr key={item.id}>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    <div>
                      <div className="font-medium">{item.description}</div>
                      {item.itemCode && (
                        <div className="text-gray-500 dark:text-gray-400 text-xs">
                          Code: {item.itemCode}
                        </div>
                      )}
                      {item.notes && (
                        <div className="text-gray-500 dark:text-gray-400 text-xs mt-1">
                          {item.notes}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {item.unitOfMeasure || "-"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                    {item.quantity}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                    {billService.formatCurrency(item.unitCost, bill.currency)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                    {item.discountPercentage > 0
                      ? `${item.discountPercentage}%`
                      : "-"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                    {item.taxRate > 0 ? `${item.taxRate}%` : "-"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900 dark:text-white">
                    {billService.formatCurrency(item.lineTotal, bill.currency)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Totals */}
        <div className="bg-gray-50 dark:bg-gray-700 px-6 py-4">
          <div className="flex justify-end">
            <div className="w-64 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  Subtotal:
                </span>
                <span className="text-gray-900 dark:text-white">
                  {billService.formatCurrency(bill.subtotal, bill.currency)}
                </span>
              </div>
              {bill.discountAmount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    Discount:
                  </span>
                  <span className="text-gray-900 dark:text-white">
                    -
                    {billService.formatCurrency(
                      bill.discountAmount,
                      bill.currency
                    )}
                  </span>
                </div>
              )}
              {bill.taxAmount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Tax:</span>
                  <span className="text-gray-900 dark:text-white">
                    {billService.formatCurrency(bill.taxAmount, bill.currency)}
                  </span>
                </div>
              )}
              <div className="flex justify-between text-base font-medium border-t border-gray-200 dark:border-gray-600 pt-2">
                <span className="text-gray-900 dark:text-white">Total:</span>
                <span className="text-gray-900 dark:text-white">
                  {billService.formatCurrency(bill.totalAmount, bill.currency)}
                </span>
              </div>
              {bill.paidAmount > 0 && (
                <div className="flex justify-between text-sm text-green-600 dark:text-green-400">
                  <span>Paid:</span>
                  <span>
                    -
                    {billService.formatCurrency(bill.paidAmount, bill.currency)}
                  </span>
                </div>
              )}
              {bill.balanceDue > 0 && (
                <div className="flex justify-between text-base font-medium text-red-600 dark:text-red-400 border-t border-gray-200 dark:border-gray-600 pt-2">
                  <span>Balance Due:</span>
                  <span>
                    {billService.formatCurrency(bill.balanceDue, bill.currency)}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillDetail;
