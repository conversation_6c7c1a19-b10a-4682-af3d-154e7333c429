import { useState, useEffect } from "react";
import {
  PlusIcon,
  CalculatorIcon,
  DocumentTextIcon,
  CogIcon,
} from "@heroicons/react/24/outline";
import type { TaxRate, TaxCategory } from "../../types/tax";
import { taxService } from "../../services/taxService";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import TaxRateList from "../../components/tax/TaxRateList";
import TaxRateForm from "../../components/tax/TaxRateForm";
import TaxCategoryManagement from "../../components/tax/TaxCategoryManagement";
import TaxReports from "../../components/tax/TaxReports";
import TaxSettings from "../../components/tax/TaxSettings";

type TabType = "rates" | "categories" | "reports" | "settings";

export default function TaxManagement() {
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  const [activeTab, setActiveTab] = useState<TabType>("rates");
  const [taxRates, setTaxRates] = useState<TaxRate[]>([]);
  const [taxCategories, setTaxCategories] = useState<TaxCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [showTaxRateForm, setShowTaxRateForm] = useState(false);
  const [editingTaxRate, setEditingTaxRate] = useState<TaxRate | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const tabs = [
    {
      id: "rates" as const,
      name: "Tax Rates",
      icon: CalculatorIcon,
      description: "Manage tax rates and jurisdictions",
    },
    {
      id: "categories" as const,
      name: "Tax Categories",
      icon: DocumentTextIcon,
      description: "Configure product/service tax categories",
    },
    {
      id: "reports" as const,
      name: "Tax Reports",
      icon: DocumentTextIcon,
      description: "Generate tax compliance reports",
    },
    {
      id: "settings" as const,
      name: "Tax Settings",
      icon: CogIcon,
      description: "Configure tax calculation settings",
    },
  ];

  useEffect(() => {
    if (currentCompany) {
      loadData();
    }
  }, [currentCompany, refreshTrigger]);

  const loadData = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      const [ratesData, categoriesData] = await Promise.all([
        taxService.getTaxRates(currentCompany.id, { _t: Date.now() }),
        taxService.getTaxCategories(currentCompany.id, { _t: Date.now() }),
      ]);
      console.log("🔍 Tax Management - Loaded data:", {
        ratesData,
        categoriesData,
      });
      setTaxRates(ratesData || []);
      setTaxCategories(categoriesData || []);
    } catch (error) {
      console.error("Failed to load tax management data:", error);
      showNotification({
        type: "error",
        title: "Error",
        message: "Failed to load tax data",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTaxRate = () => {
    setEditingTaxRate(null);
    setShowTaxRateForm(true);
  };

  const handleEditTaxRate = (taxRate: TaxRate) => {
    setEditingTaxRate(taxRate);
    setShowTaxRateForm(true);
  };

  const handleTaxRateFormClose = () => {
    setShowTaxRateForm(false);
    setEditingTaxRate(null);
  };

  const handleTaxRateSave = async (taxRateData: any) => {
    try {
      if (editingTaxRate) {
        await taxService.updateTaxRate(editingTaxRate.id, taxRateData);
        showNotification({
          type: "success",
          title: "Success",
          message: "Tax rate updated successfully",
        });
      } else {
        await taxService.createTaxRate({
          ...taxRateData,
          companyId: currentCompany!.id,
        });
        showNotification({
          type: "success",
          title: "Success",
          message: "Tax rate created successfully",
        });
      }
      setRefreshTrigger((prev) => prev + 1);
      handleTaxRateFormClose();
    } catch (error: any) {
      console.error("Failed to save tax rate:", error);
      showNotification({
        type: "error",
        title: "Error",
        message: (error as any).response?.data?.error || "Failed to save tax rate",
      });
    }
  };

  const handleDeleteTaxRate = async (taxRateId: string) => {
    try {
      await taxService.deleteTaxRate(taxRateId);
      showNotification({
        type: "success",
        title: "Success",
        message: "Tax rate deleted successfully",
      });
      setRefreshTrigger((prev) => prev + 1);
    } catch (error: any) {
      console.error("Failed to delete tax rate:", error);
      showNotification({
        type: "error",
        title: "Error",
        message: (error as any).response?.data?.error || "Failed to delete tax rate",
      });
    }
  };

  const handleCategoryUpdate = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:text-3xl sm:truncate">
            Tax Management
          </h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Manage tax rates, categories, and compliance for{" "}
            {currentCompany?.name}
          </p>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm cursor-pointer ${
                  isActive
                    ? "border-blue-500 text-blue-600 dark:text-blue-400"
                    : "border-transparent text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:border-gray-600"
                }`}
              >
                <tab.icon
                  className={`-ml-0.5 mr-2 h-5 w-5 ${
                    isActive
                      ? "text-blue-500 dark:text-blue-400"
                      : "text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"
                  }`}
                />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === "rates" && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Tax Rates</h3>
              <button
                onClick={handleCreateTaxRate}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Tax Rate
              </button>
            </div>
            <TaxRateList
              taxRates={taxRates || []}
              onEdit={handleEditTaxRate}
              onDelete={handleDeleteTaxRate}
            />
          </div>
        )}

        {activeTab === "categories" && (
          <TaxCategoryManagement
            categories={taxCategories || []}
            taxRates={taxRates || []}
            onUpdate={handleCategoryUpdate}
          />
        )}

        {activeTab === "reports" && (
          <TaxReports companyId={currentCompany?.id || ""} />
        )}

        {activeTab === "settings" && <TaxSettings taxRates={taxRates || []} />}
      </div>

      {/* Tax Rate Form Modal */}
      {showTaxRateForm && (
        <TaxRateForm
          taxRate={editingTaxRate}
          onSave={handleTaxRateSave}
          onCancel={handleTaxRateFormClose}
        />
      )}
    </div>
  );
}
