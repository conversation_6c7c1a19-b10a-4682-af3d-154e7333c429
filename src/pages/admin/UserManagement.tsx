import { useState, useEffect } from "react";
import {
  PlusIcon,
  UserIcon,
  ShieldCheckIcon,
  ClockIcon,
  GlobeAltIcon,
  PencilIcon,
} from "@heroicons/react/24/outline";
import type { User, Role } from "../../types";
import { userService } from "../../services/userService";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import UserList from "../../components/admin/UserList";
import UserForm from "../../components/admin/UserForm";
import RoleManagement from "../../components/admin/RoleManagement";
import UserInvitation from "../../components/admin/UserInvitation";

type TabType = "users" | "roles" | "invitations" | "field-permissions" | "security";

export default function UserManagement() {
  const { currentCompany } = useCompany();
  const { showNotification } = useNotification();
  const [activeTab, setActiveTab] = useState<TabType>("users");
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [showUserForm, setShowUserForm] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const tabs = [
    {
      id: "users" as const,
      name: "Users",
      icon: UserIcon,
      description: "Manage user accounts and permissions",
    },
    {
      id: "roles" as const,
      name: "Roles & Permissions",
      icon: ShieldCheckIcon,
      description: "Configure roles and permissions",
    },
    {
      id: "field-permissions" as const,
      name: "Field Permissions",
      icon: PencilIcon,
      description: "Configure field-level access controls",
    },
    {
      id: "security" as const,
      name: "Security Controls",
      icon: GlobeAltIcon,
      description: "Time-based access and IP restrictions",
    },
    {
      id: "invitations" as const,
      name: "Invitations",
      icon: PlusIcon,
      description: "Invite new users to the system",
    },
  ];

  useEffect(() => {
    if (currentCompany) {
      loadData();
    }
  }, [currentCompany, refreshTrigger]);

  const loadData = async () => {
    if (!currentCompany) return;

    try {
      setLoading(true);
      const [usersData, rolesData] = await Promise.all([
        userService.getUsers({ companyId: currentCompany.id }),
        userService.getRoles(),
      ]);
      setUsers(usersData.users || []);
      setRoles(rolesData || []);
    } catch (error) {
      console.error("Failed to load user management data:", error);
      showNotification({
        type: "error",
        title: "Error",
        message: "Failed to load user data",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = () => {
    setEditingUser(null);
    setShowUserForm(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setShowUserForm(true);
  };

  const handleUserFormClose = () => {
    setShowUserForm(false);
    setEditingUser(null);
  };

  const handleUserSave = async (userData: any) => {
    try {
      if (editingUser) {
        await userService.updateUser(editingUser.id, userData);
        showNotification({
          type: "success",
          title: "User Updated",
          message: "User updated successfully",
        });
      } else {
        await userService.createUser(userData);
        showNotification({
          type: "success",
          title: "User Created",
          message: "User created successfully",
        });
      }
      setRefreshTrigger((prev) => prev + 1);
      handleUserFormClose();
    } catch (error: any) {
      console.error("Failed to save user:", error);
      showNotification(
{
          type: "error",
          title: "Failed to save user",
          message: error.response?.data?.error || "Failed to save user",
        }
      );
    }
  };

  const handleDeleteUser = async (userId: string) => {
    try {
      await userService.deleteUser(userId);
      showNotification({
        type: "success",
        title: "User Deleted",
        message: "User deleted successfully",
      });
      setRefreshTrigger((prev) => prev + 1);
    } catch (error: any) {
      console.error("Failed to delete user:", error);
      showNotification(
        {
          type: "error",
          title: "Failed to delete user",
          message: error.response?.data?.error || "Failed to delete user",
        }
      );
    }
  };

  const handleRoleUpdate = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:text-3xl sm:truncate">
            User Management
          </h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Manage users, roles, and permissions for {currentCompany?.name}
          </p>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm cursor-pointer ${
                  isActive
                    ? "border-blue-500 text-blue-600 dark:text-blue-400"
                    : "border-transparent text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:text-gray-300 hover:border-gray-300 dark:border-gray-600"
                }`}
              >
                <tab.icon
                  className={`-ml-0.5 mr-2 h-5 w-5 ${
                    isActive
                      ? "text-blue-500 dark:text-blue-400"
                      : "text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"
                  }`}
                />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === "users" && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Users</h3>
              <button
                onClick={handleCreateUser}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add User
              </button>
            </div>
            <UserList
              users={users}
              roles={roles}
              onEdit={handleEditUser}
              onDelete={handleDeleteUser}
            />
          </div>
        )}

        {activeTab === "roles" && (
          <RoleManagement roles={roles} onUpdate={handleRoleUpdate} />
        )}

        {activeTab === "field-permissions" && (
          <FieldPermissionsTab
            users={users}
            selectedUser={selectedUser}
            onSelectUser={setSelectedUser}
          />
        )}

        {activeTab === "security" && (
          <SecurityTab
            users={users}
            selectedUser={selectedUser}
            onSelectUser={setSelectedUser}
          />
        )}

        {activeTab === "invitations" && (
          <UserInvitation
            roles={roles}
            onInviteSent={() => setRefreshTrigger((prev) => prev + 1)}
          />
        )}
      </div>

      {/* User Form Modal */}
      {showUserForm && (
        <UserForm
          user={editingUser}
          roles={roles}
          onSave={handleUserSave}
          onCancel={handleUserFormClose}
        />
      )}
    </div>
  );
}

// Field Permissions Tab Component
interface FieldPermissionsTabProps {
  users: User[];
  selectedUser: User | null;
  onSelectUser: (user: User | null) => void;
}

function FieldPermissionsTab({ users, selectedUser, onSelectUser }: FieldPermissionsTabProps) {
  return (
    <div className="grid bg-white dark:bg-gray-800 grid-cols-1 lg:grid-cols-3 gap-6">
      {/* User Selection */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Select User</h3>
        <div className="space-y-2">
          {users.map((user) => (
            <button
              key={user.id}
              onClick={() => onSelectUser(user)}
              className={`w-full text-left p-3 rounded-md transition-colors ${
                selectedUser?.id === user.id
                  ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 border'
                  : 'hover:bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-700'
              }`}
            >
              <div className="font-medium text-sm">{user.firstName} {user.lastName}</div>
              <div className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{user.role?.name}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Field Permissions */}
      <div className="lg:col-span-2 bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Field-Level Permissions</h3>
        {selectedUser ? (
          <div>
            <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
              <div className="font-medium text-blue-900">
                {selectedUser.firstName} {selectedUser.lastName}
              </div>
              <div className="text-sm text-blue-700">{selectedUser.email}</div>
            </div>

            <div className="space-y-4 bg-white dark:bg-gray-800">
              <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-4">
                Configure field-level access permissions for this user. These settings override role-based permissions.
              </div>

              {/* Permission Matrix */}
              <div className="border rounded-lg overflow-hidden bg-white dark:bg-gray-800">
                <table className="min-w-full bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr className="bg-white dark:bg-gray-800">
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase">Resource</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase">Field</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase">Permission</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <tr className="bg-white dark:bg-gray-800">
                      <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">Transactions</td>
                      <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">Amount</td>
                      <td className="px-4 py-3">
                        <select className="text-sm border-gray-300 dark:border-gray-600 rounded-md">
                          <option value="READ">Read Only</option>
                          <option value="WRITE">Read & Write</option>
                          <option value="NONE">No Access</option>
                        </select>
                      </td>
                    </tr>
                    <tr className="bg-white dark:bg-gray-800">
                      <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">Transactions</td>
                      <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">Description</td>
                      <td className="px-4 py-3">
                        <select className="text-sm border-gray-300 dark:border-gray-600 rounded-md">
                          <option value="READ">Read Only</option>
                          <option value="WRITE">Read & Write</option>
                          <option value="NONE">No Access</option>
                        </select>
                      </td>
                    </tr>
                    <tr className="bg-white dark:bg-gray-800">
                      <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">Invoices</td>
                      <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">Total Amount</td>
                      <td className="px-4 py-3">
                        <select className="text-sm border-gray-300 dark:border-gray-600 rounded-md">
                          <option value="READ">Read Only</option>
                          <option value="WRITE">Read & Write</option>
                          <option value="NONE">No Access</option>
                        </select>
                      </td>
                    </tr>
                    <tr className="bg-white dark:bg-gray-800">
                      <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">Contacts</td>
                      <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">Email</td>
                      <td className="px-4 py-3">
                        <select className="text-sm border-gray-300 dark:border-gray-600 rounded-md">
                          <option value="READ">Read Only</option>
                          <option value="WRITE">Read & Write</option>
                          <option value="NONE">No Access</option>
                        </select>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                Save Permissions
              </button>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Select a user to configure field-level permissions
          </div>
        )}
      </div>
    </div>
  );
}

// Security Tab Component
interface SecurityTabProps {
  users: User[];
  selectedUser: User | null;
  onSelectUser: (user: User | null) => void;
}

function SecurityTab({ users, selectedUser, onSelectUser }: SecurityTabProps) {
  return (
    <div className="grid bg-white dark:bg-gray-800 grid-cols-1 lg:grid-cols-3 gap-6">
      {/* User Selection */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Select User</h3>
        <div className="space-y-2">
          {users.map((user) => (
            <button
              key={user.id}
              onClick={() => onSelectUser(user)}
              className={`w-full text-left p-3 rounded-md transition-colors ${
                selectedUser?.id === user.id
                  ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 border'
                  : 'hover:bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-700'
              }`}
            >
              <div className="font-medium text-sm">{user.firstName} {user.lastName}</div>
              <div className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{user.role?.name}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Security Controls */}
      <div className="lg:col-span-2 space-y-6">
        {selectedUser ? (
          <>
            {/* Time-based Access */}
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <div className="flex items-center mb-4">
                <ClockIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mr-2" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Time-based Access</h3>
              </div>
              <div className="space-y-4 bg-white dark:bg-gray-800">
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Restrict access to specific hours and days for {selectedUser.firstName} {selectedUser.lastName}
                </div>

                <div className="grid bg-white dark:bg-gray-800 grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Start Time</label>
                    <input type="time" className="w-full border-gray-300 dark:border-gray-600 rounded-md" defaultValue="09:00" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">End Time</label>
                    <input type="time" className="w-full border-gray-300 dark:border-gray-600 rounded-md" defaultValue="17:00" />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Allowed Days</label>
                  <div className="flex space-x-2">
                    {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (
                      <label key={day} className="flex items-center">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 dark:border-gray-600"
                          defaultChecked={index < 5}
                        />
                        <span className="ml-1 text-sm">{day}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                  Save Time Restrictions
                </button>
              </div>
            </div>

            {/* IP Restrictions */}
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <div className="flex items-center mb-4">
                <GlobeAltIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mr-2" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">IP Restrictions</h3>
              </div>
              <div className="space-y-4 bg-white dark:bg-gray-800">
                <div className="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                  Control which IP addresses {selectedUser.firstName} {selectedUser.lastName} can access the system from
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Allowed IP Addresses</label>
                  <textarea
                    className="w-full border-gray-300 dark:border-gray-600 rounded-md"
                    rows={3}
                    placeholder="*************&#10;10.0.0.0/24&#10;***********/24"
                  />
                  <div className="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">
                    Enter one IP address or CIDR range per line
                  </div>
                </div>

                <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                  Save IP Restrictions
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <div className="text-center py-8 text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
              Select a user to configure security controls
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
