import { useState } from "react";
import { PlusIcon } from "@heroicons/react/24/outline";
import type { Contact, ContactType } from "../../types";
import ContactList from "../../components/contacts/ContactList";
import ContactFilters from "../../components/contacts/ContactFilters";
import ContactForm from "../../components/contacts/ContactForm";
import ContactView from "../../components/contacts/ContactView";

export default function Contacts() {
  const [showForm, setShowForm] = useState(false);
  const [showView, setShowView] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [editingContact, setEditingContact] = useState<Contact | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [filters, setFilters] = useState<{
    contactType?: ContactType;
    isActive?: boolean;
    search?: string;
  }>({});

  const handleAddContact = () => {
    setEditingContact(null);
    setShowForm(true);
  };

  const handleEditContact = (contact: Contact) => {
    setEditingContact(contact);
    setShowForm(true);
    setShowView(false);
  };

  const handleViewContact = (contact: Contact) => {
    setSelectedContact(contact);
    setShowView(true);
  };

  const handleFormSave = (_contact: Contact) => {
    setShowForm(false);
    setEditingContact(null);
    setRefreshTrigger((prev) => prev + 1);
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setEditingContact(null);
  };

  const handleViewClose = () => {
    setShowView(false);
    setSelectedContact(null);
  };

  const handleFiltersChange = (newFilters: {
    contactType?: ContactType;
    isActive?: boolean;
    search?: string;
  }) => {
    setFilters(newFilters);
  };

  return (
    <div className="space-y-6">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Contacts</h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            Manage your customers, vendors, employees, and other contacts.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <button
            type="button"
            onClick={handleAddContact}
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto cursor-pointer"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Contact
          </button>
        </div>
      </div>

      {/* Filters */}
      <ContactFilters onFiltersChange={handleFiltersChange} />

      {/* Contact List */}
      <ContactList
        filters={filters}
        onView={handleViewContact}
        onEdit={handleEditContact}
        refreshTrigger={refreshTrigger}
      />

      {/* Contact Form Modal */}
      {showForm && (
        <ContactForm
          contact={editingContact || undefined}
          onSave={handleFormSave}
          onCancel={handleFormCancel}
        />
      )}

      {/* Contact View Modal */}
      {showView && selectedContact && (
        <ContactView
          contact={selectedContact}
          onEdit={() => handleEditContact(selectedContact)}
          onClose={handleViewClose}
        />
      )}
    </div>
  );
}
