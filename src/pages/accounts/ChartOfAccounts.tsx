import { useState } from "react";
import { Plus, Download, Upload } from "lucide-react";
import { useCompany } from "../../contexts/CompanyContext";
import { useAccounts } from "../../hooks/useAccounts";
import { useNotification } from "../../contexts/NotificationContext";
import { useConfirmDialog } from "../../hooks/useConfirmDialog";
import AccountTree from "../../components/accounts/AccountTree";
import AccountForm, {
  type AccountFormData,
} from "../../components/accounts/AccountForm";
import AccountFilters from "../../components/accounts/AccountFilters";
import ConfirmDialog from "../../components/ui/ConfirmDialog";
import type { Account } from "../../types";
import type { AccountFilters as AccountFiltersType } from "../../services/accountService";

export default function ChartOfAccounts() {
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();
  const { confirm, dialogProps } = useConfirmDialog();
  const [filters, setFilters] = useState<AccountFiltersType>({ active: true });
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);
  const [parentAccount, setParentAccount] = useState<Account | null>(null);

  const {
    accountsTree,
    accounts,
    loading,
    createAccount,
    updateAccount,
    deleteAccount,
  } = useAccounts({
    companyId: currentCompany?.id || "",
    filters,
    autoFetch: !!currentCompany?.id,
  });

  const handleCreateAccount = () => {
    setEditingAccount(null);
    setParentAccount(null);
    setIsFormOpen(true);
  };

  const handleEditAccount = (account: Account) => {
    setEditingAccount(account);
    setParentAccount(null);
    setIsFormOpen(true);
  };

  const handleCreateSubAccount = (parent: Account) => {
    setEditingAccount(null);
    setParentAccount(parent);
    setIsFormOpen(true);
  };

  const handleDeleteAccount = async (account: Account) => {
    const confirmed = await confirm({
      title: "Delete Account",
      message: `Are you sure you want to delete the account "${account.name}"? This action cannot be undone.`,
      confirmLabel: "Delete",
      cancelLabel: "Cancel",
      type: "danger",
    });

    if (confirmed) {
      try {
        await deleteAccount(account.id);
        addNotification(
          "success",
          "Account Deleted",
          `Account "${account.name}" has been deleted successfully.`
        );
      } catch (error) {
        console.error("Failed to delete account:", error);
        addNotification(
          "error",
          "Delete Failed",
          "Failed to delete account. It may be in use by transactions."
        );
      }
    }
  };

  const handleFormSubmit = async (data: AccountFormData) => {
    if (!currentCompany) return;

    try {
      const accountData = {
        companyId: currentCompany.id,
        code: data.code,
        name: data.name,
        description: data.description || undefined,
        accountType: data.accountType,
        accountSubtype: data.accountSubtype || undefined,
        parentAccountId: data.parentAccountId || parentAccount?.id || undefined,
        openingBalance: data.openingBalance,
        currency: data.currency,
      };

      if (editingAccount) {
        await updateAccount(editingAccount.id, accountData);
      } else {
        await createAccount(accountData);
      }

      // Close the form and reset state on successful submission
      setIsFormOpen(false);
      setEditingAccount(null);
      setParentAccount(null);
    } catch (error) {
      // Error handling is already done in the useAccounts hook
      // Just re-throw to let the AccountForm component handle the loading state
      throw error;
    }
  };

  const handleClearFilters = () => {
    setFilters({ active: true });
  };

  if (!currentCompany) {
    return (
      <div className="text-center bg-white dark:bg-gray-800 py-12">
        <p className="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
          Please select a company to view accounts.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Chart of Accounts
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
            Manage your company's accounts and account structure
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={() => {
              addNotification(
                "info",
                "Improved Feedback System",
                "This notification now has better width and readability! The confirmation dialogs are also wider and more professional."
              );
            }}
            className="inline-flex items-center px-3 py-2 border border-blue-300 shadow-sm text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:bg-blue-900/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Test Feedback
          </button>

          <button
            onClick={() => {
              /* TODO: Implement export */
            }}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>

          <button
            onClick={() => {
              /* TODO: Implement import */
            }}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <Upload className="h-4 w-4 mr-2" />
            Import
          </button>

          <button
            onClick={handleCreateAccount}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Account
          </button>
        </div>
      </div>

      {/* Filters */}
      <AccountFilters
        filters={filters}
        onFiltersChange={setFilters}
        onClearFilters={handleClearFilters}
      />

      {/* Account Tree */}
      <AccountTree
        accounts={accountsTree}
        onEditAccount={handleEditAccount}
        onDeleteAccount={handleDeleteAccount}
        onCreateSubAccount={handleCreateSubAccount}
        loading={loading}
      />

      {/* Account Form Modal */}
      <AccountForm
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSubmit={handleFormSubmit}
        account={editingAccount}
        companyId={currentCompany.id}
        parentAccounts={accounts}
      />

      {/* Confirmation Dialog */}
      <ConfirmDialog {...dialogProps} />
    </div>
  );
}
