import { useState, useEffect } from 'react';
import {
  BuildingLibraryIcon,
  PlusIcon,
  ArrowPathIcon,
  EyeIcon,
  TrashIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ChevronRightIcon,
  HomeIcon
} from '@heroicons/react/24/outline';
import { useCompany } from '../contexts/CompanyContext';
import { apiService } from '../services/api';
import BankReconciliationPage from './BankReconciliationPage';

interface BankAccount {
  id: string;
  bankName: string;
  accountName: string;
  accountNumber: string;
  accountType: 'CHECKING' | 'SAVINGS' | 'CREDIT_CARD' | 'INVESTMENT' | 'LOAN';
  balance: number;
  currency: string;
  isActive: boolean;
  lastSyncAt?: string;
}

interface BankTransaction {
  id: string;
  transactionId: string;
  amount: number;
  description: string;
  category: string;
  date: string;
  pending: boolean;
  merchantName?: string;
  isReconciled: boolean;
}

export default function Banking() {
  const { currentCompany } = useCompany();
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([]);
  const [transactions, setTransactions] = useState<BankTransaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<string | null>(null);
  const [showTransactions, setShowTransactions] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showReconciliation, setShowReconciliation] = useState(false);

  useEffect(() => {
    if (currentCompany) {
      loadBankAccounts();
    }
  }, [currentCompany]);

  const loadBankAccounts = async () => {
    if (!currentCompany) return;
    
    try {
      setLoading(true);
      setError(null);
      const accounts = await apiService.get(`/banks/${currentCompany.id}/accounts`);
      setBankAccounts(accounts as BankAccount[]);
    } catch (err: any) {
      console.error('Failed to load bank accounts:', err);
      setError('Failed to load bank accounts. Please check your integration settings.');
    } finally {
      setLoading(false);
    }
  };

  const loadTransactions = async (accountId?: string) => {
    if (!currentCompany) return;
    
    try {
      setLoading(true);
      const params = accountId ? { bankAccountId: accountId } : {};
      const response = await apiService.get(`/banks/${currentCompany.id}/transactions`, { params });
      setTransactions((response as any).transactions || []);
      setShowTransactions(true);
    } catch (err: any) {
      console.error('Failed to load transactions:', err);
      setError('Failed to load transactions.');
    } finally {
      setLoading(false);
    }
  };

  const syncTransactions = async (accountId?: string) => {
    if (!currentCompany) return;
    
    try {
      setSyncing(true);
      const response = await apiService.post(`/banks/${currentCompany.id}/sync`, {
        bankAccountId: accountId
      });
      
      // Show success message
      alert(`Sync complete: ${(response as any).newTransactions || 0} new transactions, ${(response as any).updatedTransactions || 0} updated`);
      
      await loadBankAccounts();
      if (showTransactions) {
        await loadTransactions(selectedAccount || undefined);
      }
    } catch (err: any) {
      console.error('Failed to sync transactions:', err);
      setError('Failed to sync transactions.');
    } finally {
      setSyncing(false);
    }
  };

  const disconnectAccount = async (accountId: string) => {
    if (!currentCompany) return;
    
    if (!confirm('Are you sure you want to disconnect this bank account?')) {
      return;
    }
    
    try {
      await apiService.delete(`/banks/${currentCompany.id}/accounts/${accountId}`);
      alert('Bank account disconnected successfully');
      await loadBankAccounts();
    } catch (err: any) {
      console.error('Failed to disconnect bank account:', err);
      setError('Failed to disconnect bank account.');
    }
  };

  const reconcileTransaction = async (transactionId: string) => {
    if (!currentCompany) return;
    
    try {
      await apiService.put(`/banks/${currentCompany.id}/transactions/${transactionId}/reconcile`, {});
      alert('Transaction reconciled successfully');
      await loadTransactions(selectedAccount || undefined);
    } catch (err: any) {
      console.error('Failed to reconcile transaction:', err);
      setError('Failed to reconcile transaction.');
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'CHECKING':
        return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800';
      case 'SAVINGS':
        return 'bg-green-100 dark:bg-green-900/20 text-green-800';
      case 'CREDIT_CARD':
        return 'bg-purple-100 text-purple-800';
      case 'INVESTMENT':
        return 'bg-orange-100 text-orange-800';
      case 'LOAN':
        return 'bg-red-100 dark:bg-red-900/20 text-red-800';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  // If showing reconciliation, render the reconciliation page with breadcrumb
  if (showReconciliation) {
    return (
      <div className="space-y-6">
        {/* Breadcrumb Navigation */}
        <nav className="flex" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-4">
            <li>
              <div>
                <button
                  onClick={() => setShowReconciliation(false)}
                  className="text-gray-400 hover:text-gray-500 flex items-center"
                >
                  <HomeIcon className="flex-shrink-0 h-5 w-5" aria-hidden="true" />
                  <span className="ml-2 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                    Banking & Integration
                  </span>
                </button>
              </div>
            </li>
            <li>
              <div className="flex items-center">
                <ChevronRightIcon className="flex-shrink-0 h-5 w-5 text-gray-400" aria-hidden="true" />
                <span className="ml-4 text-sm font-medium text-gray-900 dark:text-white">
                  Bank Reconciliation
                </span>
              </div>
            </li>
          </ol>
        </nav>

        {/* Reconciliation Page */}
        <BankReconciliationPage />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Banking & Integration</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Manage your connected bank accounts, transactions, and automated reconciliation
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => setShowReconciliation(true)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <CheckCircleIcon className="h-4 w-4 mr-2" />
            Bank Reconciliation
          </button>
          <button
            onClick={() => syncTransactions()}
            disabled={syncing || bankAccounts.length === 0}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-4 w-4 mr-2 ${syncing ? 'animate-spin' : ''}`} />
            {syncing ? 'Syncing...' : 'Sync All'}
          </button>
          <button
            onClick={() => window.location.href = '/settings'}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Connect Bank
          </button>
        </div>
      </div>

      {/* Quick Stats */}
      {bankAccounts.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BuildingLibraryIcon className="h-6 w-6 text-blue-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Connected Accounts
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-white">
                      {bankAccounts.filter(acc => acc.isActive).length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ArrowPathIcon className="h-6 w-6 text-green-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Total Balance
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-white">
                      {formatCurrency(bankAccounts.reduce((sum, acc) => sum + acc.balance, 0))}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircleIcon className="h-6 w-6 text-purple-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Reconciled Today
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-white">
                      {transactions.filter(t => t.isReconciled).length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ExclamationTriangleIcon className="h-6 w-6 text-yellow-500" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Pending Review
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-white">
                      {transactions.filter(t => !t.isReconciled && !t.pending).length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-400">{error}</div>
            </div>
          </div>
        </div>
      )}

      {/* Bank Accounts */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Connected Accounts</h2>
        </div>
        <div className="p-6">
          {loading && bankAccounts.length === 0 ? (
            <div className="text-center bg-white dark:bg-gray-800 py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Loading bank accounts...</p>
            </div>
          ) : bankAccounts.length === 0 ? (
            <div className="text-center bg-white dark:bg-gray-800 py-12">
              <BuildingLibraryIcon className="h-12 w-12 mx-auto text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No bank accounts connected</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                Connect your bank accounts to automatically sync transactions.
              </p>
              <div className="mt-6">
                <button
                  onClick={() => window.location.href = '/settings'}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Connect Your First Bank
                </button>
              </div>
            </div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {bankAccounts.map((account) => (
                <div key={account.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">{account.accountName}</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{account.bankName}</p>
                    </div>
                    <button
                      onClick={() => disconnectAccount(account.id)}
                      className="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 hover:text-red-500 dark:text-red-400"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Balance</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(account.balance, account.currency)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Type</span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getAccountTypeColor(account.accountType)}`}>
                        {account.accountType.replace('_', ' ')}
                      </span>
                    </div>
                    {account.lastSyncAt && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Last Sync</span>
                        <span className="text-sm text-gray-900 dark:text-white">{formatDate(account.lastSyncAt)}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="mt-4 flex gap-2">
                    <button
                      onClick={() => {
                        setSelectedAccount(account.id);
                        loadTransactions(account.id);
                      }}
                      className="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-xs font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700"
                    >
                      <EyeIcon className="h-4 w-4 mr-1" />
                      View
                    </button>
                    <button
                      onClick={() => syncTransactions(account.id)}
                      disabled={syncing}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:border-gray-600 shadow-sm text-xs font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:bg-gray-700 disabled:opacity-50"
                    >
                      <ArrowPathIcon className={`h-4 w-4 ${syncing ? 'animate-spin' : ''}`} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Transactions */}
      {showTransactions && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">Bank Transactions</h2>
              <button
                onClick={() => loadTransactions()}
                className="text-sm text-primary-600 hover:text-primary-500"
              >
                Show All Accounts
              </button>
            </div>
          </div>
          <div className="p-6">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Loading transactions...</p>
              </div>
            ) : transactions.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">No transactions found. Try syncing your accounts.</p>
              </div>
            ) : (
              <div className="space-y-3">
                {transactions.map((transaction) => (
                  <div
                    key={transaction.id}
                    className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm font-medium text-gray-900 dark:text-white">{transaction.description}</span>
                        {transaction.pending && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800">
                            Pending
                          </span>
                        )}
                        {transaction.isReconciled && (
                          <CheckCircleIcon className="h-4 w-4 text-green-500 dark:text-green-400" />
                        )}
                      </div>
                      <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                        <span>{formatDate(transaction.date)}</span>
                        <span>{transaction.category}</span>
                        {transaction.merchantName && <span>{transaction.merchantName}</span>}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <span className={`text-sm font-medium ${
                        transaction.amount >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                      }`}>
                        {transaction.amount >= 0 ? '+' : ''}{formatCurrency(transaction.amount)}
                      </span>
                      
                      {!transaction.isReconciled && (
                        <button
                          onClick={() => reconcileTransaction(transaction.id)}
                          className="text-xs text-primary-600 hover:text-primary-500"
                        >
                          Reconcile
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
