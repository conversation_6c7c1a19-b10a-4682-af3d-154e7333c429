import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeftIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useCompany } from '../../contexts/CompanyContext';
import { useNotification } from '../../contexts/NotificationContext';

// Mock data for now - will be replaced with actual services
const mockVendors = [
  { id: '1', name: 'Acme Supplies', displayName: 'Acme Supplies' },
  { id: '2', name: 'Tech Solutions', displayName: 'Tech Solutions' },
];

const mockPurchaseOrders = [
  { id: '1', poNumber: 'PO-000001', vendorName: 'Acme Supplies' },
  { id: '2', poNumber: 'PO-000002', vendorName: 'Tech Solutions' },
];

const CreatePurchaseReceipt = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  const [loading, setLoading] = useState(false);
  const [vendors, setVendors] = useState(mockVendors);
  const [purchaseOrders, setPurchaseOrders] = useState(mockPurchaseOrders);

  const [formData, setFormData] = useState({
    companyId: currentCompany?.id || '',
    vendorId: '',
    purchaseOrderId: '',
    receiptDate: new Date().toISOString().split('T')[0],
    deliveryNoteNumber: '',
    currency: 'USD',
    exchangeRate: 1,
    deliveryAddress: '',
    deliveryInstructions: '',
    receivedBy: '',
    inspectionNotes: '',
    notes: '',
    lineItems: [
      {
        description: '',
        unitOfMeasure: '',
        quantityOrdered: 1,
        quantityReceived: 1,
        quantityAccepted: 1,
        quantityRejected: 0,
        conditionOnArrival: 'GOOD',
        inspectionNotes: '',
        batchNumber: '',
        location: '',
      }
    ],
  });

  const [errors, setErrors] = useState<string[]>([]);

  useEffect(() => {
    if (currentCompany) {
      setFormData(prev => ({ ...prev, companyId: currentCompany.id }));
      
      // Check if creating from purchase order
      const poId = searchParams.get('from_po');
      if (poId) {
        setFormData(prev => ({ ...prev, purchaseOrderId: poId }));
        // Load PO data and pre-fill form
      }
    }
  }, [currentCompany, searchParams]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleLineItemChange = (index: number, field: string, value: any) => {
    const newLineItems = [...formData.lineItems];
    newLineItems[index] = { ...newLineItems[index], [field]: value };
    
    // Auto-calculate accepted/rejected quantities
    if (field === 'quantityReceived') {
      newLineItems[index].quantityAccepted = value;
      newLineItems[index].quantityRejected = 0;
    }
    
    setFormData(prev => ({ ...prev, lineItems: newLineItems }));
  };

  const addLineItem = () => {
    setFormData(prev => ({
      ...prev,
      lineItems: [
        ...prev.lineItems,
        {
          description: '',
          unitOfMeasure: '',
          quantityOrdered: 1,
          quantityReceived: 1,
          quantityAccepted: 1,
          quantityRejected: 0,
          conditionOnArrival: 'GOOD',
          inspectionNotes: '',
          batchNumber: '',
          location: '',
        }
      ]
    }));
  };

  const removeLineItem = (index: number) => {
    if (formData.lineItems.length > 1) {
      setFormData(prev => ({
        ...prev,
        lineItems: prev.lineItems.filter((_, i) => i !== index)
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentCompany) return;

    // Basic validation
    const validationErrors: string[] = [];
    if (!formData.vendorId) validationErrors.push('Vendor is required');
    if (!formData.receiptDate) validationErrors.push('Receipt date is required');
    if (formData.lineItems.length === 0) validationErrors.push('At least one line item is required');
    
    formData.lineItems.forEach((item, index) => {
      if (!item.description?.trim()) {
        validationErrors.push(`Line item ${index + 1}: Description is required`);
      }
      if (item.quantityReceived <= 0) {
        validationErrors.push(`Line item ${index + 1}: Quantity received must be greater than 0`);
      }
    });

    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      setLoading(true);
      setErrors([]);
      
      // Mock API call - will be replaced with actual service
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      addNotification({
        type: 'success',
        title: 'Success',
        message: 'Purchase receipt created successfully',
      });
      
      navigate('/purchase-receipts');
    } catch (error: any) {
      console.error('Failed to create purchase receipt:', error);
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to create purchase receipt',
      });
    } finally {
      setLoading(false);
    }
  };

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">Please select a company</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/purchase-receipts')}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              Create Purchase Receipt
            </h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Record goods received from vendor
            </p>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Error Messages */}
        {errors.length > 0 && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
              Please fix the following errors:
            </h3>
            <ul className="mt-2 text-sm text-red-700 dark:text-red-300 list-disc list-inside">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Basic Information */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Receipt Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Vendor *
              </label>
              <select
                value={formData.vendorId}
                onChange={(e) => handleInputChange('vendorId', e.target.value)}
                required
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="">Select a vendor</option>
                {vendors.map(vendor => (
                  <option key={vendor.id} value={vendor.id}>
                    {vendor.displayName || vendor.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Purchase Order (Optional)
              </label>
              <select
                value={formData.purchaseOrderId}
                onChange={(e) => handleInputChange('purchaseOrderId', e.target.value)}
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="">Select a purchase order</option>
                {purchaseOrders.map(po => (
                  <option key={po.id} value={po.id}>
                    {po.poNumber} - {po.vendorName}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Receipt Date *
              </label>
              <input
                type="date"
                value={formData.receiptDate}
                onChange={(e) => handleInputChange('receiptDate', e.target.value)}
                required
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Delivery Note Number
              </label>
              <input
                type="text"
                value={formData.deliveryNoteNumber}
                onChange={(e) => handleInputChange('deliveryNoteNumber', e.target.value)}
                placeholder="Enter delivery note number"
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Received By
              </label>
              <input
                type="text"
                value={formData.receivedBy}
                onChange={(e) => handleInputChange('receivedBy', e.target.value)}
                placeholder="Name of person who received goods"
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
          </div>
        </div>

        {/* Line Items */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Items Received
            </h3>
            <button
              type="button"
              onClick={addLineItem}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-indigo-900 dark:text-indigo-300 dark:hover:bg-indigo-800"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              Add Item
            </button>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Description
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Qty Ordered
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Qty Received
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Qty Accepted
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Qty Rejected
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Condition
                  </th>
                  <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {formData.lineItems.map((item, index) => (
                  <tr key={index}>
                    <td className="py-2">
                      <input
                        type="text"
                        value={item.description}
                        onChange={(e) => handleLineItemChange(index, 'description', e.target.value)}
                        placeholder="Item description"
                        className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                      />
                    </td>
                    <td className="py-2">
                      <input
                        type="number"
                        value={item.quantityOrdered}
                        onChange={(e) => handleLineItemChange(index, 'quantityOrdered', parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.01"
                        className="block w-20 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                      />
                    </td>
                    <td className="py-2">
                      <input
                        type="number"
                        value={item.quantityReceived}
                        onChange={(e) => handleLineItemChange(index, 'quantityReceived', parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.01"
                        className="block w-20 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                      />
                    </td>
                    <td className="py-2">
                      <input
                        type="number"
                        value={item.quantityAccepted}
                        onChange={(e) => handleLineItemChange(index, 'quantityAccepted', parseFloat(e.target.value) || 0)}
                        min="0"
                        max={item.quantityReceived}
                        step="0.01"
                        className="block w-20 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                      />
                    </td>
                    <td className="py-2">
                      <input
                        type="number"
                        value={item.quantityRejected}
                        onChange={(e) => handleLineItemChange(index, 'quantityRejected', parseFloat(e.target.value) || 0)}
                        min="0"
                        max={item.quantityReceived}
                        step="0.01"
                        className="block w-20 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                      />
                    </td>
                    <td className="py-2">
                      <select
                        value={item.conditionOnArrival}
                        onChange={(e) => handleLineItemChange(index, 'conditionOnArrival', e.target.value)}
                        className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                      >
                        <option value="GOOD">Good</option>
                        <option value="DAMAGED">Damaged</option>
                        <option value="DEFECTIVE">Defective</option>
                        <option value="INCOMPLETE">Incomplete</option>
                        <option value="WRONG_ITEM">Wrong Item</option>
                      </select>
                    </td>
                    <td className="py-2">
                      {formData.lineItems.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeLineItem(index)}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Additional Information */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Additional Information
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Delivery Address
              </label>
              <textarea
                value={formData.deliveryAddress}
                onChange={(e) => handleInputChange('deliveryAddress', e.target.value)}
                rows={3}
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Delivery Instructions
              </label>
              <textarea
                value={formData.deliveryInstructions}
                onChange={(e) => handleInputChange('deliveryInstructions', e.target.value)}
                rows={2}
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Inspection Notes
              </label>
              <textarea
                value={formData.inspectionNotes}
                onChange={(e) => handleInputChange('inspectionNotes', e.target.value)}
                rows={3}
                placeholder="Notes about the inspection and condition of goods"
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                General Notes
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={3}
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate('/purchase-receipts')}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Creating...' : 'Create Receipt'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreatePurchaseReceipt;
