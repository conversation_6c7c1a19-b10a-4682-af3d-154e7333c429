import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  CheckIcon,
  XMarkIcon,
  ClipboardDocumentCheckIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import purchaseReceiptService from "../../services/purchaseReceiptService";
import type { PurchaseReceipt } from "../../types/purchaseReceipt";
import {
  PurchaseReceiptStatusLabels,
  PurchaseReceiptStatusColors,
  ItemConditionLabels,
  ItemConditionColors,
  canEditPurchaseReceipt,
  canDeletePurchaseReceipt,
  canInspectPurchaseReceipt,
  hasQualityIssues,
  getQualityIssuesSummary,
  getNextPossibleStatuses,
} from "../../types/purchaseReceipt";

const PurchaseReceiptDetail = () => {
  const { receiptId } = useParams<{ receiptId: string }>();
  const navigate = useNavigate();
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  const [receipt, setReceipt] = useState<PurchaseReceipt | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    if (currentCompany && receiptId) {
      loadPurchaseReceipt();
    }
  }, [currentCompany, receiptId]);

  const loadPurchaseReceipt = async () => {
    if (!currentCompany || !receiptId) return;

    try {
      setLoading(true);
      const data = await purchaseReceiptService.getPurchaseReceipt(
        currentCompany.id,
        receiptId
      );
      setReceipt(data);
    } catch (error) {
      console.error("Failed to load purchase receipt:", error);
      addNotification("error", "Error", "Failed to load purchase receipt");
      navigate("/purchase-receipts");
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (status: string, notes?: string) => {
    if (!currentCompany || !receiptId) return;

    try {
      setActionLoading(true);
      await purchaseReceiptService.updatePurchaseReceiptStatus(
        currentCompany.id,
        receiptId,
        {
          status: status as any,
          notes,
        }
      );

      addNotification(
        "success",
        "Success",
        "Purchase receipt status updated successfully"
      );

      loadPurchaseReceipt();
    } catch (error) {
      console.error("Failed to update status:", error);
      addNotification(
        "error",
        "Error",
        "Failed to update purchase receipt status"
      );
    } finally {
      setActionLoading(false);
    }
  };

  const handleQualityCheck = async (passed: boolean, notes?: string) => {
    if (!currentCompany || !receiptId) return;

    try {
      setActionLoading(true);
      await purchaseReceiptService.performQualityCheck(
        currentCompany.id,
        receiptId,
        {
          passed,
          notes,
        }
      );

      addNotification(
        "success",
        "Success",
        "Quality check completed successfully"
      );

      loadPurchaseReceipt();
    } catch (error) {
      console.error("Failed to perform quality check:", error);
      addNotification("error", "Error", "Failed to perform quality check");
    } finally {
      setActionLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!currentCompany || !receiptId || !receipt) return;

    if (!confirm("Are you sure you want to delete this purchase receipt?")) {
      return;
    }

    try {
      setActionLoading(true);
      await purchaseReceiptService.deletePurchaseReceipt(
        currentCompany.id,
        receiptId
      );

      addNotification(
        "success",
        "Success",
        "Purchase receipt deleted successfully"
      );

      navigate("/purchase-receipts");
    } catch (error) {
      console.error("Failed to delete purchase receipt:", error);
      addNotification("error", "Error", "Failed to delete purchase receipt");
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const color =
      PurchaseReceiptStatusColors[
        status as keyof typeof PurchaseReceiptStatusColors
      ];
    const label =
      PurchaseReceiptStatusLabels[
        status as keyof typeof PurchaseReceiptStatusLabels
      ];

    const colorClasses = {
      gray: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
      blue: "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300",
      yellow:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-300",
      orange:
        "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-300",
      green:
        "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300",
      red: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300",
    };

    return (
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          colorClasses[color as keyof typeof colorClasses]
        }`}
      >
        {label}
      </span>
    );
  };

  const getConditionBadge = (condition: string) => {
    const color =
      ItemConditionColors[condition as keyof typeof ItemConditionColors];
    const label =
      ItemConditionLabels[condition as keyof typeof ItemConditionLabels];

    const colorClasses = {
      green:
        "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300",
      yellow:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-300",
      orange:
        "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-300",
      red: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300",
    };

    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          colorClasses[color as keyof typeof colorClasses]
        }`}
      >
        {label}
      </span>
    );
  };

  const getQualityStatus = () => {
    if (!receipt) return null;

    if (hasQualityIssues(receipt)) {
      const issues = getQualityIssuesSummary(receipt);
      return (
        <div className="flex items-start text-red-600 dark:text-red-400">
          <ExclamationTriangleIcon className="h-5 w-5 mr-2 mt-0.5" />
          <div>
            <span className="font-medium">Quality Issues Detected</span>
            <ul className="text-sm mt-1 list-disc list-inside">
              {issues.map((issue, index) => (
                <li key={index}>{issue}</li>
              ))}
            </ul>
          </div>
        </div>
      );
    }

    if (receipt.status === "ACCEPTED") {
      return (
        <div className="flex items-center text-green-600 dark:text-green-400">
          <CheckIcon className="h-5 w-5 mr-2" />
          <span className="font-medium">
            All items accepted - No quality issues
          </span>
        </div>
      );
    }

    return null;
  };

  const getCompletionPercentage = () => {
    if (!receipt) return 0;
    return purchaseReceiptService.getReceiptCompletionPercentage(receipt);
  };

  const getAcceptancePercentage = () => {
    if (!receipt) return 0;
    return purchaseReceiptService.getAcceptancePercentage(receipt);
  };

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">
          Please select a company
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!receipt) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">
          Purchase receipt not found
        </p>
      </div>
    );
  }

  const completionPercentage = getCompletionPercentage();
  const acceptancePercentage = getAcceptancePercentage();
  const nextStatuses = getNextPossibleStatuses(receipt.status as any);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate("/purchase-receipts")}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              Purchase Receipt {receipt.receiptNumber}
            </h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Created on {purchaseReceiptService.formatDate(receipt.createdAt)}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          {canEditPurchaseReceipt(receipt.status as any) && (
            <button
              onClick={() => navigate(`/purchase-receipts/${receipt.id}/edit`)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
            >
              <PencilIcon className="h-4 w-4 mr-2" />
              Edit
            </button>
          )}
          {canDeletePurchaseReceipt(receipt.status as any) && (
            <button
              onClick={handleDelete}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Delete
            </button>
          )}
        </div>
      </div>

      {/* Status and Actions */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Status
              </label>
              {getStatusBadge(receipt.status)}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Completion
              </label>
              <div className="flex items-center">
                <div className="w-24 bg-gray-200 rounded-full h-2 dark:bg-gray-700 mr-3">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${completionPercentage}%` }}
                  ></div>
                </div>
                <span className="text-sm text-gray-900 dark:text-white">
                  {Math.round(completionPercentage)}%
                </span>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Acceptance
              </label>
              <div className="flex items-center">
                <div className="w-24 bg-gray-200 rounded-full h-2 dark:bg-gray-700 mr-3">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{ width: `${acceptancePercentage}%` }}
                  ></div>
                </div>
                <span className="text-sm text-gray-900 dark:text-white">
                  {Math.round(acceptancePercentage)}%
                </span>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Quality Status
              </label>
              {getQualityStatus()}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            {nextStatuses.map((status) => {
              let buttonText = "";
              let buttonColor = "bg-indigo-600 hover:bg-indigo-700";
              let icon = null;

              switch (status) {
                case "RECEIVED":
                  buttonText = "Mark Received";
                  buttonColor = "bg-blue-600 hover:bg-blue-700";
                  icon = <CheckIcon className="h-4 w-4 mr-2" />;
                  break;
                case "INSPECTED":
                  buttonText = "Mark Inspected";
                  buttonColor = "bg-yellow-600 hover:bg-yellow-700";
                  icon = (
                    <ClipboardDocumentCheckIcon className="h-4 w-4 mr-2" />
                  );
                  break;
                case "ACCEPTED":
                  buttonText = "Accept";
                  buttonColor = "bg-green-600 hover:bg-green-700";
                  icon = <CheckIcon className="h-4 w-4 mr-2" />;
                  break;
                case "REJECTED":
                  buttonText = "Reject";
                  buttonColor = "bg-red-600 hover:bg-red-700";
                  icon = <XMarkIcon className="h-4 w-4 mr-2" />;
                  break;
                case "PARTIALLY_ACCEPTED":
                  buttonText = "Partial Accept";
                  buttonColor = "bg-orange-600 hover:bg-orange-700";
                  icon = <ExclamationTriangleIcon className="h-4 w-4 mr-2" />;
                  break;
              }

              return (
                <button
                  key={status}
                  onClick={() => handleStatusUpdate(status)}
                  disabled={actionLoading}
                  className={`inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white ${buttonColor} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {icon}
                  {buttonText}
                </button>
              );
            })}

            {canInspectPurchaseReceipt(receipt.status as any) && (
              <>
                <button
                  onClick={() =>
                    handleQualityCheck(true, "Quality check passed")
                  }
                  disabled={actionLoading}
                  className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <CheckIcon className="h-4 w-4 mr-2" />
                  Pass QC
                </button>
                <button
                  onClick={() =>
                    handleQualityCheck(false, "Quality check failed")
                  }
                  disabled={actionLoading}
                  className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <XMarkIcon className="h-4 w-4 mr-2" />
                  Fail QC
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Receipt Information */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Details */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Receipt Details
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Receipt Number
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {receipt.receiptNumber}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Receipt Date
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {purchaseReceiptService.formatDate(receipt.receiptDate)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Delivery Note Number
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {receipt.deliveryNoteNumber || "Not provided"}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Currency
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {receipt.currency}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Received By
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {receipt.receivedBy || "Not specified"}
                </p>
              </div>
              {receipt.purchaseOrderId && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Purchase Order
                  </label>
                  <p className="mt-1 text-sm text-indigo-600 dark:text-indigo-400">
                    Related to PO (ID: {receipt.purchaseOrderId})
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Line Items */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Items Received
            </h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead>
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Item
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Qty Ordered
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Qty Received
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Qty Accepted
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Qty Rejected
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Condition
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {receipt.lineItems?.map((item, index) => {
                    const totals =
                      purchaseReceiptService.calculateLineItemTotals(item);

                    return (
                      <tr key={item.id || index}>
                        <td className="px-4 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {item.description}
                            </div>
                            {item.itemCode && (
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                Code: {item.itemCode}
                              </div>
                            )}
                            {item.unitOfMeasure && (
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                Unit: {item.unitOfMeasure}
                              </div>
                            )}
                            {item.batchNumber && (
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                Batch: {item.batchNumber}
                              </div>
                            )}
                            {item.location && (
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                Location: {item.location}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                          {item.quantityOrdered}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                          <div className="flex items-center">
                            <span>{item.quantityReceived}</span>
                            <div className="ml-2 w-16 bg-gray-200 rounded-full h-1.5 dark:bg-gray-700">
                              <div
                                className="bg-blue-600 h-1.5 rounded-full"
                                style={{
                                  width: `${totals.receivedPercentage}%`,
                                }}
                              ></div>
                            </div>
                          </div>
                          {totals.pendingQuantity > 0 && (
                            <div className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                              Pending: {totals.pendingQuantity}
                            </div>
                          )}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                          <div className="flex items-center">
                            <span>{item.quantityAccepted}</span>
                            {item.quantityReceived > 0 && (
                              <div className="ml-2 w-16 bg-gray-200 rounded-full h-1.5 dark:bg-gray-700">
                                <div
                                  className="bg-green-600 h-1.5 rounded-full"
                                  style={{
                                    width: `${totals.acceptedPercentage}%`,
                                  }}
                                ></div>
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                          <div className="flex items-center">
                            <span>{item.quantityRejected}</span>
                            {item.quantityReceived > 0 &&
                              item.quantityRejected > 0 && (
                                <div className="ml-2 w-16 bg-gray-200 rounded-full h-1.5 dark:bg-gray-700">
                                  <div
                                    className="bg-red-600 h-1.5 rounded-full"
                                    style={{
                                      width: `${totals.rejectedPercentage}%`,
                                    }}
                                  ></div>
                                </div>
                              )}
                          </div>
                        </td>
                        <td className="px-4 py-4">
                          {getConditionBadge(item.conditionOnArrival)}
                          {item.inspectionNotes && (
                            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              {item.inspectionNotes}
                            </div>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* Addresses and Instructions */}
          {(receipt.deliveryAddress || receipt.deliveryInstructions) && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Delivery Information
              </h3>
              <div className="space-y-4">
                {receipt.deliveryAddress && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Delivery Address
                    </label>
                    <div className="text-sm text-gray-900 dark:text-white whitespace-pre-line">
                      {receipt.deliveryAddress}
                    </div>
                  </div>
                )}
                {receipt.deliveryInstructions && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Delivery Instructions
                    </label>
                    <div className="text-sm text-gray-900 dark:text-white whitespace-pre-line">
                      {receipt.deliveryInstructions}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Notes */}
          {(receipt.inspectionNotes || receipt.notes) && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Notes
              </h3>
              <div className="space-y-4">
                {receipt.inspectionNotes && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Inspection Notes
                    </label>
                    <div className="text-sm text-gray-900 dark:text-white whitespace-pre-line">
                      {receipt.inspectionNotes}
                    </div>
                  </div>
                )}
                {receipt.notes && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      General Notes
                    </label>
                    <div className="text-sm text-gray-900 dark:text-white whitespace-pre-line">
                      {receipt.notes}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Vendor Information */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Vendor Information
            </h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Vendor
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {receipt.vendorName}
                </p>
              </div>
              {receipt.vendorEmail && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Email
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {receipt.vendorEmail}
                  </p>
                </div>
              )}
              {receipt.vendorPhone && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Phone
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {receipt.vendorPhone}
                  </p>
                </div>
              )}
              {receipt.vendorAddress && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Address
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white whitespace-pre-line">
                    {receipt.vendorAddress}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Receipt Summary */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Receipt Summary
            </h3>
            <div className="space-y-3">
              {receipt.lineItems && (
                <>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">
                      Total Items:
                    </span>
                    <span className="text-gray-900 dark:text-white">
                      {receipt.lineItems.length}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">
                      Total Ordered:
                    </span>
                    <span className="text-gray-900 dark:text-white">
                      {receipt.lineItems.reduce(
                        (sum, item) => sum + item.quantityOrdered,
                        0
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">
                      Total Received:
                    </span>
                    <span className="text-gray-900 dark:text-white">
                      {receipt.lineItems.reduce(
                        (sum, item) => sum + item.quantityReceived,
                        0
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">
                      Total Accepted:
                    </span>
                    <span className="text-gray-900 dark:text-white">
                      {receipt.lineItems.reduce(
                        (sum, item) => sum + item.quantityAccepted,
                        0
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">
                      Total Rejected:
                    </span>
                    <span className="text-gray-900 dark:text-white">
                      {receipt.lineItems.reduce(
                        (sum, item) => sum + item.quantityRejected,
                        0
                      )}
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Quality Control */}
          {receipt.qualityCheckPassed !== undefined && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Quality Control
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Quality Check
                  </label>
                  <div className="mt-1">
                    {receipt.qualityCheckPassed ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300">
                        <CheckIcon className="h-3 w-3 mr-1" />
                        Passed
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300">
                        <XMarkIcon className="h-3 w-3 mr-1" />
                        Failed
                      </span>
                    )}
                  </div>
                </div>
                {receipt.qualityCheckedAt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Checked Date
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {purchaseReceiptService.formatDateTime(
                        receipt.qualityCheckedAt
                      )}
                    </p>
                  </div>
                )}
                {receipt.qualityCheckedByName && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Checked By
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {receipt.qualityCheckedByName}
                    </p>
                  </div>
                )}
                {receipt.qualityCheckNotes && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      QC Notes
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {receipt.qualityCheckNotes}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* History */}
          {receipt.history && receipt.history.length > 0 && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                History
              </h3>
              <div className="space-y-3">
                {receipt.history.map((entry, index) => (
                  <div key={entry.id || index} className="text-sm">
                    <div className="flex justify-between items-start">
                      <div>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {entry.oldStatus
                            ? `${entry.oldStatus} → ${entry.newStatus}`
                            : entry.newStatus}
                        </span>
                        {entry.notes && (
                          <p className="text-gray-600 dark:text-gray-400 mt-1">
                            {entry.notes}
                          </p>
                        )}
                      </div>
                      <span className="text-gray-500 dark:text-gray-400 text-xs">
                        {purchaseReceiptService.formatDate(entry.changedAt)}
                      </span>
                    </div>
                    {entry.changedByName && (
                      <p className="text-gray-500 dark:text-gray-400 text-xs mt-1">
                        by {entry.changedByName}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PurchaseReceiptDetail;
