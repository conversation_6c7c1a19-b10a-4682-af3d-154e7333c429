import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  ArrowLeftIcon,
  PencilIcon,
  PrinterIcon,
  CheckIcon,
  XMarkIcon,
  PaperAirplaneIcon,
  ArrowPathIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import { useCompany } from "../../contexts/CompanyContext";
import { useNotification } from "../../contexts/NotificationContext";
import estimateService from "../../services/estimateService";
import type { Estimate } from "../../types/estimate";
import {
  EstimateStatusLabels,
  EstimateStatusColors,
  canEditEstimate,
  canSendEstimate,
  canAcceptEstimate,
  canDeclineEstimate,
  canConvertEstimate,
  canCancelEstimate,
  isExpired,
  getDaysOverdue,
} from "../../types/estimate";

const EstimateDetail = () => {
  const { estimateId } = useParams<{ estimateId: string }>();
  const navigate = useNavigate();
  const { currentCompany } = useCompany();
  const { addNotification } = useNotification();

  const [estimate, setEstimate] = useState<Estimate | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    if (currentCompany && estimateId) {
      loadEstimate();
    }
  }, [currentCompany, estimateId]);

  const loadEstimate = async () => {
    if (!currentCompany || !estimateId) return;

    try {
      setLoading(true);
      const estimateData = await estimateService.getEstimate(
        currentCompany.id,
        estimateId
      );
      setEstimate(estimateData);
    } catch (error) {
      console.error("Failed to load estimate:", error);
      addNotification("error", "Error", "Failed to load estimate");
      navigate("/estimates");
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (status: string, notes?: string) => {
    if (!currentCompany || !estimateId) return;

    try {
      setActionLoading(true);
      await estimateService.updateEstimateStatus(
        currentCompany.id,
        estimateId,
        {
          status: status as any,
          notes,
        }
      );

      addNotification(
        "success",
        "Success",
        "Estimate status updated successfully"
      );

      loadEstimate();
    } catch (error) {
      console.error("Failed to update status:", error);
      addNotification("error", "Error", "Failed to update estimate status");
    } finally {
      setActionLoading(false);
    }
  };

  const handleConvertToInvoice = async () => {
    if (!currentCompany || !estimateId) return;

    if (
      !confirm("Are you sure you want to convert this estimate to an invoice?")
    ) {
      return;
    }

    try {
      setActionLoading(true);
      await estimateService.convertToInvoice(currentCompany.id, estimateId);

      addNotification(
        "success",
        "Success",
        "Estimate converted to invoice successfully"
      );

      loadEstimate();
    } catch (error) {
      console.error("Failed to convert estimate:", error);
      addNotification(
        "error",
        "Error",
        "Failed to convert estimate to invoice"
      );
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (estimate: Estimate) => {
    let status = estimate.status;
    let color = EstimateStatusColors[status];
    let label = EstimateStatusLabels[status];

    // Check if estimate is expired
    if (
      isExpired(estimate) &&
      !["ACCEPTED", "CONVERTED", "CANCELLED"].includes(status)
    ) {
      status = "EXPIRED";
      color = "red";
      label = `Expired (${getDaysOverdue(estimate)} days)`;
    }

    const colorClasses = {
      gray: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
      blue: "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300",
      yellow:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-300",
      orange:
        "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-300",
      green:
        "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300",
      red: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300",
      purple:
        "bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-300",
    };

    return (
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
          colorClasses[color as keyof typeof colorClasses]
        }`}
      >
        {label}
      </span>
    );
  };

  const getValidityIndicator = (estimate: Estimate) => {
    const validity = estimateService.getValidityStatus(estimate);

    if (validity.status === "expired") {
      return (
        <div className="flex items-center text-red-600 dark:text-red-400">
          <ClockIcon className="h-4 w-4 mr-1" />
          <span className="text-sm font-medium">{validity.message}</span>
        </div>
      );
    }

    if (validity.status === "expiring") {
      return (
        <div className="flex items-center text-orange-600 dark:text-orange-400">
          <ClockIcon className="h-4 w-4 mr-1" />
          <span className="text-sm font-medium">{validity.message}</span>
        </div>
      );
    }

    return (
      <div className="flex items-center text-green-600 dark:text-green-400">
        <CheckIcon className="h-4 w-4 mr-1" />
        <span className="text-sm">{validity.message}</span>
      </div>
    );
  };

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">
          Please select a company
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!estimate) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">Estimate not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate("/estimates")}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Estimates
          </button>
        </div>

        <div className="flex items-center space-x-3">
          {/* Status Actions */}
          {canSendEstimate(estimate.status) && (
            <button
              onClick={() => handleStatusUpdate("SENT")}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <PaperAirplaneIcon className="h-4 w-4 mr-1" />
              Send
            </button>
          )}

          {canAcceptEstimate(estimate.status) && (
            <button
              onClick={() => handleStatusUpdate("ACCEPTED")}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              <CheckIcon className="h-4 w-4 mr-1" />
              Accept
            </button>
          )}

          {canDeclineEstimate(estimate.status) && (
            <button
              onClick={() => handleStatusUpdate("DECLINED")}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              <XMarkIcon className="h-4 w-4 mr-1" />
              Decline
            </button>
          )}

          {canConvertEstimate(estimate.status) && (
            <button
              onClick={handleConvertToInvoice}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
            >
              <ArrowPathIcon className="h-4 w-4 mr-1" />
              Convert to Invoice
            </button>
          )}

          {canCancelEstimate(estimate.status) && (
            <button
              onClick={() => handleStatusUpdate("CANCELLED")}
              disabled={actionLoading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              <XMarkIcon className="h-4 w-4 mr-1" />
              Cancel
            </button>
          )}

          {/* Edit Button */}
          {canEditEstimate(estimate.status) && (
            <button
              onClick={() => navigate(`/estimates/${estimateId}/edit`)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <PencilIcon className="h-4 w-4 mr-1" />
              Edit
            </button>
          )}

          {/* Print */}
          <button
            onClick={() => window.print()}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <PrinterIcon className="h-4 w-4 mr-1" />
            Print
          </button>
        </div>
      </div>

      {/* Line Items */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Line Items
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Unit
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Quantity
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Unit Price
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Discount
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Tax
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Total
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {estimate.lineItems?.map((item) => (
                <tr key={item.id}>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    <div>
                      <div className="font-medium">{item.description}</div>
                      {item.itemCode && (
                        <div className="text-gray-500 dark:text-gray-400 text-xs">
                          Code: {item.itemCode}
                        </div>
                      )}
                      {item.notes && (
                        <div className="text-gray-500 dark:text-gray-400 text-xs mt-1">
                          {item.notes}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {item.unitOfMeasure || "-"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                    {item.quantity}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                    {estimateService.formatCurrency(
                      item.unitPrice,
                      estimate.currency
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                    {item.discountPercentage > 0
                      ? `${item.discountPercentage}%`
                      : "-"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                    {item.taxRate > 0 ? `${item.taxRate}%` : "-"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900 dark:text-white">
                    {estimateService.formatCurrency(
                      item.lineTotal,
                      estimate.currency
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Totals */}
        <div className="bg-gray-50 dark:bg-gray-700 px-6 py-4">
          <div className="flex justify-end">
            <div className="w-64 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  Subtotal:
                </span>
                <span className="text-gray-900 dark:text-white">
                  {estimateService.formatCurrency(
                    estimate.subtotal,
                    estimate.currency
                  )}
                </span>
              </div>
              {estimate.discountAmount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    Discount:
                  </span>
                  <span className="text-gray-900 dark:text-white">
                    -
                    {estimateService.formatCurrency(
                      estimate.discountAmount,
                      estimate.currency
                    )}
                  </span>
                </div>
              )}
              {estimate.taxAmount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Tax:</span>
                  <span className="text-gray-900 dark:text-white">
                    {estimateService.formatCurrency(
                      estimate.taxAmount,
                      estimate.currency
                    )}
                  </span>
                </div>
              )}
              <div className="flex justify-between text-base font-medium border-t border-gray-200 dark:border-gray-600 pt-2">
                <span className="text-gray-900 dark:text-white">Total:</span>
                <span className="text-gray-900 dark:text-white">
                  {estimateService.formatCurrency(
                    estimate.totalAmount,
                    estimate.currency
                  )}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      {(estimate.billingAddress ||
        estimate.shippingAddress ||
        estimate.notes ||
        estimate.termsAndConditions) && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Additional Information
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {estimate.billingAddress && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Billing Address
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line">
                  {estimate.billingAddress}
                </p>
              </div>
            )}

            {estimate.shippingAddress && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Shipping Address
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line">
                  {estimate.shippingAddress}
                </p>
              </div>
            )}
          </div>

          {estimate.notes && (
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                Notes
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line">
                {estimate.notes}
              </p>
            </div>
          )}

          {estimate.termsAndConditions && (
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                Terms and Conditions
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line">
                {estimate.termsAndConditions}
              </p>
            </div>
          )}
        </div>
      )}

      {/* History */}
      {estimate.history && estimate.history.length > 0 && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Status History
          </h3>

          <div className="flow-root">
            <ul className="-mb-8">
              {estimate.history.map((entry, index) => (
                <li key={entry.id}>
                  <div className="relative pb-8">
                    {index !== estimate.history!.length - 1 && (
                      <span
                        className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-600"
                        aria-hidden="true"
                      />
                    )}
                    <div className="relative flex space-x-3">
                      <div>
                        <span className="h-8 w-8 rounded-full bg-gray-400 flex items-center justify-center ring-8 ring-white dark:ring-gray-800">
                          <span className="text-white text-xs font-medium">
                            {entry.newStatus.charAt(0)}
                          </span>
                        </span>
                      </div>
                      <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                        <div>
                          <p className="text-sm text-gray-900 dark:text-white">
                            Status changed to{" "}
                            <span className="font-medium">
                              {EstimateStatusLabels[entry.newStatus]}
                            </span>
                            {entry.oldStatus && (
                              <span className="text-gray-500 dark:text-gray-400">
                                {" "}
                                from {EstimateStatusLabels[entry.oldStatus]}
                              </span>
                            )}
                          </p>
                          {entry.notes && (
                            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                              {entry.notes}
                            </p>
                          )}
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            by {entry.changedByName || "System"}
                          </p>
                        </div>
                        <div className="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                          {estimateService.formatDate(entry.changedAt)}
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* Comments */}
      {estimate.comments && estimate.comments.length > 0 && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Comments
          </h3>

          <div className="space-y-4">
            {estimate.comments.map((comment) => (
              <div
                key={comment.id}
                className="border-l-4 border-indigo-400 pl-4"
              >
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {comment.createdByName || "Unknown User"}
                    {comment.isInternal && (
                      <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                        Internal
                      </span>
                    )}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {estimateService.formatDate(comment.createdAt)}
                  </p>
                </div>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line">
                  {comment.comment}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default EstimateDetail;
