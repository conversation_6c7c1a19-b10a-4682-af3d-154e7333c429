import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import { CompanyProvider } from "./contexts/CompanyContext";
import { ThemeProvider } from "./contexts/ThemeContext";
import { NotificationProvider } from "./contexts/NotificationContext";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import Layout from "./components/layout/Layout";
import Login from "./pages/auth/Login";
import Register from "./pages/auth/Register";
import Dashboard from "./pages/Dashboard";
import ChartOfAccounts from "./pages/accounts/ChartOfAccounts";
import Transactions from "./pages/transactions/Transactions";
import TransactionTemplates from "./pages/transactions/TransactionTemplates";
import ApprovalWorkflows from "./pages/workflows/Workflows";
import Reports from "./pages/reports/Reports";
import ReportBuilder from "./pages/reports/ReportBuilder";
import ReportViewer from "./pages/reports/ReportViewer";
import ReportEditor from "./pages/reports/ReportEditor";

import Contacts from "./pages/contacts/Contacts";
import Invoices from "./pages/invoices/Invoices";
import Settings from "./pages/settings/Settings";
import UserManagement from "./pages/admin/UserManagement";
import TaxManagement from "./pages/admin/TaxManagement";
import Banking from "./pages/Banking";
import BankReconciliationPage from "./pages/BankReconciliationPage";
import DetailedReconciliationView from "./components/reconciliation/DetailedReconciliationView";

import Auditing from "./pages/auditing/Auditing";
import TRACompliance from "./pages/TRACompliance";
import EFDCompliance from "./pages/EFDCompliance";
import PayrollManagement from "./pages/PayrollManagement";
import Inventory from "./pages/inventory/Inventory";
import PurchaseOrders from "./pages/purchase-orders/PurchaseOrders";
import CreatePurchaseOrder from "./pages/purchase-orders/CreatePurchaseOrder";
import PurchaseOrderDetail from "./pages/purchase-orders/PurchaseOrderDetail";
import EditPurchaseOrder from "./pages/purchase-orders/EditPurchaseOrder";
import Bills from "./pages/bills/Bills";
import CreateBill from "./pages/bills/CreateBill";
import BillDetail from "./pages/bills/BillDetail";
import Estimates from "./pages/estimates/Estimates";
import CreateEstimate from "./pages/estimates/CreateEstimate";
import EstimateDetail from "./pages/estimates/EstimateDetail";
import SalesOrders from "./pages/salesOrders/SalesOrders";
import CreateSalesOrder from "./pages/salesOrders/CreateSalesOrder";
import SalesOrderDetail from "./pages/salesOrders/SalesOrderDetail";

function App() {
  return (
    <Router>
      <ThemeProvider>
        <AuthProvider>
          <CompanyProvider>
            <NotificationProvider>
              <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
                <Routes>
                  {/* Public routes */}
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />

                  {/* Protected routes */}
                  <Route
                    path="/"
                    element={
                      <ProtectedRoute>
                        <Layout />
                      </ProtectedRoute>
                    }
                  >
                    <Route
                      index
                      element={<Navigate to="/dashboard" replace />}
                    />
                    <Route path="dashboard" element={<Dashboard />} />
                    <Route path="accounts" element={<ChartOfAccounts />} />
                    <Route path="transactions" element={<Transactions />} />
                    <Route
                      path="transactions/templates"
                      element={<TransactionTemplates />}
                    />
                    <Route path="workflows" element={<ApprovalWorkflows />} />
                    <Route path="reports" element={<Reports />} />
                    <Route path="reports/builder" element={<ReportBuilder />} />
                    <Route
                      path="reports/view/:reportId"
                      element={<ReportViewer />}
                    />
                    <Route
                      path="reports/edit/:reportId"
                      element={<ReportEditor />}
                    />
                    <Route path="banking" element={<Banking />} />
                    <Route path="contacts" element={<Contacts />} />
                    <Route path="invoices" element={<Invoices />} />
                    <Route
                      path="reconciliation"
                      element={<BankReconciliationPage />}
                    />
                    <Route
                      path="reconciliation/:reconciliationId"
                      element={<DetailedReconciliationView />}
                    />
                    <Route path="tax" element={<TaxManagement />} />
                    <Route path="admin/users" element={<UserManagement />} />
                    <Route path="auditing" element={<Auditing />} />
                    <Route path="tra-compliance" element={<TRACompliance />} />
                    <Route path="efd-compliance" element={<EFDCompliance />} />
                    <Route path="payroll" element={<PayrollManagement />} />
                    <Route path="settings" element={<Settings />} />
                    <Route path="inventory" element={<Inventory />} />
                    <Route
                      path="purchase-orders"
                      element={<PurchaseOrders />}
                    />
                    <Route
                      path="purchase-orders/new"
                      element={<CreatePurchaseOrder />}
                    />
                    <Route
                      path="purchase-orders/:poId"
                      element={<PurchaseOrderDetail />}
                    />
                    <Route
                      path="purchase-orders/:poId/edit"
                      element={<EditPurchaseOrder />}
                    />
                    <Route path="bills" element={<Bills />} />
                    <Route path="bills/new" element={<CreateBill />} />
                    <Route path="bills/:billId" element={<BillDetail />} />
                    <Route path="estimates" element={<Estimates />} />
                    <Route path="estimates/new" element={<CreateEstimate />} />
                    <Route
                      path="estimates/:estimateId"
                      element={<EstimateDetail />}
                    />
                    <Route path="sales-orders" element={<SalesOrders />} />
                    <Route
                      path="sales-orders/new"
                      element={<CreateSalesOrder />}
                    />
                    <Route
                      path="sales-orders/:salesOrderId"
                      element={<SalesOrderDetail />}
                    />
                  </Route>

                  {/* Catch all route */}
                  <Route
                    path="*"
                    element={<Navigate to="/dashboard" replace />}
                  />
                </Routes>
              </div>
            </NotificationProvider>
          </CompanyProvider>
        </AuthProvider>
      </ThemeProvider>
    </Router>
  );
}

export default App;
