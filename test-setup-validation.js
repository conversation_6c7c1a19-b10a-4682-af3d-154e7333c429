// Simple validation script to test our setup
console.log('🔍 Testing setup validation...');

// Test 1: Environment variables
console.log('\n📋 Environment Variables:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('JWT_SECRET:', process.env.JWT_SECRET ? '✅ Set' : '❌ Missing');

// Test 2: Database configuration
console.log('\n🗄️ Database Configuration:');
try {
  const config = require('./server/knexfile.ts').default;
  console.log('Available environments:', Object.keys(config));
  
  const testConfig = config.test;
  if (testConfig) {
    console.log('✅ Test environment configured');
    console.log('Test DB:', testConfig.connection.database);
  } else {
    console.log('❌ Test environment missing');
  }
} catch (error) {
  console.log('❌ Database config error:', error.message);
}

// Test 3: Test files exist
console.log('\n📁 Test Files:');
const fs = require('fs');
const testFiles = [
  'jest.config.js',
  'server/__tests__/setup.ts',
  'server/__tests__/utils/testHelpers.ts',
  'server/__tests__/auth.test.ts',
  'server/__tests__/accounts.test.ts'
];

testFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file}`);
  }
});

// Test 4: Dependencies
console.log('\n📦 Dependencies:');
const packageJson = require('./package.json');
const testDeps = ['jest', 'supertest', 'ts-jest', '@types/jest'];

testDeps.forEach(dep => {
  if (packageJson.devDependencies[dep]) {
    console.log(`✅ ${dep}: ${packageJson.devDependencies[dep]}`);
  } else {
    console.log(`❌ ${dep}: Missing`);
  }
});

console.log('\n🎯 Setup validation complete!');
console.log('\nNext steps:');
console.log('1. Ensure Node.js is in PATH');
console.log('2. Run: npm install (to install missing dependencies)');
console.log('3. Run: npm run test:server');
