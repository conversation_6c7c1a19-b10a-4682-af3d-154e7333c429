# Accounting System Scaling Guide

## Overview

This guide provides comprehensive instructions for scaling the accounting system to handle increased load and ensure high availability.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Load Balancer │    │   Load Balancer │
│    (HAProxy)    │    │     (Nginx)     │    │   (AWS ALB)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
    ┌────────────────────────────┼────────────────────────────┐
    │                            │                            │
┌───▼────┐              ┌───────▼────┐              ┌────────▼───┐
│App     │              │App         │              │App         │
│Server 1│              │Server 2    │              │Server N    │
│(Node.js│              │(Node.js)   │              │(Node.js)   │
└────────┘              └────────────┘              └────────────┘
    │                            │                            │
    └────────────────────────────┼────────────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
    ┌────▼────┐            ┌─────▼─────┐         ┌──────▼──────┐
    │Database │            │   Redis   │         │File Storage │
    │(Primary)│            │  Cluster  │         │   (S3/NFS)  │
    └─────────┘            └───────────┘         └─────────────┘
         │
    ┌────▼────┐
    │Database │
    │(Replica)│
    └─────────┘
```

## Scaling Strategies

### 1. Horizontal Scaling (Scale Out)

#### Application Servers
- **Multiple Node.js instances** behind a load balancer
- **Stateless design** ensures any server can handle any request
- **Session management** through Redis for consistency

#### Database Scaling
- **Read replicas** for read-heavy operations
- **Connection pooling** to manage database connections
- **Query optimization** and indexing

#### Caching Layer
- **Redis cluster** for distributed caching
- **Cache warming** strategies for frequently accessed data
- **Cache invalidation** patterns for data consistency

### 2. Vertical Scaling (Scale Up)

#### Server Resources
- **CPU**: Increase cores for better concurrent processing
- **Memory**: More RAM for caching and faster operations
- **Storage**: SSD for faster database operations

#### Database Optimization
- **Memory allocation** for PostgreSQL buffers
- **Connection limits** optimization
- **Query performance** tuning

## Load Balancing Configuration

### HAProxy Configuration
```haproxy
global
    daemon
    maxconn 4096

defaults
    mode http
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms

frontend accounting_frontend
    bind *:80
    bind *:443 ssl crt /etc/ssl/certs/accounting.pem
    redirect scheme https if !{ ssl_fc }
    default_backend accounting_servers

backend accounting_servers
    balance roundrobin
    option httpchk GET /health
    server app1 *********:3000 check
    server app2 *********:3000 check
    server app3 *********:3000 check
```

### Nginx Configuration
```nginx
upstream accounting_backend {
    least_conn;
    server *********:3000 max_fails=3 fail_timeout=30s;
    server *********:3000 max_fails=3 fail_timeout=30s;
    server *********:3000 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    listen 443 ssl http2;
    server_name accounting.example.com;

    ssl_certificate /etc/ssl/certs/accounting.crt;
    ssl_certificate_key /etc/ssl/private/accounting.key;

    location / {
        proxy_pass http://accounting_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Health check
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    location /health {
        access_log off;
        proxy_pass http://accounting_backend;
    }
}
```

## Database Scaling

### PostgreSQL Configuration

#### Primary Database (postgresql.conf)
```ini
# Memory settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Connection settings
max_connections = 200
shared_preload_libraries = 'pg_stat_statements'

# Performance settings
random_page_cost = 1.1
effective_io_concurrency = 200
max_worker_processes = 8
max_parallel_workers_per_gather = 4
max_parallel_workers = 8

# WAL settings
wal_buffers = 16MB
checkpoint_completion_target = 0.9
wal_compression = on

# Logging
log_min_duration_statement = 1000
log_checkpoints = on
log_connections = on
log_disconnections = on
```

#### Read Replica Setup
```sql
-- On primary server
CREATE USER replicator REPLICATION LOGIN ENCRYPTED PASSWORD 'secure_password';

-- pg_hba.conf entry
host replication replicator ********/24 md5
```

### Connection Pooling with PgBouncer
```ini
[databases]
accounting = host=localhost port=5432 dbname=accounting

[pgbouncer]
listen_port = 6432
listen_addr = *
auth_type = md5
auth_file = /etc/pgbouncer/userlist.txt
pool_mode = transaction
max_client_conn = 1000
default_pool_size = 25
reserve_pool_size = 5
reserve_pool_timeout = 5
```

## Redis Cluster Configuration

### Redis Cluster Setup
```bash
# Create Redis cluster with 6 nodes (3 masters, 3 slaves)
redis-cli --cluster create \
  *********:7000 *********:7000 *********:7000 \
  *********:7000 *********:7000 *********:7000 \
  --cluster-replicas 1
```

### Redis Configuration (redis.conf)
```ini
# Cluster configuration
cluster-enabled yes
cluster-config-file nodes.conf
cluster-node-timeout 5000
appendonly yes

# Memory optimization
maxmemory 2gb
maxmemory-policy allkeys-lru

# Network
bind 0.0.0.0
port 7000
```

## Application Configuration for Scaling

### Environment Variables
```bash
# Application
NODE_ENV=production
PORT=3000
CLUSTER_MODE=true
WORKERS=4

# Database
DATABASE_URL=*************************************/accounting
DATABASE_POOL_MIN=5
DATABASE_POOL_MAX=20
DATABASE_READ_REPLICA_URL=***********************************/accounting

# Redis
REDIS_CLUSTER_NODES=*********:7000,*********:7000,*********:7000
REDIS_PASSWORD=secure_redis_password

# Caching
CACHE_TTL_DEFAULT=300
CACHE_TTL_REPORTS=3600
CACHE_TTL_ANALYTICS=1800

# Performance
MAX_REQUEST_SIZE=10mb
REQUEST_TIMEOUT=30000
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=1000
```

### Cluster Mode Setup
```javascript
// server/cluster.js
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
  console.log(`Master ${process.pid} is running`);

  // Fork workers
  const workers = process.env.WORKERS || numCPUs;
  for (let i = 0; i < workers; i++) {
    cluster.fork();
  }

  cluster.on('exit', (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} died`);
    cluster.fork();
  });
} else {
  require('./app.js');
  console.log(`Worker ${process.pid} started`);
}
```

## Monitoring and Alerting

### Key Metrics to Monitor
- **Response Time**: Average, 95th percentile
- **Throughput**: Requests per second
- **Error Rate**: 4xx and 5xx responses
- **Database**: Connection pool usage, query performance
- **Cache**: Hit rate, memory usage
- **System**: CPU, memory, disk usage

### Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'accounting-app'
    static_configs:
      - targets: ['*********:3000', '*********:3000', '*********:3000']
    metrics_path: '/metrics'

  - job_name: 'postgres'
    static_configs:
      - targets: ['10.0.1.30:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['*********:9121']
```

### Grafana Dashboards
- **Application Performance**: Response times, throughput, errors
- **Database Performance**: Query performance, connections, locks
- **Cache Performance**: Hit rates, memory usage, evictions
- **System Resources**: CPU, memory, disk, network

## Auto-Scaling Configuration

### Kubernetes HPA (Horizontal Pod Autoscaler)
```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: accounting-app-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: accounting-app
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### AWS Auto Scaling Group
```json
{
  "AutoScalingGroupName": "accounting-app-asg",
  "MinSize": 3,
  "MaxSize": 20,
  "DesiredCapacity": 3,
  "TargetGroupARNs": ["arn:aws:elasticloadbalancing:..."],
  "HealthCheckType": "ELB",
  "HealthCheckGracePeriod": 300
}
```

## Performance Testing

### Load Testing Commands
```bash
# Light load test
npm run load-test:light

# Medium load test
npm run load-test:medium

# Heavy load test
npm run load-test:heavy

# Stress test
npm run load-test:stress

# Custom load test
ts-node server/scripts/loadTest.ts http://localhost:3000 <auth-token> <company-id> heavy
```

### Performance Benchmarks
- **Target Response Time**: < 500ms for 95% of requests
- **Target Throughput**: > 1000 requests/second
- **Target Availability**: 99.9% uptime
- **Target Error Rate**: < 0.1%

## Disaster Recovery

### Backup Strategy
- **Database**: Continuous WAL archiving + daily full backups
- **Redis**: RDB snapshots + AOF persistence
- **Application**: Blue-green deployments
- **Files**: S3 cross-region replication

### Recovery Procedures
1. **Database Recovery**: Point-in-time recovery from WAL archives
2. **Cache Recovery**: Rebuild from database with cache warming
3. **Application Recovery**: Switch to standby environment
4. **Data Recovery**: Restore from latest backup + replay WAL

## Security Considerations

### Network Security
- **VPC**: Isolated network environment
- **Security Groups**: Restrictive firewall rules
- **SSL/TLS**: End-to-end encryption
- **WAF**: Web Application Firewall protection

### Application Security
- **Rate Limiting**: Prevent abuse and DoS attacks
- **Input Validation**: Prevent injection attacks
- **Authentication**: JWT tokens with short expiration
- **Authorization**: Role-based access control

## Cost Optimization

### Resource Optimization
- **Right-sizing**: Match resources to actual usage
- **Reserved Instances**: Long-term commitments for cost savings
- **Spot Instances**: Use for non-critical workloads
- **Auto-scaling**: Scale down during low usage periods

### Monitoring Costs
- **CloudWatch**: Monitor AWS costs and usage
- **Alerts**: Set up billing alerts
- **Optimization**: Regular review and optimization

## Troubleshooting

### Common Issues
1. **High Response Times**: Check database queries, cache hit rates
2. **Memory Leaks**: Monitor heap usage, implement garbage collection
3. **Database Locks**: Identify and optimize long-running queries
4. **Cache Misses**: Review cache invalidation patterns

### Debug Commands
```bash
# Check application health
curl http://localhost:3000/health

# Monitor database connections
SELECT * FROM pg_stat_activity;

# Check Redis cluster status
redis-cli cluster nodes

# Monitor system resources
htop
iostat -x 1
```

This scaling guide provides a comprehensive approach to scaling the accounting system from a single server to a highly available, distributed system capable of handling enterprise-level loads.
