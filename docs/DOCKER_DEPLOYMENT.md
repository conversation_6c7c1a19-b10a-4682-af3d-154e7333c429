# Docker & Deployment Guide

## 🐳 Docker Setup

### Prerequisites

- Docker 20.10+ installed
- Docker Compose 2.0+ installed
- Node.js 20+ (for local development)
- PostgreSQL client tools (for database operations)

### Quick Start

#### Development Environment

```bash
# Start development environment with hot reload
npm run docker:dev

# Build and start development environment
npm run docker:dev:build

# Stop development environment
npm run docker:dev:down
```

#### Production Environment

```bash
# Start production environment
npm run docker:prod

# Build and start production environment
npm run docker:prod:build

# Stop production environment
npm run docker:prod:down
```

### Environment Configuration

1. Copy environment template:
```bash
cp .env.example .env
```

2. Update environment variables:
```bash
# Edit .env file with your configuration
nano .env
```

3. For production, create `.env.production`:
```bash
cp .env.example .env.production
# Edit production-specific values
```

### Docker Services

#### Development Stack (`docker-compose.dev.yml`)

- **postgres-dev**: Development database (port 5433)
- **postgres-test**: Test database (port 5434)
- **redis-dev**: Redis cache (port 6380)
- **app-dev**: Application with hot reload (ports 3002, 5173)

#### Production Stack (`docker-compose.yml`)

- **postgres**: Production database (port 5432)
- **redis**: Redis cache (port 6379)
- **app**: Production application (port 3002)
- **nginx**: Reverse proxy and load balancer (ports 80, 443)

### Database Setup

#### Initial Setup

```bash
# Run database migrations
docker-compose exec app npm run db:migrate

# Seed database with initial data
docker-compose exec app npm run db:seed
```

#### Database Operations

```bash
# Connect to database
docker-compose exec postgres psql -U postgres -d accounting_system

# Create database backup
npm run backup:db

# Restore from backup
npm run restore:db backup_file.sql.gz
```

### Monitoring and Logs

```bash
# View all service logs
npm run docker:logs

# View specific service logs
docker-compose logs -f app
docker-compose logs -f postgres
docker-compose logs -f nginx

# Check service status
docker-compose ps

# Check service health
docker-compose exec app curl http://localhost:3002/health
```

### Troubleshooting

#### Common Issues

1. **Port conflicts**:
```bash
# Check what's using the port
lsof -i :3002
# Kill the process or change port in .env
```

2. **Database connection issues**:
```bash
# Check database logs
docker-compose logs postgres
# Restart database service
docker-compose restart postgres
```

3. **Permission issues**:
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x scripts/*.sh
```

4. **Out of disk space**:
```bash
# Clean up Docker resources
npm run docker:clean
docker system df
```

### Performance Optimization

#### Production Optimizations

1. **Multi-stage builds**: Dockerfile uses multi-stage builds to minimize image size
2. **Layer caching**: Dependencies are cached separately from application code
3. **Non-root user**: Application runs as non-root user for security
4. **Health checks**: All services include health checks
5. **Resource limits**: Configure resource limits in docker-compose.yml

#### Scaling

```bash
# Scale application instances
docker-compose up --scale app=3

# Use Docker Swarm for production scaling
docker swarm init
docker stack deploy -c docker-compose.yml accounting-stack
```

### Security Considerations

1. **Environment variables**: Never commit sensitive data to version control
2. **SSL/TLS**: Configure SSL certificates for production
3. **Network isolation**: Services communicate through internal networks
4. **User permissions**: Application runs as non-root user
5. **Regular updates**: Keep base images and dependencies updated

### Backup and Recovery

#### Automated Backups

```bash
# Setup automated daily backups
crontab -e
# Add: 0 2 * * * /path/to/project/scripts/backup-db.sh production backup
```

#### Manual Backup

```bash
# Create backup
./scripts/backup-db.sh production backup

# List available backups
./scripts/backup-db.sh production list

# Restore from backup
./scripts/backup-db.sh production restore backup_file.sql.gz
```

#### S3 Integration

Configure AWS S3 for backup storage:

```bash
# Set environment variables
export BACKUP_S3_BUCKET=your-backup-bucket
export AWS_ACCESS_KEY_ID=your-access-key
export AWS_SECRET_ACCESS_KEY=your-secret-key
export AWS_REGION=us-east-1
```

### SSL/HTTPS Setup

1. **Generate SSL certificates**:
```bash
# Create SSL directory
mkdir -p ssl

# Generate self-signed certificate (development)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/key.pem -out ssl/cert.pem

# For production, use Let's Encrypt or your certificate provider
```

2. **Update nginx configuration**:
```bash
# Uncomment HTTPS server block in nginx.conf
# Update certificate paths
```

3. **Update environment variables**:
```bash
# Set HTTPS URLs in .env.production
CORS_ORIGIN=https://yourdomain.com
```

### CI/CD Integration

The project includes GitHub Actions workflow for automated:

- Testing on multiple Node.js versions
- Building Docker images
- Deploying to staging/production
- Running security scans
- Generating test coverage reports

See `.github/workflows/ci-cd.yml` for complete configuration.

### Deployment Scripts

#### Available Commands

```bash
# Test application
./scripts/deploy.sh development test

# Build application and Docker image
./scripts/deploy.sh production build

# Deploy to environment
./scripts/deploy.sh production deploy

# Check application health
./scripts/deploy.sh production health

# View application logs
./scripts/deploy.sh production logs

# Stop application
./scripts/deploy.sh production stop
```

#### Production Deployment

1. **Prepare environment**:
```bash
# Create production environment file
cp .env.example .env.production
# Update with production values
```

2. **Deploy application**:
```bash
# Run full deployment
./scripts/deploy.sh production deploy
```

3. **Verify deployment**:
```bash
# Check health
./scripts/deploy.sh production health

# View logs
./scripts/deploy.sh production logs
```

### Maintenance

#### Regular Tasks

1. **Update dependencies**:
```bash
npm audit
npm update
```

2. **Clean up Docker resources**:
```bash
npm run docker:clean
```

3. **Monitor disk usage**:
```bash
docker system df
df -h
```

4. **Review logs**:
```bash
# Application logs
docker-compose logs --tail=100 app

# System logs
journalctl -u docker
```

#### Health Monitoring

The application includes health check endpoints:

- `/health` - Basic health check
- `/health/detailed` - Detailed system status
- `/metrics` - Application metrics (if enabled)

Set up monitoring tools like Prometheus, Grafana, or New Relic for production monitoring.

## 🚀 CI/CD Pipeline

### GitHub Actions Workflow

The project includes a comprehensive CI/CD pipeline that automatically:

#### On Pull Requests:
- Runs linting and code quality checks
- Executes all tests (unit, integration, E2E)
- Builds the application
- Generates test coverage reports

#### On Main Branch:
- All PR checks plus:
- Builds and pushes Docker images
- Deploys to production environment
- Runs post-deployment health checks

### Pipeline Configuration

#### Required Secrets

Set these secrets in your GitHub repository:

```bash
# Docker Hub
DOCKER_USERNAME=your-docker-username
DOCKER_PASSWORD=your-docker-password

# Production Server
PRODUCTION_HOST=your-production-server.com
PRODUCTION_USER=deploy
PRODUCTION_SSH_KEY=your-private-ssh-key

# Staging Server (optional)
STAGING_HOST=your-staging-server.com
STAGING_USER=deploy
STAGING_SSH_KEY=your-staging-private-ssh-key

# Code Coverage
CODECOV_TOKEN=your-codecov-token
```

#### Environment Variables

Configure these in GitHub Actions environment:

```yaml
NODE_ENV: production
DB_HOST: postgres
DB_NAME: accounting_system
DB_USER: postgres
DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
JWT_SECRET: ${{ secrets.JWT_SECRET }}
```

### Deployment Environments

#### Staging Environment
- Triggered on `develop` branch
- Automatic deployment after successful tests
- Used for final testing before production

#### Production Environment
- Triggered on `main` branch
- Requires manual approval (configured in GitHub)
- Includes rollback capabilities

### Manual Deployment

For manual deployments or emergency fixes:

```bash
# Deploy to staging
git push origin develop

# Deploy to production
git push origin main

# Emergency deployment
./scripts/deploy.sh production deploy
```

### Rollback Procedures

#### Automatic Rollback
The deployment script includes health checks that will automatically rollback if:
- Application fails to start
- Health checks fail after deployment
- Database migrations fail

#### Manual Rollback
```bash
# Rollback to previous Docker image
docker-compose pull accounting-system:previous
docker-compose up -d

# Rollback database (if needed)
./scripts/backup-db.sh production restore previous_backup.sql.gz
```

### Monitoring and Alerts

#### Health Checks
- Application health endpoint monitoring
- Database connectivity checks
- External service dependency checks

#### Alerting
Configure alerts for:
- Deployment failures
- Application errors
- Performance degradation
- Security vulnerabilities

### Security Scanning

The CI/CD pipeline includes:
- Dependency vulnerability scanning
- Docker image security scanning
- Code quality and security analysis
- License compliance checking

### Performance Testing

Automated performance tests run:
- Load testing with realistic traffic patterns
- Database performance benchmarks
- API response time monitoring
- Memory and CPU usage analysis
