-- Database setup script for accounting_system
-- Run this as postgres superuser: psql -U postgres -f setup-database.sql

\echo 'Setting up accounting_system database and user...'

-- Create user curtis if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'curtis') THEN
        CREATE USER curtis WITH PASSWORD 'Athanas@2015';
        RAISE NOTICE 'User curtis created successfully';
    ELSE
        RAISE NOTICE 'User curtis already exists';
    END IF;
END
$$;

-- Grant necessary privileges to curtis
ALTER USER curtis CREATEDB;
ALTER USER curtis WITH SUPERUSER;

-- Terminate any existing connections to the database
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname = 'accounting_system' AND pid <> pg_backend_pid();

-- Drop database if it exists and recreate
DROP DATABASE IF EXISTS accounting_system;
CREATE DATABASE accounting_system;

-- Also create development and test databases
DROP DATABASE IF EXISTS accounting_system_dev;
CREATE DATABASE accounting_system_dev;

DROP DATABASE IF EXISTS accounting_system_test;
CREATE DATABASE accounting_system_test;

\echo 'Databases created successfully!'

-- Set ownership of databases to curtis
ALTER DATABASE accounting_system OWNER TO curtis;
ALTER DATABASE accounting_system_dev OWNER TO curtis;
ALTER DATABASE accounting_system_test OWNER TO curtis;

-- Connect to the main accounting_system database
\c accounting_system

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Make curtis the owner of the database
ALTER DATABASE accounting_system OWNER TO curtis;

-- Grant all privileges on the database to curtis
GRANT ALL PRIVILEGES ON DATABASE accounting_system TO curtis;

-- Grant usage and create on schema
GRANT USAGE, CREATE ON SCHEMA public TO curtis;

-- Grant all privileges on all tables in public schema
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO curtis;

-- Grant all privileges on all sequences in public schema
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO curtis;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO curtis;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO curtis;

-- Grant all privileges on functions
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO curtis;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO curtis;

-- Setup development database
\c accounting_system_dev

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
ALTER DATABASE accounting_system_dev OWNER TO curtis;
GRANT ALL PRIVILEGES ON DATABASE accounting_system_dev TO curtis;
GRANT USAGE, CREATE ON SCHEMA public TO curtis;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO curtis;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO curtis;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO curtis;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO curtis;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO curtis;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO curtis;

-- Setup test database
\c accounting_system_test

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
ALTER DATABASE accounting_system_test OWNER TO curtis;
GRANT ALL PRIVILEGES ON DATABASE accounting_system_test TO curtis;
GRANT USAGE, CREATE ON SCHEMA public TO curtis;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO curtis;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO curtis;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO curtis;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO curtis;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO curtis;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO curtis;

-- Return to main database
\c accounting_system

\echo 'Database permissions setup complete!'
\echo ''
\echo '=== SETUP SUMMARY ==='
\echo 'Created databases:'
\echo '  - accounting_system (production)'
\echo '  - accounting_system_dev (development)'
\echo '  - accounting_system_test (testing)'
\echo ''
\echo 'User: curtis'
\echo 'Password: Athanas@2015'
\echo 'Privileges: SUPERUSER, CREATEDB'
\echo ''
\echo 'Extensions enabled:'
\echo '  - uuid-ossp (for UUID generation)'
\echo '  - pgcrypto (for encryption functions)'
\echo ''
\echo 'Next steps:'
\echo '1. Update your .env file with database credentials'
\echo '2. Run: npm run db:migrate'
\echo '3. Run: npm run db:seed (optional)'
\echo '4. Start development: npm run dev'
\echo ''
\echo 'Database setup complete! 🚀'
