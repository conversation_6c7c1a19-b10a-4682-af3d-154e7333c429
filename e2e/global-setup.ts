import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E test setup...');

  // Wait for servers to be ready
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Wait for frontend to be ready
    console.log('⏳ Waiting for frontend server...');
    await page.goto('http://localhost:5173', { waitUntil: 'networkidle' });
    console.log('✅ Frontend server is ready');

    // Wait for backend to be ready
    console.log('⏳ Waiting for backend server...');
    await page.goto('http://localhost:3004/health');
    const healthResponse = await page.textContent('body');
    if (healthResponse?.includes('OK')) {
      console.log('✅ Backend server is ready');
    } else {
      throw new Error('Backend health check failed');
    }

    // Setup test database
    console.log('⏳ Setting up test database...');
    // You can add database setup logic here if needed
    console.log('✅ Test database is ready');

  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }

  console.log('✅ E2E test setup completed');
}

export default globalSetup;
