async function globalTeardown() {
  console.log('🧹 Starting E2E test teardown...');

  try {
    // Clean up test data
    console.log('⏳ Cleaning up test data...');
    // Add cleanup logic here if needed
    console.log('✅ Test data cleaned up');

  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error in teardown to avoid masking test failures
  }

  console.log('✅ E2E test teardown completed');
}

export default globalTeardown;
