#!/bin/bash

# NyotaBalance Deployment Script
# Usage: ./scripts/deploy.sh [environment] [action]
# Example: ./scripts/deploy.sh production deploy

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=${1:-development}
ACTION=${2:-deploy}
PROJECT_NAME="nyotabalance"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to check if required files exist
check_files() {
    local files=("Dockerfile" "docker-compose.yml" "package.json")
    
    for file in "${files[@]}"; do
        if [[ ! -f "$file" ]]; then
            print_error "Required file $file not found"
            exit 1
        fi
    done
    print_success "All required files found"
}

# Function to load environment variables
load_env() {
    local env_file=".env"
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        env_file=".env.production"
    elif [[ "$ENVIRONMENT" == "staging" ]]; then
        env_file=".env.staging"
    elif [[ "$ENVIRONMENT" == "development" ]]; then
        env_file=".env"
    fi
    
    if [[ -f "$env_file" ]]; then
        print_status "Loading environment from $env_file"
        export $(cat "$env_file" | grep -v '^#' | xargs)
    else
        print_warning "Environment file $env_file not found, using defaults"
    fi
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    
    # Start test database
    docker-compose -f docker-compose.dev.yml up -d postgres-test
    
    # Wait for database to be ready
    sleep 10
    
    # Run tests
    npm run test
    
    if [[ $? -eq 0 ]]; then
        print_success "All tests passed"
    else
        print_error "Tests failed"
        exit 1
    fi
    
    # Clean up test database
    docker-compose -f docker-compose.dev.yml down
}

# Function to build application
build_app() {
    print_status "Building application..."
    
    # Install dependencies
    npm ci
    
    # Build application
    npm run build
    
    if [[ $? -eq 0 ]]; then
        print_success "Application built successfully"
    else
        print_error "Build failed"
        exit 1
    fi
}

# Function to build Docker image
build_docker() {
    print_status "Building Docker image..."
    
    local tag="$PROJECT_NAME:latest"
    if [[ "$ENVIRONMENT" != "development" ]]; then
        tag="$PROJECT_NAME:$ENVIRONMENT"
    fi
    
    docker build -t "$tag" .
    
    if [[ $? -eq 0 ]]; then
        print_success "Docker image built: $tag"
    else
        print_error "Docker build failed"
        exit 1
    fi
}

# Function to deploy application
deploy_app() {
    print_status "Deploying application to $ENVIRONMENT..."
    
    local compose_file="docker-compose.yml"
    if [[ "$ENVIRONMENT" == "development" ]]; then
        compose_file="docker-compose.dev.yml"
    fi
    
    # Pull latest images (if not building locally)
    if [[ "$ENVIRONMENT" != "development" ]]; then
        docker-compose -f "$compose_file" pull
    fi
    
    # Start services
    docker-compose -f "$compose_file" up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Check health
    check_health
    
    print_success "Deployment completed successfully"
}

# Function to check application health
check_health() {
    print_status "Checking application health..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:${PORT:-3002}/health > /dev/null 2>&1; then
            print_success "Application is healthy"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts - waiting for application..."
        sleep 10
        ((attempt++))
    done
    
    print_error "Application health check failed"
    return 1
}

# Function to stop application
stop_app() {
    print_status "Stopping application..."
    
    local compose_file="docker-compose.yml"
    if [[ "$ENVIRONMENT" == "development" ]]; then
        compose_file="docker-compose.dev.yml"
    fi
    
    docker-compose -f "$compose_file" down
    print_success "Application stopped"
}

# Function to show logs
show_logs() {
    print_status "Showing application logs..."
    
    local compose_file="docker-compose.yml"
    if [[ "$ENVIRONMENT" == "development" ]]; then
        compose_file="docker-compose.dev.yml"
    fi
    
    docker-compose -f "$compose_file" logs -f
}

# Function to backup database
backup_db() {
    print_status "Creating database backup..."
    
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    
    docker-compose exec postgres pg_dump -U ${DB_USER:-postgres} ${DB_NAME:-accounting_system} > "$backup_file"
    
    if [[ $? -eq 0 ]]; then
        print_success "Database backup created: $backup_file"
    else
        print_error "Database backup failed"
        exit 1
    fi
}

# Main execution
main() {
    print_status "Starting deployment process..."
    print_status "Environment: $ENVIRONMENT"
    print_status "Action: $ACTION"
    
    case "$ACTION" in
        "test")
            check_docker
            check_files
            load_env
            run_tests
            ;;
        "build")
            check_docker
            check_files
            load_env
            build_app
            build_docker
            ;;
        "deploy")
            check_docker
            check_files
            load_env
            if [[ "$ENVIRONMENT" != "development" ]]; then
                run_tests
                build_app
                build_docker
            fi
            deploy_app
            ;;
        "stop")
            stop_app
            ;;
        "logs")
            show_logs
            ;;
        "backup")
            backup_db
            ;;
        "health")
            check_health
            ;;
        *)
            print_error "Unknown action: $ACTION"
            print_status "Available actions: test, build, deploy, stop, logs, backup, health"
            exit 1
            ;;
    esac
    
    print_success "Operation completed successfully!"
}

# Run main function
main "$@"
