#!/usr/bin/env node

/**
 * Fix Authentication Structure Script
 * Creates proper roles and user_companies tables and fixes user permissions
 */

const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER || 'curtis',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'accounting_system',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 5432,
});

async function fixAuthStructure() {
  console.log('🔧 Fixing Authentication Structure...\n');

  try {
    // 1. Create roles table if it doesn't exist
    console.log('📝 Creating roles table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS roles (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(50) UNIQUE NOT NULL,
        description TEXT,
        permissions JSONB DEFAULT '[]'::jsonb,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✅ Roles table ready');

    // 2. Create user_companies table if it doesn't exist
    console.log('📝 Creating user_companies table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS user_companies (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL,
        company_id UUID NOT NULL,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW(),
        UNIQUE(user_id, company_id)
      )
    `);
    console.log('✅ User_companies table ready');

    // 3. Add role_id column to users if it doesn't exist
    console.log('📝 Updating users table...');
    await pool.query(`
      ALTER TABLE users 
      ADD COLUMN IF NOT EXISTS role_id UUID
    `);
    console.log('✅ Users table updated');

    // 4. Create admin role with all permissions
    console.log('👑 Creating admin role...');
    const adminPermissions = [
      'tax:read', 'tax:calculate', 'tax:manage',
      'payroll:read', 'payroll:calculate', 'payroll:manage',
      'company:read', 'company:manage',
      'user:read', 'user:manage',
      'audit:read', 'audit:manage',
      'reports:read', 'reports:generate',
      '*' // Wildcard permission for full access
    ];

    const existingRole = await pool.query(
      'SELECT id FROM roles WHERE name = $1',
      ['admin']
    );

    let adminRoleId;
    if (existingRole.rows.length === 0) {
      const [newRole] = await pool.query(
        'INSERT INTO roles (name, description, permissions) VALUES ($1, $2, $3) RETURNING id',
        ['admin', 'Administrator with full system access', JSON.stringify(adminPermissions)]
      );
      adminRoleId = newRole.id;
      console.log('✅ Created admin role');
    } else {
      adminRoleId = existingRole.rows[0].id;
      // Update permissions
      await pool.query(
        'UPDATE roles SET permissions = $1, updated_at = NOW() WHERE id = $2',
        [JSON.stringify(adminPermissions), adminRoleId]
      );
      console.log('✅ Updated admin role permissions');
    }

    // 5. Get company ID
    console.log('🏢 Setting up company access...');
    const company = await pool.query('SELECT id FROM companies LIMIT 1');
    if (company.rows.length === 0) {
      console.log('❌ No company found! Creating default company...');
      const [newCompany] = await pool.query(
        'INSERT INTO companies (name, created_at, updated_at) VALUES ($1, NOW(), NOW()) RETURNING id',
        ['Default Company']
      );
      var companyId = newCompany.id;
      console.log('✅ Created default company');
    } else {
      var companyId = company.rows[0].id;
      console.log('✅ Found existing company');
    }

    // 6. Update users to use admin role
    console.log('👤 Updating users...');
    const users = await pool.query('SELECT id, email FROM users');
    
    for (const user of users.rows) {
      // Update user role
      await pool.query(
        'UPDATE users SET role_id = $1, updated_at = NOW() WHERE id = $2',
        [adminRoleId, user.id]
      );
      
      // Add company access
      await pool.query(`
        INSERT INTO user_companies (user_id, company_id, is_active, created_at, updated_at)
        VALUES ($1, $2, true, NOW(), NOW())
        ON CONFLICT (user_id, company_id) DO UPDATE SET
        is_active = true, updated_at = NOW()
      `, [user.id, companyId]);
      
      console.log(`✅ Updated user: ${user.email}`);
    }

    // 7. Verify setup
    console.log('\n🔍 Verifying setup...');
    const verification = await pool.query(`
      SELECT 
        u.email,
        r.name as role_name,
        r.permissions,
        uc.company_id
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      LEFT JOIN user_companies uc ON u.id = uc.user_id AND uc.is_active = true
      LIMIT 3
    `);

    verification.rows.forEach(row => {
      console.log(`✓ ${row.email}: ${row.role_name} (${row.permissions?.length || 0} permissions)`);
    });

    console.log('\n🎉 Authentication structure fixed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Roles table created with admin role');
    console.log('✅ User_companies table created');
    console.log('✅ Users updated with proper role_id');
    console.log('✅ Company access configured');
    console.log('✅ All tax permissions granted to admin users');
    console.log('\n🚀 TRA endpoints should now work!');
    console.log('🚀 Try refreshing: http://localhost:5173/tra');

  } catch (error) {
    console.error('❌ Error fixing auth structure:', error);
  } finally {
    await pool.end();
  }
}

fixAuthStructure();
