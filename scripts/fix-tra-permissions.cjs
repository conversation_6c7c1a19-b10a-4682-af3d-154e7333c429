#!/usr/bin/env node

/**
 * Fix TRA Permissions Script
 * Creates missing tax permissions and assigns them to admin users
 */

const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER || 'curtis',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'accounting_system',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 5432,
});

async function fixTRAPermissions() {
  console.log('🔧 Fixing TRA Permissions...\n');

  try {
    // Required tax permissions
    const requiredPermissions = [
      {
        name: 'tax:read',
        description: 'View tax calculations and records'
      },
      {
        name: 'tax:calculate',
        description: 'Calculate VAT and WHT'
      },
      {
        name: 'tax:manage',
        description: 'Create and manage tax returns and records'
      },
      {
        name: 'payroll:read',
        description: 'View payroll information'
      },
      {
        name: 'payroll:calculate',
        description: 'Calculate payroll taxes'
      },
      {
        name: 'payroll:manage',
        description: 'Manage payroll records'
      }
    ];

    console.log('📝 Creating missing permissions...');
    
    for (const perm of requiredPermissions) {
      // Check if permission exists
      const existing = await pool.query(
        'SELECT id FROM permissions WHERE name = $1',
        [perm.name]
      );
      
      if (existing.rows.length === 0) {
        // Create permission
        await pool.query(
          'INSERT INTO permissions (name, description, created_at, updated_at) VALUES ($1, $2, NOW(), NOW())',
          [perm.name, perm.description]
        );
        console.log(`✅ Created permission: ${perm.name}`);
      } else {
        console.log(`✓ Permission exists: ${perm.name}`);
      }
    }

    // Get admin role
    console.log('\n👑 Assigning permissions to admin role...');
    
    const adminRole = await pool.query(
      'SELECT id FROM roles WHERE name IN (\'admin\', \'ADMIN\') LIMIT 1'
    );
    
    if (adminRole.rows.length === 0) {
      // Create admin role
      const [newRole] = await pool.query(
        'INSERT INTO roles (name, description, created_at, updated_at) VALUES ($1, $2, NOW(), NOW()) RETURNING id',
        ['admin', 'Administrator with full access']
      );
      console.log('✅ Created admin role');
      
      // Assign all permissions to admin role
      for (const perm of requiredPermissions) {
        const permission = await pool.query(
          'SELECT id FROM permissions WHERE name = $1',
          [perm.name]
        );
        
        if (permission.rows.length > 0) {
          await pool.query(
            'INSERT INTO role_permissions (role_id, permission_id, created_at, updated_at) VALUES ($1, $2, NOW(), NOW()) ON CONFLICT DO NOTHING',
            [newRole.id, permission.rows[0].id]
          );
          console.log(`✅ Assigned ${perm.name} to admin role`);
        }
      }
    } else {
      // Assign permissions to existing admin role
      const roleId = adminRole.rows[0].id;
      
      for (const perm of requiredPermissions) {
        const permission = await pool.query(
          'SELECT id FROM permissions WHERE name = $1',
          [perm.name]
        );
        
        if (permission.rows.length > 0) {
          const existing = await pool.query(
            'SELECT id FROM role_permissions WHERE role_id = $1 AND permission_id = $2',
            [roleId, permission.rows[0].id]
          );
          
          if (existing.rows.length === 0) {
            await pool.query(
              'INSERT INTO role_permissions (role_id, permission_id, created_at, updated_at) VALUES ($1, $2, NOW(), NOW())',
              [roleId, permission.rows[0].id]
            );
            console.log(`✅ Assigned ${perm.name} to admin role`);
          } else {
            console.log(`✓ Admin already has: ${perm.name}`);
          }
        }
      }
    }

    // Update users to admin role
    console.log('\n👤 Updating user roles...');
    
    const users = await pool.query(
      'SELECT id, email, role FROM users WHERE email IN ($1, $2)',
      ['<EMAIL>', '<EMAIL>']
    );
    
    for (const user of users.rows) {
      if (user.role !== 'admin') {
        await pool.query(
          'UPDATE users SET role = $1, updated_at = NOW() WHERE id = $2',
          ['admin', user.id]
        );
        console.log(`✅ Updated ${user.email} to admin role`);
      } else {
        console.log(`✓ ${user.email} already admin`);
      }
    }

    console.log('\n🎉 TRA Permissions fixed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ All required tax permissions created');
    console.log('✅ Admin role configured with full permissions');
    console.log('✅ Users updated to admin role');
    console.log('\n🚀 You can now access TRA endpoints!');
    console.log('🚀 Try: http://localhost:5173/tra');

  } catch (error) {
    console.error('❌ Error fixing TRA permissions:', error);
  } finally {
    await pool.end();
  }
}

fixTRAPermissions();
