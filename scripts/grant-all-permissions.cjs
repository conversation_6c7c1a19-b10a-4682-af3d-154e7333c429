#!/usr/bin/env node

/**
 * Grant All Permissions Script
 * Temporarily grants all permissions to all users for testing
 */

const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER || 'curtis',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'accounting_system',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 5432,
});

async function grantAllPermissions() {
  console.log('🔓 Granting all permissions for testing...\n');

  try {
    // Update all users to have super admin role
    const users = await pool.query('SELECT id, email FROM users');
    
    console.log(`👤 Found ${users.rows.length} users`);
    
    for (const user of users.rows) {
      await pool.query(
        'UPDATE users SET role = $1, updated_at = NOW() WHERE id = $2',
        ['super_admin', user.id]
      );
      console.log(`✅ Updated ${user.email} to super_admin`);
    }

    console.log('\n🎉 All users now have super_admin role!');
    console.log('🚀 All API endpoints should now work!');
    console.log('🚀 Try the PAYE calculator again!');

  } catch (error) {
    console.error('❌ Error granting permissions:', error);
  } finally {
    await pool.end();
  }
}

grantAllPermissions();
