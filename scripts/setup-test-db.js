#!/usr/bin/env node

import { Client } from 'pg';
import dotenv from 'dotenv';

// Load test environment
dotenv.config({ path: '.env.test' });

async function setupTestDatabase() {
  console.log('🔧 Setting up test database...');

  // Debug environment variables
  console.log('Environment variables:');
  console.log('DB_HOST:', process.env.DB_HOST || 'localhost');
  console.log('DB_PORT:', process.env.DB_PORT || '5432');
  console.log('DB_USER:', process.env.DB_USER || 'curtis');
  console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***' : 'not set');

  // Connect to postgres database to create test database
  const adminClient = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: 'postgres', // Connect to default postgres database
    user: process.env.DB_USER || 'curtis',
    password: process.env.DB_PASSWORD || 'Athanas@2015',
  });

  try {
    await adminClient.connect();
    console.log('✅ Connected to PostgreSQL');

    // Drop test database if exists
    try {
      await adminClient.query('DROP DATABASE IF EXISTS accounting_system_test');
      console.log('🗑️ Dropped existing test database');
    } catch (error) {
      console.log('ℹ️ No existing test database to drop');
    }

    // Create test database
    await adminClient.query('CREATE DATABASE accounting_system_test');
    console.log('✅ Created test database: accounting_system_test');

  } catch (error) {
    console.error('❌ Error setting up test database:');
    console.error('Error message:', error.message);
    console.error('Error code:', error.code);
    console.error('Full error:', error);
    process.exit(1);
  } finally {
    await adminClient.end();
  }

  // Now connect to test database and run migrations
  const testClient = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: 'accounting_system_test',
    user: process.env.DB_USER || 'curtis',
    password: process.env.DB_PASSWORD || 'Athanas@2015',
  });

  try {
    await testClient.connect();
    console.log('✅ Connected to test database');

    // Run migrations using knex
    const knex = (await import('knex')).default;
    const config = (await import('../server/knexfile.ts')).default;
    
    const db = knex(config.test);
    
    console.log('🔄 Running migrations...');
    await db.migrate.latest();
    console.log('✅ Migrations completed');

    console.log('🌱 Running seeds...');
    await db.seed.run();
    console.log('✅ Seeds completed');

    await db.destroy();
    console.log('✅ Test database setup complete!');

  } catch (error) {
    console.error('❌ Error setting up test database schema:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  } finally {
    await testClient.end();
  }
}

// Run setup
setupTestDatabase().catch(console.error);
