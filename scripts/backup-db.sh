#!/bin/bash

# Database Backup Script for Comprehensive Accounting System
# Usage: ./scripts/backup-db.sh [environment]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=${1:-production}
BACKUP_DIR="backups"
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to load environment variables
load_env() {
    local env_file=".env"
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        env_file=".env.production"
    elif [[ "$ENVIRONMENT" == "staging" ]]; then
        env_file=".env.staging"
    fi
    
    if [[ -f "$env_file" ]]; then
        print_status "Loading environment from $env_file"
        export $(cat "$env_file" | grep -v '^#' | xargs)
    else
        print_warning "Environment file $env_file not found, using defaults"
    fi
}

# Function to create backup directory
create_backup_dir() {
    if [[ ! -d "$BACKUP_DIR" ]]; then
        mkdir -p "$BACKUP_DIR"
        print_status "Created backup directory: $BACKUP_DIR"
    fi
}

# Function to create database backup
create_backup() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="$BACKUP_DIR/accounting_system_${ENVIRONMENT}_${timestamp}.sql"
    
    print_status "Creating database backup..."
    print_status "Environment: $ENVIRONMENT"
    print_status "Database: ${DB_NAME:-accounting_system}"
    print_status "Backup file: $backup_file"
    
    # Create backup using Docker
    if docker-compose ps | grep -q postgres; then
        docker-compose exec -T postgres pg_dump \
            -U "${DB_USER:-postgres}" \
            -h localhost \
            -p 5432 \
            --verbose \
            --clean \
            --no-owner \
            --no-privileges \
            "${DB_NAME:-accounting_system}" > "$backup_file"
    else
        # Fallback to direct connection
        PGPASSWORD="${DB_PASSWORD:-postgres}" pg_dump \
            -U "${DB_USER:-postgres}" \
            -h "${DB_HOST:-localhost}" \
            -p "${DB_PORT:-5432}" \
            --verbose \
            --clean \
            --no-owner \
            --no-privileges \
            "${DB_NAME:-accounting_system}" > "$backup_file"
    fi
    
    if [[ $? -eq 0 ]]; then
        # Compress backup
        gzip "$backup_file"
        backup_file="${backup_file}.gz"
        
        local file_size=$(du -h "$backup_file" | cut -f1)
        print_success "Database backup created successfully"
        print_success "File: $backup_file"
        print_success "Size: $file_size"
        
        # Upload to S3 if configured
        upload_to_s3 "$backup_file"
        
        return 0
    else
        print_error "Database backup failed"
        return 1
    fi
}

# Function to upload backup to S3
upload_to_s3() {
    local backup_file="$1"
    
    if [[ -n "$BACKUP_S3_BUCKET" && -n "$AWS_ACCESS_KEY_ID" && -n "$AWS_SECRET_ACCESS_KEY" ]]; then
        print_status "Uploading backup to S3..."
        
        local s3_path="s3://$BACKUP_S3_BUCKET/database-backups/$(basename "$backup_file")"
        
        aws s3 cp "$backup_file" "$s3_path" \
            --region "${AWS_REGION:-us-east-1}" \
            --storage-class STANDARD_IA
        
        if [[ $? -eq 0 ]]; then
            print_success "Backup uploaded to S3: $s3_path"
        else
            print_warning "Failed to upload backup to S3"
        fi
    else
        print_status "S3 configuration not found, skipping upload"
    fi
}

# Function to clean old backups
cleanup_old_backups() {
    print_status "Cleaning up old backups (older than $RETENTION_DAYS days)..."
    
    # Local cleanup
    find "$BACKUP_DIR" -name "*.sql.gz" -type f -mtime +$RETENTION_DAYS -delete
    
    local deleted_count=$(find "$BACKUP_DIR" -name "*.sql.gz" -type f -mtime +$RETENTION_DAYS | wc -l)
    print_status "Deleted $deleted_count old local backups"
    
    # S3 cleanup if configured
    if [[ -n "$BACKUP_S3_BUCKET" && -n "$AWS_ACCESS_KEY_ID" && -n "$AWS_SECRET_ACCESS_KEY" ]]; then
        print_status "Cleaning up old S3 backups..."
        
        aws s3api list-objects-v2 \
            --bucket "$BACKUP_S3_BUCKET" \
            --prefix "database-backups/" \
            --query "Contents[?LastModified<='$(date -d "$RETENTION_DAYS days ago" --iso-8601)'].Key" \
            --output text | \
        while read -r key; do
            if [[ -n "$key" ]]; then
                aws s3 rm "s3://$BACKUP_S3_BUCKET/$key"
                print_status "Deleted S3 backup: $key"
            fi
        done
    fi
    
    print_success "Cleanup completed"
}

# Function to restore database from backup
restore_backup() {
    local backup_file="$1"
    
    if [[ -z "$backup_file" ]]; then
        print_error "Backup file not specified"
        print_status "Usage: $0 restore <backup_file>"
        return 1
    fi
    
    if [[ ! -f "$backup_file" ]]; then
        print_error "Backup file not found: $backup_file"
        return 1
    fi
    
    print_warning "This will restore the database and overwrite existing data!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Restore cancelled"
        return 0
    fi
    
    print_status "Restoring database from: $backup_file"
    
    # Decompress if needed
    local restore_file="$backup_file"
    if [[ "$backup_file" == *.gz ]]; then
        restore_file="${backup_file%.gz}"
        gunzip -c "$backup_file" > "$restore_file"
    fi
    
    # Restore database
    if docker-compose ps | grep -q postgres; then
        docker-compose exec -T postgres psql \
            -U "${DB_USER:-postgres}" \
            -h localhost \
            -p 5432 \
            "${DB_NAME:-accounting_system}" < "$restore_file"
    else
        PGPASSWORD="${DB_PASSWORD:-postgres}" psql \
            -U "${DB_USER:-postgres}" \
            -h "${DB_HOST:-localhost}" \
            -p "${DB_PORT:-5432}" \
            "${DB_NAME:-accounting_system}" < "$restore_file"
    fi
    
    if [[ $? -eq 0 ]]; then
        print_success "Database restored successfully"
        
        # Clean up temporary file
        if [[ "$backup_file" == *.gz && -f "$restore_file" ]]; then
            rm "$restore_file"
        fi
        
        return 0
    else
        print_error "Database restore failed"
        return 1
    fi
}

# Function to list available backups
list_backups() {
    print_status "Available local backups:"
    
    if [[ -d "$BACKUP_DIR" ]]; then
        ls -lh "$BACKUP_DIR"/*.sql.gz 2>/dev/null || print_status "No local backups found"
    else
        print_status "Backup directory not found"
    fi
    
    # List S3 backups if configured
    if [[ -n "$BACKUP_S3_BUCKET" && -n "$AWS_ACCESS_KEY_ID" && -n "$AWS_SECRET_ACCESS_KEY" ]]; then
        print_status "Available S3 backups:"
        aws s3 ls "s3://$BACKUP_S3_BUCKET/database-backups/" --human-readable
    fi
}

# Main execution
main() {
    local action=${2:-backup}
    
    print_status "Database backup script"
    print_status "Environment: $ENVIRONMENT"
    print_status "Action: $action"
    
    load_env
    create_backup_dir
    
    case "$action" in
        "backup")
            create_backup
            cleanup_old_backups
            ;;
        "restore")
            restore_backup "$3"
            ;;
        "list")
            list_backups
            ;;
        "cleanup")
            cleanup_old_backups
            ;;
        *)
            print_error "Unknown action: $action"
            print_status "Available actions: backup, restore, list, cleanup"
            exit 1
            ;;
    esac
    
    print_success "Operation completed successfully!"
}

# Run main function
main "$@"
