#!/bin/bash

# Docker Configuration Validation Script
# This script validates Docker configuration files and setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if file exists
check_file() {
    local file="$1"
    local description="$2"
    
    if [[ -f "$file" ]]; then
        print_success "$description found: $file"
        return 0
    else
        print_error "$description missing: $file"
        return 1
    fi
}

# Function to validate Dockerfile
validate_dockerfile() {
    print_status "Validating Dockerfile..."
    
    if ! check_file "Dockerfile" "Dockerfile"; then
        return 1
    fi
    
    # Check for required instructions
    local required_instructions=("FROM" "WORKDIR" "COPY" "RUN" "EXPOSE" "CMD")
    local dockerfile_content=$(cat Dockerfile)
    
    for instruction in "${required_instructions[@]}"; do
        if echo "$dockerfile_content" | grep -q "^$instruction"; then
            print_success "Dockerfile contains $instruction instruction"
        else
            print_warning "Dockerfile missing $instruction instruction"
        fi
    done
    
    # Check for multi-stage build
    if echo "$dockerfile_content" | grep -q "FROM.*AS"; then
        print_success "Dockerfile uses multi-stage build"
    else
        print_warning "Dockerfile doesn't use multi-stage build"
    fi
    
    # Check for non-root user
    if echo "$dockerfile_content" | grep -q "USER"; then
        print_success "Dockerfile sets non-root user"
    else
        print_warning "Dockerfile doesn't set non-root user"
    fi
    
    # Check for health check
    if echo "$dockerfile_content" | grep -q "HEALTHCHECK"; then
        print_success "Dockerfile includes health check"
    else
        print_warning "Dockerfile doesn't include health check"
    fi
    
    print_success "Dockerfile validation completed"
}

# Function to validate Docker Compose files
validate_docker_compose() {
    print_status "Validating Docker Compose files..."
    
    # Check main compose file
    if check_file "docker-compose.yml" "Production Docker Compose"; then
        validate_compose_file "docker-compose.yml" "production"
    fi
    
    # Check development compose file
    if check_file "docker-compose.dev.yml" "Development Docker Compose"; then
        validate_compose_file "docker-compose.dev.yml" "development"
    fi
}

# Function to validate individual compose file
validate_compose_file() {
    local file="$1"
    local env="$2"
    
    print_status "Validating $file ($env environment)..."
    
    # Check if file is valid YAML (basic check)
    if command -v python3 >/dev/null 2>&1; then
        if python3 -c "import yaml; yaml.safe_load(open('$file'))" 2>/dev/null; then
            print_success "$file is valid YAML"
        else
            print_error "$file contains invalid YAML"
            return 1
        fi
    else
        print_warning "Python3 not available, skipping YAML validation"
    fi
    
    local compose_content=$(cat "$file")
    
    # Check for required services
    local required_services=("postgres" "app")
    for service in "${required_services[@]}"; do
        if echo "$compose_content" | grep -q "$service:"; then
            print_success "$file contains $service service"
        else
            print_warning "$file missing $service service"
        fi
    done
    
    # Check for volumes
    if echo "$compose_content" | grep -q "volumes:"; then
        print_success "$file defines volumes"
    else
        print_warning "$file doesn't define volumes"
    fi
    
    # Check for networks
    if echo "$compose_content" | grep -q "networks:"; then
        print_success "$file defines networks"
    else
        print_warning "$file doesn't define networks"
    fi
    
    # Check for health checks
    if echo "$compose_content" | grep -q "healthcheck:"; then
        print_success "$file includes health checks"
    else
        print_warning "$file doesn't include health checks"
    fi
    
    print_success "$file validation completed"
}

# Function to validate environment files
validate_env_files() {
    print_status "Validating environment files..."
    
    # Check .env.example
    if check_file ".env.example" "Environment template"; then
        validate_env_file ".env.example"
    fi
    
    # Check .env.production
    if check_file ".env.production" "Production environment"; then
        validate_env_file ".env.production"
    fi
    
    # Check if .env exists (optional)
    if [[ -f ".env" ]]; then
        print_success "Local .env file found"
        validate_env_file ".env"
    else
        print_status "Local .env file not found (this is normal)"
    fi
}

# Function to validate individual environment file
validate_env_file() {
    local file="$1"
    
    print_status "Validating $file..."
    
    # Check for required variables
    local required_vars=("NODE_ENV" "PORT" "DB_HOST" "DB_NAME" "DB_USER" "JWT_SECRET")
    local env_content=$(cat "$file")
    
    for var in "${required_vars[@]}"; do
        if echo "$env_content" | grep -q "^$var="; then
            print_success "$file contains $var"
        else
            print_warning "$file missing $var"
        fi
    done
    
    # Check for sensitive data in .env.example
    if [[ "$file" == ".env.example" ]]; then
        local sensitive_patterns=("password.*=" "secret.*=" "key.*=")
        for pattern in "${sensitive_patterns[@]}"; do
            if echo "$env_content" | grep -qi "$pattern" | grep -v "your-" | grep -v "change"; then
                print_warning "$file may contain real sensitive data"
            fi
        done
    fi
    
    print_success "$file validation completed"
}

# Function to validate .dockerignore
validate_dockerignore() {
    print_status "Validating .dockerignore..."
    
    if ! check_file ".dockerignore" "Docker ignore file"; then
        return 1
    fi
    
    local dockerignore_content=$(cat .dockerignore)
    
    # Check for common patterns
    local important_patterns=("node_modules" ".git" "*.log" ".env")
    for pattern in "${important_patterns[@]}"; do
        if echo "$dockerignore_content" | grep -q "$pattern"; then
            print_success ".dockerignore excludes $pattern"
        else
            print_warning ".dockerignore doesn't exclude $pattern"
        fi
    done
    
    print_success ".dockerignore validation completed"
}

# Function to validate nginx configuration
validate_nginx() {
    print_status "Validating Nginx configuration..."
    
    if ! check_file "nginx.conf" "Nginx configuration"; then
        return 1
    fi
    
    local nginx_content=$(cat nginx.conf)
    
    # Check for required sections
    local required_sections=("events" "http" "server" "upstream")
    for section in "${required_sections[@]}"; do
        if echo "$nginx_content" | grep -q "$section"; then
            print_success "nginx.conf contains $section section"
        else
            print_warning "nginx.conf missing $section section"
        fi
    done
    
    # Check for security headers
    local security_headers=("X-Frame-Options" "X-Content-Type-Options" "X-XSS-Protection")
    for header in "${security_headers[@]}"; do
        if echo "$nginx_content" | grep -q "$header"; then
            print_success "nginx.conf includes $header security header"
        else
            print_warning "nginx.conf missing $header security header"
        fi
    done
    
    # Check for gzip compression
    if echo "$nginx_content" | grep -q "gzip on"; then
        print_success "nginx.conf enables gzip compression"
    else
        print_warning "nginx.conf doesn't enable gzip compression"
    fi
    
    print_success "Nginx configuration validation completed"
}

# Function to validate deployment scripts
validate_scripts() {
    print_status "Validating deployment scripts..."
    
    # Check deploy script
    if check_file "scripts/deploy.sh" "Deployment script"; then
        if [[ -x "scripts/deploy.sh" ]]; then
            print_success "deploy.sh is executable"
        else
            print_warning "deploy.sh is not executable"
        fi
    fi
    
    # Check backup script
    if check_file "scripts/backup-db.sh" "Database backup script"; then
        if [[ -x "scripts/backup-db.sh" ]]; then
            print_success "backup-db.sh is executable"
        else
            print_warning "backup-db.sh is not executable"
        fi
    fi
    
    print_success "Scripts validation completed"
}

# Function to validate CI/CD configuration
validate_cicd() {
    print_status "Validating CI/CD configuration..."
    
    if check_file ".github/workflows/ci-cd.yml" "GitHub Actions workflow"; then
        local workflow_content=$(cat .github/workflows/ci-cd.yml)
        
        # Check for required jobs
        local required_jobs=("test" "build" "docker")
        for job in "${required_jobs[@]}"; do
            if echo "$workflow_content" | grep -q "$job:"; then
                print_success "CI/CD workflow contains $job job"
            else
                print_warning "CI/CD workflow missing $job job"
            fi
        done
        
        # Check for security scanning
        if echo "$workflow_content" | grep -q "security\|vulnerability\|audit"; then
            print_success "CI/CD workflow includes security scanning"
        else
            print_warning "CI/CD workflow doesn't include security scanning"
        fi
    fi
    
    print_success "CI/CD validation completed"
}

# Main validation function
main() {
    print_status "Starting Docker configuration validation..."
    print_status "=========================================="
    
    local validation_passed=true
    
    # Run all validations
    validate_dockerfile || validation_passed=false
    echo
    validate_docker_compose || validation_passed=false
    echo
    validate_env_files || validation_passed=false
    echo
    validate_dockerignore || validation_passed=false
    echo
    validate_nginx || validation_passed=false
    echo
    validate_scripts || validation_passed=false
    echo
    validate_cicd || validation_passed=false
    
    echo
    print_status "=========================================="
    
    if $validation_passed; then
        print_success "All Docker configuration validations passed!"
        print_status "Your Docker setup is ready for deployment."
    else
        print_warning "Some validations failed or have warnings."
        print_status "Review the warnings above and fix any critical issues."
    fi
    
    print_status "Validation completed."
}

# Run main function
main "$@"
