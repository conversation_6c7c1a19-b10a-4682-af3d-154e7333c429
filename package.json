{"name": "nyotabalance", "private": true, "version": "1.0.0", "type": "module", "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}, "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "client:dev": "vite", "server:dev": "nodemon server/index.ts", "build": "npm run server:build && npm run client:build", "client:build": "tsc -b && vite build", "server:build": "tsc -p server/tsconfig.json --skipLib<PERSON>heck --noEmitOnError false", "lint": "eslint .", "preview": "vite preview", "start": "node dist/server/index.js", "db:migrate": "knex migrate:latest --knexfile server/knexfile.ts", "db:seed": "knex seed:run --knexfile server/knexfile.ts", "test": "npm run test:setup && npm run test:server && npm run test:client", "test:setup": "node scripts/setup-test-db.js", "test:server": "jest --config jest.config.js", "test:client": "vitest run", "test:watch": "concurrently \"npm run test:server -- --watch\" \"npm run test:client -- --watch\"", "test:coverage": "npm run test:server -- --coverage && npm run test:client -- --coverage", "test:e2e": "playwright test", "docker:build": "docker build -t accounting-system .", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:dev:build": "docker-compose -f docker-compose.dev.yml up --build", "docker:dev:down": "docker-compose -f docker-compose.dev.yml down", "docker:prod": "docker-compose up", "docker:prod:build": "docker-compose up --build", "docker:prod:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker system prune -f", "deploy:dev": "./scripts/deploy.sh development deploy", "deploy:staging": "./scripts/deploy.sh staging deploy", "deploy:prod": "./scripts/deploy.sh production deploy", "backup:db": "./scripts/backup-db.sh production backup", "restore:db": "./scripts/backup-db.sh production restore"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@tailwindcss/vite": "^4.1.8", "@types/archiver": "^6.0.3", "@types/file-saver": "^2.0.7", "@types/humps": "^2.0.6", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "archiver": "^7.0.1", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^5.1.0", "file-saver": "^2.0.5", "helmet": "^8.1.0", "html2canvas": "^1.4.1", "humps": "^2.0.1", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "knex": "^3.1.0", "lucide-react": "^0.468.0", "morgan": "^1.10.0", "node-cron": "^4.1.0", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "pg": "^8.16.0", "plaid": "^35.0.0", "puppeteer": "^24.10.0", "qrcode": "^1.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.53.2", "react-router-dom": "^6.28.0", "react-to-print": "^3.1.0", "recharts": "^2.15.3", "stripe": "^18.2.1", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.1.8", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/mocha": "^10.0.10", "@types/morgan": "^1.9.9", "@types/node": "^22.10.2", "@types/pg": "^8.15.4", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/supertest": "^2.0.16", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.1.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jsdom": "^23.0.1", "nodemon": "^3.1.9", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsx": "^4.19.4", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.2.3"}}