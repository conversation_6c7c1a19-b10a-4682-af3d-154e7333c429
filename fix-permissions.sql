-- Complete permission fix for curtis user (PostgreSQL 17 compatible)
-- Run as: psql -U postgres -f fix-permissions.sql

\echo 'Fixing permissions for curtis user (PostgreSQL 17)...'

-- Ensure curtis user exists and has proper attributes
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'curtis') THEN
        CREATE USER curtis WITH PASSWORD 'Athanas@2015';
    END IF;
END
$$;

-- Make curtis a superuser for development
ALTER USER curtis WITH SUPERUSER CREATEDB CREATEROLE LOGIN;

-- Ensure database exists and set ownership
DROP DATABASE IF EXISTS accounting_system;
CREATE DATABASE accounting_system OWNER curtis;

-- Connect to accounting_system database
\c accounting_system

-- Grant all privileges on database
GRANT ALL PRIVILEGES ON DATABASE accounting_system TO curtis;

-- Grant all privileges on public schema
GRANT ALL ON SCHEMA public TO curtis;
GRANT USAGE, CREATE ON SCHEMA public TO curtis;

-- Grant all privileges on existing tables and sequences
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO curtis;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO curtis;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO curtis;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO curtis;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO curtis;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO curtis;

-- Make curtis owner of public schema
ALTER SCHEMA public OWNER TO curtis;

-- Grant pg_read_all_settings for debugging (optional)
GRANT pg_read_all_settings TO curtis;

\echo 'Permissions fixed! Curtis now has full superuser access.'
\echo 'Database info:'
\l accounting_system
\echo 'User info:'
\du curtis
