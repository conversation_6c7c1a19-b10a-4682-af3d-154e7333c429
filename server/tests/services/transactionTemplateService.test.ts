import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { transactionTemplateService } from '../../services/transactionTemplateService';
import { TestDataFactory, DatabaseHelper, testDb } from '../setup';

describe('TransactionTemplateService', () => {
  const companyId = 'test-company-id';
  const userId = 'test-user-id';

  beforeEach(async () => {
    await DatabaseHelper.cleanDatabase();
    await DatabaseHelper.seedTestData();
  });

  describe('createTemplate', () => {
    it('should create a new transaction template', async () => {
      const templateData = {
        name: 'Test Template',
        description: 'Test Description',
        category: 'SALES' as const,
        defaultValues: { amount: 100 },
        entries: [
          {
            accountId: 'asset-account',
            description: 'Debit Entry',
            debitAmount: 100,
            creditAmount: 0,
            isVariableAmount: false,
            lineNumber: 1,
            metadata: {},
          },
          {
            accountId: 'revenue-account',
            description: 'Credit Entry',
            debitAmount: 0,
            creditAmount: 100,
            isVariableAmount: false,
            lineNumber: 2,
            metadata: {},
          },
        ],
      };

      const template = await transactionTemplateService.createTemplate(companyId, templateData);

      expect(template).toBeDefined();
      expect(template.id).toBeDefined();
      expect(template.name).toBe(templateData.name);
      expect(template.category).toBe(templateData.category);
      expect(template.entries).toHaveLength(2);

      // Verify database record
      const dbTemplate = await testDb('transaction_templates')
        .where('id', template.id)
        .first();
      expect(dbTemplate).toBeDefined();
      expect(dbTemplate.name).toBe(templateData.name);
    });

    it('should validate template balance', async () => {
      const unbalancedTemplateData = {
        name: 'Unbalanced Template',
        description: 'Test Description',
        category: 'GENERAL' as const,
        defaultValues: {},
        entries: [
          {
            accountId: 'asset-account',
            description: 'Debit Entry',
            debitAmount: 100,
            creditAmount: 0,
            isVariableAmount: false,
            lineNumber: 1,
            metadata: {},
          },
          {
            accountId: 'revenue-account',
            description: 'Credit Entry',
            debitAmount: 0,
            creditAmount: 50, // Unbalanced
            isVariableAmount: false,
            lineNumber: 2,
            metadata: {},
          },
        ],
      };

      await expect(
        transactionTemplateService.createTemplate(companyId, unbalancedTemplateData)
      ).rejects.toThrow('Template entries must be balanced');
    });

    it('should handle variable amount entries', async () => {
      const templateData = {
        name: 'Variable Template',
        description: 'Template with variable amounts',
        category: 'GENERAL' as const,
        defaultValues: { baseAmount: 100 },
        entries: [
          {
            accountId: 'asset-account',
            description: 'Variable Debit',
            debitAmount: 0,
            creditAmount: 0,
            isVariableAmount: true,
            amountFormula: '{{baseAmount}} * 1.18',
            lineNumber: 1,
            metadata: {},
          },
          {
            accountId: 'revenue-account',
            description: 'Variable Credit',
            debitAmount: 0,
            creditAmount: 0,
            isVariableAmount: true,
            amountFormula: '{{baseAmount}} * 1.18',
            lineNumber: 2,
            metadata: {},
          },
        ],
      };

      const template = await transactionTemplateService.createTemplate(companyId, templateData);

      expect(template).toBeDefined();
      expect(template.entries[0].isVariableAmount).toBe(true);
      expect(template.entries[0].amountFormula).toBe('{{baseAmount}} * 1.18');
    });
  });

  describe('getTemplates', () => {
    beforeEach(async () => {
      // Create test templates
      await testDb('transaction_templates').insert([
        TestDataFactory.createTransactionTemplate({
          id: 'template-1',
          name: 'Sales Template',
          category: 'SALES',
          usageCount: 5,
        }),
        TestDataFactory.createTransactionTemplate({
          id: 'template-2',
          name: 'Purchase Template',
          category: 'PURCHASES',
          usageCount: 3,
        }),
        TestDataFactory.createTransactionTemplate({
          id: 'template-3',
          name: 'Inactive Template',
          category: 'GENERAL',
          isActive: false,
        }),
      ]);
    });

    it('should return active templates only', async () => {
      const result = await transactionTemplateService.getTemplates(companyId);

      expect(result.templates).toHaveLength(2);
      expect(result.templates.every(t => t.isActive)).toBe(true);
    });

    it('should filter by category', async () => {
      const result = await transactionTemplateService.getTemplates(companyId, {
        category: 'SALES',
      });

      expect(result.templates).toHaveLength(1);
      expect(result.templates[0].category).toBe('SALES');
    });

    it('should search by name', async () => {
      const result = await transactionTemplateService.getTemplates(companyId, {
        search: 'Sales',
      });

      expect(result.templates).toHaveLength(1);
      expect(result.templates[0].name).toContain('Sales');
    });

    it('should sort by usage count', async () => {
      const result = await transactionTemplateService.getTemplates(companyId, {
        sortBy: 'usage',
        sortOrder: 'desc',
      });

      expect(result.templates).toHaveLength(2);
      expect(result.templates[0].usageCount).toBeGreaterThan(result.templates[1].usageCount);
    });
  });

  describe('applyTemplate', () => {
    let templateId: string;

    beforeEach(async () => {
      // Create a test template
      const template = await transactionTemplateService.createTemplate(companyId, {
        name: 'Test Template',
        description: 'Test Description',
        category: 'GENERAL' as const,
        defaultValues: { amount: 100, taxRate: 0.18 },
        entries: [
          {
            accountId: 'asset-account',
            description: 'Cash {{description}}',
            debitAmount: 100,
            creditAmount: 0,
            isVariableAmount: false,
            lineNumber: 1,
            metadata: {},
          },
          {
            accountId: 'revenue-account',
            description: 'Revenue {{description}}',
            debitAmount: 0,
            creditAmount: 100,
            isVariableAmount: false,
            lineNumber: 2,
            metadata: {},
          },
        ],
      });
      templateId = template.id;
    });

    it('should apply template with default values', async () => {
      const transactionData = await transactionTemplateService.applyTemplate(
        companyId,
        templateId,
        {
          transactionDate: '2024-01-15',
          description: 'Applied Template',
          variables: {},
        }
      );

      expect(transactionData).toBeDefined();
      expect(transactionData.description).toBe('Applied Template');
      expect(transactionData.entries).toHaveLength(2);
      expect(transactionData.entries[0].debitAmount).toBe(100);
      expect(transactionData.entries[1].creditAmount).toBe(100);
    });

    it('should apply template with variable substitution', async () => {
      const transactionData = await transactionTemplateService.applyTemplate(
        companyId,
        templateId,
        {
          transactionDate: '2024-01-15',
          description: 'Applied Template',
          variables: { description: 'from sale' },
        }
      );

      expect(transactionData.entries[0].description).toBe('Cash from sale');
      expect(transactionData.entries[1].description).toBe('Revenue from sale');
    });

    it('should handle variable amount formulas', async () => {
      // Create template with variable amounts
      const variableTemplate = await transactionTemplateService.createTemplate(companyId, {
        name: 'Variable Template',
        description: 'Template with formulas',
        category: 'GENERAL' as const,
        defaultValues: {},
        entries: [
          {
            accountId: 'asset-account',
            description: 'Cash',
            debitAmount: 0,
            creditAmount: 0,
            isVariableAmount: true,
            amountFormula: '{{amount}} * (1 + {{taxRate}})',
            lineNumber: 1,
            metadata: {},
          },
          {
            accountId: 'revenue-account',
            description: 'Revenue',
            debitAmount: 0,
            creditAmount: 0,
            isVariableAmount: true,
            amountFormula: '{{amount}} * (1 + {{taxRate}})',
            lineNumber: 2,
            metadata: {},
          },
        ],
      });

      const transactionData = await transactionTemplateService.applyTemplate(
        companyId,
        variableTemplate.id,
        {
          transactionDate: '2024-01-15',
          description: 'Variable Template',
          variables: { amount: 100, taxRate: 0.18 },
        }
      );

      expect(transactionData.entries[0].debitAmount).toBe(118);
      expect(transactionData.entries[1].creditAmount).toBe(118);
    });

    it('should increment usage count', async () => {
      await transactionTemplateService.applyTemplate(companyId, templateId, {
        transactionDate: '2024-01-15',
        description: 'Applied Template',
        variables: {},
      });

      const template = await transactionTemplateService.getTemplate(companyId, templateId);
      expect(template.usageCount).toBe(1);
    });
  });

  describe('updateTemplate', () => {
    let templateId: string;

    beforeEach(async () => {
      const template = await transactionTemplateService.createTemplate(companyId, {
        name: 'Original Template',
        description: 'Original Description',
        category: 'GENERAL' as const,
        defaultValues: {},
        entries: [
          {
            accountId: 'asset-account',
            description: 'Debit Entry',
            debitAmount: 100,
            creditAmount: 0,
            isVariableAmount: false,
            lineNumber: 1,
            metadata: {},
          },
          {
            accountId: 'revenue-account',
            description: 'Credit Entry',
            debitAmount: 0,
            creditAmount: 100,
            isVariableAmount: false,
            lineNumber: 2,
            metadata: {},
          },
        ],
      });
      templateId = template.id;
    });

    it('should update template properties', async () => {
      const updatedTemplate = await transactionTemplateService.updateTemplate(
        companyId,
        templateId,
        {
          name: 'Updated Template',
          description: 'Updated Description',
          category: 'SALES' as const,
          defaultValues: { newValue: 200 },
          entries: [
            {
              accountId: 'asset-account',
              description: 'Updated Debit Entry',
              debitAmount: 200,
              creditAmount: 0,
              isVariableAmount: false,
              lineNumber: 1,
              metadata: {},
            },
            {
              accountId: 'revenue-account',
              description: 'Updated Credit Entry',
              debitAmount: 0,
              creditAmount: 200,
              isVariableAmount: false,
              lineNumber: 2,
              metadata: {},
            },
          ],
        }
      );

      expect(updatedTemplate.name).toBe('Updated Template');
      expect(updatedTemplate.category).toBe('SALES');
      expect(updatedTemplate.entries[0].debitAmount).toBe(200);
    });

    it('should preserve usage count when updating', async () => {
      // Increment usage count first
      await testDb('transaction_templates')
        .where('id', templateId)
        .update({ usage_count: 5 });

      const updatedTemplate = await transactionTemplateService.updateTemplate(
        companyId,
        templateId,
        {
          name: 'Updated Template',
          description: 'Updated Description',
          category: 'GENERAL' as const,
          defaultValues: {},
          entries: [
            {
              accountId: 'asset-account',
              description: 'Updated Entry',
              debitAmount: 150,
              creditAmount: 0,
              isVariableAmount: false,
              lineNumber: 1,
              metadata: {},
            },
            {
              accountId: 'revenue-account',
              description: 'Updated Entry',
              debitAmount: 0,
              creditAmount: 150,
              isVariableAmount: false,
              lineNumber: 2,
              metadata: {},
            },
          ],
        }
      );

      expect(updatedTemplate.usageCount).toBe(5);
    });
  });

  describe('deleteTemplate', () => {
    let templateId: string;

    beforeEach(async () => {
      const template = await transactionTemplateService.createTemplate(companyId, {
        name: 'Template to Delete',
        description: 'Will be deleted',
        category: 'GENERAL' as const,
        defaultValues: {},
        entries: [
          {
            accountId: 'asset-account',
            description: 'Entry',
            debitAmount: 100,
            creditAmount: 0,
            isVariableAmount: false,
            lineNumber: 1,
            metadata: {},
          },
          {
            accountId: 'revenue-account',
            description: 'Entry',
            debitAmount: 0,
            creditAmount: 100,
            isVariableAmount: false,
            lineNumber: 2,
            metadata: {},
          },
        ],
      });
      templateId = template.id;
    });

    it('should soft delete template', async () => {
      await transactionTemplateService.deleteTemplate(companyId, templateId);

      const template = await testDb('transaction_templates')
        .where('id', templateId)
        .first();

      expect(template.is_active).toBe(false);
    });

    it('should not return deleted template in list', async () => {
      await transactionTemplateService.deleteTemplate(companyId, templateId);

      const result = await transactionTemplateService.getTemplates(companyId);
      expect(result.templates.find(t => t.id === templateId)).toBeUndefined();
    });
  });

  describe('validateTemplateBalance', () => {
    it('should validate balanced entries', () => {
      const entries = [
        { debitAmount: 100, creditAmount: 0 },
        { debitAmount: 0, creditAmount: 100 },
      ];

      const result = transactionTemplateService.validateTemplateBalance(entries);
      expect(result.isBalanced).toBe(true);
      expect(result.difference).toBe(0);
    });

    it('should detect unbalanced entries', () => {
      const entries = [
        { debitAmount: 100, creditAmount: 0 },
        { debitAmount: 0, creditAmount: 50 },
      ];

      const result = transactionTemplateService.validateTemplateBalance(entries);
      expect(result.isBalanced).toBe(false);
      expect(result.difference).toBe(50);
    });

    it('should handle multiple entries', () => {
      const entries = [
        { debitAmount: 100, creditAmount: 0 },
        { debitAmount: 50, creditAmount: 0 },
        { debitAmount: 0, creditAmount: 150 },
      ];

      const result = transactionTemplateService.validateTemplateBalance(entries);
      expect(result.isBalanced).toBe(true);
      expect(result.difference).toBe(0);
    });
  });
});
