import { beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { db } from '../config/database-optimized';
import { cacheService } from '../services/cacheService';
import { jobQueueService } from '../services/jobQueueService';

// Test database configuration
const testDbConfig = {
  client: 'postgresql',
  connection: {
    host: process.env.TEST_DB_HOST || 'localhost',
    port: parseInt(process.env.TEST_DB_PORT || '5432'),
    user: process.env.TEST_DB_USER || 'postgres',
    password: process.env.TEST_DB_PASSWORD || '',
    database: process.env.TEST_DB_NAME || 'nyotabalance_test',
  },
  pool: {
    min: 1,
    max: 5,
  },
  migrations: {
    directory: './migrations',
  },
};

// Test data factories
export class TestDataFactory {
  static createCompany(overrides: any = {}) {
    return {
      id: 'test-company-id',
      name: 'Test Company',
      email: '<EMAIL>',
      baseCurrency: 'USD',
      timezone: 'UTC',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static createUser(overrides: any = {}) {
    return {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'ADMIN',
      isActive: true,
      companyId: 'test-company-id',
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static createAccount(overrides: any = {}) {
    return {
      id: 'test-account-id',
      companyId: 'test-company-id',
      code: '1000',
      name: 'Test Account',
      accountType: 'ASSET',
      parentAccountId: null,
      isActive: true,
      balance: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static createTransaction(overrides: any = {}) {
    return {
      id: 'test-transaction-id',
      companyId: 'test-company-id',
      transactionNumber: 'TXN-001',
      transactionDate: new Date().toISOString().split('T')[0],
      description: 'Test Transaction',
      reference: 'REF-001',
      status: 'DRAFT',
      totalAmount: 100,
      currency: 'USD',
      exchangeRate: 1,
      createdBy: 'test-user-id',
      metadata: {},
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static createTransactionEntry(overrides: any = {}) {
    return {
      id: 'test-entry-id',
      transactionId: 'test-transaction-id',
      accountId: 'test-account-id',
      description: 'Test Entry',
      debitAmount: 100,
      creditAmount: 0,
      currency: 'USD',
      exchangeRate: 1,
      baseDebitAmount: 100,
      baseCreditAmount: 0,
      lineNumber: 1,
      metadata: {},
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static createTransactionTemplate(overrides: any = {}) {
    return {
      id: 'test-template-id',
      companyId: 'test-company-id',
      name: 'Test Template',
      description: 'Test Template Description',
      category: 'GENERAL',
      defaultValues: {},
      usageCount: 0,
      isSystemTemplate: false,
      isActive: true,
      createdBy: 'test-user-id',
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static createBankAccount(overrides: any = {}) {
    return {
      id: 'test-bank-account-id',
      companyId: 'test-company-id',
      bankName: 'Test Bank',
      accountName: 'Test Bank Account',
      accountNumber: '****1234',
      routingNumber: '*********',
      accountType: 'CHECKING',
      balance: 1000,
      currency: 'USD',
      isActive: true,
      autoReconciliation: false,
      syncFrequency: 'DAILY',
      alertSettings: {},
      reconciliationRules: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static createBankTransaction(overrides: any = {}) {
    return {
      id: 'test-bank-transaction-id',
      bankAccountId: 'test-bank-account-id',
      companyId: 'test-company-id',
      transactionId: 'external-txn-123',
      amount: -50,
      description: 'Test Bank Transaction',
      category: 'Transfer',
      date: new Date(),
      pending: false,
      paymentChannel: 'ONLINE',
      isReconciled: false,
      reconciliationStatus: 'UNRECONCILED',
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static createWorkflow(overrides: any = {}) {
    return {
      id: 'test-workflow-id',
      companyId: 'test-company-id',
      name: 'Test Workflow',
      description: 'Test Workflow Description',
      triggerType: 'EVENT',
      triggerConfig: { event: 'transaction.created' },
      isActive: true,
      version: 1,
      createdBy: 'test-user-id',
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static createBatchJob(overrides: any = {}) {
    return {
      id: 'test-batch-job-id',
      companyId: 'test-company-id',
      userId: 'test-user-id',
      jobType: 'IMPORT',
      entityType: 'TRANSACTIONS',
      status: 'PENDING',
      progress: 0,
      totalRecords: 100,
      processedRecords: 0,
      errorCount: 0,
      warningCount: 0,
      fileName: 'test-import.csv',
      fileSize: 1024,
      options: {},
      result: {},
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }
}

// Database helpers
export class DatabaseHelper {
  static async cleanDatabase() {
    // Clean up test data in reverse dependency order
    const tables = [
      'workflow_step_executions',
      'workflow_executions',
      'workflow_steps',
      'workflows',
      'batch_job_errors',
      'batch_jobs',
      'bank_alerts',
      'bank_reconciliation_rules',
      'bank_transactions',
      'bank_accounts',
      'transaction_entries',
      'transactions',
      'transaction_template_entries',
      'transaction_templates',
      'accounts',
      'users',
      'companies',
      'report_executions',
      'custom_reports',
      'cache_invalidations',
      'query_performance_log',
    ];

    for (const table of tables) {
      try {
        await db(table).del();
      } catch (error) {
        // Table might not exist, ignore error
      }
    }
  }

  static async seedTestData() {
    // Create test company
    await db('companies').insert(TestDataFactory.createCompany());

    // Create test user
    await db('users').insert(TestDataFactory.createUser());

    // Create test accounts
    await db('accounts').insert([
      TestDataFactory.createAccount({ id: 'asset-account', code: '1000', name: 'Cash', accountType: 'ASSET' }),
      TestDataFactory.createAccount({ id: 'revenue-account', code: '4000', name: 'Revenue', accountType: 'REVENUE' }),
      TestDataFactory.createAccount({ id: 'expense-account', code: '5000', name: 'Expenses', accountType: 'EXPENSE' }),
    ]);
  }

  static async createTestTransaction(data: any = {}) {
    const transaction = TestDataFactory.createTransaction(data);
    await db('transactions').insert(transaction);

    const entries = [
      TestDataFactory.createTransactionEntry({
        id: 'entry-1',
        transactionId: transaction.id,
        accountId: 'asset-account',
        debitAmount: transaction.totalAmount,
        creditAmount: 0,
      }),
      TestDataFactory.createTransactionEntry({
        id: 'entry-2',
        transactionId: transaction.id,
        accountId: 'revenue-account',
        debitAmount: 0,
        creditAmount: transaction.totalAmount,
        lineNumber: 2,
      }),
    ];

    await db('transaction_entries').insert(entries);
    return { transaction, entries };
  }
}

// Mock helpers
export class MockHelper {
  static mockRedis() {
    const mockRedis = {
      get: jest.fn().mockResolvedValue(null),
      set: jest.fn().mockResolvedValue('OK'),
      setex: jest.fn().mockResolvedValue('OK'),
      del: jest.fn().mockResolvedValue(1),
      exists: jest.fn().mockResolvedValue(0),
      keys: jest.fn().mockResolvedValue([]),
      incrby: jest.fn().mockResolvedValue(1),
      expire: jest.fn().mockResolvedValue(1),
      expireat: jest.fn().mockResolvedValue(1),
      flushdb: jest.fn().mockResolvedValue('OK'),
      info: jest.fn().mockResolvedValue('redis_version:6.0.0'),
      quit: jest.fn().mockResolvedValue('OK'),
      connect: jest.fn().mockResolvedValue(undefined),
      on: jest.fn(),
    };

    return mockRedis;
  }

  static mockJobQueue() {
    const mockQueue = {
      add: jest.fn().mockResolvedValue({ id: 'job-123' }),
      getJob: jest.fn().mockResolvedValue(null),
      getWaiting: jest.fn().mockResolvedValue([]),
      getActive: jest.fn().mockResolvedValue([]),
      getCompleted: jest.fn().mockResolvedValue([]),
      getFailed: jest.fn().mockResolvedValue([]),
      getDelayed: jest.fn().mockResolvedValue([]),
      process: jest.fn(),
      close: jest.fn().mockResolvedValue(undefined),
      clean: jest.fn().mockResolvedValue(0),
      on: jest.fn(),
    };

    return mockQueue;
  }
}

// Test environment setup
export async function setupTestEnvironment() {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.DB_NAME = 'nyotabalance_test';
  process.env.REDIS_DB = '1'; // Use different Redis DB for tests

  // Initialize services
  await cacheService.connect();
  await jobQueueService.initialize();
}

export async function teardownTestEnvironment() {
  // Cleanup services
  await cacheService.disconnect();
  await jobQueueService.shutdown();
  await db.destroy();
}

// Global test setup
beforeAll(async () => {
  await setupTestEnvironment();
});

afterAll(async () => {
  await teardownTestEnvironment();
});

beforeEach(async () => {
  await DatabaseHelper.cleanDatabase();
  await DatabaseHelper.seedTestData();
  await cacheService.flushAll();
});

afterEach(async () => {
  // Clean up after each test
  jest.clearAllMocks();
});

// Export test utilities
export {
  db as testDb,
  cacheService as testCache,
  jobQueueService as testJobQueue,
};
