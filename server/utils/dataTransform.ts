/**
 * Utility functions for transforming data between database and API formats
 */

/**
 * Convert decimal fields from strings to numbers for invoice objects
 */
export function processInvoiceNumbers(invoice: any): any {
  if (!invoice) return invoice;
  
  return {
    ...invoice,
    subtotal: parseFloat(invoice.subtotal || 0),
    tax_amount: parseFloat(invoice.tax_amount || 0),
    discount_amount: parseFloat(invoice.discount_amount || 0),
    total_amount: parseFloat(invoice.total_amount || 0),
    paid_amount: parseFloat(invoice.paid_amount || 0),
    balance_due: parseFloat(invoice.balance_due || 0),
    exchange_rate: parseFloat(invoice.exchange_rate || 1),
  };
}

/**
 * Convert decimal fields from strings to numbers for invoice line item objects
 */
export function processLineItemNumbers(lineItem: any): any {
  if (!lineItem) return lineItem;
  
  return {
    ...lineItem,
    quantity: parseFloat(lineItem.quantity || 0),
    unit_price: parseFloat(lineItem.unit_price || 0),
    discount_percent: parseFloat(lineItem.discount_percent || 0),
    discount_amount: parseFloat(lineItem.discount_amount || 0),
    line_total: parseFloat(lineItem.line_total || 0),
  };
}

/**
 * Convert decimal fields from strings to numbers for transaction objects
 */
export function processTransactionNumbers(transaction: any): any {
  if (!transaction) return transaction;
  
  return {
    ...transaction,
    total_amount: parseFloat(transaction.total_amount || 0),
    exchange_rate: parseFloat(transaction.exchange_rate || 1),
  };
}

/**
 * Convert decimal fields from strings to numbers for transaction entry objects
 */
export function processTransactionEntryNumbers(entry: any): any {
  if (!entry) return entry;
  
  return {
    ...entry,
    debit_amount: parseFloat(entry.debit_amount || 0),
    credit_amount: parseFloat(entry.credit_amount || 0),
  };
}

/**
 * Convert decimal fields from strings to numbers for account objects
 */
export function processAccountNumbers(account: any): any {
  if (!account) return account;
  
  return {
    ...account,
    opening_balance: parseFloat(account.opening_balance || 0),
    current_balance: parseFloat(account.current_balance || 0),
  };
}

/**
 * Process arrays of objects with decimal conversion
 */
export function processArrayNumbers<T>(
  array: T[], 
  processor: (item: T) => T
): T[] {
  return array.map(processor);
}

/**
 * Safe number conversion that handles null, undefined, and string values
 */
export function safeParseFloat(value: any, defaultValue: number = 0): number {
  if (value === null || value === undefined || value === '') {
    return defaultValue;
  }
  
  const parsed = parseFloat(value);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Safe integer conversion that handles null, undefined, and string values
 */
export function safeParseInt(value: any, defaultValue: number = 0): number {
  if (value === null || value === undefined || value === '') {
    return defaultValue;
  }
  
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}
