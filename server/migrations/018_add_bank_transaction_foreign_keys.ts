import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Add foreign key constraints for bank_transaction_id columns
  // These were deferred from migration 008 because bank_transactions table didn't exist yet

  // Add foreign key for bank_reconciliation_items table
  await knex.schema.alterTable("bank_reconciliation_items", (table) => {
    table
      .foreign("bank_transaction_id")
      .references("id")
      .inTable("bank_transactions")
      .onDelete("SET NULL");
  });

  // Add foreign key for reconciliation_matches table
  await knex.schema.alterTable("reconciliation_matches", (table) => {
    table
      .foreign("bank_transaction_id")
      .references("id")
      .inTable("bank_transactions")
      .onDelete("CASCADE");
  });

  // Create index for better performance
  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_bank_reconciliation_items_bank_transaction
    ON bank_reconciliation_items(bank_transaction_id) WHERE bank_transaction_id IS NOT NULL;
  `);

  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_reconciliation_matches_bank_transaction
    ON reconciliation_matches(bank_transaction_id);
  `);
}

export async function down(knex: Knex): Promise<void> {
  // Remove foreign key constraints
  await knex.schema.alterTable("bank_reconciliation_items", (table) => {
    table.dropForeign(["bank_transaction_id"]);
  });

  await knex.schema.alterTable("reconciliation_matches", (table) => {
    table.dropForeign(["bank_transaction_id"]);
  });

  // Drop indexes
  await knex.raw(
    "DROP INDEX IF EXISTS idx_bank_reconciliation_items_bank_transaction;"
  );
  await knex.raw(
    "DROP INDEX IF EXISTS idx_reconciliation_matches_bank_transaction;"
  );
}
