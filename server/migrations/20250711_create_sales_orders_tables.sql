-- Sales Orders Management Tables
-- These tables handle customer sales orders and delivery tracking

-- Sales orders table
CREATE TABLE IF NOT EXISTS sales_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES contacts(id) ON DELETE RESTRICT,
    sales_order_number VARCHAR(50) NOT NULL,
    order_date DATE NOT NULL,
    delivery_date DATE,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    exchange_rate DECIMAL(15,8) NOT NULL DEFAULT 1.0,
    
    -- Amounts
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    shipping_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    
    -- Status and workflow
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' CHECK (status IN (
        'DRAFT', 'CONFIRMED', 'IN_PROGRESS', 'SHIPPED', 'DELIVERED', 'COMPLETED', 'CANCELLED'
    )),
    
    -- Customer information
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255),
    customer_phone VARCHAR(50),
    customer_address TEXT,
    
    -- Addresses and details
    billing_address TEXT,
    shipping_address TEXT,
    delivery_instructions TEXT,
    notes TEXT,
    terms_and_conditions TEXT,
    
    -- Source tracking
    estimate_id UUID REFERENCES estimates(id),
    converted_from_estimate_at TIMESTAMP WITH TIME ZONE,
    
    -- Delivery tracking
    shipped_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    tracking_number VARCHAR(100),
    shipping_carrier VARCHAR(100),
    
    -- Audit fields
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(company_id, sales_order_number)
);

-- Sales order line items
CREATE TABLE IF NOT EXISTS sales_order_line_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sales_order_id UUID NOT NULL REFERENCES sales_orders(id) ON DELETE CASCADE,
    line_number INTEGER NOT NULL,
    
    -- Item details
    inventory_item_id UUID REFERENCES inventory_items(id),
    item_code VARCHAR(50),
    description TEXT NOT NULL,
    unit_of_measure VARCHAR(20),
    
    -- Quantities and pricing
    quantity_ordered DECIMAL(15,4) NOT NULL DEFAULT 1,
    quantity_shipped DECIMAL(15,4) NOT NULL DEFAULT 0,
    quantity_delivered DECIMAL(15,4) NOT NULL DEFAULT 0,
    unit_price DECIMAL(15,2) NOT NULL DEFAULT 0,
    discount_percentage DECIMAL(5,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    line_total DECIMAL(15,2) NOT NULL DEFAULT 0,
    
    -- Tax information
    tax_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_code VARCHAR(20),
    
    -- Additional details
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(sales_order_id, line_number)
);

-- Sales order attachments
CREATE TABLE IF NOT EXISTS sales_order_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sales_order_id UUID NOT NULL REFERENCES sales_orders(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_type VARCHAR(50),
    file_size INTEGER,
    uploaded_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Sales order history (audit trail)
CREATE TABLE IF NOT EXISTS sales_order_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sales_order_id UUID NOT NULL REFERENCES sales_orders(id) ON DELETE CASCADE,
    old_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    old_amount DECIMAL(15,2),
    new_amount DECIMAL(15,2),
    notes TEXT,
    changed_by UUID NOT NULL REFERENCES users(id),
    changed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Sales order shipments
CREATE TABLE IF NOT EXISTS sales_order_shipments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sales_order_id UUID NOT NULL REFERENCES sales_orders(id) ON DELETE CASCADE,
    shipment_number VARCHAR(50) NOT NULL,
    tracking_number VARCHAR(100),
    shipping_carrier VARCHAR(100),
    shipping_method VARCHAR(100),
    shipped_date DATE NOT NULL,
    estimated_delivery_date DATE,
    actual_delivery_date DATE,
    shipping_cost DECIMAL(15,2) NOT NULL DEFAULT 0,
    notes TEXT,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Sales order shipment items
CREATE TABLE IF NOT EXISTS sales_order_shipment_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shipment_id UUID NOT NULL REFERENCES sales_order_shipments(id) ON DELETE CASCADE,
    sales_order_line_item_id UUID NOT NULL REFERENCES sales_order_line_items(id) ON DELETE CASCADE,
    quantity_shipped DECIMAL(15,4) NOT NULL DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Sales order comments/notes
CREATE TABLE IF NOT EXISTS sales_order_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sales_order_id UUID NOT NULL REFERENCES sales_orders(id) ON DELETE CASCADE,
    comment TEXT NOT NULL,
    is_internal BOOLEAN NOT NULL DEFAULT true,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_sales_orders_company_id ON sales_orders(company_id);
CREATE INDEX IF NOT EXISTS idx_sales_orders_customer_id ON sales_orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_sales_orders_status ON sales_orders(status);
CREATE INDEX IF NOT EXISTS idx_sales_orders_order_date ON sales_orders(order_date);
CREATE INDEX IF NOT EXISTS idx_sales_orders_delivery_date ON sales_orders(delivery_date);
CREATE INDEX IF NOT EXISTS idx_sales_orders_estimate_id ON sales_orders(estimate_id);

CREATE INDEX IF NOT EXISTS idx_sales_order_line_items_sales_order_id ON sales_order_line_items(sales_order_id);
CREATE INDEX IF NOT EXISTS idx_sales_order_line_items_inventory_item_id ON sales_order_line_items(inventory_item_id);

CREATE INDEX IF NOT EXISTS idx_sales_order_attachments_sales_order_id ON sales_order_attachments(sales_order_id);
CREATE INDEX IF NOT EXISTS idx_sales_order_history_sales_order_id ON sales_order_history(sales_order_id);
CREATE INDEX IF NOT EXISTS idx_sales_order_history_changed_at ON sales_order_history(changed_at);

CREATE INDEX IF NOT EXISTS idx_sales_order_shipments_sales_order_id ON sales_order_shipments(sales_order_id);
CREATE INDEX IF NOT EXISTS idx_sales_order_shipments_shipped_date ON sales_order_shipments(shipped_date);

CREATE INDEX IF NOT EXISTS idx_sales_order_shipment_items_shipment_id ON sales_order_shipment_items(shipment_id);
CREATE INDEX IF NOT EXISTS idx_sales_order_shipment_items_line_item_id ON sales_order_shipment_items(sales_order_line_item_id);

CREATE INDEX IF NOT EXISTS idx_sales_order_comments_sales_order_id ON sales_order_comments(sales_order_id);

-- Triggers for updated_at
CREATE OR REPLACE FUNCTION update_sales_orders_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_sales_orders_updated_at
    BEFORE UPDATE ON sales_orders
    FOR EACH ROW
    EXECUTE FUNCTION update_sales_orders_updated_at();

CREATE TRIGGER trigger_sales_order_line_items_updated_at
    BEFORE UPDATE ON sales_order_line_items
    FOR EACH ROW
    EXECUTE FUNCTION update_sales_orders_updated_at();

CREATE TRIGGER trigger_sales_order_attachments_updated_at
    BEFORE UPDATE ON sales_order_attachments
    FOR EACH ROW
    EXECUTE FUNCTION update_sales_orders_updated_at();

CREATE TRIGGER trigger_sales_order_shipments_updated_at
    BEFORE UPDATE ON sales_order_shipments
    FOR EACH ROW
    EXECUTE FUNCTION update_sales_orders_updated_at();

-- Function to update sales order totals
CREATE OR REPLACE FUNCTION update_sales_order_totals(sales_order_id_param UUID)
RETURNS VOID AS $$
DECLARE
    order_subtotal DECIMAL(15,2);
    order_tax_amount DECIMAL(15,2);
    order_discount_amount DECIMAL(15,2);
    order_total_amount DECIMAL(15,2);
    order_shipping_amount DECIMAL(15,2);
BEGIN
    -- Get shipping amount
    SELECT COALESCE(shipping_amount, 0) INTO order_shipping_amount
    FROM sales_orders
    WHERE id = sales_order_id_param;
    
    -- Calculate totals from line items
    SELECT 
        COALESCE(SUM(line_total), 0),
        COALESCE(SUM(tax_amount), 0),
        COALESCE(SUM(discount_amount), 0)
    INTO order_subtotal, order_tax_amount, order_discount_amount
    FROM sales_order_line_items
    WHERE sales_order_id = sales_order_id_param;
    
    -- Calculate total amount
    order_total_amount := order_subtotal + order_tax_amount - order_discount_amount + order_shipping_amount;
    
    -- Update sales order totals
    UPDATE sales_orders SET
        subtotal = order_subtotal,
        tax_amount = order_tax_amount,
        discount_amount = order_discount_amount,
        total_amount = order_total_amount,
        updated_at = NOW()
    WHERE id = sales_order_id_param;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update sales order totals when line items change
CREATE OR REPLACE FUNCTION trigger_update_sales_order_totals()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        PERFORM update_sales_order_totals(OLD.sales_order_id);
        RETURN OLD;
    ELSE
        PERFORM update_sales_order_totals(NEW.sales_order_id);
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_sales_order_line_items_totals
    AFTER INSERT OR UPDATE OR DELETE ON sales_order_line_items
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_sales_order_totals();

-- Function to generate sales order number
CREATE OR REPLACE FUNCTION generate_sales_order_number(company_id_param UUID)
RETURNS VARCHAR(50) AS $$
DECLARE
    last_number INTEGER;
    new_number VARCHAR(50);
BEGIN
    -- Get the last sales order number for the company
    SELECT COALESCE(
        MAX(CAST(REGEXP_REPLACE(sales_order_number, '[^0-9]', '', 'g') AS INTEGER)), 
        0
    ) INTO last_number
    FROM sales_orders 
    WHERE company_id = company_id_param;
    
    -- Generate new number
    new_number := 'SO-' || LPAD((last_number + 1)::TEXT, 6, '0');
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Function to update shipment quantities
CREATE OR REPLACE FUNCTION update_shipment_quantities()
RETURNS TRIGGER AS $$
BEGIN
    -- Update line item shipped quantity
    UPDATE sales_order_line_items 
    SET quantity_shipped = (
        SELECT COALESCE(SUM(quantity_shipped), 0)
        FROM sales_order_shipment_items sosi
        WHERE sosi.sales_order_line_item_id = sales_order_line_items.id
    )
    WHERE id = NEW.sales_order_line_item_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_shipment_quantities
    AFTER INSERT OR UPDATE OR DELETE ON sales_order_shipment_items
    FOR EACH ROW
    EXECUTE FUNCTION update_shipment_quantities();
