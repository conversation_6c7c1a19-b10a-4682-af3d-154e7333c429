-- Migration: Add Enterprise Features
-- Description: Add tables and indexes for Phase 1 Medium Enterprise features
-- Date: 2024-12-01

BEGIN;

-- =====================================================
-- ENHANCED TRANSACTION TEMPLATES
-- =====================================================

-- Add new columns to transaction_templates table
ALTER TABLE transaction_templates 
ADD COLUMN IF NOT EXISTS category VARCHAR(50) DEFAULT 'GENERAL',
ADD COLUMN IF NOT EXISTS default_values JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS usage_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_system_template BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}';

-- Create template categories enum
DO $$ BEGIN
    CREATE TYPE template_category AS ENUM (
        'GENERAL', 'SALES', 'PURCHASES', 'PAYROLL', 'BANKING', 
        'TAXES', 'ADJUSTMENTS', 'DEPRECIATION', 'INVENTORY'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update category column to use enum
ALTER TABLE transaction_templates 
ALTER COLUMN category TYPE template_category USING category::template_category;

-- Add template entry enhancements
ALTER TABLE transaction_template_entries
ADD COLUMN IF NOT EXISTS is_variable_amount BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS amount_formula TEXT,
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';

-- =====================================================
-- BATCH OPERATIONS
-- =====================================================

-- Create batch jobs table
CREATE TABLE IF NOT EXISTS batch_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    job_type VARCHAR(50) NOT NULL, -- 'IMPORT', 'EXPORT'
    entity_type VARCHAR(50) NOT NULL, -- 'TRANSACTIONS', 'TEMPLATES'
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING', -- 'PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED'
    progress INTEGER DEFAULT 0,
    total_records INTEGER DEFAULT 0,
    processed_records INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    warning_count INTEGER DEFAULT 0,
    file_name VARCHAR(255),
    file_size BIGINT,
    options JSONB DEFAULT '{}',
    result JSONB DEFAULT '{}',
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create batch job errors table
CREATE TABLE IF NOT EXISTS batch_job_errors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    batch_job_id UUID NOT NULL REFERENCES batch_jobs(id) ON DELETE CASCADE,
    row_number INTEGER,
    field_name VARCHAR(100),
    error_type VARCHAR(50), -- 'VALIDATION', 'CONSTRAINT', 'FORMAT', 'BUSINESS_RULE'
    error_message TEXT NOT NULL,
    field_value TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ENHANCED APPROVAL WORKFLOWS
-- =====================================================

-- Create workflows table
CREATE TABLE IF NOT EXISTS workflows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    trigger_type VARCHAR(50) NOT NULL, -- 'EVENT', 'SCHEDULE', 'MANUAL'
    trigger_config JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    version INTEGER DEFAULT 1,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create workflow steps table
CREATE TABLE IF NOT EXISTS workflow_steps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
    step_type VARCHAR(50) NOT NULL, -- 'APPROVAL', 'NOTIFICATION', 'CONDITION', 'ACTION'
    name VARCHAR(255) NOT NULL,
    config JSONB NOT NULL DEFAULT '{}',
    position_x INTEGER DEFAULT 0,
    position_y INTEGER DEFAULT 0,
    step_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create workflow executions table
CREATE TABLE IF NOT EXISTS workflow_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
    entity_type VARCHAR(50) NOT NULL, -- 'TRANSACTION', 'INVOICE', etc.
    entity_id UUID NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING', -- 'PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED'
    current_step_id UUID REFERENCES workflow_steps(id),
    context JSONB DEFAULT '{}',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create workflow step executions table
CREATE TABLE IF NOT EXISTS workflow_step_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_execution_id UUID NOT NULL REFERENCES workflow_executions(id) ON DELETE CASCADE,
    workflow_step_id UUID NOT NULL REFERENCES workflow_steps(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING', -- 'PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'SKIPPED'
    assigned_to UUID REFERENCES users(id),
    result JSONB DEFAULT '{}',
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ADVANCED REPORTING
-- =====================================================

-- Create custom reports table
CREATE TABLE IF NOT EXISTS custom_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    report_type VARCHAR(50) NOT NULL, -- 'TABLE', 'CHART', 'SUMMARY'
    config JSONB NOT NULL DEFAULT '{}',
    is_public BOOLEAN DEFAULT FALSE,
    is_scheduled BOOLEAN DEFAULT FALSE,
    schedule_config JSONB DEFAULT '{}',
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create report executions table
CREATE TABLE IF NOT EXISTS report_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    report_id UUID NOT NULL REFERENCES custom_reports(id) ON DELETE CASCADE,
    executed_by UUID REFERENCES users(id),
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING', -- 'PENDING', 'RUNNING', 'COMPLETED', 'FAILED'
    parameters JSONB DEFAULT '{}',
    result_data JSONB,
    file_path VARCHAR(500),
    file_size BIGINT,
    record_count INTEGER,
    execution_time_ms INTEGER,
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ENHANCED BANK INTEGRATION
-- =====================================================

-- Add new columns to bank_accounts table
ALTER TABLE bank_accounts
ADD COLUMN IF NOT EXISTS auto_reconciliation BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS sync_frequency VARCHAR(20) DEFAULT 'DAILY', -- 'REAL_TIME', 'HOURLY', 'DAILY', 'WEEKLY'
ADD COLUMN IF NOT EXISTS alert_settings JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS reconciliation_rules JSONB DEFAULT '[]';

-- Create bank reconciliation rules table
CREATE TABLE IF NOT EXISTS bank_reconciliation_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    bank_account_id UUID NOT NULL REFERENCES bank_accounts(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    conditions JSONB NOT NULL DEFAULT '[]',
    actions JSONB NOT NULL DEFAULT '[]',
    priority INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create bank alerts table
CREATE TABLE IF NOT EXISTS bank_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    bank_account_id UUID NOT NULL REFERENCES bank_accounts(id) ON DELETE CASCADE,
    alert_type VARCHAR(50) NOT NULL, -- 'LOW_BALANCE', 'LARGE_TRANSACTION', 'FAILED_SYNC', 'DUPLICATE_TRANSACTION'
    alert_data JSONB NOT NULL DEFAULT '{}',
    is_read BOOLEAN DEFAULT FALSE,
    acknowledged_by UUID REFERENCES users(id),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add reconciliation status to bank_transactions
ALTER TABLE bank_transactions
ADD COLUMN IF NOT EXISTS reconciliation_status VARCHAR(20) DEFAULT 'UNRECONCILED', -- 'UNRECONCILED', 'MATCHED', 'IGNORED', 'DISPUTED'
ADD COLUMN IF NOT EXISTS accounting_transaction_id UUID REFERENCES transactions(id),
ADD COLUMN IF NOT EXISTS auto_matched BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS match_confidence DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS reference VARCHAR(255);

-- =====================================================
-- PERFORMANCE OPTIMIZATION TABLES
-- =====================================================

-- Create cache invalidation table
CREATE TABLE IF NOT EXISTS cache_invalidations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cache_key VARCHAR(255) NOT NULL,
    invalidated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reason VARCHAR(100)
);

-- Create query performance log
CREATE TABLE IF NOT EXISTS query_performance_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query_hash VARCHAR(64) NOT NULL,
    query_type VARCHAR(50),
    execution_time_ms INTEGER NOT NULL,
    rows_affected INTEGER,
    table_names TEXT[],
    user_id UUID REFERENCES users(id),
    company_id UUID REFERENCES companies(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Transaction Templates Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transaction_templates_company_category 
ON transaction_templates(company_id, category) WHERE is_active = TRUE;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transaction_templates_usage 
ON transaction_templates(usage_count DESC) WHERE is_active = TRUE;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transaction_template_entries_template 
ON transaction_template_entries(template_id, line_number);

-- Batch Operations Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_batch_jobs_company_status 
ON batch_jobs(company_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_batch_jobs_user_type 
ON batch_jobs(user_id, job_type, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_batch_job_errors_job 
ON batch_job_errors(batch_job_id, row_number);

-- Workflow Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_workflows_company_active 
ON workflows(company_id, is_active, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_workflow_executions_entity 
ON workflow_executions(entity_type, entity_id, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_workflow_executions_status 
ON workflow_executions(status, created_at) WHERE status IN ('PENDING', 'RUNNING');

-- Reporting Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_custom_reports_company_type 
ON custom_reports(company_id, report_type, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_report_executions_report_status 
ON report_executions(report_id, status, created_at DESC);

-- Bank Integration Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bank_reconciliation_rules_account_active 
ON bank_reconciliation_rules(bank_account_id, is_active, priority);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bank_alerts_company_unread 
ON bank_alerts(company_id, is_read, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bank_transactions_reconciliation 
ON bank_transactions(company_id, reconciliation_status, date DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bank_transactions_auto_match 
ON bank_transactions(bank_account_id, auto_matched, amount, date) 
WHERE reconciliation_status = 'UNRECONCILED';

-- Performance Monitoring Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_query_performance_hash_time 
ON query_performance_log(query_hash, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_query_performance_slow 
ON query_performance_log(execution_time_ms DESC, created_at DESC) 
WHERE execution_time_ms > 1000;

-- =====================================================
-- ENHANCED EXISTING INDEXES
-- =====================================================

-- Optimize transaction queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_company_date_status 
ON transactions(company_id, transaction_date DESC, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_search 
ON transactions USING gin(to_tsvector('english', description || ' ' || COALESCE(reference, '')));

-- Optimize transaction entries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transaction_entries_account_date 
ON transaction_entries(account_id, created_at DESC);

-- Optimize accounts
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_accounts_company_type_active 
ON accounts(company_id, account_type, is_active);

-- =====================================================
-- TRIGGERS FOR AUDIT AND CACHE INVALIDATION
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to new tables
CREATE TRIGGER update_batch_jobs_updated_at BEFORE UPDATE ON batch_jobs 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workflows_updated_at BEFORE UPDATE ON workflows 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workflow_executions_updated_at BEFORE UPDATE ON workflow_executions 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_custom_reports_updated_at BEFORE UPDATE ON custom_reports 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bank_reconciliation_rules_updated_at BEFORE UPDATE ON bank_reconciliation_rules 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to invalidate cache on data changes
CREATE OR REPLACE FUNCTION invalidate_cache()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO cache_invalidations (cache_key, reason) 
    VALUES (TG_TABLE_NAME || ':' || COALESCE(NEW.company_id::text, OLD.company_id::text), TG_OP);
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Add cache invalidation triggers
CREATE TRIGGER invalidate_transactions_cache AFTER INSERT OR UPDATE OR DELETE ON transactions 
FOR EACH ROW EXECUTE FUNCTION invalidate_cache();

CREATE TRIGGER invalidate_accounts_cache AFTER INSERT OR UPDATE OR DELETE ON accounts 
FOR EACH ROW EXECUTE FUNCTION invalidate_cache();

CREATE TRIGGER invalidate_templates_cache AFTER INSERT OR UPDATE OR DELETE ON transaction_templates 
FOR EACH ROW EXECUTE FUNCTION invalidate_cache();

COMMIT;
