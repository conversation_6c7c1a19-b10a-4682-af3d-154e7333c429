import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Inventory Categories
  await knex.schema.createTable('inventory_categories', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').notNullable().references('id').inTable('companies').onDelete('CASCADE');
    table.string('name', 100).notNullable();
    table.string('description', 255);
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);
    table.unique(['company_id', 'name']);
  });

  // Inventory Items
  await knex.schema.createTable('inventory_items', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').notNullable().references('id').inTable('companies').onDelete('CASCADE');
    table.string('sku', 64).notNullable();
    table.string('barcode', 64);
    table.string('name', 255).notNullable();
    table.string('description', 1024);
    table.uuid('category_id').references('id').inTable('inventory_categories').onDelete('SET NULL');
    table.string('unit_of_measure', 32);
    table.decimal('purchase_price', 18, 4);
    table.decimal('sales_price', 18, 4);
    table.enum('cost_method', ['FIFO', 'AVG']).defaultTo('FIFO');
    table.uuid('preferred_vendor_id').references('id').inTable('contacts').onDelete('SET NULL');
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);
    table.unique(['company_id', 'sku']);
  });

  // Inventory Locations
  await knex.schema.createTable('inventory_locations', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').notNullable().references('id').inTable('companies').onDelete('CASCADE');
    table.string('name', 100).notNullable();
    table.string('description', 255);
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);
    table.unique(['company_id', 'name']);
  });

  // Inventory Item Locations (Stock per Location)
  await knex.schema.createTable('inventory_item_locations', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('item_id').notNullable().references('id').inTable('inventory_items').onDelete('CASCADE');
    table.uuid('location_id').notNullable().references('id').inTable('inventory_locations').onDelete('CASCADE');
    table.decimal('quantity_on_hand', 18, 4).notNullable().defaultTo(0);
    table.decimal('reorder_point', 18, 4).defaultTo(0);
    table.decimal('min_level', 18, 4).defaultTo(0);
    table.decimal('max_level', 18, 4).defaultTo(0);
    table.timestamps(true, true);
    table.unique(['item_id', 'location_id']);
  });

  // Inventory Adjustments
  await knex.schema.createTable('inventory_adjustments', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('item_id').notNullable().references('id').inTable('inventory_items').onDelete('CASCADE');
    table.uuid('location_id').notNullable().references('id').inTable('inventory_locations').onDelete('CASCADE');
    table.enum('adjustment_type', ['INCREASE', 'DECREASE', 'CORRECTION']).notNullable();
    table.decimal('quantity', 18, 4).notNullable();
    table.string('reason', 255);
    table.string('reference', 255);
    table.uuid('user_id').references('id').inTable('users').onDelete('SET NULL');
    table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
  });

  // Inventory Transactions (Audit Trail)
  await knex.schema.createTable('inventory_transactions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('item_id').notNullable().references('id').inTable('inventory_items').onDelete('CASCADE');
    table.uuid('location_id').notNullable().references('id').inTable('inventory_locations').onDelete('CASCADE');
    table.enum('transaction_type', ['adjustment', 'purchase', 'sale', 'transfer_in', 'transfer_out', 'stocktake']).notNullable();
    table.decimal('quantity', 18, 4).notNullable();
    table.decimal('cost', 18, 4);
    table.decimal('price', 18, 4);
    table.string('related_doc_type', 32);
    table.uuid('related_doc_id');
    table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('inventory_transactions');
  await knex.schema.dropTableIfExists('inventory_adjustments');
  await knex.schema.dropTableIfExists('inventory_item_locations');
  await knex.schema.dropTableIfExists('inventory_locations');
  await knex.schema.dropTableIfExists('inventory_items');
  await knex.schema.dropTableIfExists('inventory_categories');
}
