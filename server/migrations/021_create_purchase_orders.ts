import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  console.log("🚀 Creating purchase orders tables...");

  // Create purchase order status enum
  await knex.raw(`
    DO $$ BEGIN
      CREATE TYPE purchase_order_status AS ENUM (
        'DRAFT',
        'PENDING_APPROVAL',
        'APPROVED',
        'SENT',
        'PARTIALLY_RECEIVED',
        'FULLY_RECEIVED',
        'CANCELLED',
        'CLOSED'
      );
    EXCEPTION
      WHEN duplicate_object THEN null;
    END $$;
  `);

  // Create purchase orders table
  await knex.schema.createTable("purchase_orders", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("po_number", 50).notNullable();
    table
      .uuid("vendor_id")
      .notNullable()
      .references("id")
      .inTable("contacts")
      .onDelete("RESTRICT");

    // Dates
    table.date("po_date").notNullable();
    table.date("expected_delivery_date");
    table.date("delivery_date");

    // Status and workflow
    table.specificType("status", "purchase_order_status").defaultTo("DRAFT");
    table
      .uuid("approved_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamp("approved_at");

    // Financial details
    table.string("currency", 3).defaultTo("USD");
    table.decimal("exchange_rate", 15, 8).defaultTo(1);
    table.decimal("subtotal", 15, 2).notNullable().defaultTo(0);
    table.decimal("tax_amount", 15, 2).notNullable().defaultTo(0);
    table.decimal("discount_amount", 15, 2).notNullable().defaultTo(0);
    table.decimal("total_amount", 15, 2).notNullable().defaultTo(0);

    // Addresses
    table.text("billing_address");
    table.text("shipping_address");

    // Additional details
    table.text("notes");
    table.text("terms_and_conditions");
    table.string("reference_number", 100);
    // table.uuid("project_id").references("id").inTable("projects").onDelete("SET NULL"); // For job costing - TODO: Add when projects table exists

    // Tracking
    table
      .uuid("created_by")
      .notNullable()
      .references("id")
      .inTable("users")
      .onDelete("RESTRICT");
    table
      .uuid("updated_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamps(true, true);

    // Indexes
    table.index(["company_id", "po_date"]);
    table.index(["vendor_id", "status"]);
    table.index(["status", "po_date"]);
    table.unique(["company_id", "po_number"]);
  });

  // Create purchase order line items table
  await knex.schema.createTable("purchase_order_line_items", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("purchase_order_id")
      .notNullable()
      .references("id")
      .inTable("purchase_orders")
      .onDelete("CASCADE");
    table.integer("line_number").notNullable();

    // Item details
    table
      .uuid("inventory_item_id")
      .references("id")
      .inTable("inventory_items")
      .onDelete("SET NULL");
    table.string("item_code", 100);
    table.string("description", 500).notNullable();
    table.string("unit_of_measure", 50);

    // Quantities
    table.decimal("quantity_ordered", 15, 4).notNullable();
    table.decimal("quantity_received", 15, 4).notNullable().defaultTo(0);
    table.decimal("quantity_billed", 15, 4).notNullable().defaultTo(0);

    // Pricing
    table.decimal("unit_cost", 15, 4).notNullable();
    table.decimal("discount_percentage", 5, 2).defaultTo(0);
    table.decimal("discount_amount", 15, 2).defaultTo(0);
    table.decimal("line_total", 15, 2).notNullable();

    // Tax details
    table.decimal("tax_rate", 5, 2).defaultTo(0);
    table.decimal("tax_amount", 15, 2).defaultTo(0);

    // Tracking
    table.date("expected_delivery_date");
    table.text("notes");
    table.timestamps(true, true);

    // Indexes
    table.index(["purchase_order_id", "line_number"]);
    table.index(["inventory_item_id"]);
    table.unique(["purchase_order_id", "line_number"]);
  });

  // Create purchase order attachments table
  await knex.schema.createTable("purchase_order_attachments", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("purchase_order_id")
      .notNullable()
      .references("id")
      .inTable("purchase_orders")
      .onDelete("CASCADE");
    table.string("file_name", 255).notNullable();
    table.string("file_path", 500).notNullable();
    table.string("file_type", 100);
    table.integer("file_size");
    table
      .uuid("uploaded_by")
      .notNullable()
      .references("id")
      .inTable("users")
      .onDelete("RESTRICT");
    table.timestamps(true, true);

    table.index(["purchase_order_id"]);
  });

  // Create purchase order history table (for audit trail)
  await knex.schema.createTable("purchase_order_history", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("purchase_order_id")
      .notNullable()
      .references("id")
      .inTable("purchase_orders")
      .onDelete("CASCADE");
    table.specificType("old_status", "purchase_order_status");
    table.specificType("new_status", "purchase_order_status").notNullable();
    table.text("notes");
    table
      .uuid("changed_by")
      .notNullable()
      .references("id")
      .inTable("users")
      .onDelete("RESTRICT");
    table.timestamp("changed_at").defaultTo(knex.fn.now());

    table.index(["purchase_order_id", "changed_at"]);
  });

  console.log("✅ Purchase orders tables created successfully");
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("purchase_order_history");
  await knex.schema.dropTableIfExists("purchase_order_attachments");
  await knex.schema.dropTableIfExists("purchase_order_line_items");
  await knex.schema.dropTableIfExists("purchase_orders");
  await knex.raw("DROP TYPE IF EXISTS purchase_order_status");

  console.log("✅ Purchase orders tables dropped successfully");
}
