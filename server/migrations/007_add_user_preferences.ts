import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Add preferences column to user_companies table
  await knex.schema.alterTable('user_companies', (table) => {
    table.jsonb('preferences').defaultTo('{}');
  });
}

export async function down(knex: Knex): Promise<void> {
  // Remove preferences column from user_companies table
  await knex.schema.alterTable('user_companies', (table) => {
    table.dropColumn('preferences');
  });
}
