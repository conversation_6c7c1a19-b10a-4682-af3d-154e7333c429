-- Purchase Receipts Management Tables
-- These tables handle receiving goods against purchase orders

-- Purchase receipts table
CREATE TABLE IF NOT EXISTS purchase_receipts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    vendor_id UUID NOT NULL REFERENCES contacts(id) ON DELETE RESTRICT,
    purchase_order_id UUID REFERENCES purchase_orders(id) ON DELETE RESTRICT,
    receipt_number VARCHAR(50) NOT NULL,
    receipt_date DATE NOT NULL,
    delivery_note_number VARCHAR(100),
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    exchange_rate DECIMAL(15,8) NOT NULL DEFAULT 1.0,
    
    -- Status and workflow
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' CHECK (status IN (
        'DRAFT', 'RECEIVED', 'INSPECTED', 'ACCEPTED', 'REJECTED', 'PARTIALLY_ACCEPTED'
    )),
    
    -- Vendor information
    vendor_name VARCHAR(255) NOT NULL,
    vendor_email VARCHAR(255),
    vendor_phone VARCHAR(50),
    vendor_address TEXT,
    
    -- Delivery details
    delivery_address TEXT,
    delivery_instructions TEXT,
    received_by VARCHAR(255),
    inspection_notes TEXT,
    notes TEXT,
    
    -- Quality control
    quality_check_passed BOOLEAN,
    quality_check_notes TEXT,
    quality_checked_by UUID REFERENCES users(id),
    quality_checked_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit fields
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(company_id, receipt_number)
);

-- Purchase receipt line items
CREATE TABLE IF NOT EXISTS purchase_receipt_line_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    purchase_receipt_id UUID NOT NULL REFERENCES purchase_receipts(id) ON DELETE CASCADE,
    purchase_order_line_item_id UUID REFERENCES purchase_order_line_items(id),
    line_number INTEGER NOT NULL,
    
    -- Item details
    inventory_item_id UUID REFERENCES inventory_items(id),
    item_code VARCHAR(50),
    description TEXT NOT NULL,
    unit_of_measure VARCHAR(20),
    
    -- Quantities
    quantity_ordered DECIMAL(15,4) NOT NULL DEFAULT 0,
    quantity_received DECIMAL(15,4) NOT NULL DEFAULT 0,
    quantity_accepted DECIMAL(15,4) NOT NULL DEFAULT 0,
    quantity_rejected DECIMAL(15,4) NOT NULL DEFAULT 0,
    
    -- Quality control
    condition_on_arrival VARCHAR(20) DEFAULT 'GOOD' CHECK (condition_on_arrival IN (
        'GOOD', 'DAMAGED', 'DEFECTIVE', 'INCOMPLETE', 'WRONG_ITEM'
    )),
    inspection_notes TEXT,
    
    -- Additional details
    batch_number VARCHAR(100),
    serial_numbers TEXT,
    expiry_date DATE,
    location VARCHAR(100),
    notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(purchase_receipt_id, line_number)
);

-- Purchase receipt attachments
CREATE TABLE IF NOT EXISTS purchase_receipt_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    purchase_receipt_id UUID NOT NULL REFERENCES purchase_receipts(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_type VARCHAR(50),
    file_size INTEGER,
    attachment_type VARCHAR(20) DEFAULT 'DOCUMENT' CHECK (attachment_type IN (
        'DOCUMENT', 'PHOTO', 'DELIVERY_NOTE', 'INSPECTION_REPORT', 'OTHER'
    )),
    uploaded_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Purchase receipt history (audit trail)
CREATE TABLE IF NOT EXISTS purchase_receipt_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    purchase_receipt_id UUID NOT NULL REFERENCES purchase_receipts(id) ON DELETE CASCADE,
    old_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    notes TEXT,
    changed_by UUID NOT NULL REFERENCES users(id),
    changed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Purchase receipt comments/notes
CREATE TABLE IF NOT EXISTS purchase_receipt_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    purchase_receipt_id UUID NOT NULL REFERENCES purchase_receipts(id) ON DELETE CASCADE,
    comment TEXT NOT NULL,
    is_internal BOOLEAN NOT NULL DEFAULT true,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_purchase_receipts_company_id ON purchase_receipts(company_id);
CREATE INDEX IF NOT EXISTS idx_purchase_receipts_vendor_id ON purchase_receipts(vendor_id);
CREATE INDEX IF NOT EXISTS idx_purchase_receipts_purchase_order_id ON purchase_receipts(purchase_order_id);
CREATE INDEX IF NOT EXISTS idx_purchase_receipts_status ON purchase_receipts(status);
CREATE INDEX IF NOT EXISTS idx_purchase_receipts_receipt_date ON purchase_receipts(receipt_date);

CREATE INDEX IF NOT EXISTS idx_purchase_receipt_line_items_receipt_id ON purchase_receipt_line_items(purchase_receipt_id);
CREATE INDEX IF NOT EXISTS idx_purchase_receipt_line_items_po_line_item_id ON purchase_receipt_line_items(purchase_order_line_item_id);
CREATE INDEX IF NOT EXISTS idx_purchase_receipt_line_items_inventory_item_id ON purchase_receipt_line_items(inventory_item_id);

CREATE INDEX IF NOT EXISTS idx_purchase_receipt_attachments_receipt_id ON purchase_receipt_attachments(purchase_receipt_id);
CREATE INDEX IF NOT EXISTS idx_purchase_receipt_history_receipt_id ON purchase_receipt_history(purchase_receipt_id);
CREATE INDEX IF NOT EXISTS idx_purchase_receipt_history_changed_at ON purchase_receipt_history(changed_at);

CREATE INDEX IF NOT EXISTS idx_purchase_receipt_comments_receipt_id ON purchase_receipt_comments(purchase_receipt_id);

-- Triggers for updated_at
CREATE OR REPLACE FUNCTION update_purchase_receipts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_purchase_receipts_updated_at
    BEFORE UPDATE ON purchase_receipts
    FOR EACH ROW
    EXECUTE FUNCTION update_purchase_receipts_updated_at();

CREATE TRIGGER trigger_purchase_receipt_line_items_updated_at
    BEFORE UPDATE ON purchase_receipt_line_items
    FOR EACH ROW
    EXECUTE FUNCTION update_purchase_receipts_updated_at();

CREATE TRIGGER trigger_purchase_receipt_attachments_updated_at
    BEFORE UPDATE ON purchase_receipt_attachments
    FOR EACH ROW
    EXECUTE FUNCTION update_purchase_receipts_updated_at();

-- Function to generate purchase receipt number
CREATE OR REPLACE FUNCTION generate_purchase_receipt_number(company_id_param UUID)
RETURNS VARCHAR(50) AS $$
DECLARE
    last_number INTEGER;
    new_number VARCHAR(50);
BEGIN
    -- Get the last purchase receipt number for the company
    SELECT COALESCE(
        MAX(CAST(REGEXP_REPLACE(receipt_number, '[^0-9]', '', 'g') AS INTEGER)), 
        0
    ) INTO last_number
    FROM purchase_receipts 
    WHERE company_id = company_id_param;
    
    -- Generate new number
    new_number := 'PR-' || LPAD((last_number + 1)::TEXT, 6, '0');
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Function to update purchase order received quantities
CREATE OR REPLACE FUNCTION update_po_received_quantities()
RETURNS TRIGGER AS $$
BEGIN
    -- Update purchase order line item received quantity
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE purchase_order_line_items 
        SET quantity_received = (
            SELECT COALESCE(SUM(quantity_received), 0)
            FROM purchase_receipt_line_items
            WHERE purchase_order_line_item_id = NEW.purchase_order_line_item_id
        )
        WHERE id = NEW.purchase_order_line_item_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE purchase_order_line_items 
        SET quantity_received = (
            SELECT COALESCE(SUM(quantity_received), 0)
            FROM purchase_receipt_line_items
            WHERE purchase_order_line_item_id = OLD.purchase_order_line_item_id
        )
        WHERE id = OLD.purchase_order_line_item_id;
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_po_received_quantities
    AFTER INSERT OR UPDATE OR DELETE ON purchase_receipt_line_items
    FOR EACH ROW
    EXECUTE FUNCTION update_po_received_quantities();

-- Function to update purchase order status based on received quantities
CREATE OR REPLACE FUNCTION update_po_status_from_receipts()
RETURNS TRIGGER AS $$
DECLARE
    po_id UUID;
    total_ordered DECIMAL(15,4);
    total_received DECIMAL(15,4);
    po_status VARCHAR(20);
BEGIN
    -- Get the purchase order ID
    IF TG_OP = 'DELETE' THEN
        SELECT purchase_order_id INTO po_id
        FROM purchase_receipts
        WHERE id = OLD.purchase_receipt_id;
    ELSE
        SELECT purchase_order_id INTO po_id
        FROM purchase_receipts
        WHERE id = NEW.purchase_receipt_id;
    END IF;
    
    IF po_id IS NULL THEN
        RETURN COALESCE(NEW, OLD);
    END IF;
    
    -- Calculate totals
    SELECT 
        COALESCE(SUM(quantity), 0),
        COALESCE(SUM(quantity_received), 0)
    INTO total_ordered, total_received
    FROM purchase_order_line_items
    WHERE purchase_order_id = po_id;
    
    -- Get current PO status
    SELECT status INTO po_status
    FROM purchase_orders
    WHERE id = po_id;
    
    -- Update PO status based on received quantities
    IF total_received = 0 THEN
        -- No items received yet, keep current status if it's APPROVED or SENT
        IF po_status IN ('APPROVED', 'SENT') THEN
            -- Keep current status
            NULL;
        END IF;
    ELSIF total_received >= total_ordered THEN
        -- All items received
        UPDATE purchase_orders
        SET status = 'RECEIVED', updated_at = NOW()
        WHERE id = po_id AND status != 'RECEIVED';
    ELSIF total_received > 0 THEN
        -- Partially received
        UPDATE purchase_orders
        SET status = 'PARTIALLY_RECEIVED', updated_at = NOW()
        WHERE id = po_id AND status NOT IN ('PARTIALLY_RECEIVED', 'RECEIVED');
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_po_status_from_receipts
    AFTER INSERT OR UPDATE OR DELETE ON purchase_receipt_line_items
    FOR EACH ROW
    EXECUTE FUNCTION update_po_status_from_receipts();

-- Function to validate receipt quantities
CREATE OR REPLACE FUNCTION validate_receipt_quantities()
RETURNS TRIGGER AS $$
DECLARE
    ordered_qty DECIMAL(15,4);
    already_received DECIMAL(15,4);
    max_receivable DECIMAL(15,4);
BEGIN
    -- Only validate if we have a PO line item reference
    IF NEW.purchase_order_line_item_id IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Get ordered quantity
    SELECT quantity INTO ordered_qty
    FROM purchase_order_line_items
    WHERE id = NEW.purchase_order_line_item_id;
    
    -- Get already received quantity (excluding current record)
    SELECT COALESCE(SUM(quantity_received), 0) INTO already_received
    FROM purchase_receipt_line_items
    WHERE purchase_order_line_item_id = NEW.purchase_order_line_item_id
    AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::UUID);
    
    -- Calculate maximum receivable
    max_receivable := ordered_qty - already_received;
    
    -- Validate received quantity
    IF NEW.quantity_received > max_receivable THEN
        RAISE EXCEPTION 'Cannot receive % items. Maximum receivable: % (Ordered: %, Already received: %)', 
            NEW.quantity_received, max_receivable, ordered_qty, already_received;
    END IF;
    
    -- Validate accepted + rejected = received
    IF (NEW.quantity_accepted + NEW.quantity_rejected) != NEW.quantity_received THEN
        RAISE EXCEPTION 'Accepted quantity (%) + Rejected quantity (%) must equal received quantity (%)', 
            NEW.quantity_accepted, NEW.quantity_rejected, NEW.quantity_received;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_validate_receipt_quantities
    BEFORE INSERT OR UPDATE ON purchase_receipt_line_items
    FOR EACH ROW
    EXECUTE FUNCTION validate_receipt_quantities();
