import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create transaction template categories enum
  await knex.raw(`
    CREATE TYPE template_category AS ENUM (
      'GENERAL',
      'SALES',
      'PURCHASES',
      'PAYROLL',
      'BANKING',
      'ADJUSTMENTS',
      'DEPRECIATION',
      'TAX',
      'INVENTORY',
      'FIXED_ASSETS'
    );
  `);

  // Create transaction templates table
  await knex.schema.createTable('transaction_templates', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.string('name', 255).notNullable();
    table.text('description');
    table.specificType('category', 'template_category').defaultTo('GENERAL');
    table.boolean('is_active').defaultTo(true);
    table.boolean('is_system_template').defaultTo(false);
    table.integer('usage_count').defaultTo(0);
    table.jsonb('default_values').defaultTo('{}');
    table.uuid('created_by').references('id').inTable('users').onDelete('RESTRICT');
    table.timestamps(true, true);
    
    table.index(['company_id', 'category']);
    table.index(['company_id', 'is_active']);
    table.index(['is_system_template']);
  });

  // Create transaction template entries table
  await knex.schema.createTable('transaction_template_entries', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('template_id').references('id').inTable('transaction_templates').onDelete('CASCADE');
    table.uuid('account_id').references('id').inTable('accounts').onDelete('RESTRICT');
    table.text('description');
    table.decimal('debit_amount', 15, 2).defaultTo(0);
    table.decimal('credit_amount', 15, 2).defaultTo(0);
    table.boolean('is_variable_amount').defaultTo(false);
    table.string('amount_formula', 500); // For calculated amounts
    table.integer('line_number').notNullable();
    table.jsonb('metadata').defaultTo('{}');
    table.timestamps(true, true);
    
    table.index(['template_id', 'line_number']);
    table.index(['account_id']);
  });

  // Create transaction template usage tracking
  await knex.schema.createTable('transaction_template_usage', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('template_id').references('id').inTable('transaction_templates').onDelete('CASCADE');
    table.uuid('transaction_id').references('id').inTable('transactions').onDelete('CASCADE');
    table.uuid('used_by').references('id').inTable('users').onDelete('RESTRICT');
    table.jsonb('applied_values').defaultTo('{}');
    table.timestamps(true, true);
    
    table.index(['template_id']);
    table.index(['transaction_id']);
    table.index(['used_by']);
  });

  // Insert some system templates
  const systemTemplates = [
    {
      id: knex.raw('gen_random_uuid()'),
      company_id: null, // System-wide template
      name: 'Sales Invoice Payment',
      description: 'Record payment received for sales invoice',
      category: 'SALES',
      is_system_template: true,
      default_values: JSON.stringify({
        description: 'Payment received for Invoice #{{invoice_number}}',
        reference: '{{payment_reference}}'
      }),
      created_by: null,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      company_id: null,
      name: 'Purchase Invoice Payment',
      description: 'Record payment made for purchase invoice',
      category: 'PURCHASES',
      is_system_template: true,
      default_values: JSON.stringify({
        description: 'Payment made for Invoice #{{invoice_number}}',
        reference: '{{payment_reference}}'
      }),
      created_by: null,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      company_id: null,
      name: 'Bank Transfer',
      description: 'Transfer funds between bank accounts',
      category: 'BANKING',
      is_system_template: true,
      default_values: JSON.stringify({
        description: 'Transfer from {{from_account}} to {{to_account}}',
        reference: '{{transfer_reference}}'
      }),
      created_by: null,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      company_id: null,
      name: 'Monthly Depreciation',
      description: 'Record monthly depreciation expense',
      category: 'DEPRECIATION',
      is_system_template: true,
      default_values: JSON.stringify({
        description: 'Monthly depreciation for {{period}}',
        reference: 'DEP-{{period}}'
      }),
      created_by: null,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      company_id: null,
      name: 'Salary Payment',
      description: 'Record salary payment to employee',
      category: 'PAYROLL',
      is_system_template: true,
      default_values: JSON.stringify({
        description: 'Salary payment for {{employee_name}} - {{period}}',
        reference: 'SAL-{{employee_id}}-{{period}}'
      }),
      created_by: null,
      created_at: new Date(),
      updated_at: new Date()
    }
  ];

  // Note: We'll insert these after creating the entries in a separate migration
  // to avoid foreign key constraints
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('transaction_template_usage');
  await knex.schema.dropTableIfExists('transaction_template_entries');
  await knex.schema.dropTableIfExists('transaction_templates');
  await knex.raw('DROP TYPE IF EXISTS template_category');
}
