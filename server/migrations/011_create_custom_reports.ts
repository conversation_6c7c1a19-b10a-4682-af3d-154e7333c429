import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create report type enum
  await knex.raw(`
    CREATE TYPE report_type AS ENUM ('TABLE', 'CHART', 'PIVOT', 'DASHBOARD');
  `);

  // Create report status enum
  await knex.raw(`
    CREATE TYPE report_status AS ENUM ('DRAFT', 'PUBLISHED', 'ARCHIVED');
  `);

  // Create custom reports table
  await knex.schema.createTable('custom_reports', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').notNullable().references('id').inTable('companies').onDelete('CASCADE');
    table.string('name', 255).notNullable();
    table.text('description');
    table.specificType('report_type', 'report_type').notNullable();
    table.specificType('status', 'report_status').defaultTo('DRAFT');
    table.jsonb('data_source').notNullable(); // Tables, joins, filters
    table.jsonb('columns').notNullable(); // Column definitions with formatting
    table.jsonb('group_by'); // Grouping fields
    table.jsonb('order_by'); // Sorting configuration
    table.jsonb('chart_config'); // Chart-specific configuration
    table.jsonb('parameters'); // Report parameters/variables
    table.boolean('is_public').defaultTo(false);
    table.jsonb('tags').defaultTo('[]'); // Tags for categorization
    table.uuid('created_by').references('id').inTable('users').onDelete('SET NULL');
    table.uuid('updated_by').references('id').inTable('users').onDelete('SET NULL');
    table.timestamps(true, true);
    
    table.index(['company_id', 'report_type']);
    table.index(['company_id', 'status']);
    table.index(['company_id', 'is_public']);
    table.index(['created_by']);
    table.unique(['company_id', 'name']);
  });

  // Create report executions table (for analytics and caching)
  await knex.schema.createTable('report_executions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('report_id').notNullable().references('id').inTable('custom_reports').onDelete('CASCADE');
    table.uuid('executed_by').references('id').inTable('users').onDelete('SET NULL');
    table.timestamp('executed_at').defaultTo(knex.fn.now());
    table.integer('execution_time_ms').notNullable();
    table.integer('row_count').notNullable();
    table.jsonb('parameters'); // Parameters used in execution
    table.jsonb('filters_applied'); // Filters applied during execution
    table.text('error_message'); // If execution failed
    table.boolean('cached').defaultTo(false);
    table.timestamp('cache_expires_at');
    
    table.index(['report_id', 'executed_at']);
    table.index(['executed_by', 'executed_at']);
    table.index(['executed_at']);
  });

  // Create report templates table
  await knex.schema.createTable('report_templates', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('name', 255).notNullable();
    table.text('description');
    table.specificType('report_type', 'report_type').notNullable();
    table.string('category', 100).notNullable(); // Financial, Operational, etc.
    table.jsonb('template_config').notNullable(); // Template structure
    table.jsonb('required_tables').notNullable(); // Tables needed for this template
    table.jsonb('sample_data'); // Sample data for preview
    table.boolean('is_system').defaultTo(false); // System vs user templates
    table.boolean('is_active').defaultTo(true);
    table.uuid('created_by').references('id').inTable('users').onDelete('SET NULL');
    table.timestamps(true, true);
    
    table.index(['category', 'is_active']);
    table.index(['report_type', 'is_active']);
    table.unique(['name']);
  });

  // Create report shares table (for sharing reports with specific users)
  await knex.schema.createTable('report_shares', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('report_id').notNullable().references('id').inTable('custom_reports').onDelete('CASCADE');
    table.uuid('shared_with_user_id').references('id').inTable('users').onDelete('CASCADE');
    table.string('shared_with_email', 255); // For external sharing
    table.enum('permission_level', ['VIEW', 'EDIT', 'ADMIN']).defaultTo('VIEW');
    table.timestamp('expires_at');
    table.uuid('shared_by').references('id').inTable('users').onDelete('SET NULL');
    table.timestamps(true, true);
    
    table.index(['report_id']);
    table.index(['shared_with_user_id']);
    table.index(['shared_with_email']);
    table.unique(['report_id', 'shared_with_user_id']);
  });

  // Create report schedules table (for automated report generation)
  await knex.schema.createTable('report_schedules', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('report_id').notNullable().references('id').inTable('custom_reports').onDelete('CASCADE');
    table.string('name', 255).notNullable();
    table.enum('frequency', ['DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY']).notNullable();
    table.jsonb('schedule_config').notNullable(); // Cron-like configuration
    table.jsonb('recipients').notNullable(); // Email addresses
    table.enum('format', ['PDF', 'EXCEL', 'CSV', 'JSON']).defaultTo('PDF');
    table.boolean('is_active').defaultTo(true);
    table.timestamp('last_run_at');
    table.timestamp('next_run_at');
    table.uuid('created_by').references('id').inTable('users').onDelete('SET NULL');
    table.timestamps(true, true);
    
    table.index(['report_id', 'is_active']);
    table.index(['next_run_at', 'is_active']);
  });

  // Create report cache table (for performance optimization)
  await knex.schema.createTable('report_cache', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('report_id').notNullable().references('id').inTable('custom_reports').onDelete('CASCADE');
    table.string('cache_key', 255).notNullable(); // Hash of report + parameters
    table.jsonb('cached_data').notNullable();
    table.jsonb('cached_metadata');
    table.timestamp('cached_at').defaultTo(knex.fn.now());
    table.timestamp('expires_at').notNullable();
    table.integer('hit_count').defaultTo(0);
    
    table.index(['report_id', 'cache_key']);
    table.index(['expires_at']);
    table.unique(['cache_key']);
  });

  // Create report data sources table (for managing available data sources)
  await knex.schema.createTable('report_data_sources', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').notNullable().references('id').inTable('companies').onDelete('CASCADE');
    table.string('name', 255).notNullable();
    table.text('description');
    table.string('table_name', 100).notNullable();
    table.jsonb('available_fields').notNullable(); // Field definitions
    table.jsonb('relationships'); // Foreign key relationships
    table.boolean('is_active').defaultTo(true);
    table.boolean('requires_permission').defaultTo(false);
    table.string('permission_required', 100);
    table.timestamps(true, true);
    
    table.index(['company_id', 'is_active']);
    table.index(['table_name']);
    table.unique(['company_id', 'table_name']);
  });

  // Insert default report templates
  await knex('report_templates').insert([
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Account Balance Summary',
      description: 'Summary of account balances by type',
      report_type: 'TABLE',
      category: 'Financial',
      template_config: JSON.stringify({
        dataSource: {
          tables: ['accounts', 'transaction_entries'],
          joins: [{
            table: 'transaction_entries',
            joinType: 'LEFT',
            condition: 'accounts.id = transaction_entries.account_id'
          }]
        },
        columns: [
          { field: 'accounts.code', alias: 'Account Code' },
          { field: 'accounts.name', alias: 'Account Name' },
          { field: 'accounts.type', alias: 'Account Type' },
          { field: 'SUM(transaction_entries.debit_amount - transaction_entries.credit_amount)', alias: 'Balance', aggregation: 'SUM', format: 'CURRENCY' }
        ],
        groupBy: ['accounts.id', 'accounts.code', 'accounts.name', 'accounts.type'],
        orderBy: [{ field: 'accounts.code', direction: 'ASC' }]
      }),
      required_tables: JSON.stringify(['accounts', 'transaction_entries']),
      is_system: true
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Monthly Revenue Trend',
      description: 'Revenue trend analysis by month',
      report_type: 'CHART',
      category: 'Financial',
      template_config: JSON.stringify({
        dataSource: {
          tables: ['transactions', 'transaction_entries', 'accounts'],
          joins: [
            { table: 'transaction_entries', joinType: 'INNER', condition: 'transactions.id = transaction_entries.transaction_id' },
            { table: 'accounts', joinType: 'INNER', condition: 'transaction_entries.account_id = accounts.id' }
          ],
          filters: [
            { field: 'accounts.type', operator: '=', value: 'REVENUE' },
            { field: 'transactions.status', operator: '=', value: 'POSTED' }
          ]
        },
        columns: [
          { field: 'DATE_TRUNC(\'month\', transactions.transaction_date)', alias: 'Month', format: 'DATE' },
          { field: 'SUM(transaction_entries.credit_amount)', alias: 'Revenue', aggregation: 'SUM', format: 'CURRENCY' }
        ],
        groupBy: ['DATE_TRUNC(\'month\', transactions.transaction_date)'],
        orderBy: [{ field: 'DATE_TRUNC(\'month\', transactions.transaction_date)', direction: 'ASC' }],
        chartConfig: {
          chartType: 'LINE',
          xAxis: 'Month',
          yAxis: 'Revenue'
        }
      }),
      required_tables: JSON.stringify(['transactions', 'transaction_entries', 'accounts']),
      is_system: true
    }
  ]);

  // Insert default data sources
  await knex.raw(`
    INSERT INTO report_data_sources (company_id, name, description, table_name, available_fields, is_active)
    SELECT 
      c.id as company_id,
      ds.name,
      ds.description,
      ds.table_name,
      ds.available_fields::jsonb,
      true as is_active
    FROM companies c
    CROSS JOIN (
      VALUES 
        ('Accounts', 'Chart of accounts', 'accounts', '{"id": "UUID", "code": "String", "name": "String", "type": "String", "balance": "Currency"}'),
        ('Transactions', 'All transactions', 'transactions', '{"id": "UUID", "transaction_number": "String", "transaction_date": "Date", "description": "String", "total_amount": "Currency", "status": "String"}'),
        ('Transaction Entries', 'Transaction line items', 'transaction_entries', '{"id": "UUID", "transaction_id": "UUID", "account_id": "UUID", "debit_amount": "Currency", "credit_amount": "Currency"}'),
        ('Invoices', 'Sales and purchase invoices', 'invoices', '{"id": "UUID", "invoice_number": "String", "invoice_date": "Date", "total_amount": "Currency", "status": "String"}'),
        ('Contacts', 'Customers and vendors', 'contacts', '{"id": "UUID", "name": "String", "email": "String", "contact_type": "String"}')
    ) AS ds(name, description, table_name, available_fields);
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('report_data_sources');
  await knex.schema.dropTableIfExists('report_cache');
  await knex.schema.dropTableIfExists('report_schedules');
  await knex.schema.dropTableIfExists('report_shares');
  await knex.schema.dropTableIfExists('report_templates');
  await knex.schema.dropTableIfExists('report_executions');
  await knex.schema.dropTableIfExists('custom_reports');
  
  await knex.raw('DROP TYPE IF EXISTS report_status');
  await knex.raw('DROP TYPE IF EXISTS report_type');
}
