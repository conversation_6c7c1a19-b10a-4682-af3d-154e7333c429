import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Create user 2FA table
  await knex.schema.createTable("user_2fa", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("user_id")
      .notNullable()
      .references("id")
      .inTable("users")
      .onDelete("CASCADE");
    table.string("secret", 255).notNullable(); // Base32 encoded secret
    table.boolean("is_enabled").defaultTo(false);
    table.jsonb("backup_codes"); // Hashed backup codes
    table.timestamp("setup_at");
    table.timestamp("enabled_at");
    table.timestamp("disabled_at");
    table.timestamp("last_used_at");
    table.timestamps(true, true);

    table.unique(["user_id"]);
    table.index(["user_id", "is_enabled"]);
  });

  // Create security logs table
  await knex.schema.createTable("security_logs", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("user_id")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.string("event_type", 100).notNullable(); // LOGIN, LOGOUT, 2FA_ENABLED, etc.
    table.string("ip_address", 45); // IPv6 compatible
    table.text("user_agent");
    table.jsonb("details"); // Additional event details
    table
      .enum("severity", ["LOW", "MEDIUM", "HIGH", "CRITICAL"])
      .defaultTo("MEDIUM");
    table.boolean("is_suspicious").defaultTo(false);
    table.timestamp("created_at").defaultTo(knex.fn.now());

    table.index(["user_id", "created_at"]);
    table.index(["event_type", "created_at"]);
    table.index(["is_suspicious", "severity"]);
    table.index(["ip_address", "created_at"]);
  });

  // Create login attempts table (for rate limiting and security)
  await knex.schema.createTable("login_attempts", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table.string("identifier", 255).notNullable(); // Email or IP address
    table.enum("identifier_type", ["EMAIL", "IP_ADDRESS"]).notNullable();
    table.boolean("successful").notNullable();
    table.string("ip_address", 45);
    table.text("user_agent");
    table.string("failure_reason", 100); // INVALID_PASSWORD, INVALID_2FA, etc.
    table.timestamp("attempted_at").defaultTo(knex.fn.now());

    table.index(["identifier", "attempted_at"]);
    table.index(["ip_address", "attempted_at"]);
    table.index(["successful", "attempted_at"]);
  });

  // Create trusted devices table
  await knex.schema.createTable("trusted_devices", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("user_id")
      .notNullable()
      .references("id")
      .inTable("users")
      .onDelete("CASCADE");
    table.string("device_fingerprint", 255).notNullable(); // Hash of device characteristics
    table.string("device_name", 255); // User-friendly device name
    table.string("ip_address", 45);
    table.text("user_agent");
    table.jsonb("device_info"); // Browser, OS, etc.
    table.boolean("is_active").defaultTo(true);
    table.timestamp("trusted_at").defaultTo(knex.fn.now());
    table.timestamp("last_used_at").defaultTo(knex.fn.now());
    table.timestamp("expires_at"); // Optional expiration
    table.timestamps(true, true);

    table.index(["user_id", "is_active"]);
    table.index(["device_fingerprint"]);
    table.index(["expires_at"]);
    table.unique(["user_id", "device_fingerprint"]);
  });

  // Create session management table
  await knex.schema.createTable("user_sessions", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("user_id")
      .notNullable()
      .references("id")
      .inTable("users")
      .onDelete("CASCADE");
    table.string("session_token", 255).notNullable();
    table.string("refresh_token", 255);
    table.string("ip_address", 45);
    table.text("user_agent");
    table.jsonb("device_info");
    table.boolean("is_2fa_verified").defaultTo(false);
    table.boolean("is_active").defaultTo(true);
    table.timestamp("created_at").defaultTo(knex.fn.now());
    table.timestamp("last_activity_at").defaultTo(knex.fn.now());
    table.timestamp("expires_at").notNullable();

    table.index(["user_id", "is_active"]);
    table.index(["session_token"]);
    table.index(["refresh_token"]);
    table.index(["expires_at"]);
    table.unique(["session_token"]);
  });

  // Create password history table (for password policy enforcement)
  await knex.schema.createTable("password_history", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("user_id")
      .notNullable()
      .references("id")
      .inTable("users")
      .onDelete("CASCADE");
    table.string("password_hash", 255).notNullable();
    table.timestamp("created_at").defaultTo(knex.fn.now());

    table.index(["user_id", "created_at"]);
  });

  // Create security settings table
  await knex.schema.createTable("security_settings", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.boolean("require_2fa").defaultTo(false);
    table.boolean("require_2fa_for_admin").defaultTo(true);
    table.integer("password_min_length").defaultTo(8);
    table.boolean("password_require_uppercase").defaultTo(true);
    table.boolean("password_require_lowercase").defaultTo(true);
    table.boolean("password_require_numbers").defaultTo(true);
    table.boolean("password_require_symbols").defaultTo(false);
    table.integer("password_history_count").defaultTo(5); // Remember last N passwords
    table.integer("session_timeout_minutes").defaultTo(480); // 8 hours
    table.integer("max_login_attempts").defaultTo(5);
    table.integer("lockout_duration_minutes").defaultTo(30);
    table.boolean("allow_concurrent_sessions").defaultTo(true);
    table.integer("trusted_device_duration_days").defaultTo(30);
    table.jsonb("ip_whitelist"); // Array of allowed IP addresses/ranges
    table.jsonb("ip_blacklist"); // Array of blocked IP addresses/ranges
    table.timestamps(true, true);

    table.unique(["company_id"]);
  });

  // Create API keys table (for API access)
  await knex.schema.createTable("api_keys", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("user_id")
      .notNullable()
      .references("id")
      .inTable("users")
      .onDelete("CASCADE");
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("name", 255).notNullable(); // User-friendly name
    table.string("key_hash", 255).notNullable(); // Hashed API key
    table.string("key_prefix", 10).notNullable(); // First few characters for identification
    table.jsonb("permissions"); // Array of allowed permissions
    table.jsonb("ip_restrictions"); // Array of allowed IP addresses
    table.boolean("is_active").defaultTo(true);
    table.timestamp("last_used_at");
    table.timestamp("expires_at");
    table.timestamps(true, true);

    table.index(["user_id", "is_active"]);
    table.index(["company_id", "is_active"]);
    table.index(["key_prefix"]);
    table.index(["expires_at"]);
  });

  // Insert default security settings for existing companies
  await knex.raw(`
    INSERT INTO security_settings (company_id, require_2fa, require_2fa_for_admin)
    SELECT 
      id as company_id,
      false as require_2fa,
      true as require_2fa_for_admin
    FROM companies
    WHERE NOT EXISTS (
      SELECT 1 FROM security_settings WHERE security_settings.company_id = companies.id
    );
  `);

  // Create indexes for performance
  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_security_logs_analysis
    ON security_logs(event_type, created_at, is_suspicious);
  `);

  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_login_attempts_rate_limit
    ON login_attempts(identifier, identifier_type, attempted_at);
  `);

  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_user_sessions_cleanup
    ON user_sessions(expires_at, is_active);
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("api_keys");
  await knex.schema.dropTableIfExists("security_settings");
  await knex.schema.dropTableIfExists("password_history");
  await knex.schema.dropTableIfExists("user_sessions");
  await knex.schema.dropTableIfExists("trusted_devices");
  await knex.schema.dropTableIfExists("login_attempts");
  await knex.schema.dropTableIfExists("security_logs");
  await knex.schema.dropTableIfExists("user_2fa");
}
