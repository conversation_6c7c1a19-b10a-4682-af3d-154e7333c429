import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create transaction status enum
  await knex.raw(`
    CREATE TYPE transaction_status AS ENUM (
      'DRAFT',
      'PENDING',
      'APPROVED',
      'POSTED',
      'CANCELLED',
      'REVERSED'
    );
  `);

  // Create transactions table
  await knex.schema.createTable('transactions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.string('transaction_number', 50).notNullable();
    table.date('transaction_date').notNullable();
    table.text('description').notNullable();
    table.text('reference');
    table.specificType('status', 'transaction_status').defaultTo('DRAFT');
    table.decimal('total_amount', 15, 2).notNullable();
    table.string('currency', 3).defaultTo('USD');
    table.decimal('exchange_rate', 10, 6).defaultTo(1);
    table.uuid('created_by').references('id').inTable('users').onDelete('RESTRICT');
    table.uuid('approved_by').references('id').inTable('users').onDelete('RESTRICT');
    table.timestamp('approved_at');
    table.uuid('reversed_by').references('id').inTable('users').onDelete('RESTRICT');
    table.timestamp('reversed_at');
    table.uuid('reversal_transaction_id').references('id').inTable('transactions').onDelete('SET NULL');
    table.jsonb('metadata').defaultTo('{}');
    table.timestamps(true, true);
    
    table.unique(['company_id', 'transaction_number']);
    table.index(['company_id', 'transaction_date']);
    table.index(['status']);
    table.index(['created_by']);
  });

  // Create transaction entries table (for double-entry bookkeeping)
  await knex.schema.createTable('transaction_entries', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('transaction_id').references('id').inTable('transactions').onDelete('CASCADE');
    table.uuid('account_id').references('id').inTable('accounts').onDelete('RESTRICT');
    table.text('description');
    table.decimal('debit_amount', 15, 2).defaultTo(0);
    table.decimal('credit_amount', 15, 2).defaultTo(0);
    table.string('currency', 3).defaultTo('USD');
    table.decimal('exchange_rate', 10, 6).defaultTo(1);
    table.decimal('base_debit_amount', 15, 2).defaultTo(0);
    table.decimal('base_credit_amount', 15, 2).defaultTo(0);
    table.integer('line_number').notNullable();
    table.jsonb('metadata').defaultTo('{}');
    table.timestamps(true, true);
    
    table.index(['transaction_id']);
    table.index(['account_id']);
    table.index(['transaction_id', 'line_number']);
  });

  // Create recurring transactions table
  await knex.schema.createTable('recurring_transactions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.string('name', 255).notNullable();
    table.text('description');
    table.string('frequency', 20).notNullable(); // DAILY, WEEKLY, MONTHLY, QUARTERLY, YEARLY
    table.integer('interval_count').defaultTo(1);
    table.date('start_date').notNullable();
    table.date('end_date');
    table.date('next_run_date').notNullable();
    table.boolean('is_active').defaultTo(true);
    table.jsonb('transaction_template').notNullable();
    table.uuid('created_by').references('id').inTable('users').onDelete('RESTRICT');
    table.timestamps(true, true);
    
    table.index(['company_id']);
    table.index(['next_run_date']);
    table.index(['is_active']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('recurring_transactions');
  await knex.schema.dropTableIfExists('transaction_entries');
  await knex.schema.dropTableIfExists('transactions');
  await knex.raw('DROP TYPE IF EXISTS transaction_status');
}
