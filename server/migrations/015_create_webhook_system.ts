import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Create webhooks table
  await knex.schema.createTable("webhooks", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("name", 255).notNullable();
    table.text("url").notNullable();
    table.jsonb("events").notNullable(); // Array of event types to listen for
    table.string("secret", 255).notNullable(); // Secret for signature verification
    table.boolean("is_active").defaultTo(true);
    table.jsonb("retry_policy"); // Retry configuration
    table.jsonb("headers"); // Custom headers to send
    table
      .uuid("created_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamps(true, true);

    table.index(["company_id", "is_active"]);
    // Create separate indexes - GIN only for JSONB events, regular B-tree for company_id
    table.index(["company_id"]);
    table.index(["events"], "idx_webhooks_events", "GIN");
  });

  // Create webhook deliveries table
  await knex.schema.createTable("webhook_deliveries", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("webhook_id")
      .notNullable()
      .references("id")
      .inTable("webhooks")
      .onDelete("CASCADE");
    table.string("event_type", 100).notNullable();
    table.jsonb("payload").notNullable();
    table.enum("status", ["SUCCESS", "FAILED", "PENDING"]).notNullable();
    table.integer("response_status");
    table.text("response_body");
    table.integer("response_time_ms");
    table.string("signature", 255);
    table.text("error_message");
    table.integer("retry_count").defaultTo(0);
    table.timestamp("delivered_at").defaultTo(knex.fn.now());
    table.timestamp("next_retry_at");

    table.index(["webhook_id", "delivered_at"]);
    table.index(["status", "delivered_at"]);
    table.index(["event_type", "delivered_at"]);
    table.index(["next_retry_at"], "idx_webhook_deliveries_retry");
  });

  // Create webhook events table (for tracking available events)
  await knex.schema.createTable("webhook_events", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table.string("event_type", 100).notNullable().unique();
    table.string("category", 50).notNullable();
    table.string("description", 255).notNullable();
    table.jsonb("payload_schema"); // JSON schema for the event payload
    table.boolean("is_active").defaultTo(true);
    table.timestamps(true, true);

    table.index(["category", "is_active"]);
  });

  // Create webhook subscriptions table (for fine-grained event filtering)
  await knex.schema.createTable("webhook_subscriptions", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("webhook_id")
      .notNullable()
      .references("id")
      .inTable("webhooks")
      .onDelete("CASCADE");
    table.string("event_type", 100).notNullable();
    table.jsonb("filters"); // Optional filters for the event
    table.boolean("is_active").defaultTo(true);
    table.timestamps(true, true);

    table.index(["webhook_id", "event_type"]);
    table.index(["event_type", "is_active"]);
    table.unique(["webhook_id", "event_type"]);
  });

  // Insert default webhook events
  await knex("webhook_events").insert([
    // Transaction Events
    {
      event_type: "transaction.created",
      category: "transactions",
      description: "Triggered when a new transaction is created",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          transaction_id: { type: "string" },
          amount: { type: "number" },
          description: { type: "string" },
          date: { type: "string" },
        },
      }),
    },
    {
      event_type: "transaction.updated",
      category: "transactions",
      description: "Triggered when a transaction is updated",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          transaction_id: { type: "string" },
          changes: { type: "object" },
        },
      }),
    },
    {
      event_type: "transaction.approved",
      category: "transactions",
      description: "Triggered when a transaction is approved",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          transaction_id: { type: "string" },
          approved_by: { type: "string" },
          approved_at: { type: "string" },
        },
      }),
    },
    {
      event_type: "transaction.reversed",
      category: "transactions",
      description: "Triggered when a transaction is reversed",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          transaction_id: { type: "string" },
          reversal_id: { type: "string" },
          reason: { type: "string" },
        },
      }),
    },

    // Invoice Events
    {
      event_type: "invoice.created",
      category: "invoices",
      description: "Triggered when a new invoice is created",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          invoice_id: { type: "string" },
          customer_id: { type: "string" },
          amount: { type: "number" },
          due_date: { type: "string" },
        },
      }),
    },
    {
      event_type: "invoice.sent",
      category: "invoices",
      description: "Triggered when an invoice is sent to customer",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          invoice_id: { type: "string" },
          sent_to: { type: "string" },
          sent_at: { type: "string" },
        },
      }),
    },
    {
      event_type: "invoice.paid",
      category: "invoices",
      description: "Triggered when an invoice is fully paid",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          invoice_id: { type: "string" },
          payment_id: { type: "string" },
          amount_paid: { type: "number" },
        },
      }),
    },
    {
      event_type: "invoice.overdue",
      category: "invoices",
      description: "Triggered when an invoice becomes overdue",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          invoice_id: { type: "string" },
          days_overdue: { type: "number" },
          amount_due: { type: "number" },
        },
      }),
    },

    // Payment Events
    {
      event_type: "payment.received",
      category: "payments",
      description: "Triggered when a payment is received",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          payment_id: { type: "string" },
          amount: { type: "number" },
          method: { type: "string" },
          customer_id: { type: "string" },
        },
      }),
    },
    {
      event_type: "payment.failed",
      category: "payments",
      description: "Triggered when a payment fails",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          payment_id: { type: "string" },
          error_code: { type: "string" },
          error_message: { type: "string" },
        },
      }),
    },
    {
      event_type: "payment.refunded",
      category: "payments",
      description: "Triggered when a payment is refunded",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          payment_id: { type: "string" },
          refund_id: { type: "string" },
          amount: { type: "number" },
          reason: { type: "string" },
        },
      }),
    },

    // Account Events
    {
      event_type: "account.created",
      category: "accounts",
      description: "Triggered when a new account is created",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          account_id: { type: "string" },
          name: { type: "string" },
          type: { type: "string" },
          code: { type: "string" },
        },
      }),
    },

    // Reconciliation Events
    {
      event_type: "reconciliation.completed",
      category: "reconciliation",
      description: "Triggered when bank reconciliation is completed",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          reconciliation_id: { type: "string" },
          account_id: { type: "string" },
          period: { type: "string" },
          discrepancies: { type: "number" },
        },
      }),
    },

    // Budget Events
    {
      event_type: "budget.exceeded",
      category: "budgets",
      description: "Triggered when a budget threshold is exceeded",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          budget_id: { type: "string" },
          account_id: { type: "string" },
          threshold_percentage: { type: "number" },
          actual_amount: { type: "number" },
          budgeted_amount: { type: "number" },
        },
      }),
    },

    // System Events
    {
      event_type: "user.created",
      category: "users",
      description: "Triggered when a new user is created",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          user_id: { type: "string" },
          email: { type: "string" },
          role: { type: "string" },
        },
      }),
    },
    {
      event_type: "company.settings.updated",
      category: "company",
      description: "Triggered when company settings are updated",
      payload_schema: JSON.stringify({
        type: "object",
        properties: {
          company_id: { type: "string" },
          updated_fields: { type: "array" },
        },
      }),
    },
  ]);

  // Create indexes for performance
  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_status_retry
    ON webhook_deliveries(status, next_retry_at) WHERE status = 'FAILED' AND next_retry_at IS NOT NULL;
  `);

  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_webhooks_company_events_active
    ON webhooks(company_id) WHERE is_active = true;
  `);

  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_webhooks_events_gin
    ON webhooks USING GIN(events);
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("webhook_subscriptions");
  await knex.schema.dropTableIfExists("webhook_events");
  await knex.schema.dropTableIfExists("webhook_deliveries");
  await knex.schema.dropTableIfExists("webhooks");
}
