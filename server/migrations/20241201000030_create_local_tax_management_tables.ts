import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  console.log("🇹🇿 Creating Local Tax Management tables (PAYE, SDL)...");

  // Employees table
  const employeesExists = await knex.schema.hasTable("employees");
  if (!employeesExists) {
    await knex.schema.createTable("employees", (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      table.uuid("company_id").notNullable().references("id").inTable("companies");
      
      // Personal Information
      table.string("employee_number", 50).notNullable();
      table.string("first_name", 100).notNullable();
      table.string("middle_name", 100).nullable();
      table.string("last_name", 100).notNullable();
      table.date("date_of_birth").nullable();
      table.enum("gender", ["MALE", "FEMALE", "OTHER"]).nullable();
      table.string("national_id", 50).nullable();
      table.string("passport_number", 50).nullable();
      
      // Contact Information
      table.string("email", 255).nullable();
      table.string("phone", 20).nullable();
      table.string("address", 500).nullable();
      table.string("city", 100).nullable();
      table.string("region", 100).nullable();
      table.string("postal_code", 20).nullable();
      
      // Employment Information
      table.string("job_title", 200).notNullable();
      table.string("department", 200).nullable();
      table.date("hire_date").notNullable();
      table.date("termination_date").nullable();
      table.enum("employment_status", ["ACTIVE", "INACTIVE", "TERMINATED", "SUSPENDED"]).defaultTo("ACTIVE");
      table.enum("employment_type", ["PERMANENT", "CONTRACT", "TEMPORARY", "CASUAL"]).notNullable();
      
      // Salary Information
      table.decimal("basic_salary", 15, 2).notNullable();
      table.enum("salary_frequency", ["MONTHLY", "WEEKLY", "DAILY"]).defaultTo("MONTHLY");
      table.decimal("housing_allowance", 15, 2).defaultTo(0);
      table.decimal("transport_allowance", 15, 2).defaultTo(0);
      table.decimal("other_allowances", 15, 2).defaultTo(0);
      
      // Tax Information
      table.string("tin_number", 20).nullable(); // Tax Identification Number
      table.string("nssf_number", 20).nullable(); // National Social Security Fund
      table.string("wcf_number", 20).nullable(); // Workers Compensation Fund
      table.string("nhif_number", 20).nullable(); // National Health Insurance Fund
      table.boolean("is_tax_exempt").defaultTo(false);
      table.text("tax_exemption_reason").nullable();
      
      // Bank Information
      table.string("bank_name", 200).nullable();
      table.string("bank_account_number", 50).nullable();
      table.string("bank_branch", 200).nullable();
      
      // Audit fields
      table.uuid("created_by").nullable().references("id").inTable("users");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      
      // Indexes
      table.index(["company_id", "employment_status"]);
      table.index(["employee_number"]);
      table.index(["tin_number"]);
      table.index(["hire_date"]);
      table.index("created_at");
      table.unique(["company_id", "employee_number"]);
    });
    console.log("✅ employees table created");
  }

  // PAYE Calculations table
  const payeCalculationsExists = await knex.schema.hasTable("paye_calculations");
  if (!payeCalculationsExists) {
    await knex.schema.createTable("paye_calculations", (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      table.uuid("company_id").notNullable().references("id").inTable("companies");
      table.uuid("employee_id").notNullable().references("id").inTable("employees");
      
      // Payroll Period
      table.integer("tax_year").notNullable();
      table.integer("tax_month").notNullable(); // 1-12
      table.date("payroll_date").notNullable();
      
      // Salary Components
      table.decimal("basic_salary", 15, 2).notNullable();
      table.decimal("housing_allowance", 15, 2).defaultTo(0);
      table.decimal("transport_allowance", 15, 2).defaultTo(0);
      table.decimal("other_allowances", 15, 2).defaultTo(0);
      table.decimal("overtime_pay", 15, 2).defaultTo(0);
      table.decimal("bonus", 15, 2).defaultTo(0);
      table.decimal("gross_salary", 15, 2).notNullable();
      
      // Deductions
      table.decimal("nssf_employee", 15, 2).defaultTo(0); // 10% of basic salary (max 20K TZS)
      table.decimal("nssf_employer", 15, 2).defaultTo(0); // 10% of basic salary (max 20K TZS)
      table.decimal("wcf_contribution", 15, 2).defaultTo(0); // 0.5% of gross salary
      table.decimal("nhif_contribution", 15, 2).defaultTo(0); // Based on salary bands
      table.decimal("other_deductions", 15, 2).defaultTo(0);
      table.decimal("total_deductions", 15, 2).defaultTo(0);
      
      // PAYE Calculation
      table.decimal("taxable_income", 15, 2).notNullable();
      table.decimal("tax_relief", 15, 2).defaultTo(0); // Personal relief: 220,000 TZS annually
      table.decimal("paye_tax", 15, 2).notNullable();
      table.decimal("net_salary", 15, 2).notNullable();
      
      // Tax Bands Applied (Tanzania PAYE rates)
      table.decimal("tax_band_1_amount", 15, 2).defaultTo(0); // 0% on first 270,000 TZS
      table.decimal("tax_band_2_amount", 15, 2).defaultTo(0); // 9% on 270,001 - 520,000 TZS
      table.decimal("tax_band_3_amount", 15, 2).defaultTo(0); // 20% on 520,001 - 760,000 TZS
      table.decimal("tax_band_4_amount", 15, 2).defaultTo(0); // 25% on 760,001 - 1,000,000 TZS
      table.decimal("tax_band_5_amount", 15, 2).defaultTo(0); // 30% on above 1,000,000 TZS
      
      // Status and Submission
      table.enum("calculation_status", ["DRAFT", "CALCULATED", "APPROVED", "PAID"]).defaultTo("DRAFT");
      table.enum("submission_status", ["PENDING", "SUBMITTED", "ACCEPTED", "REJECTED"]).defaultTo("PENDING");
      table.timestamp("submitted_to_tra").nullable();
      table.jsonb("tra_response").nullable();
      table.text("rejection_reason").nullable();
      
      // Audit fields
      table.uuid("calculated_by").nullable().references("id").inTable("users");
      table.uuid("approved_by").nullable().references("id").inTable("users");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      
      // Indexes
      table.index(["company_id", "tax_year", "tax_month"]);
      table.index(["employee_id", "tax_year", "tax_month"]);
      table.index(["payroll_date"]);
      table.index(["calculation_status"]);
      table.index(["submission_status"]);
      table.index("created_at");
      table.unique(["employee_id", "tax_year", "tax_month"]);
    });
    console.log("✅ paye_calculations table created");
  }

  // SDL Calculations table
  const sdlCalculationsExists = await knex.schema.hasTable("sdl_calculations");
  if (!sdlCalculationsExists) {
    await knex.schema.createTable("sdl_calculations", (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      table.uuid("company_id").notNullable().references("id").inTable("companies");
      
      // Calculation Period
      table.integer("tax_year").notNullable();
      table.integer("tax_month").notNullable(); // 1-12
      table.date("calculation_date").notNullable();
      
      // Payroll Summary
      table.integer("total_employees").notNullable();
      table.decimal("total_gross_payroll", 15, 2).notNullable();
      table.decimal("sdl_rate", 5, 2).defaultTo(6.00); // 6% of gross payroll
      table.decimal("sdl_amount", 15, 2).notNullable();
      
      // Employee Breakdown
      table.jsonb("employee_breakdown").nullable(); // Detailed employee contributions
      
      // Exemptions and Adjustments
      table.decimal("exempt_payroll", 15, 2).defaultTo(0);
      table.decimal("adjustments", 15, 2).defaultTo(0);
      table.text("adjustment_reason").nullable();
      table.decimal("final_sdl_amount", 15, 2).notNullable();
      
      // Payment Information
      table.enum("payment_status", ["PENDING", "PAID", "OVERDUE"]).defaultTo("PENDING");
      table.date("due_date").nullable();
      table.date("payment_date").nullable();
      table.string("payment_reference", 100).nullable();
      
      // TRA Submission
      table.enum("submission_status", ["PENDING", "SUBMITTED", "ACCEPTED", "REJECTED"]).defaultTo("PENDING");
      table.timestamp("submitted_to_tra").nullable();
      table.jsonb("tra_response").nullable();
      table.text("rejection_reason").nullable();
      
      // Audit fields
      table.uuid("calculated_by").nullable().references("id").inTable("users");
      table.uuid("approved_by").nullable().references("id").inTable("users");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      
      // Indexes
      table.index(["company_id", "tax_year", "tax_month"]);
      table.index(["calculation_date"]);
      table.index(["payment_status"]);
      table.index(["submission_status"]);
      table.index(["due_date"]);
      table.index("created_at");
      table.unique(["company_id", "tax_year", "tax_month"]);
    });
    console.log("✅ sdl_calculations table created");
  }

  // Local Tax Rates table (for PAYE bands, SDL rates, etc.)
  const localTaxRatesExists = await knex.schema.hasTable("local_tax_rates");
  if (!localTaxRatesExists) {
    await knex.schema.createTable("local_tax_rates", (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      
      // Tax Information
      table.enum("tax_type", ["PAYE", "SDL", "NSSF", "WCF", "NHIF"]).notNullable();
      table.string("tax_band", 100).nullable(); // e.g., "BAND_1", "BAND_2"
      table.decimal("rate", 5, 2).notNullable(); // Percentage
      table.decimal("min_amount", 15, 2).nullable(); // Minimum amount for band
      table.decimal("max_amount", 15, 2).nullable(); // Maximum amount for band
      table.decimal("fixed_amount", 15, 2).nullable(); // Fixed amount (for NHIF)
      table.text("description").nullable();
      
      // Validity Period
      table.date("effective_from").notNullable();
      table.date("effective_to").nullable();
      table.boolean("is_active").defaultTo(true);
      
      // Additional Settings
      table.jsonb("settings").nullable(); // Additional configuration
      
      // Audit fields
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      
      // Indexes
      table.index(["tax_type", "is_active"]);
      table.index(["effective_from", "effective_to"]);
      table.index("created_at");
    });
    console.log("✅ local_tax_rates table created");
  }

  // Insert default Tanzania tax rates
  await knex("local_tax_rates").insert([
    // PAYE Tax Bands (2024 rates)
    {
      tax_type: "PAYE",
      tax_band: "BAND_1",
      rate: 0.00,
      min_amount: 0,
      max_amount: 270000,
      description: "0% on first 270,000 TZS annually",
      effective_from: "2024-01-01"
    },
    {
      tax_type: "PAYE",
      tax_band: "BAND_2", 
      rate: 9.00,
      min_amount: 270001,
      max_amount: 520000,
      description: "9% on 270,001 - 520,000 TZS annually",
      effective_from: "2024-01-01"
    },
    {
      tax_type: "PAYE",
      tax_band: "BAND_3",
      rate: 20.00,
      min_amount: 520001,
      max_amount: 760000,
      description: "20% on 520,001 - 760,000 TZS annually",
      effective_from: "2024-01-01"
    },
    {
      tax_type: "PAYE",
      tax_band: "BAND_4",
      rate: 25.00,
      min_amount: 760001,
      max_amount: 1000000,
      description: "25% on 760,001 - 1,000,000 TZS annually",
      effective_from: "2024-01-01"
    },
    {
      tax_type: "PAYE",
      tax_band: "BAND_5",
      rate: 30.00,
      min_amount: 1000001,
      max_amount: null,
      description: "30% on above 1,000,000 TZS annually",
      effective_from: "2024-01-01"
    },
    
    // SDL Rate
    {
      tax_type: "SDL",
      rate: 6.00,
      description: "Skills Development Levy - 6% of gross payroll",
      effective_from: "2024-01-01"
    },
    
    // NSSF Rates
    {
      tax_type: "NSSF",
      rate: 10.00,
      max_amount: 20000,
      description: "NSSF contribution - 10% of basic salary (max 20,000 TZS monthly)",
      effective_from: "2024-01-01"
    },
    
    // WCF Rate
    {
      tax_type: "WCF",
      rate: 0.50,
      description: "Workers Compensation Fund - 0.5% of gross salary",
      effective_from: "2024-01-01"
    }
  ]);

  // Insert sample employee for testing
  const existingEmployee = await knex("employees").where("employee_number", "EMP-001").first();
  if (!existingEmployee) {
    // Get first company for sample data
    const firstCompany = await knex("companies").first();
    if (firstCompany) {
      await knex("employees").insert({
        company_id: firstCompany.id,
        employee_number: "EMP-001",
        first_name: "John",
        last_name: "Mwalimu",
        email: "<EMAIL>",
        phone: "+************",
        job_title: "Senior Accountant",
        department: "Finance",
        hire_date: "2024-01-01",
        employment_status: "ACTIVE",
        employment_type: "PERMANENT",
        basic_salary: 800000, // 800K TZS monthly
        housing_allowance: 200000,
        transport_allowance: 100000,
        tin_number: "***********",
        nssf_number: "NSSF-123456",
        bank_name: "CRDB Bank",
        bank_account_number: "*************"
      });
      console.log("✅ Sample employee inserted");
    }
  }

  console.log("✅ Local Tax Management tables created successfully");
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("local_tax_rates");
  await knex.schema.dropTableIfExists("sdl_calculations");
  await knex.schema.dropTableIfExists("paye_calculations");
  await knex.schema.dropTableIfExists("employees");
  
  console.log("✅ Local Tax Management tables dropped successfully");
}
