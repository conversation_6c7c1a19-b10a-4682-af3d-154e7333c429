import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Add transaction_id to invoices table to link invoices with their journal entries
  await knex.schema.alterTable('invoices', (table) => {
    table.uuid('transaction_id').references('id').inTable('transactions').onDelete('SET NULL');
    table.index(['transaction_id']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('invoices', (table) => {
    table.dropColumn('transaction_id');
  });
}
