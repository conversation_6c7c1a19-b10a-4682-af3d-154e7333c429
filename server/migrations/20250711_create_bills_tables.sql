-- Bills/Vendor Bills Management Tables
-- These tables extend the existing transactions system for vendor bills

-- Bills table (extends transactions for vendor bills)
CREATE TABLE IF NOT EXISTS bills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    transaction_id UUID NOT NULL REFERENCES transactions(id) ON DELETE CASCADE,
    vendor_id UUID NOT NULL REFERENCES contacts(id) ON DELETE RESTRICT,
    bill_number VARCHAR(50) NOT NULL,
    vendor_invoice_number VARCHAR(100), -- Vendor's invoice number
    bill_date DATE NOT NULL,
    due_date DATE,
    payment_terms VARCHAR(50), -- NET_30, NET_15, etc.
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    exchange_rate DECIMAL(15,8) NOT NULL DEFAULT 1.0,
    
    -- Amounts
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    paid_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    balance_due DECIMAL(15,2) NOT NULL DEFAULT 0,
    
    -- Status and workflow
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' CHECK (status IN (
        'DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'OVERDUE', 'PAID', 'PARTIALLY_PAID', 'CANCELLED'
    )),
    approval_status VARCHAR(20) DEFAULT 'PENDING' CHECK (approval_status IN (
        'PENDING', 'APPROVED', 'REJECTED'
    )),
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    
    -- Purchase Order reference
    purchase_order_id UUID REFERENCES purchase_orders(id),
    
    -- Addresses and details
    billing_address TEXT,
    shipping_address TEXT,
    notes TEXT,
    terms_and_conditions TEXT,
    
    -- Audit fields
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(company_id, bill_number),
    UNIQUE(transaction_id)
);

-- Bill line items
CREATE TABLE IF NOT EXISTS bill_line_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bill_id UUID NOT NULL REFERENCES bills(id) ON DELETE CASCADE,
    line_number INTEGER NOT NULL,
    
    -- Item details
    account_id UUID REFERENCES accounts(id), -- Expense account
    inventory_item_id UUID REFERENCES inventory_items(id),
    item_code VARCHAR(50),
    description TEXT NOT NULL,
    unit_of_measure VARCHAR(20),
    
    -- Quantities and pricing
    quantity DECIMAL(15,4) NOT NULL DEFAULT 1,
    unit_cost DECIMAL(15,2) NOT NULL DEFAULT 0,
    discount_percentage DECIMAL(5,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    line_total DECIMAL(15,2) NOT NULL DEFAULT 0,
    
    -- Tax information
    tax_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_code VARCHAR(20),
    
    -- Purchase order reference
    purchase_order_line_item_id UUID REFERENCES purchase_order_line_items(id),
    
    -- Additional details
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(bill_id, line_number)
);

-- Bill attachments
CREATE TABLE IF NOT EXISTS bill_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bill_id UUID NOT NULL REFERENCES bills(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_type VARCHAR(50),
    file_size INTEGER,
    uploaded_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Bill payments (tracks payments made against bills)
CREATE TABLE IF NOT EXISTS bill_payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bill_id UUID NOT NULL REFERENCES bills(id) ON DELETE CASCADE,
    payment_transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
    payment_date DATE NOT NULL,
    payment_amount DECIMAL(15,2) NOT NULL,
    payment_method VARCHAR(50), -- CASH, CHECK, BANK_TRANSFER, etc.
    payment_reference VARCHAR(100),
    notes TEXT,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Bill approval workflow
CREATE TABLE IF NOT EXISTS bill_approvals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bill_id UUID NOT NULL REFERENCES bills(id) ON DELETE CASCADE,
    approver_id UUID NOT NULL REFERENCES users(id),
    approval_level INTEGER NOT NULL DEFAULT 1,
    status VARCHAR(20) NOT NULL CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED')),
    comments TEXT,
    approved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(bill_id, approver_id, approval_level)
);

-- Bill history (audit trail)
CREATE TABLE IF NOT EXISTS bill_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bill_id UUID NOT NULL REFERENCES bills(id) ON DELETE CASCADE,
    old_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    old_amount DECIMAL(15,2),
    new_amount DECIMAL(15,2),
    notes TEXT,
    changed_by UUID NOT NULL REFERENCES users(id),
    changed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Recurring bills
CREATE TABLE IF NOT EXISTS recurring_bills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    vendor_id UUID NOT NULL REFERENCES contacts(id) ON DELETE RESTRICT,
    template_name VARCHAR(100) NOT NULL,
    
    -- Recurrence settings
    frequency VARCHAR(20) NOT NULL CHECK (frequency IN (
        'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY'
    )),
    interval_count INTEGER NOT NULL DEFAULT 1,
    start_date DATE NOT NULL,
    end_date DATE,
    next_bill_date DATE NOT NULL,
    
    -- Bill template data
    bill_template JSONB NOT NULL, -- Stores bill structure
    
    -- Status
    is_active BOOLEAN NOT NULL DEFAULT true,
    
    -- Audit fields
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(company_id, template_name)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_bills_company_id ON bills(company_id);
CREATE INDEX IF NOT EXISTS idx_bills_vendor_id ON bills(vendor_id);
CREATE INDEX IF NOT EXISTS idx_bills_status ON bills(status);
CREATE INDEX IF NOT EXISTS idx_bills_bill_date ON bills(bill_date);
CREATE INDEX IF NOT EXISTS idx_bills_due_date ON bills(due_date);
CREATE INDEX IF NOT EXISTS idx_bills_purchase_order_id ON bills(purchase_order_id);

CREATE INDEX IF NOT EXISTS idx_bill_line_items_bill_id ON bill_line_items(bill_id);
CREATE INDEX IF NOT EXISTS idx_bill_line_items_account_id ON bill_line_items(account_id);
CREATE INDEX IF NOT EXISTS idx_bill_line_items_inventory_item_id ON bill_line_items(inventory_item_id);

CREATE INDEX IF NOT EXISTS idx_bill_payments_bill_id ON bill_payments(bill_id);
CREATE INDEX IF NOT EXISTS idx_bill_payments_payment_date ON bill_payments(payment_date);

CREATE INDEX IF NOT EXISTS idx_bill_approvals_bill_id ON bill_approvals(bill_id);
CREATE INDEX IF NOT EXISTS idx_bill_approvals_approver_id ON bill_approvals(approver_id);

CREATE INDEX IF NOT EXISTS idx_bill_history_bill_id ON bill_history(bill_id);
CREATE INDEX IF NOT EXISTS idx_bill_history_changed_at ON bill_history(changed_at);

CREATE INDEX IF NOT EXISTS idx_recurring_bills_company_id ON recurring_bills(company_id);
CREATE INDEX IF NOT EXISTS idx_recurring_bills_next_bill_date ON recurring_bills(next_bill_date);

-- Triggers for updated_at
CREATE OR REPLACE FUNCTION update_bills_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_bills_updated_at
    BEFORE UPDATE ON bills
    FOR EACH ROW
    EXECUTE FUNCTION update_bills_updated_at();

CREATE TRIGGER trigger_bill_line_items_updated_at
    BEFORE UPDATE ON bill_line_items
    FOR EACH ROW
    EXECUTE FUNCTION update_bills_updated_at();

CREATE TRIGGER trigger_bill_attachments_updated_at
    BEFORE UPDATE ON bill_attachments
    FOR EACH ROW
    EXECUTE FUNCTION update_bills_updated_at();

CREATE TRIGGER trigger_bill_payments_updated_at
    BEFORE UPDATE ON bill_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_bills_updated_at();

CREATE TRIGGER trigger_bill_approvals_updated_at
    BEFORE UPDATE ON bill_approvals
    FOR EACH ROW
    EXECUTE FUNCTION update_bills_updated_at();

CREATE TRIGGER trigger_recurring_bills_updated_at
    BEFORE UPDATE ON recurring_bills
    FOR EACH ROW
    EXECUTE FUNCTION update_bills_updated_at();

-- Function to update bill totals and balance
CREATE OR REPLACE FUNCTION update_bill_totals(bill_id_param UUID)
RETURNS VOID AS $$
DECLARE
    bill_subtotal DECIMAL(15,2);
    bill_tax_amount DECIMAL(15,2);
    bill_discount_amount DECIMAL(15,2);
    bill_total_amount DECIMAL(15,2);
    bill_paid_amount DECIMAL(15,2);
    bill_balance_due DECIMAL(15,2);
BEGIN
    -- Calculate totals from line items
    SELECT 
        COALESCE(SUM(line_total), 0),
        COALESCE(SUM(tax_amount), 0),
        COALESCE(SUM(discount_amount), 0)
    INTO bill_subtotal, bill_tax_amount, bill_discount_amount
    FROM bill_line_items
    WHERE bill_id = bill_id_param;
    
    -- Calculate total amount
    bill_total_amount := bill_subtotal + bill_tax_amount - bill_discount_amount;
    
    -- Calculate paid amount
    SELECT COALESCE(SUM(payment_amount), 0)
    INTO bill_paid_amount
    FROM bill_payments
    WHERE bill_id = bill_id_param;
    
    -- Calculate balance due
    bill_balance_due := bill_total_amount - bill_paid_amount;
    
    -- Update bill totals
    UPDATE bills SET
        subtotal = bill_subtotal,
        tax_amount = bill_tax_amount,
        discount_amount = bill_discount_amount,
        total_amount = bill_total_amount,
        paid_amount = bill_paid_amount,
        balance_due = bill_balance_due,
        updated_at = NOW()
    WHERE id = bill_id_param;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update bill totals when line items change
CREATE OR REPLACE FUNCTION trigger_update_bill_totals()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        PERFORM update_bill_totals(OLD.bill_id);
        RETURN OLD;
    ELSE
        PERFORM update_bill_totals(NEW.bill_id);
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_bill_line_items_totals
    AFTER INSERT OR UPDATE OR DELETE ON bill_line_items
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_bill_totals();

CREATE TRIGGER trigger_bill_payments_totals
    AFTER INSERT OR UPDATE OR DELETE ON bill_payments
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_bill_totals();
