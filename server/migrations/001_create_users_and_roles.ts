import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create roles table
  await knex.schema.createTable('roles', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('name', 50).notNullable().unique();
    table.string('description', 255);
    table.jsonb('permissions').notNullable().defaultTo('[]');
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);
  });

  // Create users table
  await knex.schema.createTable('users', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('email', 255).notNullable().unique();
    table.string('password_hash', 255).notNullable();
    table.string('first_name', 100).notNullable();
    table.string('last_name', 100).notNullable();
    table.string('phone', 20);
    table.uuid('role_id').references('id').inTable('roles').onDelete('RESTRICT');
    table.boolean('is_active').defaultTo(true);
    table.boolean('email_verified').defaultTo(false);
    table.timestamp('last_login_at');
    table.string('reset_token', 255);
    table.timestamp('reset_token_expires_at');
    table.timestamps(true, true);
    
    table.index(['email']);
    table.index(['role_id']);
  });

  // Create companies table (for multi-entity support)
  await knex.schema.createTable('companies', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('name', 255).notNullable();
    table.string('legal_name', 255);
    table.string('tax_id', 50);
    table.string('registration_number', 50);
    table.text('address');
    table.string('city', 100);
    table.string('state', 100);
    table.string('postal_code', 20);
    table.string('country', 100);
    table.string('phone', 20);
    table.string('email', 255);
    table.string('website', 255);
    table.string('base_currency', 3).defaultTo('USD');
    table.jsonb('settings').defaultTo('{}');
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);
  });

  // Create user_companies junction table
  await knex.schema.createTable('user_companies', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.uuid('role_id').references('id').inTable('roles').onDelete('RESTRICT');
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);
    
    table.unique(['user_id', 'company_id']);
    table.index(['user_id']);
    table.index(['company_id']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('user_companies');
  await knex.schema.dropTableIfExists('companies');
  await knex.schema.dropTableIfExists('users');
  await knex.schema.dropTableIfExists('roles');
}
