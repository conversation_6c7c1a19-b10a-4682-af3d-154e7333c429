import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  console.log("🇹🇿 Creating EFD Compliance tables...");

  // EFD Devices table
  const efdDevicesExists = await knex.schema.hasTable("efd_devices");
  if (!efdDevicesExists) {
    await knex.schema.createTable("efd_devices", (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      table.uuid("company_id").notNullable().references("id").inTable("companies");
      
      // Device Information
      table.string("device_serial", 50).notNullable().unique();
      table.string("device_model", 100).notNullable();
      table.string("manufacturer", 100).notNullable();
      table.string("firmware_version", 50).nullable();
      
      // TRA Registration
      table.string("tra_device_id", 50).notNullable().unique();
      table.string("tra_certificate_number", 100).nullable();
      table.date("registration_date").notNullable();
      table.date("certificate_expiry").nullable();
      
      // Device Status
      table.enum("status", ["ACTIVE", "INACTIVE", "SUSPENDED", "EXPIRED"]).defaultTo("ACTIVE");
      table.enum("connection_type", ["USB", "ETHERNET", "WIFI", "BLUETOOTH"]).notNullable();
      table.string("ip_address", 45).nullable(); // IPv4 or IPv6
      table.integer("port").nullable();
      
      // Location Information
      table.string("location_name", 200).nullable();
      table.string("physical_address", 500).nullable();
      table.decimal("latitude", 10, 8).nullable();
      table.decimal("longitude", 11, 8).nullable();
      
      // Configuration
      table.jsonb("device_settings").nullable();
      table.timestamp("last_sync").nullable();
      table.timestamp("last_heartbeat").nullable();
      
      // Audit fields
      table.uuid("created_by").nullable().references("id").inTable("users");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      
      // Indexes
      table.index(["company_id", "status"]);
      table.index(["tra_device_id"]);
      table.index(["device_serial"]);
      table.index("created_at");
    });
    console.log("✅ efd_devices table created");
  }

  // EFD Transactions table
  const efdTransactionsExists = await knex.schema.hasTable("efd_transactions");
  if (!efdTransactionsExists) {
    await knex.schema.createTable("efd_transactions", (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      table.uuid("company_id").notNullable().references("id").inTable("companies");
      table.uuid("efd_device_id").notNullable().references("id").inTable("efd_devices");
      table.uuid("transaction_id").nullable().references("id").inTable("transactions");
      table.uuid("invoice_id").nullable().references("id").inTable("invoices");
      
      // EFD Transaction Details
      table.string("efd_receipt_number", 50).notNullable();
      table.string("efd_internal_number", 50).nullable();
      table.string("fiscal_code", 100).notNullable(); // Unique fiscal identifier
      table.string("qr_code", 500).nullable(); // QR code for verification
      
      // Transaction Information
      table.enum("transaction_type", [
        "SALE", 
        "REFUND", 
        "VOID", 
        "TRAINING", 
        "COPY"
      ]).notNullable();
      
      table.decimal("gross_amount", 15, 2).notNullable();
      table.decimal("vat_amount", 15, 2).defaultTo(0);
      table.decimal("net_amount", 15, 2).notNullable();
      table.decimal("discount_amount", 15, 2).defaultTo(0);
      
      // Payment Information
      table.enum("payment_method", [
        "CASH",
        "CARD", 
        "MOBILE_MONEY",
        "BANK_TRANSFER",
        "CREDIT",
        "MIXED"
      ]).notNullable();
      
      table.decimal("cash_amount", 15, 2).defaultTo(0);
      table.decimal("card_amount", 15, 2).defaultTo(0);
      table.decimal("mobile_money_amount", 15, 2).defaultTo(0);
      table.decimal("other_amount", 15, 2).defaultTo(0);
      
      // Customer Information
      table.string("customer_name", 200).nullable();
      table.string("customer_tin", 20).nullable(); // Tax Identification Number
      table.string("customer_vrn", 20).nullable(); // VAT Registration Number
      table.string("customer_mobile", 20).nullable();
      
      // EFD Compliance
      table.timestamp("efd_timestamp").notNullable();
      table.enum("submission_status", ["PENDING", "SUBMITTED", "ACCEPTED", "REJECTED"]).defaultTo("PENDING");
      table.timestamp("submitted_to_tra").nullable();
      table.jsonb("tra_response").nullable();
      table.text("rejection_reason").nullable();
      
      // Z-Report Information
      table.string("z_report_number", 50).nullable();
      table.date("z_report_date").nullable();
      
      // Audit fields
      table.uuid("created_by").nullable().references("id").inTable("users");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      
      // Indexes
      table.index(["company_id", "efd_timestamp"]);
      table.index(["efd_device_id", "efd_timestamp"]);
      table.index(["efd_receipt_number"]);
      table.index(["fiscal_code"]);
      table.index(["submission_status"]);
      table.index(["z_report_date"]);
      table.index("created_at");
      table.unique(["efd_device_id", "efd_receipt_number"]);
    });
    console.log("✅ efd_transactions table created");
  }

  // EFD Z-Reports table
  const efdZReportsExists = await knex.schema.hasTable("efd_z_reports");
  if (!efdZReportsExists) {
    await knex.schema.createTable("efd_z_reports", (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      table.uuid("company_id").notNullable().references("id").inTable("companies");
      table.uuid("efd_device_id").notNullable().references("id").inTable("efd_devices");
      
      // Z-Report Information
      table.string("z_report_number", 50).notNullable();
      table.date("report_date").notNullable();
      table.timestamp("report_timestamp").notNullable();
      table.string("fiscal_day_identifier", 100).notNullable();
      
      // Daily Totals
      table.integer("total_transactions").defaultTo(0);
      table.decimal("total_gross_sales", 15, 2).defaultTo(0);
      table.decimal("total_vat_amount", 15, 2).defaultTo(0);
      table.decimal("total_net_sales", 15, 2).defaultTo(0);
      table.decimal("total_discounts", 15, 2).defaultTo(0);
      table.decimal("total_refunds", 15, 2).defaultTo(0);
      
      // Payment Method Totals
      table.decimal("cash_total", 15, 2).defaultTo(0);
      table.decimal("card_total", 15, 2).defaultTo(0);
      table.decimal("mobile_money_total", 15, 2).defaultTo(0);
      table.decimal("other_payment_total", 15, 2).defaultTo(0);
      
      // Transaction Counters
      table.integer("sales_count").defaultTo(0);
      table.integer("refund_count").defaultTo(0);
      table.integer("void_count").defaultTo(0);
      table.integer("training_count").defaultTo(0);
      
      // EFD Status
      table.enum("report_status", ["GENERATED", "SUBMITTED", "ACCEPTED", "REJECTED"]).defaultTo("GENERATED");
      table.timestamp("submitted_to_tra").nullable();
      table.jsonb("tra_response").nullable();
      table.text("rejection_reason").nullable();
      
      // Report Data
      table.jsonb("detailed_breakdown").nullable(); // Detailed transaction breakdown
      table.text("efd_signature").nullable(); // Digital signature from EFD
      
      // Audit fields
      table.uuid("created_by").nullable().references("id").inTable("users");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      
      // Indexes
      table.index(["company_id", "report_date"]);
      table.index(["efd_device_id", "report_date"]);
      table.index(["z_report_number"]);
      table.index(["report_status"]);
      table.index("created_at");
      table.unique(["efd_device_id", "z_report_number"]);
      table.unique(["efd_device_id", "report_date"]);
    });
    console.log("✅ efd_z_reports table created");
  }

  // EFD API Integration Log table
  const efdApiLogExists = await knex.schema.hasTable("efd_api_integration_log");
  if (!efdApiLogExists) {
    await knex.schema.createTable("efd_api_integration_log", (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      table.uuid("company_id").nullable().references("id").inTable("companies");
      table.uuid("efd_device_id").nullable().references("id").inTable("efd_devices");
      
      // API Call Information
      table.string("endpoint", 200).notNullable();
      table.enum("method", ["GET", "POST", "PUT", "DELETE"]).notNullable();
      table.jsonb("request_payload").nullable();
      table.jsonb("response_payload").nullable();
      table.integer("response_status").nullable();
      table.text("error_message").nullable();
      table.integer("response_time_ms").nullable();
      table.string("correlation_id", 100).nullable();
      
      // Operation Type
      table.enum("operation_type", [
        "DEVICE_REGISTRATION",
        "TRANSACTION_SUBMISSION",
        "Z_REPORT_SUBMISSION",
        "STATUS_CHECK",
        "HEARTBEAT",
        "CONFIGURATION_UPDATE",
        "CERTIFICATE_RENEWAL",
        "DEVICE_SYNC"
      ]).notNullable();
      
      // TRA Integration
      table.string("tra_reference", 100).nullable();
      table.boolean("requires_retry").defaultTo(false);
      table.integer("retry_count").defaultTo(0);
      table.timestamp("next_retry").nullable();
      
      // Audit fields
      table.uuid("initiated_by").nullable().references("id").inTable("users");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      
      // Indexes
      table.index(["company_id", "created_at"]);
      table.index(["efd_device_id", "created_at"]);
      table.index(["operation_type", "created_at"]);
      table.index(["response_status", "created_at"]);
      table.index(["requires_retry", "next_retry"]);
      table.index("correlation_id");
    });
    console.log("✅ efd_api_integration_log table created");
  }

  // Insert sample EFD device for testing
  const existingDevice = await knex("efd_devices").where("device_serial", "EFD-TEST-001").first();
  if (!existingDevice) {
    // Get first company for sample data
    const firstCompany = await knex("companies").first();
    if (firstCompany) {
      await knex("efd_devices").insert({
        company_id: firstCompany.id,
        device_serial: "EFD-TEST-001",
        device_model: "TREMOL FP-2000",
        manufacturer: "TREMOL",
        firmware_version: "1.2.3",
        tra_device_id: "TRA-EFD-001",
        tra_certificate_number: "CERT-2024-001",
        registration_date: "2024-01-01",
        certificate_expiry: "2025-01-01",
        status: "ACTIVE",
        connection_type: "USB",
        location_name: "Main Store",
        physical_address: "123 Main Street, Dar es Salaam",
        device_settings: JSON.stringify({
          auto_cut: true,
          print_logo: true,
          receipt_footer: "Thank you for your business!",
          vat_inclusive: true
        })
      });
      console.log("✅ Sample EFD device inserted");
    }
  }

  console.log("✅ EFD Compliance tables created successfully");
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("efd_api_integration_log");
  await knex.schema.dropTableIfExists("efd_z_reports");
  await knex.schema.dropTableIfExists("efd_transactions");
  await knex.schema.dropTableIfExists("efd_devices");
  
  console.log("✅ EFD Compliance tables dropped successfully");
}
