import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Field-level permissions
  await knex.schema.createTable('field_permissions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('role_id').notNullable().references('id').inTable('roles').onDelete('CASCADE');
    table.string('resource').notNullable(); // table/entity name
    table.string('field').notNullable(); // field name
    table.enum('permission', ['READ', 'WRITE', 'NONE']).notNullable();
    table.jsonb('conditions'); // conditional access rules
    table.timestamps(true, true);
    
    table.unique(['role_id', 'resource', 'field']);
    table.index(['role_id']);
    table.index(['resource']);
  });

  // Time-based access controls
  await knex.schema.createTable('time_based_access', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.uuid('role_id').references('id').inTable('roles').onDelete('CASCADE');
    table.string('resource').notNullable();
    table.string('action').notNullable();
    table.string('start_time').notNullable(); // HH:MM format
    table.string('end_time').notNullable(); // HH:MM format
    table.jsonb('days_of_week').notNullable(); // [0,1,2,3,4,5,6]
    table.string('timezone').notNullable();
    table.boolean('is_active').defaultTo(true);
    table.timestamp('expires_at');
    table.uuid('created_by').notNullable().references('id').inTable('users').onDelete('RESTRICT');
    table.timestamps(true, true);
    
    table.index(['user_id']);
    table.index(['role_id']);
    table.index(['resource', 'action']);
    table.index(['is_active']);
  });

  // IP restrictions
  await knex.schema.createTable('ip_restrictions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.uuid('role_id').references('id').inTable('roles').onDelete('CASCADE');
    table.uuid('company_id').notNullable().references('id').inTable('companies').onDelete('CASCADE');
    table.string('ip_address'); // Single IP
    table.string('ip_range'); // CIDR notation
    table.boolean('is_whitelist').defaultTo(true); // true = allow, false = deny
    table.text('description');
    table.boolean('is_active').defaultTo(true);
    table.uuid('created_by').notNullable().references('id').inTable('users').onDelete('RESTRICT');
    table.timestamps(true, true);
    
    table.index(['user_id']);
    table.index(['role_id']);
    table.index(['company_id']);
    table.index(['is_active']);
  });

  // Access audit logs
  await knex.schema.createTable('access_audit_logs', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.uuid('company_id').notNullable().references('id').inTable('companies').onDelete('CASCADE');
    table.string('resource').notNullable();
    table.string('resource_id');
    table.string('action').notNullable();
    table.string('field');
    table.jsonb('old_value');
    table.jsonb('new_value');
    table.string('ip_address').notNullable();
    table.text('user_agent');
    table.string('session_id');
    table.boolean('success').defaultTo(true);
    table.text('failure_reason');
    table.jsonb('metadata');
    table.timestamp('timestamp').defaultTo(knex.fn.now());
    
    table.index(['user_id']);
    table.index(['company_id']);
    table.index(['resource']);
    table.index(['action']);
    table.index(['timestamp']);
    table.index(['success']);
  });

  // Session security
  await knex.schema.createTable('session_security', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.string('session_token').notNullable().unique();
    table.string('ip_address').notNullable();
    table.text('user_agent');
    table.jsonb('location'); // country, city, region
    table.boolean('is_active').defaultTo(true);
    table.timestamp('last_activity').defaultTo(knex.fn.now());
    table.timestamp('expires_at').notNullable();
    table.timestamps(true, true);
    
    table.index(['user_id']);
    table.index(['session_token']);
    table.index(['is_active']);
    table.index(['expires_at']);
  });

  // Security policies
  await knex.schema.createTable('security_policies', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').notNullable().references('id').inTable('companies').onDelete('CASCADE');
    table.string('name').notNullable();
    table.text('description');
    table.jsonb('settings').notNullable();
    table.boolean('is_active').defaultTo(true);
    table.uuid('created_by').notNullable().references('id').inTable('users').onDelete('RESTRICT');
    table.timestamps(true, true);
    
    table.index(['company_id']);
    table.index(['is_active']);
  });

  // Role hierarchy
  await knex.schema.createTable('role_hierarchy', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('parent_role_id').notNullable().references('id').inTable('roles').onDelete('CASCADE');
    table.uuid('child_role_id').notNullable().references('id').inTable('roles').onDelete('CASCADE');
    table.boolean('inherit_permissions').defaultTo(true);
    table.timestamps(true, true);
    
    table.unique(['parent_role_id', 'child_role_id']);
    table.index(['parent_role_id']);
    table.index(['child_role_id']);
  });

  // Conditional permissions
  await knex.schema.createTable('conditional_permissions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('role_id').notNullable().references('id').inTable('roles').onDelete('CASCADE');
    table.string('resource').notNullable();
    table.string('action').notNullable();
    table.jsonb('conditions').notNullable();
    table.enum('permission', ['ALLOW', 'DENY']).notNullable();
    table.integer('priority').defaultTo(0);
    table.timestamps(true, true);
    
    table.index(['role_id']);
    table.index(['resource', 'action']);
    table.index(['priority']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('conditional_permissions');
  await knex.schema.dropTableIfExists('role_hierarchy');
  await knex.schema.dropTableIfExists('security_policies');
  await knex.schema.dropTableIfExists('session_security');
  await knex.schema.dropTableIfExists('access_audit_logs');
  await knex.schema.dropTableIfExists('ip_restrictions');
  await knex.schema.dropTableIfExists('time_based_access');
  await knex.schema.dropTableIfExists('field_permissions');
}
