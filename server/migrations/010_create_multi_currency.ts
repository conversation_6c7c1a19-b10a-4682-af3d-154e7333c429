import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Create exchange rate source enum
  await knex.raw(`
    CREATE TYPE exchange_rate_source AS ENUM ('MANUAL', 'API', 'BANK', 'CENTRAL_BANK');
  `);

  // Create currencies table
  await knex.schema.createTable("currencies", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("code", 3).notNullable(); // ISO 4217 currency code (USD, EUR, TZS, etc.)
    table.string("name", 100).notNullable(); // Full currency name
    table.string("symbol", 10).notNullable(); // Currency symbol ($, €, TSh, etc.)
    table.integer("decimal_places").defaultTo(2); // Number of decimal places
    table.boolean("is_active").defaultTo(true);
    table.boolean("is_base_currency").defaultTo(false); // Company's base currency
    table
      .uuid("created_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamps(true, true);

    table.index(["company_id", "code"]);
    table.index(["company_id", "is_active"]);
    table.unique(["company_id", "code"]);
  });

  // Create exchange rates table
  await knex.schema.createTable("exchange_rates", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("from_currency", 3).notNullable();
    table.string("to_currency", 3).notNullable();
    table.decimal("rate", 15, 8).notNullable(); // High precision for exchange rates
    table.date("effective_date").notNullable();
    table.specificType("source", "exchange_rate_source").defaultTo("MANUAL");
    table.string("provider", 50); // API provider name if applicable
    table
      .uuid("created_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamps(true, true);

    table.index([
      "company_id",
      "from_currency",
      "to_currency",
      "effective_date",
    ]);
    table.index(["effective_date"]);
    table
      .foreign(["company_id", "from_currency"])
      .references(["company_id", "code"])
      .inTable("currencies");
    table
      .foreign(["company_id", "to_currency"])
      .references(["company_id", "code"])
      .inTable("currencies");
  });

  // Create currency conversion history table (for audit trail)
  await knex.schema.createTable("currency_conversions", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("entity_type", 50).notNullable(); // TRANSACTION, INVOICE, PAYMENT, etc.
    table.uuid("entity_id").notNullable();
    table.string("from_currency", 3).notNullable();
    table.string("to_currency", 3).notNullable();
    table.decimal("original_amount", 15, 2).notNullable();
    table.decimal("converted_amount", 15, 2).notNullable();
    table.decimal("exchange_rate", 15, 8).notNullable();
    table.date("conversion_date").notNullable();
    table
      .uuid("exchange_rate_id")
      .references("id")
      .inTable("exchange_rates")
      .onDelete("SET NULL");
    table.timestamps(true, true);

    table.index(["company_id", "entity_type", "entity_id"]);
    table.index(["conversion_date"]);
  });

  // Create currency revaluation table (for period-end adjustments)
  await knex.schema.createTable("currency_revaluations", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table
      .uuid("account_id")
      .notNullable()
      .references("id")
      .inTable("accounts")
      .onDelete("CASCADE");
    table.string("currency", 3).notNullable();
    table.date("revaluation_date").notNullable();
    table.decimal("original_balance", 15, 2).notNullable();
    table.decimal("revalued_balance", 15, 2).notNullable();
    table.decimal("revaluation_gain_loss", 15, 2).notNullable();
    table.decimal("exchange_rate", 15, 8).notNullable();
    table
      .uuid("journal_entry_id")
      .references("id")
      .inTable("transactions")
      .onDelete("SET NULL");
    table
      .uuid("created_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamps(true, true);

    table.index(["company_id", "revaluation_date"]);
    table.index(["account_id", "currency"]);
  });

  // Create currency settings table
  await knex.schema.createTable("currency_settings", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("base_currency", 3).notNullable();
    table.boolean("auto_update_rates").defaultTo(false);
    table.string("rate_provider", 50); // API provider for automatic updates
    table.jsonb("rate_provider_config").defaultTo("{}");
    table.boolean("enable_revaluation").defaultTo(true);
    table.string("revaluation_frequency", 20).defaultTo("MONTHLY"); // DAILY, WEEKLY, MONTHLY, QUARTERLY
    table
      .uuid("unrealized_gain_account_id")
      .references("id")
      .inTable("accounts")
      .onDelete("SET NULL");
    table
      .uuid("unrealized_loss_account_id")
      .references("id")
      .inTable("accounts")
      .onDelete("SET NULL");
    table
      .uuid("realized_gain_account_id")
      .references("id")
      .inTable("accounts")
      .onDelete("SET NULL");
    table
      .uuid("realized_loss_account_id")
      .references("id")
      .inTable("accounts")
      .onDelete("SET NULL");
    table.timestamps(true, true);

    table.unique(["company_id"]);
    table
      .foreign(["company_id", "base_currency"])
      .references(["company_id", "code"])
      .inTable("currencies");
  });

  // Insert default currencies for companies
  await knex.raw(`
    INSERT INTO currencies (company_id, code, name, symbol, decimal_places, is_active, is_base_currency)
    SELECT 
      id as company_id,
      'USD' as code,
      'US Dollar' as name,
      '$' as symbol,
      2 as decimal_places,
      true as is_active,
      true as is_base_currency
    FROM companies
    WHERE NOT EXISTS (
      SELECT 1 FROM currencies WHERE currencies.company_id = companies.id
    );
  `);

  // Insert common currencies
  await knex.raw(`
    INSERT INTO currencies (company_id, code, name, symbol, decimal_places, is_active, is_base_currency)
    SELECT 
      c.id as company_id,
      curr.code,
      curr.name,
      curr.symbol,
      curr.decimal_places,
      true as is_active,
      false as is_base_currency
    FROM companies c
    CROSS JOIN (
      VALUES 
        ('EUR', 'Euro', '€', 2),
        ('GBP', 'British Pound', '£', 2),
        ('TZS', 'Tanzanian Shilling', 'TSh', 0),
        ('KES', 'Kenyan Shilling', 'KSh', 2),
        ('UGX', 'Ugandan Shilling', 'USh', 0),
        ('RWF', 'Rwandan Franc', 'RF', 0),
        ('ZAR', 'South African Rand', 'R', 2),
        ('JPY', 'Japanese Yen', '¥', 0),
        ('CNY', 'Chinese Yuan', '¥', 2),
        ('INR', 'Indian Rupee', '₹', 2)
    ) AS curr(code, name, symbol, decimal_places)
    WHERE NOT EXISTS (
      SELECT 1 FROM currencies 
      WHERE currencies.company_id = c.id 
      AND currencies.code = curr.code
    );
  `);

  // Insert default currency settings
  await knex.raw(`
    INSERT INTO currency_settings (company_id, base_currency, auto_update_rates, enable_revaluation)
    SELECT 
      id as company_id,
      'USD' as base_currency,
      false as auto_update_rates,
      true as enable_revaluation
    FROM companies
    WHERE NOT EXISTS (
      SELECT 1 FROM currency_settings WHERE currency_settings.company_id = companies.id
    );
  `);

  // Add indexes for performance
  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_exchange_rates_lookup
    ON exchange_rates(company_id, from_currency, to_currency, effective_date DESC);
  `);

  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_currency_conversions_entity
    ON currency_conversions(entity_type, entity_id, conversion_date DESC);
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("currency_settings");
  await knex.schema.dropTableIfExists("currency_revaluations");
  await knex.schema.dropTableIfExists("currency_conversions");
  await knex.schema.dropTableIfExists("exchange_rates");
  await knex.schema.dropTableIfExists("currencies");

  await knex.raw("DROP TYPE IF EXISTS exchange_rate_source");
}
