import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Create bank connections table
  await knex.schema.createTable("bank_connections", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("bank_code", 20).notNullable(); // NBC, CRDB, NMB, etc.
    table.string("bank_name", 255).notNullable();
    table.string("account_number", 50).notNullable();
    table.string("account_name", 255).notNullable();
    table
      .enum("account_type", ["CURRENT", "SAVINGS", "FIXED_DEPOSIT", "LOAN"])
      .defaultTo("CURRENT");
    table.string("currency", 10).defaultTo("TZS");
    table.text("credentials"); // Encrypted bank API credentials
    table.boolean("is_active").defaultTo(true);
    table
      .enum("connection_status", [
        "CONNECTED",
        "FAILED",
        "PENDING",
        "DISCONNECTED",
      ])
      .defaultTo("PENDING");
    table
      .enum("sync_status", ["SUCCESS", "FAILED", "IN_PROGRESS"])
      .defaultTo("SUCCESS");
    table.timestamp("last_sync_at");
    table.timestamp("last_test_at");
    table.jsonb("sync_settings"); // Auto-sync frequency, filters, etc.
    table
      .uuid("created_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamps(true, true);

    table.index(["company_id", "is_active"]);
    table.index(["bank_code", "account_number"]);
    table.index(["connection_status"]);
    table.unique(["company_id", "bank_code", "account_number"]);
  });

  // Create bank transactions table
  await knex.schema.createTable("bank_transactions", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("connection_id")
      .notNullable()
      .references("id")
      .inTable("bank_connections")
      .onDelete("CASCADE");
    table.string("bank_transaction_id", 255).notNullable(); // Bank's internal transaction ID
    table.date("transaction_date").notNullable();
    table.text("description").notNullable();
    table.decimal("amount", 15, 4).notNullable(); // Positive for credits, negative for debits
    table.enum("transaction_type", ["CREDIT", "DEBIT"]).notNullable();
    table.decimal("balance", 15, 4); // Account balance after transaction
    table.string("reference", 255); // Bank reference number
    table.string("category", 100); // Transaction category from bank
    table.jsonb("metadata"); // Additional bank-specific data
    table
      .enum("reconciliation_status", ["UNMATCHED", "MATCHED", "IGNORED"])
      .defaultTo("UNMATCHED");
    table
      .uuid("matched_transaction_id")
      .references("id")
      .inTable("transactions")
      .onDelete("SET NULL");
    table.timestamp("synced_at").defaultTo(knex.fn.now());
    table
      .uuid("synced_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");

    table.index(["connection_id", "transaction_date"]);
    table.index(["reconciliation_status"]);
    table.index(["bank_transaction_id"]);
    table.index(["transaction_date", "amount"]);
    table.unique(["connection_id", "bank_transaction_id"]);
  });

  // Create bank integration reconciliation items table
  await knex.schema.createTable(
    "bank_integration_reconciliation_items",
    (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      table
        .uuid("bank_transaction_id")
        .references("id")
        .inTable("bank_transactions")
        .onDelete("CASCADE");
      table
        .uuid("accounting_transaction_id")
        .references("id")
        .inTable("transactions")
        .onDelete("CASCADE");
      table
        .enum("reconciliation_type", [
          "AUTO_MATCHED",
          "MANUAL_MATCHED",
          "PARTIAL_MATCHED",
        ])
        .notNullable();
      table.decimal("matched_amount", 15, 4).notNullable();
      table.decimal("variance_amount", 15, 4).defaultTo(0); // Difference if partial match
      table.text("notes"); // Reconciliation notes
      table.timestamp("reconciled_at").defaultTo(knex.fn.now());
      table
        .uuid("reconciled_by")
        .references("id")
        .inTable("users")
        .onDelete("SET NULL");
      table.timestamps(true, true);

      table.index(["bank_transaction_id"]);
      table.index(["accounting_transaction_id"]);
      table.index(["reconciliation_type"]);
      table.index(["reconciled_at"]);
    }
  );

  // Create bank sync logs table
  await knex.schema.createTable("bank_sync_logs", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("connection_id")
      .notNullable()
      .references("id")
      .inTable("bank_connections")
      .onDelete("CASCADE");
    table.date("sync_date").notNullable();
    table.date("period_start").notNullable();
    table.date("period_end").notNullable();
    table.enum("sync_type", ["MANUAL", "SCHEDULED", "REAL_TIME"]).notNullable();
    table.enum("status", ["SUCCESS", "FAILED", "PARTIAL"]).notNullable();
    table.integer("transactions_fetched").defaultTo(0);
    table.integer("transactions_synced").defaultTo(0);
    table.integer("transactions_reconciled").defaultTo(0);
    table.text("error_message");
    table.jsonb("sync_details"); // Detailed sync information
    table.timestamp("started_at").notNullable();
    table.timestamp("completed_at");
    table
      .uuid("initiated_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");

    table.index(["connection_id", "sync_date"]);
    table.index(["status", "started_at"]);
    table.index(["sync_type", "started_at"]);
  });

  // Create supported banks table
  await knex.schema.createTable("supported_banks", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table.string("bank_code", 20).notNullable().unique();
    table.string("bank_name", 255).notNullable();
    table.string("country", 10).notNullable(); // TZ, KE, UG, etc.
    table
      .enum("bank_type", [
        "COMMERCIAL",
        "CENTRAL",
        "MICROFINANCE",
        "DEVELOPMENT",
      ])
      .notNullable();
    table
      .enum("api_support", ["FULL", "PARTIAL", "LIMITED", "NONE"])
      .defaultTo("NONE");
    table.jsonb("supported_features"); // Array of supported features
    table.jsonb("auth_methods"); // Array of supported authentication methods
    table.jsonb("api_config"); // API endpoints and configuration
    table.text("description");
    table.string("website", 255);
    table.boolean("is_active").defaultTo(true);
    table.timestamps(true, true);

    table.index(["country", "is_active"]);
    table.index(["api_support"]);
    table.index(["bank_type"]);
  });

  // Create bank API credentials template table
  await knex.schema.createTable("bank_api_templates", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table.string("bank_code", 20).notNullable();
    table.string("template_name", 255).notNullable();
    table.jsonb("credential_fields"); // Required credential fields
    table.jsonb("api_endpoints"); // API endpoint configurations
    table.jsonb("request_templates"); // Request templates for different operations
    table.jsonb("response_mappings"); // How to map bank responses to our format
    table.string("version", 20).defaultTo("1.0");
    table.boolean("is_active").defaultTo(true);
    table.timestamps(true, true);

    table.index(["bank_code", "is_active"]);
    table.unique(["bank_code", "template_name", "version"]);
  });

  // Insert supported banks for Tanzania
  await knex("supported_banks").insert([
    {
      bank_code: "NBC",
      bank_name: "National Bank of Commerce (NBC)",
      country: "TZ",
      bank_type: "COMMERCIAL",
      api_support: "FULL",
      supported_features: JSON.stringify([
        "ACCOUNT_BALANCE",
        "TRANSACTION_HISTORY",
        "REAL_TIME_SYNC",
        "PAYMENT_INITIATION",
      ]),
      auth_methods: JSON.stringify(["API_KEY", "OAUTH2"]),
      api_config: JSON.stringify({
        base_url: "https://api.nbctz.com",
        endpoints: {
          auth: "/oauth/token",
          balance: "/accounts/{account_id}/balance",
          transactions: "/accounts/{account_id}/transactions",
          payments: "/payments",
        },
        rate_limits: {
          requests_per_minute: 60,
          requests_per_hour: 1000,
        },
      }),
      description: "Tanzania's leading commercial bank with full API support",
      website: "https://www.nbctz.com",
    },
    {
      bank_code: "CRDB",
      bank_name: "CRDB Bank",
      country: "TZ",
      bank_type: "COMMERCIAL",
      api_support: "FULL",
      supported_features: JSON.stringify([
        "ACCOUNT_BALANCE",
        "TRANSACTION_HISTORY",
        "REAL_TIME_SYNC",
        "PAYMENT_INITIATION",
      ]),
      auth_methods: JSON.stringify(["OAUTH2"]),
      api_config: JSON.stringify({
        base_url: "https://api.crdbbank.co.tz",
        endpoints: {
          auth: "/oauth2/token",
          balance: "/v1/accounts/{account_id}/balance",
          transactions: "/v1/accounts/{account_id}/transactions",
          payments: "/v1/payments",
        },
      }),
      description: "CRDB Bank with comprehensive API integration",
      website: "https://www.crdbbank.co.tz",
    },
    {
      bank_code: "NMB",
      bank_name: "NMB Bank",
      country: "TZ",
      bank_type: "COMMERCIAL",
      api_support: "PARTIAL",
      supported_features: JSON.stringify([
        "ACCOUNT_BALANCE",
        "TRANSACTION_HISTORY",
      ]),
      auth_methods: JSON.stringify(["API_KEY"]),
      api_config: JSON.stringify({
        base_url: "https://api.nmbbank.co.tz",
        endpoints: {
          balance: "/api/v1/balance",
          transactions: "/api/v1/transactions",
        },
      }),
      description: "NMB Bank with basic API support",
      website: "https://www.nmbbank.co.tz",
    },
    {
      bank_code: "STANBIC",
      bank_name: "Stanbic Bank Tanzania",
      country: "TZ",
      bank_type: "COMMERCIAL",
      api_support: "FULL",
      supported_features: JSON.stringify([
        "ACCOUNT_BALANCE",
        "TRANSACTION_HISTORY",
        "REAL_TIME_SYNC",
        "PAYMENT_INITIATION",
        "BULK_PAYMENTS",
      ]),
      auth_methods: JSON.stringify(["OAUTH2"]),
      api_config: JSON.stringify({
        base_url: "https://api.stanbicbank.co.tz",
        endpoints: {
          auth: "/oauth2/authorize",
          balance: "/v2/accounts/{account_id}/balance",
          transactions: "/v2/accounts/{account_id}/transactions",
          payments: "/v2/payments",
        },
      }),
      description: "Stanbic Bank with advanced API features",
      website: "https://www.stanbicbank.co.tz",
    },
    {
      bank_code: "DTB",
      bank_name: "Diamond Trust Bank",
      country: "TZ",
      bank_type: "COMMERCIAL",
      api_support: "FULL",
      supported_features: JSON.stringify([
        "ACCOUNT_BALANCE",
        "TRANSACTION_HISTORY",
        "REAL_TIME_SYNC",
      ]),
      auth_methods: JSON.stringify(["API_KEY", "OAUTH2"]),
      description: "Diamond Trust Bank with reliable API integration",
      website: "https://www.dtbafrica.com",
    },
    {
      bank_code: "BOT",
      bank_name: "Bank of Tanzania",
      country: "TZ",
      bank_type: "CENTRAL",
      api_support: "LIMITED",
      supported_features: JSON.stringify([
        "EXCHANGE_RATES",
        "REGULATORY_DATA",
        "MONETARY_POLICY",
      ]),
      auth_methods: JSON.stringify(["API_KEY"]),
      api_config: JSON.stringify({
        base_url: "https://api.bot.go.tz",
        endpoints: {
          exchange_rates: "/v1/exchange-rates",
          monetary_policy: "/v1/monetary-policy",
        },
      }),
      description: "Bank of Tanzania - Central Bank with regulatory data",
      website: "https://www.bot.go.tz",
    },
  ]);

  // Insert API templates for major banks
  await knex("bank_api_templates").insert([
    {
      bank_code: "NBC",
      template_name: "NBC API v2",
      credential_fields: JSON.stringify([
        { name: "client_id", type: "string", required: true },
        { name: "client_secret", type: "string", required: true },
        { name: "api_key", type: "string", required: false },
      ]),
      api_endpoints: JSON.stringify({
        auth: "POST /oauth/token",
        balance: "GET /accounts/{account_id}/balance",
        transactions: "GET /accounts/{account_id}/transactions",
      }),
      request_templates: JSON.stringify({
        auth: {
          grant_type: "client_credentials",
          client_id: "{client_id}",
          client_secret: "{client_secret}",
        },
        transactions: {
          from_date: "{start_date}",
          to_date: "{end_date}",
          limit: 100,
        },
      }),
      response_mappings: JSON.stringify({
        transaction: {
          id: "transaction_id",
          date: "value_date",
          description: "narrative",
          amount: "amount",
          type: "debit_credit_indicator",
          balance: "running_balance",
        },
      }),
      version: "2.0",
    },
    {
      bank_code: "CRDB",
      template_name: "CRDB API v1",
      credential_fields: JSON.stringify([
        { name: "client_id", type: "string", required: true },
        { name: "client_secret", type: "string", required: true },
      ]),
      api_endpoints: JSON.stringify({
        auth: "POST /oauth2/token",
        balance: "GET /v1/accounts/{account_id}/balance",
        transactions: "GET /v1/accounts/{account_id}/transactions",
      }),
      version: "1.0",
    },
  ]);

  // Create indexes for performance
  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_bank_transactions_reconciliation
    ON bank_transactions(connection_id, reconciliation_status, transaction_date);
  `);

  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_bank_sync_logs_monitoring
    ON bank_sync_logs(connection_id, status, started_at);
  `);

  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_bank_connections_active
    ON bank_connections(company_id, is_active, connection_status);
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("bank_api_templates");
  await knex.schema.dropTableIfExists("supported_banks");
  await knex.schema.dropTableIfExists("bank_sync_logs");
  await knex.schema.dropTableIfExists("bank_integration_reconciliation_items");
  await knex.schema.dropTableIfExists("bank_transactions");
  await knex.schema.dropTableIfExists("bank_connections");
}
