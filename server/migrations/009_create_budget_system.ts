import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create budget types enum
  await knex.raw(`
    CREATE TYPE budget_type AS ENUM ('ANNUAL', 'QUARTERLY', 'MONTHLY');
  `);

  // Create budget status enum
  await knex.raw(`
    CREATE TYPE budget_status AS ENUM ('DRAFT', 'ACTIVE', 'LOCKED', 'ARCHIVED');
  `);

  // Create budgets table
  await knex.schema.createTable('budgets', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').notNullable().references('id').inTable('companies').onDelete('CASCADE');
    table.string('name', 255).notNullable();
    table.text('description');
    table.specificType('budget_type', 'budget_type').notNullable();
    table.integer('fiscal_year').notNullable();
    table.date('start_date').notNullable();
    table.date('end_date').notNullable();
    table.string('currency', 3).defaultTo('USD');
    table.specificType('status', 'budget_status').defaultTo('DRAFT');
    table.uuid('created_by').references('id').inTable('users').onDelete('SET NULL');
    table.uuid('approved_by').references('id').inTable('users').onDelete('SET NULL');
    table.timestamp('approved_at');
    table.timestamps(true, true);
    
    table.index(['company_id', 'fiscal_year']);
    table.index(['company_id', 'budget_type']);
    table.index(['company_id', 'status']);
    table.unique(['company_id', 'name', 'fiscal_year']);
  });

  // Create budget line items table
  await knex.schema.createTable('budget_line_items', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('budget_id').notNullable().references('id').inTable('budgets').onDelete('CASCADE');
    table.uuid('account_id').notNullable().references('id').inTable('accounts').onDelete('RESTRICT');
    table.integer('line_number').notNullable();
    table.decimal('amount', 15, 2).notNullable();
    table.string('period', 20); // For monthly/quarterly breakdowns
    table.text('notes');
    table.timestamps(true, true);
    
    table.index(['budget_id']);
    table.index(['account_id']);
    table.unique(['budget_id', 'account_id', 'period']);
  });

  // Create budget vs actual views table (for caching complex calculations)
  await knex.schema.createTable('budget_vs_actual_cache', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('budget_id').notNullable().references('id').inTable('budgets').onDelete('CASCADE');
    table.uuid('account_id').notNullable().references('id').inTable('accounts').onDelete('CASCADE');
    table.string('period', 20).notNullable(); // YYYY-MM or YYYY-QQ or YYYY
    table.decimal('budget_amount', 15, 2).notNullable();
    table.decimal('actual_amount', 15, 2).notNullable();
    table.decimal('variance_amount', 15, 2).notNullable();
    table.decimal('variance_percentage', 8, 4).notNullable();
    table.timestamp('calculated_at').defaultTo(knex.fn.now());
    table.timestamps(true, true);
    
    table.index(['budget_id', 'period']);
    table.index(['account_id', 'period']);
    table.unique(['budget_id', 'account_id', 'period']);
  });

  // Create budget templates table
  await knex.schema.createTable('budget_templates', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').notNullable().references('id').inTable('companies').onDelete('CASCADE');
    table.string('name', 255).notNullable();
    table.text('description');
    table.specificType('budget_type', 'budget_type').notNullable();
    table.jsonb('template_data').notNullable(); // Stores account mappings and default amounts
    table.boolean('is_active').defaultTo(true);
    table.uuid('created_by').references('id').inTable('users').onDelete('SET NULL');
    table.timestamps(true, true);
    
    table.index(['company_id', 'is_active']);
    table.unique(['company_id', 'name']);
  });

  // Create budget approvals table (for approval workflow)
  await knex.schema.createTable('budget_approvals', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('budget_id').notNullable().references('id').inTable('budgets').onDelete('CASCADE');
    table.uuid('approver_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.enum('status', ['PENDING', 'APPROVED', 'REJECTED']).defaultTo('PENDING');
    table.text('comments');
    table.integer('approval_level').notNullable(); // For multi-level approvals
    table.timestamp('approved_at');
    table.timestamps(true, true);
    
    table.index(['budget_id', 'approval_level']);
    table.index(['approver_id', 'status']);
    table.unique(['budget_id', 'approver_id', 'approval_level']);
  });

  // Create budget revisions table (for version control)
  await knex.schema.createTable('budget_revisions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('budget_id').notNullable().references('id').inTable('budgets').onDelete('CASCADE');
    table.integer('revision_number').notNullable();
    table.jsonb('budget_data').notNullable(); // Snapshot of budget and line items
    table.text('revision_notes');
    table.uuid('created_by').references('id').inTable('users').onDelete('SET NULL');
    table.timestamps(true, true);
    
    table.index(['budget_id', 'revision_number']);
    table.unique(['budget_id', 'revision_number']);
  });

  // Create budget alerts table
  await knex.schema.createTable('budget_alerts', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('budget_id').notNullable().references('id').inTable('budgets').onDelete('CASCADE');
    table.uuid('account_id').references('id').inTable('accounts').onDelete('CASCADE');
    table.enum('alert_type', [
      'OVER_BUDGET', 
      'APPROACHING_BUDGET', 
      'UNDER_BUDGET', 
      'NO_ACTIVITY'
    ]).notNullable();
    table.decimal('threshold_percentage', 5, 2).notNullable();
    table.decimal('current_percentage', 5, 2).notNullable();
    table.decimal('budget_amount', 15, 2).notNullable();
    table.decimal('actual_amount', 15, 2).notNullable();
    table.string('period', 20).notNullable();
    table.boolean('is_acknowledged').defaultTo(false);
    table.uuid('acknowledged_by').references('id').inTable('users').onDelete('SET NULL');
    table.timestamp('acknowledged_at');
    table.timestamps(true, true);
    
    table.index(['budget_id', 'is_acknowledged']);
    table.index(['alert_type', 'created_at']);
  });

  // Create budget forecast table (for predictive analytics)
  await knex.schema.createTable('budget_forecasts', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('budget_id').notNullable().references('id').inTable('budgets').onDelete('CASCADE');
    table.uuid('account_id').notNullable().references('id').inTable('accounts').onDelete('CASCADE');
    table.string('forecast_period', 20).notNullable(); // Future period
    table.decimal('forecasted_amount', 15, 2).notNullable();
    table.decimal('confidence_level', 5, 2).notNullable(); // 0-100%
    table.string('forecast_method', 50).notNullable(); // LINEAR, SEASONAL, ML, etc.
    table.jsonb('forecast_factors'); // Factors used in calculation
    table.timestamp('calculated_at').defaultTo(knex.fn.now());
    table.timestamps(true, true);
    
    table.index(['budget_id', 'forecast_period']);
    table.index(['account_id', 'forecast_period']);
    table.unique(['budget_id', 'account_id', 'forecast_period']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('budget_forecasts');
  await knex.schema.dropTableIfExists('budget_alerts');
  await knex.schema.dropTableIfExists('budget_revisions');
  await knex.schema.dropTableIfExists('budget_approvals');
  await knex.schema.dropTableIfExists('budget_templates');
  await knex.schema.dropTableIfExists('budget_vs_actual_cache');
  await knex.schema.dropTableIfExists('budget_line_items');
  await knex.schema.dropTableIfExists('budgets');
  
  await knex.raw('DROP TYPE IF EXISTS budget_status');
  await knex.raw('DROP TYPE IF EXISTS budget_type');
}
