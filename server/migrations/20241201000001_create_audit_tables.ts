import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if tables exist before creating them
  const auditLogsExists = await knex.schema.hasTable("audit_logs");
  const accessAuditLogsExists = await knex.schema.hasTable("access_audit_logs");
  const financialAuditTrailExists = await knex.schema.hasTable("financial_audit_trail");

  // Main comprehensive audit logs table (different from audit_trail)
  if (!auditLogsExists) {
    await knex.schema.createTable("audit_logs", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table.string("table_name", 100).notNullable();
    table.string("record_id", 100).notNullable();
    table.enum("action_type", ["INSERT", "UPDATE", "DELETE"]).notNullable();
    table.jsonb("old_values").nullable();
    table.jsonb("new_values").nullable();
    table.specificType("changed_fields", "text[]").nullable();
    table.uuid("user_id").nullable().references("id").inTable("users");
    table.string("user_email", 255).nullable();
    table.string("user_role", 50).nullable();
    table.uuid("company_id").nullable().references("id").inTable("companies");
    table.string("ip_address", 45).nullable(); // IPv6 compatible
    table.text("user_agent").nullable();
    table.string("session_id", 255).nullable();
    table.timestamp("timestamp", { useTz: true }).defaultTo(knex.fn.now());
    table.text("reason").nullable();
    table.jsonb("compliance_flags").nullable();
    table.enum("risk_level", ["LOW", "MEDIUM", "HIGH", "CRITICAL"]).defaultTo("LOW");
    
    // Indexes for performance
    table.index(["table_name", "record_id"]);
    table.index(["user_id", "timestamp"]);
    table.index(["company_id", "timestamp"]);
    table.index(["action_type", "timestamp"]);
    table.index(["risk_level", "timestamp"]);
    table.index("timestamp");
    });
    console.log("✅ audit_logs table created successfully");
  }

  // System access audit logs (enhanced version)
  if (!accessAuditLogsExists) {
    await knex.schema.createTable("access_audit_logs", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table.uuid("user_id").nullable().references("id").inTable("users");
    table.string("action", 100).notNullable();
    table.string("resource", 255).nullable();
    table.boolean("success").notNullable();
    table.text("failure_reason").nullable();
    table.string("ip_address", 45).nullable(); // IPv6 compatible
    table.jsonb("location_data").nullable();
    table.jsonb("device_info").nullable();
    table.timestamp("timestamp", { useTz: true }).defaultTo(knex.fn.now());
    
    // Indexes
    table.index(["user_id", "timestamp"]);
    table.index(["action", "timestamp"]);
    table.index(["success", "timestamp"]);
    table.index("timestamp");
    });
    console.log("✅ access_audit_logs table created successfully");
  }

  // Financial transaction audit trail
  if (!financialAuditTrailExists) {
    await knex.schema.createTable("financial_audit_trail", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table.uuid("transaction_id").nullable().references("id").inTable("transactions");
    table.uuid("account_id").nullable().references("id").inTable("accounts");
    table.decimal("amount", 15, 2).nullable();
    table.string("currency", 3).nullable();
    table.string("action_type", 50).notNullable();
    table.decimal("previous_balance", 15, 2).nullable();
    table.decimal("new_balance", 15, 2).nullable();
    table.string("authorization_level", 50).nullable();
    table.uuid("approver_id").nullable().references("id").inTable("users");
    table.jsonb("compliance_check_result").nullable();
    table.timestamp("timestamp", { useTz: true }).defaultTo(knex.fn.now());
    
    // Indexes
    table.index(["transaction_id", "timestamp"]);
    table.index(["account_id", "timestamp"]);
    table.index(["action_type", "timestamp"]);
    table.index(["authorization_level", "timestamp"]);
    table.index("timestamp");
    });
    console.log("✅ financial_audit_trail table created successfully");
  }

  console.log("✅ All auditing tables processed successfully");
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("financial_audit_trail");
  await knex.schema.dropTableIfExists("access_audit_logs");
  await knex.schema.dropTableIfExists("audit_logs");
  
  console.log("✅ Audit tables dropped successfully");
}
