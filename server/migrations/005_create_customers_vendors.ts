import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create contact types enum
  await knex.raw(`
    CREATE TYPE contact_type AS ENUM (
      'CUSTOMER',
      'VENDOR',
      'EMPLOYEE',
      'OTHER'
    );
  `);

  // Create contacts table (unified customers, vendors, employees)
  await knex.schema.createTable('contacts', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.string('contact_number', 50).notNullable();
    table.specificType('contact_type', 'contact_type').notNullable();
    table.string('name', 255).notNullable();
    table.string('display_name', 255);
    table.string('email', 255);
    table.string('phone', 20);
    table.string('mobile', 20);
    table.string('website', 255);
    table.string('tax_id', 50);
    table.text('billing_address');
    table.string('billing_city', 100);
    table.string('billing_state', 100);
    table.string('billing_postal_code', 20);
    table.string('billing_country', 100);
    table.text('shipping_address');
    table.string('shipping_city', 100);
    table.string('shipping_state', 100);
    table.string('shipping_postal_code', 20);
    table.string('shipping_country', 100);
    table.string('currency', 3).defaultTo('USD');
    table.string('payment_terms', 50); // NET_30, NET_60, etc.
    table.decimal('credit_limit', 15, 2).defaultTo(0);
    table.boolean('is_active').defaultTo(true);
    table.jsonb('custom_fields').defaultTo('{}');
    table.text('notes');
    table.timestamps(true, true);
    
    table.unique(['company_id', 'contact_number']);
    table.index(['company_id', 'contact_type']);
    table.index(['name']);
    table.index(['email']);
  });

  // Create invoices table
  await knex.schema.createTable('invoices', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.string('invoice_number', 50).notNullable();
    table.uuid('contact_id').references('id').inTable('contacts').onDelete('RESTRICT');
    table.string('invoice_type', 20).notNullable(); // SALES, PURCHASE
    table.date('invoice_date').notNullable();
    table.date('due_date').notNullable();
    table.string('status', 20).defaultTo('DRAFT'); // DRAFT, SENT, PAID, OVERDUE, CANCELLED
    table.decimal('subtotal', 15, 2).notNullable();
    table.decimal('tax_amount', 15, 2).defaultTo(0);
    table.decimal('discount_amount', 15, 2).defaultTo(0);
    table.decimal('total_amount', 15, 2).notNullable();
    table.decimal('paid_amount', 15, 2).defaultTo(0);
    table.decimal('balance_due', 15, 2).notNullable();
    table.string('currency', 3).defaultTo('USD');
    table.decimal('exchange_rate', 10, 6).defaultTo(1);
    table.text('terms_and_conditions');
    table.text('notes');
    table.uuid('created_by').references('id').inTable('users').onDelete('RESTRICT');
    table.timestamps(true, true);
    
    table.unique(['company_id', 'invoice_number']);
    table.index(['company_id', 'invoice_type']);
    table.index(['contact_id']);
    table.index(['status']);
    table.index(['due_date']);
  });

  // Create invoice line items table
  await knex.schema.createTable('invoice_line_items', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('invoice_id').references('id').inTable('invoices').onDelete('CASCADE');
    table.integer('line_number').notNullable();
    table.string('item_code', 50);
    table.string('description', 500).notNullable();
    table.decimal('quantity', 10, 3).notNullable();
    table.string('unit', 20).defaultTo('EA');
    table.decimal('unit_price', 15, 2).notNullable();
    table.decimal('discount_percent', 5, 2).defaultTo(0);
    table.decimal('discount_amount', 15, 2).defaultTo(0);
    table.decimal('line_total', 15, 2).notNullable();
    table.uuid('account_id').references('id').inTable('accounts').onDelete('RESTRICT');
    table.jsonb('tax_details').defaultTo('{}');
    table.timestamps(true, true);
    
    table.index(['invoice_id']);
    table.index(['account_id']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('invoice_line_items');
  await knex.schema.dropTableIfExists('invoices');
  await knex.schema.dropTableIfExists('contacts');
  await knex.raw('DROP TYPE IF EXISTS contact_type');
}
