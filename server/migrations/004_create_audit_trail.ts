import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create audit actions enum
  await knex.raw(`
    CREATE TYPE audit_action AS ENUM (
      'CREATE',
      'UPDATE',
      'DELETE',
      'LOGIN',
      'LOGOUT',
      'APPROVE',
      'REJECT',
      'REVERSE',
      'EXPORT',
      'IMPORT',
      'VIEW'
    );
  `);

  // Create audit trail table
  await knex.schema.createTable('audit_trail', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.uuid('user_id').references('id').inTable('users').onDelete('SET NULL');
    table.string('user_email', 255);
    table.specificType('action', 'audit_action').notNullable();
    table.string('table_name', 100);
    table.uuid('record_id');
    table.jsonb('old_values');
    table.jsonb('new_values');
    table.text('description');
    table.string('ip_address', 45);
    table.string('user_agent', 500);
    table.jsonb('metadata').defaultTo('{}');
    table.timestamp('created_at').defaultTo(knex.fn.now());
    
    table.index(['company_id', 'created_at']);
    table.index(['user_id']);
    table.index(['table_name', 'record_id']);
    table.index(['action']);
  });

  // Create system logs table
  await knex.schema.createTable('system_logs', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('level', 20).notNullable(); // ERROR, WARN, INFO, DEBUG
    table.string('category', 100); // AUTH, TRANSACTION, REPORT, etc.
    table.text('message').notNullable();
    table.jsonb('context').defaultTo('{}');
    table.string('source', 100); // API endpoint, service name, etc.
    table.uuid('user_id').references('id').inTable('users').onDelete('SET NULL');
    table.uuid('company_id').references('id').inTable('companies').onDelete('SET NULL');
    table.string('correlation_id', 100); // For tracing related operations
    table.timestamp('created_at').defaultTo(knex.fn.now());
    
    table.index(['level', 'created_at']);
    table.index(['category']);
    table.index(['correlation_id']);
  });

  // Create data retention policies table
  await knex.schema.createTable('data_retention_policies', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.string('table_name', 100).notNullable();
    table.integer('retention_days').notNullable();
    table.boolean('is_active').defaultTo(true);
    table.text('description');
    table.timestamps(true, true);
    
    table.unique(['company_id', 'table_name']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('data_retention_policies');
  await knex.schema.dropTableIfExists('system_logs');
  await knex.schema.dropTableIfExists('audit_trail');
  await knex.raw('DROP TYPE IF EXISTS audit_action');
}
