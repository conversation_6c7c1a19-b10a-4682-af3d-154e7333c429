import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Create compliance reports table
  await knex.schema.createTable("compliance_reports", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table
      .enum("report_type", [
        "VAT_RETURN",
        "INCOME_TAX",
        "PAYROLL_TAX",
        "WITHHOLDING_TAX",
        "AUDIT_TRAIL",
        "FINANCIAL_STATEMENTS",
      ])
      .notNullable();
    table.string("jurisdiction", 10).defaultTo("TZ"); // TZ, KE, UG, etc.
    table.date("period_start").notNullable();
    table.date("period_end").notNullable();
    table.string("fiscal_year", 4);
    table.jsonb("report_data").notNullable(); // Generated report content
    table
      .enum("status", ["GENERATED", "SUBMITTED", "ACCEPTED", "REJECTED"])
      .defaultTo("GENERATED");
    table.string("submission_reference", 255); // Reference from government system
    table
      .uuid("generated_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table
      .uuid("submitted_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamp("generated_at").defaultTo(knex.fn.now());
    table.timestamp("submitted_at");
    table.timestamps(true, true);

    table.index(["company_id", "report_type"]);
    table.index(["jurisdiction", "report_type"]);
    table.index(["status", "submitted_at"]);
    table.index(["period_start", "period_end"]);
  });

  // Create compliance schedules table (for recurring compliance requirements)
  await knex.schema.createTable("compliance_schedules", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("report_type", 50).notNullable();
    table.string("jurisdiction", 10).defaultTo("TZ");
    table.enum("frequency", ["MONTHLY", "QUARTERLY", "ANNUALLY"]).notNullable();
    table.integer("due_day").notNullable(); // Day of month when due
    table.integer("due_month"); // Month when due (for annual reports)
    table.boolean("is_active").defaultTo(true);
    table.boolean("auto_generate").defaultTo(false);
    table.jsonb("notification_settings"); // Email notifications, etc.
    table.timestamps(true, true);

    table.index(["company_id", "is_active"]);
    table.index(["frequency", "due_day"]);
    table.unique(["company_id", "report_type", "jurisdiction"]);
  });

  // Create compliance notifications table
  await knex.schema.createTable("compliance_notifications", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table
      .uuid("schedule_id")
      .references("id")
      .inTable("compliance_schedules")
      .onDelete("CASCADE");
    table.string("report_type", 50).notNullable();
    table.date("due_date").notNullable();
    table
      .enum("notification_type", ["REMINDER", "OVERDUE", "SUBMITTED"])
      .notNullable();
    table.enum("status", ["PENDING", "SENT", "FAILED"]).defaultTo("PENDING");
    table.text("message");
    table.jsonb("recipients"); // Email addresses
    table.timestamp("scheduled_at").notNullable();
    table.timestamp("sent_at");
    table.timestamps(true, true);

    table.index(["company_id", "due_date"]);
    table.index(["status", "scheduled_at"]);
    table.index(["notification_type", "due_date"]);
  });

  // Create tax authorities table (for different jurisdictions)
  await knex.schema.createTable("tax_authorities", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table.string("jurisdiction", 10).notNullable().unique();
    table.string("name", 255).notNullable();
    table.string("abbreviation", 20);
    table.text("description");
    table.jsonb("contact_info"); // Address, phone, email, website
    table.jsonb("api_config"); // API endpoints and configuration
    table.jsonb("report_requirements"); // Specific requirements for each report type
    table.boolean("is_active").defaultTo(true);
    table.timestamps(true, true);

    table.index(["jurisdiction", "is_active"]);
  });

  // Create compliance templates table (for standardized reports)
  await knex.schema.createTable("compliance_templates", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table.string("report_type", 50).notNullable();
    table.string("jurisdiction", 10).notNullable();
    table.string("name", 255).notNullable();
    table.text("description");
    table.jsonb("template_structure"); // JSON schema for the report
    table.jsonb("validation_rules"); // Validation rules for the report
    table.string("version", 20).defaultTo("1.0");
    table.boolean("is_active").defaultTo(true);
    table.timestamps(true, true);

    table.index(["report_type", "jurisdiction"]);
    table.index(["is_active"]);
    table.unique(["report_type", "jurisdiction", "version"]);
  });

  // Insert default tax authorities
  await knex("tax_authorities").insert([
    {
      jurisdiction: "TZ",
      name: "Tanzania Revenue Authority",
      abbreviation: "TRA",
      description: "The national tax authority of Tanzania",
      contact_info: JSON.stringify({
        address: "Mapato House, Azikiwe Street, P.O. Box 11491, Dar es Salaam",
        phone: "+255 22 211 7000",
        email: "<EMAIL>",
        website: "https://www.tra.go.tz",
      }),
      api_config: JSON.stringify({
        base_url: "https://api.tra.go.tz",
        endpoints: {
          vat_return: "/vat/submit",
          withholding_tax: "/withholding/submit",
          income_tax: "/income/submit",
        },
      }),
      report_requirements: JSON.stringify({
        VAT_RETURN: {
          frequency: "QUARTERLY",
          due_date: "20th of month following quarter",
          format: "XML",
          validation: "TRA_VAT_SCHEMA_V2",
        },
        WITHHOLDING_TAX: {
          frequency: "MONTHLY",
          due_date: "7th of following month",
          format: "XML",
          validation: "TRA_WHT_SCHEMA_V1",
        },
      }),
    },
    {
      jurisdiction: "KE",
      name: "Kenya Revenue Authority",
      abbreviation: "KRA",
      description: "The national tax authority of Kenya",
      contact_info: JSON.stringify({
        address: "Times Tower, Haile Selassie Avenue, P.O. Box 48240, Nairobi",
        phone: "+254 20 281 0000",
        email: "<EMAIL>",
        website: "https://www.kra.go.ke",
      }),
    },
    {
      jurisdiction: "UG",
      name: "Uganda Revenue Authority",
      abbreviation: "URA",
      description: "The national tax authority of Uganda",
      contact_info: JSON.stringify({
        address: "Nakawa, Plot M670, P.O. Box 7279, Kampala",
        phone: "+*********** 000",
        email: "<EMAIL>",
        website: "https://www.ura.go.ug",
      }),
    },
  ]);

  // Insert default compliance schedules for Tanzania
  await knex.raw(`
    INSERT INTO compliance_schedules (company_id, report_type, jurisdiction, frequency, due_day, due_month, is_active)
    SELECT 
      c.id as company_id,
      'VAT_RETURN' as report_type,
      'TZ' as jurisdiction,
      'QUARTERLY' as frequency,
      20 as due_day,
      NULL as due_month,
      true as is_active
    FROM companies c
    WHERE c.country = 'TZ' OR c.country IS NULL;
  `);

  await knex.raw(`
    INSERT INTO compliance_schedules (company_id, report_type, jurisdiction, frequency, due_day, due_month, is_active)
    SELECT 
      c.id as company_id,
      'WITHHOLDING_TAX' as report_type,
      'TZ' as jurisdiction,
      'MONTHLY' as frequency,
      7 as due_day,
      NULL as due_month,
      true as is_active
    FROM companies c
    WHERE c.country = 'TZ' OR c.country IS NULL;
  `);

  await knex.raw(`
    INSERT INTO compliance_schedules (company_id, report_type, jurisdiction, frequency, due_day, due_month, is_active)
    SELECT 
      c.id as company_id,
      'INCOME_TAX' as report_type,
      'TZ' as jurisdiction,
      'ANNUALLY' as frequency,
      30 as due_day,
      6 as due_month,
      true as is_active
    FROM companies c
    WHERE c.country = 'TZ' OR c.country IS NULL;
  `);

  // Insert default compliance templates
  await knex("compliance_templates").insert([
    {
      report_type: "VAT_RETURN",
      jurisdiction: "TZ",
      name: "Tanzania VAT Return Template",
      description:
        "Standard VAT return template for Tanzania Revenue Authority",
      template_structure: JSON.stringify({
        company_info: {
          name: "string",
          vat_number: "string",
          address: "string",
          phone: "string",
          email: "string",
        },
        period: {
          start_date: "date",
          end_date: "date",
          quarter: "string",
        },
        vat_calculation: {
          taxable_sales: "number",
          output_vat: "number",
          taxable_purchases: "number",
          input_vat: "number",
          net_vat: "number",
          vat_payable: "number",
          vat_refundable: "number",
        },
      }),
      validation_rules: JSON.stringify({
        required_fields: [
          "company_info.vat_number",
          "period.start_date",
          "period.end_date",
        ],
        numeric_fields: [
          "vat_calculation.taxable_sales",
          "vat_calculation.output_vat",
        ],
        date_format: "YYYY-MM-DD",
        vat_rate: 18,
      }),
      version: "2.0",
    },
    {
      report_type: "WITHHOLDING_TAX",
      jurisdiction: "TZ",
      name: "Tanzania Withholding Tax Template",
      description: "Monthly withholding tax return template for TRA",
      template_structure: JSON.stringify({
        period: {
          start_date: "date",
          end_date: "date",
          month: "string",
        },
        summary: {
          total_transactions: "number",
          total_payments: "number",
          total_withholding: "number",
        },
        transactions: [
          {
            description: "string",
            date: "date",
            payment_amount: "number",
            withholding_rate: "number",
            withholding_amount: "number",
            tax_type: "string",
          },
        ],
      }),
      validation_rules: JSON.stringify({
        required_fields: ["period.start_date", "period.end_date"],
        withholding_rates: [5, 10, 15, 20],
        date_format: "YYYY-MM-DD",
      }),
      version: "1.0",
    },
  ]);

  // Add compliance-related columns to companies table if they don't exist
  const hasVatNumber = await knex.schema.hasColumn("companies", "vat_number");
  const hasTaxId = await knex.schema.hasColumn("companies", "tax_id");
  const hasCountry = await knex.schema.hasColumn("companies", "country");
  const hasFiscalYearEnd = await knex.schema.hasColumn(
    "companies",
    "fiscal_year_end"
  );

  if (!hasVatNumber || !hasTaxId || !hasCountry || !hasFiscalYearEnd) {
    await knex.schema.alterTable("companies", (table) => {
      if (!hasVatNumber) {
        table.string("vat_number", 50);
      }
      if (!hasTaxId) {
        table.string("tax_id", 50);
      }
      if (!hasCountry) {
        table.string("country", 10).defaultTo("TZ");
      }
      if (!hasFiscalYearEnd) {
        table.date("fiscal_year_end").defaultTo("2024-12-31");
      }
    });
  }

  // Create indexes for performance
  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_compliance_reports_lookup
    ON compliance_reports(company_id, report_type, period_start, period_end);
  `);

  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_compliance_schedules_due
    ON compliance_schedules(frequency, due_day, due_month, is_active);
  `);

  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_compliance_notifications_processing
    ON compliance_notifications(status, scheduled_at) WHERE status = 'PENDING';
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("compliance_templates");
  await knex.schema.dropTableIfExists("tax_authorities");
  await knex.schema.dropTableIfExists("compliance_notifications");
  await knex.schema.dropTableIfExists("compliance_schedules");
  await knex.schema.dropTableIfExists("compliance_reports");

  // Remove added columns from companies table (check each individually)
  const hasVatNumber = await knex.schema.hasColumn("companies", "vat_number");
  const hasTaxId = await knex.schema.hasColumn("companies", "tax_id");
  const hasCountry = await knex.schema.hasColumn("companies", "country");
  const hasFiscalYearEnd = await knex.schema.hasColumn(
    "companies",
    "fiscal_year_end"
  );

  if (hasVatNumber || hasTaxId || hasCountry || hasFiscalYearEnd) {
    await knex.schema.alterTable("companies", (table) => {
      if (hasVatNumber) {
        table.dropColumn("vat_number");
      }
      if (hasTaxId) {
        table.dropColumn("tax_id");
      }
      if (hasCountry) {
        table.dropColumn("country");
      }
      if (hasFiscalYearEnd) {
        table.dropColumn("fiscal_year_end");
      }
    });
  }
}
