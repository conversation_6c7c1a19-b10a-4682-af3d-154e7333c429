import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Check if tables exist before creating them

  // Export Templates Table
  const exportTemplatesExists = await knex.schema.hasTable('export_templates');
  if (!exportTemplatesExists) {
    await knex.schema.createTable('export_templates', (table) => {
      table.uuid('id').primary();
      table.string('name').notNullable();
      table.text('description');
      table.json('report_types').notNullable();
      table.json('layout').notNullable();
      table.json('styling').notNullable();
      table.uuid('created_by').notNullable();
      table.boolean('is_active').defaultTo(true);
      table.timestamps(true, true);

      table.foreign('created_by').references('id').inTable('users');
      table.index(['is_active', 'name']);
    });
  }

  // Report Schedules Table
  const reportSchedulesExists = await knex.schema.hasTable('report_schedules');
  if (!reportSchedulesExists) {
    await knex.schema.createTable('report_schedules', (table) => {
      table.uuid('id').primary();
      table.uuid('company_id').notNullable();
      table.uuid('user_id').notNullable();
      table.string('report_type').notNullable();
      table.enum('frequency', ['DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'ANNUALLY']).notNullable();
      table.integer('day_of_week'); // 0-6 for weekly
      table.integer('day_of_month'); // 1-31 for monthly
      table.string('time').notNullable(); // HH:MM format
      table.string('timezone').notNullable();
      table.json('recipients').notNullable();
      table.enum('format', ['PDF', 'EXCEL', 'BOTH']).notNullable();
      table.boolean('include_charts').defaultTo(false);
      table.string('password');
      table.timestamp('next_run').notNullable();
      table.boolean('is_active').defaultTo(true);
      table.timestamps(true, true);

      table.foreign('company_id').references('id').inTable('companies');
      table.foreign('user_id').references('id').inTable('users');
      table.index(['company_id', 'is_active']);
      table.index(['next_run', 'is_active']);
    });
  }

  // Export History Table
  const exportHistoryExists = await knex.schema.hasTable('export_history');
  if (!exportHistoryExists) {
    await knex.schema.createTable('export_history', (table) => {
      table.uuid('id').primary();
      table.uuid('company_id').notNullable();
      table.uuid('user_id').notNullable();
      table.string('report_type').notNullable();
      table.string('format').notNullable();
      table.string('filename').notNullable();
      table.bigInteger('file_size').defaultTo(0);
      table.json('parameters');
      table.enum('status', ['PENDING', 'COMPLETED', 'FAILED']).defaultTo('PENDING');
      table.text('error_message');
      table.timestamp('created_at').defaultTo(knex.fn.now());

      table.foreign('company_id').references('id').inTable('companies');
      table.foreign('user_id').references('id').inTable('users');
      table.index(['company_id', 'created_at']);
      table.index(['user_id', 'created_at']);
    });
  }

  // Plaid Items Table
  const plaidItemsExists = await knex.schema.hasTable('plaid_items');
  if (!plaidItemsExists) {
    await knex.schema.createTable('plaid_items', (table) => {
      table.uuid('id').primary();
      table.uuid('company_id').notNullable();
      table.uuid('user_id').notNullable();
      table.string('item_id').notNullable().unique();
      table.text('access_token').notNullable();
      table.string('institution_id');
      table.boolean('is_active').defaultTo(true);
      table.string('error_code');
      table.text('error_message');
      table.timestamps(true, true);

      table.foreign('company_id').references('id').inTable('companies');
      table.foreign('user_id').references('id').inTable('users');
      table.index(['company_id', 'is_active']);
    });
  }

  // Plaid Link Tokens Table
  const plaidLinkTokensExists = await knex.schema.hasTable('plaid_link_tokens');
  if (!plaidLinkTokensExists) {
    await knex.schema.createTable('plaid_link_tokens', (table) => {
      table.uuid('id').primary();
      table.uuid('company_id').notNullable();
      table.uuid('user_id').notNullable();
      table.text('link_token').notNullable();
      table.timestamp('expiration').notNullable();
      table.string('request_id').notNullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());

      table.foreign('company_id').references('id').inTable('companies');
      table.foreign('user_id').references('id').inTable('users');
      table.index(['company_id', 'expiration']);
    });
  }

  // Bank Accounts Table
  const bankAccountsExists = await knex.schema.hasTable('bank_accounts');
  if (!bankAccountsExists) {
    await knex.schema.createTable('bank_accounts', (table) => {
      table.uuid('id').primary();
      table.uuid('company_id').notNullable();
      table.string('bank_name').notNullable();
      table.string('account_name').notNullable();
      table.string('account_number');
      table.string('routing_number');
      table.enum('account_type', ['CHECKING', 'SAVINGS', 'CREDIT_CARD', 'INVESTMENT', 'LOAN']).notNullable();
      table.decimal('balance', 15, 2).defaultTo(0);
      table.string('currency', 3).defaultTo('USD');
      table.boolean('is_active').defaultTo(true);
      table.string('plaid_account_id');
      table.string('plaid_item_id');
      table.timestamp('last_sync_at');
      table.timestamps(true, true);

      table.foreign('company_id').references('id').inTable('companies');
      table.index(['company_id', 'is_active']);
      table.index(['plaid_item_id']);
    });
  }

  // Bank Transactions Table
  const bankTransactionsExists = await knex.schema.hasTable('bank_transactions');
  if (!bankTransactionsExists) {
    await knex.schema.createTable('bank_transactions', (table) => {
      table.uuid('id').primary();
      table.uuid('bank_account_id').notNullable();
      table.uuid('company_id').notNullable();
      table.string('transaction_id').notNullable(); // External transaction ID
      table.decimal('amount', 15, 2).notNullable();
      table.text('description').notNullable();
      table.string('category');
      table.string('subcategory');
      table.date('date').notNullable();
      table.boolean('pending').defaultTo(false);
      table.string('merchant_name');
      table.json('location');
      table.enum('payment_channel', ['ONLINE', 'IN_STORE', 'ATM', 'OTHER']).defaultTo('OTHER');
      table.string('account_owner');
      table.boolean('is_reconciled').defaultTo(false);
      table.timestamp('reconciled_at');
      table.uuid('reconciled_by');
      table.uuid('accounting_transaction_id');
      table.timestamps(true, true);

      table.foreign('bank_account_id').references('id').inTable('bank_accounts');
      table.foreign('company_id').references('id').inTable('companies');
      table.foreign('reconciled_by').references('id').inTable('users');
      table.foreign('accounting_transaction_id').references('id').inTable('transactions');
      table.unique(['transaction_id']);
      table.index(['bank_account_id', 'date']);
      table.index(['company_id', 'is_reconciled']);
    });
  }

  // Skip customers table as it conflicts with existing contacts table
  // The existing contacts table in migration 005 serves the same purpose

  // Payment Methods Table (referencing contacts instead of customers)
  const paymentMethodsExists = await knex.schema.hasTable('payment_methods');
  if (!paymentMethodsExists) {
    await knex.schema.createTable('payment_methods', (table) => {
      table.uuid('id').primary();
      table.uuid('company_id').notNullable();
      table.uuid('contact_id').notNullable(); // Reference contacts table instead of customers
      table.enum('type', ['CREDIT_CARD', 'BANK_ACCOUNT', 'ACH', 'WIRE']).notNullable();
      table.enum('provider', ['STRIPE', 'PLAID', 'MANUAL']).notNullable();
      table.string('provider_payment_method_id');
      table.string('last4');
      table.string('brand');
      table.integer('expiry_month');
      table.integer('expiry_year');
      table.boolean('is_default').defaultTo(false);
      table.boolean('is_active').defaultTo(true);
      table.json('metadata');
      table.timestamps(true, true);

      table.foreign('company_id').references('id').inTable('companies');
      table.foreign('contact_id').references('id').inTable('contacts');
      table.index(['contact_id', 'is_active']);
    });
  }

  // Skip invoices table as it already exists from migration 005

  // Payments Table (referencing contacts instead of customers)
  const paymentsExists = await knex.schema.hasTable('payments');
  if (!paymentsExists) {
    await knex.schema.createTable('payments', (table) => {
      table.uuid('id').primary();
      table.uuid('company_id').notNullable();
      table.uuid('contact_id').notNullable(); // Reference contacts table instead of customers
      table.uuid('payment_method_id').notNullable();
      table.uuid('invoice_id');
      table.decimal('amount', 15, 2).notNullable();
      table.string('currency', 3).defaultTo('USD');
      table.enum('status', ['PENDING', 'PROCESSING', 'SUCCEEDED', 'FAILED', 'CANCELLED', 'REFUNDED']).notNullable();
      table.string('payment_intent_id');
      table.string('provider_transaction_id');
      table.text('description');
      table.json('metadata');
      table.text('failure_reason');
      table.decimal('refunded_amount', 15, 2);
      table.timestamp('refunded_at');
      table.timestamp('processed_at');
      table.timestamps(true, true);

      table.foreign('company_id').references('id').inTable('companies');
      table.foreign('contact_id').references('id').inTable('contacts');
      table.foreign('payment_method_id').references('id').inTable('payment_methods');
      table.foreign('invoice_id').references('id').inTable('invoices');
      table.index(['company_id', 'status']);
      table.index(['contact_id', 'created_at']);
    });
  }

  // Workflows Table
  const workflowsExists = await knex.schema.hasTable('workflows');
  if (!workflowsExists) {
    await knex.schema.createTable('workflows', (table) => {
      table.uuid('id').primary();
      table.uuid('company_id').notNullable();
      table.string('name').notNullable();
      table.text('description');
      table.boolean('is_active').defaultTo(true);
      table.json('trigger').notNullable();
      table.json('conditions');
      table.json('actions').notNullable();
      table.json('metadata');
      table.uuid('created_by').notNullable();
      table.timestamp('last_run_at');
      table.timestamp('next_run_at');
      table.integer('run_count').defaultTo(0);
      table.timestamps(true, true);

      table.foreign('company_id').references('id').inTable('companies');
      table.foreign('created_by').references('id').inTable('users');
      table.index(['company_id', 'is_active']);
      table.index(['next_run_at', 'is_active']);
    });
  }

  // Workflow Executions Table
  const workflowExecutionsExists = await knex.schema.hasTable('workflow_executions');
  if (!workflowExecutionsExists) {
    await knex.schema.createTable('workflow_executions', (table) => {
      table.uuid('id').primary();
      table.uuid('workflow_id').notNullable();
      table.uuid('company_id').notNullable();
      table.enum('status', ['PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED']).notNullable();
      table.json('trigger_data');
      table.timestamp('started_at').notNullable();
      table.timestamp('completed_at');
      table.text('error');
      table.json('results');
      table.timestamp('created_at').defaultTo(knex.fn.now());

      table.foreign('workflow_id').references('id').inTable('workflows');
      table.foreign('company_id').references('id').inTable('companies');
      table.index(['workflow_id', 'created_at']);
      table.index(['company_id', 'status']);
    });
  }

  // Approval Requests Table
  const approvalRequestsExists = await knex.schema.hasTable('approval_requests');
  if (!approvalRequestsExists) {
    await knex.schema.createTable('approval_requests', (table) => {
      table.uuid('id').primary();
      table.uuid('company_id').notNullable();
      table.uuid('workflow_execution_id').notNullable();
      table.uuid('requested_by').notNullable();
      table.json('approvers').notNullable();
      table.integer('required_approvals').notNullable();
      table.integer('current_approvals').defaultTo(0);
      table.enum('status', ['PENDING', 'APPROVED', 'REJECTED', 'EXPIRED']).defaultTo('PENDING');
      table.string('title').notNullable();
      table.text('description');
      table.json('data');
      table.timestamp('expires_at');
      table.timestamps(true, true);

      table.foreign('company_id').references('id').inTable('companies');
      table.foreign('workflow_execution_id').references('id').inTable('workflow_executions');
      table.foreign('requested_by').references('id').inTable('users');
      table.index(['company_id', 'status']);
      table.index(['workflow_execution_id']);
    });
  }

  // Approval Responses Table
  const approvalResponsesExists = await knex.schema.hasTable('approval_responses');
  if (!approvalResponsesExists) {
    await knex.schema.createTable('approval_responses', (table) => {
      table.uuid('id').primary();
      table.uuid('approval_request_id').notNullable();
      table.uuid('user_id').notNullable();
      table.enum('decision', ['APPROVE', 'REJECT']).notNullable();
      table.text('comments');
      table.timestamp('created_at').defaultTo(knex.fn.now());

      table.foreign('approval_request_id').references('id').inTable('approval_requests');
      table.foreign('user_id').references('id').inTable('users');
      table.unique(['approval_request_id', 'user_id']);
    });
  }
}

export async function down(knex: Knex): Promise<void> {
  // Drop tables in reverse order to handle foreign key constraints
  // Only drop tables that this migration creates
  await knex.schema.dropTableIfExists('approval_responses');
  await knex.schema.dropTableIfExists('approval_requests');
  await knex.schema.dropTableIfExists('workflow_executions');
  await knex.schema.dropTableIfExists('workflows');
  await knex.schema.dropTableIfExists('payments');
  // Skip invoices - managed by migration 005
  await knex.schema.dropTableIfExists('payment_methods');
  // Skip customers - using contacts table from migration 005
  await knex.schema.dropTableIfExists('bank_transactions');
  await knex.schema.dropTableIfExists('bank_accounts');
  await knex.schema.dropTableIfExists('plaid_link_tokens');
  await knex.schema.dropTableIfExists('plaid_items');
  await knex.schema.dropTableIfExists('export_history');
  await knex.schema.dropTableIfExists('report_schedules');
  await knex.schema.dropTableIfExists('export_templates');
}
