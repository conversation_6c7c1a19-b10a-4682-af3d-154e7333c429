import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Create permission audit log table
  await knex.schema.createTable("permission_audit_log", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.enum("target_type", ["user", "role"]).notNullable();
    table.uuid("target_id").notNullable(); // User ID or Role ID
    table.string("action", 50).notNullable(); // replace, add, remove, assign_roles, etc.
    table.jsonb("permissions").notNullable(); // Array of permissions affected
    table.jsonb("previous_permissions"); // Previous state for rollback
    table
      .uuid("changed_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamp("changed_at").defaultTo(knex.fn.now());
    table.string("ip_address", 45);
    table.text("user_agent");
    table.text("reason"); // Optional reason for the change

    table.index(["company_id", "changed_at"]);
    table.index(["target_type", "target_id"]);
    table.index(["changed_by", "changed_at"]);
    table.index(["action", "changed_at"]);
  });

  // Enhance existing roles table with new columns
  await knex.schema.alterTable("roles", (table) => {
    // Add new columns if they don't exist
    table
      .uuid("company_id")
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.boolean("is_system_role").defaultTo(false);
    table.integer("priority").defaultTo(50); // Higher priority = more permissions
    table
      .uuid("created_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
  });

  // Add new indexes
  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_roles_company_active
    ON roles(company_id, is_active);
  `);

  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_roles_priority
    ON roles(priority);
  `);

  // Drop the old unique constraint on name and add new one with company_id
  await knex.raw(`
    ALTER TABLE roles DROP CONSTRAINT IF EXISTS roles_name_unique;
  `);

  await knex.raw(`
    CREATE UNIQUE INDEX IF NOT EXISTS roles_company_name_unique
    ON roles(company_id, name);
  `);

  // Create role permissions table (if not exists)
  await knex.schema.hasTable("role_permissions").then((exists) => {
    if (!exists) {
      return knex.schema.createTable("role_permissions", (table) => {
        table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
        table
          .uuid("role_id")
          .notNullable()
          .references("id")
          .inTable("roles")
          .onDelete("CASCADE");
        table.string("permission", 100).notNullable();
        table.timestamps(true, true);

        table.index(["role_id"]);
        table.index(["permission"]);
        table.unique(["role_id", "permission"]);
      });
    }
  });

  // Create user roles table (if not exists)
  await knex.schema.hasTable("user_roles").then((exists) => {
    if (!exists) {
      return knex.schema.createTable("user_roles", (table) => {
        table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
        table
          .uuid("user_id")
          .notNullable()
          .references("id")
          .inTable("users")
          .onDelete("CASCADE");
        table
          .uuid("role_id")
          .notNullable()
          .references("id")
          .inTable("roles")
          .onDelete("CASCADE");
        table
          .uuid("assigned_by")
          .references("id")
          .inTable("users")
          .onDelete("SET NULL");
        table.timestamp("assigned_at").defaultTo(knex.fn.now());
        table.timestamp("expires_at"); // Optional expiration
        table.boolean("is_active").defaultTo(true);
        table.timestamps(true, true);

        table.index(["user_id", "is_active"]);
        table.index(["role_id"]);
        table.index(["assigned_by"]);
        table.index(["expires_at"]);
        table.unique(["user_id", "role_id"]);
      });
    }
  });

  // Create permission templates table (for quick role setup)
  await knex.schema.createTable("permission_templates", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table.string("name", 100).notNullable();
    table.text("description");
    table.string("category", 50).notNullable(); // ADMIN, MANAGER, ACCOUNTANT, etc.
    table.jsonb("permissions").notNullable(); // Array of permission keys
    table.boolean("is_system_template").defaultTo(false);
    table.boolean("is_active").defaultTo(true);
    table.timestamps(true, true);

    table.index(["category", "is_active"]);
    table.unique(["name"]);
  });

  // Insert default permission templates
  await knex("permission_templates").insert([
    {
      id: knex.raw("gen_random_uuid()"),
      name: "Super Admin",
      description: "Full system access with all permissions",
      category: "ADMIN",
      permissions: JSON.stringify([
        "transactions:read",
        "transactions:create",
        "transactions:update",
        "transactions:delete",
        "transactions:approve",
        "transactions:reverse",
        "accounts:read",
        "accounts:create",
        "accounts:update",
        "accounts:delete",
        "invoices:read",
        "invoices:create",
        "invoices:update",
        "invoices:delete",
        "invoices:send",
        "payments:read",
        "payments:create",
        "payments:refund",
        "reports:read",
        "reports:create",
        "reports:export",
        "analytics:read",
        "analytics:create",
        "reconciliation:read",
        "reconciliation:create",
        "reconciliation:approve",
        "budgets:read",
        "budgets:create",
        "budgets:update",
        "budgets:approve",
        "tax:read",
        "tax:create",
        "tax:update",
        "currencies:read",
        "currencies:create",
        "currencies:update",
        "users:read",
        "users:create",
        "users:update",
        "users:delete",
        "permissions:read",
        "permissions:create",
        "permissions:update",
        "permissions:assign",
        "permissions:admin",
        "company:read",
        "company:update",
        "company:admin",
      ]),
      is_system_template: true,
    },
    {
      id: knex.raw("gen_random_uuid()"),
      name: "Accountant",
      description: "Full accounting access with limited admin functions",
      category: "ACCOUNTANT",
      permissions: JSON.stringify([
        "transactions:read",
        "transactions:create",
        "transactions:update",
        "transactions:approve",
        "accounts:read",
        "accounts:create",
        "accounts:update",
        "invoices:read",
        "invoices:create",
        "invoices:update",
        "invoices:send",
        "payments:read",
        "payments:create",
        "reports:read",
        "reports:create",
        "reports:export",
        "analytics:read",
        "reconciliation:read",
        "reconciliation:create",
        "reconciliation:approve",
        "budgets:read",
        "budgets:create",
        "budgets:update",
        "tax:read",
        "tax:create",
        "tax:update",
        "currencies:read",
        "currencies:update",
      ]),
      is_system_template: true,
    },
    {
      id: knex.raw("gen_random_uuid()"),
      name: "Manager",
      description: "Management access with approval and oversight permissions",
      category: "MANAGER",
      permissions: JSON.stringify([
        "transactions:read",
        "transactions:approve",
        "transactions:reverse",
        "accounts:read",
        "invoices:read",
        "invoices:send",
        "payments:read",
        "reports:read",
        "reports:export",
        "analytics:read",
        "analytics:create",
        "reconciliation:read",
        "reconciliation:approve",
        "budgets:read",
        "budgets:approve",
        "tax:read",
        "currencies:read",
        "users:read",
      ]),
      is_system_template: true,
    },
    {
      id: knex.raw("gen_random_uuid()"),
      name: "Bookkeeper",
      description: "Basic data entry and transaction management",
      category: "BOOKKEEPER",
      permissions: JSON.stringify([
        "transactions:read",
        "transactions:create",
        "transactions:update",
        "accounts:read",
        "invoices:read",
        "invoices:create",
        "invoices:update",
        "payments:read",
        "payments:create",
        "reports:read",
        "reconciliation:read",
        "reconciliation:create",
        "budgets:read",
        "tax:read",
      ]),
      is_system_template: true,
    },
    {
      id: knex.raw("gen_random_uuid()"),
      name: "Read Only",
      description: "View-only access to financial data",
      category: "VIEWER",
      permissions: JSON.stringify([
        "transactions:read",
        "accounts:read",
        "invoices:read",
        "payments:read",
        "reports:read",
        "analytics:read",
        "reconciliation:read",
        "budgets:read",
        "tax:read",
        "currencies:read",
      ]),
      is_system_template: true,
    },
  ]);

  // Note: Default roles will be created when companies are created
  // This avoids foreign key constraint issues during migration

  // Note: Role permissions will be assigned when roles are created for companies

  // Create indexes for performance
  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_permission_audit_log_analysis
    ON permission_audit_log(company_id, target_type, changed_at);
  `);

  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_user_roles_lookup
    ON user_roles(user_id, is_active, expires_at);
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("permission_templates");
  await knex.schema.dropTableIfExists("user_roles");
  await knex.schema.dropTableIfExists("role_permissions");
  await knex.schema.dropTableIfExists("roles");
  await knex.schema.dropTableIfExists("permission_audit_log");
}
