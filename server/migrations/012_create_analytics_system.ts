import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Create analytics dashboards table
  await knex.schema.createTable("analytics_dashboards", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("name", 255).notNullable();
    table.text("description");
    table.jsonb("layout").notNullable(); // Dashboard layout and widget configuration
    table.boolean("is_default").defaultTo(false);
    table.boolean("is_public").defaultTo(false);
    table
      .uuid("created_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table
      .uuid("updated_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamps(true, true);

    table.index(["company_id", "is_default"]);
    table.index(["company_id", "is_public"]);
    table.unique(["company_id", "name"]);
  });

  // Create analytics widgets table
  await knex.schema.createTable("analytics_widgets", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("name", 255).notNullable();
    table.text("description");
    table
      .enum("widget_type", ["CHART", "KPI", "TABLE", "METRIC", "GAUGE"])
      .notNullable();
    table.jsonb("configuration").notNullable(); // Widget-specific configuration
    table.jsonb("data_source").notNullable(); // Data source configuration
    table.boolean("is_template").defaultTo(false);
    table
      .uuid("created_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamps(true, true);

    table.index(["company_id", "widget_type"]);
    table.index(["company_id", "is_template"]);
  });

  // Create analytics metrics cache table
  await knex.schema.createTable("analytics_metrics_cache", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("metric_key", 255).notNullable(); // Unique identifier for the metric
    table.string("metric_type", 100).notNullable(); // KPI, TREND, RATIO, etc.
    table.jsonb("metric_data").notNullable(); // Cached metric data
    table.jsonb("calculation_params"); // Parameters used for calculation
    table.timestamp("calculated_at").defaultTo(knex.fn.now());
    table.timestamp("expires_at").notNullable();
    table.integer("cache_hits").defaultTo(0);

    table.index(["company_id", "metric_key"]);
    table.index(["metric_type", "expires_at"]);
    table.unique(["company_id", "metric_key"]);
  });

  // Create analytics alerts table
  await knex.schema.createTable("analytics_alerts", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("name", 255).notNullable();
    table.text("description");
    table.string("metric_key", 255).notNullable();
    table
      .enum("condition_type", [
        "GREATER_THAN",
        "LESS_THAN",
        "EQUALS",
        "PERCENTAGE_CHANGE",
      ])
      .notNullable();
    table.decimal("threshold_value", 15, 4).notNullable();
    table
      .enum("frequency", ["REAL_TIME", "HOURLY", "DAILY", "WEEKLY"])
      .defaultTo("DAILY");
    table.jsonb("notification_config").notNullable(); // Email, SMS, webhook config
    table.boolean("is_active").defaultTo(true);
    table.timestamp("last_triggered_at");
    table
      .uuid("created_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamps(true, true);

    table.index(["company_id", "is_active"]);
    table.index(["metric_key", "is_active"]);
  });

  // Create analytics alert history table
  await knex.schema.createTable("analytics_alert_history", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("alert_id")
      .notNullable()
      .references("id")
      .inTable("analytics_alerts")
      .onDelete("CASCADE");
    table.decimal("metric_value", 15, 4).notNullable();
    table.decimal("threshold_value", 15, 4).notNullable();
    table.text("alert_message");
    table
      .enum("severity", ["LOW", "MEDIUM", "HIGH", "CRITICAL"])
      .defaultTo("MEDIUM");
    table.boolean("is_acknowledged").defaultTo(false);
    table
      .uuid("acknowledged_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamp("acknowledged_at");
    table.timestamp("triggered_at").defaultTo(knex.fn.now());

    table.index(["alert_id", "triggered_at"]);
    table.index(["is_acknowledged", "severity"]);
  });

  // Create analytics insights table
  await knex.schema.createTable("analytics_insights", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("insight_type", 100).notNullable(); // TREND, ANOMALY, RECOMMENDATION, etc.
    table.string("title", 255).notNullable();
    table.text("description").notNullable();
    table.jsonb("insight_data"); // Supporting data for the insight
    table.enum("priority", ["LOW", "MEDIUM", "HIGH"]).defaultTo("MEDIUM");
    table
      .enum("status", ["NEW", "VIEWED", "ACTED_ON", "DISMISSED"])
      .defaultTo("NEW");
    table.decimal("confidence_score", 5, 4); // 0-1 confidence score
    table.timestamp("valid_until"); // When this insight expires
    table
      .uuid("generated_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamps(true, true);

    table.index(["company_id", "status"]);
    table.index(["insight_type", "priority"]);
    table.index(["valid_until"]);
  });

  // Create analytics benchmarks table
  await knex.schema.createTable("analytics_benchmarks", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table.string("industry", 100).notNullable();
    table.string("company_size", 50).notNullable(); // SMALL, MEDIUM, LARGE
    table.string("metric_name", 255).notNullable();
    table.decimal("percentile_25", 15, 4);
    table.decimal("percentile_50", 15, 4); // Median
    table.decimal("percentile_75", 15, 4);
    table.decimal("percentile_90", 15, 4);
    table.string("data_source", 100); // Source of benchmark data
    table.date("data_period"); // Period the benchmark data represents
    table.timestamps(true, true);

    table.index(["industry", "company_size"]);
    table.index(["metric_name"]);
    table.unique(["industry", "company_size", "metric_name", "data_period"]);
  });

  // Create analytics forecasts table
  await knex.schema.createTable("analytics_forecasts", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("metric_name", 255).notNullable();
    table.string("forecast_method", 100).notNullable(); // LINEAR, SEASONAL, ML, etc.
    table.jsonb("forecast_data").notNullable(); // Forecast values and dates
    table.jsonb("model_parameters"); // Parameters used in the forecast model
    table.decimal("accuracy_score", 5, 4); // Model accuracy (0-1)
    table.integer("forecast_horizon_days").notNullable();
    table.timestamp("forecast_generated_at").defaultTo(knex.fn.now());
    table.timestamp("forecast_valid_until").notNullable();
    table
      .uuid("generated_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");

    table.index(["company_id", "metric_name"]);
    table.index(["forecast_valid_until"]);
  });

  // Note: Default dashboard templates will be created when companies are created
  // This avoids the foreign key constraint issue during migration

  // Insert sample benchmark data
  await knex("analytics_benchmarks").insert([
    {
      industry: "Technology",
      company_size: "SMALL",
      metric_name: "profit_margin",
      percentile_25: 8.5,
      percentile_50: 15.2,
      percentile_75: 22.8,
      percentile_90: 35.1,
      data_source: "Industry Survey 2024",
      data_period: "2024-01-01",
    },
    {
      industry: "Technology",
      company_size: "MEDIUM",
      metric_name: "profit_margin",
      percentile_25: 12.3,
      percentile_50: 18.7,
      percentile_75: 26.4,
      percentile_90: 38.9,
      data_source: "Industry Survey 2024",
      data_period: "2024-01-01",
    },
    {
      industry: "Retail",
      company_size: "SMALL",
      metric_name: "profit_margin",
      percentile_25: 3.2,
      percentile_50: 6.8,
      percentile_75: 12.1,
      percentile_90: 18.5,
      data_source: "Industry Survey 2024",
      data_period: "2024-01-01",
    },
  ]);

  // Create indexes for performance
  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_analytics_metrics_cache_lookup
    ON analytics_metrics_cache(company_id, metric_key, expires_at);
  `);

  await knex.raw(`
    CREATE INDEX IF NOT EXISTS idx_analytics_alerts_monitoring
    ON analytics_alerts(is_active, frequency, last_triggered_at);
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("analytics_forecasts");
  await knex.schema.dropTableIfExists("analytics_benchmarks");
  await knex.schema.dropTableIfExists("analytics_insights");
  await knex.schema.dropTableIfExists("analytics_alert_history");
  await knex.schema.dropTableIfExists("analytics_alerts");
  await knex.schema.dropTableIfExists("analytics_metrics_cache");
  await knex.schema.dropTableIfExists("analytics_widgets");
  await knex.schema.dropTableIfExists("analytics_dashboards");
}
