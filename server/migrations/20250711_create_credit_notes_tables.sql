-- Credit Notes Management Tables
-- These tables handle customer credit notes and refunds

-- Credit notes table
CREATE TABLE IF NOT EXISTS credit_notes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES contacts(id) ON DELETE RESTRICT,
    invoice_id UUID REFERENCES invoices(id) ON DELETE RESTRICT,
    credit_note_number VARCHAR(50) NOT NULL,
    credit_date DATE NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    exchange_rate DECIMAL(15,8) NOT NULL DEFAULT 1.0,
    
    -- Amounts
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    applied_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    remaining_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    
    -- Status and workflow
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' CHECK (status IN (
        'DRAFT', 'ISSUED', 'APPLIED', 'PARTIALLY_APPLIED', 'REFUNDED', 'CANCELLED'
    )),
    
    -- Credit reason
    credit_reason VARCHAR(20) NOT NULL CHECK (credit_reason IN (
        'RETURN', 'DEFECTIVE', 'OVERCHARGE', 'DISCOUNT', 'GOODWILL', 'ERROR', 'OTHER'
    )),
    reason_description TEXT,
    
    -- Customer information
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255),
    customer_phone VARCHAR(50),
    customer_address TEXT,
    
    -- Addresses and details
    billing_address TEXT,
    shipping_address TEXT,
    notes TEXT,
    terms_and_conditions TEXT,
    
    -- Refund tracking
    refund_method VARCHAR(20) CHECK (refund_method IN (
        'CASH', 'BANK_TRANSFER', 'CREDIT_CARD', 'STORE_CREDIT', 'APPLY_TO_ACCOUNT'
    )),
    refund_reference VARCHAR(100),
    refunded_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit fields
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(company_id, credit_note_number)
);

-- Credit note line items
CREATE TABLE IF NOT EXISTS credit_note_line_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    credit_note_id UUID NOT NULL REFERENCES credit_notes(id) ON DELETE CASCADE,
    invoice_line_item_id UUID REFERENCES invoice_line_items(id),
    line_number INTEGER NOT NULL,
    
    -- Item details
    inventory_item_id UUID REFERENCES inventory_items(id),
    item_code VARCHAR(50),
    description TEXT NOT NULL,
    unit_of_measure VARCHAR(20),
    
    -- Quantities and pricing
    quantity DECIMAL(15,4) NOT NULL DEFAULT 1,
    unit_price DECIMAL(15,2) NOT NULL DEFAULT 0,
    discount_percentage DECIMAL(5,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    line_total DECIMAL(15,2) NOT NULL DEFAULT 0,
    
    -- Tax information
    tax_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_code VARCHAR(20),
    
    -- Return details
    return_reason VARCHAR(20) CHECK (return_reason IN (
        'DEFECTIVE', 'WRONG_ITEM', 'DAMAGED', 'NOT_NEEDED', 'OVERCHARGE', 'OTHER'
    )),
    return_condition VARCHAR(20) CHECK (return_condition IN (
        'NEW', 'USED', 'DAMAGED', 'DEFECTIVE', 'INCOMPLETE'
    )),
    
    -- Additional details
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(credit_note_id, line_number)
);

-- Credit note attachments
CREATE TABLE IF NOT EXISTS credit_note_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    credit_note_id UUID NOT NULL REFERENCES credit_notes(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_type VARCHAR(50),
    file_size INTEGER,
    attachment_type VARCHAR(20) DEFAULT 'DOCUMENT' CHECK (attachment_type IN (
        'DOCUMENT', 'PHOTO', 'RETURN_RECEIPT', 'INSPECTION_REPORT', 'OTHER'
    )),
    uploaded_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Credit note history (audit trail)
CREATE TABLE IF NOT EXISTS credit_note_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    credit_note_id UUID NOT NULL REFERENCES credit_notes(id) ON DELETE CASCADE,
    old_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    old_amount DECIMAL(15,2),
    new_amount DECIMAL(15,2),
    notes TEXT,
    changed_by UUID NOT NULL REFERENCES users(id),
    changed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Credit note applications (tracking where credits are applied)
CREATE TABLE IF NOT EXISTS credit_note_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    credit_note_id UUID NOT NULL REFERENCES credit_notes(id) ON DELETE CASCADE,
    invoice_id UUID REFERENCES invoices(id) ON DELETE CASCADE,
    application_date DATE NOT NULL,
    applied_amount DECIMAL(15,2) NOT NULL,
    notes TEXT,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Credit note comments/notes
CREATE TABLE IF NOT EXISTS credit_note_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    credit_note_id UUID NOT NULL REFERENCES credit_notes(id) ON DELETE CASCADE,
    comment TEXT NOT NULL,
    is_internal BOOLEAN NOT NULL DEFAULT true,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_credit_notes_company_id ON credit_notes(company_id);
CREATE INDEX IF NOT EXISTS idx_credit_notes_customer_id ON credit_notes(customer_id);
CREATE INDEX IF NOT EXISTS idx_credit_notes_invoice_id ON credit_notes(invoice_id);
CREATE INDEX IF NOT EXISTS idx_credit_notes_status ON credit_notes(status);
CREATE INDEX IF NOT EXISTS idx_credit_notes_credit_date ON credit_notes(credit_date);
CREATE INDEX IF NOT EXISTS idx_credit_notes_credit_reason ON credit_notes(credit_reason);

CREATE INDEX IF NOT EXISTS idx_credit_note_line_items_credit_note_id ON credit_note_line_items(credit_note_id);
CREATE INDEX IF NOT EXISTS idx_credit_note_line_items_invoice_line_item_id ON credit_note_line_items(invoice_line_item_id);
CREATE INDEX IF NOT EXISTS idx_credit_note_line_items_inventory_item_id ON credit_note_line_items(inventory_item_id);

CREATE INDEX IF NOT EXISTS idx_credit_note_attachments_credit_note_id ON credit_note_attachments(credit_note_id);
CREATE INDEX IF NOT EXISTS idx_credit_note_history_credit_note_id ON credit_note_history(credit_note_id);
CREATE INDEX IF NOT EXISTS idx_credit_note_history_changed_at ON credit_note_history(changed_at);

CREATE INDEX IF NOT EXISTS idx_credit_note_applications_credit_note_id ON credit_note_applications(credit_note_id);
CREATE INDEX IF NOT EXISTS idx_credit_note_applications_invoice_id ON credit_note_applications(invoice_id);
CREATE INDEX IF NOT EXISTS idx_credit_note_applications_application_date ON credit_note_applications(application_date);

CREATE INDEX IF NOT EXISTS idx_credit_note_comments_credit_note_id ON credit_note_comments(credit_note_id);

-- Triggers for updated_at
CREATE OR REPLACE FUNCTION update_credit_notes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_credit_notes_updated_at
    BEFORE UPDATE ON credit_notes
    FOR EACH ROW
    EXECUTE FUNCTION update_credit_notes_updated_at();

CREATE TRIGGER trigger_credit_note_line_items_updated_at
    BEFORE UPDATE ON credit_note_line_items
    FOR EACH ROW
    EXECUTE FUNCTION update_credit_notes_updated_at();

CREATE TRIGGER trigger_credit_note_attachments_updated_at
    BEFORE UPDATE ON credit_note_attachments
    FOR EACH ROW
    EXECUTE FUNCTION update_credit_notes_updated_at();

-- Function to update credit note totals
CREATE OR REPLACE FUNCTION update_credit_note_totals(credit_note_id_param UUID)
RETURNS VOID AS $$
DECLARE
    credit_subtotal DECIMAL(15,2);
    credit_tax_amount DECIMAL(15,2);
    credit_discount_amount DECIMAL(15,2);
    credit_total_amount DECIMAL(15,2);
BEGIN
    -- Calculate totals from line items
    SELECT 
        COALESCE(SUM(line_total), 0),
        COALESCE(SUM(tax_amount), 0),
        COALESCE(SUM(discount_amount), 0)
    INTO credit_subtotal, credit_tax_amount, credit_discount_amount
    FROM credit_note_line_items
    WHERE credit_note_id = credit_note_id_param;
    
    -- Calculate total amount
    credit_total_amount := credit_subtotal + credit_tax_amount - credit_discount_amount;
    
    -- Update credit note totals
    UPDATE credit_notes SET
        subtotal = credit_subtotal,
        tax_amount = credit_tax_amount,
        discount_amount = credit_discount_amount,
        total_amount = credit_total_amount,
        updated_at = NOW()
    WHERE id = credit_note_id_param;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update credit note totals when line items change
CREATE OR REPLACE FUNCTION trigger_update_credit_note_totals()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        PERFORM update_credit_note_totals(OLD.credit_note_id);
        RETURN OLD;
    ELSE
        PERFORM update_credit_note_totals(NEW.credit_note_id);
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_credit_note_line_items_totals
    AFTER INSERT OR UPDATE OR DELETE ON credit_note_line_items
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_credit_note_totals();

-- Function to update credit note remaining amount
CREATE OR REPLACE FUNCTION update_credit_note_remaining_amount()
RETURNS TRIGGER AS $$
DECLARE
    total_applied DECIMAL(15,2);
    credit_total DECIMAL(15,2);
    credit_note_id_param UUID;
BEGIN
    -- Get credit note ID
    IF TG_OP = 'DELETE' THEN
        credit_note_id_param := OLD.credit_note_id;
    ELSE
        credit_note_id_param := NEW.credit_note_id;
    END IF;
    
    -- Calculate total applied amount
    SELECT COALESCE(SUM(applied_amount), 0) INTO total_applied
    FROM credit_note_applications
    WHERE credit_note_id = credit_note_id_param;
    
    -- Get credit note total
    SELECT total_amount INTO credit_total
    FROM credit_notes
    WHERE id = credit_note_id_param;
    
    -- Update remaining amount and status
    UPDATE credit_notes SET
        applied_amount = total_applied,
        remaining_amount = credit_total - total_applied,
        status = CASE 
            WHEN total_applied = 0 THEN 'ISSUED'
            WHEN total_applied >= credit_total THEN 'APPLIED'
            ELSE 'PARTIALLY_APPLIED'
        END,
        updated_at = NOW()
    WHERE id = credit_note_id_param;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_credit_note_remaining_amount
    AFTER INSERT OR UPDATE OR DELETE ON credit_note_applications
    FOR EACH ROW
    EXECUTE FUNCTION update_credit_note_remaining_amount();

-- Function to generate credit note number
CREATE OR REPLACE FUNCTION generate_credit_note_number(company_id_param UUID)
RETURNS VARCHAR(50) AS $$
DECLARE
    last_number INTEGER;
    new_number VARCHAR(50);
BEGIN
    -- Get the last credit note number for the company
    SELECT COALESCE(
        MAX(CAST(REGEXP_REPLACE(credit_note_number, '[^0-9]', '', 'g') AS INTEGER)), 
        0
    ) INTO last_number
    FROM credit_notes 
    WHERE company_id = company_id_param;
    
    -- Generate new number
    new_number := 'CN-' || LPAD((last_number + 1)::TEXT, 6, '0');
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Function to validate credit note applications
CREATE OR REPLACE FUNCTION validate_credit_note_application()
RETURNS TRIGGER AS $$
DECLARE
    credit_remaining DECIMAL(15,2);
    invoice_balance DECIMAL(15,2);
BEGIN
    -- Get credit note remaining amount
    SELECT remaining_amount INTO credit_remaining
    FROM credit_notes
    WHERE id = NEW.credit_note_id;
    
    -- Validate application amount doesn't exceed remaining credit
    IF NEW.applied_amount > credit_remaining THEN
        RAISE EXCEPTION 'Application amount (%) exceeds remaining credit (%)', 
            NEW.applied_amount, credit_remaining;
    END IF;
    
    -- If applying to an invoice, validate it doesn't exceed invoice balance
    IF NEW.invoice_id IS NOT NULL THEN
        -- This would need to be implemented when invoice system is complete
        -- For now, just ensure positive amount
        IF NEW.applied_amount <= 0 THEN
            RAISE EXCEPTION 'Application amount must be positive';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_validate_credit_note_application
    BEFORE INSERT OR UPDATE ON credit_note_applications
    FOR EACH ROW
    EXECUTE FUNCTION validate_credit_note_application();
