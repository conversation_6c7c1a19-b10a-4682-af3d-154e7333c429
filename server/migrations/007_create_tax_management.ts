import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create tax type enum
  await knex.raw(`
    CREATE TYPE tax_type AS ENUM (
      'sales_tax',
      'vat',
      'gst',
      'income_tax',
      'property_tax',
      'excise_tax',
      'customs_duty',
      'other'
    );
  `);

  // Create tax calculation method enum
  await knex.raw(`
    CREATE TYPE tax_calculation_method AS ENUM (
      'exclusive',
      'inclusive',
      'compound'
    );
  `);

  // Create tax rounding method enum
  await knex.raw(`
    CREATE TYPE tax_rounding_method AS ENUM (
      'round',
      'round_up',
      'round_down',
      'truncate'
    );
  `);

  // Create tax rates table
  await knex.schema.createTable('tax_rates', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.string('name', 255).notNullable();
    table.text('description');
    table.decimal('rate', 8, 5).notNullable(); // Percentage (e.g., 8.25000 for 8.25%)
    table.specificType('type', 'tax_type').notNullable();
    table.string('jurisdiction', 255).notNullable(); // e.g., "Federal", "California", "Los Angeles County"
    table.string('jurisdiction_code', 10); // e.g., "US", "CA", "LAC"
    table.boolean('is_active').defaultTo(true);
    table.boolean('is_default').defaultTo(false);
    table.date('effective_date').notNullable();
    table.date('expiry_date');
    table.boolean('applicable_to_products').defaultTo(true);
    table.boolean('applicable_to_services').defaultTo(true);
    table.jsonb('settings').defaultTo('{}');
    table.timestamps(true, true);
    
    table.unique(['company_id', 'name']);
    table.index(['company_id', 'type']);
    table.index(['jurisdiction']);
    table.index(['is_active']);
    table.index(['effective_date']);
  });

  // Create tax categories table
  await knex.schema.createTable('tax_categories', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.string('name', 255).notNullable();
    table.text('description');
    table.uuid('default_tax_rate_id').references('id').inTable('tax_rates').onDelete('SET NULL');
    table.boolean('is_active').defaultTo(true);
    table.jsonb('settings').defaultTo('{}');
    table.timestamps(true, true);
    
    table.unique(['company_id', 'name']);
    table.index(['company_id']);
    table.index(['is_active']);
  });

  // Create tax settings table
  await knex.schema.createTable('tax_settings', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.uuid('default_tax_rate_id').references('id').inTable('tax_rates').onDelete('SET NULL');
    table.specificType('tax_calculation_method', 'tax_calculation_method').defaultTo('exclusive');
    table.specificType('rounding_method', 'tax_rounding_method').defaultTo('round');
    table.integer('rounding_precision').defaultTo(2); // Number of decimal places
    table.boolean('include_tax_in_price').defaultTo(false);
    table.boolean('enable_tax_exemptions').defaultTo(true);
    table.boolean('require_tax_certificates').defaultTo(false);
    table.boolean('auto_calculate_tax').defaultTo(true);
    table.string('tax_reporting_currency', 3).defaultTo('USD');
    table.jsonb('settings').defaultTo('{}');
    table.timestamps(true, true);
    
    table.unique(['company_id']);
  });

  // Create tax exemptions table
  await knex.schema.createTable('tax_exemptions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.uuid('contact_id').references('id').inTable('contacts').onDelete('CASCADE');
    table.uuid('tax_rate_id').references('id').inTable('tax_rates').onDelete('CASCADE');
    table.string('exemption_type', 50).notNullable(); // FULL, PARTIAL, CERTIFICATE
    table.string('certificate_number', 100);
    table.date('effective_date').notNullable();
    table.date('expiry_date');
    table.text('reason');
    table.boolean('is_active').defaultTo(true);
    table.jsonb('metadata').defaultTo('{}');
    table.timestamps(true, true);
    
    table.index(['company_id']);
    table.index(['contact_id']);
    table.index(['tax_rate_id']);
    table.index(['is_active']);
  });

  // Create tax calculations table (for audit trail)
  await knex.schema.createTable('tax_calculations', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.string('entity_type', 50).notNullable(); // INVOICE, TRANSACTION, QUOTE
    table.uuid('entity_id').notNullable();
    table.uuid('tax_rate_id').references('id').inTable('tax_rates').onDelete('RESTRICT');
    table.decimal('taxable_amount', 15, 2).notNullable();
    table.decimal('tax_amount', 15, 2).notNullable();
    table.decimal('tax_rate', 8, 5).notNullable();
    table.string('calculation_method', 20).notNullable();
    table.jsonb('calculation_details').defaultTo('{}');
    table.timestamps(true, true);
    
    table.index(['company_id']);
    table.index(['entity_type', 'entity_id']);
    table.index(['tax_rate_id']);
  });

  // Create tax jurisdictions table
  await knex.schema.createTable('tax_jurisdictions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.string('name', 255).notNullable();
    table.string('code', 10).notNullable();
    table.string('type', 50).notNullable(); // FEDERAL, STATE, COUNTY, CITY
    table.uuid('parent_jurisdiction_id').references('id').inTable('tax_jurisdictions').onDelete('SET NULL');
    table.boolean('is_active').defaultTo(true);
    table.jsonb('settings').defaultTo('{}');
    table.timestamps(true, true);
    
    table.unique(['company_id', 'code']);
    table.index(['company_id']);
    table.index(['type']);
    table.index(['parent_jurisdiction_id']);
  });

  // Create tax reports table
  await knex.schema.createTable('tax_reports', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.string('report_type', 50).notNullable(); // SALES_TAX, VAT_RETURN, GST_RETURN
    table.string('period_type', 20).notNullable(); // MONTHLY, QUARTERLY, YEARLY
    table.date('period_start').notNullable();
    table.date('period_end').notNullable();
    table.string('status', 20).defaultTo('DRAFT'); // DRAFT, GENERATED, FILED, PAID
    table.decimal('total_sales', 15, 2).defaultTo(0);
    table.decimal('taxable_sales', 15, 2).defaultTo(0);
    table.decimal('tax_collected', 15, 2).defaultTo(0);
    table.decimal('tax_paid', 15, 2).defaultTo(0);
    table.decimal('tax_due', 15, 2).defaultTo(0);
    table.date('due_date');
    table.date('filed_date');
    table.date('paid_date');
    table.jsonb('report_data').defaultTo('{}');
    table.uuid('generated_by').references('id').inTable('users').onDelete('SET NULL');
    table.timestamps(true, true);
    
    table.index(['company_id']);
    table.index(['report_type']);
    table.index(['period_start', 'period_end']);
    table.index(['status']);
    table.index(['due_date']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('tax_reports');
  await knex.schema.dropTableIfExists('tax_jurisdictions');
  await knex.schema.dropTableIfExists('tax_calculations');
  await knex.schema.dropTableIfExists('tax_exemptions');
  await knex.schema.dropTableIfExists('tax_settings');
  await knex.schema.dropTableIfExists('tax_categories');
  await knex.schema.dropTableIfExists('tax_rates');
  await knex.raw('DROP TYPE IF EXISTS tax_rounding_method');
  await knex.raw('DROP TYPE IF EXISTS tax_calculation_method');
  await knex.raw('DROP TYPE IF EXISTS tax_type');
}
