import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create account types enum
  await knex.raw(`
    CREATE TYPE account_type AS ENUM (
      'ASSET',
      'LIABILITY', 
      'EQUITY',
      'REVENUE',
      'EXPENSE'
    );
  `);

  // Create account subtypes enum
  await knex.raw(`
    CREATE TYPE account_subtype AS ENUM (
      'CURRENT_ASSET',
      'NON_CURRENT_ASSET',
      'CURRENT_LIABILITY',
      'NON_CURRENT_LIABILITY',
      'OWNER_EQUITY',
      'RETAINED_EARNINGS',
      'OPERATING_REVENUE',
      'NON_OPERATING_REVENUE',
      'OPERATING_EXPENSE',
      'NON_OPERATING_EXPENSE',
      'COST_OF_GOODS_SOLD'
    );
  `);

  // Create chart of accounts table
  await knex.schema.createTable('accounts', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('company_id').references('id').inTable('companies').onDelete('CASCADE');
    table.string('code', 20).notNullable();
    table.string('name', 255).notNullable();
    table.text('description');
    table.specificType('account_type', 'account_type').notNullable();
    table.specificType('account_subtype', 'account_subtype');
    table.uuid('parent_account_id').references('id').inTable('accounts').onDelete('SET NULL');
    table.boolean('is_active').defaultTo(true);
    table.boolean('is_system').defaultTo(false);
    table.decimal('opening_balance', 15, 2).defaultTo(0);
    table.string('currency', 3).defaultTo('USD');
    table.jsonb('tax_settings').defaultTo('{}');
    table.jsonb('metadata').defaultTo('{}');
    table.timestamps(true, true);
    
    table.unique(['company_id', 'code']);
    table.index(['company_id', 'account_type']);
    table.index(['parent_account_id']);
  });

  // Create account balances table for historical tracking
  await knex.schema.createTable('account_balances', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('account_id').references('id').inTable('accounts').onDelete('CASCADE');
    table.date('balance_date').notNullable();
    table.decimal('debit_balance', 15, 2).defaultTo(0);
    table.decimal('credit_balance', 15, 2).defaultTo(0);
    table.decimal('net_balance', 15, 2).defaultTo(0);
    table.timestamps(true, true);
    
    table.unique(['account_id', 'balance_date']);
    table.index(['balance_date']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('account_balances');
  await knex.schema.dropTableIfExists('accounts');
  await knex.raw('DROP TYPE IF EXISTS account_subtype');
  await knex.raw('DROP TYPE IF EXISTS account_type');
}
