import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Bank Reconciliations Table
  await knex.schema.createTable("bank_reconciliations", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table
      .uuid("bank_account_id")
      .notNullable()
      .references("id")
      .inTable("accounts")
      .onDelete("CASCADE");
    table.date("statement_date").notNullable();
    table.decimal("statement_beginning_balance", 15, 2).notNullable();
    table.decimal("statement_ending_balance", 15, 2).notNullable();
    table.decimal("reconciled_balance", 15, 2).defaultTo(0);
    table.decimal("difference", 15, 2).defaultTo(0);
    table
      .enum("status", ["IN_PROGRESS", "COMPLETED", "REVIEWED", "LOCKED"])
      .defaultTo("IN_PROGRESS");
    table
      .uuid("reconciled_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table
      .uuid("reviewed_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table
      .uuid("locked_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamp("reconciled_at");
    table.timestamp("reviewed_at");
    table.timestamp("locked_at");
    table.text("notes");
    table.timestamps(true, true);

    table.index(["company_id", "bank_account_id"]);
    table.index(["statement_date"]);
    table.index(["status"]);
    table.unique(["bank_account_id", "statement_date"]);
  });

  // Bank Reconciliation Items Table
  await knex.schema.createTable("bank_reconciliation_items", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("reconciliation_id")
      .notNullable()
      .references("id")
      .inTable("bank_reconciliations")
      .onDelete("CASCADE");
    table
      .uuid("transaction_id")
      .references("id")
      .inTable("transactions")
      .onDelete("SET NULL");
    table.uuid("bank_transaction_id"); // Will add foreign key constraint in later migration
    table
      .enum("type", ["TRANSACTION", "BANK_TRANSACTION", "ADJUSTMENT"])
      .notNullable();
    table.text("description").notNullable();
    table.decimal("amount", 15, 2).notNullable();
    table.date("transaction_date").notNullable();
    table.string("check_number");
    table.string("reference");
    table.boolean("is_reconciled").defaultTo(false);
    table.timestamp("reconciled_at");
    table
      .uuid("reconciled_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.decimal("match_confidence", 5, 2);
    table.uuid("matched_transaction_id");
    table.text("adjustment_reason");
    table.timestamps(true, true);

    table.index(["reconciliation_id"]);
    table.index(["transaction_id"]);
    table.index(["bank_transaction_id"]);
    table.index(["is_reconciled"]);
    table.index(["transaction_date"]);
  });

  // Reconciliation Rules Table
  await knex.schema.createTable("reconciliation_rules", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("company_id")
      .notNullable()
      .references("id")
      .inTable("companies")
      .onDelete("CASCADE");
    table.string("name").notNullable();
    table.text("description");
    table.boolean("is_active").defaultTo(true);
    table.integer("priority").defaultTo(0);
    table.jsonb("conditions").notNullable();
    table.jsonb("actions").notNullable();
    table
      .uuid("created_by")
      .notNullable()
      .references("id")
      .inTable("users")
      .onDelete("RESTRICT");
    table.timestamps(true, true);

    table.index(["company_id", "is_active"]);
    table.index(["priority"]);
  });

  // Reconciliation Adjustments Table
  await knex.schema.createTable("reconciliation_adjustments", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table
      .uuid("reconciliation_id")
      .notNullable()
      .references("id")
      .inTable("bank_reconciliations")
      .onDelete("CASCADE");
    table
      .enum("type", ["BANK_ERROR", "BOOK_ERROR", "TIMING_DIFFERENCE", "OTHER"])
      .notNullable();
    table.text("description").notNullable();
    table.decimal("amount", 15, 2).notNullable();
    table
      .uuid("account_id")
      .notNullable()
      .references("id")
      .inTable("accounts")
      .onDelete("RESTRICT");
    table.string("reference");
    table.text("notes");
    table
      .uuid("created_by")
      .notNullable()
      .references("id")
      .inTable("users")
      .onDelete("RESTRICT");
    table.timestamps(true, true);

    table.index(["reconciliation_id"]);
    table.index(["account_id"]);
    table.index(["type"]);
  });

  // Reconciliation Matches Table (for AI matching history)
  await knex.schema.createTable("reconciliation_matches", (table) => {
    table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
    table.uuid("bank_transaction_id"); // Will add foreign key constraint in later migration
    table
      .uuid("transaction_id")
      .notNullable()
      .references("id")
      .inTable("transactions")
      .onDelete("CASCADE");
    table
      .enum("match_type", ["EXACT", "FUZZY", "MANUAL", "RULE_BASED"])
      .notNullable();
    table.decimal("confidence", 5, 2).notNullable();
    table.jsonb("matching_factors").notNullable();
    table
      .enum("suggested_action", ["AUTO_MATCH", "REVIEW", "MANUAL_REVIEW"])
      .notNullable();
    table.boolean("is_accepted").defaultTo(false);
    table
      .uuid("accepted_by")
      .references("id")
      .inTable("users")
      .onDelete("SET NULL");
    table.timestamp("accepted_at");
    table.timestamps(true, true);

    table.index(["bank_transaction_id"]);
    table.index(["transaction_id"]);
    table.index(["match_type"]);
    table.index(["confidence"]);
    table.unique(["bank_transaction_id", "transaction_id"]);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("reconciliation_matches");
  await knex.schema.dropTableIfExists("reconciliation_adjustments");
  await knex.schema.dropTableIfExists("reconciliation_rules");
  await knex.schema.dropTableIfExists("bank_reconciliation_items");
  await knex.schema.dropTableIfExists("bank_reconciliations");
}
