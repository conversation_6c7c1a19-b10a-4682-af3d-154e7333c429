import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  console.log("🇹🇿 Creating TRA Integration tables...");

  // TRA VAT Returns table
  const traVatReturnsExists = await knex.schema.hasTable("tra_vat_returns");
  if (!traVatReturnsExists) {
    await knex.schema.createTable("tra_vat_returns", (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      table.uuid("company_id").notNullable().references("id").inTable("companies");
      table.string("return_period", 7).notNullable(); // YYYY-MM format
      table.string("tra_reference_number", 50).nullable();
      table.enum("status", ["DRAFT", "SUBMITTED", "ACCEPTED", "REJECTED", "AMENDED"]).defaultTo("DRAFT");
      
      // VAT Calculations
      table.decimal("total_sales", 15, 2).defaultTo(0);
      table.decimal("vat_on_sales", 15, 2).defaultTo(0);
      table.decimal("total_purchases", 15, 2).defaultTo(0);
      table.decimal("vat_on_purchases", 15, 2).defaultTo(0);
      table.decimal("net_vat_payable", 15, 2).defaultTo(0);
      table.decimal("vat_refund_claimed", 15, 2).defaultTo(0);
      
      // Zero-rated and exempt supplies
      table.decimal("zero_rated_supplies", 15, 2).defaultTo(0);
      table.decimal("exempt_supplies", 15, 2).defaultTo(0);
      
      // Import VAT
      table.decimal("import_vat", 15, 2).defaultTo(0);
      
      // Penalties and interest
      table.decimal("penalties", 15, 2).defaultTo(0);
      table.decimal("interest", 15, 2).defaultTo(0);
      
      // Filing information
      table.timestamp("due_date").nullable();
      table.timestamp("submitted_date").nullable();
      table.timestamp("accepted_date").nullable();
      table.text("rejection_reason").nullable();
      table.jsonb("tra_response").nullable();
      table.jsonb("supporting_documents").nullable();
      
      // Audit fields
      table.uuid("created_by").nullable().references("id").inTable("users");
      table.uuid("submitted_by").nullable().references("id").inTable("users");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      
      // Indexes
      table.index(["company_id", "return_period"]);
      table.index(["status", "due_date"]);
      table.index("created_at");
      table.unique(["company_id", "return_period"]);
    });
    console.log("✅ tra_vat_returns table created");
  }

  // TRA Withholding Tax table
  const traWithholdingTaxExists = await knex.schema.hasTable("tra_withholding_tax");
  if (!traWithholdingTaxExists) {
    await knex.schema.createTable("tra_withholding_tax", (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      table.uuid("company_id").notNullable().references("id").inTable("companies");
      table.uuid("transaction_id").nullable().references("id").inTable("transactions");
      table.uuid("contact_id").nullable().references("id").inTable("contacts");
      
      // WHT Details
      table.string("wht_certificate_number", 50).nullable();
      table.decimal("gross_amount", 15, 2).notNullable();
      table.decimal("wht_rate", 5, 2).notNullable(); // Percentage
      table.decimal("wht_amount", 15, 2).notNullable();
      table.decimal("net_amount", 15, 2).notNullable();
      
      // WHT Categories (TRA defined)
      table.enum("wht_category", [
        "PROFESSIONAL_SERVICES", // 5%
        "CONSULTANCY", // 5%
        "MANAGEMENT_FEES", // 5%
        "TECHNICAL_SERVICES", // 5%
        "RENT", // 10%
        "INTEREST", // 10%
        "DIVIDENDS", // 10%
        "ROYALTIES", // 15%
        "COMMISSIONS", // 5%
        "TRANSPORT", // 5%
        "CONSTRUCTION", // 5%
        "SECURITY_SERVICES", // 5%
        "CLEANING_SERVICES", // 5%
        "CATERING", // 5%
        "OTHER"
      ]).notNullable();
      
      table.string("service_description", 500).nullable();
      table.date("service_date").notNullable();
      table.date("payment_date").notNullable();
      
      // TRA Submission
      table.enum("submission_status", ["PENDING", "SUBMITTED", "ACCEPTED", "REJECTED"]).defaultTo("PENDING");
      table.string("tra_reference", 50).nullable();
      table.timestamp("submitted_date").nullable();
      table.jsonb("tra_response").nullable();
      
      // Audit fields
      table.uuid("created_by").nullable().references("id").inTable("users");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      
      // Indexes
      table.index(["company_id", "service_date"]);
      table.index(["wht_category", "service_date"]);
      table.index(["submission_status"]);
      table.index("created_at");
    });
    console.log("✅ tra_withholding_tax table created");
  }

  // TRA Tax Rates Configuration table
  const traTaxRatesExists = await knex.schema.hasTable("tra_tax_rates");
  if (!traTaxRatesExists) {
    await knex.schema.createTable("tra_tax_rates", (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      table.string("tax_type", 50).notNullable(); // VAT, WHT, etc.
      table.string("category", 100).notNullable();
      table.decimal("rate", 5, 2).notNullable(); // Percentage
      table.text("description").nullable();
      table.decimal("threshold_amount", 15, 2).nullable(); // Minimum amount for application
      table.boolean("is_active").defaultTo(true);
      table.date("effective_from").notNullable();
      table.date("effective_to").nullable();
      table.jsonb("conditions").nullable(); // Additional conditions
      
      // Audit fields
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      
      // Indexes
      table.index(["tax_type", "category"]);
      table.index(["is_active", "effective_from"]);
      table.index("effective_from");
    });
    console.log("✅ tra_tax_rates table created");
  }

  // TRA API Integration Log table
  const traApiLogExists = await knex.schema.hasTable("tra_api_integration_log");
  if (!traApiLogExists) {
    await knex.schema.createTable("tra_api_integration_log", (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      table.uuid("company_id").nullable().references("id").inTable("companies");
      table.string("endpoint", 200).notNullable();
      table.enum("method", ["GET", "POST", "PUT", "DELETE"]).notNullable();
      table.jsonb("request_payload").nullable();
      table.jsonb("response_payload").nullable();
      table.integer("response_status").nullable();
      table.text("error_message").nullable();
      table.integer("response_time_ms").nullable();
      table.string("correlation_id", 100).nullable();
      table.enum("operation_type", [
        "VAT_SUBMISSION",
        "WHT_SUBMISSION", 
        "TAX_RATE_SYNC",
        "CERTIFICATE_DOWNLOAD",
        "STATUS_CHECK",
        "AUTHENTICATION"
      ]).notNullable();
      
      // Audit fields
      table.uuid("initiated_by").nullable().references("id").inTable("users");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      
      // Indexes
      table.index(["company_id", "created_at"]);
      table.index(["operation_type", "created_at"]);
      table.index(["response_status", "created_at"]);
      table.index("correlation_id");
    });
    console.log("✅ tra_api_integration_log table created");
  }

  // Insert default TRA tax rates
  await knex("tra_tax_rates").insert([
    {
      tax_type: "VAT",
      category: "STANDARD",
      rate: 18.00,
      description: "Standard VAT rate for most goods and services",
      threshold_amount: *********, // 100M TZS annual turnover
      effective_from: "2023-01-01"
    },
    {
      tax_type: "VAT",
      category: "ZERO_RATED",
      rate: 0.00,
      description: "Zero-rated supplies (exports, basic food items, etc.)",
      effective_from: "2023-01-01"
    },
    {
      tax_type: "WHT",
      category: "PROFESSIONAL_SERVICES",
      rate: 5.00,
      description: "Withholding tax on professional services",
      threshold_amount: 30000, // 30K TZS
      effective_from: "2023-01-01"
    },
    {
      tax_type: "WHT",
      category: "CONSULTANCY",
      rate: 5.00,
      description: "Withholding tax on consultancy services",
      threshold_amount: 30000,
      effective_from: "2023-01-01"
    },
    {
      tax_type: "WHT",
      category: "RENT",
      rate: 10.00,
      description: "Withholding tax on rental payments",
      threshold_amount: 30000,
      effective_from: "2023-01-01"
    },
    {
      tax_type: "WHT",
      category: "INTEREST",
      rate: 10.00,
      description: "Withholding tax on interest payments",
      threshold_amount: 30000,
      effective_from: "2023-01-01"
    },
    {
      tax_type: "WHT",
      category: "DIVIDENDS",
      rate: 10.00,
      description: "Withholding tax on dividend payments",
      threshold_amount: 30000,
      effective_from: "2023-01-01"
    },
    {
      tax_type: "WHT",
      category: "ROYALTIES",
      rate: 15.00,
      description: "Withholding tax on royalty payments",
      threshold_amount: 30000,
      effective_from: "2023-01-01"
    }
  ]);

  console.log("✅ TRA Integration tables created successfully with default tax rates");
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("tra_api_integration_log");
  await knex.schema.dropTableIfExists("tra_tax_rates");
  await knex.schema.dropTableIfExists("tra_withholding_tax");
  await knex.schema.dropTableIfExists("tra_vat_returns");
  
  console.log("✅ TRA Integration tables dropped successfully");
}
