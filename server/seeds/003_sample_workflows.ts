import type { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // Get the first company ID for sample data
  const company = await knex('companies').first();
  if (!company) {
    console.log('No companies found, skipping workflow seed');
    return;
  }

  // Get the first user ID for sample data
  const user = await knex('users').first();
  if (!user) {
    console.log('No users found, skipping workflow seed');
    return;
  }

  // Delete existing workflows for this company
  await knex('workflows').where('company_id', company.id).del();

  // Insert sample workflows
  await knex('workflows').insert([
    {
      id: knex.raw('gen_random_uuid()'),
      company_id: company.id,
      name: 'Invoice Approval Workflow',
      description: 'Automatically route invoices over $1,000 for manager approval',
      is_active: true,
      trigger: JSON.stringify({
        type: 'EVENT',
        config: {
          event: 'invoice_created',
          condition: {
            field: 'amount',
            operator: 'greater_than',
            value: 1000
          }
        }
      }),
      conditions: JSON.stringify([
        {
          field: 'amount',
          operator: 'greater_than',
          value: 1000
        }
      ]),
      actions: JSON.stringify([
        {
          type: 'APPROVAL_REQUEST',
          config: {
            approvers: ['manager'],
            message: 'Please review and approve this invoice',
            timeout: 48 // hours
          }
        },
        {
          type: 'EMAIL',
          config: {
            to: ['<EMAIL>'],
            subject: 'Invoice Approval Required',
            template: 'invoice_approval'
          }
        }
      ]),
      created_by: user.id,
      run_count: 156,
      last_run_at: new Date('2024-01-15T10:30:00Z'),
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      company_id: company.id,
      name: 'Monthly Financial Reports',
      description: 'Generate and email monthly financial reports to stakeholders',
      is_active: true,
      trigger: JSON.stringify({
        type: 'SCHEDULE',
        config: {
          schedule: '0 9 1 * *' // First day of month at 9 AM
        }
      }),
      actions: JSON.stringify([
        {
          type: 'GENERATE_REPORT',
          config: {
            reportType: 'financial_summary',
            period: 'monthly'
          }
        },
        {
          type: 'EMAIL',
          config: {
            to: ['<EMAIL>', '<EMAIL>'],
            subject: 'Monthly Financial Report',
            template: 'monthly_report'
          }
        }
      ]),
      created_by: user.id,
      run_count: 12,
      last_run_at: new Date('2024-01-01T09:00:00Z'),
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      company_id: company.id,
      name: 'Overdue Payment Reminders',
      description: 'Send automated reminders for overdue invoices',
      is_active: true,
      trigger: JSON.stringify({
        type: 'SCHEDULE',
        config: {
          schedule: '0 8 * * *' // Daily at 8 AM
        }
      }),
      conditions: JSON.stringify([
        {
          field: 'due_date',
          operator: 'less_than',
          value: 'today'
        },
        {
          field: 'status',
          operator: 'equals',
          value: 'unpaid'
        }
      ]),
      actions: JSON.stringify([
        {
          type: 'EMAIL',
          config: {
            to: ['customer'],
            subject: 'Payment Reminder',
            template: 'payment_reminder'
          }
        },
        {
          type: 'NOTIFICATION',
          config: {
            message: 'Payment reminder sent',
            type: 'info'
          }
        }
      ]),
      created_by: user.id,
      run_count: 45,
      last_run_at: new Date('2024-01-15T08:00:00Z'),
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      company_id: company.id,
      name: 'Bank Transaction Sync',
      description: 'Automatically import and categorize bank transactions',
      is_active: false,
      trigger: JSON.stringify({
        type: 'EVENT',
        config: {
          event: 'bank_webhook'
        }
      }),
      actions: JSON.stringify([
        {
          type: 'WEBHOOK',
          config: {
            url: 'https://api.bank.com/transactions',
            method: 'GET',
            headers: {
              'Authorization': 'Bearer token'
            }
          }
        },
        {
          type: 'CATEGORIZE_TRANSACTIONS',
          config: {
            autoMatch: true,
            confidence: 0.8
          }
        }
      ]),
      created_by: user.id,
      run_count: 89,
      last_run_at: new Date('2024-01-10T14:22:00Z'),
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      company_id: company.id,
      name: 'Expense Report Approval',
      description: 'Route expense reports through approval hierarchy',
      is_active: false,
      trigger: JSON.stringify({
        type: 'EVENT',
        config: {
          event: 'expense_report_submitted'
        }
      }),
      actions: JSON.stringify([
        {
          type: 'APPROVAL_REQUEST',
          config: {
            approvers: ['direct_manager', 'finance_team'],
            sequential: true,
            timeout: 72
          }
        }
      ]),
      created_by: user.id,
      run_count: 0,
      created_at: new Date(),
      updated_at: new Date()
    }
  ]);

  console.log('✅ Sample workflows seeded successfully');
}
