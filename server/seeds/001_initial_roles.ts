import type { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // Deletes ALL existing entries
  await knex('roles').del();

  // Insert seed entries
  await knex('roles').insert([
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'super_admin',
      description: 'Super Administrator with full system access',
      permissions: JSON.stringify(['*']),
      is_active: true,
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'admin',
      description: 'Company Administrator',
      permissions: JSON.stringify([
        'accounts:*',
        'transactions:*',
        'contacts:*',
        'invoices:*',
        'reports:*',
        'users:read',
        'companies:*',
        'audit:read',
      ]),
      is_active: true,
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'accountant',
      description: 'Accountant with financial management access',
      permissions: JSON.stringify([
        'accounts:read',
        'accounts:create',
        'accounts:update',
        'transactions:*',
        'contacts:read',
        'invoices:*',
        'reports:read',
        'audit:read',
      ]),
      is_active: true,
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'user',
      description: 'Regular user with limited access',
      permissions: JSON.stringify([
        'accounts:read',
        'transactions:read',
        'contacts:read',
        'invoices:read',
        'reports:read',
      ]),
      is_active: true,
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'viewer',
      description: 'Read-only access',
      permissions: JSON.stringify([
        'accounts:read',
        'transactions:read',
        'contacts:read',
        'invoices:read',
        'reports:read',
      ]),
      is_active: true,
    },
  ]);
}
