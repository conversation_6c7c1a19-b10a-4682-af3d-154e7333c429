import type { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // Delete existing roles
  await knex('roles').del();

  // Insert default roles
  await knex('roles').insert([
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'admin',
      description: 'Full system administrator with all permissions',
      permissions: JSON.stringify([
        // User Management
        'users:read', 'users:create', 'users:update', 'users:delete',
        // Company Management
        'companies:read', 'companies:create', 'companies:update', 'companies:delete',
        // Accounting
        'accounts:read', 'accounts:create', 'accounts:update', 'accounts:delete',
        'transactions:read', 'transactions:create', 'transactions:update', 'transactions:delete', 'transactions:approve',
        // Invoicing
        'invoices:read', 'invoices:create', 'invoices:update', 'invoices:delete', 'invoices:send',
        // Contacts
        'contacts:read', 'contacts:create', 'contacts:update', 'contacts:delete',
        // Reports
        'reports:read', 'reports:export',
        // Settings
        'settings:read', 'settings:update',
        // Audit
        'audit:read',
      ]),
      is_active: true,
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'accountant',
      description: 'Full accounting access with limited administrative permissions',
      permissions: JSON.stringify([
        // Accounting
        'accounts:read', 'accounts:create', 'accounts:update',
        'transactions:read', 'transactions:create', 'transactions:update', 'transactions:approve',
        // Invoicing
        'invoices:read', 'invoices:create', 'invoices:update', 'invoices:send',
        // Contacts
        'contacts:read', 'contacts:create', 'contacts:update',
        // Reports
        'reports:read', 'reports:export',
        // Settings (limited)
        'settings:read',
      ]),
      is_active: true,
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'bookkeeper',
      description: 'Data entry and basic accounting functions',
      permissions: JSON.stringify([
        // Accounting (limited)
        'accounts:read',
        'transactions:read', 'transactions:create', 'transactions:update',
        // Invoicing
        'invoices:read', 'invoices:create', 'invoices:update',
        // Contacts
        'contacts:read', 'contacts:create', 'contacts:update',
        // Reports (limited)
        'reports:read',
      ]),
      is_active: true,
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'manager',
      description: 'Management access with reporting and oversight capabilities',
      permissions: JSON.stringify([
        // Accounting (read-only)
        'accounts:read',
        'transactions:read', 'transactions:approve',
        // Invoicing
        'invoices:read', 'invoices:send',
        // Contacts
        'contacts:read',
        // Reports
        'reports:read', 'reports:export',
        // Settings (read-only)
        'settings:read',
        // Audit
        'audit:read',
      ]),
      is_active: true,
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'viewer',
      description: 'Read-only access to financial data',
      permissions: JSON.stringify([
        // Accounting (read-only)
        'accounts:read',
        'transactions:read',
        // Invoicing (read-only)
        'invoices:read',
        // Contacts (read-only)
        'contacts:read',
        // Reports (read-only)
        'reports:read',
      ]),
      is_active: true,
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'user',
      description: 'Basic user with minimal permissions',
      permissions: JSON.stringify([
        // Basic read access
        'accounts:read',
        'transactions:read',
        'contacts:read',
        'invoices:read',
      ]),
      is_active: true,
    },
  ]);
}
