import type { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // Get the first company and workflows for sample data
  const company = await knex('companies').first();
  if (!company) {
    console.log('No companies found, skipping workflow execution seed');
    return;
  }

  const workflows = await knex('workflows').where('company_id', company.id);
  if (workflows.length === 0) {
    console.log('No workflows found, skipping workflow execution seed');
    return;
  }

  const user = await knex('users').first();
  if (!user) {
    console.log('No users found, skipping workflow execution seed');
    return;
  }

  // Delete existing workflow executions for this company
  await knex('workflow_executions').where('company_id', company.id).del();

  const executions = [];
  
  // Create sample executions for each workflow
  for (const workflow of workflows) {
    const executionCount = Math.floor(Math.random() * 20) + 5; // 5-25 executions per workflow
    
    for (let i = 0; i < executionCount; i++) {
      const startedAt = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000); // Last 30 days
      const completedAt = new Date(startedAt.getTime() + Math.random() * 60 * 60 * 1000); // Up to 1 hour duration
      const status = Math.random() > 0.1 ? 'COMPLETED' : 'FAILED'; // 90% success rate
      
      executions.push({
        id: knex.raw('gen_random_uuid()'),
        company_id: company.id,
        workflow_id: workflow.id,
        status,
        trigger_data: JSON.stringify({
          source: 'test',
          timestamp: startedAt.toISOString()
        }),
        started_at: startedAt,
        completed_at: status === 'COMPLETED' ? completedAt : null,
        error: status === 'FAILED' ? 'Sample error message' : null,
        results: status === 'COMPLETED' ? JSON.stringify({
          success: true,
          steps_completed: 2,
          duration: completedAt.getTime() - startedAt.getTime()
        }) : null,
        created_at: startedAt
      });
    }
  }

  // Insert all executions
  if (executions.length > 0) {
    await knex('workflow_executions').insert(executions);
  }

  console.log(`✅ ${executions.length} sample workflow executions seeded successfully`);
}
