import type { K<PERSON> } from "knex";
import bcrypt from "bcryptjs";

export async function seed(knex: Knex): Promise<void> {
  // Get roles
  const adminRole = await knex("roles").where("name", "admin").first();
  const userRole = await knex("roles").where("name", "user").first();

  if (!adminRole || !userRole) {
    throw new Error("Required roles not found. Please run role seeds first.");
  }

  // Create demo users
  const hashedPassword = await bcrypt.hash("Athanas@2015", 12);

  // Insert demo admin user
  const [adminUser] = await knex("users")
    .insert({
      id: knex.raw("gen_random_uuid()"),
      email: "<EMAIL>",
      password_hash: hashedPassword,
      first_name: "<PERSON><PERSON><PERSON>",
      last_name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      phone: "+************",
      role_id: adminRole.id,
      is_active: true,
      email_verified: true,
    })
    .returning("*");

  // Insert demo regular user
  const [regularUser] = await knex("users")
    .insert({
      id: knex.raw("gen_random_uuid()"),
      email: "<EMAIL>",
      password_hash: hashedPassword,
      first_name: "John",
      last_name: "Doe",
      phone: "******-0102",
      role_id: userRole.id,
      is_active: true,
      email_verified: true,
    })
    .returning("*");

  // Create demo company
  const [demoCompany] = await knex("companies")
    .insert({
      id: knex.raw("gen_random_uuid()"),
      name: "Demo Company Inc.",
      legal_name: "Demo Company Incorporated",
      tax_id: "12-3456789",
      registration_number: "REG123456",
      address: "123 Business Street",
      city: "Business City",
      state: "CA",
      postal_code: "90210",
      country: "United States",
      phone: "******-0100",
      email: "<EMAIL>",
      website: "https://democompany.com",
      base_currency: "USD",
      settings: JSON.stringify({
        fiscal_year_start: "01-01",
        time_zone: "America/Los_Angeles",
        date_format: "MM/DD/YYYY",
        number_format: "en-US",
      }),
      is_active: true,
    })
    .returning("*");

  // Associate users with company
  await knex("user_companies").insert([
    {
      id: knex.raw("gen_random_uuid()"),
      user_id: adminUser.id,
      company_id: demoCompany.id,
      role_id: adminRole.id,
      is_active: true,
    },
    {
      id: knex.raw("gen_random_uuid()"),
      user_id: regularUser.id,
      company_id: demoCompany.id,
      role_id: userRole.id,
      is_active: true,
    },
  ]);

  console.log("Demo data created:");
  console.log("Admin User: <EMAIL> / password123");
  console.log("Regular User: <EMAIL> / password123");
  console.log("Company: Demo Company Inc.");
}
