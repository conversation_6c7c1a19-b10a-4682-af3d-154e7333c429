import db from "../config/database";
import { auditService } from "./auditService";

// Payroll Management interfaces
export interface Employee {
  id?: string;
  companyId: string;
  employeeNumber: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  dateOfBirth?: Date;
  gender?: 'MALE' | 'FEMALE' | 'OTHER';
  nationalId?: string;
  email?: string;
  phone?: string;
  address?: string;
  jobTitle: string;
  department?: string;
  hireDate: Date;
  terminationDate?: Date;
  employmentStatus: 'ACTIVE' | 'INACTIVE' | 'TERMINATED' | 'SUSPENDED';
  employmentType: 'PERMANENT' | 'CONTRACT' | 'TEMPORARY' | 'CASUAL';
  basicSalary: number;
  salaryFrequency: 'MONTHLY' | 'WEEKLY' | 'DAILY';
  housingAllowance: number;
  transportAllowance: number;
  otherAllowances: number;
  tinNumber?: string;
  nssfNumber?: string;
  wcfNumber?: string;
  nhifNumber?: string;
  isTaxExempt: boolean;
  taxExemptionReason?: string;
  bankName?: string;
  bankAccountNumber?: string;
  bankBranch?: string;
}

export interface PAYECalculation {
  id?: string;
  companyId: string;
  employeeId: string;
  taxYear: number;
  taxMonth: number;
  payrollDate: Date;
  basicSalary: number;
  housingAllowance: number;
  transportAllowance: number;
  otherAllowances: number;
  overtimePay: number;
  bonus: number;
  grossSalary: number;
  nssfEmployee: number;
  nssfEmployer: number;
  wcfContribution: number;
  nhifContribution: number;
  otherDeductions: number;
  totalDeductions: number;
  taxableIncome: number;
  taxRelief: number;
  payeTax: number;
  netSalary: number;
  taxBand1Amount: number;
  taxBand2Amount: number;
  taxBand3Amount: number;
  taxBand4Amount: number;
  taxBand5Amount: number;
  calculationStatus: 'DRAFT' | 'CALCULATED' | 'APPROVED' | 'PAID';
  submissionStatus: 'PENDING' | 'SUBMITTED' | 'ACCEPTED' | 'REJECTED';
}

export interface SDLCalculation {
  id?: string;
  companyId: string;
  taxYear: number;
  taxMonth: number;
  calculationDate: Date;
  totalEmployees: number;
  totalGrossPayroll: number;
  sdlRate: number;
  sdlAmount: number;
  exemptPayroll: number;
  adjustments: number;
  adjustmentReason?: string;
  finalSdlAmount: number;
  paymentStatus: 'PENDING' | 'PAID' | 'OVERDUE';
  dueDate?: Date;
  paymentDate?: Date;
  paymentReference?: string;
  submissionStatus: 'PENDING' | 'SUBMITTED' | 'ACCEPTED' | 'REJECTED';
}

export interface PayrollTaxRate {
  id?: string;
  taxType: 'PAYE' | 'SDL' | 'NSSF' | 'WCF' | 'NHIF';
  taxBand?: string;
  rate: number;
  minAmount?: number;
  maxAmount?: number;
  fixedAmount?: number;
  description?: string;
  effectiveFrom: Date;
  effectiveTo?: Date;
  isActive: boolean;
}

export class PayrollService {
  /**
   * Calculate PAYE for an employee
   */
  static async calculatePAYE(
    companyId: string,
    employeeId: string,
    taxYear: number,
    taxMonth: number,
    salaryData: {
      basicSalary: number;
      housingAllowance?: number;
      transportAllowance?: number;
      otherAllowances?: number;
      overtimePay?: number;
      bonus?: number;
      otherDeductions?: number;
    }
  ): Promise<PAYECalculation> {
    try {
      const {
        basicSalary,
        housingAllowance = 0,
        transportAllowance = 0,
        otherAllowances = 0,
        overtimePay = 0,
        bonus = 0,
        otherDeductions = 0
      } = salaryData;

      // Calculate gross salary
      const grossSalary = basicSalary + housingAllowance + transportAllowance + otherAllowances + overtimePay + bonus;

      // Calculate statutory deductions
      const nssfEmployee = await this.calculateNSSF(basicSalary);
      const nssfEmployer = nssfEmployee; // Same amount for employer
      const wcfContribution = await this.calculateWCF(grossSalary);
      const nhifContribution = await this.calculateNHIF(grossSalary);

      const totalDeductions = nssfEmployee + wcfContribution + nhifContribution + otherDeductions;

      // Calculate taxable income (gross salary - statutory deductions)
      const taxableIncome = grossSalary - nssfEmployee - wcfContribution - nhifContribution;

      // Calculate annual taxable income for PAYE bands
      const annualTaxableIncome = taxableIncome * 12;

      // Calculate tax relief (220,000 TZS annually)
      const taxRelief = 220000 / 12; // Monthly relief

      // Calculate PAYE using Tanzania tax bands
      const payeCalculation = await this.calculatePAYETaxBands(annualTaxableIncome);
      const monthlyPAYE = Math.max(0, (payeCalculation.totalTax / 12) - taxRelief);

      // Calculate net salary
      const netSalary = grossSalary - totalDeductions - monthlyPAYE;

      const calculation: PAYECalculation = {
        companyId,
        employeeId,
        taxYear,
        taxMonth,
        payrollDate: new Date(),
        basicSalary,
        housingAllowance,
        transportAllowance,
        otherAllowances,
        overtimePay,
        bonus,
        grossSalary: Math.round(grossSalary * 100) / 100,
        nssfEmployee: Math.round(nssfEmployee * 100) / 100,
        nssfEmployer: Math.round(nssfEmployer * 100) / 100,
        wcfContribution: Math.round(wcfContribution * 100) / 100,
        nhifContribution: Math.round(nhifContribution * 100) / 100,
        otherDeductions,
        totalDeductions: Math.round(totalDeductions * 100) / 100,
        taxableIncome: Math.round(taxableIncome * 100) / 100,
        taxRelief: Math.round(taxRelief * 100) / 100,
        payeTax: Math.round(monthlyPAYE * 100) / 100,
        netSalary: Math.round(netSalary * 100) / 100,
        taxBand1Amount: Math.round((payeCalculation.band1Tax / 12) * 100) / 100,
        taxBand2Amount: Math.round((payeCalculation.band2Tax / 12) * 100) / 100,
        taxBand3Amount: Math.round((payeCalculation.band3Tax / 12) * 100) / 100,
        taxBand4Amount: Math.round((payeCalculation.band4Tax / 12) * 100) / 100,
        taxBand5Amount: Math.round((payeCalculation.band5Tax / 12) * 100) / 100,
        calculationStatus: 'CALCULATED',
        submissionStatus: 'PENDING'
      };

      return calculation;
    } catch (error) {
      console.error('Error calculating PAYE:', error);
      throw new Error('Failed to calculate PAYE');
    }
  }

  /**
   * Calculate PAYE tax bands for annual income
   */
  private static async calculatePAYETaxBands(annualIncome: number): Promise<{
    totalTax: number;
    band1Tax: number;
    band2Tax: number;
    band3Tax: number;
    band4Tax: number;
    band5Tax: number;
  }> {
    let remainingIncome = annualIncome;
    let totalTax = 0;
    let band1Tax = 0, band2Tax = 0, band3Tax = 0, band4Tax = 0, band5Tax = 0;

    // Band 1: 0% on first 270,000 TZS
    if (remainingIncome > 270000) {
      band1Tax = 0;
      remainingIncome -= 270000;
    } else {
      band1Tax = 0;
      remainingIncome = 0;
    }

    // Band 2: 9% on 270,001 - 520,000 TZS
    if (remainingIncome > 0) {
      const bandAmount = Math.min(remainingIncome, 250000); // 520,000 - 270,000
      band2Tax = bandAmount * 0.09;
      totalTax += band2Tax;
      remainingIncome -= bandAmount;
    }

    // Band 3: 20% on 520,001 - 760,000 TZS
    if (remainingIncome > 0) {
      const bandAmount = Math.min(remainingIncome, 240000); // 760,000 - 520,000
      band3Tax = bandAmount * 0.20;
      totalTax += band3Tax;
      remainingIncome -= bandAmount;
    }

    // Band 4: 25% on 760,001 - 1,000,000 TZS
    if (remainingIncome > 0) {
      const bandAmount = Math.min(remainingIncome, 240000); // 1,000,000 - 760,000
      band4Tax = bandAmount * 0.25;
      totalTax += band4Tax;
      remainingIncome -= bandAmount;
    }

    // Band 5: 30% on above 1,000,000 TZS
    if (remainingIncome > 0) {
      band5Tax = remainingIncome * 0.30;
      totalTax += band5Tax;
    }

    return { totalTax, band1Tax, band2Tax, band3Tax, band4Tax, band5Tax };
  }

  /**
   * Calculate NSSF contribution
   */
  private static async calculateNSSF(basicSalary: number): Promise<number> {
    // NSSF: 10% of basic salary, maximum 20,000 TZS monthly
    const nssfAmount = basicSalary * 0.10;
    return Math.min(nssfAmount, 20000);
  }

  /**
   * Calculate WCF contribution
   */
  private static async calculateWCF(grossSalary: number): Promise<number> {
    // WCF: 0.5% of gross salary
    return grossSalary * 0.005;
  }

  /**
   * Calculate NHIF contribution
   */
  private static async calculateNHIF(grossSalary: number): Promise<number> {
    // NHIF contribution based on salary bands (simplified)
    if (grossSalary <= 15000) return 150;
    if (grossSalary <= 20000) return 300;
    if (grossSalary <= 25000) return 400;
    if (grossSalary <= 30000) return 500;
    if (grossSalary <= 35000) return 600;
    if (grossSalary <= 40000) return 750;
    if (grossSalary <= 45000) return 900;
    if (grossSalary <= 50000) return 1000;
    if (grossSalary <= 60000) return 1200;
    if (grossSalary <= 70000) return 1300;
    if (grossSalary <= 80000) return 1400;
    if (grossSalary <= 90000) return 1500;
    if (grossSalary <= 100000) return 1600;
    return 1700; // Above 100,000 TZS
  }

  /**
   * Calculate SDL for company payroll
   */
  static async calculateSDL(
    companyId: string,
    taxYear: number,
    taxMonth: number,
    payrollData: {
      totalEmployees: number;
      totalGrossPayroll: number;
      exemptPayroll?: number;
      adjustments?: number;
      adjustmentReason?: string;
    }
  ): Promise<SDLCalculation> {
    try {
      const {
        totalEmployees,
        totalGrossPayroll,
        exemptPayroll = 0,
        adjustments = 0,
        adjustmentReason
      } = payrollData;

      // SDL rate: 6% of gross payroll
      const sdlRate = 6.00;
      const taxablePayroll = totalGrossPayroll - exemptPayroll;
      const sdlAmount = taxablePayroll * (sdlRate / 100);
      const finalSdlAmount = sdlAmount + adjustments;

      // Calculate due date (15th of following month)
      const dueDate = new Date(taxYear, taxMonth, 15);

      const calculation: SDLCalculation = {
        companyId,
        taxYear,
        taxMonth,
        calculationDate: new Date(),
        totalEmployees,
        totalGrossPayroll: Math.round(totalGrossPayroll * 100) / 100,
        sdlRate,
        sdlAmount: Math.round(sdlAmount * 100) / 100,
        exemptPayroll: Math.round(exemptPayroll * 100) / 100,
        adjustments: Math.round(adjustments * 100) / 100,
        adjustmentReason,
        finalSdlAmount: Math.round(finalSdlAmount * 100) / 100,
        paymentStatus: 'PENDING',
        dueDate,
        submissionStatus: 'PENDING'
      };

      return calculation;
    } catch (error) {
      console.error('Error calculating SDL:', error);
      throw new Error('Failed to calculate SDL');
    }
  }

  /**
   * Save PAYE calculation
   */
  static async savePAYECalculation(calculation: PAYECalculation): Promise<string> {
    try {
      const [result] = await db('paye_calculations').insert({
        company_id: calculation.companyId,
        employee_id: calculation.employeeId,
        tax_year: calculation.taxYear,
        tax_month: calculation.taxMonth,
        payroll_date: calculation.payrollDate,
        basic_salary: calculation.basicSalary,
        housing_allowance: calculation.housingAllowance,
        transport_allowance: calculation.transportAllowance,
        other_allowances: calculation.otherAllowances,
        overtime_pay: calculation.overtimePay,
        bonus: calculation.bonus,
        gross_salary: calculation.grossSalary,
        nssf_employee: calculation.nssfEmployee,
        nssf_employer: calculation.nssfEmployer,
        wcf_contribution: calculation.wcfContribution,
        nhif_contribution: calculation.nhifContribution,
        other_deductions: calculation.otherDeductions,
        total_deductions: calculation.totalDeductions,
        taxable_income: calculation.taxableIncome,
        tax_relief: calculation.taxRelief,
        paye_tax: calculation.payeTax,
        net_salary: calculation.netSalary,
        tax_band_1_amount: calculation.taxBand1Amount,
        tax_band_2_amount: calculation.taxBand2Amount,
        tax_band_3_amount: calculation.taxBand3Amount,
        tax_band_4_amount: calculation.taxBand4Amount,
        tax_band_5_amount: calculation.taxBand5Amount,
        calculation_status: calculation.calculationStatus,
        submission_status: calculation.submissionStatus,
        created_at: new Date(),
        updated_at: new Date()
      }).returning('id');

      // Log audit event
      await auditService.log({
        companyId: calculation.companyId,
        action: 'CREATE',
        description: `PAYE calculated for employee ${calculation.employeeId}`,
        metadata: {
          employeeId: calculation.employeeId,
          taxYear: calculation.taxYear,
          taxMonth: calculation.taxMonth,
          grossSalary: calculation.grossSalary,
          payeTax: calculation.payeTax,
          netSalary: calculation.netSalary
        }
      });

      return result.id;
    } catch (error) {
      console.error('Error saving PAYE calculation:', error);
      throw new Error('Failed to save PAYE calculation');
    }
  }

  /**
   * Save SDL calculation
   */
  static async saveSDLCalculation(calculation: SDLCalculation): Promise<string> {
    try {
      const [result] = await db('sdl_calculations').insert({
        company_id: calculation.companyId,
        tax_year: calculation.taxYear,
        tax_month: calculation.taxMonth,
        calculation_date: calculation.calculationDate,
        total_employees: calculation.totalEmployees,
        total_gross_payroll: calculation.totalGrossPayroll,
        sdl_rate: calculation.sdlRate,
        sdl_amount: calculation.sdlAmount,
        exempt_payroll: calculation.exemptPayroll,
        adjustments: calculation.adjustments,
        adjustment_reason: calculation.adjustmentReason,
        final_sdl_amount: calculation.finalSdlAmount,
        payment_status: calculation.paymentStatus,
        due_date: calculation.dueDate,
        submission_status: calculation.submissionStatus,
        created_at: new Date(),
        updated_at: new Date()
      }).returning('id');

      // Log audit event
      await auditService.log({
        companyId: calculation.companyId,
        action: 'CREATE',
        description: `SDL calculated for ${calculation.taxYear}-${calculation.taxMonth}`,
        metadata: {
          taxYear: calculation.taxYear,
          taxMonth: calculation.taxMonth,
          totalEmployees: calculation.totalEmployees,
          totalGrossPayroll: calculation.totalGrossPayroll,
          finalSdlAmount: calculation.finalSdlAmount
        }
      });

      return result.id;
    } catch (error) {
      console.error('Error saving SDL calculation:', error);
      throw new Error('Failed to save SDL calculation');
    }
  }

  /**
   * Get employees for a company
   */
  static async getEmployees(
    companyId: string,
    limit: number = 10,
    offset: number = 0
  ): Promise<{ employees: Employee[]; total: number }> {
    try {
      const [employees, countResult] = await Promise.all([
        db('employees')
          .where('company_id', companyId)
          .orderBy('employee_number')
          .limit(limit)
          .offset(offset),
        db('employees')
          .where('company_id', companyId)
          .count('* as count')
          .first()
      ]);

      return {
        employees: employees.map(e => ({
          id: e.id,
          companyId: e.company_id,
          employeeNumber: e.employee_number,
          firstName: e.first_name,
          middleName: e.middle_name,
          lastName: e.last_name,
          dateOfBirth: e.date_of_birth ? new Date(e.date_of_birth) : undefined,
          gender: e.gender,
          nationalId: e.national_id,
          email: e.email,
          phone: e.phone,
          address: e.address,
          jobTitle: e.job_title,
          department: e.department,
          hireDate: new Date(e.hire_date),
          terminationDate: e.termination_date ? new Date(e.termination_date) : undefined,
          employmentStatus: e.employment_status,
          employmentType: e.employment_type,
          basicSalary: parseFloat(e.basic_salary),
          salaryFrequency: e.salary_frequency,
          housingAllowance: parseFloat(e.housing_allowance),
          transportAllowance: parseFloat(e.transport_allowance),
          otherAllowances: parseFloat(e.other_allowances),
          tinNumber: e.tin_number,
          nssfNumber: e.nssf_number,
          wcfNumber: e.wcf_number,
          nhifNumber: e.nhif_number,
          isTaxExempt: e.is_tax_exempt,
          taxExemptionReason: e.tax_exemption_reason,
          bankName: e.bank_name,
          bankAccountNumber: e.bank_account_number,
          bankBranch: e.bank_branch
        })),
        total: parseInt(String(countResult?.count || '0'))
      };
    } catch (error) {
      console.error('Error getting employees:', error);
      throw new Error('Failed to get employees');
    }
  }

  /**
   * Get PAYE calculations for a company
   */
  static async getPAYECalculations(
    companyId: string,
    taxYear?: number,
    taxMonth?: number,
    limit: number = 10,
    offset: number = 0
  ): Promise<{ calculations: PAYECalculation[]; total: number }> {
    try {
      let query = db('paye_calculations')
        .where('company_id', companyId);

      if (taxYear) query = query.where('tax_year', taxYear);
      if (taxMonth) query = query.where('tax_month', taxMonth);

      const [calculations, countResult] = await Promise.all([
        query.clone()
          .orderBy('tax_year', 'desc')
          .orderBy('tax_month', 'desc')
          .orderBy('employee_id', 'desc')
          .limit(limit)
          .offset(offset),
        query.clone()
          .count('* as count')
          .first()
      ]);

      return {
        calculations: calculations.map(c => ({
          id: c.id,
          companyId: c.company_id,
          employeeId: c.employee_id,
          taxYear: c.tax_year,
          taxMonth: c.tax_month,
          payrollDate: new Date(c.payroll_date),
          basicSalary: parseFloat(c.basic_salary),
          housingAllowance: parseFloat(c.housing_allowance),
          transportAllowance: parseFloat(c.transport_allowance),
          otherAllowances: parseFloat(c.other_allowances),
          overtimePay: parseFloat(c.overtime_pay),
          bonus: parseFloat(c.bonus),
          grossSalary: parseFloat(c.gross_salary),
          nssfEmployee: parseFloat(c.nssf_employee),
          nssfEmployer: parseFloat(c.nssf_employer),
          wcfContribution: parseFloat(c.wcf_contribution),
          nhifContribution: parseFloat(c.nhif_contribution),
          otherDeductions: parseFloat(c.other_deductions),
          totalDeductions: parseFloat(c.total_deductions),
          taxableIncome: parseFloat(c.taxable_income),
          taxRelief: parseFloat(c.tax_relief),
          payeTax: parseFloat(c.paye_tax),
          netSalary: parseFloat(c.net_salary),
          taxBand1Amount: parseFloat(c.tax_band_1_amount),
          taxBand2Amount: parseFloat(c.tax_band_2_amount),
          taxBand3Amount: parseFloat(c.tax_band_3_amount),
          taxBand4Amount: parseFloat(c.tax_band_4_amount),
          taxBand5Amount: parseFloat(c.tax_band_5_amount),
          calculationStatus: c.calculation_status,
          submissionStatus: c.submission_status
        })),
        total: parseInt(String(countResult?.count || '0'))
      };
    } catch (error) {
      console.error('Error getting PAYE calculations:', error);
      throw new Error('Failed to get PAYE calculations');
    }
  }

  /**
   * Get SDL calculations for a company
   */
  static async getSDLCalculations(
    companyId: string,
    taxYear?: number,
    limit: number = 10,
    offset: number = 0
  ): Promise<{ calculations: SDLCalculation[]; total: number }> {
    try {
      let query = db('sdl_calculations')
        .where('company_id', companyId);

      if (taxYear) query = query.where('tax_year', taxYear);

      const [calculations, countResult] = await Promise.all([
        query.clone()
          .orderBy('tax_year', 'desc')
          .orderBy('tax_month', 'desc')
          .limit(limit)
          .offset(offset),
        query.clone()
          .count('* as count')
          .first()
      ]);

      return {
        calculations: calculations.map(c => ({
          id: c.id,
          companyId: c.company_id,
          taxYear: c.tax_year,
          taxMonth: c.tax_month,
          calculationDate: new Date(c.calculation_date),
          totalEmployees: c.total_employees,
          totalGrossPayroll: parseFloat(c.total_gross_payroll),
          sdlRate: parseFloat(c.sdl_rate),
          sdlAmount: parseFloat(c.sdl_amount),
          exemptPayroll: parseFloat(c.exempt_payroll),
          adjustments: parseFloat(c.adjustments),
          adjustmentReason: c.adjustment_reason,
          finalSdlAmount: parseFloat(c.final_sdl_amount),
          paymentStatus: c.payment_status,
          dueDate: c.due_date ? new Date(c.due_date) : undefined,
          paymentDate: c.payment_date ? new Date(c.payment_date) : undefined,
          paymentReference: c.payment_reference,
          submissionStatus: c.submission_status
        })),
        total: parseInt(String(countResult?.count || '0'))
      };
    } catch (error) {
      console.error('Error getting SDL calculations:', error);
      throw new Error('Failed to get SDL calculations');
    }
  }
}

export const payrollService = PayrollService;
