import { db } from '../config/database.js';

export interface AuditReportFilters {
  startDate?: Date;
  endDate?: Date;
  userId?: string;
  userEmail?: string;
  companyId?: string;
  tableName?: string;
  actionType?: 'INSERT' | 'UPDATE' | 'DELETE';
  riskLevel?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  ipAddress?: string;
  limit?: number;
  offset?: number;
}

export interface AccessReportFilters {
  startDate?: Date;
  endDate?: Date;
  userId?: string;
  action?: string;
  resource?: string;
  success?: boolean;
  ipAddress?: string;
  limit?: number;
  offset?: number;
}

export interface FinancialReportFilters {
  startDate?: Date;
  endDate?: Date;
  accountId?: string;
  actionType?: string;
  minAmount?: number;
  maxAmount?: number;
  currency?: string;
  authorizationLevel?: string;
  limit?: number;
  offset?: number;
}

export interface AuditSummary {
  totalLogs: number;
  riskDistribution: { [key: string]: number };
  actionDistribution: { [key: string]: number };
  userActivity: { userEmail: string; count: number }[];
  tableActivity: { tableName: string; count: number }[];
  timelineData: { date: string; count: number }[];
}

export class AuditReportService {
  /**
   * Get audit logs with filtering and pagination
   */
  static async getAuditLogs(filters: AuditReportFilters = {}) {
    try {
      let query = db('audit_logs')
        .select([
          'id',
          'table_name',
          'record_id',
          'action_type',
          'old_values',
          'new_values',
          'changed_fields',
          'user_id',
          'user_email',
          'user_role',
          'company_id',
          'ip_address',
          'user_agent',
          'session_id',
          'timestamp',
          'reason',
          'compliance_flags',
          'risk_level'
        ])
        .orderBy('timestamp', 'desc');

      // Apply filters
      if (filters.startDate) {
        query = query.where('timestamp', '>=', filters.startDate);
      }
      if (filters.endDate) {
        query = query.where('timestamp', '<=', filters.endDate);
      }
      if (filters.userId) {
        query = query.where('user_id', filters.userId);
      }
      if (filters.userEmail) {
        query = query.where('user_email', 'like', `%${filters.userEmail}%`);
      }
      if (filters.companyId) {
        query = query.where('company_id', filters.companyId);
      }
      if (filters.tableName) {
        query = query.where('table_name', filters.tableName);
      }
      if (filters.actionType) {
        query = query.where('action_type', filters.actionType);
      }
      if (filters.riskLevel) {
        query = query.where('risk_level', filters.riskLevel);
      }
      if (filters.ipAddress) {
        query = query.where('ip_address', 'like', `%${filters.ipAddress}%`);
      }

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }
      if (filters.offset) {
        query = query.offset(filters.offset);
      }

      const logs = await query;
      
      // Get total count for pagination
      let countQuery = db('audit_logs').count('* as count');
      
      // Apply same filters for count
      if (filters.startDate) countQuery = countQuery.where('timestamp', '>=', filters.startDate);
      if (filters.endDate) countQuery = countQuery.where('timestamp', '<=', filters.endDate);
      if (filters.userId) countQuery = countQuery.where('user_id', filters.userId);
      if (filters.userEmail) countQuery = countQuery.where('user_email', 'like', `%${filters.userEmail}%`);
      if (filters.companyId) countQuery = countQuery.where('company_id', filters.companyId);
      if (filters.tableName) countQuery = countQuery.where('table_name', filters.tableName);
      if (filters.actionType) countQuery = countQuery.where('action_type', filters.actionType);
      if (filters.riskLevel) countQuery = countQuery.where('risk_level', filters.riskLevel);
      if (filters.ipAddress) countQuery = countQuery.where('ip_address', 'like', `%${filters.ipAddress}%`);

      const totalResult = await countQuery.first();
      const total = parseInt(totalResult?.count || '0');

      return {
        logs,
        total,
        page: Math.floor((filters.offset || 0) / (filters.limit || 10)) + 1,
        totalPages: Math.ceil(total / (filters.limit || 10))
      };

    } catch (error) {
      console.error('❌ Failed to get audit logs:', error);
      throw new Error('Failed to get audit logs');
    }
  }

  /**
   * Get access logs with filtering and pagination
   */
  static async getAccessLogs(filters: AccessReportFilters = {}) {
    try {
      let query = db('access_audit_logs')
        .select([
          'id',
          'user_id',
          'action',
          'resource',
          'success',
          'failure_reason',
          'ip_address',
          'location_data',
          'device_info',
          'timestamp'
        ])
        .orderBy('timestamp', 'desc');

      // Apply filters
      if (filters.startDate) {
        query = query.where('timestamp', '>=', filters.startDate);
      }
      if (filters.endDate) {
        query = query.where('timestamp', '<=', filters.endDate);
      }
      if (filters.userId) {
        query = query.where('user_id', filters.userId);
      }
      if (filters.action) {
        query = query.where('action', 'like', `%${filters.action}%`);
      }
      if (filters.resource) {
        query = query.where('resource', 'like', `%${filters.resource}%`);
      }
      if (filters.success !== undefined) {
        query = query.where('success', filters.success);
      }
      if (filters.ipAddress) {
        query = query.where('ip_address', 'like', `%${filters.ipAddress}%`);
      }

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }
      if (filters.offset) {
        query = query.offset(filters.offset);
      }

      const logs = await query;
      
      // Get total count
      let countQuery = db('access_audit_logs').count('* as count');
      
      // Apply same filters for count
      if (filters.startDate) countQuery = countQuery.where('timestamp', '>=', filters.startDate);
      if (filters.endDate) countQuery = countQuery.where('timestamp', '<=', filters.endDate);
      if (filters.userId) countQuery = countQuery.where('user_id', filters.userId);
      if (filters.action) countQuery = countQuery.where('action', 'like', `%${filters.action}%`);
      if (filters.resource) countQuery = countQuery.where('resource', 'like', `%${filters.resource}%`);
      if (filters.success !== undefined) countQuery = countQuery.where('success', filters.success);
      if (filters.ipAddress) countQuery = countQuery.where('ip_address', 'like', `%${filters.ipAddress}%`);

      const totalResult = await countQuery.first();
      const total = parseInt(totalResult?.count || '0');

      return {
        logs,
        total,
        page: Math.floor((filters.offset || 0) / (filters.limit || 10)) + 1,
        totalPages: Math.ceil(total / (filters.limit || 10))
      };

    } catch (error) {
      console.error('❌ Failed to get access logs:', error);
      throw new Error('Failed to get access logs');
    }
  }

  /**
   * Get financial audit trail with filtering and pagination
   */
  static async getFinancialAuditTrail(filters: FinancialReportFilters = {}) {
    try {
      let query = db('financial_audit_trail')
        .select([
          'id',
          'transaction_id',
          'account_id',
          'amount',
          'currency',
          'action_type',
          'previous_balance',
          'new_balance',
          'authorization_level',
          'approver_id',
          'compliance_check_result',
          'timestamp'
        ])
        .orderBy('timestamp', 'desc');

      // Apply filters
      if (filters.startDate) {
        query = query.where('timestamp', '>=', filters.startDate);
      }
      if (filters.endDate) {
        query = query.where('timestamp', '<=', filters.endDate);
      }
      if (filters.accountId) {
        query = query.where('account_id', filters.accountId);
      }
      if (filters.actionType) {
        query = query.where('action_type', 'like', `%${filters.actionType}%`);
      }
      if (filters.minAmount) {
        query = query.where('amount', '>=', filters.minAmount);
      }
      if (filters.maxAmount) {
        query = query.where('amount', '<=', filters.maxAmount);
      }
      if (filters.currency) {
        query = query.where('currency', filters.currency);
      }
      if (filters.authorizationLevel) {
        query = query.where('authorization_level', filters.authorizationLevel);
      }

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }
      if (filters.offset) {
        query = query.offset(filters.offset);
      }

      const logs = await query;
      
      // Get total count
      let countQuery = db('financial_audit_trail').count('* as count');
      
      // Apply same filters for count
      if (filters.startDate) countQuery = countQuery.where('timestamp', '>=', filters.startDate);
      if (filters.endDate) countQuery = countQuery.where('timestamp', '<=', filters.endDate);
      if (filters.accountId) countQuery = countQuery.where('account_id', filters.accountId);
      if (filters.actionType) countQuery = countQuery.where('action_type', 'like', `%${filters.actionType}%`);
      if (filters.minAmount) countQuery = countQuery.where('amount', '>=', filters.minAmount);
      if (filters.maxAmount) countQuery = countQuery.where('amount', '<=', filters.maxAmount);
      if (filters.currency) countQuery = countQuery.where('currency', filters.currency);
      if (filters.authorizationLevel) countQuery = countQuery.where('authorization_level', filters.authorizationLevel);

      const totalResult = await countQuery.first();
      const total = parseInt(totalResult?.count || '0');

      return {
        logs,
        total,
        page: Math.floor((filters.offset || 0) / (filters.limit || 10)) + 1,
        totalPages: Math.ceil(total / (filters.limit || 10))
      };

    } catch (error) {
      console.error('❌ Failed to get financial audit trail:', error);
      throw new Error('Failed to get financial audit trail');
    }
  }

  /**
   * Generate audit summary statistics
   */
  static async getAuditSummary(filters: AuditReportFilters = {}): Promise<AuditSummary> {
    try {
      // Base query with filters
      let baseQuery = db('audit_logs');

      if (filters.startDate) baseQuery = baseQuery.where('timestamp', '>=', filters.startDate);
      if (filters.endDate) baseQuery = baseQuery.where('timestamp', '<=', filters.endDate);
      if (filters.companyId) baseQuery = baseQuery.where('company_id', filters.companyId);

      // Total logs count
      const totalResult = await baseQuery.clone().count('* as count').first();
      const totalLogs = parseInt(totalResult?.count || '0');

      // Risk level distribution
      const riskDistribution: { [key: string]: number } = {};
      const riskResults = await baseQuery.clone()
        .select('risk_level')
        .count('* as count')
        .groupBy('risk_level');

      riskResults.forEach(row => {
        riskDistribution[row.risk_level] = parseInt(row.count);
      });

      // Action type distribution
      const actionDistribution: { [key: string]: number } = {};
      const actionResults = await baseQuery.clone()
        .select('action_type')
        .count('* as count')
        .groupBy('action_type');

      actionResults.forEach(row => {
        actionDistribution[row.action_type] = parseInt(row.count);
      });

      // User activity (top 10 most active users)
      const userActivity = await baseQuery.clone()
        .select('user_email')
        .count('* as count')
        .whereNotNull('user_email')
        .groupBy('user_email')
        .orderBy('count', 'desc')
        .limit(10);

      // Table activity (most audited tables)
      const tableActivity = await baseQuery.clone()
        .select('table_name')
        .count('* as count')
        .whereNotNull('table_name')
        .groupBy('table_name')
        .orderBy('count', 'desc')
        .limit(10);

      // Timeline data (daily activity for last 30 days)
      const timelineData = await baseQuery.clone()
        .select(db.raw("DATE(timestamp) as date"))
        .count('* as count')
        .where('timestamp', '>=', db.raw("NOW() - INTERVAL '30 days'"))
        .groupBy(db.raw("DATE(timestamp)"))
        .orderBy('date', 'asc');

      return {
        totalLogs,
        riskDistribution,
        actionDistribution,
        userActivity: userActivity.map(row => ({
          userEmail: row.user_email,
          count: parseInt(row.count)
        })),
        tableActivity: tableActivity.map(row => ({
          tableName: row.table_name,
          count: parseInt(row.count)
        })),
        timelineData: timelineData.map(row => ({
          date: row.date,
          count: parseInt(row.count)
        }))
      };

    } catch (error) {
      console.error('❌ Failed to generate audit summary:', error);
      throw new Error('Failed to generate audit summary');
    }
  }

  /**
   * Get suspicious activity report
   */
  static async getSuspiciousActivity(filters: AuditReportFilters = {}) {
    try {
      // High-risk activities
      const highRiskActivities = await this.getAuditLogs({
        ...filters,
        riskLevel: 'HIGH',
        limit: 50
      });

      // Failed access attempts
      const failedAccess = await this.getAccessLogs({
        startDate: filters.startDate,
        endDate: filters.endDate,
        success: false,
        limit: 50
      });

      // Multiple failed attempts from same IP
      const suspiciousIPs = await db('access_audit_logs')
        .select('ip_address')
        .count('* as failed_attempts')
        .where('success', false)
        .where('timestamp', '>=', filters.startDate || db.raw("NOW() - INTERVAL '24 hours'"))
        .groupBy('ip_address')
        .having('failed_attempts', '>=', 5)
        .orderBy('failed_attempts', 'desc');

      // Unusual activity patterns (users active outside normal hours)
      const unusualActivity = await db('audit_logs')
        .select('user_email', 'ip_address')
        .count('* as activity_count')
        .whereRaw("EXTRACT(HOUR FROM timestamp) NOT BETWEEN 8 AND 18") // Outside 8 AM - 6 PM
        .where('timestamp', '>=', filters.startDate || db.raw("NOW() - INTERVAL '7 days'"))
        .whereNotNull('user_email')
        .groupBy('user_email', 'ip_address')
        .having('activity_count', '>=', 10)
        .orderBy('activity_count', 'desc');

      return {
        highRiskActivities: highRiskActivities.logs,
        failedAccess: failedAccess.logs,
        suspiciousIPs,
        unusualActivity
      };

    } catch (error) {
      console.error('❌ Failed to get suspicious activity:', error);
      throw new Error('Failed to get suspicious activity');
    }
  }

  /**
   * Export audit data to CSV format
   */
  static async exportToCsv(type: 'audit' | 'access' | 'financial', filters: any = {}) {
    try {
      let data: any[] = [];
      let headers: string[] = [];

      switch (type) {
        case 'audit':
          const auditResult = await this.getAuditLogs({ ...filters, limit: 10000 });
          data = auditResult.logs;
          headers = [
            'ID', 'Table Name', 'Record ID', 'Action Type', 'User Email',
            'User Role', 'IP Address', 'Risk Level', 'Timestamp', 'Reason'
          ];
          break;

        case 'access':
          const accessResult = await this.getAccessLogs({ ...filters, limit: 10000 });
          data = accessResult.logs;
          headers = [
            'ID', 'Action', 'Resource', 'Success', 'Failure Reason',
            'IP Address', 'Timestamp'
          ];
          break;

        case 'financial':
          const financialResult = await this.getFinancialAuditTrail({ ...filters, limit: 10000 });
          data = financialResult.logs;
          headers = [
            'ID', 'Transaction ID', 'Account ID', 'Amount', 'Currency',
            'Action Type', 'Authorization Level', 'Timestamp'
          ];
          break;
      }

      // Convert to CSV format
      const csvRows = [headers.join(',')];

      data.forEach(row => {
        const values = headers.map(header => {
          const key = header.toLowerCase().replace(/ /g, '_');
          let value = row[key] || '';

          // Handle special formatting
          if (typeof value === 'object') {
            value = JSON.stringify(value);
          }

          // Escape commas and quotes
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            value = `"${value.replace(/"/g, '""')}"`;
          }

          return value;
        });

        csvRows.push(values.join(','));
      });

      return csvRows.join('\n');

    } catch (error) {
      console.error('❌ Failed to export to CSV:', error);
      throw new Error('Failed to export to CSV');
    }
  }
}
