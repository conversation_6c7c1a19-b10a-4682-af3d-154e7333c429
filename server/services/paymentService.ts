import Stripe from 'stripe';
import { db } from '../config/database';
import { v4 as uuidv4 } from 'uuid';

export interface PaymentMethod {
  id: string;
  companyId: string;
  customerId?: string;
  type: 'CREDIT_CARD' | 'BANK_ACCOUNT' | 'ACH' | 'WIRE';
  provider: 'STRIPE' | 'PLAID' | 'MANUAL';
  providerPaymentMethodId?: string;
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
  isActive: boolean;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface Payment {
  id: string;
  companyId: string;
  invoiceId?: string;
  customerId?: string;
  paymentMethodId: string;
  amount: number;
  currency: string;
  status: 'PENDING' | 'PROCESSING' | 'SUCCEEDED' | 'FAILED' | 'CANCELLED' | 'REFUNDED';
  paymentIntentId?: string;
  providerTransactionId?: string;
  description?: string;
  metadata?: any;
  failureReason?: string;
  refundedAmount?: number;
  refundedAt?: Date;
  processedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Invoice {
  id: string;
  companyId: string;
  customerId: string;
  invoiceNumber: string;
  amount: number;
  currency: string;
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  dueDate: Date;
  paidAt?: Date;
  description?: string;
  lineItems: InvoiceLineItem[];
  paymentLink?: string;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface InvoiceLineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
  taxRate?: number;
  taxAmount?: number;
}

export interface Customer {
  id: string;
  companyId: string;
  name: string;
  email: string;
  phone?: string;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
  stripeCustomerId?: string;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

class PaymentService {
  private stripe: Stripe;

  constructor() {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-05-28.basil',
    });
  }

  /**
   * Create a customer
   */
  public async createCustomer(
    companyId: string,
    customerData: Omit<Customer, 'id' | 'companyId' | 'stripeCustomerId' | 'createdAt' | 'updatedAt'>
  ): Promise<Customer> {
    try {
      // Create customer in Stripe
      const stripeCustomer = await this.stripe.customers.create({
        name: customerData.name,
        email: customerData.email,
        phone: customerData.phone,
        address: customerData.address,
        metadata: {
          companyId,
          ...customerData.metadata,
        },
      });

      const customerId = uuidv4();
      const customer: Customer = {
        id: customerId,
        companyId,
        ...customerData,
        stripeCustomerId: stripeCustomer.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Save to database
      await db('customers').insert({
        id: customer.id,
        company_id: customer.companyId,
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address ? JSON.stringify(customer.address) : null,
        stripe_customer_id: customer.stripeCustomerId,
        metadata: customer.metadata ? JSON.stringify(customer.metadata) : null,
        created_at: customer.createdAt,
        updated_at: customer.updatedAt,
      });

      return customer;
    } catch (error) {
      console.error('Failed to create customer:', error);
      throw new Error('Failed to create customer');
    }
  }

  /**
   * Create payment method
   */
  public async createPaymentMethod(
    companyId: string,
    customerId: string,
    paymentMethodData: {
      type: PaymentMethod['type'];
      stripePaymentMethodId?: string;
      isDefault?: boolean;
    }
  ): Promise<PaymentMethod> {
    try {
      const customer = await this.getCustomer(companyId, customerId);
      if (!customer) {
        throw new Error('Customer not found');
      }

      let stripePaymentMethod: Stripe.PaymentMethod | undefined;
      if (paymentMethodData.stripePaymentMethodId) {
        // Attach existing Stripe payment method to customer
        stripePaymentMethod = await this.stripe.paymentMethods.attach(
          paymentMethodData.stripePaymentMethodId,
          { customer: customer.stripeCustomerId! }
        );
      }

      const paymentMethodId = uuidv4();
      const paymentMethod: PaymentMethod = {
        id: paymentMethodId,
        companyId,
        customerId,
        type: paymentMethodData.type,
        provider: 'STRIPE',
        providerPaymentMethodId: stripePaymentMethod?.id,
        last4: stripePaymentMethod?.card?.last4,
        brand: stripePaymentMethod?.card?.brand,
        expiryMonth: stripePaymentMethod?.card?.exp_month,
        expiryYear: stripePaymentMethod?.card?.exp_year,
        isDefault: paymentMethodData.isDefault || false,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // If this is the default payment method, unset others
      if (paymentMethod.isDefault) {
        await db('payment_methods')
          .where('customer_id', customerId)
          .update({ is_default: false });
      }

      // Save to database
      await db('payment_methods').insert({
        id: paymentMethod.id,
        company_id: paymentMethod.companyId,
        customer_id: paymentMethod.customerId,
        type: paymentMethod.type,
        provider: paymentMethod.provider,
        provider_payment_method_id: paymentMethod.providerPaymentMethodId,
        last4: paymentMethod.last4,
        brand: paymentMethod.brand,
        expiry_month: paymentMethod.expiryMonth,
        expiry_year: paymentMethod.expiryYear,
        is_default: paymentMethod.isDefault,
        is_active: paymentMethod.isActive,
        metadata: paymentMethod.metadata ? JSON.stringify(paymentMethod.metadata) : null,
        created_at: paymentMethod.createdAt,
        updated_at: paymentMethod.updatedAt,
      });

      return paymentMethod;
    } catch (error) {
      console.error('Failed to create payment method:', error);
      throw new Error('Failed to create payment method');
    }
  }

  /**
   * Create invoice
   */
  public async createInvoice(
    companyId: string,
    invoiceData: Omit<Invoice, 'id' | 'companyId' | 'invoiceNumber' | 'paymentLink' | 'createdAt' | 'updatedAt'>
  ): Promise<Invoice> {
    try {
      const invoiceId = uuidv4();
      const invoiceNumber = await this.generateInvoiceNumber(companyId);

      const invoice: Invoice = {
        id: invoiceId,
        companyId,
        invoiceNumber,
        ...invoiceData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Save to database
      await db('invoices').insert({
        id: invoice.id,
        company_id: invoice.companyId,
        customer_id: invoice.customerId,
        invoice_number: invoice.invoiceNumber,
        amount: invoice.amount,
        currency: invoice.currency,
        status: invoice.status,
        due_date: invoice.dueDate,
        paid_at: invoice.paidAt,
        description: invoice.description,
        line_items: JSON.stringify(invoice.lineItems),
        payment_link: invoice.paymentLink,
        metadata: invoice.metadata ? JSON.stringify(invoice.metadata) : null,
        created_at: invoice.createdAt,
        updated_at: invoice.updatedAt,
      });

      // Create payment link if invoice is sent
      if (invoice.status === 'SENT') {
        const paymentLink = await this.createPaymentLink(invoice);
        await db('invoices')
          .where('id', invoiceId)
          .update({ payment_link: paymentLink });
        invoice.paymentLink = paymentLink;
      }

      return invoice;
    } catch (error) {
      console.error('Failed to create invoice:', error);
      throw new Error('Failed to create invoice');
    }
  }

  /**
   * Process payment
   */
  public async processPayment(
    companyId: string,
    paymentData: {
      customerId: string;
      paymentMethodId: string;
      amount: number;
      currency: string;
      description?: string;
      invoiceId?: string;
      metadata?: any;
    }
  ): Promise<Payment> {
    try {
      const customer = await this.getCustomer(companyId, paymentData.customerId);
      const paymentMethod = await this.getPaymentMethod(companyId, paymentData.paymentMethodId);

      if (!customer || !paymentMethod) {
        throw new Error('Customer or payment method not found');
      }

      // Create payment intent in Stripe
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(paymentData.amount * 100), // Convert to cents
        currency: paymentData.currency.toLowerCase(),
        customer: customer.stripeCustomerId!,
        payment_method: paymentMethod.providerPaymentMethodId!,
        description: paymentData.description,
        confirm: true,
        return_url: `${process.env.FRONTEND_URL}/payments/return`,
        metadata: {
          companyId,
          invoiceId: paymentData.invoiceId || '',
          ...paymentData.metadata,
        },
      });

      const paymentId = uuidv4();
      const payment: Payment = {
        id: paymentId,
        companyId,
        customerId: paymentData.customerId,
        paymentMethodId: paymentData.paymentMethodId,
        invoiceId: paymentData.invoiceId,
        amount: paymentData.amount,
        currency: paymentData.currency,
        status: this.mapStripeStatus(paymentIntent.status),
        paymentIntentId: paymentIntent.id,
        providerTransactionId: paymentIntent.id,
        description: paymentData.description,
        metadata: paymentData.metadata,
        processedAt: paymentIntent.status === 'succeeded' ? new Date() : undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Save to database
      await db('payments').insert({
        id: payment.id,
        company_id: payment.companyId,
        customer_id: payment.customerId,
        payment_method_id: payment.paymentMethodId,
        invoice_id: payment.invoiceId,
        amount: payment.amount,
        currency: payment.currency,
        status: payment.status,
        payment_intent_id: payment.paymentIntentId,
        provider_transaction_id: payment.providerTransactionId,
        description: payment.description,
        metadata: payment.metadata ? JSON.stringify(payment.metadata) : null,
        processed_at: payment.processedAt,
        created_at: payment.createdAt,
        updated_at: payment.updatedAt,
      });

      // Update invoice status if payment succeeded
      if (payment.status === 'SUCCEEDED' && payment.invoiceId) {
        await this.markInvoiceAsPaid(payment.invoiceId);
      }

      return payment;
    } catch (error) {
      console.error('Failed to process payment:', error);
      throw new Error('Failed to process payment');
    }
  }

  /**
   * Refund payment
   */
  public async refundPayment(
    companyId: string,
    paymentId: string,
    amount?: number,
    reason?: string
  ): Promise<Payment> {
    try {
      const payment = await this.getPayment(companyId, paymentId);
      if (!payment) {
        throw new Error('Payment not found');
      }

      if (payment.status !== 'SUCCEEDED') {
        throw new Error('Can only refund successful payments');
      }

      const refundAmount = amount || payment.amount;

      // Create refund in Stripe
      await this.stripe.refunds.create({
        payment_intent: payment.paymentIntentId!,
        amount: Math.round(refundAmount * 100), // Convert to cents
        reason: reason as Stripe.RefundCreateParams.Reason,
        metadata: {
          companyId,
          originalPaymentId: paymentId,
        },
      });

      // Update payment in database
      const updatedPayment = {
        ...payment,
        status: refundAmount === payment.amount ? 'REFUNDED' : 'SUCCEEDED',
        refundedAmount: (payment.refundedAmount || 0) + refundAmount,
        refundedAt: new Date(),
        updatedAt: new Date(),
      } as Payment;

      await db('payments')
        .where('id', paymentId)
        .update({
          status: updatedPayment.status,
          refunded_amount: updatedPayment.refundedAmount,
          refunded_at: updatedPayment.refundedAt,
          updated_at: updatedPayment.updatedAt,
        });

      return updatedPayment;
    } catch (error) {
      console.error('Failed to refund payment:', error);
      throw new Error('Failed to refund payment');
    }
  }

  /**
   * Handle Stripe webhook
   */
  public async handleWebhook(event: Stripe.Event): Promise<void> {
    try {
      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
          break;
        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;
        default:
          console.log('Unhandled Stripe webhook event:', event.type);
      }
    } catch (error: any) {
      console.error('Failed to handle Stripe webhook:', error);
      throw new Error(`Webhook processing failed: ${error.message}`);
    }
  }

  // Private helper methods
  private async getCustomer(companyId: string, customerId: string): Promise<Customer | null> {
    const customer = await db('customers')
      .where('id', customerId)
      .where('company_id', companyId)
      .first();

    if (!customer) return null;

    return {
      id: customer.id,
      companyId: customer.company_id,
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      address: customer.address ? JSON.parse(customer.address) : undefined,
      stripeCustomerId: customer.stripe_customer_id,
      metadata: customer.metadata ? JSON.parse(customer.metadata) : undefined,
      createdAt: customer.created_at,
      updatedAt: customer.updated_at,
    };
  }

  private async getPaymentMethod(companyId: string, paymentMethodId: string): Promise<PaymentMethod | null> {
    const paymentMethod = await db('payment_methods')
      .where('id', paymentMethodId)
      .where('company_id', companyId)
      .first();

    if (!paymentMethod) return null;

    return {
      id: paymentMethod.id,
      companyId: paymentMethod.company_id,
      customerId: paymentMethod.customer_id,
      type: paymentMethod.type,
      provider: paymentMethod.provider,
      providerPaymentMethodId: paymentMethod.provider_payment_method_id,
      last4: paymentMethod.last4,
      brand: paymentMethod.brand,
      expiryMonth: paymentMethod.expiry_month,
      expiryYear: paymentMethod.expiry_year,
      isDefault: paymentMethod.is_default,
      isActive: paymentMethod.is_active,
      metadata: paymentMethod.metadata ? JSON.parse(paymentMethod.metadata) : undefined,
      createdAt: paymentMethod.created_at,
      updatedAt: paymentMethod.updated_at,
    };
  }

  private async getPayment(companyId: string, paymentId: string): Promise<Payment | null> {
    const payment = await db('payments')
      .where('id', paymentId)
      .where('company_id', companyId)
      .first();

    if (!payment) return null;

    return {
      id: payment.id,
      companyId: payment.company_id,
      customerId: payment.customer_id,
      paymentMethodId: payment.payment_method_id,
      invoiceId: payment.invoice_id,
      amount: parseFloat(payment.amount),
      currency: payment.currency,
      status: payment.status,
      paymentIntentId: payment.payment_intent_id,
      providerTransactionId: payment.provider_transaction_id,
      description: payment.description,
      metadata: payment.metadata ? JSON.parse(payment.metadata) : undefined,
      failureReason: payment.failure_reason,
      refundedAmount: payment.refunded_amount ? parseFloat(payment.refunded_amount) : undefined,
      refundedAt: payment.refunded_at,
      processedAt: payment.processed_at,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at,
    };
  }

  private async generateInvoiceNumber(companyId: string): Promise<string> {
    const year = new Date().getFullYear();
    const prefix = `INV-${year}-`;
    
    const lastInvoice = await db('invoices')
      .where('company_id', companyId)
      .where('invoice_number', 'like', `${prefix}%`)
      .orderBy('invoice_number', 'desc')
      .first();

    let nextNumber = 1;
    if (lastInvoice) {
      const lastNumber = parseInt(lastInvoice.invoice_number.replace(prefix, ''));
      nextNumber = lastNumber + 1;
    }

    return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
  }

  private async createPaymentLink(invoice: Invoice): Promise<string> {
    try {
      // First create a product
      const product = await this.stripe.products.create({
        name: `Invoice ${invoice.invoiceNumber}`,
        description: invoice.description || `Payment for invoice ${invoice.invoiceNumber}`,
        metadata: {
          companyId: invoice.companyId,
          invoiceId: invoice.id,
        },
      });

      // Then create a price for the product
      const price = await this.stripe.prices.create({
        product: product.id,
        unit_amount: Math.round(invoice.amount * 100),
        currency: invoice.currency.toLowerCase(),
      });

      // Create Stripe payment link
      const paymentLink = await this.stripe.paymentLinks.create({
        line_items: [
          {
            price: price.id,
            quantity: 1,
          },
        ],
        metadata: {
          companyId: invoice.companyId,
          invoiceId: invoice.id,
        },
      });

      return paymentLink.url;
    } catch (error: any) {
      console.error('Failed to create payment link:', error);
      throw new Error('Failed to create payment link');
    }
  }

  private async markInvoiceAsPaid(invoiceId: string): Promise<void> {
    await db('invoices')
      .where('id', invoiceId)
      .update({
        status: 'PAID',
        paid_at: new Date(),
        updated_at: new Date(),
      });
  }

  private mapStripeStatus(stripeStatus: string): Payment['status'] {
    switch (stripeStatus) {
      case 'requires_payment_method':
      case 'requires_confirmation':
      case 'requires_action':
        return 'PENDING';
      case 'processing':
        return 'PROCESSING';
      case 'succeeded':
        return 'SUCCEEDED';
      case 'canceled':
        return 'CANCELLED';
      default:
        return 'FAILED';
    }
  }

  private async handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    const invoiceId = paymentIntent.metadata.invoiceId;

    // Update payment status
    await db('payments')
      .where('payment_intent_id', paymentIntent.id)
      .update({
        status: 'SUCCEEDED',
        processed_at: new Date(),
        updated_at: new Date(),
      });

    // Update invoice if applicable
    if (invoiceId) {
      await this.markInvoiceAsPaid(invoiceId);
    }
  }

  private async handlePaymentFailed(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    await db('payments')
      .where('payment_intent_id', paymentIntent.id)
      .update({
        status: 'FAILED',
        failure_reason: paymentIntent.last_payment_error?.message,
        updated_at: new Date(),
      });
  }

  private async handleInvoicePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    const invoiceId = invoice.metadata?.invoiceId;

    if (invoiceId) {
      await this.markInvoiceAsPaid(invoiceId);
    }
  }
}

export const paymentService = new PaymentService();
