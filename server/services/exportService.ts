import puppeteer from 'puppeteer';
import ExcelJS from 'exceljs';
import { db } from '../config/database';
import { v4 as uuidv4 } from 'uuid';

export interface ExportOptions {
  format: 'PDF' | 'EXCEL' | 'CSV';
  template?: string;
  includeCharts?: boolean;
  includeDetails?: boolean;
  orientation?: 'portrait' | 'landscape';
  paperSize?: 'A4' | 'Letter' | 'Legal';
  companyLogo?: boolean;
  watermark?: string;
  password?: string;
}

export interface ReportData {
  title: string;
  subtitle?: string;
  companyInfo: {
    name: string;
    address?: string;
    phone?: string;
    email?: string;
    logo?: string;
  };
  reportDate: string;
  periodInfo?: {
    startDate?: string;
    endDate?: string;
    asOfDate?: string;
  };
  tables: Array<{
    headers: string[];
    rows: (string | number)[][];
    title?: string;
    subtitle?: string;
    footer?: string;
  }>;
  charts?: Array<{
    type: 'bar' | 'pie' | 'line' | 'area';
    title: string;
    data: any[];
    labels: string[];
    colors?: string[];
  }>;
  summary?: {
    totalAssets?: number;
    totalLiabilities?: number;
    totalEquity?: number;
    netIncome?: number;
    totalRevenue?: number;
    totalExpenses?: number;
  };
  notes?: string[];
  metadata?: {
    generatedBy: string;
    generatedAt: string;
    reportType: string;
    version: string;
  };
}

class ExportService {
  /**
   * Generate PDF from report data
   */
  public async generatePDF(reportData: ReportData, options: ExportOptions): Promise<Buffer> {
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
      const page = await browser.newPage();
      
      // Generate HTML content
      const htmlContent = this.generateHTMLReport(reportData, options);
      
      await page.setContent(htmlContent, { waitUntil: 'networkidle0' });
      
      // Configure PDF options
      const pdfOptions: any = {
        format: options.paperSize || 'A4',
        orientation: options.orientation || 'portrait',
        printBackground: true,
        margin: {
          top: '1in',
          right: '0.5in',
          bottom: '1in',
          left: '0.5in'
        },
        displayHeaderFooter: true,
        headerTemplate: this.generatePDFHeader(reportData, options),
        footerTemplate: this.generatePDFFooter(reportData, options),
      };
      
      const pdfBuffer = await page.pdf(pdfOptions);

      // Convert Uint8Array to Buffer
      const buffer = Buffer.from(pdfBuffer);

      // Add password protection if specified
      if (options.password) {
        return this.addPDFPassword(buffer, options.password);
      }

      return buffer;
    } finally {
      await browser.close();
    }
  }

  /**
   * Generate Excel from report data
   */
  public async generateExcel(reportData: ReportData, options: ExportOptions): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    
    // Set workbook properties
    workbook.creator = reportData.metadata?.generatedBy || 'Accounting System';
    workbook.lastModifiedBy = reportData.metadata?.generatedBy || 'Accounting System';
    workbook.created = new Date();
    workbook.modified = new Date();
    
    // Create main worksheet
    const worksheet = workbook.addWorksheet(reportData.title);
    
    // Add company header
    this.addExcelHeader(worksheet, reportData, options);
    
    let currentRow = 8; // Start after header
    
    // Add tables
    for (const table of reportData.tables) {
      currentRow = this.addExcelTable(worksheet, table, currentRow);
      currentRow += 2; // Add spacing between tables
    }
    
    // Add summary if provided
    if (reportData.summary) {
      currentRow = this.addExcelSummary(worksheet, reportData.summary, currentRow);
    }
    
    // Add charts if requested and provided
    if (options.includeCharts && reportData.charts) {
      for (const chartData of reportData.charts) {
        currentRow = this.addExcelChart(worksheet, chartData, currentRow);
        currentRow += 15; // Add spacing for chart
      }
    }
    
    // Style the worksheet
    this.styleExcelWorksheet(worksheet, reportData, options);
    
    // Add password protection if specified
    if (options.password) {
      await worksheet.protect(options.password, {});
    }
    
    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  /**
   * Generate comprehensive report package
   */
  public async generateReportPackage(
    companyId: string,
    _reportType: 'MONTHLY' | 'QUARTERLY' | 'ANNUAL',
    period: { startDate: string; endDate: string },
    options: any
  ): Promise<Buffer> {
    // Get all required reports
    const reports = await this.generateAllReports(companyId, period, options);
    
    if (options.format === 'BOTH') {
      // Create ZIP file with both formats
      return this.createReportZip(reports);
    } else if (options.format === 'PDF') {
      // Combine all reports into single PDF
      return this.combinePDFReports(reports);
    } else {
      // Create Excel workbook with multiple sheets
      return this.combineExcelReports(reports);
    }
  }

  /**
   * Schedule automated report generation
   */
  public async scheduleReport(
    companyId: string,
    schedule: any,
    userId: string
  ): Promise<{ scheduleId: string; nextRun: string }> {
    const scheduleId = uuidv4();
    
    // Calculate next run time
    const nextRun = this.calculateNextRun(schedule);
    
    // Save schedule to database
    await db('report_schedules').insert({
      id: scheduleId,
      company_id: companyId,
      user_id: userId,
      report_type: schedule.reportType,
      frequency: schedule.frequency,
      day_of_week: schedule.dayOfWeek,
      day_of_month: schedule.dayOfMonth,
      time: schedule.time,
      timezone: schedule.timezone,
      recipients: JSON.stringify(schedule.recipients),
      format: schedule.format,
      include_charts: schedule.includeCharts,
      password: schedule.password,
      next_run: nextRun,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    });
    
    return { scheduleId, nextRun: nextRun.toISOString() };
  }

  /**
   * Get scheduled reports for a company
   */
  public async getScheduledReports(companyId: string): Promise<any[]> {
    const schedules = await db('report_schedules')
      .where('company_id', companyId)
      .where('is_active', true)
      .orderBy('created_at', 'desc');
    
    return schedules.map(schedule => ({
      ...schedule,
      recipients: JSON.parse(schedule.recipients)
    }));
  }

  /**
   * Delete scheduled report
   */
  public async deleteScheduledReport(scheduleId: string, userId: string): Promise<void> {
    await db('report_schedules')
      .where('id', scheduleId)
      .where('user_id', userId)
      .update({
        is_active: false,
        updated_at: new Date()
      });
  }

  /**
   * Get export templates
   */
  public async getExportTemplates(): Promise<any[]> {
    return await db('export_templates')
      .where('is_active', true)
      .orderBy('name');
  }

  /**
   * Create custom export template
   */
  public async createTemplate(template: any, userId: string): Promise<{ templateId: string }> {
    const templateId = uuidv4();
    
    await db('export_templates').insert({
      id: templateId,
      name: template.name,
      description: template.description,
      report_types: JSON.stringify(template.reportTypes),
      layout: JSON.stringify(template.layout),
      styling: JSON.stringify(template.styling),
      created_by: userId,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    });
    
    return { templateId };
  }

  /**
   * Get export history
   */
  public async getExportHistory(
    companyId: string,
    page: number,
    limit: number
  ): Promise<{ exports: any[]; total: number; page: number; totalPages: number }> {
    const offset = (page - 1) * limit;
    
    const [exports, totalCount] = await Promise.all([
      db('export_history')
        .where('company_id', companyId)
        .orderBy('created_at', 'desc')
        .limit(limit)
        .offset(offset),
      db('export_history')
        .where('company_id', companyId)
        .count('* as count')
        .first()
    ]);
    
    const total = parseInt(totalCount?.count as string) || 0;
    const totalPages = Math.ceil(total / limit);
    
    return {
      exports,
      total,
      page,
      totalPages
    };
  }

  /**
   * Log export activity
   */
  public async logExport(
    companyId: string,
    userId: string,
    reportType: string,
    format: string,
    filename: string
  ): Promise<void> {
    await db('export_history').insert({
      id: uuidv4(),
      company_id: companyId,
      user_id: userId,
      report_type: reportType,
      format: format,
      filename: filename,
      file_size: 0, // Will be updated after generation
      created_at: new Date()
    });
  }

  // Private helper methods
  private generateHTMLReport(reportData: ReportData, options: ExportOptions): string {
    // Implementation for generating HTML content
    // This would include CSS styling and HTML structure
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>${reportData.title}</title>
          <style>
            ${this.getReportCSS(options)}
          </style>
        </head>
        <body>
          ${this.generateReportBody(reportData, options)}
        </body>
      </html>
    `;
  }

  private getReportCSS(_options: ExportOptions): string {
    // Return comprehensive CSS for report styling
    return `
      body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
      .header { text-align: center; margin-bottom: 30px; }
      .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
      .report-title { font-size: 20px; margin-bottom: 5px; }
      .report-subtitle { font-size: 16px; color: #666; }
      table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
      th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
      th { background-color: #f2f2f2; font-weight: bold; }
      .currency { text-align: right; }
      .summary { background-color: #f9f9f9; padding: 15px; margin-top: 20px; }
      .watermark { position: fixed; bottom: 10px; right: 10px; opacity: 0.3; }
    `;
  }

  private generateReportBody(reportData: ReportData, options: ExportOptions): string {
    // Generate the main report body HTML
    let html = `
      <div class="header">
        <div class="company-name">${reportData.companyInfo.name}</div>
        <div class="report-title">${reportData.title}</div>
        ${reportData.subtitle ? `<div class="report-subtitle">${reportData.subtitle}</div>` : ''}
      </div>
    `;

    // Add tables
    for (const table of reportData.tables) {
      html += this.generateTableHTML(table);
    }

    // Add summary
    if (reportData.summary) {
      html += this.generateSummaryHTML(reportData.summary);
    }

    // Add watermark
    if (options.watermark) {
      html += `<div class="watermark">${options.watermark}</div>`;
    }

    return html;
  }

  private generateTableHTML(table: any): string {
    let html = `<table>`;
    
    // Add headers
    html += '<thead><tr>';
    for (const header of table.headers) {
      html += `<th>${header}</th>`;
    }
    html += '</tr></thead>';
    
    // Add rows
    html += '<tbody>';
    for (const row of table.rows) {
      html += '<tr>';
      for (const cell of row) {
        const cellClass = typeof cell === 'number' ? 'currency' : '';
        html += `<td class="${cellClass}">${this.formatCellValue(cell)}</td>`;
      }
      html += '</tr>';
    }
    html += '</tbody></table>';
    
    return html;
  }

  private generateSummaryHTML(summary: any): string {
    let html = '<div class="summary"><h3>Summary</h3>';
    
    for (const [key, value] of Object.entries(summary)) {
      if (value !== undefined && value !== null) {
        const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        html += `<p><strong>${label}:</strong> ${this.formatCurrencyValue(value as number)}</p>`;
      }
    }
    
    html += '</div>';
    return html;
  }

  private formatCellValue(value: any): string {
    if (typeof value === 'number') {
      return this.formatCurrencyValue(value);
    }
    return String(value);
  }

  private formatCurrencyValue(value: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  }

  // Additional helper methods would be implemented here...
  private generatePDFHeader(reportData: ReportData, _options: ExportOptions): string {
    return `<div style="font-size: 10px; text-align: center;">
      <span class="title">${reportData.title}</span>
    </div>`;
  }

  private generatePDFFooter(_reportData: ReportData, _options: ExportOptions): string {
    return `<div style="font-size: 8px; text-align: center;">
      <span class="pageNumber"></span> | Generated: <span class="date"></span>
    </div>`;
  }

  private addPDFPassword(buffer: Buffer, _password: string): Buffer {
    // Implementation for adding password protection to PDF
    // This would require a PDF manipulation library
    return buffer;
  }

  private addExcelHeader(worksheet: any, reportData: ReportData, _options: ExportOptions): void {
    // Add company name
    worksheet.getCell('A1').value = reportData.companyInfo.name;
    worksheet.getCell('A1').font = { size: 16, bold: true };
    
    // Add report title
    worksheet.getCell('A3').value = reportData.title;
    worksheet.getCell('A3').font = { size: 14, bold: true };
    
    // Add report date
    worksheet.getCell('A5').value = `Report Date: ${reportData.reportDate}`;
  }

  private addExcelTable(worksheet: any, table: any, startRow: number): number {
    let currentRow = startRow;
    
    // Add table title if provided
    if (table.title) {
      worksheet.getCell(`A${currentRow}`).value = table.title;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
      currentRow += 2;
    }
    
    // Add headers
    table.headers.forEach((header: string, index: number) => {
      const cell = worksheet.getCell(currentRow, index + 1);
      cell.value = header;
      cell.font = { bold: true };
      cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };
    });
    currentRow++;
    
    // Add data rows
    table.rows.forEach((row: any[]) => {
      row.forEach((cell: any, index: number) => {
        worksheet.getCell(currentRow, index + 1).value = cell;
      });
      currentRow++;
    });
    
    return currentRow;
  }

  private addExcelSummary(worksheet: any, summary: any, startRow: number): number {
    let currentRow = startRow + 2;
    
    worksheet.getCell(`A${currentRow}`).value = 'Summary';
    worksheet.getCell(`A${currentRow}`).font = { bold: true, size: 12 };
    currentRow += 2;
    
    for (const [key, value] of Object.entries(summary)) {
      if (value !== undefined && value !== null) {
        const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        worksheet.getCell(`A${currentRow}`).value = label;
        worksheet.getCell(`B${currentRow}`).value = value;
        currentRow++;
      }
    }
    
    return currentRow;
  }

  private addExcelChart(_worksheet: any, _chartData: any, startRow: number): number {
    // Implementation for adding charts to Excel
    // This would require ExcelJS chart functionality
    return startRow + 10;
  }

  private styleExcelWorksheet(worksheet: any, _reportData: ReportData, _options: ExportOptions): void {
    // Apply styling to the worksheet
    worksheet.columns.forEach((column: any) => {
      column.width = 15;
    });
  }

  private async generateAllReports(_companyId: string, _period: any, _options: any): Promise<any[]> {
    // Implementation for generating all required reports
    return [];
  }

  private async createReportZip(_reports: any[]): Promise<Buffer> {
    // Implementation for creating ZIP file
    return Buffer.alloc(0);
  }

  private async combinePDFReports(_reports: any[]): Promise<Buffer> {
    // Implementation for combining PDF reports
    return Buffer.alloc(0);
  }

  private async combineExcelReports(_reports: any[]): Promise<Buffer> {
    // Implementation for combining Excel reports
    return Buffer.alloc(0);
  }

  private calculateNextRun(_schedule: any): Date {
    // Implementation for calculating next run time based on schedule
    return new Date();
  }
}

export const exportService = new ExportService();
