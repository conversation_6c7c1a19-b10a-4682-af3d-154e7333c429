import db from "../config/database";
import { auditService } from "./auditService";

// EFD Compliance interfaces
export interface EFDDevice {
  id?: string;
  companyId: string;
  deviceSerial: string;
  deviceModel: string;
  manufacturer: string;
  firmwareVersion?: string;
  traDeviceId: string;
  traCertificateNumber?: string;
  registrationDate: Date;
  certificateExpiry?: Date;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'EXPIRED';
  connectionType: 'USB' | 'ETHERNET' | 'WIFI' | 'BLUETOOTH';
  ipAddress?: string;
  port?: number;
  locationName?: string;
  physicalAddress?: string;
  latitude?: number;
  longitude?: number;
  deviceSettings?: any;
  lastSync?: Date;
  lastHeartbeat?: Date;
}

export interface EFDTransaction {
  id?: string;
  companyId: string;
  efdDeviceId: string;
  transactionId?: string;
  invoiceId?: string;
  efdReceiptNumber: string;
  efdInternalNumber?: string;
  fiscalCode: string;
  qrCode?: string;
  transactionType: 'SALE' | 'REFUND' | 'VOID' | 'TRAINING' | 'COPY';
  grossAmount: number;
  vatAmount: number;
  netAmount: number;
  discountAmount: number;
  paymentMethod: 'CASH' | 'CARD' | 'MOBILE_MONEY' | 'BANK_TRANSFER' | 'CREDIT' | 'MIXED';
  cashAmount: number;
  cardAmount: number;
  mobileMoney: number;
  otherAmount: number;
  customerName?: string;
  customerTin?: string;
  customerVrn?: string;
  customerMobile?: string;
  efdTimestamp: Date;
  submissionStatus: 'PENDING' | 'SUBMITTED' | 'ACCEPTED' | 'REJECTED';
  submittedToTra?: Date;
  traResponse?: any;
  rejectionReason?: string;
  zReportNumber?: string;
  zReportDate?: Date;
}

export interface EFDZReport {
  id?: string;
  companyId: string;
  efdDeviceId: string;
  zReportNumber: string;
  reportDate: Date;
  reportTimestamp: Date;
  fiscalDayIdentifier: string;
  totalTransactions: number;
  totalGrossSales: number;
  totalVatAmount: number;
  totalNetSales: number;
  totalDiscounts: number;
  totalRefunds: number;
  cashTotal: number;
  cardTotal: number;
  mobileMoney: number;
  otherPaymentTotal: number;
  salesCount: number;
  refundCount: number;
  voidCount: number;
  trainingCount: number;
  reportStatus: 'GENERATED' | 'SUBMITTED' | 'ACCEPTED' | 'REJECTED';
  submittedToTra?: Date;
  traResponse?: any;
  rejectionReason?: string;
  detailedBreakdown?: any;
  efdSignature?: string;
}

export class EFDComplianceService {
  /**
   * Register a new EFD device
   */
  static async registerDevice(deviceData: EFDDevice): Promise<string> {
    try {
      const [result] = await db('efd_devices').insert({
        company_id: deviceData.companyId,
        device_serial: deviceData.deviceSerial,
        device_model: deviceData.deviceModel,
        manufacturer: deviceData.manufacturer,
        firmware_version: deviceData.firmwareVersion,
        tra_device_id: deviceData.traDeviceId,
        tra_certificate_number: deviceData.traCertificateNumber,
        registration_date: deviceData.registrationDate,
        certificate_expiry: deviceData.certificateExpiry,
        status: deviceData.status,
        connection_type: deviceData.connectionType,
        ip_address: deviceData.ipAddress,
        port: deviceData.port,
        location_name: deviceData.locationName,
        physical_address: deviceData.physicalAddress,
        latitude: deviceData.latitude,
        longitude: deviceData.longitude,
        device_settings: deviceData.deviceSettings ? JSON.stringify(deviceData.deviceSettings) : null,
        created_at: new Date(),
        updated_at: new Date()
      }).returning('id');
      
      // Log audit event
      await auditService.log({
        companyId: deviceData.companyId,
        action: 'REGISTER_EFD_DEVICE',
        description: `EFD device registered: ${deviceData.deviceSerial}`,
        metadata: { 
          deviceSerial: deviceData.deviceSerial,
          traDeviceId: deviceData.traDeviceId,
          manufacturer: deviceData.manufacturer 
        }
      });
      
      return result.id;
    } catch (error) {
      console.error('Error registering EFD device:', error);
      throw new Error('Failed to register EFD device');
    }
  }

  /**
   * Record EFD transaction
   */
  static async recordTransaction(transactionData: EFDTransaction): Promise<string> {
    try {
      // Generate fiscal code if not provided
      if (!transactionData.fiscalCode) {
        transactionData.fiscalCode = await this.generateFiscalCode(
          transactionData.efdDeviceId,
          transactionData.efdReceiptNumber
        );
      }

      const [result] = await db('efd_transactions').insert({
        company_id: transactionData.companyId,
        efd_device_id: transactionData.efdDeviceId,
        transaction_id: transactionData.transactionId,
        invoice_id: transactionData.invoiceId,
        efd_receipt_number: transactionData.efdReceiptNumber,
        efd_internal_number: transactionData.efdInternalNumber,
        fiscal_code: transactionData.fiscalCode,
        qr_code: transactionData.qrCode,
        transaction_type: transactionData.transactionType,
        gross_amount: transactionData.grossAmount,
        vat_amount: transactionData.vatAmount,
        net_amount: transactionData.netAmount,
        discount_amount: transactionData.discountAmount,
        payment_method: transactionData.paymentMethod,
        cash_amount: transactionData.cashAmount,
        card_amount: transactionData.cardAmount,
        mobile_money_amount: transactionData.mobileMoney,
        other_amount: transactionData.otherAmount,
        customer_name: transactionData.customerName,
        customer_tin: transactionData.customerTin,
        customer_vrn: transactionData.customerVrn,
        customer_mobile: transactionData.customerMobile,
        efd_timestamp: transactionData.efdTimestamp,
        submission_status: transactionData.submissionStatus,
        created_at: new Date(),
        updated_at: new Date()
      }).returning('id');
      
      // Log audit event
      await auditService.log({
        companyId: transactionData.companyId,
        action: 'RECORD_EFD_TRANSACTION',
        description: `EFD transaction recorded: ${transactionData.efdReceiptNumber}`,
        metadata: { 
          efdReceiptNumber: transactionData.efdReceiptNumber,
          fiscalCode: transactionData.fiscalCode,
          grossAmount: transactionData.grossAmount,
          transactionType: transactionData.transactionType
        }
      });
      
      return result.id;
    } catch (error) {
      console.error('Error recording EFD transaction:', error);
      throw new Error('Failed to record EFD transaction');
    }
  }

  /**
   * Generate Z-Report for a device and date
   */
  static async generateZReport(
    companyId: string,
    efdDeviceId: string,
    reportDate: Date
  ): Promise<EFDZReport> {
    try {
      const startDate = new Date(reportDate);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(reportDate);
      endDate.setHours(23, 59, 59, 999);
      
      // Get all transactions for the day
      const transactions = await db('efd_transactions')
        .where('company_id', companyId)
        .where('efd_device_id', efdDeviceId)
        .whereBetween('efd_timestamp', [startDate, endDate])
        .orderBy('efd_timestamp');
      
      // Calculate totals
      let totalGrossSales = 0;
      let totalVatAmount = 0;
      let totalNetSales = 0;
      let totalDiscounts = 0;
      let totalRefunds = 0;
      let cashTotal = 0;
      let cardTotal = 0;
      let mobileMoney = 0;
      let otherPaymentTotal = 0;
      let salesCount = 0;
      let refundCount = 0;
      let voidCount = 0;
      let trainingCount = 0;
      
      transactions.forEach(tx => {
        const gross = parseFloat(tx.gross_amount);
        const vat = parseFloat(tx.vat_amount);
        const net = parseFloat(tx.net_amount);
        const discount = parseFloat(tx.discount_amount);
        
        if (tx.transaction_type === 'SALE') {
          totalGrossSales += gross;
          totalVatAmount += vat;
          totalNetSales += net;
          totalDiscounts += discount;
          salesCount++;
        } else if (tx.transaction_type === 'REFUND') {
          totalRefunds += gross;
          refundCount++;
        } else if (tx.transaction_type === 'VOID') {
          voidCount++;
        } else if (tx.transaction_type === 'TRAINING') {
          trainingCount++;
        }
        
        // Payment method totals
        cashTotal += parseFloat(tx.cash_amount);
        cardTotal += parseFloat(tx.card_amount);
        mobileMoney += parseFloat(tx.mobile_money_amount);
        otherPaymentTotal += parseFloat(tx.other_amount);
      });
      
      // Generate Z-Report number
      const zReportNumber = await this.generateZReportNumber(efdDeviceId, reportDate);
      const fiscalDayIdentifier = `${efdDeviceId}-${reportDate.toISOString().split('T')[0]}`;
      
      const zReport: EFDZReport = {
        companyId,
        efdDeviceId,
        zReportNumber,
        reportDate,
        reportTimestamp: new Date(),
        fiscalDayIdentifier,
        totalTransactions: transactions.length,
        totalGrossSales: Math.round(totalGrossSales * 100) / 100,
        totalVatAmount: Math.round(totalVatAmount * 100) / 100,
        totalNetSales: Math.round(totalNetSales * 100) / 100,
        totalDiscounts: Math.round(totalDiscounts * 100) / 100,
        totalRefunds: Math.round(totalRefunds * 100) / 100,
        cashTotal: Math.round(cashTotal * 100) / 100,
        cardTotal: Math.round(cardTotal * 100) / 100,
        mobileMoney: Math.round(mobileMoney * 100) / 100,
        otherPaymentTotal: Math.round(otherPaymentTotal * 100) / 100,
        salesCount,
        refundCount,
        voidCount,
        trainingCount,
        reportStatus: 'GENERATED',
        detailedBreakdown: {
          transactions: transactions.map(tx => ({
            receiptNumber: tx.efd_receipt_number,
            fiscalCode: tx.fiscal_code,
            type: tx.transaction_type,
            amount: tx.gross_amount,
            timestamp: tx.efd_timestamp
          }))
        }
      };
      
      return zReport;
    } catch (error) {
      console.error('Error generating Z-Report:', error);
      throw new Error('Failed to generate Z-Report');
    }
  }

  /**
   * Save Z-Report to database
   */
  static async saveZReport(zReport: EFDZReport): Promise<string> {
    try {
      const [result] = await db('efd_z_reports').insert({
        company_id: zReport.companyId,
        efd_device_id: zReport.efdDeviceId,
        z_report_number: zReport.zReportNumber,
        report_date: zReport.reportDate,
        report_timestamp: zReport.reportTimestamp,
        fiscal_day_identifier: zReport.fiscalDayIdentifier,
        total_transactions: zReport.totalTransactions,
        total_gross_sales: zReport.totalGrossSales,
        total_vat_amount: zReport.totalVatAmount,
        total_net_sales: zReport.totalNetSales,
        total_discounts: zReport.totalDiscounts,
        total_refunds: zReport.totalRefunds,
        cash_total: zReport.cashTotal,
        card_total: zReport.cardTotal,
        mobile_money_total: zReport.mobileMoney,
        other_payment_total: zReport.otherPaymentTotal,
        sales_count: zReport.salesCount,
        refund_count: zReport.refundCount,
        void_count: zReport.voidCount,
        training_count: zReport.trainingCount,
        report_status: zReport.reportStatus,
        detailed_breakdown: JSON.stringify(zReport.detailedBreakdown),
        efd_signature: zReport.efdSignature,
        created_at: new Date(),
        updated_at: new Date()
      }).returning('id');
      
      // Log audit event
      await auditService.log({
        companyId: zReport.companyId,
        action: 'GENERATE_Z_REPORT',
        description: `Z-Report generated: ${zReport.zReportNumber}`,
        metadata: { 
          zReportNumber: zReport.zReportNumber,
          reportDate: zReport.reportDate,
          totalTransactions: zReport.totalTransactions,
          totalGrossSales: zReport.totalGrossSales
        }
      });
      
      return result.id;
    } catch (error) {
      console.error('Error saving Z-Report:', error);
      throw new Error('Failed to save Z-Report');
    }
  }

  /**
   * Get EFD devices for a company
   */
  static async getDevices(
    companyId: string,
    limit: number = 10,
    offset: number = 0
  ): Promise<{ devices: EFDDevice[]; total: number }> {
    try {
      const [devices, countResult] = await Promise.all([
        db('efd_devices')
          .where('company_id', companyId)
          .orderBy('created_at', 'desc')
          .limit(limit)
          .offset(offset),
        db('efd_devices')
          .where('company_id', companyId)
          .count('* as count')
          .first()
      ]);
      
      return {
        devices: devices.map(d => ({
          id: d.id,
          companyId: d.company_id,
          deviceSerial: d.device_serial,
          deviceModel: d.device_model,
          manufacturer: d.manufacturer,
          firmwareVersion: d.firmware_version,
          traDeviceId: d.tra_device_id,
          traCertificateNumber: d.tra_certificate_number,
          registrationDate: new Date(d.registration_date),
          certificateExpiry: d.certificate_expiry ? new Date(d.certificate_expiry) : undefined,
          status: d.status,
          connectionType: d.connection_type,
          ipAddress: d.ip_address,
          port: d.port,
          locationName: d.location_name,
          physicalAddress: d.physical_address,
          latitude: d.latitude ? parseFloat(d.latitude) : undefined,
          longitude: d.longitude ? parseFloat(d.longitude) : undefined,
          deviceSettings: d.device_settings ? JSON.parse(d.device_settings) : undefined,
          lastSync: d.last_sync ? new Date(d.last_sync) : undefined,
          lastHeartbeat: d.last_heartbeat ? new Date(d.last_heartbeat) : undefined
        })),
        total: parseInt(countResult?.count || '0')
      };
    } catch (error) {
      console.error('Error getting EFD devices:', error);
      throw new Error('Failed to get EFD devices');
    }
  }

  /**
   * Get EFD transactions for a company
   */
  static async getTransactions(
    companyId: string,
    efdDeviceId?: string,
    limit: number = 10,
    offset: number = 0
  ): Promise<{ transactions: EFDTransaction[]; total: number }> {
    try {
      let query = db('efd_transactions')
        .where('company_id', companyId);
      
      if (efdDeviceId) {
        query = query.where('efd_device_id', efdDeviceId);
      }
      
      const [transactions, countResult] = await Promise.all([
        query.clone()
          .orderBy('efd_timestamp', 'desc')
          .limit(limit)
          .offset(offset),
        query.clone()
          .count('* as count')
          .first()
      ]);
      
      return {
        transactions: transactions.map(t => ({
          id: t.id,
          companyId: t.company_id,
          efdDeviceId: t.efd_device_id,
          transactionId: t.transaction_id,
          invoiceId: t.invoice_id,
          efdReceiptNumber: t.efd_receipt_number,
          efdInternalNumber: t.efd_internal_number,
          fiscalCode: t.fiscal_code,
          qrCode: t.qr_code,
          transactionType: t.transaction_type,
          grossAmount: parseFloat(t.gross_amount),
          vatAmount: parseFloat(t.vat_amount),
          netAmount: parseFloat(t.net_amount),
          discountAmount: parseFloat(t.discount_amount),
          paymentMethod: t.payment_method,
          cashAmount: parseFloat(t.cash_amount),
          cardAmount: parseFloat(t.card_amount),
          mobileMoney: parseFloat(t.mobile_money_amount),
          otherAmount: parseFloat(t.other_amount),
          customerName: t.customer_name,
          customerTin: t.customer_tin,
          customerVrn: t.customer_vrn,
          customerMobile: t.customer_mobile,
          efdTimestamp: new Date(t.efd_timestamp),
          submissionStatus: t.submission_status,
          submittedToTra: t.submitted_to_tra ? new Date(t.submitted_to_tra) : undefined,
          traResponse: t.tra_response ? JSON.parse(t.tra_response) : undefined,
          rejectionReason: t.rejection_reason,
          zReportNumber: t.z_report_number,
          zReportDate: t.z_report_date ? new Date(t.z_report_date) : undefined
        })),
        total: parseInt(countResult?.count || '0')
      };
    } catch (error) {
      console.error('Error getting EFD transactions:', error);
      throw new Error('Failed to get EFD transactions');
    }
  }

  /**
   * Generate fiscal code for transaction
   */
  private static async generateFiscalCode(efdDeviceId: string, receiptNumber: string): Promise<string> {
    const timestamp = Date.now();
    const deviceShort = efdDeviceId.slice(-8);
    return `FC-${deviceShort}-${receiptNumber}-${timestamp}`;
  }

  /**
   * Generate Z-Report number
   */
  private static async generateZReportNumber(efdDeviceId: string, reportDate: Date): Promise<string> {
    const dateStr = reportDate.toISOString().split('T')[0].replace(/-/g, '');
    const deviceShort = efdDeviceId.slice(-6);
    
    // Get count of reports for this device to generate sequence
    const count = await db('efd_z_reports')
      .where('efd_device_id', efdDeviceId)
      .count('* as count')
      .first();
    
    const sequence = (parseInt(count?.count || '0') + 1).toString().padStart(4, '0');
    
    return `Z-${deviceShort}-${dateStr}-${sequence}`;
  }

  /**
   * Check if transaction amount requires EFD
   */
  static requiresEFD(amount: number): boolean {
    // Tanzania EFD requirement: transactions above 5,000 TZS
    return amount >= 5000;
  }

  /**
   * Validate EFD transaction data
   */
  static validateTransaction(transaction: Partial<EFDTransaction>): string[] {
    const errors: string[] = [];
    
    if (!transaction.efdReceiptNumber) {
      errors.push('EFD receipt number is required');
    }
    
    if (!transaction.grossAmount || transaction.grossAmount <= 0) {
      errors.push('Valid gross amount is required');
    }
    
    if (!transaction.paymentMethod) {
      errors.push('Payment method is required');
    }
    
    if (!transaction.efdTimestamp) {
      errors.push('EFD timestamp is required');
    }
    
    // Validate payment amounts sum to gross amount
    const totalPayments = (transaction.cashAmount || 0) + 
                         (transaction.cardAmount || 0) + 
                         (transaction.mobileMoney || 0) + 
                         (transaction.otherAmount || 0);
    
    if (Math.abs(totalPayments - (transaction.grossAmount || 0)) > 0.01) {
      errors.push('Payment amounts must sum to gross amount');
    }
    
    return errors;
  }
}

export const efdComplianceService = EFDComplianceService;
