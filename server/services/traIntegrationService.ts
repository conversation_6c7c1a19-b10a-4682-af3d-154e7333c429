import db from "../config/database";
import { auditService } from "./auditService";

// TRA Integration interfaces
export interface TRAVATReturn {
  id?: string;
  companyId: string;
  returnPeriod: string; // YYYY-MM
  totalSales: number;
  vatOnSales: number;
  totalPurchases: number;
  vatOnPurchases: number;
  netVatPayable: number;
  vatRefundClaimed: number;
  zeroRatedSupplies: number;
  exemptSupplies: number;
  importVat: number;
  penalties: number;
  interest: number;
  status: 'DRAFT' | 'SUBMITTED' | 'ACCEPTED' | 'REJECTED' | 'AMENDED';
  dueDate?: Date;
  submittedDate?: Date;
  traReferenceNumber?: string;
}

export interface TRAWithholdingTax {
  id?: string;
  companyId: string;
  transactionId?: string;
  contactId?: string;
  whtCertificateNumber?: string;
  grossAmount: number;
  whtRate: number;
  whtAmount: number;
  netAmount: number;
  whtCategory: string;
  serviceDescription?: string;
  serviceDate: Date;
  paymentDate: Date;
  submissionStatus: 'PENDING' | 'SUBMITTED' | 'ACCEPTED' | 'REJECTED';
  traReference?: string;
}

export interface TRATaxRate {
  taxType: string;
  category: string;
  rate: number;
  description?: string;
  thresholdAmount?: number;
  effectiveFrom: Date;
  effectiveTo?: Date;
}

export interface TRACalculationResult {
  vatAmount: number;
  whtAmount: number;
  netAmount: number;
  applicableTaxes: {
    taxType: string;
    category: string;
    rate: number;
    amount: number;
    description: string;
  }[];
  complianceFlags: string[];
  requiresSubmission: boolean;
}

export class TRAIntegrationService {
  /**
   * Calculate VAT for a transaction
   */
  static async calculateVAT(
    companyId: string,
    amount: number,
    isVATInclusive: boolean = false,
    category: string = 'STANDARD'
  ): Promise<{ vatAmount: number; netAmount: number; vatRate: number }> {
    try {
      // Get current VAT rate
      const vatRate = await this.getCurrentTaxRate('VAT', category);
      
      let vatAmount: number;
      let netAmount: number;
      
      if (isVATInclusive) {
        // Amount includes VAT - extract VAT
        netAmount = amount / (1 + vatRate.rate / 100);
        vatAmount = amount - netAmount;
      } else {
        // Amount excludes VAT - add VAT
        netAmount = amount;
        vatAmount = amount * (vatRate.rate / 100);
      }
      
      return {
        vatAmount: Math.round(vatAmount * 100) / 100,
        netAmount: Math.round(netAmount * 100) / 100,
        vatRate: vatRate.rate
      };
    } catch (error) {
      console.error('Error calculating VAT:', error);
      throw new Error('Failed to calculate VAT');
    }
  }

  /**
   * Calculate Withholding Tax
   */
  static async calculateWithholdingTax(
    companyId: string,
    grossAmount: number,
    whtCategory: string
  ): Promise<{ whtAmount: number; netAmount: number; whtRate: number }> {
    try {
      // Check if amount meets threshold
      const whtRate = await this.getCurrentTaxRate('WHT', whtCategory);
      
      if (grossAmount < (whtRate.thresholdAmount || 0)) {
        return {
          whtAmount: 0,
          netAmount: grossAmount,
          whtRate: 0
        };
      }
      
      const whtAmount = grossAmount * (whtRate.rate / 100);
      const netAmount = grossAmount - whtAmount;
      
      return {
        whtAmount: Math.round(whtAmount * 100) / 100,
        netAmount: Math.round(netAmount * 100) / 100,
        whtRate: whtRate.rate
      };
    } catch (error) {
      console.error('Error calculating WHT:', error);
      throw new Error('Failed to calculate withholding tax');
    }
  }

  /**
   * Get current tax rate for a specific type and category
   */
  static async getCurrentTaxRate(taxType: string, category: string): Promise<TRATaxRate> {
    try {
      const currentDate = new Date();
      
      const taxRate = await db('tra_tax_rates')
        .where('tax_type', taxType)
        .where('category', category)
        .where('is_active', true)
        .where('effective_from', '<=', currentDate)
        .where(function() {
          this.whereNull('effective_to').orWhere('effective_to', '>=', currentDate);
        })
        .orderBy('effective_from', 'desc')
        .first();
      
      if (!taxRate) {
        throw new Error(`Tax rate not found for ${taxType} - ${category}`);
      }
      
      return {
        taxType: taxRate.tax_type,
        category: taxRate.category,
        rate: parseFloat(taxRate.rate),
        description: taxRate.description,
        thresholdAmount: taxRate.threshold_amount ? parseFloat(taxRate.threshold_amount) : undefined,
        effectiveFrom: new Date(taxRate.effective_from),
        effectiveTo: taxRate.effective_to ? new Date(taxRate.effective_to) : undefined
      };
    } catch (error) {
      console.error('Error getting tax rate:', error);
      throw error;
    }
  }

  /**
   * Create VAT return for a period
   */
  static async createVATReturn(vatReturn: TRAVATReturn): Promise<string> {
    try {
      const [result] = await db('tra_vat_returns').insert({
        company_id: vatReturn.companyId,
        return_period: vatReturn.returnPeriod,
        total_sales: vatReturn.totalSales,
        vat_on_sales: vatReturn.vatOnSales,
        total_purchases: vatReturn.totalPurchases,
        vat_on_purchases: vatReturn.vatOnPurchases,
        net_vat_payable: vatReturn.netVatPayable,
        vat_refund_claimed: vatReturn.vatRefundClaimed,
        zero_rated_supplies: vatReturn.zeroRatedSupplies,
        exempt_supplies: vatReturn.exemptSupplies,
        import_vat: vatReturn.importVat,
        penalties: vatReturn.penalties,
        interest: vatReturn.interest,
        status: vatReturn.status,
        due_date: vatReturn.dueDate,
        created_at: new Date(),
        updated_at: new Date()
      }).returning('id');
      
      // Log audit event
      await auditService.log({
        companyId: vatReturn.companyId,
        action: 'CREATE_VAT_RETURN',
        description: `VAT return created for period ${vatReturn.returnPeriod}`,
        metadata: { returnPeriod: vatReturn.returnPeriod, netVatPayable: vatReturn.netVatPayable }
      });
      
      return result.id;
    } catch (error) {
      console.error('Error creating VAT return:', error);
      throw new Error('Failed to create VAT return');
    }
  }

  /**
   * Record withholding tax transaction
   */
  static async recordWithholdingTax(whtData: TRAWithholdingTax): Promise<string> {
    try {
      const [result] = await db('tra_withholding_tax').insert({
        company_id: whtData.companyId,
        transaction_id: whtData.transactionId,
        contact_id: whtData.contactId,
        wht_certificate_number: whtData.whtCertificateNumber,
        gross_amount: whtData.grossAmount,
        wht_rate: whtData.whtRate,
        wht_amount: whtData.whtAmount,
        net_amount: whtData.netAmount,
        wht_category: whtData.whtCategory,
        service_description: whtData.serviceDescription,
        service_date: whtData.serviceDate,
        payment_date: whtData.paymentDate,
        submission_status: whtData.submissionStatus,
        tra_reference: whtData.traReference,
        created_at: new Date(),
        updated_at: new Date()
      }).returning('id');
      
      // Log audit event
      await auditService.log({
        companyId: whtData.companyId,
        action: 'RECORD_WHT',
        description: `Withholding tax recorded for ${whtData.whtCategory}`,
        metadata: { 
          whtCategory: whtData.whtCategory, 
          grossAmount: whtData.grossAmount,
          whtAmount: whtData.whtAmount 
        }
      });
      
      return result.id;
    } catch (error) {
      console.error('Error recording withholding tax:', error);
      throw new Error('Failed to record withholding tax');
    }
  }

  /**
   * Generate VAT return for a specific period
   */
  static async generateVATReturn(
    companyId: string, 
    returnPeriod: string
  ): Promise<TRAVATReturn> {
    try {
      const startDate = new Date(`${returnPeriod}-01`);
      const endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
      
      // Get all transactions for the period
      const transactions = await db('transactions')
        .where('company_id', companyId)
        .whereBetween('transaction_date', [startDate, endDate])
        .where('status', 'COMPLETED');
      
      let totalSales = 0;
      let vatOnSales = 0;
      let totalPurchases = 0;
      let vatOnPurchases = 0;
      let zeroRatedSupplies = 0;
      let exemptSupplies = 0;
      
      for (const transaction of transactions) {
        const amount = parseFloat(transaction.amount);
        
        if (transaction.type === 'INCOME') {
          totalSales += amount;
          // Calculate VAT on sales (assuming 18% standard rate)
          const vatCalc = await this.calculateVAT(companyId, amount, true);
          vatOnSales += vatCalc.vatAmount;
        } else if (transaction.type === 'EXPENSE') {
          totalPurchases += amount;
          // Calculate VAT on purchases
          const vatCalc = await this.calculateVAT(companyId, amount, true);
          vatOnPurchases += vatCalc.vatAmount;
        }
      }
      
      const netVatPayable = vatOnSales - vatOnPurchases;
      
      // Calculate due date (20th of following month)
      const dueDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 20);
      
      return {
        companyId,
        returnPeriod,
        totalSales: Math.round(totalSales * 100) / 100,
        vatOnSales: Math.round(vatOnSales * 100) / 100,
        totalPurchases: Math.round(totalPurchases * 100) / 100,
        vatOnPurchases: Math.round(vatOnPurchases * 100) / 100,
        netVatPayable: Math.round(netVatPayable * 100) / 100,
        vatRefundClaimed: 0,
        zeroRatedSupplies: Math.round(zeroRatedSupplies * 100) / 100,
        exemptSupplies: Math.round(exemptSupplies * 100) / 100,
        importVat: 0,
        penalties: 0,
        interest: 0,
        status: 'DRAFT',
        dueDate
      };
    } catch (error) {
      console.error('Error generating VAT return:', error);
      throw new Error('Failed to generate VAT return');
    }
  }

  /**
   * Get VAT returns for a company
   */
  static async getVATReturns(
    companyId: string,
    limit: number = 10,
    offset: number = 0
  ): Promise<{ returns: TRAVATReturn[]; total: number }> {
    try {
      const [returns, countResult] = await Promise.all([
        db('tra_vat_returns')
          .where('company_id', companyId)
          .orderBy('return_period', 'desc')
          .limit(limit)
          .offset(offset),
        db('tra_vat_returns')
          .where('company_id', companyId)
          .count('* as count')
          .first()
      ]);
      
      return {
        returns: returns.map(r => ({
          id: r.id,
          companyId: r.company_id,
          returnPeriod: r.return_period,
          totalSales: parseFloat(r.total_sales),
          vatOnSales: parseFloat(r.vat_on_sales),
          totalPurchases: parseFloat(r.total_purchases),
          vatOnPurchases: parseFloat(r.vat_on_purchases),
          netVatPayable: parseFloat(r.net_vat_payable),
          vatRefundClaimed: parseFloat(r.vat_refund_claimed),
          zeroRatedSupplies: parseFloat(r.zero_rated_supplies),
          exemptSupplies: parseFloat(r.exempt_supplies),
          importVat: parseFloat(r.import_vat),
          penalties: parseFloat(r.penalties),
          interest: parseFloat(r.interest),
          status: r.status,
          dueDate: r.due_date,
          submittedDate: r.submitted_date,
          traReferenceNumber: r.tra_reference_number
        })),
        total: parseInt(countResult?.count || '0')
      };
    } catch (error) {
      console.error('Error getting VAT returns:', error);
      throw new Error('Failed to get VAT returns');
    }
  }

  /**
   * Get withholding tax records for a company
   */
  static async getWithholdingTaxRecords(
    companyId: string,
    limit: number = 10,
    offset: number = 0
  ): Promise<{ records: TRAWithholdingTax[]; total: number }> {
    try {
      const [records, countResult] = await Promise.all([
        db('tra_withholding_tax')
          .where('company_id', companyId)
          .orderBy('service_date', 'desc')
          .limit(limit)
          .offset(offset),
        db('tra_withholding_tax')
          .where('company_id', companyId)
          .count('* as count')
          .first()
      ]);

      return {
        records: records.map(r => ({
          id: r.id,
          companyId: r.company_id,
          transactionId: r.transaction_id,
          contactId: r.contact_id,
          whtCertificateNumber: r.wht_certificate_number,
          grossAmount: parseFloat(r.gross_amount),
          whtRate: parseFloat(r.wht_rate),
          whtAmount: parseFloat(r.wht_amount),
          netAmount: parseFloat(r.net_amount),
          whtCategory: r.wht_category,
          serviceDescription: r.service_description,
          serviceDate: new Date(r.service_date),
          paymentDate: new Date(r.payment_date),
          submissionStatus: r.submission_status,
          traReference: r.tra_reference
        })),
        total: parseInt(countResult?.count || '0')
      };
    } catch (error) {
      console.error('Error getting WHT records:', error);
      throw new Error('Failed to get withholding tax records');
    }
  }

  /**
   * Get all active tax rates
   */
  static async getAllActiveTaxRates(): Promise<TRATaxRate[]> {
    try {
      const currentDate = new Date();

      const taxRates = await db('tra_tax_rates')
        .where('is_active', true)
        .where('effective_from', '<=', currentDate)
        .where(function() {
          this.whereNull('effective_to').orWhere('effective_to', '>=', currentDate);
        })
        .orderBy(['tax_type', 'category']);

      return taxRates.map(rate => ({
        taxType: rate.tax_type,
        category: rate.category,
        rate: parseFloat(rate.rate),
        description: rate.description,
        thresholdAmount: rate.threshold_amount ? parseFloat(rate.threshold_amount) : undefined,
        effectiveFrom: new Date(rate.effective_from),
        effectiveTo: rate.effective_to ? new Date(rate.effective_to) : undefined
      }));
    } catch (error) {
      console.error('Error getting all tax rates:', error);
      throw new Error('Failed to get tax rates');
    }
  }
}

export const traIntegrationService = TRAIntegrationService;
