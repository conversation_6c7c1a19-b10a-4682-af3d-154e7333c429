import Bull, { Job, Queue, JobOptions } from 'bull';
import { cacheService } from './cacheService';

export interface JobData {
  id: string;
  type: string;
  payload: any;
  companyId: string;
  userId?: string;
  metadata?: Record<string, any>;
}

export interface JobProgress {
  percentage: number;
  message: string;
  data?: any;
}

export interface JobResult {
  success: boolean;
  data?: any;
  error?: string;
  metrics?: {
    duration: number;
    recordsProcessed?: number;
    errorsCount?: number;
  };
}

class JobQueueService {
  private queues: Map<string, Queue> = new Map();
  private processors: Map<string, Function> = new Map();
  private isInitialized = false;

  constructor() {
    this.setupDefaultQueues();
  }

  private setupDefaultQueues(): void {
    // High priority queue for real-time operations
    this.createQueue('high-priority', {
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
      },
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    // Default queue for standard operations
    this.createQueue('default', {
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
      },
      defaultJobOptions: {
        removeOnComplete: 50,
        removeOnFail: 25,
        attempts: 2,
        backoff: {
          type: 'exponential',
          delay: 5000,
        },
      },
    });

    // Low priority queue for batch operations
    this.createQueue('batch', {
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
      },
      defaultJobOptions: {
        removeOnComplete: 25,
        removeOnFail: 10,
        attempts: 1,
        delay: 1000,
      },
    });

    // Scheduled queue for recurring tasks
    this.createQueue('scheduled', {
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
      },
      defaultJobOptions: {
        removeOnComplete: 10,
        removeOnFail: 5,
        attempts: 1,
      },
    });
  }

  private createQueue(name: string, options: any): Queue {
    const queue = new Bull(name, options);
    this.queues.set(name, queue);

    // Setup event handlers
    queue.on('completed', (job: Job, result: any) => {
      console.log(`Job ${job.id} completed in queue ${name}:`, result);
    });

    queue.on('failed', (job: Job, err: Error) => {
      console.error(`Job ${job.id} failed in queue ${name}:`, err.message);
    });

    queue.on('progress', (job: Job, progress: number) => {
      console.log(`Job ${job.id} progress: ${progress}%`);
    });

    queue.on('stalled', (job: Job) => {
      console.warn(`Job ${job.id} stalled in queue ${name}`);
    });

    return queue;
  }

  /**
   * Initialize job processors
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    // Register default processors
    this.registerProcessor('batch-import', this.processBatchImport.bind(this));
    this.registerProcessor('batch-export', this.processBatchExport.bind(this));
    this.registerProcessor('bank-sync', this.processBankSync.bind(this));
    this.registerProcessor('reconciliation', this.processReconciliation.bind(this));
    this.registerProcessor('report-generation', this.processReportGeneration.bind(this));
    this.registerProcessor('notification', this.processNotification.bind(this));
    this.registerProcessor('cleanup', this.processCleanup.bind(this));

    // Start processing
    for (const [queueName, queue] of this.queues) {
      queue.process('*', this.processJob.bind(this));
      console.log(`Started processing queue: ${queueName}`);
    }

    this.isInitialized = true;
    console.log('Job queue service initialized');
  }

  /**
   * Register a job processor
   */
  public registerProcessor(jobType: string, processor: Function): void {
    this.processors.set(jobType, processor);
  }

  /**
   * Add job to queue
   */
  public async addJob(
    queueName: string,
    jobType: string,
    data: Omit<JobData, 'id' | 'type'>,
    options: JobOptions = {}
  ): Promise<Job> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const jobData: JobData = {
      id: `${jobType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: jobType,
      ...data,
    };

    const job = await queue.add(jobType, jobData, options);
    console.log(`Added job ${job.id} to queue ${queueName}`);
    
    return job;
  }

  /**
   * Get job status
   */
  public async getJobStatus(queueName: string, jobId: string): Promise<any> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const job = await queue.getJob(jobId);
    if (!job) {
      return null;
    }

    return {
      id: job.id,
      name: job.name,
      data: job.data,
      progress: job.progress(),
      state: await job.getState(),
      createdAt: new Date(job.timestamp),
      processedOn: job.processedOn ? new Date(job.processedOn) : null,
      finishedOn: job.finishedOn ? new Date(job.finishedOn) : null,
      failedReason: job.failedReason,
      returnvalue: job.returnvalue,
    };
  }

  /**
   * Get queue statistics
   */
  public async getQueueStats(queueName: string): Promise<any> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const [waiting, active, completed, failed, delayed] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed(),
    ]);

    return {
      name: queueName,
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
      total: waiting.length + active.length + completed.length + failed.length + delayed.length,
    };
  }

  /**
   * Process individual job
   */
  private async processJob(job: Job): Promise<JobResult> {
    const startTime = Date.now();
    const { type, payload, companyId, userId } = job.data as JobData;

    try {
      console.log(`Processing job ${job.id} of type ${type}`);

      const processor = this.processors.get(type);
      if (!processor) {
        throw new Error(`No processor found for job type: ${type}`);
      }

      // Update progress
      await job.progress(0);

      // Execute processor
      const result = await processor(job, payload, { companyId, userId });

      // Update progress to 100%
      await job.progress(100);

      const duration = Date.now() - startTime;
      console.log(`Job ${job.id} completed in ${duration}ms`);

      return {
        success: true,
        data: result,
        metrics: {
          duration,
          recordsProcessed: result?.recordsProcessed,
          errorsCount: result?.errorsCount,
        },
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`Job ${job.id} failed after ${duration}ms:`, error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metrics: {
          duration,
        },
      };
    }
  }

  /**
   * Job processors
   */
  private async processBatchImport(job: Job, payload: any, context: any): Promise<any> {
    const { file, options, mapping } = payload;
    
    await job.progress(10);
    // Implementation would go here
    await job.progress(50);
    // More processing...
    await job.progress(90);
    
    return {
      recordsProcessed: 100,
      errorsCount: 0,
      successCount: 100,
    };
  }

  private async processBatchExport(job: Job, payload: any, context: any): Promise<any> {
    const { format, filters, columns } = payload;
    
    await job.progress(20);
    // Implementation would go here
    await job.progress(60);
    // More processing...
    await job.progress(90);
    
    return {
      filename: 'export.csv',
      recordCount: 500,
      fileSize: 1024000,
    };
  }

  private async processBankSync(job: Job, payload: any, context: any): Promise<any> {
    const { bankAccountId } = payload;
    
    await job.progress(25);
    // Implementation would go here
    await job.progress(75);
    
    return {
      newTransactions: 10,
      updatedTransactions: 5,
    };
  }

  private async processReconciliation(job: Job, payload: any, context: any): Promise<any> {
    const { rules, transactions } = payload;
    
    await job.progress(30);
    // Implementation would go here
    await job.progress(80);
    
    return {
      processedTransactions: 50,
      autoMatched: 30,
      rulesApplied: 45,
    };
  }

  private async processReportGeneration(job: Job, payload: any, context: any): Promise<any> {
    const { reportConfig, filters } = payload;
    
    await job.progress(40);
    // Implementation would go here
    await job.progress(85);
    
    return {
      reportId: 'report-123',
      recordCount: 1000,
      generatedAt: new Date(),
    };
  }

  private async processNotification(job: Job, payload: any, context: any): Promise<any> {
    const { type, recipients, message } = payload;
    
    await job.progress(50);
    // Implementation would go here
    
    return {
      sent: recipients.length,
      failed: 0,
    };
  }

  private async processCleanup(job: Job, payload: any, context: any): Promise<any> {
    const { type, olderThan } = payload;
    
    await job.progress(60);
    // Implementation would go here
    
    return {
      deletedRecords: 100,
      freedSpace: 1024000,
    };
  }

  /**
   * Schedule recurring job
   */
  public async scheduleRecurringJob(
    jobType: string,
    data: Omit<JobData, 'id' | 'type'>,
    cronExpression: string
  ): Promise<void> {
    const queue = this.queues.get('scheduled');
    if (!queue) {
      throw new Error('Scheduled queue not found');
    }

    await queue.add(jobType, {
      id: `${jobType}-recurring`,
      type: jobType,
      ...data,
    }, {
      repeat: { cron: cronExpression },
      removeOnComplete: 5,
      removeOnFail: 3,
    });

    console.log(`Scheduled recurring job ${jobType} with cron: ${cronExpression}`);
  }

  /**
   * Clean up completed jobs
   */
  public async cleanupJobs(): Promise<void> {
    for (const [queueName, queue] of this.queues) {
      await queue.clean(24 * 60 * 60 * 1000, 'completed'); // Remove completed jobs older than 24 hours
      await queue.clean(7 * 24 * 60 * 60 * 1000, 'failed'); // Remove failed jobs older than 7 days
      console.log(`Cleaned up jobs in queue: ${queueName}`);
    }
  }

  /**
   * Shutdown all queues
   */
  public async shutdown(): Promise<void> {
    console.log('Shutting down job queue service...');
    
    for (const [queueName, queue] of this.queues) {
      await queue.close();
      console.log(`Closed queue: ${queueName}`);
    }
    
    this.isInitialized = false;
    console.log('Job queue service shutdown complete');
  }
}

// Create singleton instance
export const jobQueueService = new JobQueueService();

// Job type constants
export const JobTypes = {
  BATCH_IMPORT: 'batch-import',
  BATCH_EXPORT: 'batch-export',
  BANK_SYNC: 'bank-sync',
  RECONCILIATION: 'reconciliation',
  REPORT_GENERATION: 'report-generation',
  NOTIFICATION: 'notification',
  CLEANUP: 'cleanup',
} as const;

// Queue name constants
export const QueueNames = {
  HIGH_PRIORITY: 'high-priority',
  DEFAULT: 'default',
  BATCH: 'batch',
  SCHEDULED: 'scheduled',
} as const;

export default jobQueueService;
