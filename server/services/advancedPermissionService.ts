import { db } from '../config/database';
import type { 
  FieldPermission, 
  TimeBasedAccess, 
  IPRestriction, 
  AccessAuditLog,
  SessionSecurity,
  SecurityPolicy,
  PermissionCheck,
  PermissionResult
} from '../types/permissions';

class AdvancedPermissionService {
  /**
   * Check comprehensive permissions including field-level, time-based, and IP restrictions
   */
  async checkPermission(check: PermissionCheck): Promise<PermissionResult> {
    try {
      // Get user's roles and company
      const userRoles = await this.getUserRoles(check.userId);
      if (!userRoles.length) {
        return { allowed: false, reason: 'No roles assigned' };
      }

      // Check basic role permissions first
      const hasBasicPermission = await this.checkBasicPermission(
        userRoles.map(r => r.id), 
        check.resource, 
        check.action
      );
      
      if (!hasBasicPermission) {
        await this.logAccess(check, false, 'Insufficient role permissions');
        return { allowed: false, reason: 'Insufficient permissions' };
      }

      // Check time-based restrictions
      const timeAllowed = await this.checkTimeBasedAccess(
        check.userId, 
        userRoles.map(r => r.id),
        check.resource, 
        check.action,
        check.context?.timestamp
      );
      
      if (!timeAllowed) {
        await this.logAccess(check, false, 'Outside allowed time window');
        return { allowed: false, reason: 'Access not allowed at this time' };
      }

      // Check IP restrictions
      const ipAllowed = await this.checkIPRestrictions(
        check.userId,
        userRoles.map(r => r.id),
        userRoles[0].companyId,
        check.context?.ipAddress
      );
      
      if (!ipAllowed) {
        await this.logAccess(check, false, 'IP address not allowed');
        return { allowed: false, reason: 'Access from this IP address is not allowed' };
      }

      // Get field-level permissions (using private method that accepts array)
      const fieldPermissions = await this.getFieldPermissions(
        userRoles.map(r => r.id),
        check.resource
      );

      // Check specific field permission if field is specified
      if (check.field) {
        const fieldPerm = fieldPermissions[check.field];
        if (!fieldPerm || fieldPerm === 'NONE') {
          await this.logAccess(check, false, `No access to field: ${check.field}`);
          return { allowed: false, reason: `No access to field: ${check.field}` };
        }
        
        if (check.action === 'WRITE' && fieldPerm === 'READ') {
          await this.logAccess(check, false, `Read-only access to field: ${check.field}`);
          return { allowed: false, reason: `Read-only access to field: ${check.field}` };
        }
      }

      // Log successful access
      await this.logAccess(check, true);

      return {
        allowed: true,
        restrictions: {
          fieldPermissions,
        },
      };
    } catch (error) {
      console.error('Permission check error:', error);
      await this.logAccess(check, false, 'System error during permission check');
      return { allowed: false, reason: 'System error' };
    }
  }

  /**
   * Check time-based access restrictions
   */
  private async checkTimeBasedAccess(
    userId: string,
    roleIds: string[],
    resource: string,
    action: string,
    timestamp?: string
  ): Promise<boolean> {
    const now = new Date(timestamp || Date.now());
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
    const currentDay = now.getDay(); // 0 = Sunday

    const restrictions = await db('time_based_access')
      .where(function() {
        this.where('user_id', userId).orWhereIn('role_id', roleIds);
      })
      .where('resource', resource)
      .where('action', action)
      .where('is_active', true)
      .where(function() {
        this.whereNull('expires_at').orWhere('expires_at', '>', now);
      });

    if (restrictions.length === 0) {
      return true; // No restrictions = allowed
    }

    // Check if current time falls within any allowed window
    for (const restriction of restrictions) {
      const allowedDays = JSON.parse(restriction.days_of_week);
      if (!allowedDays.includes(currentDay)) {
        continue;
      }

      const startTime = restriction.start_time;
      const endTime = restriction.end_time;

      // Handle overnight time windows (e.g., 22:00 to 06:00)
      if (startTime > endTime) {
        if (currentTime >= startTime || currentTime <= endTime) {
          return true;
        }
      } else {
        if (currentTime >= startTime && currentTime <= endTime) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Check IP address restrictions
   */
  private async checkIPRestrictions(
    userId: string,
    roleIds: string[],
    companyId: string,
    ipAddress?: string
  ): Promise<boolean> {
    if (!ipAddress) {
      return false; // No IP provided
    }

    const restrictions = await db('ip_restrictions')
      .where(function() {
        this.where('user_id', userId)
          .orWhereIn('role_id', roleIds)
          .orWhere('company_id', companyId);
      })
      .where('is_active', true);

    if (restrictions.length === 0) {
      return true; // No restrictions = allowed
    }

    let hasWhitelist = false;
    let isWhitelisted = false;
    let isBlacklisted = false;

    for (const restriction of restrictions) {
      if (restriction.is_whitelist) {
        hasWhitelist = true;
        if (this.ipMatches(ipAddress, restriction.ip_address, restriction.ip_range)) {
          isWhitelisted = true;
        }
      } else {
        if (this.ipMatches(ipAddress, restriction.ip_address, restriction.ip_range)) {
          isBlacklisted = true;
        }
      }
    }

    // If blacklisted, deny access
    if (isBlacklisted) {
      return false;
    }

    // If there's a whitelist and IP is not whitelisted, deny access
    if (hasWhitelist && !isWhitelisted) {
      return false;
    }

    return true;
  }

  /**
   * Check if IP address matches restriction
   */
  private ipMatches(ipAddress: string, singleIP?: string, ipRange?: string): boolean {
    if (singleIP && ipAddress === singleIP) {
      return true;
    }

    if (ipRange) {
      // Simple CIDR matching (for production, use a proper IP library)
      const [network, prefixLength] = ipRange.split('/');
      if (network && prefixLength) {
        // This is a simplified implementation
        // In production, use libraries like 'ip-range-check' or 'netmask'
        return ipAddress.startsWith(network.split('.').slice(0, Math.floor(parseInt(prefixLength) / 8)).join('.'));
      }
    }

    return false;
  }

  /**
   * Get field-level permissions for roles and resource
   */
  private async getFieldPermissions(
    roleIds: string[],
    resource: string
  ): Promise<Record<string, 'READ' | 'WRITE' | 'NONE'>> {
    const permissions = await db('field_permissions')
      .whereIn('role_id', roleIds)
      .where('resource', resource);

    const fieldPerms: Record<string, 'READ' | 'WRITE' | 'NONE'> = {};

    for (const perm of permissions) {
      const currentPerm = fieldPerms[perm.field];
      
      // Take the highest permission level
      if (!currentPerm || this.getPermissionLevel(perm.permission) > this.getPermissionLevel(currentPerm)) {
        fieldPerms[perm.field] = perm.permission;
      }
    }

    return fieldPerms;
  }

  /**
   * Get permission level for comparison
   */
  private getPermissionLevel(permission: string): number {
    switch (permission) {
      case 'NONE': return 0;
      case 'READ': return 1;
      case 'WRITE': return 2;
      default: return 0;
    }
  }

  /**
   * Check basic role permissions
   */
  private async checkBasicPermission(
    roleIds: string[],
    resource: string,
    action: string
  ): Promise<boolean> {
    const roles = await db('roles')
      .whereIn('id', roleIds)
      .where('is_active', true);

    for (const role of roles) {
      try {
        // Handle both string (JSON) and already parsed array
        const permissions = Array.isArray(role.permissions)
          ? role.permissions
          : JSON.parse(role.permissions || '[]');

        const permission = `${resource}:${action}`;

        if (permissions.includes(permission) || permissions.includes(`${resource}:*`) || permissions.includes('*:*')) {
          return true;
        }
      } catch (error) {
        console.error('Permission parsing error for role', role.name, ':', error);
      }
    }
    return false;
  }

  /**
   * Get user roles with company information
   */
  private async getUserRoles(userId: string) {
    return await db('user_companies')
      .join('roles', 'user_companies.role_id', 'roles.id')
      .join('companies', 'user_companies.company_id', 'companies.id')
      .where('user_companies.user_id', userId)
      .where('user_companies.is_active', true)
      .where('roles.is_active', true)
      .where('companies.is_active', true)
      .select(
        'roles.id',
        'roles.name',
        'roles.permissions',
        'companies.id as companyId',
        'companies.name as companyName'
      );
  }

  /**
   * Log access attempt
   */
  private async logAccess(
    check: PermissionCheck,
    success: boolean,
    failureReason?: string
  ): Promise<void> {
    try {
      const userRoles = await this.getUserRoles(check.userId);
      const companyId = userRoles[0]?.companyId;

      if (!companyId) return;

      await db('access_audit_logs').insert({
        user_id: check.userId,
        company_id: companyId,
        resource: check.resource,
        resource_id: check.resourceId,
        action: check.action,
        field: check.field,
        ip_address: check.context?.ipAddress || 'unknown',
        user_agent: check.context?.sessionId || 'unknown',
        session_id: check.context?.sessionId,
        success,
        failure_reason: failureReason,
        timestamp: new Date(check.context?.timestamp || Date.now()),
      });
    } catch (error) {
      console.error('Failed to log access:', error);
    }
  }

  /**
   * Create or update field permission
   */
  async setFieldPermission(
    roleId: string,
    resource: string,
    field: string,
    permission: 'READ' | 'WRITE' | 'NONE',
    conditions?: any
  ): Promise<void> {
    await db('field_permissions')
      .insert({
        role_id: roleId,
        resource,
        field,
        permission,
        conditions: conditions ? JSON.stringify(conditions) : null,
      })
      .onConflict(['role_id', 'resource', 'field'])
      .merge(['permission', 'conditions', 'updated_at']);
  }

  /**
   * Create time-based access restriction
   */
  async createTimeBasedAccess(data: Omit<TimeBasedAccess, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {
    await db('time_based_access').insert({
      user_id: data.userId,
      role_id: data.roleId,
      resource: data.resource,
      action: data.action,
      start_time: data.startTime,
      end_time: data.endTime,
      days_of_week: JSON.stringify(data.daysOfWeek),
      timezone: data.timezone,
      is_active: data.isActive,
      expires_at: data.expiresAt,
      created_by: data.createdBy,
    });
  }

  /**
   * Create IP restriction
   */
  async createIPRestriction(data: Omit<IPRestriction, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {
    await db('ip_restrictions').insert({
      user_id: data.userId,
      role_id: data.roleId,
      company_id: data.companyId,
      ip_address: data.ipAddress,
      ip_range: data.ipRange,
      is_whitelist: data.isWhitelist,
      description: data.description,
      is_active: data.isActive,
      created_by: data.createdBy,
    });
  }

  /**
   * Get access audit logs
   */
  async getAuditLogs(
    companyId: string,
    filters: {
      userId?: string;
      resource?: string;
      action?: string;
      success?: boolean;
      dateFrom?: string;
      dateTo?: string;
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<AccessAuditLog[]> {
    let query = db('access_audit_logs')
      .where('company_id', companyId)
      .orderBy('timestamp', 'desc');

    if (filters.userId) query = query.where('user_id', filters.userId);
    if (filters.resource) query = query.where('resource', filters.resource);
    if (filters.action) query = query.where('action', filters.action);
    if (filters.success !== undefined) query = query.where('success', filters.success);
    if (filters.dateFrom) query = query.where('timestamp', '>=', filters.dateFrom);
    if (filters.dateTo) query = query.where('timestamp', '<=', filters.dateTo);
    if (filters.limit) query = query.limit(filters.limit);
    if (filters.offset) query = query.offset(filters.offset);

    const logs = await query;
    return logs.map(this.mapDatabaseAuditLog);
  }

  /**
   * Get field permissions for a single role and resource (public method)
   */
  async getFieldPermissionsForRole(roleId: string, resource?: string) {
    let query = db('field_permissions').where('role_id', roleId);

    if (resource) {
      query = query.where('resource', resource);
    }

    const permissions = await query;
    const result: Record<string, Record<string, string>> = {};

    for (const perm of permissions) {
      if (!result[perm.resource]) {
        result[perm.resource] = {};
      }
      result[perm.resource][perm.field] = perm.permission;
    }

    return resource ? result[resource] || {} : result;
  }

  /**
   * Get time-based access restrictions
   */
  async getTimeBasedAccess(filters: {
    companyId: string;
    userId?: string;
    roleId?: string;
  }) {
    let query = db('time_based_access')
      .join('users', 'time_based_access.user_id', 'users.id')
      .join('user_companies', 'users.id', 'user_companies.user_id')
      .where('user_companies.company_id', filters.companyId);

    if (filters.userId) {
      query = query.where('time_based_access.user_id', filters.userId);
    }
    if (filters.roleId) {
      query = query.where('time_based_access.role_id', filters.roleId);
    }

    return await query.select('time_based_access.*');
  }

  /**
   * Get IP restrictions
   */
  async getIPRestrictions(filters: {
    companyId: string;
    userId?: string;
    roleId?: string;
  }) {
    let query = db('ip_restrictions').where('company_id', filters.companyId);

    if (filters.userId) {
      query = query.where('user_id', filters.userId);
    }
    if (filters.roleId) {
      query = query.where('role_id', filters.roleId);
    }

    return await query;
  }

  /**
   * Delete IP restriction
   */
  async deleteIPRestriction(restrictionId: string): Promise<void> {
    await db('ip_restrictions').where('id', restrictionId).del();
  }

  /**
   * Get security policy for company
   */
  async getSecurityPolicy(companyId: string) {
    const policy = await db('security_policies')
      .where('company_id', companyId)
      .where('is_active', true)
      .first();

    return policy;
  }

  /**
   * Update security policy
   */
  async updateSecurityPolicy(companyId: string, data: any): Promise<void> {
    await db('security_policies')
      .where('company_id', companyId)
      .update({
        name: data.name,
        description: data.description,
        settings: JSON.stringify(data.settings),
        updated_at: new Date(),
      });
  }

  /**
   * Get active sessions
   */
  async getActiveSessions(filters: {
    companyId: string;
    userId?: string;
  }) {
    let query = db('session_security')
      .join('users', 'session_security.user_id', 'users.id')
      .join('user_companies', 'users.id', 'user_companies.user_id')
      .where('user_companies.company_id', filters.companyId)
      .where('session_security.is_active', true)
      .where('session_security.expires_at', '>', new Date());

    if (filters.userId) {
      query = query.where('session_security.user_id', filters.userId);
    }

    return await query.select(
      'session_security.*',
      'users.email',
      'users.first_name',
      'users.last_name'
    );
  }

  /**
   * Terminate session
   */
  async terminateSession(sessionId: string): Promise<void> {
    await db('session_security')
      .where('id', sessionId)
      .update({
        is_active: false,
        updated_at: new Date(),
      });
  }

  /**
   * Set bulk field permissions
   */
  async setBulkFieldPermissions(
    roleId: string,
    permissions: Array<{
      resource: string;
      field: string;
      permission: 'READ' | 'WRITE' | 'NONE';
    }>
  ): Promise<void> {
    const trx = await db.transaction();

    try {
      // Delete existing permissions for this role
      await trx('field_permissions').where('role_id', roleId).del();

      // Insert new permissions
      if (permissions.length > 0) {
        await trx('field_permissions').insert(
          permissions.map(p => ({
            role_id: roleId,
            resource: p.resource,
            field: p.field,
            permission: p.permission,
          }))
        );
      }

      await trx.commit();
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  private mapDatabaseAuditLog(dbLog: any): AccessAuditLog {
    return {
      id: dbLog.id,
      userId: dbLog.user_id,
      companyId: dbLog.company_id,
      resource: dbLog.resource,
      resourceId: dbLog.resource_id,
      action: dbLog.action,
      field: dbLog.field,
      oldValue: dbLog.old_value,
      newValue: dbLog.new_value,
      ipAddress: dbLog.ip_address,
      userAgent: dbLog.user_agent,
      sessionId: dbLog.session_id,
      success: dbLog.success,
      failureReason: dbLog.failure_reason,
      metadata: dbLog.metadata,
      timestamp: dbLog.timestamp,
    };
  }
}

export const advancedPermissionService = new AdvancedPermissionService();
