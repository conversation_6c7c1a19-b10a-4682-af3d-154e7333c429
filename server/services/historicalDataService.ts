import { db } from "../config/database";

export interface HistoricalDataPoint {
  period: string;
  value: number;
  label?: string;
}

export interface ExpenseBreakdown {
  label: string;
  value: number;
  color: string;
  accountType?: string;
}

/**
 * Service for collecting and aggregating historical financial data
 */
export class HistoricalDataService {
  /**
   * Get revenue trend data for the last 6 months
   */
  async getRevenueTrend(companyId: string): Promise<HistoricalDataPoint[]> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 6);

      // Get monthly revenue data
      const revenueData = await db("transactions")
        .select(
          db.raw("DATE_TRUNC('month', transaction_date) as month"),
          db.raw("SUM(transaction_entries.credit_amount) as revenue")
        )
        .join(
          "transaction_entries",
          "transactions.id",
          "transaction_entries.transaction_id"
        )
        .join("accounts", "transaction_entries.account_id", "accounts.id")
        .where("transactions.company_id", companyId)
        .where("transactions.status", "POSTED")
        .where("accounts.account_type", "REVENUE")
        .where(
          "transactions.transaction_date",
          ">=",
          startDate.toISOString().split("T")[0]
        )
        .where(
          "transactions.transaction_date",
          "<=",
          endDate.toISOString().split("T")[0]
        )
        .groupBy(db.raw("DATE_TRUNC('month', transaction_date)"))
        .orderBy("month");

      // Fill in missing months with zero values
      const result: HistoricalDataPoint[] = [];
      const current = new Date(startDate);

      while (current <= endDate) {
        const monthKey = current.toISOString().slice(0, 7); // YYYY-MM format
        const monthData = revenueData.find(
          (d) => new Date(d.month).toISOString().slice(0, 7) === monthKey
        );

        result.push({
          period: current.toLocaleDateString("en-US", { month: "short" }),
          value: parseFloat(monthData?.revenue || "0"),
          label: "Revenue",
        });

        current.setMonth(current.getMonth() + 1);
      }

      return result.slice(-6); // Return last 6 months
    } catch (error) {
      console.error("Error fetching revenue trend:", error);
      return [];
    }
  }

  /**
   * Get expense trend data for the last 6 months
   */
  async getExpenseTrend(companyId: string): Promise<HistoricalDataPoint[]> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 6);

      const expenseData = await db("transactions")
        .select(
          db.raw("DATE_TRUNC('month', transaction_date) as month"),
          db.raw("SUM(transaction_entries.debit_amount) as expenses")
        )
        .join(
          "transaction_entries",
          "transactions.id",
          "transaction_entries.transaction_id"
        )
        .join("accounts", "transaction_entries.account_id", "accounts.id")
        .where("transactions.company_id", companyId)
        .where("transactions.status", "POSTED")
        .where("accounts.account_type", "EXPENSE")
        .where(
          "transactions.transaction_date",
          ">=",
          startDate.toISOString().split("T")[0]
        )
        .where(
          "transactions.transaction_date",
          "<=",
          endDate.toISOString().split("T")[0]
        )
        .groupBy(db.raw("DATE_TRUNC('month', transaction_date)"))
        .orderBy("month");

      // Fill in missing months with zero values
      const result: HistoricalDataPoint[] = [];
      const current = new Date(startDate);

      while (current <= endDate) {
        const monthKey = current.toISOString().slice(0, 7);
        const monthData = expenseData.find(
          (d) => new Date(d.month).toISOString().slice(0, 7) === monthKey
        );

        result.push({
          period: current.toLocaleDateString("en-US", { month: "short" }),
          value: parseFloat(monthData?.expenses || "0"),
          label: "Expenses",
        });

        current.setMonth(current.getMonth() + 1);
      }

      return result.slice(-6);
    } catch (error) {
      console.error("Error fetching expense trend:", error);
      return [];
    }
  }

  /**
   * Get cash flow trend data for the last 6 months
   */
  async getCashFlowTrend(companyId: string): Promise<HistoricalDataPoint[]> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 6);

      const cashFlowData = await db("transactions")
        .select(
          db.raw("DATE_TRUNC('month', transaction_date) as month"),
          db.raw(
            "SUM(transaction_entries.debit_amount - transaction_entries.credit_amount) as cash_flow"
          )
        )
        .join(
          "transaction_entries",
          "transactions.id",
          "transaction_entries.transaction_id"
        )
        .join("accounts", "transaction_entries.account_id", "accounts.id")
        .where("transactions.company_id", companyId)
        .where("transactions.status", "POSTED")
        .where("accounts.account_type", "ASSET")
        .where(function () {
          this.where("accounts.name", "like", "%cash%")
            .orWhere("accounts.name", "like", "%bank%")
            .orWhere("accounts.code", "1000")
            .orWhere("accounts.code", "1001");
        })
        .where(
          "transactions.transaction_date",
          ">=",
          startDate.toISOString().split("T")[0]
        )
        .where(
          "transactions.transaction_date",
          "<=",
          endDate.toISOString().split("T")[0]
        )
        .groupBy(db.raw("DATE_TRUNC('month', transaction_date)"))
        .orderBy("month");

      // Fill in missing months and calculate cumulative cash flow
      const result: HistoricalDataPoint[] = [];
      const current = new Date(startDate);
      let cumulativeCashFlow = 0;

      while (current <= endDate) {
        const monthKey = current.toISOString().slice(0, 7);
        const monthData = cashFlowData.find(
          (d) => new Date(d.month).toISOString().slice(0, 7) === monthKey
        );

        const monthlyChange = parseFloat(monthData?.cash_flow || "0");
        cumulativeCashFlow += monthlyChange;

        result.push({
          period: current.toLocaleDateString("en-US", { month: "short" }),
          value: cumulativeCashFlow,
          label: "Cash Flow",
        });

        current.setMonth(current.getMonth() + 1);
      }

      return result.slice(-6);
    } catch (error) {
      console.error("Error fetching cash flow trend:", error);
      return [];
    }
  }

  /**
   * Get expense breakdown by category
   */
  async getExpenseBreakdown(
    companyId: string,
    period: "month" | "quarter" | "year" = "month"
  ): Promise<ExpenseBreakdown[]> {
    try {
      // Use the same date logic as the main dashboard
      const now = new Date();
      let startDate: Date;

      switch (period) {
        case "year":
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        case "quarter":
          const quarter = Math.floor(now.getMonth() / 3);
          startDate = new Date(now.getFullYear(), quarter * 3, 1);
          break;
        case "month":
        default:
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
      }

      const expenseData = await db("transactions")
        .select(
          "accounts.name as account_name",
          "accounts.code as account_code",
          db.raw("SUM(transaction_entries.debit_amount) as total_amount")
        )
        .join(
          "transaction_entries",
          "transactions.id",
          "transaction_entries.transaction_id"
        )
        .join("accounts", "transaction_entries.account_id", "accounts.id")
        .where("transactions.company_id", companyId)
        .where("transactions.status", "POSTED")
        .where("accounts.account_type", "EXPENSE")
        .where(
          "transactions.transaction_date",
          ">=",
          startDate.toISOString().split("T")[0]
        )
        .where(
          "transactions.transaction_date",
          "<=",
          now.toISOString().split("T")[0]
        )
        .groupBy("accounts.id", "accounts.name", "accounts.code")
        .orderBy("total_amount", "desc");

      console.log(`Expense breakdown query for ${companyId} (${period}):`, {
        startDate: startDate.toISOString().split("T")[0],
        endDate: now.toISOString().split("T")[0],
        expenseDataCount: expenseData.length,
        expenseData: expenseData,
      });

      // Define blue color palette for expense categories
      const colors = [
        "#3B82F6", // Blue
        "#60A5FA", // Light Blue
        "#93C5FD", // Lighter Blue
        "#DBEAFE", // Very Light Blue
        "#1E40AF", // Dark Blue
        "#1D4ED8", // Medium Dark Blue
        "#2563EB", // Medium Blue
        "#3730A3", // Darker Blue
        "#4338CA", // Purple Blue
        "#6366F1", // Indigo
      ];

      // Categorize expenses
      const categorizedExpenses: { [key: string]: number } = {};

      expenseData.forEach((expense: any) => {
        const amount = parseFloat(expense.total_amount || "0");
        const accountName = expense.account_name.toLowerCase();

        console.log(
          `Processing expense: ${expense.account_name} -> ${accountName} (${amount})`
        );

        // Categorize based on account name patterns
        if (
          accountName.includes("cost of goods") ||
          accountName.includes("cogs") ||
          accountName.includes("inventory")
        ) {
          categorizedExpenses["Cost of Goods Sold"] =
            (categorizedExpenses["Cost of Goods Sold"] || 0) + amount;
        } else if (
          accountName.includes("salary") ||
          accountName.includes("wage") ||
          accountName.includes("payroll")
        ) {
          categorizedExpenses["Payroll & Benefits"] =
            (categorizedExpenses["Payroll & Benefits"] || 0) + amount;
        } else if (
          accountName.includes("rent") ||
          accountName.includes("utilities") ||
          accountName.includes("office")
        ) {
          console.log(`✅ Categorized as Office & Facilities: ${accountName}`);
          categorizedExpenses["Office & Facilities"] =
            (categorizedExpenses["Office & Facilities"] || 0) + amount;
        } else if (
          accountName.includes("marketing") ||
          accountName.includes("advertising") ||
          accountName.includes("promotion")
        ) {
          categorizedExpenses["Marketing & Advertising"] =
            (categorizedExpenses["Marketing & Advertising"] || 0) + amount;
        } else if (
          accountName.includes("travel") ||
          accountName.includes("meal") ||
          accountName.includes("entertainment")
        ) {
          categorizedExpenses["Travel & Entertainment"] =
            (categorizedExpenses["Travel & Entertainment"] || 0) + amount;
        } else if (
          accountName.includes("professional") ||
          accountName.includes("legal") ||
          accountName.includes("consulting")
        ) {
          categorizedExpenses["Professional Services"] =
            (categorizedExpenses["Professional Services"] || 0) + amount;
        } else {
          console.log(`❓ Categorized as Other: ${accountName}`);
          categorizedExpenses["Other Operating Expenses"] =
            (categorizedExpenses["Other Operating Expenses"] || 0) + amount;
        }
      });

      // Convert to breakdown format
      const breakdown: ExpenseBreakdown[] = Object.entries(categorizedExpenses)
        .map(([label, value], index) => ({
          label,
          value,
          color: colors[index % colors.length],
        }))
        .filter((item) => item.value > 0)
        .sort((a, b) => b.value - a.value);

      console.log(`Final expense breakdown result:`, {
        categorizedExpenses,
        breakdown,
        breakdownCount: breakdown.length,
      });

      return breakdown;
    } catch (error) {
      console.error("Error fetching expense breakdown:", error);
      return [];
    }
  }

  /**
   * Get combined revenue and expense trend for comparison
   */
  async getRevenueExpenseTrend(companyId: string): Promise<{
    revenue: HistoricalDataPoint[];
    expenses: HistoricalDataPoint[];
  }> {
    const [revenue, expenses] = await Promise.all([
      this.getRevenueTrend(companyId),
      this.getExpenseTrend(companyId),
    ]);

    return { revenue, expenses };
  }
}

export const historicalDataService = new HistoricalDataService();
