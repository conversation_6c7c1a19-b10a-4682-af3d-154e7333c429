import { db } from '../config/database.js';
import { v4 as uuidv4 } from 'uuid';

export interface TransactionTemplate {
  id: string;
  companyId?: string;
  name: string;
  description?: string;
  category: TemplateCategory;
  isActive: boolean;
  isSystemTemplate: boolean;
  usageCount: number;
  defaultValues: Record<string, any>;
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
  entries: TransactionTemplateEntry[];
}

export interface TransactionTemplateEntry {
  id: string;
  templateId: string;
  accountId: string;
  accountCode?: string;
  accountName?: string;
  description?: string;
  debitAmount: number;
  creditAmount: number;
  isVariableAmount: boolean;
  amountFormula?: string;
  lineNumber: number;
  metadata: Record<string, any>;
}

export type TemplateCategory = 
  | 'GENERAL'
  | 'SALES'
  | 'PURCHASES'
  | 'PAYROLL'
  | 'BANKING'
  | 'ADJUSTMENTS'
  | 'DEPRECIATION'
  | 'TAX'
  | 'INVENTORY'
  | 'FIXED_ASSETS';

export interface CreateTemplateData {
  companyId: string;
  name: string;
  description?: string;
  category: TemplateCategory;
  defaultValues?: Record<string, any>;
  entries: Array<{
    accountId: string;
    description?: string;
    debitAmount: number;
    creditAmount: number;
    isVariableAmount?: boolean;
    amountFormula?: string;
    lineNumber: number;
    metadata?: Record<string, any>;
  }>;
}

export interface ApplyTemplateData {
  templateId: string;
  transactionDate: string;
  description?: string;
  reference?: string;
  variables?: Record<string, any>;
  entryOverrides?: Array<{
    lineNumber: number;
    debitAmount?: number;
    creditAmount?: number;
    description?: string;
  }>;
}

class TransactionTemplateService {
  /**
   * Get all templates for a company (including system templates)
   */
  public async getTemplates(
    companyId: string,
    options: {
      category?: TemplateCategory;
      isActive?: boolean;
      includeSystemTemplates?: boolean;
      search?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{
    templates: TransactionTemplate[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const {
      category,
      isActive = true,
      includeSystemTemplates = true,
      search,
      page = 1,
      limit = 50
    } = options;

    let query = db('transaction_templates as tt')
      .select(
        'tt.*',
        db.raw(`
          json_agg(
            json_build_object(
              'id', tte.id,
              'accountId', tte.account_id,
              'accountCode', a.code,
              'accountName', a.name,
              'description', tte.description,
              'debitAmount', tte.debit_amount,
              'creditAmount', tte.credit_amount,
              'isVariableAmount', tte.is_variable_amount,
              'amountFormula', tte.amount_formula,
              'lineNumber', tte.line_number,
              'metadata', tte.metadata
            ) ORDER BY tte.line_number
          ) as entries
        `)
      )
      .leftJoin('transaction_template_entries as tte', 'tt.id', 'tte.template_id')
      .leftJoin('accounts as a', 'tte.account_id', 'a.id')
      .where(function() {
        this.where('tt.company_id', companyId);
        if (includeSystemTemplates) {
          this.orWhere('tt.is_system_template', true);
        }
      })
      .groupBy('tt.id');

    // Apply filters
    if (category) {
      query = query.where('tt.category', category);
    }

    if (isActive !== undefined) {
      query = query.where('tt.is_active', isActive);
    }

    if (search) {
      query = query.where(function() {
        this.whereILike('tt.name', `%${search}%`)
          .orWhereILike('tt.description', `%${search}%`);
      });
    }

    // Get total count
    const countQuery = query.clone().clearSelect().clearGroup().count('* as total');
    const [{ total }] = await countQuery;

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.offset(offset).limit(limit);

    // Order by usage count and name
    query = query.orderBy([
      { column: 'tt.usage_count', order: 'desc' },
      { column: 'tt.name', order: 'asc' }
    ]);

    const templates = await query;

    return {
      templates: templates.map(this.mapTemplateFromDb),
      total: parseInt(total as string),
      page,
      totalPages: Math.ceil(parseInt(total as string) / limit)
    };
  }

  /**
   * Get a single template by ID
   */
  public async getTemplate(templateId: string, companyId?: string): Promise<TransactionTemplate | null> {
    const template = await db('transaction_templates as tt')
      .select(
        'tt.*',
        db.raw(`
          json_agg(
            json_build_object(
              'id', tte.id,
              'accountId', tte.account_id,
              'accountCode', a.code,
              'accountName', a.name,
              'description', tte.description,
              'debitAmount', tte.debit_amount,
              'creditAmount', tte.credit_amount,
              'isVariableAmount', tte.is_variable_amount,
              'amountFormula', tte.amount_formula,
              'lineNumber', tte.line_number,
              'metadata', tte.metadata
            ) ORDER BY tte.line_number
          ) as entries
        `)
      )
      .leftJoin('transaction_template_entries as tte', 'tt.id', 'tte.template_id')
      .leftJoin('accounts as a', 'tte.account_id', 'a.id')
      .where('tt.id', templateId)
      .where(function() {
        if (companyId) {
          this.where('tt.company_id', companyId).orWhere('tt.is_system_template', true);
        }
      })
      .groupBy('tt.id')
      .first();

    return template ? this.mapTemplateFromDb(template) : null;
  }

  /**
   * Create a new template
   */
  public async createTemplate(data: CreateTemplateData, userId: string): Promise<TransactionTemplate> {
    const templateId = uuidv4();

    await db.transaction(async (trx) => {
      // Insert template
      await trx('transaction_templates').insert({
        id: templateId,
        company_id: data.companyId,
        name: data.name,
        description: data.description,
        category: data.category,
        default_values: JSON.stringify(data.defaultValues || {}),
        created_by: userId,
        created_at: new Date(),
        updated_at: new Date()
      });

      // Insert template entries
      const entries = data.entries.map(entry => ({
        id: uuidv4(),
        template_id: templateId,
        account_id: entry.accountId,
        description: entry.description,
        debit_amount: entry.debitAmount,
        credit_amount: entry.creditAmount,
        is_variable_amount: entry.isVariableAmount || false,
        amount_formula: entry.amountFormula,
        line_number: entry.lineNumber,
        metadata: JSON.stringify(entry.metadata || {}),
        created_at: new Date(),
        updated_at: new Date()
      }));

      await trx('transaction_template_entries').insert(entries);
    });

    const template = await this.getTemplate(templateId, data.companyId);
    if (!template) {
      throw new Error('Failed to create template');
    }

    return template;
  }

  /**
   * Update a template
   */
  public async updateTemplate(
    templateId: string,
    data: Partial<CreateTemplateData>,
    userId: string,
    companyId: string
  ): Promise<TransactionTemplate> {
    await db.transaction(async (trx) => {
      // Update template
      const updateData: any = {
        updated_at: new Date()
      };

      if (data.name) updateData.name = data.name;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.category) updateData.category = data.category;
      if (data.defaultValues !== undefined) {
        updateData.default_values = JSON.stringify(data.defaultValues);
      }

      await trx('transaction_templates')
        .where('id', templateId)
        .where('company_id', companyId)
        .update(updateData);

      // Update entries if provided
      if (data.entries) {
        // Delete existing entries
        await trx('transaction_template_entries')
          .where('template_id', templateId)
          .del();

        // Insert new entries
        const entries = data.entries.map(entry => ({
          id: uuidv4(),
          template_id: templateId,
          account_id: entry.accountId,
          description: entry.description,
          debit_amount: entry.debitAmount,
          credit_amount: entry.creditAmount,
          is_variable_amount: entry.isVariableAmount || false,
          amount_formula: entry.amountFormula,
          line_number: entry.lineNumber,
          metadata: JSON.stringify(entry.metadata || {}),
          created_at: new Date(),
          updated_at: new Date()
        }));

        await trx('transaction_template_entries').insert(entries);
      }
    });

    const template = await this.getTemplate(templateId, companyId);
    if (!template) {
      throw new Error('Template not found after update');
    }

    return template;
  }

  /**
   * Delete a template
   */
  public async deleteTemplate(templateId: string, companyId: string): Promise<void> {
    const result = await db('transaction_templates')
      .where('id', templateId)
      .where('company_id', companyId)
      .where('is_system_template', false)
      .del();

    if (result === 0) {
      throw new Error('Template not found or cannot be deleted');
    }
  }

  /**
   * Apply template to create transaction data
   */
  public async applyTemplate(
    data: ApplyTemplateData,
    companyId: string,
    userId: string
  ): Promise<{
    transactionDate: string;
    description: string;
    reference?: string;
    entries: Array<{
      accountId: string;
      description?: string;
      debitAmount: number;
      creditAmount: number;
    }>;
  }> {
    const template = await this.getTemplate(data.templateId, companyId);
    if (!template) {
      throw new Error('Template not found');
    }

    // Process variables and default values
    const variables = { ...template.defaultValues, ...data.variables };

    // Replace variables in description and reference
    let description = data.description || template.defaultValues.description || template.name;
    let reference = data.reference || template.defaultValues.reference;

    description = this.replaceVariables(description, variables);
    if (reference) {
      reference = this.replaceVariables(reference, variables);
    }

    // Process entries
    const entries = template.entries.map(entry => {
      const override = data.entryOverrides?.find(o => o.lineNumber === entry.lineNumber);

      let debitAmount = override?.debitAmount ?? entry.debitAmount;
      let creditAmount = override?.creditAmount ?? entry.creditAmount;
      let entryDescription = override?.description ?? entry.description;

      // Apply formula if variable amount
      if (entry.isVariableAmount && entry.amountFormula) {
        const calculatedAmount = this.evaluateFormula(entry.amountFormula, variables);
        if (entry.debitAmount > 0) {
          debitAmount = calculatedAmount;
        } else {
          creditAmount = calculatedAmount;
        }
      }

      // Replace variables in entry description
      if (entryDescription) {
        entryDescription = this.replaceVariables(entryDescription, variables);
      }

      return {
        accountId: entry.accountId,
        description: entryDescription,
        debitAmount,
        creditAmount
      };
    });

    // Track template usage
    await this.trackTemplateUsage(data.templateId, userId, variables);

    return {
      transactionDate: data.transactionDate,
      description,
      reference,
      entries
    };
  }

  /**
   * Track template usage
   */
  private async trackTemplateUsage(
    templateId: string,
    userId: string,
    appliedValues: Record<string, any>
  ): Promise<void> {
    await db.transaction(async (trx) => {
      // Increment usage count
      await trx('transaction_templates')
        .where('id', templateId)
        .increment('usage_count', 1);

      // Record usage
      await trx('transaction_template_usage').insert({
        id: uuidv4(),
        template_id: templateId,
        used_by: userId,
        applied_values: JSON.stringify(appliedValues),
        created_at: new Date(),
        updated_at: new Date()
      });
    });
  }

  /**
   * Replace variables in text
   */
  private replaceVariables(text: string, variables: Record<string, any>): string {
    return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables[key] !== undefined ? String(variables[key]) : match;
    });
  }

  /**
   * Evaluate simple formulas
   */
  private evaluateFormula(formula: string, variables: Record<string, any>): number {
    try {
      // Replace variables in formula
      let processedFormula = this.replaceVariables(formula, variables);

      // Simple math evaluation (extend as needed)
      // For security, only allow basic math operations
      const allowedChars = /^[0-9+\-*/.() ]+$/;
      if (!allowedChars.test(processedFormula)) {
        throw new Error('Invalid formula');
      }

      return eval(processedFormula);
    } catch (error) {
      console.error('Formula evaluation error:', error);
      return 0;
    }
  }

  /**
   * Get template categories
   */
  public async getCategories(): Promise<Array<{ value: TemplateCategory; label: string }>> {
    return Promise.resolve([
      { value: 'GENERAL', label: 'General' },
      { value: 'SALES', label: 'Sales' },
      { value: 'PURCHASES', label: 'Purchases' },
      { value: 'PAYROLL', label: 'Payroll' },
      { value: 'BANKING', label: 'Banking' },
      { value: 'ADJUSTMENTS', label: 'Adjustments' },
      { value: 'DEPRECIATION', label: 'Depreciation' },
      { value: 'TAX', label: 'Tax' },
      { value: 'INVENTORY', label: 'Inventory' },
      { value: 'FIXED_ASSETS', label: 'Fixed Assets' }
    ]);
  }

  /**
   * Map database row to TransactionTemplate
   */
  private mapTemplateFromDb(row: any): TransactionTemplate {
    return {
      id: row.id,
      companyId: row.company_id,
      name: row.name,
      description: row.description,
      category: row.category,
      isActive: row.is_active,
      isSystemTemplate: row.is_system_template,
      usageCount: row.usage_count,
      defaultValues: row.default_values || {},
      createdBy: row.created_by,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      entries: row.entries || []
    };
  }
}

export const transactionTemplateService = new TransactionTemplateService();
