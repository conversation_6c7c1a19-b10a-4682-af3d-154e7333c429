import { db } from '../config/database.js';
import { auditService } from './auditService.js';

// Tanzania-specific compliance interfaces
export interface TRAComplianceCheck {
  transactionId: string;
  amount: number;
  currency: string;
  transactionType: string;
  vatApplicable: boolean;
  withholdingTaxApplicable: boolean;
  efdRequired: boolean;
  complianceStatus: 'COMPLIANT' | 'NON_COMPLIANT' | 'REQUIRES_REVIEW';
  flags: string[];
  riskScore: number;
}

export interface BOTComplianceCheck {
  transactionId: string;
  amount: number;
  currency: string;
  isForexTransaction: boolean;
  exceedsReportingThreshold: boolean;
  requiresCentralBankReporting: boolean;
  complianceStatus: 'COMPLIANT' | 'NON_COMPLIANT' | 'REQUIRES_REVIEW';
  flags: string[];
  riskScore: number;
}

export interface AMLScreeningResult {
  entityId: string;
  entityType: 'CUSTOMER' | 'SUPPLIER' | 'EMPLOYEE';
  entityName: string;
  isPEP: boolean; // Politically Exposed Person
  isSanctioned: boolean;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  screeningDate: Date;
  flags: string[];
  requiresEnhancedDueDiligence: boolean;
}

export interface ComplianceReport {
  reportId: string;
  reportType: 'TRA_MONTHLY' | 'BOT_QUARTERLY' | 'AML_SUSPICIOUS' | 'CUSTOM';
  period: {
    startDate: Date;
    endDate: Date;
  };
  companyId: string;
  generatedBy: string;
  generatedAt: Date;
  data: any;
  status: 'DRAFT' | 'SUBMITTED' | 'APPROVED' | 'REJECTED';
}

export class TanzaniaComplianceService {
  // TRA (Tanzania Revenue Authority) thresholds and rules
  private static readonly TRA_THRESHOLDS = {
    VAT_THRESHOLD: *********, // 100M TZS annual turnover
    WITHHOLDING_TAX_THRESHOLD: 30000, // 30K TZS per transaction
    EFD_REQUIRED_AMOUNT: 5000, // 5K TZS per transaction
    LARGE_TRANSACTION_THRESHOLD: ******** // 10M TZS
  };

  // BOT (Bank of Tanzania) thresholds and rules
  private static readonly BOT_THRESHOLDS = {
    FOREX_REPORTING_THRESHOLD_USD: 10000, // $10,000 USD
    LARGE_CASH_TRANSACTION_TZS: 5000000, // 5M TZS
    CROSS_BORDER_REPORTING_THRESHOLD: 10000 // $10,000 USD equivalent
  };

  // AML thresholds and rules
  private static readonly AML_THRESHOLDS = {
    SUSPICIOUS_CASH_AMOUNT_TZS: ********, // 10M TZS
    SUSPICIOUS_FREQUENCY_DAILY: 5, // 5 transactions per day
    SUSPICIOUS_FREQUENCY_MONTHLY: 50, // 50 transactions per month
    HIGH_RISK_COUNTRIES: ['AF', 'IR', 'KP', 'SY'] // ISO country codes
  };

  /**
   * Perform TRA compliance check for a transaction
   */
  static async performTRAComplianceCheck(transaction: {
    id: string;
    amount: number;
    currency: string;
    type: string;
    companyId: string;
    customerId?: string;
  }): Promise<TRAComplianceCheck> {
    try {
      const flags: string[] = [];
      let riskScore = 0;
      let complianceStatus: 'COMPLIANT' | 'NON_COMPLIANT' | 'REQUIRES_REVIEW' = 'COMPLIANT';

      // Convert to TZS if needed
      const amountInTZS = transaction.currency === 'TZS' ? 
        transaction.amount : 
        transaction.amount * 2300; // Approximate USD to TZS rate

      // Check VAT applicability
      const vatApplicable = await this.checkVATApplicability(transaction.companyId, amountInTZS);
      if (vatApplicable) {
        flags.push('VAT_APPLICABLE');
        riskScore += 0.2;
      }

      // Check withholding tax
      const withholdingTaxApplicable = amountInTZS >= this.TRA_THRESHOLDS.WITHHOLDING_TAX_THRESHOLD;
      if (withholdingTaxApplicable) {
        flags.push('WITHHOLDING_TAX_REQUIRED');
        riskScore += 0.3;
      }

      // Check EFD requirement
      const efdRequired = amountInTZS >= this.TRA_THRESHOLDS.EFD_REQUIRED_AMOUNT;
      if (efdRequired) {
        flags.push('EFD_REQUIRED');
        riskScore += 0.1;
      }

      // Check large transaction threshold
      if (amountInTZS >= this.TRA_THRESHOLDS.LARGE_TRANSACTION_THRESHOLD) {
        flags.push('LARGE_TRANSACTION');
        riskScore += 0.4;
        complianceStatus = 'REQUIRES_REVIEW';
      }

      // Check transaction frequency for the company
      const dailyTransactionCount = await this.getDailyTransactionCount(transaction.companyId);
      if (dailyTransactionCount > 20) {
        flags.push('HIGH_FREQUENCY_TRANSACTIONS');
        riskScore += 0.3;
      }

      // Determine final compliance status
      if (riskScore >= 0.7) {
        complianceStatus = 'REQUIRES_REVIEW';
      } else if (riskScore >= 0.5) {
        complianceStatus = 'NON_COMPLIANT';
      }

      const result: TRAComplianceCheck = {
        transactionId: transaction.id,
        amount: transaction.amount,
        currency: transaction.currency,
        transactionType: transaction.type,
        vatApplicable,
        withholdingTaxApplicable,
        efdRequired,
        complianceStatus,
        flags,
        riskScore: Math.round(riskScore * 100) / 100
      };

      // Log compliance check
      await auditService.logFinancialTransaction({
        transactionId: transaction.id,
        actionType: 'TRA_COMPLIANCE_CHECK',
        complianceCheckResult: result
      });

      return result;

    } catch (error) {
      console.error('❌ TRA compliance check failed:', error);
      throw new Error('TRA compliance check failed');
    }
  }

  /**
   * Perform BOT compliance check for a transaction
   */
  static async performBOTComplianceCheck(transaction: {
    id: string;
    amount: number;
    currency: string;
    type: string;
    isInternational: boolean;
    companyId: string;
  }): Promise<BOTComplianceCheck> {
    try {
      const flags: string[] = [];
      let riskScore = 0;
      let complianceStatus: 'COMPLIANT' | 'NON_COMPLIANT' | 'REQUIRES_REVIEW' = 'COMPLIANT';

      // Check if it's a forex transaction
      const isForexTransaction = transaction.currency !== 'TZS';
      if (isForexTransaction) {
        flags.push('FOREX_TRANSACTION');
        riskScore += 0.2;
      }

      // Convert to USD for threshold checks
      const amountInUSD = transaction.currency === 'USD' ? 
        transaction.amount : 
        transaction.currency === 'TZS' ? 
          transaction.amount / 2300 : // TZS to USD
          transaction.amount; // Assume other currencies are close to USD

      // Check reporting thresholds
      const exceedsReportingThreshold = amountInUSD >= this.BOT_THRESHOLDS.FOREX_REPORTING_THRESHOLD_USD;
      if (exceedsReportingThreshold) {
        flags.push('EXCEEDS_REPORTING_THRESHOLD');
        riskScore += 0.4;
      }

      // Check if central bank reporting is required
      const requiresCentralBankReporting = transaction.isInternational && exceedsReportingThreshold;
      if (requiresCentralBankReporting) {
        flags.push('CENTRAL_BANK_REPORTING_REQUIRED');
        riskScore += 0.5;
        complianceStatus = 'REQUIRES_REVIEW';
      }

      // Check for large cash transactions in TZS
      if (transaction.currency === 'TZS' && 
          transaction.amount >= this.BOT_THRESHOLDS.LARGE_CASH_TRANSACTION_TZS) {
        flags.push('LARGE_CASH_TRANSACTION');
        riskScore += 0.6;
        complianceStatus = 'REQUIRES_REVIEW';
      }

      // Check cross-border transaction patterns
      if (transaction.isInternational) {
        const monthlyInternationalCount = await this.getMonthlyInternationalTransactionCount(transaction.companyId);
        if (monthlyInternationalCount > 10) {
          flags.push('HIGH_INTERNATIONAL_ACTIVITY');
          riskScore += 0.3;
        }
      }

      // Determine final compliance status
      if (riskScore >= 0.8) {
        complianceStatus = 'NON_COMPLIANT';
      } else if (riskScore >= 0.6) {
        complianceStatus = 'REQUIRES_REVIEW';
      }

      const result: BOTComplianceCheck = {
        transactionId: transaction.id,
        amount: transaction.amount,
        currency: transaction.currency,
        isForexTransaction,
        exceedsReportingThreshold,
        requiresCentralBankReporting,
        complianceStatus,
        flags,
        riskScore: Math.round(riskScore * 100) / 100
      };

      // Log compliance check
      await auditService.logFinancialTransaction({
        transactionId: transaction.id,
        actionType: 'BOT_COMPLIANCE_CHECK',
        complianceCheckResult: result
      });

      return result;

    } catch (error) {
      console.error('❌ BOT compliance check failed:', error);
      throw new Error('BOT compliance check failed');
    }
  }

  /**
   * Perform AML screening for an entity
   */
  static async performAMLScreening(entity: {
    id: string;
    name: string;
    type: 'CUSTOMER' | 'SUPPLIER' | 'EMPLOYEE';
    country?: string;
    identificationNumber?: string;
  }): Promise<AMLScreeningResult> {
    try {
      const flags: string[] = [];
      let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';

      // Check against PEP (Politically Exposed Person) list
      const isPEP = await this.checkPEPStatus(entity.name, entity.identificationNumber);
      if (isPEP) {
        flags.push('POLITICALLY_EXPOSED_PERSON');
        riskLevel = 'HIGH';
      }

      // Check against sanctions list
      const isSanctioned = await this.checkSanctionsList(entity.name, entity.country);
      if (isSanctioned) {
        flags.push('SANCTIONED_ENTITY');
        riskLevel = 'CRITICAL';
      }

      // Check high-risk countries
      if (entity.country && this.AML_THRESHOLDS.HIGH_RISK_COUNTRIES.includes(entity.country)) {
        flags.push('HIGH_RISK_COUNTRY');
        if (riskLevel === 'LOW') riskLevel = 'MEDIUM';
      }

      // Check transaction patterns for existing entities
      const suspiciousPatterns = await this.checkSuspiciousTransactionPatterns(entity.id);
      if (suspiciousPatterns.length > 0) {
        flags.push(...suspiciousPatterns);
        if (riskLevel === 'LOW') riskLevel = 'MEDIUM';
      }

      // Determine if enhanced due diligence is required
      const requiresEnhancedDueDiligence = isPEP || isSanctioned || riskLevel === 'HIGH' || riskLevel === 'CRITICAL';

      const result: AMLScreeningResult = {
        entityId: entity.id,
        entityType: entity.type,
        entityName: entity.name,
        isPEP,
        isSanctioned,
        riskLevel,
        screeningDate: new Date(),
        flags,
        requiresEnhancedDueDiligence
      };

      // Log AML screening
      await auditService.logDataChange({
        tableName: 'aml_screening',
        recordId: entity.id,
        actionType: 'INSERT',
        newValues: result,
        reason: 'AML screening performed',
        riskLevel: riskLevel === 'CRITICAL' ? 'CRITICAL' : riskLevel === 'HIGH' ? 'HIGH' : 'MEDIUM'
      });

      return result;

    } catch (error) {
      console.error('❌ AML screening failed:', error);
      throw new Error('AML screening failed');
    }
  }

  // Helper methods
  private static async checkVATApplicability(companyId: string, amount: number): Promise<boolean> {
    // Simplified VAT check - in real implementation, check company's VAT registration status
    return amount >= 100000; // 100K TZS threshold for VAT
  }

  private static async getDailyTransactionCount(companyId: string): Promise<number> {
    const result = await db('transactions')
      .where('company_id', companyId)
      .where('created_at', '>=', db.raw("CURRENT_DATE"))
      .count('* as count')
      .first();
    
    return parseInt(result?.count || '0');
  }

  private static async getMonthlyInternationalTransactionCount(companyId: string): Promise<number> {
    // Simplified check - in real implementation, check transaction metadata for international flag
    const result = await db('transactions')
      .where('company_id', companyId)
      .where('created_at', '>=', db.raw("DATE_TRUNC('month', CURRENT_DATE)"))
      .count('* as count')
      .first();
    
    return Math.floor(parseInt(result?.count || '0') * 0.1); // Assume 10% are international
  }

  private static async checkPEPStatus(name: string, identificationNumber?: string): Promise<boolean> {
    // Simplified PEP check - in real implementation, check against official PEP databases
    const pepKeywords = ['minister', 'mp', 'senator', 'governor', 'ambassador', 'judge'];
    return pepKeywords.some(keyword => name.toLowerCase().includes(keyword));
  }

  private static async checkSanctionsList(name: string, country?: string): Promise<boolean> {
    // Simplified sanctions check - in real implementation, check against OFAC, UN, EU sanctions lists
    const sanctionedCountries = ['IR', 'KP', 'SY', 'AF'];
    return country ? sanctionedCountries.includes(country) : false;
  }

  private static async checkSuspiciousTransactionPatterns(entityId: string): Promise<string[]> {
    const flags: string[] = [];
    
    // Check for high-frequency transactions
    const dailyCount = await db('transactions')
      .where('customer_id', entityId)
      .where('created_at', '>=', db.raw("CURRENT_DATE"))
      .count('* as count')
      .first();
    
    if (parseInt(dailyCount?.count || '0') >= this.AML_THRESHOLDS.SUSPICIOUS_FREQUENCY_DAILY) {
      flags.push('HIGH_FREQUENCY_TRANSACTIONS');
    }

    // Check for round number transactions (potential structuring)
    const roundNumberCount = await db('transactions')
      .where('customer_id', entityId)
      .where('created_at', '>=', db.raw("CURRENT_DATE - INTERVAL '30 days'"))
      .whereRaw('amount % 1000 = 0') // Round thousands
      .count('* as count')
      .first();
    
    if (parseInt(roundNumberCount?.count || '0') >= 5) {
      flags.push('POTENTIAL_STRUCTURING');
    }

    return flags;
  }

  /**
   * Generate TRA monthly compliance report
   */
  static async generateTRAMonthlyReport(companyId: string, month: number, year: number): Promise<ComplianceReport> {
    try {
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);

      // Get all transactions for the period
      const transactions = await db('transactions')
        .where('company_id', companyId)
        .whereBetween('created_at', [startDate, endDate]);

      // Calculate VAT summary
      const vatTransactions = transactions.filter(t => t.amount >= 100000);
      const totalVATAmount = vatTransactions.reduce((sum, t) => sum + (t.amount * 0.18), 0);

      // Calculate withholding tax summary
      const withholdingTransactions = transactions.filter(t => t.amount >= this.TRA_THRESHOLDS.WITHHOLDING_TAX_THRESHOLD);
      const totalWithholdingTax = withholdingTransactions.reduce((sum, t) => sum + (t.amount * 0.05), 0);

      // EFD transactions
      const efdTransactions = transactions.filter(t => t.amount >= this.TRA_THRESHOLDS.EFD_REQUIRED_AMOUNT);

      const reportData = {
        period: { startDate, endDate },
        summary: {
          totalTransactions: transactions.length,
          totalAmount: transactions.reduce((sum, t) => sum + t.amount, 0),
          vatTransactions: vatTransactions.length,
          totalVATAmount,
          withholdingTransactions: withholdingTransactions.length,
          totalWithholdingTax,
          efdTransactions: efdTransactions.length
        },
        transactions: transactions.map(t => ({
          id: t.id,
          amount: t.amount,
          currency: t.currency,
          date: t.created_at,
          vatApplicable: t.amount >= 100000,
          withholdingTaxApplicable: t.amount >= this.TRA_THRESHOLDS.WITHHOLDING_TAX_THRESHOLD,
          efdRequired: t.amount >= this.TRA_THRESHOLDS.EFD_REQUIRED_AMOUNT
        }))
      };

      const report: ComplianceReport = {
        reportId: `TRA_${companyId}_${year}_${month.toString().padStart(2, '0')}`,
        reportType: 'TRA_MONTHLY',
        period: { startDate, endDate },
        companyId,
        generatedBy: 'SYSTEM',
        generatedAt: new Date(),
        data: reportData,
        status: 'DRAFT'
      };

      return report;

    } catch (error) {
      console.error('❌ TRA monthly report generation failed:', error);
      throw new Error('TRA monthly report generation failed');
    }
  }

  /**
   * Generate BOT quarterly compliance report
   */
  static async generateBOTQuarterlyReport(companyId: string, quarter: number, year: number): Promise<ComplianceReport> {
    try {
      const startMonth = (quarter - 1) * 3 + 1;
      const startDate = new Date(year, startMonth - 1, 1);
      const endDate = new Date(year, startMonth + 2, 0);

      // Get forex and large transactions
      const forexTransactions = await db('transactions')
        .where('company_id', companyId)
        .where('currency', '!=', 'TZS')
        .whereBetween('created_at', [startDate, endDate]);

      const largeTZSTransactions = await db('transactions')
        .where('company_id', companyId)
        .where('currency', 'TZS')
        .where('amount', '>=', this.BOT_THRESHOLDS.LARGE_CASH_TRANSACTION_TZS)
        .whereBetween('created_at', [startDate, endDate]);

      const reportData = {
        period: { startDate, endDate },
        summary: {
          forexTransactions: forexTransactions.length,
          totalForexAmount: forexTransactions.reduce((sum, t) => sum + t.amount, 0),
          largeTZSTransactions: largeTZSTransactions.length,
          totalLargeTZSAmount: largeTZSTransactions.reduce((sum, t) => sum + t.amount, 0),
          reportableTransactions: forexTransactions.filter(t =>
            (t.currency === 'USD' ? t.amount : t.amount / 2300) >= this.BOT_THRESHOLDS.FOREX_REPORTING_THRESHOLD_USD
          ).length
        },
        transactions: [...forexTransactions, ...largeTZSTransactions].map(t => ({
          id: t.id,
          amount: t.amount,
          currency: t.currency,
          date: t.created_at,
          type: t.currency === 'TZS' ? 'LARGE_TZS' : 'FOREX',
          requiresReporting: t.currency === 'TZS' ?
            t.amount >= this.BOT_THRESHOLDS.LARGE_CASH_TRANSACTION_TZS :
            (t.currency === 'USD' ? t.amount : t.amount / 2300) >= this.BOT_THRESHOLDS.FOREX_REPORTING_THRESHOLD_USD
        }))
      };

      const report: ComplianceReport = {
        reportId: `BOT_${companyId}_${year}_Q${quarter}`,
        reportType: 'BOT_QUARTERLY',
        period: { startDate, endDate },
        companyId,
        generatedBy: 'SYSTEM',
        generatedAt: new Date(),
        data: reportData,
        status: 'DRAFT'
      };

      return report;

    } catch (error) {
      console.error('❌ BOT quarterly report generation failed:', error);
      throw new Error('BOT quarterly report generation failed');
    }
  }

  /**
   * Generate suspicious activity report for AML compliance
   */
  static async generateSuspiciousActivityReport(companyId: string, startDate: Date, endDate: Date): Promise<ComplianceReport> {
    try {
      // Find suspicious transactions
      const suspiciousTransactions = await db('transactions')
        .where('company_id', companyId)
        .whereBetween('created_at', [startDate, endDate])
        .where(function() {
          this.where('amount', '>=', TanzaniaComplianceService.AML_THRESHOLDS.SUSPICIOUS_CASH_AMOUNT_TZS)
            .orWhereRaw('amount % 1000 = 0 AND amount >= 50000'); // Round numbers above 50K
        });

      // Find customers with high transaction frequency
      const highFrequencyCustomers = await db('transactions')
        .select('customer_id')
        .count('* as transaction_count')
        .where('company_id', companyId)
        .whereBetween('created_at', [startDate, endDate])
        .groupBy('customer_id')
        .having('transaction_count', '>=', this.AML_THRESHOLDS.SUSPICIOUS_FREQUENCY_MONTHLY);

      const reportData = {
        period: { startDate, endDate },
        summary: {
          suspiciousTransactions: suspiciousTransactions.length,
          totalSuspiciousAmount: suspiciousTransactions.reduce((sum, t) => sum + t.amount, 0),
          highFrequencyCustomers: highFrequencyCustomers.length,
          flaggedActivities: suspiciousTransactions.length + highFrequencyCustomers.length
        },
        suspiciousTransactions: suspiciousTransactions.map(t => ({
          id: t.id,
          amount: t.amount,
          currency: t.currency,
          date: t.created_at,
          customerId: t.customer_id,
          flags: [
            t.amount >= this.AML_THRESHOLDS.SUSPICIOUS_CASH_AMOUNT_TZS ? 'LARGE_CASH_TRANSACTION' : null,
            t.amount % 1000 === 0 ? 'ROUND_NUMBER_TRANSACTION' : null
          ].filter(Boolean)
        })),
        highFrequencyCustomers: highFrequencyCustomers.map(c => ({
          customerId: c.customer_id,
          transactionCount: c.transaction_count,
          flags: ['HIGH_FREQUENCY_TRANSACTIONS']
        }))
      };

      const report: ComplianceReport = {
        reportId: `AML_${companyId}_${startDate.toISOString().split('T')[0]}_${endDate.toISOString().split('T')[0]}`,
        reportType: 'AML_SUSPICIOUS',
        period: { startDate, endDate },
        companyId,
        generatedBy: 'SYSTEM',
        generatedAt: new Date(),
        data: reportData,
        status: 'DRAFT'
      };

      return report;

    } catch (error) {
      console.error('❌ Suspicious activity report generation failed:', error);
      throw new Error('Suspicious activity report generation failed');
    }
  }

  /**
   * Perform comprehensive compliance check for a transaction
   */
  static async performComprehensiveComplianceCheck(transaction: {
    id: string;
    amount: number;
    currency: string;
    type: string;
    companyId: string;
    customerId?: string;
    isInternational?: boolean;
  }) {
    try {
      // Perform all compliance checks
      const traCheck = await this.performTRAComplianceCheck(transaction);
      const botCheck = await this.performBOTComplianceCheck({
        ...transaction,
        isInternational: transaction.isInternational || false
      });

      // Perform AML screening if customer is involved
      let amlScreening = null;
      if (transaction.customerId) {
        // Get customer details (simplified)
        const customer = await db('customers').where('id', transaction.customerId).first();
        if (customer) {
          amlScreening = await this.performAMLScreening({
            id: customer.id,
            name: customer.name,
            type: 'CUSTOMER',
            country: customer.country,
            identificationNumber: customer.identification_number
          });
        }
      }

      // Calculate overall risk score
      const overallRiskScore = Math.max(
        traCheck.riskScore,
        botCheck.riskScore,
        amlScreening ? (amlScreening.riskLevel === 'CRITICAL' ? 1.0 :
                       amlScreening.riskLevel === 'HIGH' ? 0.8 :
                       amlScreening.riskLevel === 'MEDIUM' ? 0.5 : 0.2) : 0
      );

      // Determine overall compliance status
      let overallStatus: 'COMPLIANT' | 'NON_COMPLIANT' | 'REQUIRES_REVIEW' = 'COMPLIANT';
      if (overallRiskScore >= 0.8 ||
          traCheck.complianceStatus === 'NON_COMPLIANT' ||
          botCheck.complianceStatus === 'NON_COMPLIANT' ||
          (amlScreening && amlScreening.riskLevel === 'CRITICAL')) {
        overallStatus = 'NON_COMPLIANT';
      } else if (overallRiskScore >= 0.5 ||
                 traCheck.complianceStatus === 'REQUIRES_REVIEW' ||
                 botCheck.complianceStatus === 'REQUIRES_REVIEW' ||
                 (amlScreening && (amlScreening.riskLevel === 'HIGH' || amlScreening.requiresEnhancedDueDiligence))) {
        overallStatus = 'REQUIRES_REVIEW';
      }

      const comprehensiveResult = {
        transactionId: transaction.id,
        overallStatus,
        overallRiskScore: Math.round(overallRiskScore * 100) / 100,
        traCompliance: traCheck,
        botCompliance: botCheck,
        amlScreening,
        timestamp: new Date(),
        requiresManualReview: overallStatus !== 'COMPLIANT',
        recommendedActions: this.generateRecommendedActions(traCheck, botCheck, amlScreening)
      };

      // Log comprehensive compliance check
      await auditService.logFinancialTransaction({
        transactionId: transaction.id,
        actionType: 'COMPREHENSIVE_COMPLIANCE_CHECK',
        complianceCheckResult: comprehensiveResult
      });

      return comprehensiveResult;

    } catch (error) {
      console.error('❌ Comprehensive compliance check failed:', error);
      throw new Error('Comprehensive compliance check failed');
    }
  }

  /**
   * Generate recommended actions based on compliance checks
   */
  private static generateRecommendedActions(
    traCheck: TRAComplianceCheck,
    botCheck: BOTComplianceCheck,
    amlScreening: AMLScreeningResult | null
  ): string[] {
    const actions: string[] = [];

    // TRA-related actions
    if (traCheck.efdRequired) {
      actions.push('Generate EFD receipt for this transaction');
    }
    if (traCheck.withholdingTaxApplicable) {
      actions.push('Calculate and remit withholding tax to TRA');
    }
    if (traCheck.vatApplicable) {
      actions.push('Include VAT in monthly VAT return');
    }

    // BOT-related actions
    if (botCheck.requiresCentralBankReporting) {
      actions.push('Submit transaction report to Bank of Tanzania');
    }
    if (botCheck.isForexTransaction && botCheck.exceedsReportingThreshold) {
      actions.push('File forex transaction report with BOT');
    }

    // AML-related actions
    if (amlScreening) {
      if (amlScreening.isPEP) {
        actions.push('Conduct enhanced due diligence for PEP customer');
      }
      if (amlScreening.isSanctioned) {
        actions.push('URGENT: Review transaction against sanctions list');
      }
      if (amlScreening.requiresEnhancedDueDiligence) {
        actions.push('Perform enhanced customer due diligence');
      }
      if (amlScreening.riskLevel === 'HIGH' || amlScreening.riskLevel === 'CRITICAL') {
        actions.push('Consider filing suspicious transaction report');
      }
    }

    return actions;
  }
}
