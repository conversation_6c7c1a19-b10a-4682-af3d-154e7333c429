import { db } from "../config/database.js";
import { v4 as uuidv4 } from 'uuid';

export interface AuditLogData {
  companyId: string;
  userId?: string;
  userEmail: string;
  action: 'CREATE' | 'UPDATE' | 'DELETE' | 'LOGIN' | 'LOGOUT' | 'APPROVE' | 'REJECT' | 'REVERSE' | 'EXPORT' | 'IMPORT' | 'VIEW';
  tableName?: string;
  recordId?: string;
  oldValues?: any;
  newValues?: any;
  description: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: any;
}

// New comprehensive audit interfaces
export interface AuditLogEntry {
  tableName: string;
  recordId: string;
  actionType: 'INSERT' | 'UPDATE' | 'DELETE';
  oldValues?: any;
  newValues?: any;
  changedFields?: string[];
  userId?: string;
  userEmail?: string;
  userRole?: string;
  companyId?: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  reason?: string;
  complianceFlags?: any;
  riskLevel?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export interface AccessAuditEntry {
  userId?: string;
  action: string;
  resource?: string;
  success: boolean;
  failureReason?: string;
  ipAddress?: string;
  locationData?: any;
  deviceInfo?: any;
}

export interface FinancialAuditEntry {
  transactionId?: string;
  accountId?: string;
  amount?: number;
  currency?: string;
  actionType: string;
  previousBalance?: number;
  newBalance?: number;
  authorizationLevel?: string;
  approverId?: string;
  complianceCheckResult?: any;
}

class AuditService {
  /**
   * Log an audit event
   */
  public async log(data: AuditLogData): Promise<void> {
    try {
      await db("audit_trail").insert({
        company_id: data.companyId,
        user_id: data.userId || null,
        user_email: data.userEmail,
        action: data.action,
        table_name: data.tableName || null,
        record_id: data.recordId || null,
        old_values: data.oldValues ? JSON.stringify(data.oldValues) : null,
        new_values: data.newValues ? JSON.stringify(data.newValues) : null,
        description: data.description,
        ip_address: data.ipAddress || null,
        user_agent: data.userAgent || null,
        metadata: data.metadata ? JSON.stringify(data.metadata) : '{}',
        created_at: new Date(),
      });
    } catch (error) {
      console.error("Failed to log audit event:", error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Log user authentication events
   */
  public async logAuth(data: {
    companyId?: string;
    userId?: string;
    userEmail: string;
    action: 'LOGIN' | 'LOGOUT';
    ipAddress?: string;
    userAgent?: string;
    success: boolean;
    reason?: string;
  }): Promise<void> {
    await this.log({
      companyId: data.companyId || '',
      userId: data.userId,
      userEmail: data.userEmail,
      action: data.action,
      description: data.success 
        ? `User ${data.action.toLowerCase()} successful`
        : `User ${data.action.toLowerCase()} failed: ${data.reason || 'Unknown error'}`,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      metadata: {
        success: data.success,
        reason: data.reason,
      },
    });
  }

  /**
   * Log CRUD operations
   */
  public async logCrud(data: {
    companyId: string;
    userId: string;
    userEmail: string;
    action: 'CREATE' | 'UPDATE' | 'DELETE';
    tableName: string;
    recordId: string;
    oldValues?: any;
    newValues?: any;
    ipAddress?: string;
    userAgent?: string;
  }): Promise<void> {
    const actionMap = {
      CREATE: 'created',
      UPDATE: 'updated', 
      DELETE: 'deleted',
    };

    await this.log({
      companyId: data.companyId,
      userId: data.userId,
      userEmail: data.userEmail,
      action: data.action,
      tableName: data.tableName,
      recordId: data.recordId,
      oldValues: data.oldValues,
      newValues: data.newValues,
      description: `${data.tableName} record ${actionMap[data.action]}`,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
    });
  }

  /**
   * Log transaction workflow events
   */
  public async logTransactionWorkflow(data: {
    companyId: string;
    userId: string;
    userEmail: string;
    action: 'APPROVE' | 'REJECT' | 'REVERSE';
    transactionId: string;
    transactionNumber: string;
    fromStatus?: string;
    toStatus?: string;
    ipAddress?: string;
    userAgent?: string;
    reason?: string;
  }): Promise<void> {
    const actionMap = {
      APPROVE: 'approved',
      REJECT: 'rejected',
      REVERSE: 'reversed',
    };

    let description = `Transaction ${data.transactionNumber} ${actionMap[data.action]}`;
    if (data.fromStatus && data.toStatus) {
      description += ` (${data.fromStatus} → ${data.toStatus})`;
    }
    if (data.reason) {
      description += ` - ${data.reason}`;
    }

    await this.log({
      companyId: data.companyId,
      userId: data.userId,
      userEmail: data.userEmail,
      action: data.action,
      tableName: 'transactions',
      recordId: data.transactionId,
      description,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      metadata: {
        transactionNumber: data.transactionNumber,
        fromStatus: data.fromStatus,
        toStatus: data.toStatus,
        reason: data.reason,
      },
    });
  }

  /**
   * Log data export events
   */
  public async logExport(data: {
    companyId: string;
    userId: string;
    userEmail: string;
    exportType: string;
    recordCount?: number;
    filters?: any;
    ipAddress?: string;
    userAgent?: string;
  }): Promise<void> {
    await this.log({
      companyId: data.companyId,
      userId: data.userId,
      userEmail: data.userEmail,
      action: 'EXPORT',
      description: `Exported ${data.exportType}${data.recordCount ? ` (${data.recordCount} records)` : ''}`,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      metadata: {
        exportType: data.exportType,
        recordCount: data.recordCount,
        filters: data.filters,
      },
    });
  }

  /**
   * Extract request information for audit logging
   */
  public extractRequestInfo(req: any): {
    ipAddress: string;
    userAgent: string;
    userId?: string;
    userEmail?: string;
    companyId?: string;
  } {
    return {
      ipAddress: req.ip || req.connection?.remoteAddress || 'unknown',
      userAgent: req.get('User-Agent') || 'unknown',
      userId: req.user?.id,
      userEmail: req.user?.email || 'unknown',
      companyId: req.user?.companyId || req.params?.companyId,
    };
  }

  /**
   * NEW COMPREHENSIVE AUDIT METHODS
   */

  /**
   * Log general data changes (new comprehensive method)
   */
  public async logDataChange(entry: AuditLogEntry): Promise<string> {
    try {
      const auditId = uuidv4();

      await db('audit_logs').insert({
        id: auditId,
        table_name: entry.tableName,
        record_id: entry.recordId,
        action_type: entry.actionType,
        old_values: entry.oldValues ? JSON.stringify(entry.oldValues) : null,
        new_values: entry.newValues ? JSON.stringify(entry.newValues) : null,
        changed_fields: entry.changedFields || null,
        user_id: entry.userId || null,
        user_email: entry.userEmail || null,
        user_role: entry.userRole || null,
        company_id: entry.companyId || null,
        ip_address: entry.ipAddress || null,
        user_agent: entry.userAgent || null,
        session_id: entry.sessionId || null,
        reason: entry.reason || null,
        compliance_flags: entry.complianceFlags ? JSON.stringify(entry.complianceFlags) : null,
        risk_level: entry.riskLevel || 'LOW',
        timestamp: new Date()
      });

      console.log(`✅ Audit log created: ${entry.actionType} on ${entry.tableName}:${entry.recordId}`);
      return auditId;
    } catch (error) {
      console.error('❌ Failed to create audit log:', error);
      throw new Error('Failed to create audit log');
    }
  }

  /**
   * Log system access attempts (new comprehensive method)
   */
  public async logAccess(entry: AccessAuditEntry): Promise<string> {
    try {
      const auditId = uuidv4();

      await db('access_audit_logs').insert({
        id: auditId,
        user_id: entry.userId || null,
        action: entry.action,
        resource: entry.resource || null,
        success: entry.success,
        failure_reason: entry.failureReason || null,
        ip_address: entry.ipAddress || null,
        location_data: entry.locationData ? JSON.stringify(entry.locationData) : null,
        device_info: entry.deviceInfo ? JSON.stringify(entry.deviceInfo) : null,
        timestamp: new Date()
      });

      console.log(`✅ Access audit log created: ${entry.action} - ${entry.success ? 'SUCCESS' : 'FAILED'}`);
      return auditId;
    } catch (error) {
      console.error('❌ Failed to create access audit log:', error);
      throw new Error('Failed to create access audit log');
    }
  }

  /**
   * Log financial transactions (new comprehensive method)
   */
  public async logFinancialTransaction(entry: FinancialAuditEntry): Promise<string> {
    try {
      const auditId = uuidv4();

      await db('financial_audit_trail').insert({
        id: auditId,
        transaction_id: entry.transactionId || null,
        account_id: entry.accountId || null,
        amount: entry.amount || null,
        currency: entry.currency || null,
        action_type: entry.actionType,
        previous_balance: entry.previousBalance || null,
        new_balance: entry.newBalance || null,
        authorization_level: entry.authorizationLevel || null,
        approver_id: entry.approverId || null,
        compliance_check_result: entry.complianceCheckResult ? JSON.stringify(entry.complianceCheckResult) : null,
        timestamp: new Date()
      });

      console.log(`✅ Financial audit log created: ${entry.actionType} - ${entry.amount} ${entry.currency}`);
      return auditId;
    } catch (error) {
      console.error('❌ Failed to create financial audit log:', error);
      throw new Error('Failed to create financial audit log');
    }
  }

  /**
   * Create sample audit data for testing
   */
  public async createSampleData(companyId: string): Promise<void> {
    const sampleLogs = [
      {
        companyId,
        userId: null,
        userEmail: '<EMAIL>',
        action: 'LOGIN' as const,
        description: 'User login successful',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
      {
        companyId,
        userId: null,
        userEmail: '<EMAIL>',
        action: 'CREATE' as const,
        tableName: 'accounts',
        recordId: '123e4567-e89b-12d3-a456-************',
        description: 'accounts record created',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
      {
        companyId,
        userId: null,
        userEmail: '<EMAIL>',
        action: 'UPDATE' as const,
        tableName: 'transactions',
        recordId: '123e4567-e89b-12d3-a456-************',
        description: 'transactions record updated',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
      {
        companyId,
        userId: null,
        userEmail: '<EMAIL>',
        action: 'APPROVE' as const,
        tableName: 'transactions',
        recordId: '123e4567-e89b-12d3-a456-************',
        description: 'Transaction TXN-001 approved (PENDING → APPROVED)',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
      {
        companyId,
        userId: null,
        userEmail: '<EMAIL>',
        action: 'EXPORT' as const,
        description: 'Exported audit logs (25 records)',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    ];

    for (const logData of sampleLogs) {
      await this.log(logData);
    }
  }
}

export const auditService = new AuditService();
