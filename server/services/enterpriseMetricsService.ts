import { db } from "../config/database";

/**
 * Service for calculating enterprise-level metrics and KPIs
 */
export class EnterpriseMetricsService {
  /**
   * Calculate gross profit margin
   * Formula: (Revenue - COGS) / Revenue * 100
   */
  async getGrossProfitMargin(companyId: string, startDate: Date, endDate: Date): Promise<number> {
    try {
      // Get revenue
      const revenueResult = await db('transactions')
        .join('transaction_entries', 'transactions.id', 'transaction_entries.transaction_id')
        .join('accounts', 'transaction_entries.account_id', 'accounts.id')
        .where('transactions.company_id', companyId)
        .where('transactions.status', 'POSTED')
        .where('accounts.account_type', 'REVENUE')
        .where('transactions.transaction_date', '>=', startDate.toISOString().split('T')[0])
        .where('transactions.transaction_date', '<=', endDate.toISOString().split('T')[0])
        .sum('transaction_entries.credit_amount as total')
        .first();

      // Get cost of goods sold
      const cogsResult = await db('transactions')
        .join('transaction_entries', 'transactions.id', 'transaction_entries.transaction_id')
        .join('accounts', 'transaction_entries.account_id', 'accounts.id')
        .where('transactions.company_id', companyId)
        .where('transactions.status', 'POSTED')
        .where('accounts.account_type', 'EXPENSE')
        .where('accounts.name', 'like', '%cost of goods sold%')
        .where('transactions.transaction_date', '>=', startDate.toISOString().split('T')[0])
        .where('transactions.transaction_date', '<=', endDate.toISOString().split('T')[0])
        .sum('transaction_entries.debit_amount as total')
        .first();

      const revenue = parseFloat(revenueResult?.total || '0');
      const cogs = parseFloat(cogsResult?.total || '0');

      if (revenue === 0) return 0;
      return ((revenue - cogs) / revenue) * 100;
    } catch (error) {
      console.error('Error calculating gross profit margin:', error);
      return 0;
    }
  }

  /**
   * Calculate operating cash flow
   */
  async getOperatingCashFlow(companyId: string, startDate: Date, endDate: Date): Promise<number> {
    try {
      // Get cash inflows (credits to cash accounts)
      const cashInflowResult = await db('transactions')
        .join('transaction_entries', 'transactions.id', 'transaction_entries.transaction_id')
        .join('accounts', 'transaction_entries.account_id', 'accounts.id')
        .where('transactions.company_id', companyId)
        .where('transactions.status', 'POSTED')
        .where('accounts.account_type', 'ASSET')
        .where(function() {
          this.where('accounts.name', 'like', '%cash%')
            .orWhere('accounts.name', 'like', '%bank%')
        })
        .where('transactions.transaction_date', '>=', startDate.toISOString().split('T')[0])
        .where('transactions.transaction_date', '<=', endDate.toISOString().split('T')[0])
        .sum('transaction_entries.credit_amount as total')
        .first();

      // Get cash outflows (debits from cash accounts)
      const cashOutflowResult = await db('transactions')
        .join('transaction_entries', 'transactions.id', 'transaction_entries.transaction_id')
        .join('accounts', 'transaction_entries.account_id', 'accounts.id')
        .where('transactions.company_id', companyId)
        .where('transactions.status', 'POSTED')
        .where('accounts.account_type', 'ASSET')
        .where(function() {
          this.where('accounts.name', 'like', '%cash%')
            .orWhere('accounts.name', 'like', '%bank%')
        })
        .where('transactions.transaction_date', '>=', startDate.toISOString().split('T')[0])
        .where('transactions.transaction_date', '<=', endDate.toISOString().split('T')[0])
        .sum('transaction_entries.debit_amount as total')
        .first();

      const inflow = parseFloat(cashInflowResult?.total || '0');
      const outflow = parseFloat(cashOutflowResult?.total || '0');

      return inflow - outflow;
    } catch (error) {
      console.error('Error calculating operating cash flow:', error);
      return 0;
    }
  }

  /**
   * Calculate accounts payable balance
   */
  async getAccountsPayable(companyId: string): Promise<number> {
    try {
      const result = await db('accounts')
        .join('transaction_entries', 'accounts.id', 'transaction_entries.account_id')
        .join('transactions', 'transaction_entries.transaction_id', 'transactions.id')
        .where('accounts.company_id', companyId)
        .where('accounts.account_type', 'LIABILITY')
        .where(function() {
          this.where('accounts.name', 'like', '%payable%')
            .orWhere('accounts.code', '2000')
        })
        .where('transactions.status', 'POSTED')
        .sum('transaction_entries.credit_amount as credits')
        .sum('transaction_entries.debit_amount as debits')
        .first();

      const credits = parseFloat(result?.credits || '0');
      const debits = parseFloat(result?.debits || '0');

      return credits - debits;
    } catch (error) {
      console.error('Error calculating accounts payable:', error);
      return 0;
    }
  }

  /**
   * Calculate working capital
   * Formula: Current Assets - Current Liabilities
   */
  async getWorkingCapital(companyId: string): Promise<number> {
    try {
      // Get current assets
      const assetsResult = await db('accounts')
        .join('transaction_entries', 'accounts.id', 'transaction_entries.account_id')
        .join('transactions', 'transaction_entries.transaction_id', 'transactions.id')
        .where('accounts.company_id', companyId)
        .where('accounts.account_type', 'ASSET')
        .where('transactions.status', 'POSTED')
        .sum('transaction_entries.debit_amount as debits')
        .sum('transaction_entries.credit_amount as credits')
        .first();

      // Get current liabilities
      const liabilitiesResult = await db('accounts')
        .join('transaction_entries', 'accounts.id', 'transaction_entries.account_id')
        .join('transactions', 'transaction_entries.transaction_id', 'transactions.id')
        .where('accounts.company_id', companyId)
        .where('accounts.account_type', 'LIABILITY')
        .where('transactions.status', 'POSTED')
        .sum('transaction_entries.credit_amount as credits')
        .sum('transaction_entries.debit_amount as debits')
        .first();

      const assets = parseFloat(assetsResult?.debits || '0') - parseFloat(assetsResult?.credits || '0');
      const liabilities = parseFloat(liabilitiesResult?.credits || '0') - parseFloat(liabilitiesResult?.debits || '0');

      return assets - liabilities;
    } catch (error) {
      console.error('Error calculating working capital:', error);
      return 0;
    }
  }

  /**
   * Calculate burn rate (average monthly expenses)
   */
  async getBurnRate(companyId: string, months: number = 3): Promise<number> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - months);

      const result = await db('transactions')
        .join('transaction_entries', 'transactions.id', 'transaction_entries.transaction_id')
        .join('accounts', 'transaction_entries.account_id', 'accounts.id')
        .where('transactions.company_id', companyId)
        .where('transactions.status', 'POSTED')
        .where('accounts.account_type', 'EXPENSE')
        .where('transactions.transaction_date', '>=', startDate.toISOString().split('T')[0])
        .where('transactions.transaction_date', '<=', endDate.toISOString().split('T')[0])
        .sum('transaction_entries.debit_amount as total')
        .first();

      const totalExpenses = parseFloat(result?.total || '0');
      return totalExpenses / months;
    } catch (error) {
      console.error('Error calculating burn rate:', error);
      return 0;
    }
  }

  /**
   * Calculate average collection period (days to collect A/R)
   */
  async getAverageCollectionPeriod(companyId: string): Promise<number> {
    try {
      // Get average accounts receivable
      const arResult = await db('accounts')
        .join('transaction_entries', 'accounts.id', 'transaction_entries.account_id')
        .join('transactions', 'transaction_entries.transaction_id', 'transactions.id')
        .where('accounts.company_id', companyId)
        .where('accounts.account_type', 'ASSET')
        .where(function() {
          this.where('accounts.name', 'like', '%receivable%')
            .orWhere('accounts.code', '1200')
        })
        .where('transactions.status', 'POSTED')
        .sum('transaction_entries.debit_amount as debits')
        .sum('transaction_entries.credit_amount as credits')
        .first();

      // Get annual revenue
      const endDate = new Date();
      const startDate = new Date();
      startDate.setFullYear(startDate.getFullYear() - 1);

      const revenueResult = await db('transactions')
        .join('transaction_entries', 'transactions.id', 'transaction_entries.transaction_id')
        .join('accounts', 'transaction_entries.account_id', 'accounts.id')
        .where('transactions.company_id', companyId)
        .where('transactions.status', 'POSTED')
        .where('accounts.account_type', 'REVENUE')
        .where('transactions.transaction_date', '>=', startDate.toISOString().split('T')[0])
        .where('transactions.transaction_date', '<=', endDate.toISOString().split('T')[0])
        .sum('transaction_entries.credit_amount as total')
        .first();

      const ar = parseFloat(arResult?.debits || '0') - parseFloat(arResult?.credits || '0');
      const annualRevenue = parseFloat(revenueResult?.total || '0');

      if (annualRevenue === 0) return 0;
      return (ar / annualRevenue) * 365;
    } catch (error) {
      console.error('Error calculating average collection period:', error);
      return 0;
    }
  }

  /**
   * Calculate average payment period (days to pay A/P)
   */
  async getAveragePaymentPeriod(companyId: string): Promise<number> {
    try {
      // Get average accounts payable
      const apResult = await db('accounts')
        .join('transaction_entries', 'accounts.id', 'transaction_entries.account_id')
        .join('transactions', 'transaction_entries.transaction_id', 'transactions.id')
        .where('accounts.company_id', companyId)
        .where('accounts.account_type', 'LIABILITY')
        .where(function() {
          this.where('accounts.name', 'like', '%payable%')
            .orWhere('accounts.code', '2000')
        })
        .where('transactions.status', 'POSTED')
        .sum('transaction_entries.credit_amount as credits')
        .sum('transaction_entries.debit_amount as debits')
        .first();

      // Get annual purchases (approximated by COGS)
      const endDate = new Date();
      const startDate = new Date();
      startDate.setFullYear(startDate.getFullYear() - 1);

      const purchasesResult = await db('transactions')
        .join('transaction_entries', 'transactions.id', 'transaction_entries.transaction_id')
        .join('accounts', 'transaction_entries.account_id', 'accounts.id')
        .where('transactions.company_id', companyId)
        .where('transactions.status', 'POSTED')
        .where('accounts.account_type', 'EXPENSE')
        .where('accounts.name', 'like', '%cost of goods sold%')
        .where('transactions.transaction_date', '>=', startDate.toISOString().split('T')[0])
        .where('transactions.transaction_date', '<=', endDate.toISOString().split('T')[0])
        .sum('transaction_entries.debit_amount as total')
        .first();

      const ap = parseFloat(apResult?.credits || '0') - parseFloat(apResult?.debits || '0');
      const annualPurchases = parseFloat(purchasesResult?.total || '0');

      if (annualPurchases === 0) return 0;
      return (ap / annualPurchases) * 365;
    } catch (error) {
      console.error('Error calculating average payment period:', error);
      return 0;
    }
  }
}

export const enterpriseMetricsService = new EnterpriseMetricsService();
