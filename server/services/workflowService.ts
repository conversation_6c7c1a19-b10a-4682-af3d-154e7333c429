import { db } from '../config/database';
import { v4 as uuidv4 } from 'uuid';
import * as cron from 'node-cron';
import { emailService } from './emailService';
import { notificationService } from './notificationService';

export interface WorkflowTrigger {
  type: 'SCHEDULE' | 'EVENT' | 'MANUAL' | 'CONDITION';
  config: {
    schedule?: string; // Cron expression
    event?: string; // Event name
    condition?: {
      field: string;
      operator: 'equals' | 'greater_than' | 'less_than' | 'contains' | 'not_equals';
      value: any;
    };
  };
}

export interface WorkflowAction {
  type: 'EMAIL' | 'NOTIFICATION' | 'CREATE_TRANSACTION' | 'UPDATE_RECORD' | 'GENERATE_REPORT' | 'WEBHOOK' | 'APPROVAL_REQUEST';
  config: {
    // Email action
    to?: string[];
    subject?: string;
    template?: string;
    data?: any;
    
    // Notification action
    message?: string;
    users?: string[];
    
    // Transaction action
    accountId?: string;
    amount?: number;
    description?: string;
    
    // Update record action
    table?: string;
    recordId?: string;
    updates?: any;
    
    // Report action
    reportType?: string;
    format?: string;
    recipients?: string[];
    
    // Webhook action
    url?: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    headers?: any;
    payload?: any;
    
    // Approval action
    approvers?: string[];
    approvalType?: string;
    requiredApprovals?: number;
  };
}

export interface WorkflowCondition {
  field: string;
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains' | 'not_equals' | 'is_null' | 'is_not_null';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

export interface Workflow {
  id: string;
  companyId: string;
  name: string;
  description?: string;
  isActive: boolean;
  trigger: WorkflowTrigger;
  conditions?: WorkflowCondition[];
  actions: WorkflowAction[];
  metadata?: any;
  createdBy: string;
  lastRunAt?: Date;
  nextRunAt?: Date;
  runCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  companyId: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  triggerData?: any;
  startedAt: Date;
  completedAt?: Date;
  error?: string;
  results?: any;
  createdAt: Date;
}

export interface ApprovalRequest {
  id: string;
  companyId: string;
  workflowExecutionId: string;
  requestedBy: string;
  approvers: string[];
  requiredApprovals: number;
  currentApprovals: number;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'EXPIRED';
  title: string;
  description?: string;
  data?: any;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface ApprovalResponse {
  id: string;
  approvalRequestId: string;
  userId: string;
  decision: 'APPROVE' | 'REJECT';
  comments?: string;
  createdAt: Date;
}

class WorkflowService {
  private scheduledJobs: Map<string, cron.ScheduledTask> = new Map();

  constructor() {
    this.initializeScheduledWorkflows();
  }

  /**
   * Create a new workflow
   */
  public async createWorkflow(
    companyId: string,
    userId: string,
    workflowData: Omit<Workflow, 'id' | 'companyId' | 'createdBy' | 'lastRunAt' | 'nextRunAt' | 'runCount' | 'createdAt' | 'updatedAt'>
  ): Promise<Workflow> {
    try {
      const workflowId = uuidv4();
      const workflow: Workflow = {
        id: workflowId,
        companyId,
        createdBy: userId,
        ...workflowData,
        runCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Calculate next run time for scheduled workflows
      if (workflow.trigger.type === 'SCHEDULE' && workflow.trigger.config.schedule) {
        workflow.nextRunAt = this.calculateNextRun(workflow.trigger.config.schedule);
      }

      // Save to database
      await db('workflows').insert({
        id: workflow.id,
        company_id: workflow.companyId,
        name: workflow.name,
        description: workflow.description,
        is_active: workflow.isActive,
        trigger: JSON.stringify(workflow.trigger),
        conditions: workflow.conditions ? JSON.stringify(workflow.conditions) : null,
        actions: JSON.stringify(workflow.actions),
        metadata: workflow.metadata ? JSON.stringify(workflow.metadata) : null,
        created_by: workflow.createdBy,
        last_run_at: workflow.lastRunAt,
        next_run_at: workflow.nextRunAt,
        run_count: workflow.runCount,
        created_at: workflow.createdAt,
        updated_at: workflow.updatedAt,
      });

      // Schedule if it's a scheduled workflow
      if (workflow.isActive && workflow.trigger.type === 'SCHEDULE') {
        this.scheduleWorkflow(workflow);
      }

      return workflow;
    } catch (error) {
      console.error('Failed to create workflow:', error);
      throw new Error('Failed to create workflow');
    }
  }

  /**
   * Execute a workflow
   */
  public async executeWorkflow(
    workflowId: string,
    triggerData?: any,
    _userId?: string
  ): Promise<WorkflowExecution> {
    let execution: WorkflowExecution | null = null;

    try {
      const workflow = await this.getWorkflow(workflowId);
      if (!workflow) {
        throw new Error('Workflow not found');
      }

      if (!workflow.isActive) {
        throw new Error('Workflow is not active');
      }

      const executionId = uuidv4();
      execution = {
        id: executionId,
        workflowId: workflow.id,
        companyId: workflow.companyId,
        status: 'PENDING',
        triggerData,
        startedAt: new Date(),
        createdAt: new Date(),
      };

      // Save execution to database
      await db('workflow_executions').insert({
        id: execution.id,
        workflow_id: execution.workflowId,
        company_id: execution.companyId,
        status: execution.status,
        trigger_data: triggerData ? JSON.stringify(triggerData) : null,
        started_at: execution.startedAt,
        created_at: execution.createdAt,
      });

      // Check conditions if any
      if (workflow.conditions && workflow.conditions.length > 0) {
        const conditionsMet = await this.evaluateConditions(workflow.conditions, triggerData);
        if (!conditionsMet) {
          await this.updateExecutionStatus(executionId, 'COMPLETED', 'Conditions not met');
          return { ...execution, status: 'COMPLETED' };
        }
      }

      // Update status to running
      await this.updateExecutionStatus(executionId, 'RUNNING');
      execution.status = 'RUNNING';

      // Execute actions
      const results = await this.executeActions(workflow.actions, triggerData, execution);

      // Update execution with results
      await this.updateExecutionStatus(executionId, 'COMPLETED', null, results);
      execution.status = 'COMPLETED';
      execution.completedAt = new Date();
      execution.results = results;

      // Update workflow run count and last run time
      await db('workflows')
        .where('id', workflowId)
        .update({
          last_run_at: new Date(),
          run_count: db.raw('run_count + 1'),
          next_run_at: workflow.trigger.type === 'SCHEDULE'
            ? this.calculateNextRun(workflow.trigger.config.schedule!)
            : null,
          updated_at: new Date(),
        });

      return execution;
    } catch (error: any) {
      console.error('Failed to execute workflow:', error);

      // Update execution status to failed
      if (execution) {
        await this.updateExecutionStatus(execution.id, 'FAILED', error.message);
      }

      throw new Error('Failed to execute workflow');
    }
  }

  /**
   * Trigger workflows based on events
   */
  public async triggerEventWorkflows(
    companyId: string,
    eventName: string,
    eventData: any
  ): Promise<void> {
    try {
      const workflows = await db('workflows')
        .where('company_id', companyId)
        .where('is_active', true)
        .whereRaw("trigger->>'type' = ?", ['EVENT'])
        .whereRaw("trigger->'config'->>'event' = ?", [eventName]);

      for (const workflowData of workflows) {
        const workflow = this.mapDatabaseWorkflowToModel(workflowData);
        await this.executeWorkflow(workflow.id, eventData);
      }
    } catch (error) {
      console.error('Failed to trigger event workflows:', error);
    }
  }

  /**
   * Create approval request
   */
  public async createApprovalRequest(
    companyId: string,
    workflowExecutionId: string,
    requestData: {
      requestedBy: string;
      approvers: string[];
      requiredApprovals: number;
      title: string;
      description?: string;
      data?: any;
      expiresAt?: Date;
    }
  ): Promise<ApprovalRequest> {
    try {
      const approvalId = uuidv4();
      const approval: ApprovalRequest = {
        id: approvalId,
        companyId,
        workflowExecutionId,
        ...requestData,
        currentApprovals: 0,
        status: 'PENDING',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await db('approval_requests').insert({
        id: approval.id,
        company_id: approval.companyId,
        workflow_execution_id: approval.workflowExecutionId,
        requested_by: approval.requestedBy,
        approvers: JSON.stringify(approval.approvers),
        required_approvals: approval.requiredApprovals,
        current_approvals: approval.currentApprovals,
        status: approval.status,
        title: approval.title,
        description: approval.description,
        data: approval.data ? JSON.stringify(approval.data) : null,
        expires_at: approval.expiresAt,
        created_at: approval.createdAt,
        updated_at: approval.updatedAt,
      });

      // Send notifications to approvers
      await this.notifyApprovers(approval);

      return approval;
    } catch (error) {
      console.error('Failed to create approval request:', error);
      throw new Error('Failed to create approval request');
    }
  }

  /**
   * Process approval
   */
  public async processApproval(
    approvalId: string,
    userId: string,
    decision: 'APPROVE' | 'REJECT',
    comments?: string
  ): Promise<ApprovalRequest> {
    try {
      const approval = await this.getApprovalRequest(approvalId);
      if (!approval) {
        throw new Error('Approval request not found');
      }

      if (approval.status !== 'PENDING') {
        throw new Error('Approval request is no longer pending');
      }

      if (!approval.approvers.includes(userId)) {
        throw new Error('User is not authorized to approve this request');
      }

      // Check if user has already approved
      const existingApproval = await db('approval_responses')
        .where('approval_request_id', approvalId)
        .where('user_id', userId)
        .first();

      if (existingApproval) {
        throw new Error('User has already responded to this approval request');
      }

      // Record the approval response
      await db('approval_responses').insert({
        id: uuidv4(),
        approval_request_id: approvalId,
        user_id: userId,
        decision,
        comments,
        created_at: new Date(),
      });

      // Update approval request
      let newStatus: 'PENDING' | 'APPROVED' | 'REJECTED' | 'EXPIRED' = approval.status;
      let currentApprovals = approval.currentApprovals;

      if (decision === 'APPROVE') {
        currentApprovals++;
        if (currentApprovals >= approval.requiredApprovals) {
          newStatus = 'APPROVED';
        }
      } else {
        newStatus = 'REJECTED';
      }

      await db('approval_requests')
        .where('id', approvalId)
        .update({
          current_approvals: currentApprovals,
          status: newStatus,
          updated_at: new Date(),
        });

      const updatedApproval = { ...approval, currentApprovals, status: newStatus };

      // Continue workflow execution if approved
      if (newStatus === 'APPROVED') {
        await this.continueWorkflowAfterApproval(approval.workflowExecutionId);
      } else if (newStatus === 'REJECTED') {
        await this.updateExecutionStatus(approval.workflowExecutionId, 'FAILED', 'Approval rejected');
      }

      return updatedApproval;
    } catch (error) {
      console.error('Failed to process approval:', error);
      throw new Error('Failed to process approval');
    }
  }

  /**
   * Get workflows for a company
   */
  public async getWorkflows(
    companyId: string,
    options: {
      isActive?: boolean;
      triggerType?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{
    workflows: Workflow[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const { isActive, triggerType, page = 1, limit = 20 } = options;

    let query = db('workflows').where('company_id', companyId);

    if (isActive !== undefined) {
      query = query.where('is_active', isActive);
    }

    if (triggerType) {
      query = query.whereRaw("trigger->>'type' = ?", [triggerType]);
    }

    const totalCount = await query.clone().count('* as count').first();
    const total = parseInt(totalCount?.count as string) || 0;

    const offset = (page - 1) * limit;
    const workflows = await query
      .orderBy('created_at', 'desc')
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return {
      workflows: workflows.map(this.mapDatabaseWorkflowToModel),
      total,
      page,
      totalPages,
    };
  }

  /**
   * Get workflow statistics for a company
   */
  public async getWorkflowStats(companyId: string): Promise<{
    activeWorkflows: number;
    totalRuns: number;
    successRate: number;
    needsAttention: number;
    recentExecutions: any[];
  }> {
    try {
      // Get workflow counts
      const activeCount = await db('workflows')
        .where('company_id', companyId)
        .where('is_active', true)
        .count('* as count')
        .first();

      const pausedCount = await db('workflows')
        .where('company_id', companyId)
        .where('is_active', false)
        .count('* as count')
        .first();

      // Get total runs
      const totalRunsResult = await db('workflows')
        .where('company_id', companyId)
        .sum('run_count as total')
        .first();

      // Get execution statistics
      const executionStats = await db('workflow_executions')
        .where('company_id', companyId)
        .select(
          db.raw('COUNT(*) as total_executions'),
          db.raw('COUNT(CASE WHEN status = \'COMPLETED\' THEN 1 END) as successful_executions'),
          db.raw('COUNT(CASE WHEN status = \'FAILED\' THEN 1 END) as failed_executions')
        )
        .first();

      // Get recent executions
      const recentExecutions = await db('workflow_executions as we')
        .join('workflows as w', 'we.workflow_id', 'w.id')
        .where('we.company_id', companyId)
        .select(
          'we.id',
          'we.status',
          'we.started_at',
          'we.completed_at',
          'w.name as workflow_name'
        )
        .orderBy('we.started_at', 'desc')
        .limit(10);

      const activeWorkflows = parseInt(activeCount?.count as string) || 0;
      const totalRuns = parseInt(totalRunsResult?.total as string) || 0;
      const totalExecutions = parseInt(executionStats?.total_executions as string) || 0;
      const successfulExecutions = parseInt(executionStats?.successful_executions as string) || 0;
      const failedExecutions = parseInt(executionStats?.failed_executions as string) || 0;

      const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0;
      const needsAttention = parseInt(pausedCount?.count as string) || 0 + (failedExecutions > 0 ? 1 : 0);

      return {
        activeWorkflows,
        totalRuns,
        successRate: Math.round(successRate * 10) / 10, // Round to 1 decimal
        needsAttention,
        recentExecutions: recentExecutions.map(exec => ({
          id: exec.id,
          workflowName: exec.workflow_name,
          status: exec.status,
          startedAt: exec.started_at,
          completedAt: exec.completed_at,
          duration: exec.completed_at
            ? new Date(exec.completed_at).getTime() - new Date(exec.started_at).getTime()
            : null
        }))
      };
    } catch (error) {
      console.error('Failed to get workflow stats:', error);
      throw new Error('Failed to get workflow stats');
    }
  }

  /**
   * Update workflow status (activate/deactivate)
   */
  public async updateWorkflowStatus(
    workflowId: string,
    isActive: boolean
  ): Promise<Workflow> {
    try {
      const workflow = await this.getWorkflow(workflowId);
      if (!workflow) {
        throw new Error('Workflow not found');
      }

      await db('workflows')
        .where('id', workflowId)
        .update({
          is_active: isActive,
          updated_at: new Date(),
        });

      // Handle scheduling
      if (isActive && workflow.trigger.type === 'SCHEDULE') {
        this.scheduleWorkflow({ ...workflow, isActive });
      } else if (!isActive) {
        // Remove from scheduled jobs
        const task = this.scheduledJobs.get(workflowId);
        if (task) {
          task.stop();
          this.scheduledJobs.delete(workflowId);
        }
      }

      return { ...workflow, isActive };
    } catch (error) {
      console.error('Failed to update workflow status:', error);
      throw new Error('Failed to update workflow status');
    }
  }

  /**
   * Update workflow
   */
  public async updateWorkflow(
    workflowId: string,
    updates: Partial<Omit<Workflow, 'id' | 'companyId' | 'createdBy' | 'createdAt'>>
  ): Promise<Workflow> {
    try {
      const workflow = await this.getWorkflow(workflowId);
      if (!workflow) {
        throw new Error('Workflow not found');
      }

      const updateData: any = {
        updated_at: new Date(),
      };

      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.description !== undefined) updateData.description = updates.description;
      if (updates.isActive !== undefined) updateData.is_active = updates.isActive;
      if (updates.trigger !== undefined) updateData.trigger = JSON.stringify(updates.trigger);
      if (updates.conditions !== undefined) updateData.conditions = updates.conditions ? JSON.stringify(updates.conditions) : null;
      if (updates.actions !== undefined) updateData.actions = JSON.stringify(updates.actions);
      if (updates.metadata !== undefined) updateData.metadata = updates.metadata ? JSON.stringify(updates.metadata) : null;

      await db('workflows')
        .where('id', workflowId)
        .update(updateData);

      // Handle scheduling changes
      const task = this.scheduledJobs.get(workflowId);
      if (task) {
        task.stop();
        this.scheduledJobs.delete(workflowId);
      }

      const updatedWorkflow = { ...workflow, ...updates, updatedAt: new Date() };

      if (updatedWorkflow.isActive && updatedWorkflow.trigger.type === 'SCHEDULE') {
        this.scheduleWorkflow(updatedWorkflow);
      }

      return updatedWorkflow;
    } catch (error) {
      console.error('Failed to update workflow:', error);
      throw new Error('Failed to update workflow');
    }
  }

  /**
   * Delete workflow
   */
  public async deleteWorkflow(workflowId: string): Promise<void> {
    try {
      const workflow = await this.getWorkflow(workflowId);
      if (!workflow) {
        throw new Error('Workflow not found');
      }

      // Remove from scheduled jobs
      const task = this.scheduledJobs.get(workflowId);
      if (task) {
        task.stop();
        this.scheduledJobs.delete(workflowId);
      }

      // Delete related records (executions, approvals, etc.)
      await db.transaction(async (trx) => {
        // Delete approval responses first (foreign key constraint)
        await trx('approval_responses')
          .whereIn('approval_request_id',
            trx('approval_requests')
              .select('id')
              .whereIn('workflow_execution_id',
                trx('workflow_executions')
                  .select('id')
                  .where('workflow_id', workflowId)
              )
          )
          .del();

        // Delete approval requests
        await trx('approval_requests')
          .whereIn('workflow_execution_id',
            trx('workflow_executions')
              .select('id')
              .where('workflow_id', workflowId)
          )
          .del();

        // Delete workflow executions
        await trx('workflow_executions')
          .where('workflow_id', workflowId)
          .del();

        // Delete the workflow
        await trx('workflows')
          .where('id', workflowId)
          .del();
      });
    } catch (error) {
      console.error('Failed to delete workflow:', error);
      throw new Error('Failed to delete workflow');
    }
  }

  // Private helper methods
  private async initializeScheduledWorkflows(): Promise<void> {
    try {
      const scheduledWorkflows = await db('workflows')
        .where('is_active', true)
        .whereRaw("trigger->>'type' = ?", ['SCHEDULE']);

      for (const workflowData of scheduledWorkflows) {
        const workflow = this.mapDatabaseWorkflowToModel(workflowData);
        this.scheduleWorkflow(workflow);
      }
    } catch (error) {
      console.error('Failed to initialize scheduled workflows:', error);
    }
  }

  private scheduleWorkflow(workflow: Workflow): void {
    if (workflow.trigger.type !== 'SCHEDULE' || !workflow.trigger.config.schedule) {
      return;
    }

    try {
      const task = cron.schedule(
        workflow.trigger.config.schedule,
        async () => {
          try {
            await this.executeWorkflow(workflow.id);
          } catch (error) {
            console.error(`Failed to execute scheduled workflow ${workflow.id}:`, error);
          }
        }
      );

      this.scheduledJobs.set(workflow.id, task);
      task.start();
    } catch (error) {
      console.error(`Failed to schedule workflow ${workflow.id}:`, error);
    }
  }

  private calculateNextRun(cronExpression: string): Date {
    // Simple implementation - in production, use a proper cron parser like node-cron-parser
    // For now, we'll use a basic calculation based on common patterns
    const now = new Date();

    // Basic parsing for common patterns
    if (cronExpression === '0 9 * * 1') { // Every Monday at 9 AM
      const nextRun = new Date(now);
      nextRun.setDate(now.getDate() + (1 + 7 - now.getDay()) % 7);
      nextRun.setHours(9, 0, 0, 0);
      return nextRun;
    }

    // Default to 24 hours from now
    const nextRun = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    return nextRun;
  }

  private async evaluateConditions(
    conditions: WorkflowCondition[],
    data: any
  ): Promise<boolean> {
    // Simple condition evaluation - can be enhanced for complex logic
    for (const condition of conditions) {
      const fieldValue = this.getNestedValue(data, condition.field);
      const conditionMet = this.evaluateCondition(fieldValue, condition.operator, condition.value);
      
      if (!conditionMet) {
        return false; // All conditions must be met (AND logic)
      }
    }
    return true;
  }

  private evaluateCondition(fieldValue: any, operator: string, expectedValue: any): boolean {
    switch (operator) {
      case 'equals':
        return fieldValue === expectedValue;
      case 'not_equals':
        return fieldValue !== expectedValue;
      case 'greater_than':
        return fieldValue > expectedValue;
      case 'less_than':
        return fieldValue < expectedValue;
      case 'contains':
        return String(fieldValue).includes(String(expectedValue));
      case 'is_null':
        return fieldValue == null;
      case 'is_not_null':
        return fieldValue != null;
      default:
        return false;
    }
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private async executeActions(
    actions: WorkflowAction[],
    triggerData: any,
    execution: WorkflowExecution
  ): Promise<any[]> {
    const results = [];

    for (const action of actions) {
      try {
        let result: any;

        switch (action.type) {
          case 'EMAIL':
            result = await this.executeEmailAction(action, triggerData);
            break;
          case 'NOTIFICATION':
            result = await this.executeNotificationAction(action, triggerData);
            break;
          case 'APPROVAL_REQUEST':
            result = await this.executeApprovalAction(action, triggerData, execution);
            break;
          case 'GENERATE_REPORT':
            result = await this.executeReportAction(action, triggerData);
            break;
          case 'WEBHOOK':
            result = await this.executeWebhookAction(action, triggerData);
            break;
          default:
            result = { error: `Unknown action type: ${action.type}` };
        }

        results.push({ action: action.type, result });
      } catch (error: any) {
        results.push({ action: action.type, error: error.message });
      }
    }

    return results;
  }

  private async executeEmailAction(action: WorkflowAction, data: any): Promise<any> {
    if (!action.config.to || !action.config.subject) {
      throw new Error('Email action requires to and subject');
    }

    return await emailService.sendEmail({
      to: action.config.to,
      subject: action.config.subject,
      template: action.config.template,
      data: { ...data, ...action.config.data },
    });
  }

  private async executeNotificationAction(action: WorkflowAction, data: any): Promise<any> {
    if (!action.config.message || !action.config.users) {
      throw new Error('Notification action requires message and users');
    }

    return await notificationService.sendNotification({
      message: action.config.message,
      users: action.config.users,
      data,
    });
  }

  private async executeApprovalAction(
    action: WorkflowAction,
    data: any,
    execution: WorkflowExecution
  ): Promise<any> {
    if (!action.config.approvers || !action.config.approvalType) {
      throw new Error('Approval action requires approvers and approval type');
    }

    // Pause workflow execution
    await this.updateExecutionStatus(execution.id, 'PENDING', 'Waiting for approval');

    return await this.createApprovalRequest(execution.companyId, execution.id, {
      requestedBy: 'system', // Could be enhanced to track actual user
      approvers: action.config.approvers,
      requiredApprovals: action.config.requiredApprovals || 1,
      title: action.config.approvalType,
      description: `Approval required for workflow execution`,
      data,
    });
  }

  private async executeReportAction(_action: WorkflowAction, _data: any): Promise<any> {
    // Implementation would integrate with export service
    return { message: 'Report generation not implemented yet' };
  }

  private async executeWebhookAction(action: WorkflowAction, data: any): Promise<any> {
    if (!action.config.url) {
      throw new Error('Webhook action requires URL');
    }

    try {
      const fetch = (await import('node-fetch')).default;
      const response = await fetch(action.config.url, {
        method: action.config.method || 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...action.config.headers,
        },
        body: JSON.stringify(action.config.payload || data),
      });

      return {
        status: response.status,
        statusText: response.statusText,
        data: await response.text(),
      };
    } catch (error: any) {
      throw new Error(`Webhook request failed: ${error.message}`);
    }
  }

  private async updateExecutionStatus(
    executionId: string,
    status: WorkflowExecution['status'],
    error?: string,
    results?: any
  ): Promise<void> {
    const updates: any = {
      status,
      updated_at: new Date(),
    };

    if (status === 'COMPLETED' || status === 'FAILED') {
      updates.completed_at = new Date();
    }

    if (error) {
      updates.error = error;
    }

    if (results) {
      updates.results = JSON.stringify(results);
    }

    await db('workflow_executions')
      .where('id', executionId)
      .update(updates);
  }

  private async getWorkflow(workflowId: string): Promise<Workflow | null> {
    const workflow = await db('workflows').where('id', workflowId).first();
    return workflow ? this.mapDatabaseWorkflowToModel(workflow) : null;
  }

  private async getApprovalRequest(approvalId: string): Promise<ApprovalRequest | null> {
    const approval = await db('approval_requests').where('id', approvalId).first();
    if (!approval) return null;

    return {
      id: approval.id,
      companyId: approval.company_id,
      workflowExecutionId: approval.workflow_execution_id,
      requestedBy: approval.requested_by,
      approvers: JSON.parse(approval.approvers),
      requiredApprovals: approval.required_approvals,
      currentApprovals: approval.current_approvals,
      status: approval.status,
      title: approval.title,
      description: approval.description,
      data: approval.data ? JSON.parse(approval.data) : undefined,
      expiresAt: approval.expires_at,
      createdAt: approval.created_at,
      updatedAt: approval.updated_at,
    };
  }

  private async notifyApprovers(approval: ApprovalRequest): Promise<void> {
    // Send notifications to all approvers
    for (const approverId of approval.approvers) {
      await notificationService.sendNotification({
        userId: approverId,
        title: 'Approval Required',
        message: approval.title,
        type: 'APPROVAL_REQUEST',
        data: { approvalId: approval.id },
      });
    }
  }

  private async continueWorkflowAfterApproval(executionId: string): Promise<void> {
    // Resume workflow execution after approval
    await this.updateExecutionStatus(executionId, 'RUNNING');
    // Additional logic to continue workflow would go here
  }

  private mapDatabaseWorkflowToModel(dbWorkflow: any): Workflow {
    // Helper function to safely parse JSON (handles both string and object cases)
    const safeJsonParse = (value: any, fieldName: string) => {
      if (value === null || value === undefined) {
        return null;
      }

      if (typeof value === 'string') {
        try {
          return JSON.parse(value);
        } catch (error) {
          console.error(`Failed to parse JSON for ${fieldName}:`, value, error);
          return null;
        }
      }

      if (typeof value === 'object') {
        return value; // Already an object
      }

      console.error(`Unexpected type for ${fieldName}:`, typeof value, value);
      return null;
    };

    return {
      id: dbWorkflow.id,
      companyId: dbWorkflow.company_id,
      name: dbWorkflow.name,
      description: dbWorkflow.description,
      isActive: dbWorkflow.is_active,
      trigger: safeJsonParse(dbWorkflow.trigger, 'trigger'),
      conditions: dbWorkflow.conditions ? safeJsonParse(dbWorkflow.conditions, 'conditions') : undefined,
      actions: safeJsonParse(dbWorkflow.actions, 'actions'),
      metadata: dbWorkflow.metadata ? safeJsonParse(dbWorkflow.metadata, 'metadata') : undefined,
      createdBy: dbWorkflow.created_by,
      lastRunAt: dbWorkflow.last_run_at,
      nextRunAt: dbWorkflow.next_run_at,
      runCount: dbWorkflow.run_count,
      createdAt: dbWorkflow.created_at,
      updatedAt: dbWorkflow.updated_at,
    };
  }
}

export const workflowService = new WorkflowService();
