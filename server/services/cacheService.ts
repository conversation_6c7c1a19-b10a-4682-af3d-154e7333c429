import Redis from 'ioredis';
import { promisify } from 'util';

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  prefix?: string;
  compress?: boolean;
  serialize?: boolean;
}

interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
  hitRate: number;
}

class CacheService {
  private redis: Redis;
  private isConnected: boolean = false;
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    errors: 0,
    hitRate: 0,
  };

  constructor() {
    const redisConfig = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD || undefined,
      db: parseInt(process.env.REDIS_DB || '0'),
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keepAlive: 30000,
      connectTimeout: 10000,
      commandTimeout: 5000,
    };

    this.redis = new Redis(redisConfig);
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.redis.on('connect', () => {
      console.log('Redis connected');
      this.isConnected = true;
    });

    this.redis.on('ready', () => {
      console.log('Redis ready');
    });

    this.redis.on('error', (error) => {
      console.error('Redis error:', error);
      this.stats.errors++;
      this.isConnected = false;
    });

    this.redis.on('close', () => {
      console.log('Redis connection closed');
      this.isConnected = false;
    });

    this.redis.on('reconnecting', () => {
      console.log('Redis reconnecting...');
    });
  }

  /**
   * Initialize Redis connection
   */
  public async connect(): Promise<void> {
    try {
      await this.redis.connect();
      console.log('Cache service initialized');
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      // Don't throw error - allow app to continue without cache
    }
  }

  /**
   * Close Redis connection
   */
  public async disconnect(): Promise<void> {
    try {
      await this.redis.quit();
      console.log('Cache service disconnected');
    } catch (error) {
      console.error('Error disconnecting from Redis:', error);
    }
  }

  /**
   * Get value from cache
   */
  public async get<T = any>(key: string, options: CacheOptions = {}): Promise<T | null> {
    if (!this.isConnected) {
      this.stats.misses++;
      return null;
    }

    try {
      const fullKey = this.buildKey(key, options.prefix);
      const value = await this.redis.get(fullKey);

      if (value === null) {
        this.stats.misses++;
        return null;
      }

      this.stats.hits++;
      this.updateHitRate();

      if (options.serialize !== false) {
        return JSON.parse(value);
      }

      return value as T;
    } catch (error) {
      console.error('Cache get error:', error);
      this.stats.errors++;
      this.stats.misses++;
      return null;
    }
  }

  /**
   * Set value in cache
   */
  public async set(
    key: string,
    value: any,
    options: CacheOptions = {}
  ): Promise<boolean> {
    if (!this.isConnected) {
      return false;
    }

    try {
      const fullKey = this.buildKey(key, options.prefix);
      const ttl = options.ttl || 3600; // Default 1 hour
      
      let serializedValue: string;
      if (options.serialize !== false) {
        serializedValue = JSON.stringify(value);
      } else {
        serializedValue = value;
      }

      await this.redis.setex(fullKey, ttl, serializedValue);
      this.stats.sets++;
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      this.stats.errors++;
      return false;
    }
  }

  /**
   * Delete value from cache
   */
  public async delete(key: string, options: CacheOptions = {}): Promise<boolean> {
    if (!this.isConnected) {
      return false;
    }

    try {
      const fullKey = this.buildKey(key, options.prefix);
      const result = await this.redis.del(fullKey);
      this.stats.deletes++;
      return result > 0;
    } catch (error) {
      console.error('Cache delete error:', error);
      this.stats.errors++;
      return false;
    }
  }

  /**
   * Delete multiple keys by pattern
   */
  public async deletePattern(pattern: string, options: CacheOptions = {}): Promise<number> {
    if (!this.isConnected) {
      return 0;
    }

    try {
      const fullPattern = this.buildKey(pattern, options.prefix);
      const keys = await this.redis.keys(fullPattern);
      
      if (keys.length === 0) {
        return 0;
      }

      const result = await this.redis.del(...keys);
      this.stats.deletes += result;
      return result;
    } catch (error) {
      console.error('Cache delete pattern error:', error);
      this.stats.errors++;
      return 0;
    }
  }

  /**
   * Check if key exists
   */
  public async exists(key: string, options: CacheOptions = {}): Promise<boolean> {
    if (!this.isConnected) {
      return false;
    }

    try {
      const fullKey = this.buildKey(key, options.prefix);
      const result = await this.redis.exists(fullKey);
      return result === 1;
    } catch (error) {
      console.error('Cache exists error:', error);
      this.stats.errors++;
      return false;
    }
  }

  /**
   * Get or set pattern - get from cache or execute function and cache result
   */
  public async getOrSet<T = any>(
    key: string,
    fetchFunction: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(key, options);
    if (cached !== null) {
      return cached;
    }

    // Execute function and cache result
    try {
      const result = await fetchFunction();
      await this.set(key, result, options);
      return result;
    } catch (error) {
      console.error('Cache getOrSet error:', error);
      throw error;
    }
  }

  /**
   * Increment counter
   */
  public async increment(key: string, amount: number = 1, options: CacheOptions = {}): Promise<number> {
    if (!this.isConnected) {
      return 0;
    }

    try {
      const fullKey = this.buildKey(key, options.prefix);
      const result = await this.redis.incrby(fullKey, amount);
      
      // Set expiration if specified
      if (options.ttl) {
        await this.redis.expire(fullKey, options.ttl);
      }
      
      return result;
    } catch (error) {
      console.error('Cache increment error:', error);
      this.stats.errors++;
      return 0;
    }
  }

  /**
   * Set with expiration at specific time
   */
  public async setExpireAt(
    key: string,
    value: any,
    expireAt: Date,
    options: CacheOptions = {}
  ): Promise<boolean> {
    if (!this.isConnected) {
      return false;
    }

    try {
      const fullKey = this.buildKey(key, options.prefix);
      let serializedValue: string;
      
      if (options.serialize !== false) {
        serializedValue = JSON.stringify(value);
      } else {
        serializedValue = value;
      }

      await this.redis.set(fullKey, serializedValue);
      await this.redis.expireat(fullKey, Math.floor(expireAt.getTime() / 1000));
      this.stats.sets++;
      return true;
    } catch (error) {
      console.error('Cache setExpireAt error:', error);
      this.stats.errors++;
      return false;
    }
  }

  /**
   * Get cache statistics
   */
  public getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Reset cache statistics
   */
  public resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      hitRate: 0,
    };
  }

  /**
   * Get Redis info
   */
  public async getRedisInfo(): Promise<any> {
    if (!this.isConnected) {
      return null;
    }

    try {
      const info = await this.redis.info();
      return this.parseRedisInfo(info);
    } catch (error) {
      console.error('Error getting Redis info:', error);
      return null;
    }
  }

  /**
   * Flush all cache
   */
  public async flushAll(): Promise<boolean> {
    if (!this.isConnected) {
      return false;
    }

    try {
      await this.redis.flushdb();
      this.resetStats();
      return true;
    } catch (error) {
      console.error('Cache flush error:', error);
      this.stats.errors++;
      return false;
    }
  }

  private buildKey(key: string, prefix?: string): string {
    const appPrefix = process.env.CACHE_PREFIX || 'nyotabalance';
    const fullPrefix = prefix ? `${appPrefix}:${prefix}` : appPrefix;
    return `${fullPrefix}:${key}`;
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  private parseRedisInfo(info: string): any {
    const lines = info.split('\r\n');
    const result: any = {};
    let section = '';

    for (const line of lines) {
      if (line.startsWith('#')) {
        section = line.substring(2).toLowerCase();
        result[section] = {};
      } else if (line.includes(':')) {
        const [key, value] = line.split(':');
        if (section) {
          result[section][key] = isNaN(Number(value)) ? value : Number(value);
        }
      }
    }

    return result;
  }
}

// Cache key builders for different data types
export class CacheKeys {
  static transaction(companyId: string, transactionId: string): string {
    return `transaction:${companyId}:${transactionId}`;
  }

  static transactionList(companyId: string, filters: string): string {
    return `transactions:${companyId}:${filters}`;
  }

  static account(companyId: string, accountId: string): string {
    return `account:${companyId}:${accountId}`;
  }

  static accountList(companyId: string): string {
    return `accounts:${companyId}`;
  }

  static bankAccount(companyId: string, bankAccountId: string): string {
    return `bank_account:${companyId}:${bankAccountId}`;
  }

  static bankTransactions(companyId: string, bankAccountId: string, filters: string): string {
    return `bank_transactions:${companyId}:${bankAccountId}:${filters}`;
  }

  static template(companyId: string, templateId: string): string {
    return `template:${companyId}:${templateId}`;
  }

  static templateList(companyId: string): string {
    return `templates:${companyId}`;
  }

  static report(companyId: string, reportId: string, params: string): string {
    return `report:${companyId}:${reportId}:${params}`;
  }

  static userSession(userId: string): string {
    return `session:${userId}`;
  }

  static companySettings(companyId: string): string {
    return `company:${companyId}:settings`;
  }

  static rateLimit(identifier: string): string {
    return `rate_limit:${identifier}`;
  }
}

// Create singleton instance
  // Business-specific cache methods for accounting system

  // Financial Reports Caching
  async cacheFinancialReport(companyId: string, reportType: string, params: any, data: any): Promise<void> {
    const key = this.buildCacheKey('financial_reports', companyId, reportType, this.hashParams(params));
    await this.set(key, data, { ttl: 3600 }); // 1 hour TTL
  }

  async getFinancialReport(companyId: string, reportType: string, params: any): Promise<any | null> {
    const key = this.buildCacheKey('financial_reports', companyId, reportType, this.hashParams(params));
    return await this.get(key);
  }

  async invalidateFinancialReports(companyId: string): Promise<void> {
    const pattern = this.buildCacheKey('financial_reports', companyId, '*');
    await this.deletePattern(pattern);
  }

  // Analytics Caching
  async cacheAnalytics(companyId: string, analyticsType: string, params: any, data: any): Promise<void> {
    const key = this.buildCacheKey('analytics', companyId, analyticsType, this.hashParams(params));
    await this.set(key, data, { ttl: 1800 }); // 30 minutes TTL
  }

  async getAnalytics(companyId: string, analyticsType: string, params: any): Promise<any | null> {
    const key = this.buildCacheKey('analytics', companyId, analyticsType, this.hashParams(params));
    return await this.get(key);
  }

  async invalidateAnalytics(companyId: string): Promise<void> {
    const pattern = this.buildCacheKey('analytics', companyId, '*');
    await this.deletePattern(pattern);
  }

  // Exchange Rates Caching
  async cacheExchangeRates(rates: any): Promise<void> {
    const key = this.buildCacheKey('exchange_rates', 'current');
    await this.set(key, rates, { ttl: 300 }); // 5 minutes TTL
  }

  async getExchangeRates(): Promise<any | null> {
    const key = this.buildCacheKey('exchange_rates', 'current');
    return await this.get(key);
  }

  // User Permissions Caching
  async cacheUserPermissions(userId: string, companyId: string, permissions: string[]): Promise<void> {
    const key = this.buildCacheKey('user_permissions', userId, companyId);
    await this.set(key, permissions, { ttl: 1800 }); // 30 minutes TTL
  }

  async getUserPermissions(userId: string, companyId: string): Promise<string[] | null> {
    const key = this.buildCacheKey('user_permissions', userId, companyId);
    return await this.get(key);
  }

  async invalidateUserPermissions(userId: string): Promise<void> {
    const pattern = this.buildCacheKey('user_permissions', userId, '*');
    await this.deletePattern(pattern);
  }

  // Account Balances Caching
  async cacheAccountBalances(companyId: string, balances: any): Promise<void> {
    const key = this.buildCacheKey('account_balances', companyId);
    await this.set(key, balances, { ttl: 600 }); // 10 minutes TTL
  }

  async getAccountBalances(companyId: string): Promise<any | null> {
    const key = this.buildCacheKey('account_balances', companyId);
    return await this.get(key);
  }

  async invalidateAccountBalances(companyId: string): Promise<void> {
    const key = this.buildCacheKey('account_balances', companyId);
    await this.delete(key);
  }

  // Dashboard Data Caching
  async cacheDashboardData(companyId: string, userId: string, data: any): Promise<void> {
    const key = this.buildCacheKey('dashboard', companyId, userId);
    await this.set(key, data, { ttl: 300 }); // 5 minutes TTL
  }

  async getDashboardData(companyId: string, userId: string): Promise<any | null> {
    const key = this.buildCacheKey('dashboard', companyId, userId);
    return await this.get(key);
  }

  // Custom Reports Caching
  async cacheCustomReport(reportId: string, params: any, data: any): Promise<void> {
    const key = this.buildCacheKey('custom_reports', reportId, this.hashParams(params));
    await this.set(key, data, { ttl: 3600 }); // 1 hour TTL
  }

  async getCustomReport(reportId: string, params: any): Promise<any | null> {
    const key = this.buildCacheKey('custom_reports', reportId, this.hashParams(params));
    return await this.get(key);
  }

  // Budget Data Caching
  async cacheBudgetData(companyId: string, budgetId: string, data: any): Promise<void> {
    const key = this.buildCacheKey('budgets', companyId, budgetId);
    await this.set(key, data, { ttl: 3600 }); // 1 hour TTL
  }

  async getBudgetData(companyId: string, budgetId: string): Promise<any | null> {
    const key = this.buildCacheKey('budgets', companyId, budgetId);
    return await this.get(key);
  }

  async invalidateBudgetData(companyId: string): Promise<void> {
    const pattern = this.buildCacheKey('budgets', companyId, '*');
    await this.deletePattern(pattern);
  }

  // Tax Rates Caching
  async cacheTaxRates(companyId: string, rates: any): Promise<void> {
    const key = this.buildCacheKey('tax_rates', companyId);
    await this.set(key, rates, { ttl: 86400 }); // 24 hours TTL
  }

  async getTaxRates(companyId: string): Promise<any | null> {
    const key = this.buildCacheKey('tax_rates', companyId);
    return await this.get(key);
  }

  // Reconciliation Data Caching
  async cacheReconciliationData(companyId: string, accountId: string, data: any): Promise<void> {
    const key = this.buildCacheKey('reconciliation', companyId, accountId);
    await this.set(key, data, { ttl: 1800 }); // 30 minutes TTL
  }

  async getReconciliationData(companyId: string, accountId: string): Promise<any | null> {
    const key = this.buildCacheKey('reconciliation', companyId, accountId);
    return await this.get(key);
  }

  // Company-wide cache invalidation
  async invalidateCompanyCache(companyId: string): Promise<void> {
    const patterns = [
      this.buildCacheKey('financial_reports', companyId, '*'),
      this.buildCacheKey('analytics', companyId, '*'),
      this.buildCacheKey('account_balances', companyId),
      this.buildCacheKey('dashboard', companyId, '*'),
      this.buildCacheKey('budgets', companyId, '*'),
      this.buildCacheKey('tax_rates', companyId),
      this.buildCacheKey('reconciliation', companyId, '*')
    ];

    for (const pattern of patterns) {
      await this.deletePattern(pattern);
    }
  }

  // Utility methods
  private buildCacheKey(...parts: string[]): string {
    return parts.filter(Boolean).join(':');
  }

  private hashParams(params: any): string {
    if (!params || typeof params !== 'object') return 'default';

    const crypto = require('crypto');
    const sorted = JSON.stringify(params, Object.keys(params).sort());
    return crypto.createHash('md5').update(sorted).digest('hex').substring(0, 8);
  }
}

export const cacheService = new CacheService();

// Cache decorators for easy use
export function Cacheable(ttl: number = 3600, prefix?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const cacheKey = `${target.constructor.name}:${propertyName}:${JSON.stringify(args)}`;
      
      return await cacheService.getOrSet(
        cacheKey,
        () => method.apply(this, args),
        { ttl, prefix }
      );
    };

    return descriptor;
  };
}

export default cacheService;
