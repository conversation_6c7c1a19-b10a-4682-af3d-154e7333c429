import {
  PlaidApi,
  Configuration,
  PlaidEnvironments,
  CountryCode,
  Products,
  DepositoryAccountSubtype,
  CreditAccountSubtype
} from 'plaid';
import { db } from '../config/database';
import { v4 as uuidv4 } from 'uuid';

export interface BankAccount {
  id: string;
  companyId: string;
  bankName: string;
  accountName: string;
  accountNumber: string;
  routingNumber: string;
  accountType: 'CHECKING' | 'SAVINGS' | 'CREDIT_CARD' | 'INVESTMENT' | 'LOAN';
  balance: number;
  currency: string;
  isActive: boolean;
  plaidAccountId?: string;
  plaidItemId?: string;
  lastSyncAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  // Enhanced enterprise features
  autoReconciliation: boolean;
  reconciliationRules: ReconciliationRule[];
  syncFrequency: 'REAL_TIME' | 'HOURLY' | 'DAILY' | 'WEEKLY';
  alertSettings: BankAlertSettings;
}

export interface ReconciliationRule {
  id: string;
  name: string;
  conditions: Array<{
    field: string;
    operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'regex';
    value: string;
  }>;
  actions: Array<{
    type: 'AUTO_MATCH' | 'CREATE_TRANSACTION' | 'ASSIGN_CATEGORY' | 'SET_REFERENCE';
    config: Record<string, any>;
  }>;
  priority: number;
  isActive: boolean;
}

export interface BankAlertSettings {
  lowBalance: {
    enabled: boolean;
    threshold: number;
    recipients: string[];
  };
  largeTransaction: {
    enabled: boolean;
    threshold: number;
    recipients: string[];
  };
  failedSync: {
    enabled: boolean;
    recipients: string[];
  };
  duplicateTransaction: {
    enabled: boolean;
    recipients: string[];
  };
}

export interface BankTransaction {
  id: string;
  bankAccountId: string;
  companyId: string;
  transactionId: string; // External transaction ID
  amount: number;
  description: string;
  category: string;
  subcategory?: string;
  date: Date;
  pending: boolean;
  merchantName?: string;
  location?: {
    address?: string;
    city?: string;
    region?: string;
    postalCode?: string;
    country?: string;
  };
  paymentChannel: 'ONLINE' | 'IN_STORE' | 'ATM' | 'OTHER';
  accountOwner?: string;
  isReconciled: boolean;
  reconciledAt?: Date;
  reconciledBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PlaidLinkToken {
  linkToken: string;
  expiration: Date;
  requestId: string;
}

class BankIntegrationService {
  private plaidClient: PlaidApi;

  constructor() {
    const configuration = new Configuration({
      basePath: process.env.PLAID_ENV === 'production' 
        ? PlaidEnvironments.production 
        : PlaidEnvironments.sandbox,
      baseOptions: {
        headers: {
          'PLAID-CLIENT-ID': process.env.PLAID_CLIENT_ID!,
          'PLAID-SECRET': process.env.PLAID_SECRET!,
        },
      },
    });
    
    this.plaidClient = new PlaidApi(configuration);
  }

  /**
   * Create Plaid Link token for bank account connection
   */
  public async createLinkToken(
    companyId: string,
    userId: string,
    products: string[] = ['transactions', 'accounts']
  ): Promise<PlaidLinkToken> {
    try {
      const response = await this.plaidClient.linkTokenCreate({
        user: {
          client_user_id: userId,
        },
        client_name: 'NyotaBalance',
        products: products as Products[],
        country_codes: [CountryCode.Us],
        language: 'en',
        webhook: `${process.env.API_BASE_URL}/api/webhooks/plaid`,
        account_filters: {
          depository: {
            account_subtypes: [
              DepositoryAccountSubtype.Checking,
              DepositoryAccountSubtype.Savings
            ],
          },
          credit: {
            account_subtypes: [CreditAccountSubtype.CreditCard],
          },
        },
      });

      const linkToken = response.data.link_token;
      const expiration = new Date(response.data.expiration);

      // Store link token in database for tracking
      await db('plaid_link_tokens').insert({
        id: uuidv4(),
        company_id: companyId,
        user_id: userId,
        link_token: linkToken,
        expiration: expiration,
        request_id: response.data.request_id,
        created_at: new Date(),
      });

      return {
        linkToken,
        expiration,
        requestId: response.data.request_id,
      };
    } catch (error) {
      console.error('Failed to create Plaid link token:', error);
      throw new Error('Failed to create bank connection link');
    }
  }

  /**
   * Exchange public token for access token and connect bank account
   */
  public async connectBankAccount(
    companyId: string,
    userId: string,
    publicToken: string
  ): Promise<{ bankAccounts: BankAccount[]; itemId: string }> {
    try {
      // Exchange public token for access token
      const exchangeResponse = await this.plaidClient.itemPublicTokenExchange({
        public_token: publicToken,
      });

      const accessToken = exchangeResponse.data.access_token;
      const itemId = exchangeResponse.data.item_id;

      // Get account information
      const accountsResponse = await this.plaidClient.accountsGet({
        access_token: accessToken,
      });

      const accounts = accountsResponse.data.accounts;
      const bankAccounts: BankAccount[] = [];

      // Store access token and item information
      await db('plaid_items').insert({
        id: uuidv4(),
        company_id: companyId,
        user_id: userId,
        item_id: itemId,
        access_token: accessToken,
        institution_id: accountsResponse.data.item.institution_id,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      });

      // Create bank accounts in our system
      for (const account of accounts) {
        const bankAccountId = uuidv4();
        
        const bankAccount: BankAccount = {
          id: bankAccountId,
          companyId,
          bankName: account.name,
          accountName: account.official_name || account.name,
          accountNumber: account.mask || '',
          routingNumber: '', // Will be populated from auth endpoint if needed
          accountType: this.mapPlaidAccountType(account.type, account.subtype),
          balance: account.balances.current || 0,
          currency: account.balances.iso_currency_code || 'USD',
          isActive: true,
          plaidAccountId: account.account_id,
          plaidItemId: itemId,
          lastSyncAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        await db('bank_accounts').insert({
          id: bankAccount.id,
          company_id: bankAccount.companyId,
          bank_name: bankAccount.bankName,
          account_name: bankAccount.accountName,
          account_number: bankAccount.accountNumber,
          routing_number: bankAccount.routingNumber,
          account_type: bankAccount.accountType,
          balance: bankAccount.balance,
          currency: bankAccount.currency,
          is_active: bankAccount.isActive,
          plaid_account_id: bankAccount.plaidAccountId,
          plaid_item_id: bankAccount.plaidItemId,
          last_sync_at: bankAccount.lastSyncAt,
          created_at: bankAccount.createdAt,
          updated_at: bankAccount.updatedAt,
        });

        bankAccounts.push(bankAccount);
      }

      // Sync initial transactions
      await this.syncTransactions(companyId, accessToken, accounts);

      return { bankAccounts, itemId };
    } catch (error) {
      console.error('Failed to connect bank account:', error);
      throw new Error('Failed to connect bank account');
    }
  }

  /**
   * Sync transactions from Plaid
   */
  public async syncTransactions(
    companyId: string,
    accessToken?: string,
    _accounts?: any[]
  ): Promise<{ newTransactions: number; updatedTransactions: number }> {
    try {
      let plaidAccessToken = accessToken;

      if (!plaidAccessToken) {
        // Get access token from database
        const plaidItem = await db('plaid_items')
          .where('company_id', companyId)
          .where('is_active', true)
          .first();

        if (!plaidItem) {
          throw new Error('No active Plaid connection found');
        }

        plaidAccessToken = plaidItem.access_token;
      }

      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30); // Last 30 days

      // Get transactions from Plaid
      const transactionsResponse = await this.plaidClient.transactionsGet({
        access_token: plaidAccessToken!,
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0],
        options: {
          count: 500,
        },
      });

      const transactions = transactionsResponse.data.transactions;
      let newTransactions = 0;
      let updatedTransactions = 0;

      for (const transaction of transactions) {
        // Check if transaction already exists
        const existingTransaction = await db('bank_transactions')
          .where('transaction_id', transaction.transaction_id)
          .first();

        // Get our bank account ID
        const bankAccount = await db('bank_accounts')
          .where('plaid_account_id', transaction.account_id)
          .where('company_id', companyId)
          .first();

        if (!bankAccount) {
          continue; // Skip if we don't have this account
        }

        const bankTransaction: any = {
          bank_account_id: bankAccount.id,
          company_id: companyId,
          transaction_id: transaction.transaction_id,
          amount: -transaction.amount, // Plaid uses negative for outflows
          description: transaction.name,
          category: transaction.personal_finance_category?.primary || 'Other',
          subcategory: transaction.personal_finance_category?.detailed || null,
          date: new Date(transaction.date),
          pending: transaction.pending,
          merchant_name: transaction.merchant_name,
          location: transaction.location ? JSON.stringify({
            address: transaction.location.address,
            city: transaction.location.city,
            region: transaction.location.region,
            postalCode: transaction.location.postal_code,
            country: transaction.location.country,
          }) : null,
          payment_channel: this.mapPaymentChannel(transaction.payment_channel),
          account_owner: transaction.account_owner,
          is_reconciled: false,
          updated_at: new Date(),
        };

        if (existingTransaction) {
          // Update existing transaction
          await db('bank_transactions')
            .where('id', existingTransaction.id)
            .update(bankTransaction);
          updatedTransactions++;
        } else {
          // Insert new transaction
          await db('bank_transactions').insert({
            id: uuidv4(),
            ...bankTransaction,
            created_at: new Date(),
          });
          newTransactions++;
        }
      }

      // Update last sync time
      await db('bank_accounts')
        .where('company_id', companyId)
        .update({
          last_sync_at: new Date(),
          updated_at: new Date(),
        });

      return { newTransactions, updatedTransactions };
    } catch (error) {
      console.error('Failed to sync transactions:', error);
      throw new Error('Failed to sync bank transactions');
    }
  }

  /**
   * Get bank accounts for a company
   */
  public async getBankAccounts(companyId: string): Promise<BankAccount[]> {
    const accounts = await db('bank_accounts')
      .where('company_id', companyId)
      .where('is_active', true)
      .orderBy('created_at', 'desc');

    return accounts.map(account => ({
      id: account.id,
      companyId: account.company_id,
      bankName: account.bank_name,
      accountName: account.account_name,
      accountNumber: account.account_number,
      routingNumber: account.routing_number,
      accountType: account.account_type,
      balance: parseFloat(account.balance),
      currency: account.currency,
      isActive: account.is_active,
      plaidAccountId: account.plaid_account_id,
      plaidItemId: account.plaid_item_id,
      lastSyncAt: account.last_sync_at,
      createdAt: account.created_at,
      updatedAt: account.updated_at,
    }));
  }

  /**
   * Get bank transactions for a company
   */
  public async getBankTransactions(
    companyId: string,
    options: {
      bankAccountId?: string;
      startDate?: Date;
      endDate?: Date;
      category?: string;
      reconciled?: boolean;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{
    transactions: BankTransaction[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const {
      bankAccountId,
      startDate,
      endDate,
      category,
      reconciled,
      page = 1,
      limit = 50,
    } = options;

    let query = db('bank_transactions')
      .where('company_id', companyId);

    if (bankAccountId) {
      query = query.where('bank_account_id', bankAccountId);
    }

    if (startDate) {
      query = query.where('date', '>=', startDate);
    }

    if (endDate) {
      query = query.where('date', '<=', endDate);
    }

    if (category) {
      query = query.where('category', category);
    }

    if (reconciled !== undefined) {
      query = query.where('is_reconciled', reconciled);
    }

    const totalCount = await query.clone().count('* as count').first();
    const total = parseInt(totalCount?.count as string) || 0;

    const offset = (page - 1) * limit;
    const transactions = await query
      .orderBy('date', 'desc')
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return {
      transactions: transactions.map(this.mapDatabaseTransactionToModel),
      total,
      page,
      totalPages,
    };
  }

  /**
   * Reconcile bank transaction
   */
  public async reconcileTransaction(
    transactionId: string,
    userId: string,
    accountingTransactionId?: string
  ): Promise<void> {
    await db('bank_transactions')
      .where('id', transactionId)
      .update({
        is_reconciled: true,
        reconciled_at: new Date(),
        reconciled_by: userId,
        accounting_transaction_id: accountingTransactionId,
        updated_at: new Date(),
      });
  }

  /**
   * Disconnect bank account
   */
  public async disconnectBankAccount(
    companyId: string,
    bankAccountId: string
  ): Promise<void> {
    // Deactivate bank account
    await db('bank_accounts')
      .where('id', bankAccountId)
      .where('company_id', companyId)
      .update({
        is_active: false,
        updated_at: new Date(),
      });

    // Deactivate Plaid item if no other accounts are using it
    const bankAccount = await db('bank_accounts')
      .where('id', bankAccountId)
      .first();

    if (bankAccount?.plaid_item_id) {
      const otherAccounts = await db('bank_accounts')
        .where('plaid_item_id', bankAccount.plaid_item_id)
        .where('is_active', true)
        .where('id', '!=', bankAccountId);

      if (otherAccounts.length === 0) {
        await db('plaid_items')
          .where('item_id', bankAccount.plaid_item_id)
          .update({
            is_active: false,
            updated_at: new Date(),
          });
      }
    }
  }

  /**
   * Handle Plaid webhook
   */
  public async handleWebhook(webhookData: any): Promise<void> {
    const { webhook_type, webhook_code, item_id } = webhookData;

    switch (webhook_type) {
      case 'TRANSACTIONS':
        if (webhook_code === 'DEFAULT_UPDATE') {
          // New transactions available
          await this.handleTransactionUpdate(item_id);
        }
        break;
      case 'ITEM':
        if (webhook_code === 'ERROR') {
          // Item error - mark as inactive
          await this.handleItemError(item_id, webhookData.error);
        }
        break;
      default:
        console.log('Unhandled webhook type:', webhook_type);
    }
  }

  /**
   * Create or update reconciliation rule
   */
  public async createReconciliationRule(
    companyId: string,
    bankAccountId: string,
    rule: Omit<ReconciliationRule, 'id'>
  ): Promise<ReconciliationRule> {
    const ruleId = uuidv4();
    const newRule: ReconciliationRule = {
      id: ruleId,
      ...rule,
    };

    await db('bank_reconciliation_rules').insert({
      id: ruleId,
      company_id: companyId,
      bank_account_id: bankAccountId,
      name: rule.name,
      conditions: JSON.stringify(rule.conditions),
      actions: JSON.stringify(rule.actions),
      priority: rule.priority,
      is_active: rule.isActive,
      created_at: new Date(),
      updated_at: new Date(),
    });

    return newRule;
  }

  /**
   * Apply reconciliation rules to transactions
   */
  public async applyReconciliationRules(
    companyId: string,
    bankAccountId?: string
  ): Promise<{ processedTransactions: number; autoMatched: number; rulesApplied: number }> {
    try {
      // Get active reconciliation rules
      let rulesQuery = db('bank_reconciliation_rules')
        .where('company_id', companyId)
        .where('is_active', true)
        .orderBy('priority', 'asc');

      if (bankAccountId) {
        rulesQuery = rulesQuery.where('bank_account_id', bankAccountId);
      }

      const rules = await rulesQuery;

      // Get unreconciled transactions
      let transactionsQuery = db('bank_transactions')
        .where('company_id', companyId)
        .where('is_reconciled', false);

      if (bankAccountId) {
        transactionsQuery = transactionsQuery.where('bank_account_id', bankAccountId);
      }

      const transactions = await transactionsQuery;

      let processedTransactions = 0;
      let autoMatched = 0;
      let rulesApplied = 0;

      for (const transaction of transactions) {
        for (const rule of rules) {
          const conditions = JSON.parse(rule.conditions);
          const actions = JSON.parse(rule.actions);

          // Check if rule conditions match
          if (this.evaluateRuleConditions(transaction, conditions)) {
            // Apply rule actions
            await this.applyRuleActions(transaction, actions);
            rulesApplied++;

            // Check if this resulted in auto-matching
            if (actions.some((action: any) => action.type === 'AUTO_MATCH')) {
              autoMatched++;
            }
            break; // Apply only the first matching rule
          }
        }
        processedTransactions++;
      }

      return { processedTransactions, autoMatched, rulesApplied };
    } catch (error) {
      console.error('Failed to apply reconciliation rules:', error);
      throw new Error('Failed to apply reconciliation rules');
    }
  }

  /**
   * Set up bank alerts
   */
  public async updateBankAlerts(
    companyId: string,
    bankAccountId: string,
    alertSettings: BankAlertSettings
  ): Promise<void> {
    await db('bank_accounts')
      .where('id', bankAccountId)
      .where('company_id', companyId)
      .update({
        alert_settings: JSON.stringify(alertSettings),
        updated_at: new Date(),
      });
  }

  /**
   * Check and send alerts
   */
  public async checkAndSendAlerts(companyId: string): Promise<void> {
    try {
      const bankAccounts = await this.getBankAccounts(companyId);

      for (const account of bankAccounts) {
        if (!account.alertSettings) continue;

        // Check low balance alert
        if (account.alertSettings.lowBalance.enabled &&
            account.balance < account.alertSettings.lowBalance.threshold) {
          await this.sendAlert('LOW_BALANCE', account, {
            balance: account.balance,
            threshold: account.alertSettings.lowBalance.threshold,
          });
        }

        // Check for large transactions in the last hour
        if (account.alertSettings.largeTransaction.enabled) {
          const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
          const largeTransactions = await db('bank_transactions')
            .where('bank_account_id', account.id)
            .where('created_at', '>=', oneHourAgo)
            .where('amount', '>', account.alertSettings.largeTransaction.threshold);

          for (const transaction of largeTransactions) {
            await this.sendAlert('LARGE_TRANSACTION', account, {
              transaction: this.mapDatabaseTransactionToModel(transaction),
              threshold: account.alertSettings.largeTransaction.threshold,
            });
          }
        }
      }
    } catch (error) {
      console.error('Failed to check and send alerts:', error);
    }
  }

  /**
   * Bulk reconciliation
   */
  public async bulkReconcile(
    companyId: string,
    reconciliations: Array<{
      bankTransactionId: string;
      accountingTransactionId?: string;
      action: 'MATCH' | 'CREATE' | 'IGNORE';
    }>,
    userId: string
  ): Promise<{ processed: number; matched: number; created: number; ignored: number }> {
    let processed = 0;
    let matched = 0;
    let created = 0;
    let ignored = 0;

    for (const reconciliation of reconciliations) {
      try {
        switch (reconciliation.action) {
          case 'MATCH':
            await this.reconcileTransaction(
              reconciliation.bankTransactionId,
              userId,
              reconciliation.accountingTransactionId
            );
            matched++;
            break;

          case 'CREATE':
            // Create accounting transaction from bank transaction
            const bankTransaction = await db('bank_transactions')
              .where('id', reconciliation.bankTransactionId)
              .first();

            if (bankTransaction) {
              // This would integrate with your transaction service
              // await transactionService.createFromBankTransaction(bankTransaction, userId);
              created++;
            }
            break;

          case 'IGNORE':
            await db('bank_transactions')
              .where('id', reconciliation.bankTransactionId)
              .update({
                is_reconciled: true,
                reconciled_at: new Date(),
                reconciled_by: userId,
                reconciliation_status: 'IGNORED',
                updated_at: new Date(),
              });
            ignored++;
            break;
        }
        processed++;
      } catch (error) {
        console.error(`Failed to process reconciliation for ${reconciliation.bankTransactionId}:`, error);
      }
    }

    return { processed, matched, created, ignored };
  }

  // Private helper methods
  private mapPlaidAccountType(type: string, subtype?: string): BankAccount['accountType'] {
    switch (type) {
      case 'depository':
        return subtype === 'savings' ? 'SAVINGS' : 'CHECKING';
      case 'credit':
        return 'CREDIT_CARD';
      case 'investment':
        return 'INVESTMENT';
      case 'loan':
        return 'LOAN';
      default:
        return 'CHECKING';
    }
  }

  private mapPaymentChannel(channel?: string): BankTransaction['paymentChannel'] {
    switch (channel) {
      case 'online':
        return 'ONLINE';
      case 'in store':
        return 'IN_STORE';
      case 'atm':
        return 'ATM';
      default:
        return 'OTHER';
    }
  }

  private mapDatabaseTransactionToModel(dbTransaction: any): BankTransaction {
    return {
      id: dbTransaction.id,
      bankAccountId: dbTransaction.bank_account_id,
      companyId: dbTransaction.company_id,
      transactionId: dbTransaction.transaction_id,
      amount: parseFloat(dbTransaction.amount),
      description: dbTransaction.description,
      category: dbTransaction.category,
      subcategory: dbTransaction.subcategory,
      date: dbTransaction.date,
      pending: dbTransaction.pending,
      merchantName: dbTransaction.merchant_name,
      location: dbTransaction.location ? JSON.parse(dbTransaction.location) : undefined,
      paymentChannel: dbTransaction.payment_channel,
      accountOwner: dbTransaction.account_owner,
      isReconciled: dbTransaction.is_reconciled,
      reconciledAt: dbTransaction.reconciled_at,
      reconciledBy: dbTransaction.reconciled_by,
      createdAt: dbTransaction.created_at,
      updatedAt: dbTransaction.updated_at,
    };
  }

  private async handleTransactionUpdate(itemId: string): Promise<void> {
    // Get company ID from item
    const plaidItem = await db('plaid_items')
      .where('item_id', itemId)
      .where('is_active', true)
      .first();

    if (plaidItem) {
      await this.syncTransactions(plaidItem.company_id, plaidItem.access_token);
    }
  }

  private async handleItemError(itemId: string, error: any): Promise<void> {
    console.error('Plaid item error:', error);

    // Mark item as inactive
    await db('plaid_items')
      .where('item_id', itemId)
      .update({
        is_active: false,
        error_code: error.error_code,
        error_message: error.error_message,
        updated_at: new Date(),
      });

    // Mark associated bank accounts as inactive
    await db('bank_accounts')
      .where('plaid_item_id', itemId)
      .update({
        is_active: false,
        updated_at: new Date(),
      });
  }

  private evaluateRuleConditions(transaction: any, conditions: any[]): boolean {
    return conditions.every(condition => {
      const fieldValue = transaction[condition.field]?.toString().toLowerCase() || '';
      const conditionValue = condition.value.toLowerCase();

      switch (condition.operator) {
        case 'equals':
          return fieldValue === conditionValue;
        case 'contains':
          return fieldValue.includes(conditionValue);
        case 'starts_with':
          return fieldValue.startsWith(conditionValue);
        case 'ends_with':
          return fieldValue.endsWith(conditionValue);
        case 'regex':
          try {
            const regex = new RegExp(condition.value, 'i');
            return regex.test(fieldValue);
          } catch {
            return false;
          }
        default:
          return false;
      }
    });
  }

  private async applyRuleActions(transaction: any, actions: any[]): Promise<void> {
    for (const action of actions) {
      switch (action.type) {
        case 'AUTO_MATCH':
          // Find matching accounting transaction
          const matchingTransaction = await this.findMatchingAccountingTransaction(
            transaction,
            action.config
          );
          if (matchingTransaction) {
            await this.reconcileTransaction(transaction.id, 'system', matchingTransaction.id);
          }
          break;

        case 'ASSIGN_CATEGORY':
          await db('bank_transactions')
            .where('id', transaction.id)
            .update({
              category: action.config.category,
              subcategory: action.config.subcategory,
              updated_at: new Date(),
            });
          break;

        case 'SET_REFERENCE':
          await db('bank_transactions')
            .where('id', transaction.id)
            .update({
              reference: action.config.reference,
              updated_at: new Date(),
            });
          break;
      }
    }
  }

  private async findMatchingAccountingTransaction(bankTransaction: any, config: any): Promise<any> {
    // This would implement sophisticated matching logic
    // For now, simple amount and date matching
    const matchWindow = config.matchWindow || 3; // days
    const startDate = new Date(bankTransaction.date);
    startDate.setDate(startDate.getDate() - matchWindow);
    const endDate = new Date(bankTransaction.date);
    endDate.setDate(endDate.getDate() + matchWindow);

    return await db('transactions')
      .where('company_id', bankTransaction.company_id)
      .where('total_amount', Math.abs(bankTransaction.amount))
      .whereBetween('transaction_date', [startDate, endDate])
      .where('status', 'POSTED')
      .whereNull('bank_transaction_id') // Not already reconciled
      .first();
  }

  private async sendAlert(type: string, account: any, data: any): Promise<void> {
    // This would integrate with your notification service
    console.log(`Alert: ${type} for account ${account.id}`, data);

    // Store alert in database
    await db('bank_alerts').insert({
      id: uuidv4(),
      company_id: account.company_id,
      bank_account_id: account.id,
      alert_type: type,
      alert_data: JSON.stringify(data),
      is_read: false,
      created_at: new Date(),
    });
  }
}

export const bankIntegrationService = new BankIntegrationService();
