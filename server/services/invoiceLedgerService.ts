import { db } from "../config/database";
import { v4 as uuidv4 } from "uuid";

export interface InvoiceJournalEntry {
  invoiceId: string;
  invoiceNumber: string;
  invoiceType: 'SALES' | 'PURCHASE';
  totalAmount: number;
  contactId: string;
  invoiceDate: string;
  companyId: string;
  userId: string;
}

export interface PaymentJournalEntry {
  invoiceId: string;
  invoiceNumber: string;
  invoiceType: 'SALES' | 'PURCHASE';
  paidAmount: number;
  paymentDate: string;
  companyId: string;
  userId: string;
}

class InvoiceLedgerService {
  /**
   * Create journal entry when invoice is sent (revenue/expense recognition)
   */
  public async createInvoiceJournalEntry(data: InvoiceJournalEntry): Promise<string> {
    const transactionId = uuidv4();
    
    try {
      await db.transaction(async (trx) => {
        // Get default accounts for the company
        const accounts = await this.getDefaultAccounts(data.companyId, trx);
        
        // Generate transaction number
        const transactionNumber = await this.generateTransactionNumber(data.companyId, trx);
        
        // Determine journal entry based on invoice type
        let entries;
        let description;
        
        if (data.invoiceType === 'SALES') {
          // Sales Invoice: Dr. A/R, Cr. Revenue
          description = `Sales Invoice ${data.invoiceNumber} - Revenue Recognition`;
          entries = [
            {
              accountId: accounts.accountsReceivable,
              description: `A/R for Invoice ${data.invoiceNumber}`,
              debitAmount: data.totalAmount,
              creditAmount: 0
            },
            {
              accountId: accounts.salesRevenue,
              description: `Revenue from Invoice ${data.invoiceNumber}`,
              debitAmount: 0,
              creditAmount: data.totalAmount
            }
          ];
        } else {
          // Purchase Invoice: Dr. Expense/Asset, Cr. A/P
          description = `Purchase Invoice ${data.invoiceNumber} - Expense Recognition`;
          entries = [
            {
              accountId: accounts.operatingExpense,
              description: `Expense from Invoice ${data.invoiceNumber}`,
              debitAmount: data.totalAmount,
              creditAmount: 0
            },
            {
              accountId: accounts.accountsPayable,
              description: `A/P for Invoice ${data.invoiceNumber}`,
              debitAmount: 0,
              creditAmount: data.totalAmount
            }
          ];
        }
        
        // Create transaction record
        await trx("transactions").insert({
          id: transactionId,
          company_id: data.companyId,
          transaction_number: transactionNumber,
          transaction_date: data.invoiceDate,
          description,
          reference: `INV-${data.invoiceNumber}`,
          status: 'POSTED', // Auto-post invoice transactions
          total_amount: data.totalAmount,
          created_by: data.userId,
        });
        
        // Create transaction entries
        const entryInserts = entries.map((entry, index) => ({
          id: uuidv4(),
          transaction_id: transactionId,
          line_number: index + 1,
          account_id: entry.accountId,
          description: entry.description,
          debit_amount: entry.debitAmount,
          credit_amount: entry.creditAmount,
        }));
        
        await trx("transaction_entries").insert(entryInserts);
        
        // Update invoice with transaction reference
        await trx("invoices")
          .where("id", data.invoiceId)
          .update({
            transaction_id: transactionId,
            updated_at: new Date()
          });
      });
      
      return transactionId;
    } catch (error) {
      console.error("Failed to create invoice journal entry:", error);
      throw new Error("Failed to create journal entry for invoice");
    }
  }
  
  /**
   * Create journal entry when invoice payment is received
   */
  public async createPaymentJournalEntry(data: PaymentJournalEntry): Promise<string> {
    const transactionId = uuidv4();
    
    try {
      await db.transaction(async (trx) => {
        // Get default accounts for the company
        const accounts = await this.getDefaultAccounts(data.companyId, trx);
        
        // Generate transaction number
        const transactionNumber = await this.generateTransactionNumber(data.companyId, trx);
        
        // Determine journal entry based on invoice type
        let entries;
        let description;
        
        if (data.invoiceType === 'SALES') {
          // Sales Payment: Dr. Cash, Cr. A/R
          description = `Payment received for Invoice ${data.invoiceNumber}`;
          entries = [
            {
              accountId: accounts.cash,
              description: `Cash received for Invoice ${data.invoiceNumber}`,
              debitAmount: data.paidAmount,
              creditAmount: 0
            },
            {
              accountId: accounts.accountsReceivable,
              description: `A/R payment for Invoice ${data.invoiceNumber}`,
              debitAmount: 0,
              creditAmount: data.paidAmount
            }
          ];
        } else {
          // Purchase Payment: Dr. A/P, Cr. Cash
          description = `Payment made for Invoice ${data.invoiceNumber}`;
          entries = [
            {
              accountId: accounts.accountsPayable,
              description: `A/P payment for Invoice ${data.invoiceNumber}`,
              debitAmount: data.paidAmount,
              creditAmount: 0
            },
            {
              accountId: accounts.cash,
              description: `Cash paid for Invoice ${data.invoiceNumber}`,
              debitAmount: 0,
              creditAmount: data.paidAmount
            }
          ];
        }
        
        // Create transaction record
        await trx("transactions").insert({
          id: transactionId,
          company_id: data.companyId,
          transaction_number: transactionNumber,
          transaction_date: data.paymentDate,
          description,
          reference: `PAY-${data.invoiceNumber}`,
          status: 'POSTED', // Auto-post payment transactions
          total_amount: data.paidAmount,
          created_by: data.userId,
        });
        
        // Create transaction entries
        const entryInserts = entries.map((entry, index) => ({
          id: uuidv4(),
          transaction_id: transactionId,
          line_number: index + 1,
          account_id: entry.accountId,
          description: entry.description,
          debit_amount: entry.debitAmount,
          credit_amount: entry.creditAmount,
        }));
        
        await trx("transaction_entries").insert(entryInserts);
      });
      
      return transactionId;
    } catch (error) {
      console.error("Failed to create payment journal entry:", error);
      throw new Error("Failed to create journal entry for payment");
    }
  }
  
  /**
   * Get or create default accounts for invoice transactions
   */
  private async getDefaultAccounts(companyId: string, trx: any) {
    // Try to find existing accounts
    const accounts = await trx("accounts")
      .where("company_id", companyId)
      .where("is_active", true)
      .select("id", "code", "account_type");
    
    const accountMap: any = {};
    
    // Map existing accounts
    accounts.forEach((account: any) => {
      if (account.code === '1200' || account.account_type === 'ASSET') {
        if (account.code === '1200') accountMap.accountsReceivable = account.id;
        if (account.code === '1000' || account.code === '1001') accountMap.cash = account.id;
      }
      if (account.code === '2000' || account.account_type === 'LIABILITY') {
        if (account.code === '2000') accountMap.accountsPayable = account.id;
      }
      if (account.code === '4000' || account.account_type === 'REVENUE') {
        if (account.code === '4000') accountMap.salesRevenue = account.id;
      }
      if (account.code === '5000' || account.account_type === 'EXPENSE') {
        if (account.code === '5000') accountMap.operatingExpense = account.id;
      }
    });
    
    // Use first available accounts if specific codes not found
    if (!accountMap.cash) {
      const cashAccount = accounts.find((a: any) => a.account_type === 'ASSET');
      accountMap.cash = cashAccount?.id;
    }
    
    if (!accountMap.accountsReceivable) {
      const arAccount = accounts.find((a: any) => a.account_type === 'ASSET' && a.id !== accountMap.cash);
      accountMap.accountsReceivable = arAccount?.id || accountMap.cash;
    }
    
    if (!accountMap.accountsPayable) {
      const apAccount = accounts.find((a: any) => a.account_type === 'LIABILITY');
      accountMap.accountsPayable = apAccount?.id;
    }
    
    if (!accountMap.salesRevenue) {
      const revenueAccount = accounts.find((a: any) => a.account_type === 'REVENUE');
      accountMap.salesRevenue = revenueAccount?.id;
    }
    
    if (!accountMap.operatingExpense) {
      const expenseAccount = accounts.find((a: any) => a.account_type === 'EXPENSE');
      accountMap.operatingExpense = expenseAccount?.id;
    }
    
    return accountMap;
  }
  
  /**
   * Generate transaction number
   */
  private async generateTransactionNumber(companyId: string, trx: any): Promise<string> {
    const lastTransaction = await trx("transactions")
      .where("company_id", companyId)
      .orderBy("created_at", "desc")
      .first();
    
    const lastNumber = lastTransaction?.transaction_number
      ? parseInt(lastTransaction.transaction_number.replace(/\D/g, ""))
      : 0;
    
    return `TXN-${String(lastNumber + 1).padStart(6, "0")}`;
  }
  
  /**
   * Reverse invoice journal entry (for cancelled invoices)
   */
  public async reverseInvoiceJournalEntry(invoiceId: string, userId: string): Promise<void> {
    try {
      await db.transaction(async (trx) => {
        // Find the original transaction
        const invoice = await trx("invoices")
          .where("id", invoiceId)
          .first();
        
        if (!invoice?.transaction_id) {
          return; // No transaction to reverse
        }
        
        // Get original transaction entries
        const originalEntries = await trx("transaction_entries")
          .where("transaction_id", invoice.transaction_id)
          .select("*");
        
        if (originalEntries.length === 0) {
          return;
        }
        
        // Create reversal transaction
        const reversalId = uuidv4();
        const transactionNumber = await this.generateTransactionNumber(invoice.company_id, trx);
        
        await trx("transactions").insert({
          id: reversalId,
          company_id: invoice.company_id,
          transaction_number: transactionNumber,
          transaction_date: new Date().toISOString().split('T')[0],
          description: `Reversal of Invoice ${invoice.invoice_number}`,
          reference: `REV-${invoice.invoice_number}`,
          status: 'POSTED',
          total_amount: invoice.total_amount,
          created_by: userId,
        });
        
        // Create reversal entries (swap debits and credits)
        const reversalEntries = originalEntries.map((entry: any, index: number) => ({
          id: uuidv4(),
          transaction_id: reversalId,
          line_number: index + 1,
          account_id: entry.account_id,
          description: `Reversal: ${entry.description}`,
          debit_amount: entry.credit_amount, // Swap
          credit_amount: entry.debit_amount, // Swap
        }));
        
        await trx("transaction_entries").insert(reversalEntries);
      });
    } catch (error) {
      console.error("Failed to reverse invoice journal entry:", error);
      throw new Error("Failed to reverse journal entry");
    }
  }
}

export const invoiceLedgerService = new InvoiceLedgerService();
