import { db } from '../config/database';
import type { 
  BankReconciliation, 
  BankReconciliationItem, 
  ReconciliationMatch,
  ReconciliationRule,
  ReconciliationFilters,
  ReconciliationSummary,
  BulkReconciliationAction
} from '../types/bankReconciliation';

class BankReconciliationService {
  /**
   * Create a new bank reconciliation
   */
  async createReconciliation(data: {
    companyId: string;
    bankAccountId: string;
    statementDate: string;
    statementBeginningBalance: number;
    statementEndingBalance: number;
    reconciledBy: string;
  }): Promise<BankReconciliation> {
    const [reconciliation] = await db('bank_reconciliations')
      .insert({
        company_id: data.companyId,
        bank_account_id: data.bankAccountId,
        statement_date: data.statementDate,
        statement_beginning_balance: data.statementBeginningBalance,
        statement_ending_balance: data.statementEndingBalance,
        reconciled_by: data.reconciledBy,
        status: 'IN_PROGRESS',
      })
      .returning('*');

    return this.mapDatabaseReconciliation(reconciliation);
  }

  /**
   * Get reconciliation with smart matching suggestions
   */
  async getReconciliationWithMatches(
    reconciliationId: string
  ): Promise<{
    reconciliation: BankReconciliation;
    items: BankReconciliationItem[];
    suggestions: ReconciliationMatch[];
    summary: ReconciliationSummary;
  }> {
    const reconciliation = await this.getReconciliation(reconciliationId);
    const items = await this.getReconciliationItems(reconciliationId);
    const suggestions = await this.generateMatchingSuggestions(reconciliationId);
    const summary = await this.getReconciliationSummary(reconciliationId);

    return { reconciliation, items, suggestions, summary };
  }

  /**
   * AI-powered transaction matching
   */
  async generateMatchingSuggestions(
    reconciliationId: string
  ): Promise<ReconciliationMatch[]> {
    const reconciliation = await this.getReconciliation(reconciliationId);
    
    // Get unmatched bank transactions
    const bankTransactions = await db('bank_transactions')
      .where('bank_account_id', reconciliation.bankAccountId)
      .where('is_reconciled', false)
      .whereBetween('date', [
        new Date(reconciliation.statementDate).toISOString().split('T')[0],
        new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30 days forward
      ]);

    // Get unmatched accounting transactions
    const accountTransactions = await db('transactions')
      .join('transaction_entries', 'transactions.id', 'transaction_entries.transaction_id')
      .join('accounts', 'transaction_entries.account_id', 'accounts.id')
      .where('accounts.id', reconciliation.bankAccountId)
      .where('transactions.company_id', reconciliation.companyId)
      .whereNull('transactions.reconciled_at')
      .select('transactions.*', 'transaction_entries.debit_amount', 'transaction_entries.credit_amount');

    const matches: ReconciliationMatch[] = [];

    for (const bankTxn of bankTransactions) {
      for (const accTxn of accountTransactions) {
        const match = this.calculateMatchScore(bankTxn, accTxn);
        if (match.confidence > 0.5) { // Only suggest matches with >50% confidence
          matches.push(match);
        }
      }
    }

    // Sort by confidence descending
    return matches.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Calculate matching score between bank and accounting transactions
   */
  private calculateMatchScore(bankTxn: any, accTxn: any): ReconciliationMatch {
    let confidence = 0;
    const factors = {
      amount: false,
      date: false,
      description: false,
      reference: false,
    };

    // Amount matching (exact match = 40 points, close match = 20 points)
    const bankAmount = Math.abs(bankTxn.amount);
    const accAmount = Math.abs(accTxn.debit_amount || accTxn.credit_amount);
    if (bankAmount === accAmount) {
      confidence += 0.4;
      factors.amount = true;
    } else if (Math.abs(bankAmount - accAmount) / Math.max(bankAmount, accAmount) < 0.01) {
      confidence += 0.2;
      factors.amount = true;
    }

    // Date matching (same day = 30 points, within 3 days = 15 points)
    const bankDate = new Date(bankTxn.date);
    const accDate = new Date(accTxn.transaction_date);
    const daysDiff = Math.abs((bankDate.getTime() - accDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff === 0) {
      confidence += 0.3;
      factors.date = true;
    } else if (daysDiff <= 3) {
      confidence += 0.15;
      factors.date = true;
    }

    // Description matching (fuzzy string matching)
    const descSimilarity = this.calculateStringSimilarity(
      bankTxn.description?.toLowerCase() || '',
      accTxn.description?.toLowerCase() || ''
    );
    if (descSimilarity > 0.7) {
      confidence += 0.2;
      factors.description = true;
    } else if (descSimilarity > 0.4) {
      confidence += 0.1;
      factors.description = true;
    }

    // Reference matching (check number, reference, etc.)
    if (bankTxn.reference && accTxn.reference && 
        bankTxn.reference.toLowerCase() === accTxn.reference.toLowerCase()) {
      confidence += 0.1;
      factors.reference = true;
    }

    let suggestedAction: 'AUTO_MATCH' | 'REVIEW' | 'MANUAL_REVIEW';
    if (confidence >= 0.9) {
      suggestedAction = 'AUTO_MATCH';
    } else if (confidence >= 0.7) {
      suggestedAction = 'REVIEW';
    } else {
      suggestedAction = 'MANUAL_REVIEW';
    }

    return {
      bankTransactionId: bankTxn.id,
      transactionId: accTxn.id,
      matchType: 'FUZZY',
      confidence,
      matchingFactors: factors,
      suggestedAction,
    };
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }
    
    const maxLength = Math.max(str1.length, str2.length);
    return maxLength === 0 ? 1 : (maxLength - matrix[str2.length][str1.length]) / maxLength;
  }

  /**
   * Get reconciliations with pagination
   */
  async getReconciliations(filters: {
    companyId: string;
    bankAccountId?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
    page: number;
    limit: number;
  }) {
    let query = db('bank_reconciliations')
      .where('company_id', filters.companyId);

    if (filters.bankAccountId) {
      query = query.where('bank_account_id', filters.bankAccountId);
    }
    if (filters.status) {
      query = query.where('status', filters.status);
    }
    if (filters.dateFrom) {
      query = query.where('statement_date', '>=', filters.dateFrom);
    }
    if (filters.dateTo) {
      query = query.where('statement_date', '<=', filters.dateTo);
    }

    const total = await query.clone().count('* as count').first();
    const reconciliations = await query
      .orderBy('statement_date', 'desc')
      .limit(filters.limit)
      .offset((filters.page - 1) * filters.limit);

    return {
      data: reconciliations.map(this.mapDatabaseReconciliation),
      total: parseInt(total?.count as string || '0'),
      page: filters.page,
      totalPages: Math.ceil(parseInt(total?.count as string || '0') / filters.limit),
    };
  }

  /**
   * Match transactions
   */
  async matchTransactions(data: {
    reconciliationId: string;
    bankTransactionId: string;
    transactionId: string;
    matchType: string;
    matchedBy: string;
  }): Promise<void> {
    await db('reconciliation_matches').insert({
      bank_transaction_id: data.bankTransactionId,
      transaction_id: data.transactionId,
      match_type: data.matchType,
      confidence: 1.0,
      matching_factors: JSON.stringify({ manual: true }),
      suggested_action: 'AUTO_MATCH',
      is_accepted: true,
      accepted_by: data.matchedBy,
      accepted_at: new Date(),
    });

    // Update both transactions as reconciled
    await db('bank_transactions')
      .where('id', data.bankTransactionId)
      .update({ is_reconciled: true, reconciled_at: new Date() });

    await db('transactions')
      .where('id', data.transactionId)
      .update({ reconciled_at: new Date() });
  }

  /**
   * Create adjustment
   */
  async createAdjustment(data: {
    reconciliationId: string;
    type: string;
    description: string;
    amount: number;
    accountId: string;
    reference?: string;
    notes?: string;
    createdBy: string;
  }) {
    const [adjustment] = await db('reconciliation_adjustments')
      .insert({
        reconciliation_id: data.reconciliationId,
        type: data.type,
        description: data.description,
        amount: data.amount,
        account_id: data.accountId,
        reference: data.reference,
        notes: data.notes,
        created_by: data.createdBy,
      })
      .returning('*');

    return adjustment;
  }

  /**
   * Update reconciliation status
   */
  async updateReconciliationStatus(data: {
    reconciliationId: string;
    status: string;
    notes?: string;
    updatedBy: string;
  }): Promise<void> {
    const updates: any = {
      status: data.status,
      notes: data.notes,
      updated_at: new Date(),
    };

    if (data.status === 'COMPLETED') {
      updates.reconciled_at = new Date();
      updates.reconciled_by = data.updatedBy;
    } else if (data.status === 'REVIEWED') {
      updates.reviewed_at = new Date();
      updates.reviewed_by = data.updatedBy;
    } else if (data.status === 'LOCKED') {
      updates.locked_at = new Date();
      updates.locked_by = data.updatedBy;
    }

    await db('bank_reconciliations')
      .where('id', data.reconciliationId)
      .update(updates);
  }

  /**
   * Perform auto-matching
   */
  async performAutoMatching(data: {
    reconciliationId: string;
    confidenceThreshold: number;
    matchedBy: string;
  }) {
    const suggestions = await this.generateMatchingSuggestions(data.reconciliationId);
    const autoMatches = suggestions.filter(s =>
      s.confidence >= data.confidenceThreshold && s.suggestedAction === 'AUTO_MATCH'
    );

    let matchedCount = 0;
    for (const match of autoMatches) {
      try {
        await this.matchTransactions({
          reconciliationId: data.reconciliationId,
          bankTransactionId: match.bankTransactionId,
          transactionId: match.transactionId,
          matchType: 'AUTO',
          matchedBy: data.matchedBy,
        });
        matchedCount++;
      } catch (error) {
        console.error('Auto-match failed:', error);
      }
    }

    return {
      totalSuggestions: suggestions.length,
      autoMatched: matchedCount,
      requiresReview: suggestions.filter(s => s.suggestedAction === 'REVIEW').length,
    };
  }

  /**
   * Get reconciliation rules
   */
  async getReconciliationRules(companyId: string) {
    const rules = await db('reconciliation_rules')
      .where('company_id', companyId)
      .where('is_active', true)
      .orderBy('priority', 'desc');

    return rules;
  }

  /**
   * Create reconciliation rule
   */
  async createReconciliationRule(data: any) {
    const [rule] = await db('reconciliation_rules')
      .insert({
        company_id: data.companyId,
        name: data.name,
        description: data.description,
        is_active: data.isActive,
        priority: data.priority,
        conditions: JSON.stringify(data.conditions),
        actions: JSON.stringify(data.actions),
        created_by: data.createdBy,
      })
      .returning('*');

    return rule;
  }

  /**
   * Bulk reconciliation actions
   */
  async performBulkAction(action: BulkReconciliationAction): Promise<void> {
    const trx = await db.transaction();
    
    try {
      switch (action.action) {
        case 'RECONCILE':
          await trx('bank_reconciliation_items')
            .whereIn('id', action.itemIds)
            .update({
              is_reconciled: true,
              reconciled_at: new Date(),
            });
          break;
          
        case 'UNRECONCILE':
          await trx('bank_reconciliation_items')
            .whereIn('id', action.itemIds)
            .update({
              is_reconciled: false,
              reconciled_at: null,
            });
          break;
      }
      
      await trx.commit();
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  // Helper methods
  private async getReconciliation(id: string): Promise<BankReconciliation> {
    const reconciliation = await db('bank_reconciliations').where('id', id).first();
    if (!reconciliation) throw new Error('Reconciliation not found');
    return this.mapDatabaseReconciliation(reconciliation);
  }

  private async getReconciliationItems(reconciliationId: string): Promise<BankReconciliationItem[]> {
    const items = await db('bank_reconciliation_items')
      .where('reconciliation_id', reconciliationId);
    return items.map(this.mapDatabaseReconciliationItem);
  }

  private async getReconciliationSummary(reconciliationId: string): Promise<ReconciliationSummary> {
    const items = await this.getReconciliationItems(reconciliationId);
    
    return {
      totalTransactions: items.length,
      reconciledTransactions: items.filter(i => i.isReconciled).length,
      unreconciledTransactions: items.filter(i => !i.isReconciled).length,
      totalAmount: items.reduce((sum, i) => sum + i.amount, 0),
      reconciledAmount: items.filter(i => i.isReconciled).reduce((sum, i) => sum + i.amount, 0),
      unreconciledAmount: items.filter(i => !i.isReconciled).reduce((sum, i) => sum + i.amount, 0),
      outstandingChecks: items.filter(i => !i.isReconciled && i.amount < 0).length,
      outstandingDeposits: items.filter(i => !i.isReconciled && i.amount > 0).length,
      adjustments: items.filter(i => i.type === 'ADJUSTMENT').length,
    };
  }

  private mapDatabaseReconciliation(dbRec: any): BankReconciliation {
    return {
      id: dbRec.id,
      companyId: dbRec.company_id,
      bankAccountId: dbRec.bank_account_id,
      statementDate: dbRec.statement_date,
      statementBeginningBalance: parseFloat(dbRec.statement_beginning_balance),
      statementEndingBalance: parseFloat(dbRec.statement_ending_balance),
      reconciledBalance: parseFloat(dbRec.reconciled_balance || 0),
      difference: parseFloat(dbRec.difference || 0),
      status: dbRec.status,
      reconciledBy: dbRec.reconciled_by,
      reviewedBy: dbRec.reviewed_by,
      lockedBy: dbRec.locked_by,
      reconciledAt: dbRec.reconciled_at,
      reviewedAt: dbRec.reviewed_at,
      lockedAt: dbRec.locked_at,
      notes: dbRec.notes,
      createdAt: dbRec.created_at,
      updatedAt: dbRec.updated_at,
    };
  }

  private mapDatabaseReconciliationItem(dbItem: any): BankReconciliationItem {
    return {
      id: dbItem.id,
      reconciliationId: dbItem.reconciliation_id,
      transactionId: dbItem.transaction_id,
      bankTransactionId: dbItem.bank_transaction_id,
      type: dbItem.type,
      description: dbItem.description,
      amount: parseFloat(dbItem.amount),
      transactionDate: dbItem.transaction_date,
      checkNumber: dbItem.check_number,
      reference: dbItem.reference,
      isReconciled: dbItem.is_reconciled,
      reconciledAt: dbItem.reconciled_at,
      reconciledBy: dbItem.reconciled_by,
      matchConfidence: dbItem.match_confidence ? parseFloat(dbItem.match_confidence) : undefined,
      matchedTransactionId: dbItem.matched_transaction_id,
      adjustmentReason: dbItem.adjustment_reason,
      createdAt: dbItem.created_at,
      updatedAt: dbItem.updated_at,
    };
  }
}

export const bankReconciliationService = new BankReconciliationService();
