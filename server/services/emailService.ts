import nodemailer from 'nodemailer';

interface EmailOptions {
  to: string | string[];
  subject: string;
  template?: string;
  data?: any;
  html?: string;
  text?: string;
}

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }

  public async sendEmail(options: EmailOptions): Promise<any> {
    try {
      const mailOptions = {
        from: `${process.env.FROM_NAME || 'Accounting System'} <${process.env.FROM_EMAIL}>`,
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        subject: options.subject,
        html: options.html || this.renderTemplate(options.template, options.data),
        text: options.text,
      };

      const result = await this.transporter.sendMail(mailOptions);
      return result;
    } catch (error) {
      console.error('Failed to send email:', error);
      throw new Error('Failed to send email');
    }
  }

  private renderTemplate(template?: string, data?: any): string {
    // Simple template rendering - in production you'd use a proper template engine
    if (!template) {
      return JSON.stringify(data || {});
    }
    
    let rendered = template;
    if (data) {
      Object.keys(data).forEach(key => {
        rendered = rendered.replace(new RegExp(`{{${key}}}`, 'g'), data[key]);
      });
    }
    
    return rendered;
  }
}

export const emailService = new EmailService();
