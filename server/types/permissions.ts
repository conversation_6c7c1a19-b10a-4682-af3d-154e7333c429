export interface FieldPermission {
  id: string;
  roleId: string;
  resource: string; // e.g., 'transactions', 'accounts', 'users'
  field: string; // e.g., 'amount', 'description', 'email'
  permission: 'READ' | 'WRITE' | 'NONE';
  conditions?: {
    ownedBy?: boolean; // Can only access records they own
    departmentOnly?: boolean; // Can only access records from their department
    amountLimit?: number; // Can only access records below certain amount
    dateRange?: { days: number }; // Can only access records within date range
  };
  createdAt: string;
  updatedAt: string;
}

export interface TimeBasedAccess {
  id: string;
  userId: string;
  roleId?: string;
  resource: string;
  action: string;
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
  daysOfWeek: number[]; // 0-6, Sunday = 0
  timezone: string;
  isActive: boolean;
  expiresAt?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface IPRestriction {
  id: string;
  userId?: string;
  roleId?: string;
  companyId: string;
  ipAddress?: string; // Single IP
  ipRange?: string; // CIDR notation
  isWhitelist: boolean; // true = allow, false = deny
  description?: string;
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface AccessAuditLog {
  id: string;
  userId: string;
  companyId: string;
  resource: string;
  resourceId?: string;
  action: string;
  field?: string;
  oldValue?: any;
  newValue?: any;
  ipAddress: string;
  userAgent: string;
  sessionId: string;
  success: boolean;
  failureReason?: string;
  metadata?: Record<string, any>;
  timestamp: string;
}

export interface SessionSecurity {
  id: string;
  userId: string;
  sessionToken: string;
  ipAddress: string;
  userAgent: string;
  location?: {
    country?: string;
    city?: string;
    region?: string;
  };
  isActive: boolean;
  lastActivity: string;
  expiresAt: string;
  createdAt: string;
}

export interface SecurityPolicy {
  id: string;
  companyId: string;
  name: string;
  description?: string;
  settings: {
    sessionTimeout: number; // minutes
    maxConcurrentSessions: number;
    requireMFA: boolean;
    passwordPolicy: {
      minLength: number;
      requireUppercase: boolean;
      requireLowercase: boolean;
      requireNumbers: boolean;
      requireSpecialChars: boolean;
      maxAge: number; // days
    };
    ipRestrictions: {
      enabled: boolean;
      allowUnknownIPs: boolean;
    };
    timeRestrictions: {
      enabled: boolean;
      defaultWorkingHours: {
        start: string;
        end: string;
        daysOfWeek: number[];
      };
    };
    auditSettings: {
      logAllAccess: boolean;
      logFailedAttempts: boolean;
      retentionDays: number;
    };
  };
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface PermissionCheck {
  userId: string;
  resource: string;
  action: string;
  field?: string;
  resourceId?: string;
  context?: {
    ipAddress?: string;
    timestamp?: string;
    sessionId?: string;
  };
}

export interface PermissionResult {
  allowed: boolean;
  reason?: string;
  restrictions?: {
    fieldPermissions?: Record<string, 'READ' | 'WRITE' | 'NONE'>;
    conditions?: any;
  };
}

export interface RoleHierarchy {
  id: string;
  parentRoleId: string;
  childRoleId: string;
  inheritPermissions: boolean;
  createdAt: string;
}

export interface ConditionalPermission {
  id: string;
  roleId: string;
  resource: string;
  action: string;
  conditions: {
    field: string;
    operator: 'EQUALS' | 'NOT_EQUALS' | 'GREATER_THAN' | 'LESS_THAN' | 'CONTAINS' | 'IN' | 'NOT_IN';
    value: any;
    logicalOperator?: 'AND' | 'OR';
  }[];
  permission: 'ALLOW' | 'DENY';
  priority: number;
  createdAt: string;
  updatedAt: string;
}
