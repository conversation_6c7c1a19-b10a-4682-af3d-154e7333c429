export interface BankReconciliation {
  id: string;
  companyId: string;
  bankAccountId: string;
  statementDate: string;
  statementBeginningBalance: number;
  statementEndingBalance: number;
  reconciledBalance: number;
  difference: number;
  status: 'IN_PROGRESS' | 'COMPLETED' | 'REVIEWED' | 'LOCKED';
  reconciledBy: string;
  reviewedBy?: string;
  lockedBy?: string;
  reconciledAt?: string;
  reviewedAt?: string;
  lockedAt?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BankReconciliationItem {
  id: string;
  reconciliationId: string;
  transactionId?: string; // Link to existing transaction
  bankTransactionId?: string; // Link to bank transaction
  type: 'TRANSACTION' | 'BANK_TRANSACTION' | 'ADJUSTMENT';
  description: string;
  amount: number;
  transactionDate: string;
  checkNumber?: string;
  reference?: string;
  isReconciled: boolean;
  reconciledAt?: string;
  reconciledBy?: string;
  matchConfidence?: number; // AI matching confidence score
  matchedTransactionId?: string;
  adjustmentReason?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ReconciliationMatch {
  bankTransactionId: string;
  transactionId: string;
  matchType: 'EXACT' | 'FUZZY' | 'MANUAL' | 'RULE_BASED';
  confidence: number;
  matchingFactors: {
    amount: boolean;
    date: boolean;
    description: boolean;
    reference: boolean;
  };
  suggestedAction: 'AUTO_MATCH' | 'REVIEW' | 'MANUAL_REVIEW';
}

export interface ReconciliationRule {
  id: string;
  companyId: string;
  name: string;
  description: string;
  isActive: boolean;
  priority: number;
  conditions: {
    amountRange?: { min?: number; max?: number };
    descriptionContains?: string[];
    descriptionRegex?: string;
    dateRange?: { days: number };
    accountTypes?: string[];
    transactionTypes?: string[];
  };
  actions: {
    autoMatch: boolean;
    category?: string;
    tags?: string[];
    notes?: string;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface ReconciliationFilters {
  status?: BankReconciliation['status'];
  dateFrom?: string;
  dateTo?: string;
  bankAccountId?: string;
  reconciledBy?: string;
  amountRange?: { min?: number; max?: number };
  isReconciled?: boolean;
  search?: string;
}

export interface ReconciliationSummary {
  totalTransactions: number;
  reconciledTransactions: number;
  unreconciledTransactions: number;
  totalAmount: number;
  reconciledAmount: number;
  unreconciledAmount: number;
  outstandingChecks: number;
  outstandingDeposits: number;
  adjustments: number;
}

export interface BulkReconciliationAction {
  action: 'RECONCILE' | 'UNRECONCILE' | 'MATCH' | 'UNMATCH';
  itemIds: string[];
  reconciliationId: string;
  notes?: string;
}

export interface ReconciliationAdjustment {
  id: string;
  reconciliationId: string;
  type: 'BANK_ERROR' | 'BOOK_ERROR' | 'TIMING_DIFFERENCE' | 'OTHER';
  description: string;
  amount: number;
  accountId: string;
  reference?: string;
  notes?: string;
  createdBy: string;
  createdAt: string;
}
