import type { Request, Response, NextFunction } from "express";
import { v4 as uuidv4 } from "uuid";

export interface RequestWithId extends Request {
  id: string;
}

export const requestLogger = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const reqWithId = req as RequestWithId;
  reqWithId.id = uuidv4();

  const start = Date.now();

  // Log request
  console.log(`[${reqWithId.id}] ${req.method} ${req.originalUrl} - ${req.ip}`);

  // Override res.end to log response
  const originalEnd = res.end.bind(res);
  res.end = function (chunk?: any, encoding?: any, cb?: any) {
    const duration = Date.now() - start;
    console.log(`[${reqWithId.id}] ${res.statusCode} - ${duration}ms`);
    return originalEnd(chunk, encoding, cb);
  };

  next();
};
