import { Request, Response, NextFunction } from 'express';
import { performance } from 'perf_hooks';
import { db } from '../config/database';
import { cacheService } from '../services/cacheService';

interface PerformanceMetrics {
  requestId: string;
  method: string;
  url: string;
  statusCode: number;
  responseTime: number;
  memoryUsage: NodeJS.MemoryUsage;
  dbQueries: number;
  cacheHits: number;
  cacheMisses: number;
  timestamp: Date;
  userAgent?: string;
  ip?: string;
  userId?: string;
  companyId?: string;
}

interface SlowQueryLog {
  query: string;
  duration: number;
  timestamp: Date;
  requestId: string;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private slowQueries: SlowQueryLog[] = [];
  private readonly SLOW_QUERY_THRESHOLD = 1000; // 1 second
  private readonly METRICS_RETENTION_HOURS = 24;
  private readonly MAX_METRICS_IN_MEMORY = 10000;

  // Request performance tracking
  trackRequest() {
    return (req: Request, res: Response, next: NextFunction) => {
      const startTime = performance.now();
      const requestId = this.generateRequestId();
      const startMemory = process.memoryUsage();

      // Add request ID to request object
      (req as any).requestId = requestId;
      (req as any).startTime = startTime;
      (req as any).dbQueries = 0;
      (req as any).cacheHits = 0;
      (req as any).cacheMisses = 0;

      // Override res.end to capture metrics
      const originalEnd = res.end.bind(res);
      res.end = (...args: any[]) => {
        const endTime = performance.now();
        const responseTime = endTime - startTime;
        const endMemory = process.memoryUsage();

        // Calculate memory delta
        const memoryDelta = {
          rss: endMemory.rss - startMemory.rss,
          heapTotal: endMemory.heapTotal - startMemory.heapTotal,
          heapUsed: endMemory.heapUsed - startMemory.heapUsed,
          external: endMemory.external - startMemory.external,
          arrayBuffers: endMemory.arrayBuffers - startMemory.arrayBuffers
        };

        // Create performance metrics
        const metrics: PerformanceMetrics = {
          requestId,
          method: req.method,
          url: req.originalUrl,
          statusCode: res.statusCode,
          responseTime,
          memoryUsage: memoryDelta,
          dbQueries: (req as any).dbQueries || 0,
          cacheHits: (req as any).cacheHits || 0,
          cacheMisses: (req as any).cacheMisses || 0,
          timestamp: new Date(),
          userAgent: req.get('User-Agent'),
          ip: req.ip,
          userId: (req as any).user?.id,
          companyId: req.params.companyId
        };

        // Store metrics
        this.storeMetrics(metrics);

        // Add performance headers
        res.set({
          'X-Response-Time': `${responseTime.toFixed(2)}ms`,
          'X-Request-ID': requestId,
          'X-DB-Queries': (req as any).dbQueries?.toString() || '0',
          'X-Cache-Status': (req as any).cacheHits > 0 ? 'HIT' : 'MISS'
        });

        // Log slow requests
        if (responseTime > 5000) { // 5 seconds
          console.warn(`🐌 Slow request detected: ${req.method} ${req.originalUrl} - ${responseTime.toFixed(2)}ms`);
        }

        return originalEnd(...args);
      };

      next();
    };
  }

  // Database query tracking
  trackDatabaseQueries() {
    const originalQuery = db.raw.bind(db);
    
    db.raw = function(sql: string, bindings?: any) {
      const startTime = performance.now();
      
      return originalQuery(sql, bindings).then((result: any) => {
        const duration = performance.now() - startTime;
        
        // Track slow queries
        if (duration > performanceMonitor.SLOW_QUERY_THRESHOLD) {
          performanceMonitor.logSlowQuery(sql, duration);
        }
        
        return result;
      });
    };
  }

  // Memory usage monitoring
  monitorMemoryUsage() {
    setInterval(() => {
      const usage = process.memoryUsage();
      const usageInMB = {
        rss: Math.round(usage.rss / 1024 / 1024),
        heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
        external: Math.round(usage.external / 1024 / 1024)
      };

      // Log memory warnings
      if (usageInMB.heapUsed > 512) { // 512MB threshold
        console.warn(`⚠️ High memory usage detected: ${usageInMB.heapUsed}MB heap used`);
      }

      // Store memory metrics in cache for monitoring
      cacheService.set('system:memory_usage', usageInMB, { ttl: 300 });
    }, 30000); // Every 30 seconds
  }

  // CPU usage monitoring
  monitorCPUUsage() {
    setInterval(() => {
      const usage = process.cpuUsage();
      const cpuPercent = {
        user: usage.user / 1000000, // Convert to seconds
        system: usage.system / 1000000
      };

      cacheService.set('system:cpu_usage', cpuPercent, { ttl: 300 });
    }, 30000);
  }

  // Store performance metrics
  private storeMetrics(metrics: PerformanceMetrics) {
    // Store in memory (with rotation)
    this.metrics.push(metrics);
    if (this.metrics.length > this.MAX_METRICS_IN_MEMORY) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS_IN_MEMORY / 2);
    }

    // Store in cache for real-time monitoring
    cacheService.set(`metrics:${metrics.requestId}`, metrics, { ttl: 3600 });

    // Aggregate metrics for dashboard
    this.updateAggregateMetrics(metrics);
  }

  // Log slow queries
  private logSlowQuery(query: string, duration: number, requestId?: string) {
    const slowQuery: SlowQueryLog = {
      query: query.substring(0, 500), // Truncate long queries
      duration,
      timestamp: new Date(),
      requestId: requestId || 'unknown'
    };

    this.slowQueries.push(slowQuery);
    console.warn(`🐌 Slow query detected: ${duration.toFixed(2)}ms - ${query.substring(0, 100)}...`);

    // Keep only recent slow queries
    const cutoff = new Date(Date.now() - this.METRICS_RETENTION_HOURS * 60 * 60 * 1000);
    this.slowQueries = this.slowQueries.filter(q => q.timestamp > cutoff);
  }

  // Update aggregate metrics
  private updateAggregateMetrics(metrics: PerformanceMetrics) {
    const key = `aggregate:${new Date().toISOString().split('T')[0]}`; // Daily aggregates
    
    cacheService.get(key).then(existing => {
      const aggregates = existing || {
        totalRequests: 0,
        averageResponseTime: 0,
        slowRequests: 0,
        errorRequests: 0,
        totalDbQueries: 0,
        cacheHitRate: 0
      };

      aggregates.totalRequests++;
      aggregates.averageResponseTime = (aggregates.averageResponseTime * (aggregates.totalRequests - 1) + metrics.responseTime) / aggregates.totalRequests;
      
      if (metrics.responseTime > 5000) aggregates.slowRequests++;
      if (metrics.statusCode >= 400) aggregates.errorRequests++;
      
      aggregates.totalDbQueries += metrics.dbQueries;
      
      const totalCacheRequests = metrics.cacheHits + metrics.cacheMisses;
      if (totalCacheRequests > 0) {
        aggregates.cacheHitRate = metrics.cacheHits / totalCacheRequests;
      }

      cacheService.set(key, aggregates, { ttl: 86400 }); // 24 hours
    });
  }

  // Get performance statistics
  async getStats(timeframe: 'hour' | 'day' | 'week' = 'hour') {
    const now = new Date();
    let cutoff: Date;

    switch (timeframe) {
      case 'hour':
        cutoff = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case 'day':
        cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        cutoff = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
    }

    const recentMetrics = this.metrics.filter(m => m.timestamp > cutoff);

    if (recentMetrics.length === 0) {
      return null;
    }

    const stats = {
      totalRequests: recentMetrics.length,
      averageResponseTime: recentMetrics.reduce((sum, m) => sum + m.responseTime, 0) / recentMetrics.length,
      slowRequests: recentMetrics.filter(m => m.responseTime > 5000).length,
      errorRequests: recentMetrics.filter(m => m.statusCode >= 400).length,
      totalDbQueries: recentMetrics.reduce((sum, m) => sum + m.dbQueries, 0),
      averageDbQueries: recentMetrics.reduce((sum, m) => sum + m.dbQueries, 0) / recentMetrics.length,
      cacheHitRate: this.calculateCacheHitRate(recentMetrics),
      topSlowEndpoints: this.getTopSlowEndpoints(recentMetrics),
      memoryTrend: this.getMemoryTrend(recentMetrics),
      timeframe
    };

    return stats;
  }

  // Get slow queries
  getSlowQueries(limit: number = 50) {
    return this.slowQueries
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit);
  }

  // Calculate cache hit rate
  private calculateCacheHitRate(metrics: PerformanceMetrics[]): number {
    const totalHits = metrics.reduce((sum, m) => sum + m.cacheHits, 0);
    const totalMisses = metrics.reduce((sum, m) => sum + m.cacheMisses, 0);
    const total = totalHits + totalMisses;
    
    return total > 0 ? (totalHits / total) * 100 : 0;
  }

  // Get top slow endpoints
  private getTopSlowEndpoints(metrics: PerformanceMetrics[], limit: number = 10) {
    const endpointStats = new Map<string, { count: number; totalTime: number; avgTime: number }>();

    metrics.forEach(m => {
      const endpoint = `${m.method} ${m.url.split('?')[0]}`;
      const existing = endpointStats.get(endpoint) || { count: 0, totalTime: 0, avgTime: 0 };
      
      existing.count++;
      existing.totalTime += m.responseTime;
      existing.avgTime = existing.totalTime / existing.count;
      
      endpointStats.set(endpoint, existing);
    });

    return Array.from(endpointStats.entries())
      .map(([endpoint, stats]) => ({ endpoint, ...stats }))
      .sort((a, b) => b.avgTime - a.avgTime)
      .slice(0, limit);
  }

  // Get memory usage trend
  private getMemoryTrend(metrics: PerformanceMetrics[]) {
    return metrics.map(m => ({
      timestamp: m.timestamp,
      heapUsed: m.memoryUsage.heapUsed,
      rss: m.memoryUsage.rss
    }));
  }

  // Generate unique request ID
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Health check
  async healthCheck() {
    const memoryUsage = process.memoryUsage();
    const stats = await this.getStats('hour');
    
    return {
      status: 'healthy',
      memory: {
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        rss: Math.round(memoryUsage.rss / 1024 / 1024)
      },
      performance: stats,
      slowQueries: this.slowQueries.length,
      uptime: process.uptime()
    };
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Initialize monitoring
export const initializePerformanceMonitoring = () => {
  performanceMonitor.trackDatabaseQueries();
  performanceMonitor.monitorMemoryUsage();
  performanceMonitor.monitorCPUUsage();
  
  console.log('🚀 Performance monitoring initialized');
};

// Export middleware
export const performanceMiddleware = performanceMonitor.trackRequest();

// Rate limiting middleware for performance protection
export const rateLimitMiddleware = (windowMs: number = 15 * 60 * 1000, max: number = 100) => {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction) => {
    const key = req.ip || 'unknown';
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean up old entries
    for (const [ip, data] of requests.entries()) {
      if (data.resetTime < windowStart) {
        requests.delete(ip);
      }
    }

    // Get or create request data
    let requestData = requests.get(key);
    if (!requestData || requestData.resetTime < windowStart) {
      requestData = { count: 0, resetTime: now + windowMs };
      requests.set(key, requestData);
    }

    // Check rate limit
    if (requestData.count >= max) {
      res.status(429).json({
        error: 'Too many requests',
        retryAfter: Math.ceil((requestData.resetTime - now) / 1000)
      });
      return;
    }

    // Increment counter
    requestData.count++;

    // Add rate limit headers
    res.set({
      'X-RateLimit-Limit': max.toString(),
      'X-RateLimit-Remaining': (max - requestData.count).toString(),
      'X-RateLimit-Reset': new Date(requestData.resetTime).toISOString()
    });

    next();
  };
};
