import type { Request, Response, NextFunction } from "express";
import { auditService } from "../services/auditService";

export interface AuditableRequest extends Request {
  auditData?: {
    action?: 'CREATE' | 'UPDATE' | 'DELETE' | 'APPROVE' | 'REJECT' | 'REVERSE' | 'EXPORT' | 'VIEW';
    tableName?: string;
    recordId?: string;
    oldValues?: any;
    newValues?: any;
    description?: string;
    metadata?: any;
  };
}

/**
 * Middleware to automatically log audit events after successful operations
 */
export const auditLogger = (
  req: AuditableRequest,
  res: Response,
  next: NextFunction
) => {
  // Store original res.json to intercept successful responses
  const originalJson = res.json.bind(res);

  res.json = function (body: any) {
    // Only log if the response is successful (2xx status codes)
    if (res.statusCode >= 200 && res.statusCode < 300 && req.auditData) {
      // Extract request info
      const requestInfo = auditService.extractRequestInfo(req);
      
      // Log the audit event asynchronously (don't block the response)
      setImmediate(async () => {
        try {
          if (req.auditData?.action && ['CREATE', 'UPDATE', 'DELETE'].includes(req.auditData.action)) {
            await auditService.logCrud({
              companyId: requestInfo.companyId || '',
              userId: requestInfo.userId || '',
              userEmail: requestInfo.userEmail || '',
              action: req.auditData.action as 'CREATE' | 'UPDATE' | 'DELETE',
              tableName: req.auditData.tableName || '',
              recordId: req.auditData.recordId || '',
              oldValues: req.auditData.oldValues,
              newValues: req.auditData.newValues,
              ipAddress: requestInfo.ipAddress,
              userAgent: requestInfo.userAgent,
            });
          } else if (req.auditData?.action && ['APPROVE', 'REJECT', 'REVERSE'].includes(req.auditData.action)) {
            await auditService.log({
              companyId: requestInfo.companyId || '',
              userId: requestInfo.userId,
              userEmail: requestInfo.userEmail || '',
              action: req.auditData.action as 'APPROVE' | 'REJECT' | 'REVERSE',
              tableName: req.auditData.tableName,
              recordId: req.auditData.recordId,
              description: req.auditData.description || `${req.auditData.action} action performed`,
              ipAddress: requestInfo.ipAddress,
              userAgent: requestInfo.userAgent,
              metadata: req.auditData.metadata,
            });
          } else if (req.auditData?.action === 'EXPORT') {
            await auditService.logExport({
              companyId: requestInfo.companyId || '',
              userId: requestInfo.userId || '',
              userEmail: requestInfo.userEmail || '',
              exportType: req.auditData.description || 'data',
              recordCount: req.auditData.metadata?.recordCount,
              filters: req.auditData.metadata?.filters,
              ipAddress: requestInfo.ipAddress,
              userAgent: requestInfo.userAgent,
            });
          } else {
            // Generic audit log
            await auditService.log({
              companyId: requestInfo.companyId || '',
              userId: requestInfo.userId,
              userEmail: requestInfo.userEmail || '',
              action: req.auditData.action || 'VIEW',
              tableName: req.auditData.tableName,
              recordId: req.auditData.recordId,
              description: req.auditData.description || 'Action performed',
              ipAddress: requestInfo.ipAddress,
              userAgent: requestInfo.userAgent,
              metadata: req.auditData.metadata,
            });
          }
        } catch (error) {
          console.error('Failed to log audit event:', error);
        }
      });
    }

    return originalJson(body);
  };

  next();
};

/**
 * Helper function to set audit data on the request
 */
export const setAuditData = (
  req: AuditableRequest,
  data: {
    action: 'CREATE' | 'UPDATE' | 'DELETE' | 'APPROVE' | 'REJECT' | 'REVERSE' | 'EXPORT' | 'VIEW';
    tableName?: string;
    recordId?: string;
    oldValues?: any;
    newValues?: any;
    description?: string;
    metadata?: any;
  }
) => {
  req.auditData = data;
};

/**
 * Middleware factory for common CRUD operations
 */
export const auditCrud = (tableName: string) => {
  return {
    create: (req: AuditableRequest, _res: Response, next: NextFunction) => {
      setAuditData(req, {
        action: 'CREATE',
        tableName,
        description: `${tableName} record created`,
      });
      next();
    },
    
    update: (recordId?: string) => (req: AuditableRequest, _res: Response, next: NextFunction) => {
      setAuditData(req, {
        action: 'UPDATE',
        tableName,
        recordId: recordId || req.params.id,
        description: `${tableName} record updated`,
      });
      next();
    },
    
    delete: (recordId?: string) => (req: AuditableRequest, _res: Response, next: NextFunction) => {
      setAuditData(req, {
        action: 'DELETE',
        tableName,
        recordId: recordId || req.params.id,
        description: `${tableName} record deleted`,
      });
      next();
    },
  };
};

/**
 * Middleware for transaction workflow actions
 */
export const auditTransactionWorkflow = (action: 'APPROVE' | 'REJECT' | 'REVERSE') => {
  return (req: AuditableRequest, _res: Response, next: NextFunction) => {
    setAuditData(req, {
      action,
      tableName: 'transactions',
      recordId: req.params.transactionId,
      description: `Transaction ${action.toLowerCase()}`,
    });
    next();
  };
};

/**
 * Middleware for export operations
 */
export const auditExport = (exportType: string) => {
  return (req: AuditableRequest, _res: Response, next: NextFunction) => {
    setAuditData(req, {
      action: 'EXPORT',
      description: exportType,
      metadata: {
        filters: req.query,
      },
    });
    next();
  };
};
