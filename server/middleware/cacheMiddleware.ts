import { Request, Response, NextFunction } from 'express';
import { cacheService } from '../services/cacheService';
import crypto from 'crypto';

interface CacheMiddlewareOptions {
  ttl?: number; // Time to live in seconds
  keyGenerator?: (req: Request) => string;
  condition?: (req: Request, res: Response) => boolean;
  skipCache?: (req: Request) => boolean;
  varyBy?: string[]; // Headers to vary cache by
  companySpecific?: boolean; // Whether to include company ID in cache key
}

// Default cache middleware
export const cacheMiddleware = (options: CacheMiddlewareOptions = {}) => {
  const {
    ttl = 300, // 5 minutes default
    keyGenerator,
    condition,
    skipCache,
    varyBy = [],
    companySpecific = true
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    // Only cache GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Skip cache if condition is not met
    if (skipCache && skipCache(req)) {
      return next();
    }

    try {
      // Generate cache key
      const cacheKey = keyGenerator ? keyGenerator(req) : generateDefaultCacheKey(req, varyBy, companySpecific);
      
      // Try to get cached response
      const cachedResponse = await cacheService.get(cacheKey);
      
      if (cachedResponse) {
        // Add cache headers
        res.set({
          'X-Cache': 'HIT',
          'X-Cache-Key': cacheKey,
          'Cache-Control': `public, max-age=${ttl}`
        });
        
        return res.json(cachedResponse);
      }

      // Store original res.json method
      const originalJson = res.json.bind(res);
      
      // Override res.json to cache the response
      res.json = function(data: any) {
        // Check if we should cache this response
        if (!condition || condition(req, res)) {
          // Cache the response asynchronously
          setImmediate(() => {
            cacheService.set(cacheKey, data, { ttl });
          });
        }

        // Add cache headers
        res.set({
          'X-Cache': 'MISS',
          'X-Cache-Key': cacheKey,
          'Cache-Control': `public, max-age=${ttl}`
        });

        // Call original res.json
        return originalJson(data);
      };

      next();
    } catch (error) {
      console.error('Cache middleware error:', error);
      next();
    }
  };
};

// Specific cache middleware for financial reports
export const financialReportsCacheMiddleware = cacheMiddleware({
  ttl: 3600, // 1 hour
  keyGenerator: (req) => {
    const companyId = req.params.companyId;
    const reportType = req.params.reportType || req.path.split('/').pop();
    const queryHash = hashObject(req.query);
    return `financial_reports:${companyId}:${reportType}:${queryHash}`;
  },
  condition: (req, res) => res.statusCode === 200,
  skipCache: (req) => req.query.nocache === 'true'
});

// Specific cache middleware for analytics
export const analyticsCacheMiddleware = cacheMiddleware({
  ttl: 1800, // 30 minutes
  keyGenerator: (req) => {
    const companyId = req.params.companyId;
    const analyticsType = req.params.analyticsType || 'overview';
    const queryHash = hashObject(req.query);
    return `analytics:${companyId}:${analyticsType}:${queryHash}`;
  },
  condition: (req, res) => res.statusCode === 200
});

// Specific cache middleware for dashboard data
export const dashboardCacheMiddleware = cacheMiddleware({
  ttl: 300, // 5 minutes
  keyGenerator: (req) => {
    const companyId = req.params.companyId;
    const userId = (req as any).user?.id;
    return `dashboard:${companyId}:${userId}`;
  },
  condition: (req, res) => res.statusCode === 200
});

// Specific cache middleware for account balances
export const accountBalancesCacheMiddleware = cacheMiddleware({
  ttl: 600, // 10 minutes
  keyGenerator: (req) => {
    const companyId = req.params.companyId;
    return `account_balances:${companyId}`;
  },
  condition: (req, res) => res.statusCode === 200
});

// Specific cache middleware for custom reports
export const customReportsCacheMiddleware = cacheMiddleware({
  ttl: 3600, // 1 hour
  keyGenerator: (req) => {
    const reportId = req.params.reportId;
    const queryHash = hashObject(req.query);
    return `custom_reports:${reportId}:${queryHash}`;
  },
  condition: (req, res) => res.statusCode === 200,
  skipCache: (req) => req.query.nocache === 'true'
});

// Cache invalidation middleware
export const cacheInvalidationMiddleware = (patterns: string[] | ((req: Request) => string[])) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Store original res.json method
    const originalJson = res.json.bind(res);
    
    // Override res.json to invalidate cache after successful operations
    res.json = function(data: any) {
      // Only invalidate on successful operations
      if (res.statusCode >= 200 && res.statusCode < 300) {
        setImmediate(async () => {
          try {
            const invalidationPatterns = typeof patterns === 'function' ? patterns(req) : patterns;
            
            for (const pattern of invalidationPatterns) {
              await cacheService.deletePattern(pattern);
            }
          } catch (error) {
            console.error('Cache invalidation error:', error);
          }
        });
      }

      // Call original res.json
      return originalJson(data);
    };

    next();
  };
};

// Company-specific cache invalidation
export const companyInvalidationMiddleware = cacheInvalidationMiddleware((req) => {
  const companyId = req.params.companyId;
  return [
    `financial_reports:${companyId}:*`,
    `analytics:${companyId}:*`,
    `account_balances:${companyId}`,
    `dashboard:${companyId}:*`,
    `budgets:${companyId}:*`
  ];
});

// Transaction-specific cache invalidation
export const transactionInvalidationMiddleware = cacheInvalidationMiddleware((req) => {
  const companyId = req.params.companyId;
  return [
    `financial_reports:${companyId}:*`,
    `analytics:${companyId}:*`,
    `account_balances:${companyId}`,
    `dashboard:${companyId}:*`,
    `reconciliation:${companyId}:*`
  ];
});

// Invoice-specific cache invalidation
export const invoiceInvalidationMiddleware = cacheInvalidationMiddleware((req) => {
  const companyId = req.params.companyId;
  return [
    `financial_reports:${companyId}:*`,
    `analytics:${companyId}:*`,
    `dashboard:${companyId}:*`
  ];
});

// Budget-specific cache invalidation
export const budgetInvalidationMiddleware = cacheInvalidationMiddleware((req) => {
  const companyId = req.params.companyId;
  return [
    `budgets:${companyId}:*`,
    `analytics:${companyId}:*`,
    `dashboard:${companyId}:*`
  ];
});

// Utility functions
function generateDefaultCacheKey(req: Request, varyBy: string[] = [], companySpecific: boolean = true): string {
  const parts: string[] = ['api'];
  
  // Add company ID if company-specific
  if (companySpecific && req.params.companyId) {
    parts.push(req.params.companyId);
  }
  
  // Add path
  parts.push(req.path.replace(/\//g, '_'));
  
  // Add query parameters
  if (Object.keys(req.query).length > 0) {
    parts.push(hashObject(req.query));
  }
  
  // Add vary headers
  if (varyBy.length > 0) {
    const varyValues = varyBy.map(header => req.get(header) || '').join('|');
    if (varyValues) {
      parts.push(hashString(varyValues));
    }
  }
  
  return parts.join(':');
}

function hashObject(obj: any): string {
  const sorted = JSON.stringify(obj, Object.keys(obj).sort());
  return hashString(sorted);
}

function hashString(str: string): string {
  return crypto.createHash('md5').update(str).digest('hex').substring(0, 8);
}

// Cache warming utilities
export class CacheWarmer {
  static async warmFinancialReports(companyId: string): Promise<void> {
    const reportTypes = ['balance-sheet', 'income-statement', 'cash-flow', 'trial-balance'];
    const periods = ['current-month', 'current-quarter', 'current-year'];
    
    for (const reportType of reportTypes) {
      for (const period of periods) {
        try {
          // This would typically call the actual report generation endpoint
          console.log(`Warming cache for ${reportType} - ${period} for company ${companyId}`);
          // await generateReport(companyId, reportType, { period });
        } catch (error) {
          console.error(`Failed to warm cache for ${reportType}:`, error);
        }
      }
    }
  }

  static async warmDashboardData(companyId: string, userIds: string[]): Promise<void> {
    for (const userId of userIds) {
      try {
        console.log(`Warming dashboard cache for user ${userId} in company ${companyId}`);
        // await generateDashboardData(companyId, userId);
      } catch (error) {
        console.error(`Failed to warm dashboard cache for user ${userId}:`, error);
      }
    }
  }

  static async warmAnalytics(companyId: string): Promise<void> {
    const analyticsTypes = ['overview', 'financial-metrics', 'trends', 'kpis'];
    
    for (const analyticsType of analyticsTypes) {
      try {
        console.log(`Warming analytics cache for ${analyticsType} for company ${companyId}`);
        // await generateAnalytics(companyId, analyticsType);
      } catch (error) {
        console.error(`Failed to warm analytics cache for ${analyticsType}:`, error);
      }
    }
  }
}

// Cache health monitoring
export const cacheHealthMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const health = await cacheService.healthCheck();
    req.cacheHealth = health;
    next();
  } catch (error) {
    console.error('Cache health check failed:', error);
    req.cacheHealth = { status: 'unhealthy', error: error.message };
    next();
  }
};

// Extend Request interface
declare global {
  namespace Express {
    interface Request {
      cacheHealth?: any;
    }
  }
}
