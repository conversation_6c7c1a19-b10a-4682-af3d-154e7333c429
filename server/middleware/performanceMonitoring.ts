import { Request, Response, NextFunction } from 'express';
import { cacheService } from '../services/cacheService';
import { db } from '../config/database-optimized';

interface PerformanceMetrics {
  requestCount: number;
  averageResponseTime: number;
  errorRate: number;
  slowQueries: number;
  cacheHitRate: number;
  activeConnections: number;
  memoryUsage: number;
  cpuUsage: number;
}

interface RequestMetrics {
  method: string;
  path: string;
  statusCode: number;
  responseTime: number;
  timestamp: Date;
  userId?: string;
  companyId?: string;
  userAgent?: string;
  ip: string;
}

class PerformanceMonitor {
  private metrics: Map<string, any> = new Map();
  private requestHistory: RequestMetrics[] = [];
  private maxHistorySize = 10000;

  /**
   * Middleware to track request performance
   */
  public trackRequest() {
    return (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now();
      const originalSend = res.send;

      // Override res.send to capture response time
      res.send = function(data: any) {
        const responseTime = Date.now() - startTime;
        
        // Record request metrics
        const requestMetrics: RequestMetrics = {
          method: req.method,
          path: req.route?.path || req.path,
          statusCode: res.statusCode,
          responseTime,
          timestamp: new Date(),
          userId: (req as any).user?.id,
          companyId: (req as any).user?.companyId,
          userAgent: req.get('User-Agent'),
          ip: req.ip || req.connection.remoteAddress || '',
        };

        // Store metrics
        performanceMonitor.recordRequest(requestMetrics);

        // Log slow requests
        if (responseTime > 1000) {
          console.warn(`Slow request detected: ${req.method} ${req.path} - ${responseTime}ms`);
        }

        // Call original send
        return originalSend.call(this, data);
      };

      next();
    };
  }

  /**
   * Record request metrics
   */
  public recordRequest(metrics: RequestMetrics): void {
    // Add to history
    this.requestHistory.push(metrics);

    // Maintain history size
    if (this.requestHistory.length > this.maxHistorySize) {
      this.requestHistory.shift();
    }

    // Update aggregated metrics
    this.updateAggregatedMetrics(metrics);

    // Store in cache for real-time dashboard
    this.updateCacheMetrics(metrics);
  }

  /**
   * Update aggregated metrics
   */
  private updateAggregatedMetrics(metrics: RequestMetrics): void {
    const key = `${metrics.method}:${metrics.path}`;
    const existing = this.metrics.get(key) || {
      count: 0,
      totalResponseTime: 0,
      errors: 0,
      lastAccessed: new Date(),
    };

    existing.count++;
    existing.totalResponseTime += metrics.responseTime;
    existing.lastAccessed = metrics.timestamp;

    if (metrics.statusCode >= 400) {
      existing.errors++;
    }

    this.metrics.set(key, existing);
  }

  /**
   * Update cache metrics for real-time dashboard
   */
  private async updateCacheMetrics(metrics: RequestMetrics): Promise<void> {
    try {
      const now = new Date();
      const minuteKey = `metrics:${now.getFullYear()}-${now.getMonth()}-${now.getDate()}-${now.getHours()}-${now.getMinutes()}`;

      // Increment request count for this minute
      await cacheService.increment(`${minuteKey}:requests`, 1, { ttl: 3600 });

      // Track response times
      const responseTimes = await cacheService.get(`${minuteKey}:response_times`) || [];
      responseTimes.push(metrics.responseTime);
      
      // Keep only last 100 response times per minute
      if (responseTimes.length > 100) {
        responseTimes.shift();
      }
      
      await cacheService.set(`${minuteKey}:response_times`, responseTimes, { ttl: 3600 });

      // Track errors
      if (metrics.statusCode >= 400) {
        await cacheService.increment(`${minuteKey}:errors`, 1, { ttl: 3600 });
      }

      // Track unique users
      if (metrics.userId) {
        const activeUsers = await cacheService.get(`${minuteKey}:active_users`) || new Set();
        activeUsers.add(metrics.userId);
        await cacheService.set(`${minuteKey}:active_users`, Array.from(activeUsers), { ttl: 3600 });
      }

    } catch (error) {
      console.error('Failed to update cache metrics:', error);
    }
  }

  /**
   * Get current performance metrics
   */
  public async getCurrentMetrics(): Promise<PerformanceMetrics> {
    try {
      const [
        cacheStats,
        dbStats,
        systemStats
      ] = await Promise.all([
        this.getCacheMetrics(),
        this.getDatabaseMetrics(),
        this.getSystemMetrics(),
      ]);

      const recentRequests = this.getRecentRequests(300000); // Last 5 minutes
      const requestCount = recentRequests.length;
      const averageResponseTime = requestCount > 0 
        ? recentRequests.reduce((sum, req) => sum + req.responseTime, 0) / requestCount 
        : 0;
      const errorRate = requestCount > 0 
        ? (recentRequests.filter(req => req.statusCode >= 400).length / requestCount) * 100 
        : 0;

      return {
        requestCount,
        averageResponseTime: Math.round(averageResponseTime),
        errorRate: Math.round(errorRate * 100) / 100,
        slowQueries: dbStats.slowQueries || 0,
        cacheHitRate: cacheStats.hitRate || 0,
        activeConnections: dbStats.activeConnections || 0,
        memoryUsage: systemStats.memoryUsage || 0,
        cpuUsage: systemStats.cpuUsage || 0,
      };
    } catch (error) {
      console.error('Failed to get current metrics:', error);
      return {
        requestCount: 0,
        averageResponseTime: 0,
        errorRate: 0,
        slowQueries: 0,
        cacheHitRate: 0,
        activeConnections: 0,
        memoryUsage: 0,
        cpuUsage: 0,
      };
    }
  }

  /**
   * Get cache performance metrics
   */
  private async getCacheMetrics(): Promise<any> {
    try {
      const stats = cacheService.getStats();
      const redisInfo = await cacheService.getRedisInfo();

      return {
        hitRate: stats.hitRate,
        hits: stats.hits,
        misses: stats.misses,
        errors: stats.errors,
        memoryUsage: redisInfo?.memory?.used_memory_human || 'N/A',
        connectedClients: redisInfo?.clients?.connected_clients || 0,
      };
    } catch (error) {
      console.error('Failed to get cache metrics:', error);
      return {};
    }
  }

  /**
   * Get database performance metrics
   */
  private async getDatabaseMetrics(): Promise<any> {
    try {
      const [
        connectionStats,
        slowQueries,
        tableStats
      ] = await Promise.all([
        db.raw(`
          SELECT count(*) as total_connections,
                 count(*) FILTER (WHERE state = 'active') as active_connections,
                 count(*) FILTER (WHERE state = 'idle') as idle_connections
          FROM pg_stat_activity 
          WHERE datname = current_database()
        `),
        db.raw(`
          SELECT count(*) as slow_queries
          FROM query_performance_log 
          WHERE execution_time_ms > 1000 
            AND created_at > NOW() - INTERVAL '5 minutes'
        `),
        db.raw(`
          SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del
          FROM pg_stat_user_tables 
          ORDER BY (n_tup_ins + n_tup_upd + n_tup_del) DESC 
          LIMIT 5
        `)
      ]);

      return {
        activeConnections: connectionStats.rows[0]?.active_connections || 0,
        totalConnections: connectionStats.rows[0]?.total_connections || 0,
        idleConnections: connectionStats.rows[0]?.idle_connections || 0,
        slowQueries: slowQueries.rows[0]?.slow_queries || 0,
        topTables: tableStats.rows || [],
      };
    } catch (error) {
      console.error('Failed to get database metrics:', error);
      return {};
    }
  }

  /**
   * Get system performance metrics
   */
  private getSystemMetrics(): any {
    try {
      const memUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();

      return {
        memoryUsage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
        external: Math.round(memUsage.external / 1024 / 1024), // MB
        cpuUser: cpuUsage.user,
        cpuSystem: cpuUsage.system,
        uptime: Math.round(process.uptime()),
      };
    } catch (error) {
      console.error('Failed to get system metrics:', error);
      return {};
    }
  }

  /**
   * Get recent requests within time window
   */
  private getRecentRequests(timeWindowMs: number): RequestMetrics[] {
    const cutoff = new Date(Date.now() - timeWindowMs);
    return this.requestHistory.filter(req => req.timestamp >= cutoff);
  }

  /**
   * Get endpoint performance statistics
   */
  public getEndpointStats(): Array<{
    endpoint: string;
    count: number;
    averageResponseTime: number;
    errorRate: number;
    lastAccessed: Date;
  }> {
    const stats: any[] = [];

    for (const [endpoint, metrics] of this.metrics.entries()) {
      stats.push({
        endpoint,
        count: metrics.count,
        averageResponseTime: Math.round(metrics.totalResponseTime / metrics.count),
        errorRate: Math.round((metrics.errors / metrics.count) * 100 * 100) / 100,
        lastAccessed: metrics.lastAccessed,
      });
    }

    return stats.sort((a, b) => b.count - a.count);
  }

  /**
   * Get real-time metrics for dashboard
   */
  public async getRealTimeMetrics(minutes: number = 60): Promise<any> {
    try {
      const metrics = [];
      const now = new Date();

      for (let i = 0; i < minutes; i++) {
        const time = new Date(now.getTime() - (i * 60 * 1000));
        const minuteKey = `metrics:${time.getFullYear()}-${time.getMonth()}-${time.getDate()}-${time.getHours()}-${time.getMinutes()}`;

        const [requests, errors, responseTimes, activeUsers] = await Promise.all([
          cacheService.get(`${minuteKey}:requests`) || 0,
          cacheService.get(`${minuteKey}:errors`) || 0,
          cacheService.get(`${minuteKey}:response_times`) || [],
          cacheService.get(`${minuteKey}:active_users`) || [],
        ]);

        const avgResponseTime = responseTimes.length > 0
          ? responseTimes.reduce((sum: number, time: number) => sum + time, 0) / responseTimes.length
          : 0;

        metrics.unshift({
          timestamp: time.toISOString(),
          requests: Number(requests),
          errors: Number(errors),
          averageResponseTime: Math.round(avgResponseTime),
          activeUsers: Array.isArray(activeUsers) ? activeUsers.length : 0,
          errorRate: requests > 0 ? Math.round((errors / requests) * 100 * 100) / 100 : 0,
        });
      }

      return metrics;
    } catch (error) {
      console.error('Failed to get real-time metrics:', error);
      return [];
    }
  }

  /**
   * Generate performance report
   */
  public async generateReport(hours: number = 24): Promise<any> {
    try {
      const endTime = new Date();
      const startTime = new Date(endTime.getTime() - (hours * 60 * 60 * 1000));

      const recentRequests = this.requestHistory.filter(
        req => req.timestamp >= startTime && req.timestamp <= endTime
      );

      const totalRequests = recentRequests.length;
      const totalErrors = recentRequests.filter(req => req.statusCode >= 400).length;
      const averageResponseTime = totalRequests > 0
        ? recentRequests.reduce((sum, req) => sum + req.responseTime, 0) / totalRequests
        : 0;

      // Group by endpoint
      const endpointStats = new Map();
      recentRequests.forEach(req => {
        const key = `${req.method} ${req.path}`;
        const existing = endpointStats.get(key) || {
          count: 0,
          totalTime: 0,
          errors: 0,
        };

        existing.count++;
        existing.totalTime += req.responseTime;
        if (req.statusCode >= 400) {
          existing.errors++;
        }

        endpointStats.set(key, existing);
      });

      const topEndpoints = Array.from(endpointStats.entries())
        .map(([endpoint, stats]: [string, any]) => ({
          endpoint,
          requests: stats.count,
          averageResponseTime: Math.round(stats.totalTime / stats.count),
          errorRate: Math.round((stats.errors / stats.count) * 100 * 100) / 100,
        }))
        .sort((a, b) => b.requests - a.requests)
        .slice(0, 10);

      // Get current system metrics
      const currentMetrics = await this.getCurrentMetrics();

      return {
        period: {
          start: startTime.toISOString(),
          end: endTime.toISOString(),
          hours,
        },
        summary: {
          totalRequests,
          totalErrors,
          errorRate: totalRequests > 0 ? Math.round((totalErrors / totalRequests) * 100 * 100) / 100 : 0,
          averageResponseTime: Math.round(averageResponseTime),
        },
        topEndpoints,
        currentMetrics,
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Failed to generate performance report:', error);
      throw error;
    }
  }

  /**
   * Clear old metrics data
   */
  public clearOldMetrics(olderThanHours: number = 24): void {
    const cutoff = new Date(Date.now() - (olderThanHours * 60 * 60 * 1000));
    this.requestHistory = this.requestHistory.filter(req => req.timestamp >= cutoff);
    
    console.log(`Cleared metrics older than ${olderThanHours} hours`);
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Middleware function
export const trackPerformance = () => performanceMonitor.trackRequest();

// Cleanup old metrics every hour
setInterval(() => {
  performanceMonitor.clearOldMetrics(24);
}, 60 * 60 * 1000);

export default performanceMonitor;
