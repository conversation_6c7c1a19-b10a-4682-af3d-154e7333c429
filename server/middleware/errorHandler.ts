import type { Request, Response, NextFunction } from "express";
import { ZodError } from "zod";

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export class ValidationError extends Error {
  statusCode = 400;
  isOperational = true;

  constructor(message: string) {
    super(message);
    this.name = "ValidationError";
  }
}

export class NotFoundError extends Error {
  statusCode = 404;
  isOperational = true;

  constructor(message: string = "Resource not found") {
    super(message);
    this.name = "NotFoundError";
  }
}

export class UnauthorizedError extends Error {
  statusCode = 401;
  isOperational = true;

  constructor(message: string = "Unauthorized") {
    super(message);
    this.name = "UnauthorizedError";
  }
}

export class ForbiddenError extends Error {
  statusCode = 403;
  isOperational = true;

  constructor(message: string = "Forbidden") {
    super(message);
    this.name = "ForbiddenError";
  }
}

export class ConflictError extends Error {
  statusCode = 409;
  isOperational = true;

  constructor(message: string) {
    super(message);
    this.name = "ConflictError";
  }
}

export const errorHandler = (
  error: AppError | ZodError | Error,
  _req: Request,
  res: Response,
  _next: NextFunction
) => {
  let statusCode = 500;
  let message = "Internal Server Error";

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    statusCode = 400;
    message = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
  } else if ('statusCode' in error && error.statusCode) {
    statusCode = error.statusCode;
    message = error.message;
  } else {
    message = error.message || "Internal Server Error";
  }

  // Log error details
  console.error(`Error ${statusCode}: ${message}`);
  console.error(error.stack);

  // Don't expose internal errors in production
  const isDevelopment = process.env.NODE_ENV === "development";

  const errorResponse: any = {
    error: {
      message:
        statusCode === 500 && !isDevelopment
          ? "Internal Server Error"
          : message,
      status: statusCode,
    },
  };

  if (isDevelopment) {
    errorResponse.error.stack = error.stack;
  }

  res.status(statusCode).json(errorResponse);
};

export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
