import { Request, Response, NextFunction } from 'express';
import { auditService } from '../services/auditService.js';

// Extend Request interface to include audit context
declare global {
  namespace Express {
    interface Request {
      auditContext?: {
        userId?: string;
        userEmail?: string;
        userRole?: string;
        companyId?: string;
        ipAddress?: string;
        userAgent?: string;
        sessionId?: string;
      };
    }
  }
}

interface AuditMiddlewareOptions {
  // Which HTTP methods to audit
  methods?: string[];
  // Which routes to exclude from auditing
  excludeRoutes?: string[];
  // Which routes to include (if specified, only these will be audited)
  includeRoutes?: string[];
  // Whether to audit successful requests only
  successOnly?: boolean;
  // Whether to capture request/response bodies
  captureBody?: boolean;
  // Risk level for different route patterns
  riskLevels?: {
    [pattern: string]: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  };
}

/**
 * Comprehensive audit middleware for automatic API request logging
 * This uses the new auditing system (separate from audit_trail)
 */
export function createComprehensiveAuditMiddleware(options: AuditMiddlewareOptions = {}) {
  const {
    methods = ['POST', 'PUT', 'PATCH', 'DELETE'],
    excludeRoutes = ['/health', '/ping', '/metrics'],
    includeRoutes = [],
    successOnly = false,
    captureBody = false,
    riskLevels = {
      '/admin': 'HIGH',
      '/users': 'MEDIUM',
      '/companies': 'HIGH',
      '/transactions': 'MEDIUM',
      '/accounts': 'MEDIUM',
      '/reports': 'LOW',
      '/auth': 'MEDIUM'
    }
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip if method not in audit list
    if (!methods.includes(req.method)) {
      return next();
    }

    // Skip if route is excluded
    if (excludeRoutes.some(route => req.path.includes(route))) {
      return next();
    }

    // Skip if includeRoutes is specified and route is not included
    if (includeRoutes.length > 0 && !includeRoutes.some(route => req.path.includes(route))) {
      return next();
    }

    // Extract audit context from request
    const auditContext = extractAuditContext(req);
    req.auditContext = auditContext;

    // Determine risk level based on route
    const riskLevel = determineRiskLevel(req.path, riskLevels);

    // Store original response methods
    const originalSend = res.send;
    const originalJson = res.json;

    let responseBody: any;
    let responseStatusCode: number;

    // Intercept response to capture status and body
    res.send = function(body: any) {
      responseBody = body;
      responseStatusCode = res.statusCode;
      return originalSend.call(this, body);
    };

    res.json = function(body: any) {
      responseBody = body;
      responseStatusCode = res.statusCode;
      return originalJson.call(this, body);
    };

    // Continue with request processing
    next();

    // Log after response is sent
    res.on('finish', async () => {
      try {
        // Skip if successOnly is true and request failed
        if (successOnly && responseStatusCode >= 400) {
          return;
        }

        // Log access attempt using new auditing system
        await auditService.logAccess({
          userId: auditContext.userId,
          action: `${req.method} ${req.path}`,
          resource: req.path,
          success: responseStatusCode < 400,
          failureReason: responseStatusCode >= 400 ? `HTTP ${responseStatusCode}` : undefined,
          ipAddress: auditContext.ipAddress,
          deviceInfo: {
            userAgent: auditContext.userAgent,
            method: req.method,
            statusCode: responseStatusCode,
            requestBody: captureBody ? req.body : undefined,
            responseBody: captureBody ? responseBody : undefined
          }
        });

        // Log data changes for specific operations
        if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method) && responseStatusCode < 400) {
          await logDataChange(req, res, auditContext, riskLevel, responseBody);
        }

      } catch (error) {
        console.error('❌ Comprehensive audit middleware error:', error);
        // Don't throw error to avoid breaking the main request
      }
    });
  };
}

/**
 * Extract audit context from request
 */
function extractAuditContext(req: Request) {
  // Get IP address (handle proxy headers)
  const ipAddress = req.ip || 
    req.headers['x-forwarded-for'] as string ||
    req.headers['x-real-ip'] as string ||
    req.connection?.remoteAddress ||
    'unknown';

  return {
    userId: (req as any).user?.id,
    userEmail: (req as any).user?.email || 'unknown',
    userRole: (req as any).user?.role || 'unknown',
    companyId: (req as any).user?.companyId || req.params?.companyId,
    ipAddress: Array.isArray(ipAddress) ? ipAddress[0] : ipAddress,
    userAgent: req.headers['user-agent'] || 'unknown',
    sessionId: req.sessionID || req.headers['x-session-id'] as string
  };
}

/**
 * Determine risk level based on route pattern
 */
function determineRiskLevel(
  path: string, 
  riskLevels: { [pattern: string]: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' }
): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
  for (const [pattern, level] of Object.entries(riskLevels)) {
    if (path.includes(pattern)) {
      return level;
    }
  }
  return 'LOW';
}

/**
 * Log data changes for CRUD operations using new auditing system
 */
async function logDataChange(
  req: Request, 
  res: Response, 
  auditContext: any, 
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL',
  responseBody: any
) {
  try {
    // Extract table name from route
    const tableName = extractTableName(req.path);
    if (!tableName) return;

    // Extract record ID from response or params
    const recordId = extractRecordId(req, responseBody);
    if (!recordId) return;

    // Determine action type
    const actionType = mapMethodToAction(req.method);
    if (!actionType) return;

    // Log the data change using new auditing system
    await auditService.logDataChange({
      tableName,
      recordId,
      actionType,
      oldValues: req.method === 'PUT' || req.method === 'PATCH' ? req.body.oldValues : undefined,
      newValues: req.method !== 'DELETE' ? req.body : undefined,
      changedFields: req.method === 'PATCH' ? Object.keys(req.body) : undefined,
      userId: auditContext.userId,
      userEmail: auditContext.userEmail,
      userRole: auditContext.userRole,
      companyId: auditContext.companyId,
      ipAddress: auditContext.ipAddress,
      userAgent: auditContext.userAgent,
      sessionId: auditContext.sessionId,
      reason: req.body.reason || `${actionType} operation via API`,
      riskLevel
    });

  } catch (error) {
    console.error('❌ Data change logging error:', error);
  }
}

/**
 * Extract table name from API route
 */
function extractTableName(path: string): string | null {
  // Common API patterns: /api/v1/accounts, /companies/123/accounts
  const patterns = [
    /\/api\/v\d+\/(\w+)/,  // /api/v1/accounts
    /\/(\w+)\/[^\/]+$/,    // /accounts/123
    /\/(\w+)$/             // /accounts
  ];

  for (const pattern of patterns) {
    const match = path.match(pattern);
    if (match) {
      return match[1];
    }
  }

  return null;
}

/**
 * Extract record ID from request or response
 */
function extractRecordId(req: Request, responseBody: any): string | null {
  // Try to get ID from URL params
  if (req.params.id) {
    return req.params.id;
  }

  // Try to get ID from response body
  if (responseBody && typeof responseBody === 'object') {
    if (responseBody.id) return responseBody.id;
    if (responseBody.data && responseBody.data.id) return responseBody.data.id;
  }

  // Try to get ID from request body
  if (req.body && req.body.id) {
    return req.body.id;
  }

  return null;
}

/**
 * Map HTTP method to audit action type
 */
function mapMethodToAction(method: string): 'INSERT' | 'UPDATE' | 'DELETE' | null {
  switch (method) {
    case 'POST': return 'INSERT';
    case 'PUT':
    case 'PATCH': return 'UPDATE';
    case 'DELETE': return 'DELETE';
    default: return null;
  }
}

/**
 * Middleware for financial transaction auditing
 */
export function createFinancialAuditMiddleware() {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Only audit financial routes
    if (!req.path.includes('/transactions') && !req.path.includes('/accounts')) {
      return next();
    }

    const originalSend = res.send;
    let responseBody: any;

    res.send = function(body: any) {
      responseBody = body;
      return originalSend.call(this, body);
    };

    next();

    res.on('finish', async () => {
      try {
        if (res.statusCode < 400 && ['POST', 'PUT', 'PATCH'].includes(req.method)) {
          await logFinancialTransaction(req, responseBody);
        }
      } catch (error) {
        console.error('❌ Financial audit middleware error:', error);
      }
    });
  };
}

/**
 * Log financial transaction for compliance using new auditing system
 */
async function logFinancialTransaction(req: Request, responseBody: any) {
  try {
    const auditContext = req.auditContext;
    if (!auditContext) return;

    // Extract financial data from request/response
    const amount = req.body.amount || responseBody?.amount;
    const currency = req.body.currency || responseBody?.currency || 'TZS';
    const accountId = req.body.accountId || req.params.accountId || responseBody?.accountId;
    const transactionId = responseBody?.id || req.body.id;

    if (!amount || !accountId) return;

    await auditService.logFinancialTransaction({
      transactionId,
      accountId,
      amount: parseFloat(amount),
      currency,
      actionType: `${req.method}_TRANSACTION`,
      authorizationLevel: auditContext.userRole || 'USER',
      approverId: auditContext.userId,
      complianceCheckResult: {
        timestamp: new Date().toISOString(),
        ipAddress: auditContext.ipAddress,
        userAgent: auditContext.userAgent,
        method: req.method,
        route: req.path
      }
    });

  } catch (error) {
    console.error('❌ Financial transaction logging error:', error);
  }
}
