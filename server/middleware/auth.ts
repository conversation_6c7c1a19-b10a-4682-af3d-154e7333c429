import type { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { UnauthorizedError, ForbiddenError } from "./errorHandler";
import { db } from "../config/database";

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    roleId: string;
    companyId?: string;
    permissions: string[];
  };
}

export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1];

    if (!token) {
      throw new UnauthorizedError("Access token required");
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new Error("JWT_SECRET not configured");
    }

    const decoded = jwt.verify(token, jwtSecret) as any;

    // Fetch user details with role and permissions
    const user = await db("users")
      .select("users.id", "users.email", "users.role_id", "roles.permissions")
      .join("roles", "users.role_id", "roles.id")
      .where("users.id", decoded.userId)
      .where("users.is_active", true)
      .first();

    if (!user) {
      throw new UnauthorizedError("Invalid token");
    }

    req.user = {
      id: user.id,
      email: user.email,
      roleId: user.role_id,
      permissions: user.permissions || [],
    };

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new UnauthorizedError("Invalid token"));
    } else {
      next(error);
    }
  }
};

export const requirePermission = (permission: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new UnauthorizedError("Authentication required"));
    }

    if (
      !req.user.permissions.includes(permission) &&
      !req.user.permissions.includes("*")
    ) {
      return next(new ForbiddenError(`Permission required: ${permission}`));
    }

    next();
  };
};

export const requireCompanyAccess = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return next(new UnauthorizedError("Authentication required"));
    }

    const companyId =
      req.params.companyId || req.body.companyId || req.query.companyId;

    if (!companyId) {
      return next(new ForbiddenError("Company ID required"));
    }

    // Check if user has access to this company
    const userCompany = await db("user_companies")
      .where("user_id", req.user.id)
      .where("company_id", companyId)
      .where("is_active", true)
      .first();

    if (!userCompany) {
      return next(new ForbiddenError("Access denied to this company"));
    }

    req.user.companyId = companyId;
    next();
  } catch (error) {
    next(error);
  }
};

// Alias for compatibility
export const requireAuth = authenticateToken;
