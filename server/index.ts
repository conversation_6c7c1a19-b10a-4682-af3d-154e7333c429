import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import * as dotenv from "dotenv";
import { createServer } from "http";

// Load environment variables
const envResult = dotenv.config({ path: ".env" });
if (envResult.error) {
  console.error("Failed to load .env file:", envResult.error);
  // Try alternative path
  const envResult2 = dotenv.config({ path: ".env" });
  if (envResult2.error) {
    console.error(
      "Failed to load .env file from current directory:",
      envResult2.error
    );
  }
}

// Debug environment variables
console.log("Environment variables loaded:");
console.log("DB_USER:", process.env.DB_USER);
console.log("DB_NAME:", process.env.DB_NAME);
console.log("DB_HOST:", process.env.DB_HOST);

// Import routes
import authRoutes from "./routes/auth";
import userRoutes from "./routes/users";
import roleRoutes from "./routes/roles";
import companyRoutes from "./routes/companies";
import accountRoutes from "./routes/accounts";
import transactionRoutes from "./routes/transactions";
import transactionTemplateRoutes from "./routes/transactionTemplates";
import contactRoutes from "./routes/contacts";
import invoiceRoutes from "./routes/invoices";
import reportRoutes from "./routes/reports";
import auditRoutes from "./routes/audit";
import settingsRoutes from "./routes/settings";
import dashboardRoutes from "./routes/dashboard";
import taxRoutes from "./routes/tax";
import exportRoutes from "./routes/exports";
import bankRoutes from "./routes/banks";
import paymentRoutes from "./routes/payments";
import workflowRoutes from "./routes/workflows";
import bankReconciliationRoutes from "./routes/bankReconciliation";
import permissionRoutes from "./routes/permissions";
import traRoutes from "./routes/tra";
import efdRoutes from "./routes/efd";
import payrollRoutes from "./routes/localTax";
import customReportsRoutes from "./routes/customReports";

// Import middleware
import { errorHandler } from "./middleware/errorHandler";
import { requestLogger } from "./middleware/requestLogger";
import { rateLimiter } from "./middleware/rateLimiter";

// Import database
// import { initializeDatabase } from "./config/database";

const app = express();
const PORT = process.env.PORT || 3002;
console.log(
  "Starting server on port",
  PORT,
  "- permissions temporarily disabled - contacts ready"
);

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
  })
);

// CORS configuration
app.use(
  cors({
    origin: process.env.CORS_ORIGIN || "http://localhost:5173",
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

// Rate limiting
app.use(rateLimiter);

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Logging middleware
app.use(morgan("combined"));
app.use(requestLogger);

// Health check endpoint
app.get("/health", (_req, res) => {
  res.status(200).json({
    status: "OK",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || "development",
  });
});

// API routes
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/roles", roleRoutes);
app.use("/api/companies", companyRoutes);
app.use("/api/accounts", accountRoutes);
app.use("/api/transactions", transactionRoutes);
app.use("/api/transaction-templates", transactionTemplateRoutes);
app.use("/api/contacts", contactRoutes);
app.use("/api/invoices", invoiceRoutes);
app.use("/api/reports", reportRoutes);
app.use("/api/audit", auditRoutes);
app.use("/api/settings", settingsRoutes);
app.use("/api/dashboard", dashboardRoutes);
app.use("/api/tax", taxRoutes);
app.use("/api/exports", exportRoutes);
app.use("/api/banks", bankRoutes);
app.use("/api/payments", paymentRoutes);
app.use("/api/workflows", workflowRoutes);
app.use("/api/reconciliation", bankReconciliationRoutes);
app.use("/api/tra", traRoutes);
app.use("/api/efd", efdRoutes);
app.use("/api/payroll", payrollRoutes);
app.use("/api/permissions", permissionRoutes);
app.use("/api/custom-reports", customReportsRoutes);

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: "Not Found",
    message: `Route ${req.originalUrl} not found`,
  });
});

// Error handling middleware
app.use(errorHandler);

// Initialize database and start server
async function startServer() {
  try {
    // await initializeDatabase();
    console.log("✅ Database initialization skipped for now");

    const server = createServer(app);

    server.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV || "development"}`);
      console.log(`🔗 API URL: http://localhost:${PORT}`);
    });

    // Graceful shutdown
    process.on("SIGTERM", () => {
      console.log("SIGTERM received, shutting down gracefully");
      server.close(() => {
        console.log("Process terminated");
        process.exit(0);
      });
    });

    process.on("SIGINT", () => {
      console.log("SIGINT received, shutting down gracefully");
      server.close(() => {
        console.log("Process terminated");
        process.exit(0);
      });
    });
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
  }
}

startServer();
