import knex from "knex";
import * as dotenv from "dotenv";
import path from "path";

// Load environment variables first - try multiple paths
const envPaths = [".env", "../.env", ".env.test"];
for (const envPath of envPaths) {
  const result = dotenv.config({ path: envPath });
  if (!result.error) {
    console.log(`✅ Loaded environment from ${envPath}`);
    break;
  }
}

import config from "../knexfile";

const environment = process.env.NODE_ENV || "development";
console.log(`🔧 Database environment: ${environment}`);

const dbConfig = config[environment];

if (!dbConfig) {
  throw new Error(`No database configuration found for environment: ${environment}`);
}

export const db = knex(dbConfig);

export async function initializeDatabase() {
  try {
    // Test database connection
    await db.raw("SELECT 1");
    console.log("✅ Database connection established");

    // Run migrations
    const [batchNo, log] = await db.migrate.latest();
    if (log.length === 0) {
      console.log("✅ Database is up to date");
    } else {
      console.log(`✅ Ran ${log.length} migrations in batch ${batchNo}`);
      log.forEach((migration: string) => console.log(`  - ${migration}`));
    }

    return db;
  } catch (error) {
    console.error("❌ Database initialization failed:", error);
    throw error;
  }
}

export async function closeDatabase() {
  await db.destroy();
  console.log("✅ Database connection closed");
}

export default db;
