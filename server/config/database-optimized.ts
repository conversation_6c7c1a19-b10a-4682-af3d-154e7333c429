import knex from 'knex';
import { Pool } from 'pg';

// Enhanced database configuration with connection pooling and optimization
const databaseConfig = {
  client: 'postgresql',
  connection: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'nyotabalance',
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  },
  pool: {
    min: parseInt(process.env.DB_POOL_MIN || '5'),
    max: parseInt(process.env.DB_POOL_MAX || '20'),
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 100,
    propagateCreateError: false,
  },
  migrations: {
    directory: './migrations',
    tableName: 'knex_migrations',
  },
  seeds: {
    directory: './seeds',
  },
  // Performance optimizations
  acquireConnectionTimeout: 30000,
  asyncStackTraces: process.env.NODE_ENV === 'development',
  debug: process.env.NODE_ENV === 'development',
};

// Create optimized database instance
export const db = knex(databaseConfig);

// Connection health monitoring
export class DatabaseHealthMonitor {
  private static instance: DatabaseHealthMonitor;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private connectionStats = {
    totalConnections: 0,
    activeConnections: 0,
    idleConnections: 0,
    errors: 0,
    lastError: null as Error | null,
    lastHealthCheck: new Date(),
  };

  public static getInstance(): DatabaseHealthMonitor {
    if (!DatabaseHealthMonitor.instance) {
      DatabaseHealthMonitor.instance = new DatabaseHealthMonitor();
    }
    return DatabaseHealthMonitor.instance;
  }

  public startMonitoring(): void {
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, 30000); // Check every 30 seconds

    console.log('Database health monitoring started');
  }

  public stopMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
    console.log('Database health monitoring stopped');
  }

  private async performHealthCheck(): Promise<void> {
    try {
      const startTime = Date.now();
      
      // Simple health check query
      await db.raw('SELECT 1');
      
      const responseTime = Date.now() - startTime;
      
      // Update connection stats
      const pool = (db as any).client.pool;
      this.connectionStats = {
        totalConnections: pool.numUsed() + pool.numFree(),
        activeConnections: pool.numUsed(),
        idleConnections: pool.numFree(),
        errors: this.connectionStats.errors,
        lastError: null,
        lastHealthCheck: new Date(),
      };

      // Log warning if response time is high
      if (responseTime > 1000) {
        console.warn(`Database health check slow: ${responseTime}ms`);
      }

      // Log warning if connection pool is getting full
      if (this.connectionStats.activeConnections > 15) {
        console.warn(`High database connection usage: ${this.connectionStats.activeConnections}/20`);
      }

    } catch (error) {
      this.connectionStats.errors++;
      this.connectionStats.lastError = error as Error;
      console.error('Database health check failed:', error);
    }
  }

  public getConnectionStats() {
    return { ...this.connectionStats };
  }

  public async getDetailedStats() {
    try {
      // Get database-specific statistics
      const [
        connectionCount,
        activeQueries,
        databaseSize,
        tableStats
      ] = await Promise.all([
        db.raw(`
          SELECT count(*) as total_connections,
                 count(*) FILTER (WHERE state = 'active') as active_connections,
                 count(*) FILTER (WHERE state = 'idle') as idle_connections
          FROM pg_stat_activity 
          WHERE datname = current_database()
        `),
        db.raw(`
          SELECT query, state, query_start, state_change
          FROM pg_stat_activity 
          WHERE datname = current_database() 
            AND state = 'active' 
            AND query NOT LIKE '%pg_stat_activity%'
        `),
        db.raw(`
          SELECT pg_size_pretty(pg_database_size(current_database())) as size
        `),
        db.raw(`
          SELECT schemaname, tablename, 
                 pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                 n_tup_ins as inserts,
                 n_tup_upd as updates,
                 n_tup_del as deletes
          FROM pg_stat_user_tables 
          ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC 
          LIMIT 10
        `)
      ]);

      return {
        connections: connectionCount.rows[0],
        activeQueries: activeQueries.rows,
        databaseSize: databaseSize.rows[0],
        largestTables: tableStats.rows,
        poolStats: this.connectionStats,
      };
    } catch (error) {
      console.error('Failed to get detailed database stats:', error);
      return null;
    }
  }
}

// Query optimization utilities
export class QueryOptimizer {
  /**
   * Add query timing and logging for slow queries
   */
  public static enableQueryLogging(): void {
    db.on('query', (query) => {
      const startTime = Date.now();
      
      query.response = query.response || {};
      query.response.then = function(resolve: any, reject: any) {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // Log slow queries (> 1 second)
        if (duration > 1000) {
          console.warn(`Slow query detected (${duration}ms):`, {
            sql: query.sql,
            bindings: query.bindings,
            duration,
          });
        }
        
        // Log very slow queries (> 5 seconds) as errors
        if (duration > 5000) {
          console.error(`Very slow query detected (${duration}ms):`, {
            sql: query.sql,
            bindings: query.bindings,
            duration,
          });
        }
        
        return Promise.prototype.then.call(this, resolve, reject);
      };
    });
  }

  /**
   * Create optimized pagination query
   */
  public static paginateQuery(
    query: any,
    page: number = 1,
    limit: number = 50,
    maxLimit: number = 1000
  ) {
    // Enforce reasonable limits
    const safeLimit = Math.min(Math.max(limit, 1), maxLimit);
    const safePage = Math.max(page, 1);
    const offset = (safePage - 1) * safeLimit;

    return {
      query: query.limit(safeLimit).offset(offset),
      pagination: {
        page: safePage,
        limit: safeLimit,
        offset,
      },
    };
  }

  /**
   * Create cursor-based pagination for better performance on large datasets
   */
  public static cursorPaginate(
    query: any,
    cursor?: string,
    limit: number = 50,
    orderBy: string = 'created_at',
    direction: 'asc' | 'desc' = 'desc'
  ) {
    const safeLimit = Math.min(Math.max(limit, 1), 1000);
    
    let paginatedQuery = query.orderBy(orderBy, direction).limit(safeLimit + 1);
    
    if (cursor) {
      const operator = direction === 'desc' ? '<' : '>';
      paginatedQuery = paginatedQuery.where(orderBy, operator, cursor);
    }
    
    return paginatedQuery;
  }

  /**
   * Optimize transaction queries with proper indexes
   */
  public static optimizeTransactionQuery(baseQuery: any, filters: any = {}) {
    let query = baseQuery;

    // Use indexes efficiently
    if (filters.companyId) {
      query = query.where('company_id', filters.companyId);
    }

    if (filters.status) {
      query = query.where('status', filters.status);
    }

    if (filters.dateFrom || filters.dateTo) {
      if (filters.dateFrom) {
        query = query.where('transaction_date', '>=', filters.dateFrom);
      }
      if (filters.dateTo) {
        query = query.where('transaction_date', '<=', filters.dateTo);
      }
    }

    if (filters.accountId) {
      // Join with transaction_entries for account filtering
      query = query
        .join('transaction_entries', 'transactions.id', 'transaction_entries.transaction_id')
        .where('transaction_entries.account_id', filters.accountId)
        .groupBy('transactions.id');
    }

    if (filters.search) {
      query = query.where(function() {
        this.where('description', 'ilike', `%${filters.search}%`)
            .orWhere('reference', 'ilike', `%${filters.search}%`)
            .orWhere('transaction_number', 'ilike', `%${filters.search}%`);
      });
    }

    return query;
  }
}

// Connection lifecycle management
export async function initializeDatabase(): Promise<void> {
  try {
    console.log('Initializing database connection...');
    
    // Test connection
    await db.raw('SELECT 1');
    console.log('Database connection established');
    
    // Enable query logging in development
    if (process.env.NODE_ENV === 'development') {
      QueryOptimizer.enableQueryLogging();
    }
    
    // Start health monitoring
    DatabaseHealthMonitor.getInstance().startMonitoring();
    
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

export async function closeDatabase(): Promise<void> {
  try {
    console.log('Closing database connection...');
    
    // Stop health monitoring
    DatabaseHealthMonitor.getInstance().stopMonitoring();
    
    // Close connection pool
    await db.destroy();
    console.log('Database connection closed');
    
  } catch (error) {
    console.error('Error closing database connection:', error);
    throw error;
  }
}

// Export optimized database instance
export default db;
