import request from 'supertest';
import express from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { TestHelpers, TEST_DATA } from './utils/testHelpers';
import authRoutes from '../routes/auth';
import { errorHandler } from '../middleware/errorHandler';

// Create test app with proper middleware
const app = express();
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Auth routes already include authentication middleware where needed
app.use('/api/auth', authRoutes);
app.use(errorHandler);

describe('Authentication Routes', () => {
  beforeEach(async () => {
    await TestHelpers.cleanDatabase();
  });

  afterAll(async () => {
    await TestHelpers.cleanDatabase();
  });

  describe('POST /api/auth/login', () => {
    it('should login with valid credentials', async () => {
      // Create test user
      const testUser = await TestHelpers.createTestUser(
        TEST_DATA.VALID_EMAIL,
        TEST_DATA.VALID_PASSWORD
      );

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: TEST_DATA.VALID_EMAIL,
          password: TEST_DATA.VALID_PASSWORD
        })
        .expect(200);

      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(TEST_DATA.VALID_EMAIL);
      expect(response.body.user).not.toHaveProperty('passwordHash');
    });

    it('should reject invalid email', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: TEST_DATA.VALID_PASSWORD
        })
        .expect(401);

      expect(response.body.error.message).toContain('Invalid credentials');
    });

    it('should reject invalid password', async () => {
      await TestHelpers.createTestUser(
        TEST_DATA.VALID_EMAIL,
        TEST_DATA.VALID_PASSWORD
      );

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: TEST_DATA.VALID_EMAIL,
          password: 'wrongpassword'
        })
        .expect(401);

      expect(response.body.error.message).toContain('Invalid credentials');
    });

    it('should validate email format', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: TEST_DATA.INVALID_EMAIL,
          password: TEST_DATA.VALID_PASSWORD
        })
        .expect(400);

      expect(response.body.error.message).toContain('Invalid email format');
    });

    it('should require password', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: TEST_DATA.VALID_EMAIL
        })
        .expect(400);

      expect(response.body.error.message).toContain('Required');
    });

    it('should reject inactive user', async () => {
      const roleId = await TestHelpers.createTestRole();
      const hashedPassword = await bcrypt.hash(TEST_DATA.VALID_PASSWORD, 12);

      // Create inactive user directly
      const { db } = await import('../config/database');
      await db('users').insert({
        email: TEST_DATA.VALID_EMAIL,
        password_hash: hashedPassword,
        first_name: 'Test',
        last_name: 'User',
        role_id: roleId,
        is_active: false,
        email_verified: true
      });

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: TEST_DATA.VALID_EMAIL,
          password: TEST_DATA.VALID_PASSWORD
        })
        .expect(401);

      expect(response.body.error.message).toContain('Invalid credentials');
    });
  });

  describe('POST /api/auth/register', () => {
    it('should register new user with valid data', async () => {
      // Create a role first
      await TestHelpers.createTestRole('user', ['users:read']);

      const userData = {
        email: '<EMAIL>',
        password: TEST_DATA.VALID_PASSWORD,
        firstName: 'New',
        lastName: 'User',
        phone: '+1234567890'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(userData.email);
      expect(response.body.user.firstName).toBe(userData.firstName);
      expect(response.body.user).not.toHaveProperty('passwordHash');
    });

    it('should reject duplicate email', async () => {
      await TestHelpers.createTestUser(TEST_DATA.VALID_EMAIL);

      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: TEST_DATA.VALID_EMAIL,
          password: TEST_DATA.VALID_PASSWORD,
          firstName: 'Test',
          lastName: 'User'
        })
        .expect(400);

      expect(response.body.error.message).toContain('User with this email already exists');
    });

    it('should validate email format', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: TEST_DATA.INVALID_EMAIL,
          password: TEST_DATA.VALID_PASSWORD,
          firstName: 'Test',
          lastName: 'User'
        })
        .expect(400);

      expect(response.body.error.message).toContain('Invalid email format');
    });

    it('should validate password strength', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: TEST_DATA.WEAK_PASSWORD,
          firstName: 'Test',
          lastName: 'User'
        })
        .expect(400);

      expect(response.body.error.message).toContain('Password must be at least 8 characters');
    });

    it('should require first and last name', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: TEST_DATA.VALID_PASSWORD
        })
        .expect(400);

      expect(response.body.error.message).toContain('Required');
    });
  });

  describe('GET /api/auth/me', () => {
    it('should return current user with valid token', async () => {
      const testUser = await TestHelpers.createTestUser();

      const response = await request(app)
        .get('/api/auth/me')
        .set(TestHelpers.getAuthHeader(testUser.token))
        .expect(200);

      expect(response.body.user.id).toBe(testUser.id);
      expect(response.body.user.email).toBe(testUser.email);
      expect(response.body.user).not.toHaveProperty('passwordHash');
    });

    it('should reject request without token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .expect(401);

      expect(response.body.error.message).toContain('Access token required');
    });

    it('should reject invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set({ Authorization: 'Bearer invalid-token' })
        .expect(401);

      expect(response.body.error.message).toContain('Invalid token');
    });

    it('should reject expired token', async () => {
      const expiredToken = jwt.sign(
        { userId: 'test-id', email: '<EMAIL>' },
        process.env.JWT_SECRET!,
        { expiresIn: '-1h' }
      );

      const response = await request(app)
        .get('/api/auth/me')
        .set({ Authorization: `Bearer ${expiredToken}` })
        .expect(401);

      expect(response.body.error.message).toContain('Invalid token');
    });
  });
});
