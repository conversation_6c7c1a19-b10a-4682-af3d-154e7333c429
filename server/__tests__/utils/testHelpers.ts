import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

// Import database after environment is set
let db: any;

async function getDb() {
  if (!db) {
    const { db: database } = await import('../../config/database');
    db = database;
  }
  return db;
}

export interface TestUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  roleId: string;
  token: string;
}

export interface TestCompany {
  id: string;
  name: string;
  baseCurrency: string;
}

export class TestHelpers {
  /**
   * Clean all test data from database
   */
  static async cleanDatabase(): Promise<void> {
    const database = await getDb();

    // Clean in order to respect foreign key constraints
    const tables = [
      'transaction_entries',
      'transactions',
      'invoices',
      'contacts',
      'accounts',
      'user_companies',
      'users',
      'companies'
      // Don't delete roles - they're needed for tests
    ];

    // Disable foreign key checks temporarily
    await database.raw('SET session_replication_role = replica;');

    try {
      for (const table of tables) {
        await database(table).del();
      }
    } finally {
      // Re-enable foreign key checks
      await database.raw('SET session_replication_role = DEFAULT;');
    }

    // Small delay to ensure cleanup is complete
    await new Promise(resolve => setTimeout(resolve, 10));
  }

  /**
   * Create a complete test setup with user, company, and association
   */
  static async createTestSetup(): Promise<{
    user: TestUser;
    company: TestCompany;
    authHeader: { Authorization: string };
  }> {
    // Create user and company with retry logic
    let user: TestUser;
    let company: TestCompany;
    let attempts = 0;
    const maxAttempts = 3;

    while (attempts < maxAttempts) {
      try {
        user = await this.createTestUser();
        company = await this.createTestCompany();

        // Small delay to ensure database consistency
        await new Promise(resolve => setTimeout(resolve, 50));

        // Associate user with company
        await this.associateUserWithCompany(user.id, company.id, user.roleId);

        return {
          user,
          company,
          authHeader: this.getAuthHeader(user.token)
        };
      } catch (error) {
        attempts++;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.warn(`Test setup attempt ${attempts} failed:`, errorMessage);
        if (attempts >= maxAttempts) {
          throw error;
        }
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    throw new Error('Failed to create test setup after maximum attempts');
  }

  /**
   * Get or create a test role
   */
  static async createTestRole(name: string = 'admin', permissions: string[] = ['*']): Promise<string> {
    const database = await getDb();

    // Try to find existing role first
    const existingRole = await database('roles').where('name', name).first();
    if (existingRole) {
      return existingRole.id;
    }

    // Create new role if it doesn't exist
    const [role] = await database('roles').insert({
      name: `test_${name}_${Date.now()}`, // Make unique
      description: `Test role: ${name}`,
      permissions: JSON.stringify(permissions),
      is_active: true
    }).returning('id');

    return role.id;
  }

  /**
   * Create a test user
   */
  static async createTestUser(
    email?: string,
    password: string = 'password123',
    roleId?: string
  ): Promise<TestUser> {
    if (!roleId) {
      roleId = await this.createTestRole();
    }

    // Generate unique email if not provided
    const uniqueEmail = email || `test-${Date.now()}-${Math.random().toString(36).substring(2, 11)}@example.com`;

    const database = await getDb();
    const hashedPassword = await bcrypt.hash(password, 12);

    const [user] = await database('users').insert({
      email: uniqueEmail,
      password_hash: hashedPassword,
      first_name: 'Test',
      last_name: 'User',
      role_id: roleId,
      is_active: true,
      email_verified: true
    }).returning('*');

    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET!,
      { expiresIn: '1h' }
    );

    return {
      id: user.id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      roleId: user.role_id,
      token
    };
  }

  /**
   * Create a test company
   */
  static async createTestCompany(
    name: string = 'Test Company',
    baseCurrency: string = 'USD'
  ): Promise<TestCompany> {
    const database = await getDb();
    const [company] = await database('companies').insert({
      name,
      legal_name: name,
      base_currency: baseCurrency,
      is_active: true
    }).returning('*');

    return {
      id: company.id,
      name: company.name,
      baseCurrency: company.base_currency
    };
  }

  /**
   * Associate user with company
   */
  static async associateUserWithCompany(
    userId: string,
    companyId: string,
    roleId: string
  ): Promise<void> {
    const database = await getDb();

    // Verify user exists
    const user = await database('users').where('id', userId).first();
    if (!user) {
      throw new Error(`User with id ${userId} does not exist`);
    }

    // Verify company exists
    const company = await database('companies').where('id', companyId).first();
    if (!company) {
      throw new Error(`Company with id ${companyId} does not exist`);
    }

    // Verify role exists
    const role = await database('roles').where('id', roleId).first();
    if (!role) {
      throw new Error(`Role with id ${roleId} does not exist`);
    }

    // Check if association already exists
    const existing = await database('user_companies')
      .where('user_id', userId)
      .where('company_id', companyId)
      .first();

    if (!existing) {
      await database('user_companies').insert({
        user_id: userId,
        company_id: companyId,
        role_id: roleId,
        is_active: true
      });
    }
  }

  /**
   * Create a test account
   */
  static async createTestAccount(
    companyId: string,
    accountData: Partial<{
      code: string;
      name: string;
      accountType: string;
      accountSubtype: string;
      openingBalance: number;
    }> = {}
  ): Promise<string> {
    const database = await getDb();
    const defaultData = {
      code: '1000',
      name: 'Test Cash Account',
      accountType: 'ASSET',
      accountSubtype: 'CURRENT_ASSET',
      openingBalance: 0,
      ...accountData
    };

    const [account] = await database('accounts').insert({
      company_id: companyId,
      code: defaultData.code,
      name: defaultData.name,
      account_type: defaultData.accountType,
      account_subtype: defaultData.accountSubtype,
      opening_balance: defaultData.openingBalance,
      is_active: true
    }).returning('id');

    return account.id;
  }

  /**
   * Create a test transaction
   */
  static async createTestTransaction(
    companyId: string,
    createdBy: string,
    transactionData: Partial<{
      description: string;
      amount: number;
      status: string;
      entries: Array<{
        accountId: string;
        debitAmount: number;
        creditAmount: number;
        description: string;
      }>;
    }> = {}
  ): Promise<string> {
    const database = await getDb();
    const defaultData = {
      description: 'Test Transaction',
      amount: 100,
      status: 'POSTED',
      entries: [],
      ...transactionData
    };

    const [transaction] = await database('transactions').insert({
      company_id: companyId,
      transaction_number: `TEST-${Date.now()}`,
      transaction_date: new Date().toISOString().split('T')[0],
      description: defaultData.description,
      status: defaultData.status,
      total_amount: defaultData.amount,
      created_by: createdBy
    }).returning('id');

    // Create transaction entries if provided
    if (defaultData.entries.length > 0) {
      const entries = defaultData.entries.map((entry, index) => ({
        transaction_id: transaction.id,
        account_id: entry.accountId,
        description: entry.description,
        debit_amount: entry.debitAmount,
        credit_amount: entry.creditAmount,
        line_number: index + 1
      }));

      await database('transaction_entries').insert(entries);
    }

    return transaction.id;
  }

  /**
   * Get authorization header for test user
   */
  static getAuthHeader(token: string): { Authorization: string } {
    return { Authorization: `Bearer ${token}` };
  }

  /**
   * Wait for a specified amount of time
   */
  static async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export commonly used test data
export const TEST_DATA = {
  VALID_EMAIL: '<EMAIL>',
  VALID_PASSWORD: 'password123',
  INVALID_EMAIL: 'invalid-email',
  WEAK_PASSWORD: '123',
  COMPANY_NAME: 'Test Company Ltd',
  ACCOUNT_TYPES: ['ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE'],
  TRANSACTION_STATUSES: ['DRAFT', 'PENDING', 'APPROVED', 'POSTED', 'CANCELLED', 'REVERSED']
};
