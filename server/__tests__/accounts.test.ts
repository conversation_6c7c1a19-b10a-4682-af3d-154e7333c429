import request from 'supertest';
import express from 'express';
import { TestHelpers, TEST_DATA } from './utils/testHelpers';
import accountRoutes from '../routes/accounts';
import { errorHandler } from '../middleware/errorHandler';
import { authenticateToken } from '../middleware/auth';

// Create test app with proper middleware
const app = express();
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Add authentication middleware
app.use('/api/accounts', authenticateToken);
app.use('/api/accounts', accountRoutes);
app.use(errorHandler);

describe('Accounts API', () => {
  let testUser: any;
  let testCompany: any;
  let authHeader: any;

  beforeEach(async () => {
    await TestHelpers.cleanDatabase();

    // Create complete test setup
    const setup = await TestHelpers.createTestSetup();
    testUser = setup.user;
    testCompany = setup.company;
    authHeader = setup.authHeader;
  });

  afterAll(async () => {
    await TestHelpers.cleanDatabase();
  });

  describe('GET /api/accounts/:companyId', () => {
    it('should return accounts for company', async () => {
      // Create test accounts
      await TestHelpers.createTestAccount(testCompany.id, {
        code: '1000',
        name: 'Cash',
        accountType: 'ASSET'
      });
      
      await TestHelpers.createTestAccount(testCompany.id, {
        code: '2000',
        name: 'Accounts Payable',
        accountType: 'LIABILITY'
      });

      const response = await request(app)
        .get(`/api/accounts/${testCompany.id}`)
        .set(authHeader)
        .expect(200);

      expect(response.body).toHaveLength(2);
      expect(response.body[0]).toHaveProperty('code');
      expect(response.body[0]).toHaveProperty('name');
      expect(response.body[0]).toHaveProperty('account_type');
    });

    it('should filter accounts by type', async () => {
      await TestHelpers.createTestAccount(testCompany.id, {
        code: '1000',
        name: 'Cash',
        accountType: 'ASSET'
      });
      
      await TestHelpers.createTestAccount(testCompany.id, {
        code: '2000',
        name: 'Accounts Payable',
        accountType: 'LIABILITY'
      });

      const response = await request(app)
        .get(`/api/accounts/${testCompany.id}?type=ASSET`)
        .set(authHeader)
        .expect(200);

      expect(response.body).toHaveLength(1);
      expect(response.body[0].account_type).toBe('ASSET');
    });

    it('should search accounts by name', async () => {
      await TestHelpers.createTestAccount(testCompany.id, {
        code: '1000',
        name: 'Cash in Bank',
        accountType: 'ASSET'
      });
      
      await TestHelpers.createTestAccount(testCompany.id, {
        code: '1001',
        name: 'Petty Cash',
        accountType: 'ASSET'
      });

      const response = await request(app)
        .get(`/api/accounts/${testCompany.id}?search=Bank`)
        .set(authHeader)
        .expect(200);

      expect(response.body).toHaveLength(1);
      expect(response.body[0].name).toContain('Bank');
    });

    it('should require authentication', async () => {
      // Create a separate company for this test to avoid setup issues
      const tempCompany = await TestHelpers.createTestCompany('Temp Company');

      const response = await request(app)
        .get(`/api/accounts/${tempCompany.id}`)
        .expect(401);

      expect(response.body.error.message).toContain('Access token required');
    });

    it('should require company access', async () => {
      // Create another company
      const otherCompany = await TestHelpers.createTestCompany('Other Company');

      const response = await request(app)
        .get(`/api/accounts/${otherCompany.id}`)
        .set(authHeader)
        .expect(403);

      expect(response.body.error.message).toContain('Access denied to this company');
    });
  });

  describe('POST /api/accounts', () => {
    it('should create new account with valid data', async () => {
      const accountData = {
        companyId: testCompany.id,
        code: '1000',
        name: 'Cash Account',
        description: 'Main cash account',
        accountType: 'ASSET',
        accountSubtype: 'CURRENT_ASSET',
        openingBalance: 1000,
        currency: 'USD'
      };

      const response = await request(app)
        .post('/api/accounts')
        .set(authHeader)
        .send(accountData)
        .expect(201);

      expect(response.body.code).toBe(accountData.code);
      expect(response.body.name).toBe(accountData.name);
      expect(response.body.account_type).toBe(accountData.accountType);
      expect(parseFloat(response.body.opening_balance)).toBe(accountData.openingBalance);
    });

    it('should reject duplicate account code', async () => {
      // Create first account
      await TestHelpers.createTestAccount(testCompany.id, {
        code: '1000',
        name: 'Existing Account'
      });

      const accountData = {
        code: '1000',
        name: 'Duplicate Code Account',
        accountType: 'ASSET'
      };

      const response = await request(app)
        .post('/api/accounts')
        .set(authHeader)
        .send({ ...accountData, companyId: testCompany.id })
        .expect(400);

      expect(response.body.error.message).toContain('Account code already exists');
    });

    it('should validate required fields', async () => {
      // Ensure we have a valid auth header
      if (!authHeader || !authHeader.Authorization) {
        throw new Error('Auth header not properly set up');
      }

      const response = await request(app)
        .post('/api/accounts')
        .set(authHeader)
        .send({ companyId: testCompany.id })
        .expect(400);

      expect(response.body.error.message).toContain('Required');
    });

    it('should validate account type', async () => {
      const accountData = {
        companyId: testCompany.id,
        code: '1000',
        name: 'Test Account',
        accountType: 'INVALID_TYPE'
      };

      const response = await request(app)
        .post('/api/accounts')
        .set(authHeader)
        .send(accountData)
        .expect(400);

      expect(response.body.error.message).toContain('Invalid enum value');
    });

    it('should set default values', async () => {
      const accountData = {
        companyId: testCompany.id,
        code: '1000',
        name: 'Test Account',
        accountType: 'ASSET'
      };

      const response = await request(app)
        .post('/api/accounts')
        .set(authHeader)
        .send(accountData)
        .expect(201);

      expect(parseFloat(response.body.opening_balance)).toBe(0);
      expect(response.body.currency).toBe('USD');
      expect(response.body.is_active).toBe(true);
    });
  });

  describe('PUT /api/accounts/:companyId/:accountId', () => {
    let accountId: string;

    beforeEach(async () => {
      accountId = await TestHelpers.createTestAccount(testCompany.id, {
        code: '1000',
        name: 'Original Account',
        accountType: 'ASSET'
      });
    });

    it('should update account with valid data', async () => {
      const updateData = {
        name: 'Updated Account Name',
        description: 'Updated description'
      };

      const response = await request(app)
        .put(`/api/accounts/${testCompany.id}/${accountId}`)
        .set(authHeader)
        .send(updateData)
        .expect(200);

      expect(response.body.name).toBe(updateData.name);
      expect(response.body.description).toBe(updateData.description);
    });

    it('should allow updating account code to unique value', async () => {
      const updateData = {
        code: '2000',
        name: 'Updated Account'
      };

      const response = await request(app)
        .put(`/api/accounts/${testCompany.id}/${accountId}`)
        .set(authHeader)
        .send(updateData)
        .expect(200);

      // Code should be updated
      expect(response.body.code).toBe('2000');
      expect(response.body.name).toBe(updateData.name);
    });

    it('should return 404 for non-existent account', async () => {
      const fakeId = '********-0000-0000-0000-************';
      
      const response = await request(app)
        .put(`/api/accounts/${testCompany.id}/${fakeId}`)
        .set(authHeader)
        .send({ name: 'Updated Name' })
        .expect(404);

      expect(response.body.error.message).toContain('Account not found');
    });
  });

  describe('DELETE /api/accounts/:companyId/:accountId', () => {
    let accountId: string;

    beforeEach(async () => {
      accountId = await TestHelpers.createTestAccount(testCompany.id, {
        code: '1000',
        name: 'Account to Delete',
        accountType: 'ASSET'
      });
    });

    it('should delete account successfully', async () => {
      const response = await request(app)
        .delete(`/api/accounts/${testCompany.id}/${accountId}`)
        .set(authHeader)
        .expect(200);

      expect(response.body.message).toContain('deleted successfully');

      // Verify account is actually deleted
      const { db } = await import('../config/database');
      const account = await db('accounts').where('id', accountId).first();
      expect(account).toBeUndefined();
    });

    it('should prevent deletion of accounts with transactions', async () => {
      // Create a transaction using this account
      await TestHelpers.createTestTransaction(testCompany.id, testUser.id, {
        entries: [{
          accountId,
          debitAmount: 100,
          creditAmount: 0,
          description: 'Test entry'
        }]
      });

      const response = await request(app)
        .delete(`/api/accounts/${testCompany.id}/${accountId}`)
        .set(authHeader)
        .expect(400);

      expect(response.body.error.message).toContain('Cannot delete account with existing transactions');
    });

    it('should return 404 for non-existent account', async () => {
      const fakeId = '********-0000-0000-0000-************';
      
      const response = await request(app)
        .delete(`/api/accounts/${testCompany.id}/${fakeId}`)
        .set(authHeader)
        .expect(404);

      expect(response.body.error.message).toContain('Account not found');
    });
  });
});
