import { config } from 'dotenv';
import path from 'path';
import '@testing-library/jest-dom';

// Load test environment variables first
config({ path: path.resolve(__dirname, '../../.env.test') });

// Set test environment
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-for-testing-only';
process.env.DB_NAME = 'accounting_system_test';

// Global test timeout
jest.setTimeout(30000);

// Suppress console logs in tests for cleaner output
const originalConsole = global.console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
};

// Global cleanup
afterAll(async () => {
  // Restore console
  global.console = originalConsole;

  // Close any open database connections
  try {
    const { db } = await import('../config/database');
    if (db && typeof db.destroy === 'function') {
      await db.destroy();
    }
  } catch (error) {
    // Ignore cleanup errors
  }
});
