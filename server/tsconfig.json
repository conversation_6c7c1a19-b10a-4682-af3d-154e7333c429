{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2020"], "outDir": "../dist/server", "rootDir": ".", "strict": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "noEmit": false, "types": ["node"]}, "include": ["**/*.ts"], "exclude": ["node_modules", "../dist"]}