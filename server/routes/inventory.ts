import { Router, Request, Response } from "express";
import { z } from "zod";
import { requireAuth } from "../middleware/auth";
import { requireCompanyAccess } from "../middleware/companyAccess";
import { asyncHandler } from "../middleware/asyncHandler";
import { db } from "../config/database";

const router = Router();

// Validation schemas
const inventoryItemSchema = z.object({
  sku: z.string().min(1),
  barcode: z.string().optional(),
  name: z.string().min(1),
  description: z.string().optional(),
  category_id: z.string().uuid().optional(),
  unit_of_measure: z.string().optional(),
  purchase_price: z.number().optional(),
  sales_price: z.number().optional(),
  cost_method: z.enum(["FIFO", "AVG"]).optional(),
  preferred_vendor_id: z.string().uuid().optional(),
  is_active: z.boolean().optional(),
});

// GET /api/inventory/:companyId/items
router.get(
  "/:companyId/items",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const items = await db("inventory_items").where({ company_id: companyId, is_active: true });
    res.json(items);
  })
);

// POST /api/inventory/:companyId/items
router.post(
  "/:companyId/items",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const parsed = inventoryItemSchema.parse(req.body);
    try {
      const [newItem] = await db("inventory_items")
        .insert({ ...parsed, company_id: companyId })
        .returning("*");
      res.status(201).json(newItem);
    } catch (error) {
      console.error("Failed to create inventory item:", error);
      res.status(500).json({ error: "Failed to create inventory item" });
    }
  })
);

// GET /api/inventory/:companyId/items/:id
router.get(
  "/:companyId/items/:id",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, id } = req.params;
    const item = await db("inventory_items").where({ id, company_id: companyId }).first();
    if (!item) return res.status(404).json({ error: "Item not found" });
    res.json(item);
  })
);

// PUT /api/inventory/:companyId/items/:id
router.put(
  "/:companyId/items/:id",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, id } = req.params;
    const updates = inventoryItemSchema.partial().parse(req.body);
    try {
      const [updated] = await db("inventory_items")
        .where({ id, company_id: companyId })
        .update(updates)
        .returning("*");
      if (!updated) return res.status(404).json({ error: "Item not found" });
      res.json(updated);
    } catch (error) {
      console.error("Failed to update inventory item:", error);
      res.status(500).json({ error: "Failed to update inventory item" });
    }
  })
);

// DELETE /api/inventory/:companyId/items/:id
router.delete(
  "/:companyId/items/:id",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, id } = req.params;
    try {
      await db("inventory_items").where({ id, company_id: companyId }).update({ is_active: false });
      res.status(204).send();
    } catch (error) {
      console.error("Failed to delete inventory item:", error);
      res.status(500).json({ error: "Failed to delete inventory item" });
    }
  })
);

// --- Stock by Location ---
// GET /api/inventory/:companyId/items/:id/stock
router.get(
  "/:companyId/items/:id/stock",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, id } = req.params;
    try {
      const stock = await db("inventory_item_locations")
        .join("inventory_locations", "inventory_item_locations.location_id", "inventory_locations.id")
        .select(
          "inventory_locations.id as location_id",
          "inventory_locations.name as location_name",
          "inventory_item_locations.quantity_on_hand",
          "inventory_item_locations.reorder_point",
          "inventory_item_locations.min_level",
          "inventory_item_locations.max_level"
        )
        .where({ "inventory_item_locations.item_id": id, "inventory_locations.company_id": companyId });
      res.json(stock);
    } catch (error) {
      console.error("Failed to fetch stock by location:", error);
      res.status(500).json({ error: "Failed to fetch stock by location" });
    }
  })
);

// --- Stock Adjustment ---
const adjustSchema = z.object({
  location_id: z.string().uuid(),
  quantity: z.number(),
  adjustment_type: z.enum(["INCREASE", "DECREASE", "CORRECTION"]),
  reason: z.string().optional(),
  reference: z.string().optional(),
});

// POST /api/inventory/:companyId/items/:id/adjust
router.post(
  "/:companyId/items/:id/adjust",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, id } = req.params;
    const { id: userId } = req.user;
    const parsed = adjustSchema.parse(req.body);
    try {
      // Update stock
      await db("inventory_item_locations")
        .where({ item_id: id, location_id: parsed.location_id })
        .increment("quantity_on_hand", parsed.quantity);

      // Log adjustment
      await db("inventory_adjustments").insert({
        item_id: id,
        location_id: parsed.location_id,
        adjustment_type: parsed.adjustment_type,
        quantity: parsed.quantity,
        reason: parsed.reason,
        reference: parsed.reference,
        user_id: userId,
        created_at: new Date(),
      });

      // Log as transaction
      await db("inventory_transactions").insert({
        item_id: id,
        location_id: parsed.location_id,
        transaction_type: "adjustment",
        quantity: parsed.quantity,
        cost: null,
        price: null,
        related_doc_type: "adjustment",
        related_doc_id: null,
        created_at: new Date(),
      });
      res.status(200).json({ success: true });
    } catch (error) {
      console.error("Failed to adjust stock:", error);
      res.status(500).json({ error: "Failed to adjust stock" });
    }
  })
);

// --- Stock Transfer ---
const transferSchema = z.object({
  item_id: z.string().uuid(),
  from_location_id: z.string().uuid(),
  to_location_id: z.string().uuid(),
  quantity: z.number(),
  reference: z.string().optional(),
});

// POST /api/inventory/:companyId/transfer
router.post(
  "/:companyId/transfer",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { id: userId } = req.user;
    const parsed = transferSchema.parse(req.body);
    try {
      // Decrement from source
      await db("inventory_item_locations")
        .where({ item_id: parsed.item_id, location_id: parsed.from_location_id })
        .decrement("quantity_on_hand", parsed.quantity);

      // Increment at destination
      await db("inventory_item_locations")
        .where({ item_id: parsed.item_id, location_id: parsed.to_location_id })
        .increment("quantity_on_hand", parsed.quantity);

      // Log as transaction (out)
      await db("inventory_transactions").insert({
        item_id: parsed.item_id,
        location_id: parsed.from_location_id,
        transaction_type: "transfer_out",
        quantity: -parsed.quantity,
        cost: null,
        price: null,
        related_doc_type: "transfer",
        related_doc_id: null,
        created_at: new Date(),
      });

      // Log as transaction (in)
      await db("inventory_transactions").insert({
        item_id: parsed.item_id,
        location_id: parsed.to_location_id,
        transaction_type: "transfer_in",
        quantity: parsed.quantity,
        cost: null,
        price: null,
        related_doc_type: "transfer",
        related_doc_id: null,
        created_at: new Date(),
      });
      res.status(200).json({ success: true });
    } catch (error) {
      console.error("Failed to transfer stock:", error);
      res.status(500).json({ error: "Failed to transfer stock" });
    }
  })
);

// --- Inventory Reporting Endpoints (outline) ---
router.get(
  "/:companyId/reports/valuation",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    // TODO: Implement inventory valuation aggregation
    res.json({});
  })
);

router.get(
  "/:companyId/reports/low-stock",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    // TODO: Implement low stock detection
    res.json({});
  })
);

router.get(
  "/:companyId/reports/activity",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    // TODO: Implement inventory transaction log
    res.json({});
  })
);

export default router;
