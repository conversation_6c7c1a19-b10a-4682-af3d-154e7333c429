import { Router, Request, Response } from 'express';
import { authenticateToken, requireCompanyAccess } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { auditLogger, setAuditData } from '../middleware/auditMiddleware';
import { 
  transactionTemplateService, 
  type CreateTemplateData, 
  type ApplyTemplateData,
  type TemplateCategory 
} from '../services/transactionTemplateService';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    companyId: string;
  };
}

const router = Router();

// GET /api/transaction-templates/categories - Get template categories
router.get(
  '/categories',
  authenticateToken,
  asyncHandler(async (req: Request, res: Response) => {
    const categories = await transactionTemplateService.getCategories();
    res.json(categories);
  })
);

// GET /api/transaction-templates/:companyId - Get all templates
router.get(
  '/:companyId',
  authenticateToken,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const {
      category,
      isActive,
      includeSystemTemplates,
      search,
      page,
      limit
    } = req.query;

    const options = {
      category: category as TemplateCategory,
      isActive: isActive === 'true' ? true : isActive === 'false' ? false : undefined,
      includeSystemTemplates: includeSystemTemplates !== 'false',
      search: search as string,
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined
    };

    const result = await transactionTemplateService.getTemplates(companyId, options);
    res.json(result);
  })
);

// GET /api/transaction-templates/:companyId/:templateId - Get single template
router.get(
  '/:companyId/:templateId',
  authenticateToken,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, templateId } = req.params;

    const template = await transactionTemplateService.getTemplate(templateId, companyId);
    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }

    res.json(template);
  })
);

// POST /api/transaction-templates/:companyId - Create new template
router.post(
  '/:companyId',
  authenticateToken,
  requireCompanyAccess,
  auditLogger,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { companyId } = req.params;
    const templateData: CreateTemplateData = {
      ...req.body,
      companyId
    };

    // Validate required fields
    if (!templateData.name || !templateData.entries || templateData.entries.length === 0) {
      return res.status(400).json({
        error: 'Template name and entries are required'
      });
    }

    // Validate entries balance
    const totalDebits = templateData.entries.reduce((sum, entry) => sum + (entry.debitAmount || 0), 0);
    const totalCredits = templateData.entries.reduce((sum, entry) => sum + (entry.creditAmount || 0), 0);
    
    if (Math.abs(totalDebits - totalCredits) > 0.01) {
      return res.status(400).json({
        error: 'Template entries must be balanced (debits = credits)'
      });
    }

    const template = await transactionTemplateService.createTemplate(templateData, req.user!.id);

    // Set audit data
    setAuditData(req, {
      action: 'CREATE',
      tableName: 'transaction_templates',
      recordId: template.id,
      newValues: template,
      description: `Created transaction template: ${template.name}`
    });

    res.status(201).json(template);
  })
);

// PUT /api/transaction-templates/:companyId/:templateId - Update template
router.put(
  '/:companyId/:templateId',
  authenticateToken,
  requireCompanyAccess,
  auditLogger,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { companyId, templateId } = req.params;

    // Get existing template for audit
    const existingTemplate = await transactionTemplateService.getTemplate(templateId, companyId);
    if (!existingTemplate) {
      return res.status(404).json({ error: 'Template not found' });
    }

    if (existingTemplate.isSystemTemplate) {
      return res.status(403).json({ error: 'Cannot modify system templates' });
    }

    // Validate entries balance if entries are provided
    if (req.body.entries) {
      const totalDebits = req.body.entries.reduce((sum: number, entry: any) => sum + (entry.debitAmount || 0), 0);
      const totalCredits = req.body.entries.reduce((sum: number, entry: any) => sum + (entry.creditAmount || 0), 0);
      
      if (Math.abs(totalDebits - totalCredits) > 0.01) {
        return res.status(400).json({
          error: 'Template entries must be balanced (debits = credits)'
        });
      }
    }

    const updatedTemplate = await transactionTemplateService.updateTemplate(
      templateId,
      req.body,
      req.user!.id,
      companyId
    );

    // Set audit data
    setAuditData(req, {
      action: 'UPDATE',
      tableName: 'transaction_templates',
      recordId: templateId,
      oldValues: existingTemplate,
      newValues: updatedTemplate,
      description: `Updated transaction template: ${updatedTemplate.name}`
    });

    res.json(updatedTemplate);
  })
);

// DELETE /api/transaction-templates/:companyId/:templateId - Delete template
router.delete(
  '/:companyId/:templateId',
  authenticateToken,
  requireCompanyAccess,
  auditLogger,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { companyId, templateId } = req.params;

    // Get existing template for audit
    const existingTemplate = await transactionTemplateService.getTemplate(templateId, companyId);
    if (!existingTemplate) {
      return res.status(404).json({ error: 'Template not found' });
    }

    if (existingTemplate.isSystemTemplate) {
      return res.status(403).json({ error: 'Cannot delete system templates' });
    }

    await transactionTemplateService.deleteTemplate(templateId, companyId);

    // Set audit data
    setAuditData(req, {
      action: 'DELETE',
      tableName: 'transaction_templates',
      recordId: templateId,
      oldValues: existingTemplate,
      description: `Deleted transaction template: ${existingTemplate.name}`
    });

    res.status(204).send();
  })
);

// POST /api/transaction-templates/:companyId/:templateId/apply - Apply template
router.post(
  '/:companyId/:templateId/apply',
  authenticateToken,
  requireCompanyAccess,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { companyId, templateId } = req.params;
    const applyData: ApplyTemplateData = {
      ...req.body,
      templateId
    };

    // Validate required fields
    if (!applyData.transactionDate) {
      return res.status(400).json({
        error: 'Transaction date is required'
      });
    }

    const transactionData = await transactionTemplateService.applyTemplate(
      applyData,
      companyId,
      req.user!.id
    );

    res.json(transactionData);
  })
);

export default router;
