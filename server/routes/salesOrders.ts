import { Router, Request, Response } from "express";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import db from "../config/database";
import { requireAuth, requireCompanyAccess } from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";

const router = Router();

// Validation schemas
const createSalesOrderSchema = z.object({
  companyId: z.string().uuid(),
  customerId: z.string().uuid(),
  orderDate: z.string(),
  deliveryDate: z.string().optional(),
  currency: z.string().default("USD"),
  exchangeRate: z.number().default(1),
  shippingAmount: z.number().default(0),
  billingAddress: z.string().optional(),
  shippingAddress: z.string().optional(),
  deliveryInstructions: z.string().optional(),
  notes: z.string().optional(),
  termsAndConditions: z.string().optional(),
  estimateId: z.string().uuid().optional(),
  lineItems: z.array(
    z.object({
      inventoryItemId: z.string().uuid().optional(),
      itemCode: z.string().optional(),
      description: z.string(),
      unitOfMeasure: z.string().optional(),
      quantityOrdered: z.number(),
      unitPrice: z.number(),
      discountPercentage: z.number().default(0),
      taxRate: z.number().default(0),
      taxCode: z.string().optional(),
      notes: z.string().optional(),
    })
  ),
});

const updateSalesOrderSchema = createSalesOrderSchema
  .partial()
  .omit({ companyId: true });

const salesOrderFiltersSchema = z.object({
  status: z.string().optional(),
  customerId: z.string().uuid().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  deliveryDateFrom: z.string().optional(),
  deliveryDateTo: z.string().optional(),
  search: z.string().optional(),
  page: z.number().default(1),
  limit: z.number().default(50),
});

// GET /api/sales-orders/:companyId - Get all sales orders
router.get(
  "/:companyId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const filters = salesOrderFiltersSchema.parse(req.query);

    let query = db("sales_orders")
      .select(
        "sales_orders.*",
        "contacts.name as customer_name",
        "contacts.display_name as customer_display_name",
        "users.name as created_by_name"
      )
      .leftJoin("contacts", "sales_orders.customer_id", "contacts.id")
      .leftJoin("users", "sales_orders.created_by", "users.id")
      .where("sales_orders.company_id", companyId);

    // Apply filters
    if (filters.status) {
      query = query.where("sales_orders.status", filters.status);
    }
    if (filters.customerId) {
      query = query.where("sales_orders.customer_id", filters.customerId);
    }
    if (filters.dateFrom) {
      query = query.where("sales_orders.order_date", ">=", filters.dateFrom);
    }
    if (filters.dateTo) {
      query = query.where("sales_orders.order_date", "<=", filters.dateTo);
    }
    if (filters.deliveryDateFrom) {
      query = query.where(
        "sales_orders.delivery_date",
        ">=",
        filters.deliveryDateFrom
      );
    }
    if (filters.deliveryDateTo) {
      query = query.where(
        "sales_orders.delivery_date",
        "<=",
        filters.deliveryDateTo
      );
    }
    if (filters.search) {
      query = query.where(function () {
        this.where(
          "sales_orders.sales_order_number",
          "ilike",
          `%${filters.search}%`
        ).orWhere("contacts.name", "ilike", `%${filters.search}%`);
      });
    }

    // Pagination
    const offset = (filters.page - 1) * filters.limit;
    const totalQuery = query.clone().clearSelect().count("* as count").first();
    const dataQuery = query
      .offset(offset)
      .limit(filters.limit)
      .orderBy("sales_orders.order_date", "desc");

    const [total, salesOrders] = await Promise.all([totalQuery, dataQuery]);

    res.json({
      data: salesOrders,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total: Number(total?.count || 0),
        pages: Math.ceil(Number(total?.count || 0) / filters.limit),
      },
    });
  })
);

// GET /api/sales-orders/:companyId/:salesOrderId - Get single sales order
router.get(
  "/:companyId/:salesOrderId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, salesOrderId } = req.params;

    const salesOrder = await db("sales_orders")
      .select(
        "sales_orders.*",
        "contacts.name as customer_name",
        "contacts.display_name as customer_display_name",
        "contacts.email as customer_email",
        "contacts.phone as customer_phone",
        "contacts.billing_address as customer_billing_address",
        "users.name as created_by_name"
      )
      .leftJoin("contacts", "sales_orders.customer_id", "contacts.id")
      .leftJoin("users", "sales_orders.created_by", "users.id")
      .where("sales_orders.company_id", companyId)
      .where("sales_orders.id", salesOrderId)
      .first();

    if (!salesOrder) {
      return res.status(404).json({ error: "Sales order not found" });
    }

    // Get line items
    const lineItems = await db("sales_order_line_items")
      .select(
        "sales_order_line_items.*",
        "inventory_items.name as inventory_item_name",
        "inventory_items.sku as inventory_item_sku"
      )
      .leftJoin(
        "inventory_items",
        "sales_order_line_items.inventory_item_id",
        "inventory_items.id"
      )
      .where("sales_order_id", salesOrderId)
      .orderBy("line_number");

    // Get attachments
    const attachments = await db("sales_order_attachments")
      .select("*")
      .where("sales_order_id", salesOrderId);

    // Get history
    const history = await db("sales_order_history")
      .select("sales_order_history.*", "users.name as changed_by_name")
      .leftJoin("users", "sales_order_history.changed_by", "users.id")
      .where("sales_order_id", salesOrderId)
      .orderBy("changed_at", "desc");

    // Get comments
    const comments = await db("sales_order_comments")
      .select("sales_order_comments.*", "users.name as created_by_name")
      .leftJoin("users", "sales_order_comments.created_by", "users.id")
      .where("sales_order_id", salesOrderId)
      .orderBy("created_at", "desc");

    // Get shipments
    const shipments = await db("sales_order_shipments")
      .select("*")
      .where("sales_order_id", salesOrderId)
      .orderBy("shipped_date", "desc");

    res.json({
      ...salesOrder,
      lineItems,
      attachments,
      history,
      comments,
      shipments,
    });
  })
);

// POST /api/sales-orders - Create sales order
router.post(
  "/",
  requireAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const data = createSalesOrderSchema.parse(req.body);
    const userId = (req as any).user.id;

    const trx = await db.transaction();

    try {
      // Get customer information
      const customer = await trx("contacts")
        .where("id", data.customerId)
        .where("company_id", data.companyId)
        .first();

      if (!customer) {
        return res.status(404).json({ error: "Customer not found" });
      }

      // Generate sales order number
      const salesOrderNumber = await trx.raw(
        "SELECT generate_sales_order_number(?) as number",
        [data.companyId]
      );

      // Calculate totals
      let subtotal = 0;
      let taxAmount = 0;
      let discountAmount = 0;
      const processedLineItems = data.lineItems.map((item, index) => {
        const lineSubtotal = item.quantityOrdered * item.unitPrice;
        const lineDiscountAmount =
          (lineSubtotal * item.discountPercentage) / 100;
        const lineTotal = lineSubtotal - lineDiscountAmount;
        const lineTaxAmount = (lineTotal * item.taxRate) / 100;

        subtotal += lineSubtotal;
        discountAmount += lineDiscountAmount;
        taxAmount += lineTaxAmount;

        return {
          ...item,
          lineNumber: index + 1,
          discountAmount: lineDiscountAmount,
          lineTotal,
          taxAmount: lineTaxAmount,
        };
      });

      const totalAmount =
        subtotal - discountAmount + taxAmount + data.shippingAmount;

      // Create sales order
      const salesOrderId = uuidv4();
      await trx("sales_orders").insert({
        id: salesOrderId,
        company_id: data.companyId,
        customer_id: data.customerId,
        sales_order_number: salesOrderNumber.rows[0].number,
        order_date: data.orderDate,
        delivery_date: data.deliveryDate,
        currency: data.currency,
        exchange_rate: data.exchangeRate,
        subtotal,
        tax_amount: taxAmount,
        discount_amount: discountAmount,
        shipping_amount: data.shippingAmount,
        total_amount: totalAmount,
        customer_name: customer.display_name || customer.name,
        customer_email: customer.email,
        customer_phone: customer.phone,
        customer_address: customer.billing_address,
        billing_address: data.billingAddress,
        shipping_address: data.shippingAddress,
        delivery_instructions: data.deliveryInstructions,
        notes: data.notes,
        terms_and_conditions: data.termsAndConditions,
        estimate_id: data.estimateId,
        converted_from_estimate_at: data.estimateId ? new Date() : null,
        created_by: userId,
      });

      // Create line items
      const lineItemsToInsert = processedLineItems.map((item) => ({
        id: uuidv4(),
        sales_order_id: salesOrderId,
        line_number: item.lineNumber,
        inventory_item_id: item.inventoryItemId,
        item_code: item.itemCode,
        description: item.description,
        unit_of_measure: item.unitOfMeasure,
        quantity_ordered: item.quantityOrdered,
        unit_price: item.unitPrice,
        discount_percentage: item.discountPercentage,
        discount_amount: item.discountAmount,
        line_total: item.lineTotal,
        tax_rate: item.taxRate,
        tax_amount: item.taxAmount,
        tax_code: item.taxCode,
        notes: item.notes,
      }));

      await trx("sales_order_line_items").insert(lineItemsToInsert);

      // Create history entry
      await trx("sales_order_history").insert({
        id: uuidv4(),
        sales_order_id: salesOrderId,
        old_status: null,
        new_status: "DRAFT",
        notes: data.estimateId
          ? "Sales order created from estimate"
          : "Sales order created",
        changed_by: userId,
      });

      // If created from estimate, update estimate status
      if (data.estimateId) {
        await trx("estimates").where("id", data.estimateId).update({
          status: "CONVERTED",
          converted_to_sales_order_id: salesOrderId,
          converted_at: new Date(),
          updated_by: userId,
        });
      }

      await trx.commit();

      // Fetch the created sales order with related data
      const createdSalesOrder = await db("sales_orders")
        .select(
          "sales_orders.*",
          "contacts.name as customer_name",
          "contacts.display_name as customer_display_name"
        )
        .leftJoin("contacts", "sales_orders.customer_id", "contacts.id")
        .where("sales_orders.id", salesOrderId)
        .first();

      res.status(201).json(createdSalesOrder);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// PUT /api/sales-orders/:companyId/:salesOrderId - Update sales order
router.put(
  "/:companyId/:salesOrderId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, salesOrderId } = req.params;
    const data = updateSalesOrderSchema.parse(req.body);
    const userId = (req as any).user.id;

    const trx = await db.transaction();

    try {
      // Check if sales order exists and can be edited
      const existingSalesOrder = await trx("sales_orders")
        .where("id", salesOrderId)
        .where("company_id", companyId)
        .first();

      if (!existingSalesOrder) {
        return res.status(404).json({ error: "Sales order not found" });
      }

      if (!["DRAFT", "CONFIRMED"].includes(existingSalesOrder.status)) {
        return res.status(400).json({
          error: "Cannot edit sales order in current status",
        });
      }

      // Update sales order basic info
      const updateData: any = {
        updated_by: userId,
        updated_at: new Date(),
      };

      if (data.customerId) {
        const customer = await trx("contacts")
          .where("id", data.customerId)
          .where("company_id", companyId)
          .first();

        if (!customer) {
          return res.status(404).json({ error: "Customer not found" });
        }

        updateData.customer_id = data.customerId;
        updateData.customer_name = customer.display_name || customer.name;
        updateData.customer_email = customer.email;
        updateData.customer_phone = customer.phone;
        updateData.customer_address = customer.billing_address;
      }

      if (data.orderDate) updateData.order_date = data.orderDate;
      if (data.deliveryDate) updateData.delivery_date = data.deliveryDate;
      if (data.currency) updateData.currency = data.currency;
      if (data.exchangeRate) updateData.exchange_rate = data.exchangeRate;
      if (data.shippingAmount !== undefined)
        updateData.shipping_amount = data.shippingAmount;
      if (data.billingAddress !== undefined)
        updateData.billing_address = data.billingAddress;
      if (data.shippingAddress !== undefined)
        updateData.shipping_address = data.shippingAddress;
      if (data.deliveryInstructions !== undefined)
        updateData.delivery_instructions = data.deliveryInstructions;
      if (data.notes !== undefined) updateData.notes = data.notes;
      if (data.termsAndConditions !== undefined)
        updateData.terms_and_conditions = data.termsAndConditions;

      // Handle line items if provided
      if (data.lineItems) {
        // Delete existing line items
        await trx("sales_order_line_items")
          .where("sales_order_id", salesOrderId)
          .del();

        // Calculate new totals
        let subtotal = 0;
        let taxAmount = 0;
        let discountAmount = 0;
        const processedLineItems = data.lineItems.map((item, index) => {
          const lineSubtotal = item.quantityOrdered * item.unitPrice;
          const lineDiscountAmount =
            (lineSubtotal * (item.discountPercentage || 0)) / 100;
          const lineTotal = lineSubtotal - lineDiscountAmount;
          const lineTaxAmount = (lineTotal * (item.taxRate || 0)) / 100;

          subtotal += lineSubtotal;
          discountAmount += lineDiscountAmount;
          taxAmount += lineTaxAmount;

          return {
            ...item,
            lineNumber: index + 1,
            discountAmount: lineDiscountAmount,
            lineTotal,
            taxAmount: lineTaxAmount,
          };
        });

        const totalAmount =
          subtotal -
          discountAmount +
          taxAmount +
          (data.shippingAmount || existingSalesOrder.shipping_amount);

        // Update totals
        updateData.subtotal = subtotal;
        updateData.tax_amount = taxAmount;
        updateData.discount_amount = discountAmount;
        updateData.total_amount = totalAmount;

        // Insert new line items
        const lineItemsToInsert = processedLineItems.map((item) => ({
          id: uuidv4(),
          sales_order_id: salesOrderId,
          line_number: item.lineNumber,
          inventory_item_id: item.inventoryItemId,
          item_code: item.itemCode,
          description: item.description,
          unit_of_measure: item.unitOfMeasure,
          quantity_ordered: item.quantityOrdered,
          unit_price: item.unitPrice,
          discount_percentage: item.discountPercentage || 0,
          discount_amount: item.discountAmount,
          line_total: item.lineTotal,
          tax_rate: item.taxRate || 0,
          tax_amount: item.taxAmount,
          tax_code: item.taxCode,
          notes: item.notes,
        }));

        await trx("sales_order_line_items").insert(lineItemsToInsert);
      }

      // Update sales order
      await trx("sales_orders").where("id", salesOrderId).update(updateData);

      // Create history entry
      await trx("sales_order_history").insert({
        id: uuidv4(),
        sales_order_id: salesOrderId,
        old_status: existingSalesOrder.status,
        new_status: existingSalesOrder.status,
        old_amount: existingSalesOrder.total_amount,
        new_amount: updateData.total_amount || existingSalesOrder.total_amount,
        notes: "Sales order updated",
        changed_by: userId,
      });

      await trx.commit();

      // Fetch updated sales order
      const updatedSalesOrder = await db("sales_orders")
        .select(
          "sales_orders.*",
          "contacts.name as customer_name",
          "contacts.display_name as customer_display_name"
        )
        .leftJoin("contacts", "sales_orders.customer_id", "contacts.id")
        .where("sales_orders.id", salesOrderId)
        .first();

      res.json(updatedSalesOrder);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// POST /api/sales-orders/:companyId/:salesOrderId/status - Update sales order status
router.post(
  "/:companyId/:salesOrderId/status",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, salesOrderId } = req.params;
    const { status, notes } = req.body;
    const userId = (req as any).user.id;

    const validStatuses = [
      "DRAFT",
      "CONFIRMED",
      "IN_PROGRESS",
      "SHIPPED",
      "DELIVERED",
      "COMPLETED",
      "CANCELLED",
    ];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: "Invalid status" });
    }

    const trx = await db.transaction();

    try {
      const salesOrder = await trx("sales_orders")
        .where("id", salesOrderId)
        .where("company_id", companyId)
        .first();

      if (!salesOrder) {
        return res.status(404).json({ error: "Sales order not found" });
      }

      // Update status
      await trx("sales_orders")
        .where("id", salesOrderId)
        .update({
          status,
          updated_by: userId,
          updated_at: new Date(),
          ...(status === "SHIPPED" && { shipped_at: new Date() }),
          ...(status === "DELIVERED" && { delivered_at: new Date() }),
        });

      // Create history entry
      await trx("sales_order_history").insert({
        id: uuidv4(),
        sales_order_id: salesOrderId,
        old_status: salesOrder.status,
        new_status: status,
        notes: notes || `Status changed to ${status}`,
        changed_by: userId,
      });

      await trx.commit();

      res.json({
        message: "Sales order status updated successfully",
        status,
      });
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// DELETE /api/sales-orders/:companyId/:salesOrderId - Delete sales order
router.delete(
  "/:companyId/:salesOrderId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, salesOrderId } = req.params;

    const salesOrder = await db("sales_orders")
      .where("id", salesOrderId)
      .where("company_id", companyId)
      .first();

    if (!salesOrder) {
      return res.status(404).json({ error: "Sales order not found" });
    }

    if (!["DRAFT", "CANCELLED"].includes(salesOrder.status)) {
      return res.status(400).json({
        error: "Cannot delete sales order in current status",
      });
    }

    await db("sales_orders").where("id", salesOrderId).del();

    res.json({ message: "Sales order deleted successfully" });
  })
);

// POST /api/sales-orders/:companyId/:salesOrderId/convert-from-estimate - Convert estimate to sales order
router.post(
  "/:companyId/convert-from-estimate/:estimateId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, estimateId } = req.params;
    const userId = (req as any).user.id;

    const trx = await db.transaction();

    try {
      const estimate = await trx("estimates")
        .where("id", estimateId)
        .where("company_id", companyId)
        .first();

      if (!estimate) {
        return res.status(404).json({ error: "Estimate not found" });
      }

      if (estimate.status !== "ACCEPTED") {
        return res.status(400).json({
          error: "Can only convert accepted estimates to sales orders",
        });
      }

      // Get estimate line items
      const estimateLineItems = await trx("estimate_line_items").where(
        "estimate_id",
        estimateId
      );

      // Create sales order data from estimate
      const salesOrderData = {
        companyId,
        customerId: estimate.customer_id,
        orderDate: new Date().toISOString().split("T")[0],
        deliveryDate: undefined,
        currency: estimate.currency,
        exchangeRate: estimate.exchange_rate,
        shippingAmount: 0,
        billingAddress: estimate.billing_address,
        shippingAddress: estimate.shipping_address,
        notes: estimate.notes,
        termsAndConditions: estimate.terms_and_conditions,
        estimateId: estimateId,
        lineItems: estimateLineItems.map((item) => ({
          inventoryItemId: item.inventory_item_id,
          itemCode: item.item_code,
          description: item.description,
          unitOfMeasure: item.unit_of_measure,
          quantityOrdered: item.quantity,
          unitPrice: item.unit_price,
          discountPercentage: item.discount_percentage,
          taxRate: item.tax_rate,
          taxCode: item.tax_code,
          notes: item.notes,
        })),
      };

      // Use the create sales order logic
      const createRequest = { body: salesOrderData, user: { id: userId } };

      // Generate sales order number
      const salesOrderNumber = await trx.raw(
        "SELECT generate_sales_order_number(?) as number",
        [companyId]
      );

      // Calculate totals
      let subtotal = 0;
      let taxAmount = 0;
      let discountAmount = 0;
      const processedLineItems = salesOrderData.lineItems.map((item, index) => {
        const lineSubtotal = item.quantityOrdered * item.unitPrice;
        const lineDiscountAmount =
          (lineSubtotal * item.discountPercentage) / 100;
        const lineTotal = lineSubtotal - lineDiscountAmount;
        const lineTaxAmount = (lineTotal * item.taxRate) / 100;

        subtotal += lineSubtotal;
        discountAmount += lineDiscountAmount;
        taxAmount += lineTaxAmount;

        return {
          ...item,
          lineNumber: index + 1,
          discountAmount: lineDiscountAmount,
          lineTotal,
          taxAmount: lineTaxAmount,
        };
      });

      const totalAmount =
        subtotal - discountAmount + taxAmount + salesOrderData.shippingAmount;

      // Create sales order
      const salesOrderId = uuidv4();
      await trx("sales_orders").insert({
        id: salesOrderId,
        company_id: companyId,
        customer_id: salesOrderData.customerId,
        sales_order_number: salesOrderNumber.rows[0].number,
        order_date: salesOrderData.orderDate,
        delivery_date: salesOrderData.deliveryDate,
        currency: salesOrderData.currency,
        exchange_rate: salesOrderData.exchangeRate,
        subtotal,
        tax_amount: taxAmount,
        discount_amount: discountAmount,
        shipping_amount: salesOrderData.shippingAmount,
        total_amount: totalAmount,
        customer_name: estimate.customer_name,
        customer_email: estimate.customer_email,
        customer_phone: estimate.customer_phone,
        customer_address: estimate.customer_address,
        billing_address: salesOrderData.billingAddress,
        shipping_address: salesOrderData.shippingAddress,
        notes: salesOrderData.notes,
        terms_and_conditions: salesOrderData.termsAndConditions,
        estimate_id: estimateId,
        converted_from_estimate_at: new Date(),
        created_by: userId,
      });

      // Create line items
      const lineItemsToInsert = processedLineItems.map((item) => ({
        id: uuidv4(),
        sales_order_id: salesOrderId,
        line_number: item.lineNumber,
        inventory_item_id: item.inventoryItemId,
        item_code: item.itemCode,
        description: item.description,
        unit_of_measure: item.unitOfMeasure,
        quantity_ordered: item.quantityOrdered,
        unit_price: item.unitPrice,
        discount_percentage: item.discountPercentage,
        discount_amount: item.discountAmount,
        line_total: item.lineTotal,
        tax_rate: item.taxRate,
        tax_amount: item.taxAmount,
        tax_code: item.taxCode,
        notes: item.notes,
      }));

      await trx("sales_order_line_items").insert(lineItemsToInsert);

      // Update estimate status
      await trx("estimates").where("id", estimateId).update({
        status: "CONVERTED",
        converted_to_sales_order_id: salesOrderId,
        converted_at: new Date(),
        updated_by: userId,
      });

      // Create history entries
      await trx("sales_order_history").insert({
        id: uuidv4(),
        sales_order_id: salesOrderId,
        old_status: null,
        new_status: "DRAFT",
        notes: "Sales order created from estimate",
        changed_by: userId,
      });

      await trx("estimate_history").insert({
        id: uuidv4(),
        estimate_id: estimateId,
        old_status: "ACCEPTED",
        new_status: "CONVERTED",
        notes: "Estimate converted to sales order",
        changed_by: userId,
      });

      await trx.commit();

      res.json({
        message: "Estimate converted to sales order successfully",
        salesOrderId,
        salesOrderNumber: salesOrderNumber.rows[0].number,
      });
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

export default router;
