import express from "express";
import type { Request, Response } from "express";
import {
  authenticateToken,
  requireCompanyAccess,
  requirePermission,
} from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { traIntegrationService } from "../services/traIntegrationService";
import { auditLogger } from "../middleware/auditMiddleware";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// VAT Calculation endpoint
router.post(
  "/:companyId/vat/calculate",
  requireCompanyAccess,
  requirePermission("tax:calculate"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { amount, isVATInclusive = false, category = 'STANDARD' } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        error: "Valid amount is required"
      });
    }

    const result = await traIntegrationService.calculateVAT(
      companyId,
      parseFloat(amount),
      isVATInclusive,
      category
    );

    res.json({
      success: true,
      data: result,
      message: "VAT calculated successfully"
    });
  })
);

// Withholding Tax Calculation endpoint
router.post(
  "/:companyId/wht/calculate",
  requireCompanyAccess,
  requirePermission("tax:calculate"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { grossAmount, whtCategory } = req.body;

    if (!grossAmount || grossAmount <= 0) {
      return res.status(400).json({
        error: "Valid gross amount is required"
      });
    }

    if (!whtCategory) {
      return res.status(400).json({
        error: "WHT category is required"
      });
    }

    const result = await traIntegrationService.calculateWithholdingTax(
      companyId,
      parseFloat(grossAmount),
      whtCategory
    );

    res.json({
      success: true,
      data: result,
      message: "Withholding tax calculated successfully"
    });
  })
);

// Create VAT Return
router.post(
  "/:companyId/vat/returns",
  requireCompanyAccess,
  requirePermission("tax:manage"),
  auditLogger,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const vatReturnData = { ...req.body, companyId };

    // Set audit data
    (req as any).auditData = {
      action: 'CREATE',
      tableName: 'tra_vat_returns',
      description: `VAT return created for period ${vatReturnData.returnPeriod}`,
      newValues: vatReturnData
    };

    const vatReturnId = await traIntegrationService.createVATReturn(vatReturnData);

    res.status(201).json({
      success: true,
      data: { id: vatReturnId },
      message: "VAT return created successfully"
    });
  })
);

// Generate VAT Return for period
router.post(
  "/:companyId/vat/returns/generate",
  requireCompanyAccess,
  requirePermission("tax:manage"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { returnPeriod } = req.body;

    if (!returnPeriod || !/^\d{4}-\d{2}$/.test(returnPeriod)) {
      return res.status(400).json({
        error: "Valid return period (YYYY-MM) is required"
      });
    }

    const vatReturn = await traIntegrationService.generateVATReturn(
      companyId,
      returnPeriod
    );

    res.json({
      success: true,
      data: vatReturn,
      message: "VAT return generated successfully"
    });
  })
);

// Get VAT Returns
router.get(
  "/:companyId/vat/returns",
  requireCompanyAccess,
  requirePermission("tax:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { limit = 10, offset = 0 } = req.query;

    const result = await traIntegrationService.getVATReturns(
      companyId,
      parseInt(limit as string),
      parseInt(offset as string)
    );

    res.json({
      success: true,
      data: result.returns,
      total: result.total,
      message: "VAT returns retrieved successfully"
    });
  })
);

// Record Withholding Tax
router.post(
  "/:companyId/wht/records",
  requireCompanyAccess,
  requirePermission("tax:manage"),
  auditLogger,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const whtData = { ...req.body, companyId };

    // Validate required fields
    const requiredFields = ['grossAmount', 'whtCategory', 'serviceDate', 'paymentDate'];
    for (const field of requiredFields) {
      if (!whtData[field]) {
        return res.status(400).json({
          error: `${field} is required`
        });
      }
    }

    // Calculate WHT if not provided
    if (!whtData.whtAmount || !whtData.whtRate) {
      const calculation = await traIntegrationService.calculateWithholdingTax(
        companyId,
        parseFloat(whtData.grossAmount),
        whtData.whtCategory
      );
      whtData.whtAmount = calculation.whtAmount;
      whtData.whtRate = calculation.whtRate;
      whtData.netAmount = calculation.netAmount;
    }

    // Set audit data
    (req as any).auditData = {
      action: 'CREATE',
      tableName: 'tra_withholding_tax',
      description: `WHT record created for ${whtData.whtCategory}`,
      newValues: whtData
    };

    const whtId = await traIntegrationService.recordWithholdingTax(whtData);

    res.status(201).json({
      success: true,
      data: { id: whtId },
      message: "Withholding tax recorded successfully"
    });
  })
);

// Get Withholding Tax Records
router.get(
  "/:companyId/wht/records",
  requireCompanyAccess,
  requirePermission("tax:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { limit = 10, offset = 0 } = req.query;

    const result = await traIntegrationService.getWithholdingTaxRecords(
      companyId,
      parseInt(limit as string),
      parseInt(offset as string)
    );

    res.json({
      success: true,
      data: result.records,
      total: result.total,
      message: "Withholding tax records retrieved successfully"
    });
  })
);

// Get Tax Rates
router.get(
  "/:companyId/tax-rates",
  requireCompanyAccess,
  requirePermission("tax:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { taxType, category } = req.query;

    try {
      if (taxType && category) {
        // Get specific tax rate
        const taxRate = await traIntegrationService.getCurrentTaxRate(
          taxType as string,
          category as string
        );
        res.json({
          success: true,
          data: taxRate,
          message: "Tax rate retrieved successfully"
        });
      } else {
        // Get all active tax rates
        const taxRates = await traIntegrationService.getAllActiveTaxRates();
        res.json({
          success: true,
          data: taxRates,
          message: "Tax rates retrieved successfully"
        });
      }
    } catch (error) {
      res.status(404).json({
        error: "Tax rate not found"
      });
    }
  })
);

// Get TRA Integration Statistics
router.get(
  "/:companyId/statistics",
  requireCompanyAccess,
  requirePermission("tax:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { period = 'current-month' } = req.query;

    // This would be implemented to provide statistics
    // For now, return mock data
    const statistics = {
      vatReturns: {
        submitted: 12,
        pending: 1,
        totalVatCollected: 45000000, // TZS
        totalVatPaid: 38000000
      },
      withholdingTax: {
        certificates: 156,
        totalWhtDeducted: 12500000,
        pendingSubmissions: 3
      },
      compliance: {
        score: 95,
        issues: 2,
        lastSubmission: new Date().toISOString()
      }
    };

    res.json({
      success: true,
      data: statistics,
      message: "TRA integration statistics retrieved successfully"
    });
  })
);

export default router;
