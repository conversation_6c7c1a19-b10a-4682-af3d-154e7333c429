import { Router, Request, Response } from "express";
import { db } from "../config/database";
import { authenticateToken, requireCompanyAccess } from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { enterpriseMetricsService } from "../services/enterpriseMetricsService";
import { historicalDataService } from "../services/historicalDataService";

const router = Router();

// Apply authentication to all routes
router.use(authenticateToken);

// GET /api/dashboard/:companyId
router.get(
  "/:companyId",
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { period = "month" } = req.query;

    // Calculate date range based on period
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case "year":
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      case "quarter":
        const quarter = Math.floor(now.getMonth() / 3);
        startDate = new Date(now.getFullYear(), quarter * 3, 1);
        break;
      case "month":
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
    }

    try {
      // Get financial data from posted transactions
      const transactions = await db("transactions")
        .select(
          "transaction_entries.account_id",
          "transaction_entries.debit_amount",
          "transaction_entries.credit_amount",
          "accounts.account_type",
          "accounts.code",
          "accounts.name"
        )
        .join(
          "transaction_entries",
          "transactions.id",
          "transaction_entries.transaction_id"
        )
        .join("accounts", "transaction_entries.account_id", "accounts.id")
        .where("transactions.company_id", companyId)
        .where("transactions.status", "POSTED")
        .where(
          "transactions.transaction_date",
          ">=",
          startDate.toISOString().split("T")[0]
        )
        .where(
          "transactions.transaction_date",
          "<=",
          now.toISOString().split("T")[0]
        );

      // Calculate financial metrics
      let revenue = 0;
      let expenses = 0;
      let accountsReceivable = 0;
      let accountsPayable = 0;
      let cashBalance = 0;

      transactions.forEach((entry: any) => {
        const debit = parseFloat(entry.debit_amount || 0);
        const credit = parseFloat(entry.credit_amount || 0);
        const netAmount = credit - debit;

        switch (entry.account_type) {
          case "REVENUE":
            revenue += netAmount;
            break;
          case "EXPENSE":
            expenses += -netAmount; // Expenses are typically debits
            break;
          case "ASSET":
            if (
              entry.code === "1200" ||
              entry.name.toLowerCase().includes("receivable")
            ) {
              accountsReceivable += debit - credit;
            } else if (
              entry.code === "1000" ||
              entry.code === "1001" ||
              entry.name.toLowerCase().includes("cash")
            ) {
              cashBalance += debit - credit;
            }
            break;
          case "LIABILITY":
            if (
              entry.code === "2000" ||
              entry.name.toLowerCase().includes("payable")
            ) {
              accountsPayable += credit - debit;
            }
            break;
        }
      });

      // Get invoice statistics
      const invoiceStatsResult = await db("invoices")
        .select(
          db.raw("COUNT(*) FILTER (WHERE status = ?) as pending_count", [
            "SENT",
          ]),
          db.raw(
            "COUNT(*) FILTER (WHERE status = ? AND due_date < CURRENT_DATE) as overdue_count",
            ["SENT"]
          ),
          db.raw("COUNT(*) as total_invoices")
        )
        .where("company_id", companyId)
        .first();

      const invoiceStats = invoiceStatsResult as any;

      // Get contact count
      const contactStatsResult = await db("contacts")
        .count("* as total_contacts")
        .where("company_id", companyId)
        .where("is_active", true)
        .first();

      const contactStats = contactStatsResult as any;

      // Get transaction count
      const transactionStatsResult = await db("transactions")
        .count("* as total_transactions")
        .where("company_id", companyId)
        .where("status", "POSTED")
        .first();

      const transactionStats = transactionStatsResult as any;

      // Get recent activity
      const recentInvoices = await db("invoices")
        .select(
          "invoices.id",
          "invoices.invoice_number",
          "invoices.total_amount",
          "invoices.currency",
          "invoices.status",
          "invoices.created_at",
          "invoices.invoice_type",
          "contacts.name as contact_name"
        )
        .leftJoin("contacts", "invoices.contact_id", "contacts.id")
        .where("invoices.company_id", companyId)
        .orderBy("invoices.created_at", "desc")
        .limit(5);

      const recentTransactions = await db("transactions")
        .select(
          "id",
          "transaction_number",
          "description",
          "total_amount",
          "status",
          "created_at"
        )
        .where("company_id", companyId)
        .orderBy("created_at", "desc")
        .limit(5);

      // Format recent activity
      const recentActivity = [
        ...recentInvoices.map((invoice: any) => ({
          id: invoice.id,
          type: "invoice",
          title: `${
            invoice.invoice_type === "SALES" ? "Sales" : "Purchase"
          } Invoice ${invoice.invoice_number}`,
          description: `${invoice.contact_name || "Unknown Contact"} - ${
            invoice.status
          }`,
          amount: parseFloat(invoice.total_amount || 0),
          currency: invoice.currency,
          date: invoice.created_at,
          status: invoice.status,
        })),
        ...recentTransactions.map((transaction: any) => ({
          id: transaction.id,
          type: "transaction",
          title: `Transaction ${transaction.transaction_number}`,
          description: transaction.description,
          amount: parseFloat(transaction.total_amount || 0),
          currency: "USD",
          date: transaction.created_at,
          status: transaction.status,
        })),
      ]
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, 10);

      // Calculate enterprise metrics
      const grossProfitMargin =
        await enterpriseMetricsService.getGrossProfitMargin(
          companyId,
          startDate,
          now
        );
      const operatingCashFlow =
        await enterpriseMetricsService.getOperatingCashFlow(
          companyId,
          startDate,
          now
        );
      const workingCapital = await enterpriseMetricsService.getWorkingCapital(
        companyId
      );
      const burnRate = await enterpriseMetricsService.getBurnRate(companyId, 3);
      const avgCollectionPeriod =
        await enterpriseMetricsService.getAverageCollectionPeriod(companyId);
      const avgPaymentPeriod =
        await enterpriseMetricsService.getAveragePaymentPeriod(companyId);

      // Calculate growth percentages (vs previous period)
      let revenueGrowth = 0;
      let expenseGrowth = 0;

      try {
        const prevStartDate = new Date(startDate);
        const prevEndDate = new Date(startDate);

        switch (period) {
          case "year":
            prevStartDate.setFullYear(prevStartDate.getFullYear() - 1);
            prevEndDate.setFullYear(prevEndDate.getFullYear() - 1);
            break;
          case "quarter":
            prevStartDate.setMonth(prevStartDate.getMonth() - 3);
            prevEndDate.setMonth(prevEndDate.getMonth() - 3);
            break;
          case "month":
          default:
            prevStartDate.setMonth(prevStartDate.getMonth() - 1);
            prevEndDate.setMonth(prevEndDate.getMonth() - 1);
            break;
        }

        // Get previous period data for growth calculation
        const prevTransactions = await db("transactions")
          .select(
            "transaction_entries.account_id",
            "transaction_entries.debit_amount",
            "transaction_entries.credit_amount",
            "accounts.account_type"
          )
          .join(
            "transaction_entries",
            "transactions.id",
            "transaction_entries.transaction_id"
          )
          .join("accounts", "transaction_entries.account_id", "accounts.id")
          .where("transactions.company_id", companyId)
          .where("transactions.status", "POSTED")
          .where(
            "transactions.transaction_date",
            ">=",
            prevStartDate.toISOString().split("T")[0]
          )
          .where(
            "transactions.transaction_date",
            "<=",
            prevEndDate.toISOString().split("T")[0]
          );

        let prevRevenue = 0;
        let prevExpenses = 0;

        prevTransactions.forEach((entry: any) => {
          const debit = parseFloat(entry.debit_amount || 0);
          const credit = parseFloat(entry.credit_amount || 0);
          const netAmount = credit - debit;

          switch (entry.account_type) {
            case "REVENUE":
              prevRevenue += netAmount;
              break;
            case "EXPENSE":
              prevExpenses += -netAmount;
              break;
          }
        });

        // Calculate growth percentages
        if (prevRevenue > 0) {
          revenueGrowth = ((revenue - prevRevenue) / prevRevenue) * 100;
        }
        if (prevExpenses > 0) {
          expenseGrowth = ((expenses - prevExpenses) / prevExpenses) * 100;
        }
      } catch (error) {
        console.error("Error calculating growth metrics:", error);
      }

      const dashboardData = {
        stats: {
          revenue: Math.max(0, revenue),
          expenses: Math.max(0, expenses),
          netIncome: revenue - expenses,
          pendingInvoices: parseInt(String(invoiceStats?.pending_count || 0)),
          overdueInvoices: parseInt(String(invoiceStats?.overdue_count || 0)),
          totalContacts: parseInt(String(contactStats?.total_contacts || 0)),
          totalTransactions: parseInt(
            String(transactionStats?.total_transactions || 0)
          ),
          accountsReceivable: Math.max(0, accountsReceivable),
          accountsPayable: Math.max(0, accountsPayable),
          cashBalance: cashBalance,
          // Enterprise metrics
          grossProfitMargin,
          operatingCashFlow,
          workingCapital,
          burnRate,
          avgCollectionPeriod,
          avgPaymentPeriod,
          revenueGrowth,
          expenseGrowth,
        },
        recentActivity,
      };

      res.json(dashboardData);
    } catch (error) {
      console.error("Dashboard data fetch error:", error);
      res.status(500).json({ error: "Failed to fetch dashboard data" });
    }
  })
);

// GET /api/dashboard/:companyId/financial-summary
router.get(
  "/:companyId/financial-summary",
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { period = "month" } = req.query;

    // This endpoint can be used for more detailed financial summaries
    // For now, return a basic financial summary
    try {
      // Get basic financial data
      const transactions = await db("transactions")
        .select(
          "transaction_entries.account_id",
          "transaction_entries.debit_amount",
          "transaction_entries.credit_amount",
          "accounts.account_type"
        )
        .join(
          "transaction_entries",
          "transactions.id",
          "transaction_entries.transaction_id"
        )
        .join("accounts", "transaction_entries.account_id", "accounts.id")
        .where("transactions.company_id", companyId)
        .where("transactions.status", "POSTED");

      let revenue = 0;
      let expenses = 0;

      transactions.forEach((transaction: any) => {
        const amount = parseFloat(transaction.credit_amount || 0) - parseFloat(transaction.debit_amount || 0);

        switch (transaction.account_type) {
          case "REVENUE":
            revenue += amount;
            break;
          case "EXPENSE":
            expenses += Math.abs(amount);
            break;
        }
      });

      res.json({
        period,
        revenue: Math.max(0, revenue),
        expenses: Math.max(0, expenses),
        netIncome: revenue - expenses,
        profitMargin: revenue > 0 ? ((revenue - expenses) / revenue) * 100 : 0,
      });
    } catch (error) {
      console.error("Error fetching financial summary:", error);
      res.status(500).json({ error: "Failed to fetch financial summary" });
    }
  })
);

// GET /api/dashboard/:companyId/recent-activity
router.get(
  "/:companyId/recent-activity",
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { limit = 10 } = req.query;

    try {
      // Get recent invoices
      const recentInvoices = await db("invoices")
        .select(
          "invoices.id",
          "invoices.invoice_number",
          "invoices.total_amount",
          "invoices.currency",
          "invoices.status",
          "invoices.created_at",
          "invoices.invoice_type",
          "contacts.name as contact_name"
        )
        .leftJoin("contacts", "invoices.contact_id", "contacts.id")
        .where("invoices.company_id", companyId)
        .orderBy("invoices.created_at", "desc")
        .limit(Math.floor(Number(limit) / 2));

      // Get recent transactions
      const recentTransactions = await db("transactions")
        .select(
          "id",
          "transaction_number",
          "description",
          "total_amount",
          "status",
          "created_at"
        )
        .where("company_id", companyId)
        .orderBy("created_at", "desc")
        .limit(Math.floor(Number(limit) / 2));

      // Format and combine activity
      const recentActivity = [
        ...recentInvoices.map((invoice: any) => ({
          id: invoice.id,
          type: "invoice",
          title: `${
            invoice.invoice_type === "SALES" ? "Sales" : "Purchase"
          } Invoice ${invoice.invoice_number}`,
          description: `${invoice.contact_name || "Unknown Contact"} - ${
            invoice.status
          }`,
          amount: parseFloat(invoice.total_amount || 0),
          currency: invoice.currency,
          date: invoice.created_at,
          status: invoice.status,
        })),
        ...recentTransactions.map((transaction: any) => ({
          id: transaction.id,
          type: "transaction",
          title: `Transaction ${transaction.transaction_number}`,
          description: transaction.description,
          amount: parseFloat(transaction.total_amount || 0),
          currency: "USD",
          date: transaction.created_at,
          status: transaction.status,
        })),
      ]
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, Number(limit));

      res.json(recentActivity);
    } catch (error) {
      console.error("Recent activity fetch error:", error);
      res.status(500).json({ error: "Failed to fetch recent activity" });
    }
  })
);

// GET /api/dashboard/:companyId/charts/revenue-trend
router.get(
  "/:companyId/charts/revenue-trend",
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;

    try {
      const revenueTrend = await historicalDataService.getRevenueTrend(
        companyId
      );
      res.json(revenueTrend);
    } catch (error) {
      console.error("Revenue trend fetch error:", error);
      res.status(500).json({ error: "Failed to fetch revenue trend data" });
    }
  })
);

// GET /api/dashboard/:companyId/charts/expense-trend
router.get(
  "/:companyId/charts/expense-trend",
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;

    try {
      const expenseTrend = await historicalDataService.getExpenseTrend(
        companyId
      );
      res.json(expenseTrend);
    } catch (error) {
      console.error("Expense trend fetch error:", error);
      res.status(500).json({ error: "Failed to fetch expense trend data" });
    }
  })
);

// GET /api/dashboard/:companyId/charts/cash-flow-trend
router.get(
  "/:companyId/charts/cash-flow-trend",
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;

    try {
      const cashFlowTrend = await historicalDataService.getCashFlowTrend(
        companyId
      );
      res.json(cashFlowTrend);
    } catch (error) {
      console.error("Cash flow trend fetch error:", error);
      res.status(500).json({ error: "Failed to fetch cash flow trend data" });
    }
  })
);

// GET /api/dashboard/:companyId/charts/expense-breakdown
router.get(
  "/:companyId/charts/expense-breakdown",
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { period = "month" } = req.query;

    try {
      const expenseBreakdown = await historicalDataService.getExpenseBreakdown(
        companyId,
        period as "month" | "quarter" | "year"
      );
      res.json(expenseBreakdown);
    } catch (error) {
      console.error("Expense breakdown fetch error:", error);
      res.status(500).json({ error: "Failed to fetch expense breakdown data" });
    }
  })
);

// GET /api/dashboard/:companyId/charts/revenue-expense-comparison
router.get(
  "/:companyId/charts/revenue-expense-comparison",
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;

    try {
      const comparisonData = await historicalDataService.getRevenueExpenseTrend(
        companyId
      );
      res.json(comparisonData);
    } catch (error) {
      console.error("Revenue expense comparison fetch error:", error);
      res
        .status(500)
        .json({ error: "Failed to fetch revenue expense comparison data" });
    }
  })
);

export default router;
