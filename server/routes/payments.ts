import { Router, Request, Response } from "express";
import { z } from "zod";
import { requireAuth } from "../middleware/auth";
import { requireCompanyAccess } from "../middleware/companyAccess";
import { asyncHandler } from "../middleware/asyncHandler";
import { paymentService } from "../services/paymentService";
import Stripe from "stripe";

const router = Router();

// Validation schemas
const createCustomerSchema = z.object({
  name: z.string(),
  email: z.string().email(),
  phone: z.string().optional(),
  address: z
    .object({
      line1: z.string().optional(),
      line2: z.string().optional(),
      city: z.string().optional(),
      state: z.string().optional(),
      postalCode: z.string().optional(),
      country: z.string().optional(),
    })
    .optional(),
  metadata: z.any().optional(),
});

const createPaymentMethodSchema = z.object({
  type: z.enum(["CREDIT_CARD", "BANK_ACCOUNT", "ACH", "WIRE"]),
  stripePaymentMethodId: z.string().optional(),
  isDefault: z.boolean().default(false),
});

const createInvoiceSchema = z.object({
  customerId: z.string(),
  amount: z.number().positive(),
  currency: z.string().default("USD"),
  status: z
    .enum(["DRAFT", "SENT", "PAID", "OVERDUE", "CANCELLED"])
    .default("DRAFT"),
  dueDate: z.string().transform((str) => new Date(str)),
  description: z.string().optional(),
  lineItems: z.array(
    z.object({
      id: z.string(),
      description: z.string(),
      quantity: z.number().positive(),
      unitPrice: z.number(),
      amount: z.number(),
      taxRate: z.number().optional(),
      taxAmount: z.number().optional(),
    })
  ),
  metadata: z.any().optional(),
});

const processPaymentSchema = z.object({
  customerId: z.string(),
  paymentMethodId: z.string(),
  amount: z.number().positive(),
  currency: z.string().default("USD"),
  description: z.string().optional(),
  invoiceId: z.string().optional(),
  metadata: z.any().optional(),
});

const refundPaymentSchema = z.object({
  amount: z.number().positive().optional(),
  reason: z.string().optional(),
});

// POST /api/payments/:companyId/customers - Create customer
router.post(
  "/:companyId/customers",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const customerData = createCustomerSchema.parse(req.body);

    try {
      const customer = await paymentService.createCustomer(
        companyId,
        customerData as any
      );
      res.status(201).json(customer);
    } catch (error) {
      console.error("Failed to create customer:", error);
      res.status(500).json({ error: "Failed to create customer" });
    }
  })
);

// POST /api/payments/:companyId/customers/:customerId/payment-methods - Create payment method
router.post(
  "/:companyId/customers/:customerId/payment-methods",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, customerId } = req.params;
    const paymentMethodData = createPaymentMethodSchema.parse(req.body);

    try {
      const paymentMethod = await paymentService.createPaymentMethod(
        companyId,
        customerId,
        paymentMethodData as any
      );
      res.status(201).json(paymentMethod);
    } catch (error) {
      console.error("Failed to create payment method:", error);
      res.status(500).json({ error: "Failed to create payment method" });
    }
  })
);

// POST /api/payments/:companyId/invoices - Create invoice
router.post(
  "/:companyId/invoices",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const invoiceData = createInvoiceSchema.parse(req.body);

    try {
      const invoice = await paymentService.createInvoice(
        companyId,
        invoiceData as any
      );
      res.status(201).json(invoice);
    } catch (error) {
      console.error("Failed to create invoice:", error);
      res.status(500).json({ error: "Failed to create invoice" });
    }
  })
);

// POST /api/payments/:companyId/process - Process payment
router.post(
  "/:companyId/process",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const paymentData = processPaymentSchema.parse(req.body);

    try {
      const payment = await paymentService.processPayment(
        companyId,
        paymentData as any
      );
      res.status(201).json(payment);
    } catch (error) {
      console.error("Failed to process payment:", error);
      res.status(500).json({ error: "Failed to process payment" });
    }
  })
);

// POST /api/payments/:companyId/payments/:paymentId/refund - Refund payment
router.post(
  "/:companyId/payments/:paymentId/refund",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, paymentId } = req.params;
    const { amount, reason } = refundPaymentSchema.parse(req.body);

    try {
      const payment = await paymentService.refundPayment(
        companyId,
        paymentId,
        amount,
        reason
      );
      res.json(payment);
    } catch (error) {
      console.error("Failed to refund payment:", error);
      res.status(500).json({ error: "Failed to refund payment" });
    }
  })
);

// GET /api/payments/:companyId/customers - Get customers
router.get(
  "/:companyId/customers",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { page = "1", limit = "20" } = req.query;

    try {
      // Implementation would be added to paymentService
      res.json({
        customers: [],
        total: 0,
        page: parseInt(page as string),
        totalPages: 0,
      });
    } catch (error) {
      console.error("Failed to get customers:", error);
      res.status(500).json({ error: "Failed to get customers" });
    }
  })
);

// GET /api/payments/:companyId/invoices - Get invoices
router.get(
  "/:companyId/invoices",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { status, page = "1", limit = "20" } = req.query;

    try {
      // Implementation would be added to paymentService
      res.json({
        invoices: [],
        total: 0,
        page: parseInt(page as string),
        totalPages: 0,
      });
    } catch (error) {
      console.error("Failed to get invoices:", error);
      res.status(500).json({ error: "Failed to get invoices" });
    }
  })
);

// GET /api/payments/:companyId/payments - Get payments
router.get(
  "/:companyId/payments",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { status, customerId, page = "1", limit = "20" } = req.query;

    try {
      // Implementation would be added to paymentService
      res.json({
        payments: [],
        total: 0,
        page: parseInt(page as string),
        totalPages: 0,
      });
    } catch (error) {
      console.error("Failed to get payments:", error);
      res.status(500).json({ error: "Failed to get payments" });
    }
  })
);

// POST /api/payments/webhooks/stripe - Stripe webhook handler
router.post(
  "/webhooks/stripe",
  asyncHandler(async (req: Request, res: Response) => {
    const sig = req.headers["stripe-signature"] as string;
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

    let event: Stripe.Event;

    try {
      const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
        apiVersion: "2025-05-28.basil" as any,
      });

      event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
    } catch (err) {
      console.error("Webhook signature verification failed:", err);
      return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    try {
      await paymentService.handleWebhook(event);
      res.status(200).json({ received: true });
    } catch (error) {
      console.error("Failed to handle Stripe webhook:", error);
      res.status(500).json({ error: "Failed to handle webhook" });
    }
  })
);

// ClickPesa Payment Integration

const clickpesaConfig = {
  apiKey: process.env.CLICKPESA_API_KEY || "",
  secretKey: process.env.CLICKPESA_SECRET_KEY || "",
  baseUrl: process.env.CLICKPESA_BASE_URL || "https://api.clickpesa.com",
};

// POST /api/payments/clickpesa/initiate
router.post(
  "/clickpesa/initiate",
  requireAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const {
      invoiceId,
      companyId,
      customerId,
      phoneNumber,
      amount,
      currency = "TZS",
      description,
    } = req.body;

    try {
      // Validate required fields
      if (!invoiceId || !companyId || !phoneNumber || !amount) {
        return res.status(400).json({
          error:
            "Missing required fields: invoiceId, companyId, phoneNumber, amount",
        });
      }

      // Generate unique transaction ID
      const transactionId = `TXN-${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 9)}`;

      // Create payment request
      const paymentRequest = {
        vendor_id: clickpesaConfig.apiKey,
        vendor_cust_id: customerId,
        phone_number: phoneNumber,
        amount: amount,
        currency: currency,
        transaction_id: transactionId,
        description: description || `Payment for Invoice`,
        callback_url: `${process.env.APP_URL}/api/payments/clickpesa/callback`,
        redirect_url: `${process.env.APP_URL}/invoices/${invoiceId}/payment-success`,
      };

      // Generate signature for authentication
      const signature = generateClickPesaSignature(
        paymentRequest,
        clickpesaConfig.secretKey
      );

      // Send payment request to ClickPesa
      const axios = require("axios");
      const response = await axios.post(
        `${clickpesaConfig.baseUrl}/checkout`,
        paymentRequest,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${clickpesaConfig.apiKey}`,
            "X-Signature": signature,
          },
        }
      );

      // Store payment record in database
      const { db } = require("../config/database");
      const payment = await db("payments")
        .insert({
          id: require("uuid").v4(),
          invoice_id: invoiceId,
          company_id: companyId,
          amount: amount,
          currency: currency,
          payment_method: "CLICKPESA",
          clickpesa_transaction_id: transactionId,
          clickpesa_checkout_id: response.data.checkout_id,
          phone_number: phoneNumber,
          status: "PENDING",
          created_at: new Date(),
          updated_at: new Date(),
        })
        .returning("*");

      res.json({
        success: true,
        paymentId: payment[0].id,
        checkoutUrl: response.data.checkout_url,
        transactionId: transactionId,
        message: "Payment initiated successfully",
      });
    } catch (error) {
      console.error("ClickPesa payment initiation error:", error);
      res.status(500).json({
        error: "Failed to initiate payment",
        details: error.response?.data || error.message,
      });
    }
  })
);

// POST /api/payments/clickpesa/callback
router.post(
  "/clickpesa/callback",
  asyncHandler(async (req: Request, res: Response) => {
    const {
      transaction_id,
      status,
      amount,
      phone_number,
      reference,
      failure_reason,
    } = req.body;

    try {
      // Verify callback signature
      const isValidSignature = verifyClickPesaSignature(
        req.body,
        clickpesaConfig.secretKey
      );
      if (!isValidSignature) {
        console.error("Invalid ClickPesa callback signature");
        return res.status(400).json({ error: "Invalid signature" });
      }

      const { db } = require("../config/database");

      // Find the payment record
      const payment = await db("payments")
        .where("clickpesa_transaction_id", transaction_id)
        .first();

      if (!payment) {
        console.error(
          `Payment not found for transaction ID: ${transaction_id}`
        );
        return res.status(404).json({ error: "Payment not found" });
      }

      // Update payment status based on callback
      switch (status) {
        case "SUCCESS":
          await db("payments")
            .where("clickpesa_transaction_id", transaction_id)
            .update({
              status: "COMPLETED",
              completed_at: new Date(),
              clickpesa_reference: reference,
              updated_at: new Date(),
            });

          // Apply payment to invoice
          await applyPaymentToInvoice(payment.invoice_id, amount);

          console.log(`Payment completed for transaction ${transaction_id}`);
          break;

        case "FAILED":
          await db("payments")
            .where("clickpesa_transaction_id", transaction_id)
            .update({
              status: "FAILED",
              failure_reason: failure_reason,
              updated_at: new Date(),
            });

          console.log(
            `Payment failed for transaction ${transaction_id}: ${failure_reason}`
          );
          break;

        case "CANCELLED":
          await db("payments")
            .where("clickpesa_transaction_id", transaction_id)
            .update({
              status: "CANCELLED",
              updated_at: new Date(),
            });

          console.log(`Payment cancelled for transaction ${transaction_id}`);
          break;

        default:
          console.log(
            `Unknown payment status: ${status} for transaction ${transaction_id}`
          );
      }

      res.json({ success: true, message: "Callback processed successfully" });
    } catch (error) {
      console.error("ClickPesa callback processing error:", error);
      res.status(500).json({ error: "Failed to process callback" });
    }
  })
);

// Helper function to generate ClickPesa signature
function generateClickPesaSignature(data: any, secretKey: string): string {
  const crypto = require("crypto");
  const sortedKeys = Object.keys(data).sort();
  const signatureString = sortedKeys
    .map((key) => `${key}=${data[key]}`)
    .join("&");
  return crypto
    .createHmac("sha256", secretKey)
    .update(signatureString)
    .digest("hex");
}

// Helper function to verify ClickPesa callback signature
function verifyClickPesaSignature(data: any, secretKey: string): boolean {
  const receivedSignature = data.signature;
  delete data.signature;
  const expectedSignature = generateClickPesaSignature(data, secretKey);
  return receivedSignature === expectedSignature;
}

// Helper function to apply payment to invoice
async function applyPaymentToInvoice(invoiceId: string, amount: number) {
  try {
    const { db } = require("../config/database");

    // Get current invoice
    const invoice = await db("invoices").where("id", invoiceId).first();

    if (!invoice) {
      throw new Error(`Invoice not found: ${invoiceId}`);
    }

    // Calculate new paid amount and balance
    const newPaidAmount = parseFloat(invoice.paid_amount) + parseFloat(amount.toString());
    const newBalanceDue = parseFloat(invoice.total_amount) - newPaidAmount;

    // Determine new status
    let newStatus = invoice.status;
    if (newBalanceDue <= 0.01) {
      newStatus = "PAID";
    } else if (newPaidAmount > 0) {
      newStatus = "PARTIALLY_PAID";
    }

    // Update invoice
    await db("invoices").where("id", invoiceId).update({
      paid_amount: newPaidAmount,
      balance_due: newBalanceDue,
      status: newStatus,
      updated_at: new Date(),
    });

    console.log(
      `Applied payment of ${amount} to invoice ${invoice.invoice_number}`
    );
  } catch (error) {
    console.error("Error applying payment to invoice:", error);
    throw error;
  }
}

export default router;
