import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { requireAuth } from '../middleware/auth';
import { requireCompanyAccess } from '../middleware/companyAccess';
import { asyncHandler } from '../middleware/asyncHandler';
import { workflowService } from '../services/workflowService';

const router = Router();

// Validation schemas
const workflowTriggerSchema = z.object({
  type: z.enum(['SCHEDULE', 'EVENT', 'MANUAL', 'CONDITION']),
  config: z.object({
    schedule: z.string().optional(),
    event: z.string().optional(),
    condition: z.object({
      field: z.string(),
      operator: z.enum(['equals', 'greater_than', 'less_than', 'contains', 'not_equals']),
      value: z.any(),
    }).optional(),
  }),
});

const workflowActionSchema = z.object({
  type: z.enum(['EMAIL', 'NOTIFICATION', 'CREATE_TRANSACTION', 'UPDATE_RECORD', 'GENERATE_REPORT', 'WEBHOOK', 'APPROVAL_REQUEST']),
  config: z.object({
    to: z.array(z.string()).optional(),
    subject: z.string().optional(),
    template: z.string().optional(),
    data: z.any().optional(),
    message: z.string().optional(),
    users: z.array(z.string()).optional(),
    accountId: z.string().optional(),
    amount: z.number().optional(),
    description: z.string().optional(),
    table: z.string().optional(),
    recordId: z.string().optional(),
    updates: z.any().optional(),
    reportType: z.string().optional(),
    format: z.string().optional(),
    recipients: z.array(z.string()).optional(),
    url: z.string().optional(),
    method: z.enum(['GET', 'POST', 'PUT', 'DELETE']).optional(),
    headers: z.any().optional(),
    payload: z.any().optional(),
    approvers: z.array(z.string()).optional(),
    approvalType: z.string().optional(),
    requiredApprovals: z.number().optional(),
  }),
});

const workflowConditionSchema = z.object({
  field: z.string(),
  operator: z.enum(['equals', 'greater_than', 'less_than', 'contains', 'not_equals', 'is_null', 'is_not_null']),
  value: z.any(),
  logicalOperator: z.enum(['AND', 'OR']).optional(),
});

const createWorkflowSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
  trigger: workflowTriggerSchema,
  conditions: z.array(workflowConditionSchema).optional(),
  actions: z.array(workflowActionSchema),
  metadata: z.any().optional(),
});

const executeWorkflowSchema = z.object({
  triggerData: z.any().optional(),
});

const processApprovalSchema = z.object({
  decision: z.enum(['APPROVE', 'REJECT']),
  comments: z.string().optional(),
});

// POST /api/workflows/:companyId - Create workflow
router.post(
  '/:companyId',
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const workflowData = createWorkflowSchema.parse(req.body);
    
    try {
      const workflow = await workflowService.createWorkflow(
        companyId,
        (req as any).user!.id,
        workflowData as any
      );
      res.status(201).json(workflow);
    } catch (error) {
      console.error('Failed to create workflow:', error);
      res.status(500).json({ error: 'Failed to create workflow' });
    }
  })
);

// GET /api/workflows/:companyId/stats - Get workflow statistics
router.get(
  '/:companyId/stats',
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;

    try {
      const stats = await workflowService.getWorkflowStats(companyId);
      res.json(stats);
    } catch (error) {
      console.error('Failed to get workflow stats:', error);
      res.status(500).json({ error: 'Failed to get workflow stats' });
    }
  })
);

// GET /api/workflows/:companyId - Get workflows
router.get(
  '/:companyId',
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const {
      isActive,
      triggerType,
      page = '1',
      limit = '20'
    } = req.query;

    try {
      const options = {
        isActive: isActive === 'true' ? true : isActive === 'false' ? false : undefined,
        triggerType: triggerType as string,
        page: parseInt(page as string),
        limit: parseInt(limit as string),
      };

      const workflows = await workflowService.getWorkflows(companyId, options);
      res.json(workflows);
    } catch (error) {
      console.error('Failed to get workflows:', error);
      res.status(500).json({ error: 'Failed to get workflows' });
    }
  })
);

// POST /api/workflows/:companyId/:workflowId/execute - Execute workflow
router.post(
  '/:companyId/:workflowId/execute',
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { workflowId } = req.params;
    const { triggerData } = executeWorkflowSchema.parse(req.body);
    
    try {
      const execution = await workflowService.executeWorkflow(
        workflowId,
        triggerData,
        (req as any).user!.id
      );
      res.json(execution);
    } catch (error) {
      console.error('Failed to execute workflow:', error);
      res.status(500).json({ error: 'Failed to execute workflow' });
    }
  })
);

// POST /api/workflows/:companyId/trigger/:eventName - Trigger event workflows
router.post(
  '/:companyId/trigger/:eventName',
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, eventName } = req.params;
    const eventData = req.body;
    
    try {
      await workflowService.triggerEventWorkflows(companyId, eventName, eventData);
      res.json({ message: 'Event workflows triggered successfully' });
    } catch (error) {
      console.error('Failed to trigger event workflows:', error);
      res.status(500).json({ error: 'Failed to trigger event workflows' });
    }
  })
);

// GET /api/workflows/:companyId/approvals - Get approval requests
router.get(
  '/:companyId/approvals',
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { status, page = '1', limit = '20' } = req.query;
    
    try {
      // Implementation would be added to workflowService
      res.json({ approvals: [], total: 0, page: parseInt(page as string), totalPages: 0 });
    } catch (error) {
      console.error('Failed to get approval requests:', error);
      res.status(500).json({ error: 'Failed to get approval requests' });
    }
  })
);

// POST /api/workflows/:companyId/approvals/:approvalId/process - Process approval
router.post(
  '/:companyId/approvals/:approvalId/process',
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { approvalId } = req.params;
    const { decision, comments } = processApprovalSchema.parse(req.body);
    
    try {
      const approval = await workflowService.processApproval(
        approvalId,
        (req as any).user!.id,
        decision,
        comments
      );
      res.json(approval);
    } catch (error) {
      console.error('Failed to process approval:', error);
      res.status(500).json({ error: 'Failed to process approval' });
    }
  })
);

// GET /api/workflows/:companyId/executions - Get workflow executions
router.get(
  '/:companyId/executions',
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { workflowId, status, page = '1', limit = '20' } = req.query;
    
    try {
      // Implementation would be added to workflowService
      res.json({ executions: [], total: 0, page: parseInt(page as string), totalPages: 0 });
    } catch (error) {
      console.error('Failed to get workflow executions:', error);
      res.status(500).json({ error: 'Failed to get workflow executions' });
    }
  })
);

// PUT /api/workflows/:companyId/:workflowId - Update workflow
router.put(
  '/:companyId/:workflowId',
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { workflowId } = req.params;
    const workflowData = createWorkflowSchema.partial().parse(req.body);

    try {
      const workflow = await workflowService.updateWorkflow(workflowId, workflowData as any);
      res.json(workflow);
    } catch (error) {
      console.error('Failed to update workflow:', error);
      res.status(500).json({ error: 'Failed to update workflow' });
    }
  })
);

// PUT /api/workflows/:companyId/:workflowId/status - Update workflow status
router.put(
  '/:companyId/:workflowId/status',
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { workflowId } = req.params;
    const { isActive } = z.object({ isActive: z.boolean() }).parse(req.body);

    try {
      const workflow = await workflowService.updateWorkflowStatus(workflowId, isActive);
      res.json(workflow);
    } catch (error) {
      console.error('Failed to update workflow status:', error);
      res.status(500).json({ error: 'Failed to update workflow status' });
    }
  })
);

// DELETE /api/workflows/:companyId/:workflowId - Delete workflow
router.delete(
  '/:companyId/:workflowId',
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { workflowId } = req.params;

    try {
      await workflowService.deleteWorkflow(workflowId);
      res.json({ message: 'Workflow deleted successfully' });
    } catch (error) {
      console.error('Failed to delete workflow:', error);
      res.status(500).json({ error: 'Failed to delete workflow' });
    }
  })
);

export default router;
