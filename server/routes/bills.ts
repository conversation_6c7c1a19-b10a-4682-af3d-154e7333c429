import { Router, Request, Response } from "express";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import db from "../config/database";
import { requireAuth, requireCompanyAccess } from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";

const router = Router();

// Validation schemas
const createBillSchema = z.object({
  companyId: z.string().uuid(),
  vendorId: z.string().uuid(),
  vendorInvoiceNumber: z.string().optional(),
  billDate: z.string(),
  dueDate: z.string().optional(),
  paymentTerms: z.string().optional(),
  currency: z.string().default("USD"),
  exchangeRate: z.number().default(1),
  billingAddress: z.string().optional(),
  shippingAddress: z.string().optional(),
  notes: z.string().optional(),
  termsAndConditions: z.string().optional(),
  purchaseOrderId: z.string().uuid().optional(),
  lineItems: z.array(
    z.object({
      accountId: z.string().uuid().optional(),
      inventoryItemId: z.string().uuid().optional(),
      itemCode: z.string().optional(),
      description: z.string(),
      unitOfMeasure: z.string().optional(),
      quantity: z.number(),
      unitCost: z.number(),
      discountPercentage: z.number().default(0),
      taxRate: z.number().default(0),
      taxCode: z.string().optional(),
      notes: z.string().optional(),
    })
  ),
});

const updateBillSchema = createBillSchema.partial().omit({ companyId: true });

const billFiltersSchema = z.object({
  status: z.string().optional(),
  vendorId: z.string().uuid().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  dueDateFrom: z.string().optional(),
  dueDateTo: z.string().optional(),
  search: z.string().optional(),
  page: z.number().default(1),
  limit: z.number().default(50),
});

const payBillSchema = z.object({
  paymentDate: z.string(),
  paymentAmount: z.number(),
  paymentMethod: z.string(),
  paymentReference: z.string().optional(),
  notes: z.string().optional(),
});

// GET /api/bills/:companyId - Get all bills
router.get(
  "/:companyId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const filters = billFiltersSchema.parse(req.query);

    let query = db("bills")
      .select(
        "bills.*",
        "contacts.name as vendor_name",
        "contacts.display_name as vendor_display_name",
        "users.name as created_by_name"
      )
      .leftJoin("contacts", "bills.vendor_id", "contacts.id")
      .leftJoin("users", "bills.created_by", "users.id")
      .where("bills.company_id", companyId);

    // Apply filters
    if (filters.status) {
      query = query.where("bills.status", filters.status);
    }
    if (filters.vendorId) {
      query = query.where("bills.vendor_id", filters.vendorId);
    }
    if (filters.dateFrom) {
      query = query.where("bills.bill_date", ">=", filters.dateFrom);
    }
    if (filters.dateTo) {
      query = query.where("bills.bill_date", "<=", filters.dateTo);
    }
    if (filters.dueDateFrom) {
      query = query.where("bills.due_date", ">=", filters.dueDateFrom);
    }
    if (filters.dueDateTo) {
      query = query.where("bills.due_date", "<=", filters.dueDateTo);
    }
    if (filters.search) {
      query = query.where(function () {
        this.where("bills.bill_number", "ilike", `%${filters.search}%`)
          .orWhere(
            "bills.vendor_invoice_number",
            "ilike",
            `%${filters.search}%`
          )
          .orWhere("contacts.name", "ilike", `%${filters.search}%`);
      });
    }

    // Pagination
    const offset = (filters.page - 1) * filters.limit;
    const totalQuery = query.clone().clearSelect().count("* as count").first();
    const dataQuery = query
      .offset(offset)
      .limit(filters.limit)
      .orderBy("bills.bill_date", "desc");

    const [total, bills] = await Promise.all([totalQuery, dataQuery]);

    res.json({
      data: bills,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total: Number(total?.count || 0),
        pages: Math.ceil(Number(total?.count || 0) / filters.limit),
      },
    });
  })
);

// GET /api/bills/:companyId/:billId - Get single bill
router.get(
  "/:companyId/:billId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, billId } = req.params;

    const bill = await db("bills")
      .select(
        "bills.*",
        "contacts.name as vendor_name",
        "contacts.display_name as vendor_display_name",
        "contacts.email as vendor_email",
        "contacts.phone as vendor_phone",
        "contacts.billing_address as vendor_billing_address",
        "users.name as created_by_name",
        "approver.name as approved_by_name"
      )
      .leftJoin("contacts", "bills.vendor_id", "contacts.id")
      .leftJoin("users", "bills.created_by", "users.id")
      .leftJoin("users as approver", "bills.approved_by", "approver.id")
      .where("bills.company_id", companyId)
      .where("bills.id", billId)
      .first();

    if (!bill) {
      return res.status(404).json({ error: "Bill not found" });
    }

    // Get line items
    const lineItems = await db("bill_line_items")
      .select(
        "bill_line_items.*",
        "accounts.name as account_name",
        "accounts.code as account_code",
        "inventory_items.name as inventory_item_name",
        "inventory_items.sku as inventory_item_sku"
      )
      .leftJoin("accounts", "bill_line_items.account_id", "accounts.id")
      .leftJoin(
        "inventory_items",
        "bill_line_items.inventory_item_id",
        "inventory_items.id"
      )
      .where("bill_id", billId)
      .orderBy("line_number");

    // Get attachments
    const attachments = await db("bill_attachments")
      .select("*")
      .where("bill_id", billId);

    // Get payments
    const payments = await db("bill_payments")
      .select("bill_payments.*", "users.name as created_by_name")
      .leftJoin("users", "bill_payments.created_by", "users.id")
      .where("bill_id", billId)
      .orderBy("payment_date", "desc");

    // Get history
    const history = await db("bill_history")
      .select("bill_history.*", "users.name as changed_by_name")
      .leftJoin("users", "bill_history.changed_by", "users.id")
      .where("bill_id", billId)
      .orderBy("changed_at", "desc");

    res.json({
      ...bill,
      lineItems,
      attachments,
      payments,
      history,
    });
  })
);

// POST /api/bills - Create bill
router.post(
  "/",
  requireAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const data = createBillSchema.parse(req.body);
    const userId = (req as any).user.id;

    const trx = await db.transaction();

    try {
      // Generate bill number
      const lastBill = await trx("bills")
        .where("company_id", data.companyId)
        .orderBy("created_at", "desc")
        .first();

      const lastNumber = lastBill?.bill_number
        ? parseInt(lastBill.bill_number.replace(/\D/g, ""))
        : 0;
      const billNumber = `BILL-${String(lastNumber + 1).padStart(6, "0")}`;

      // Calculate totals
      let subtotal = 0;
      let taxAmount = 0;
      let discountAmount = 0;
      const processedLineItems = data.lineItems.map((item, index) => {
        const lineSubtotal = item.quantity * item.unitCost;
        const lineDiscountAmount =
          (lineSubtotal * item.discountPercentage) / 100;
        const lineTotal = lineSubtotal - lineDiscountAmount;
        const lineTaxAmount = (lineTotal * item.taxRate) / 100;

        subtotal += lineSubtotal;
        discountAmount += lineDiscountAmount;
        taxAmount += lineTaxAmount;

        return {
          ...item,
          lineNumber: index + 1,
          discountAmount: lineDiscountAmount,
          lineTotal,
          taxAmount: lineTaxAmount,
        };
      });

      const totalAmount = subtotal - discountAmount + taxAmount;

      // Create transaction record first
      const transactionId = uuidv4();
      await trx("transactions").insert({
        id: transactionId,
        company_id: data.companyId,
        type: "BILL",
        contact_id: data.vendorId,
        date: data.billDate,
        due_date: data.dueDate,
        reference_number: billNumber,
        description: `Bill from ${data.vendorInvoiceNumber || "vendor"}`,
        amount: totalAmount,
        currency: data.currency,
        exchange_rate: data.exchangeRate,
        status: "PENDING",
        notes: data.notes,
        created_by: userId,
      });

      // Create bill
      const billId = uuidv4();
      await trx("bills").insert({
        id: billId,
        company_id: data.companyId,
        transaction_id: transactionId,
        vendor_id: data.vendorId,
        bill_number: billNumber,
        vendor_invoice_number: data.vendorInvoiceNumber,
        bill_date: data.billDate,
        due_date: data.dueDate,
        payment_terms: data.paymentTerms,
        currency: data.currency,
        exchange_rate: data.exchangeRate,
        subtotal,
        tax_amount: taxAmount,
        discount_amount: discountAmount,
        total_amount: totalAmount,
        balance_due: totalAmount,
        billing_address: data.billingAddress,
        shipping_address: data.shippingAddress,
        notes: data.notes,
        terms_and_conditions: data.termsAndConditions,
        purchase_order_id: data.purchaseOrderId,
        created_by: userId,
      });

      // Create line items
      const lineItemsToInsert = processedLineItems.map((item) => ({
        id: uuidv4(),
        bill_id: billId,
        line_number: item.lineNumber,
        account_id: item.accountId,
        inventory_item_id: item.inventoryItemId,
        item_code: item.itemCode,
        description: item.description,
        unit_of_measure: item.unitOfMeasure,
        quantity: item.quantity,
        unit_cost: item.unitCost,
        discount_percentage: item.discountPercentage,
        discount_amount: item.discountAmount,
        line_total: item.lineTotal,
        tax_rate: item.taxRate,
        tax_amount: item.taxAmount,
        tax_code: item.taxCode,
        notes: item.notes,
      }));

      await trx("bill_line_items").insert(lineItemsToInsert);

      // Create history entry
      await trx("bill_history").insert({
        id: uuidv4(),
        bill_id: billId,
        old_status: null,
        new_status: "DRAFT",
        notes: "Bill created",
        changed_by: userId,
      });

      await trx.commit();

      // Fetch the created bill with related data
      const createdBill = await db("bills")
        .select(
          "bills.*",
          "contacts.name as vendor_name",
          "contacts.display_name as vendor_display_name"
        )
        .leftJoin("contacts", "bills.vendor_id", "contacts.id")
        .where("bills.id", billId)
        .first();

      res.status(201).json(createdBill);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// PUT /api/bills/:companyId/:billId - Update bill
router.put(
  "/:companyId/:billId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, billId } = req.params;
    const data = updateBillSchema.parse(req.body);
    const userId = (req as any).user.id;

    // Check if bill exists and is editable
    const existingBill = await db("bills")
      .where("company_id", companyId)
      .where("id", billId)
      .first();

    if (!existingBill) {
      return res.status(404).json({ error: "Bill not found" });
    }

    if (["PAID", "CANCELLED"].includes(existingBill.status)) {
      return res.status(400).json({
        error: "Cannot edit bill in current status",
      });
    }

    const trx = await db.transaction();

    try {
      const updateData: any = {
        updated_by: userId,
        updated_at: new Date(),
      };

      // Update basic fields
      if (data.vendorId) updateData.vendor_id = data.vendorId;
      if (data.vendorInvoiceNumber !== undefined)
        updateData.vendor_invoice_number = data.vendorInvoiceNumber;
      if (data.billDate) updateData.bill_date = data.billDate;
      if (data.dueDate !== undefined) updateData.due_date = data.dueDate;
      if (data.paymentTerms !== undefined)
        updateData.payment_terms = data.paymentTerms;
      if (data.currency) updateData.currency = data.currency;
      if (data.exchangeRate) updateData.exchange_rate = data.exchangeRate;
      if (data.billingAddress !== undefined)
        updateData.billing_address = data.billingAddress;
      if (data.shippingAddress !== undefined)
        updateData.shipping_address = data.shippingAddress;
      if (data.notes !== undefined) updateData.notes = data.notes;
      if (data.termsAndConditions !== undefined)
        updateData.terms_and_conditions = data.termsAndConditions;

      // Update line items if provided
      if (data.lineItems) {
        // Delete existing line items
        await trx("bill_line_items").where("bill_id", billId).del();

        // Calculate new totals
        let subtotal = 0;
        let taxAmount = 0;
        let discountAmount = 0;
        const processedLineItems = data.lineItems.map((item, index) => {
          const lineSubtotal = item.quantity! * item.unitCost!;
          const lineDiscountAmount =
            (lineSubtotal * (item.discountPercentage || 0)) / 100;
          const lineTotal = lineSubtotal - lineDiscountAmount;
          const lineTaxAmount = (lineTotal * (item.taxRate || 0)) / 100;

          subtotal += lineSubtotal;
          discountAmount += lineDiscountAmount;
          taxAmount += lineTaxAmount;

          return {
            ...item,
            lineNumber: index + 1,
            discountAmount: lineDiscountAmount,
            lineTotal,
            taxAmount: lineTaxAmount,
          };
        });

        const totalAmount = subtotal - discountAmount + taxAmount;

        // Update totals
        updateData.subtotal = subtotal;
        updateData.tax_amount = taxAmount;
        updateData.discount_amount = discountAmount;
        updateData.total_amount = totalAmount;
        updateData.balance_due = totalAmount - existingBill.paid_amount;

        // Insert new line items
        const lineItemsToInsert = processedLineItems.map((item) => ({
          id: uuidv4(),
          bill_id: billId,
          line_number: item.lineNumber,
          account_id: item.accountId,
          inventory_item_id: item.inventoryItemId,
          item_code: item.itemCode,
          description: item.description!,
          unit_of_measure: item.unitOfMeasure,
          quantity: item.quantity!,
          unit_cost: item.unitCost!,
          discount_percentage: item.discountPercentage || 0,
          discount_amount: item.discountAmount,
          line_total: item.lineTotal,
          tax_rate: item.taxRate || 0,
          tax_amount: item.taxAmount,
          tax_code: item.taxCode,
          notes: item.notes,
        }));

        await trx("bill_line_items").insert(lineItemsToInsert);

        // Update related transaction
        await trx("transactions")
          .where("id", existingBill.transaction_id)
          .update({
            amount: totalAmount,
            updated_at: new Date(),
          });
      }

      // Update bill
      await trx("bills").where("id", billId).update(updateData);

      await trx.commit();

      // Fetch updated bill
      const updatedBill = await db("bills")
        .select(
          "bills.*",
          "contacts.name as vendor_name",
          "contacts.display_name as vendor_display_name"
        )
        .leftJoin("contacts", "bills.vendor_id", "contacts.id")
        .where("bills.id", billId)
        .first();

      res.json(updatedBill);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// POST /api/bills/:companyId/:billId/status - Update bill status
router.post(
  "/:companyId/:billId/status",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, billId } = req.params;
    const { status, notes } = req.body;
    const userId = (req as any).user.id;

    const validStatuses = [
      "DRAFT",
      "PENDING_APPROVAL",
      "APPROVED",
      "OVERDUE",
      "PAID",
      "PARTIALLY_PAID",
      "CANCELLED",
    ];

    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: "Invalid status" });
    }

    const existingBill = await db("bills")
      .where("company_id", companyId)
      .where("id", billId)
      .first();

    if (!existingBill) {
      return res.status(404).json({ error: "Bill not found" });
    }

    const trx = await db.transaction();

    try {
      const updateData: any = {
        status,
        updated_by: userId,
        updated_at: new Date(),
      };

      // Set approval fields if approving
      if (status === "APPROVED") {
        updateData.approved_by = userId;
        updateData.approved_at = new Date();
      }

      // Update bill
      await trx("bills").where("id", billId).update(updateData);

      // Update related transaction status
      let transactionStatus = "PENDING";
      if (status === "PAID") transactionStatus = "COMPLETED";
      if (status === "CANCELLED") transactionStatus = "CANCELLED";

      await trx("transactions")
        .where("id", existingBill.transaction_id)
        .update({ status: transactionStatus });

      // Create history entry
      await trx("bill_history").insert({
        id: uuidv4(),
        bill_id: billId,
        old_status: existingBill.status,
        new_status: status,
        notes: notes || `Status changed to ${status}`,
        changed_by: userId,
      });

      await trx.commit();

      res.json({ message: "Status updated successfully", status });
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// POST /api/bills/:companyId/:billId/pay - Record payment for bill
router.post(
  "/:companyId/:billId/pay",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, billId } = req.params;
    const data = payBillSchema.parse(req.body);
    const userId = (req as any).user.id;

    const existingBill = await db("bills")
      .where("company_id", companyId)
      .where("id", billId)
      .first();

    if (!existingBill) {
      return res.status(404).json({ error: "Bill not found" });
    }

    if (existingBill.status === "PAID") {
      return res.status(400).json({ error: "Bill is already paid" });
    }

    if (data.paymentAmount > existingBill.balance_due) {
      return res
        .status(400)
        .json({ error: "Payment amount exceeds balance due" });
    }

    const trx = await db.transaction();

    try {
      // Create payment record
      await trx("bill_payments").insert({
        id: uuidv4(),
        bill_id: billId,
        payment_date: data.paymentDate,
        payment_amount: data.paymentAmount,
        payment_method: data.paymentMethod,
        payment_reference: data.paymentReference,
        notes: data.notes,
        created_by: userId,
      });

      // Update bill amounts (trigger will handle this automatically)
      // But we need to update status based on payment
      const newPaidAmount = existingBill.paid_amount + data.paymentAmount;
      const newBalanceDue = existingBill.total_amount - newPaidAmount;

      let newStatus = existingBill.status;
      if (newBalanceDue <= 0) {
        newStatus = "PAID";
      } else if (newPaidAmount > 0) {
        newStatus = "PARTIALLY_PAID";
      }

      await trx("bills").where("id", billId).update({
        status: newStatus,
        updated_by: userId,
        updated_at: new Date(),
      });

      // Create history entry
      await trx("bill_history").insert({
        id: uuidv4(),
        bill_id: billId,
        old_status: existingBill.status,
        new_status: newStatus,
        notes: `Payment of ${data.paymentAmount} recorded`,
        changed_by: userId,
      });

      await trx.commit();

      res.json({
        message: "Payment recorded successfully",
        newStatus,
        paidAmount: newPaidAmount,
        balanceDue: newBalanceDue,
      });
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// DELETE /api/bills/:companyId/:billId - Delete bill
router.delete(
  "/:companyId/:billId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, billId } = req.params;

    const existingBill = await db("bills")
      .where("company_id", companyId)
      .where("id", billId)
      .first();

    if (!existingBill) {
      return res.status(404).json({ error: "Bill not found" });
    }

    if (!["DRAFT", "CANCELLED"].includes(existingBill.status)) {
      return res.status(400).json({
        error: "Can only delete draft or cancelled bills",
      });
    }

    const trx = await db.transaction();

    try {
      // Delete bill (cascade will handle related records)
      await trx("bills").where("id", billId).del();

      // Delete related transaction
      await trx("transactions").where("id", existingBill.transaction_id).del();

      await trx.commit();

      res.json({ message: "Bill deleted successfully" });
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

export default router;
