import express from "express";
import type { Request, Response } from "express";
import { z } from "zod";
import { as<PERSON><PERSON><PERSON><PERSON>, NotFoundError, ValidationError } from "../middleware/errorHandler";
import { authenticateToken, requirePermission } from "../middleware/auth";
import db from "../config/database";

// Extend Request interface to include user
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    companyId: string;
  };
}

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const updateUserSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  phone: z.string().optional(),
  email: z.string().email().optional(),
});

// GET /api/users/me
router.get(
  "/me",
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const user = await db("users")
      .select(
        "users.id",
        "users.email",
        "users.first_name",
        "users.last_name",
        "users.phone",
        "users.is_active",
        "users.last_login_at",
        "users.created_at",
        "users.updated_at",
        "roles.id as role_id",
        "roles.name as role_name",
        "roles.permissions"
      )
      .join("roles", "users.role_id", "roles.id")
      .where("users.id", req.user!.id)
      .first();

    if (!user) {
      throw new NotFoundError("User not found");
    }

    // Get user's companies
    const companies = await db("user_companies")
      .select(
        "companies.id",
        "companies.name",
        "companies.base_currency",
        "user_companies.role_id"
      )
      .join("companies", "user_companies.company_id", "companies.id")
      .where("user_companies.user_id", user.id)
      .where("user_companies.is_active", true)
      .where("companies.is_active", true);

    res.json({
      id: user.id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      phone: user.phone,
      isActive: user.is_active,
      lastLoginAt: user.last_login_at,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      role: {
        id: user.role_id,
        name: user.role_name,
        permissions: user.permissions,
      },
      companies,
    });
  })
);

// PUT /api/users/me
router.put(
  "/me",
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const data = updateUserSchema.parse(req.body);

    const [updatedUser] = await db("users")
      .where("id", req.user!.id)
      .update({
        ...data,
        first_name: data.firstName,
        last_name: data.lastName,
        updated_at: new Date(),
      })
      .returning([
        "id",
        "email",
        "first_name",
        "last_name",
        "phone",
        "is_active",
        "created_at",
        "updated_at",
      ]);

    res.json({
      id: updatedUser.id,
      email: updatedUser.email,
      firstName: updatedUser.first_name,
      lastName: updatedUser.last_name,
      phone: updatedUser.phone,
      isActive: updatedUser.is_active,
      createdAt: updatedUser.created_at,
      updatedAt: updatedUser.updated_at,
    });
  })
);

// POST /api/users (admin only)
router.post(
  "/",
  requirePermission("users:create"),
  asyncHandler(async (req: Request, res: Response) => {
    const createUserSchema = z.object({
      firstName: z.string().min(1),
      lastName: z.string().min(1),
      email: z.string().email(),
      phone: z.string().optional(),
      roleId: z.string().uuid(),
      password: z.string().min(8),
      isActive: z.boolean().default(true),
    });

    const data = createUserSchema.parse(req.body);

    // Check if user already exists
    const existingUser = await db("users")
      .where("email", data.email.toLowerCase())
      .first();

    if (existingUser) {
      throw new ValidationError("User with this email already exists");
    }

    // Hash password
    const bcrypt = await import("bcryptjs");
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(data.password, saltRounds);

    // Create user
    const [newUser] = await db("users")
      .insert({
        email: data.email.toLowerCase(),
        password_hash: passwordHash,
        first_name: data.firstName,
        last_name: data.lastName,
        phone: data.phone,
        role_id: data.roleId,
        is_active: data.isActive,
      })
      .returning([
        "id",
        "email",
        "first_name",
        "last_name",
        "phone",
        "is_active",
        "created_at",
        "updated_at",
      ]);

    // Get role information
    const role = await db("roles").where("id", data.roleId).first();

    res.status(201).json({
      id: newUser.id,
      email: newUser.email,
      firstName: newUser.first_name,
      lastName: newUser.last_name,
      phone: newUser.phone,
      isActive: newUser.is_active,
      createdAt: newUser.created_at,
      updatedAt: newUser.updated_at,
      role: {
        id: role.id,
        name: role.name,
        permissions: role.permissions,
      },
    });
  })
);

// GET /api/users (admin only)
router.get(
  "/",
  requirePermission("users:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { page = 1, limit = 10, search, companyId } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    let query = db("users")
      .select(
        "users.id",
        "users.email",
        "users.first_name",
        "users.last_name",
        "users.phone",
        "users.is_active",
        "users.last_login_at",
        "users.created_at",
        "roles.id as role_id",
        "roles.name as role_name",
        "roles.description as role_description",
        "roles.permissions as role_permissions",
        "roles.is_active as role_is_active"
      )
      .join("roles", "users.role_id", "roles.id")
      .orderBy("users.created_at", "desc");

    // Create a separate count query without the JOIN
    let countQuery = db("users");

    // Filter by company if companyId is provided
    if (companyId) {
      query = query
        .join("user_companies", "users.id", "user_companies.user_id")
        .where("user_companies.company_id", companyId);

      countQuery = countQuery
        .join("user_companies", "users.id", "user_companies.user_id")
        .where("user_companies.company_id", companyId);
    }

    if (search) {
      const searchCondition = function () {
        this.where("users.first_name", "ilike", `%${search}%`)
          .orWhere("users.last_name", "ilike", `%${search}%`)
          .orWhere("users.email", "ilike", `%${search}%`);
      };

      query = query.where(searchCondition);
      countQuery = countQuery.where(searchCondition);
    }

    const total = await countQuery.count("* as count").first();
    const users = await query.limit(Number(limit)).offset(offset);

    res.json({
      data: users.map((user) => ({
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        phone: user.phone,
        isActive: user.is_active,
        lastLoginAt: user.last_login_at,
        createdAt: user.created_at,
        updatedAt: user.updated_at || user.created_at,
        companies: [], // TODO: Implement user companies
        role: {
          id: user.role_id,
          name: user.role_name,
          description: user.role_description,
          permissions: user.role_permissions || [],
          isActive: user.role_is_active,
        },
      })),
      total: parseInt(total?.count as string),
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(parseInt(total?.count as string) / Number(limit)),
    });
  })
);

// PUT /api/users/:userId (admin only)
router.put(
  "/:userId",
  requirePermission("users:update"),
  asyncHandler(async (req: Request, res: Response) => {
    const { userId } = req.params;
    const updateData = updateUserSchema.parse(req.body);

    // Check if user exists
    const existingUser = await db("users").where("id", userId).first();
    if (!existingUser) {
      throw new NotFoundError("User not found");
    }

    // If email is being updated, check for duplicates
    if (updateData.email && updateData.email !== existingUser.email) {
      const emailExists = await db("users")
        .where("email", updateData.email.toLowerCase())
        .where("id", "!=", userId)
        .first();

      if (emailExists) {
        throw new ValidationError("Email already in use");
      }
    }

    // Update user
    const [updatedUser] = await db("users")
      .where("id", userId)
      .update({
        ...updateData,
        first_name: updateData.firstName,
        last_name: updateData.lastName,
        updated_at: new Date(),
      })
      .returning([
        "id",
        "email",
        "first_name",
        "last_name",
        "phone",
        "is_active",
        "role_id",
        "created_at",
        "updated_at",
      ]);

    // Get role information
    const role = await db("roles").where("id", updatedUser.role_id).first();

    res.json({
      id: updatedUser.id,
      email: updatedUser.email,
      firstName: updatedUser.first_name,
      lastName: updatedUser.last_name,
      phone: updatedUser.phone,
      isActive: updatedUser.is_active,
      createdAt: updatedUser.created_at,
      updatedAt: updatedUser.updated_at,
      role: {
        id: role.id,
        name: role.name,
        permissions: role.permissions,
      },
    });
  })
);

// DELETE /api/users/:userId (admin only)
router.delete(
  "/:userId",
  requirePermission("users:delete"),
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { userId } = req.params;

    // Check if user exists
    const existingUser = await db("users").where("id", userId).first();
    if (!existingUser) {
      throw new NotFoundError("User not found");
    }

    // Prevent self-deletion
    if (userId === req.user!.id) {
      throw new ValidationError("Cannot delete your own account");
    }

    // Delete user (this will cascade to user_companies)
    await db("users").where("id", userId).del();

    res.status(204).send();
  })
);

export default router;
