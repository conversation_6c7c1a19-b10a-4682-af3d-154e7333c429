import express from "express";
import type { Request, Response } from "express";
import { z } from "zod";
import {
  authenticateToken,
  requireCompanyAccess,
  requirePermission,
} from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { db } from "../config/database";
import { v4 as uuidv4 } from "uuid";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const createBudgetSchema = z.object({
  companyId: z.string().uuid(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  budgetType: z.enum(["ANNUAL", "QUARTERLY", "MONTHLY"]),
  fiscalYear: z.number().int().min(2020).max(2030),
  startDate: z.string(),
  endDate: z.string(),
  currency: z.string().length(3).default("USD"),
  status: z.enum(["DRAFT", "ACTIVE", "LOCKED", "ARCHIVED"]).default("DRAFT"),
  lineItems: z.array(
    z.object({
      accountId: z.string().uuid(),
      amount: z.number(),
      period: z.string().optional(),
      notes: z.string().optional(),
    })
  ),
});

const updateBudgetSchema = createBudgetSchema.partial().extend({
  id: z.string().uuid(),
});

// GET /api/budgets/:companyId - Get all budgets for a company
router.get(
  "/:companyId",
  requireCompanyAccess,
  requirePermission("budgets:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { fiscalYear, budgetType, status, page = 1, limit = 20 } = req.query;

    try {
      let query = db("budgets")
        .select(
          "budgets.*",
          db.raw("COUNT(budget_line_items.id) as line_item_count"),
          db.raw("SUM(budget_line_items.amount) as total_budget_amount")
        )
        .leftJoin(
          "budget_line_items",
          "budgets.id",
          "budget_line_items.budget_id"
        )
        .where("budgets.company_id", companyId)
        .groupBy("budgets.id");

      // Apply filters
      if (fiscalYear) {
        query = query.where("budgets.fiscal_year", fiscalYear);
      }
      if (budgetType) {
        query = query.where("budgets.budget_type", budgetType);
      }
      if (status) {
        query = query.where("budgets.status", status);
      }

      // Pagination
      const offset = (parseInt(page as string) - 1) * parseInt(limit as string);
      const budgets = await query
        .orderBy("budgets.created_at", "desc")
        .limit(parseInt(limit as string))
        .offset(offset);

      // Get total count
      const totalQuery = db("budgets")
        .where("company_id", companyId)
        .count("* as count")
        .first();

      if (fiscalYear) {
        totalQuery.where("fiscal_year", fiscalYear);
      }
      if (budgetType) {
        totalQuery.where("budget_type", budgetType);
      }
      if (status) {
        totalQuery.where("status", status);
      }

      const totalResult = await totalQuery;
      const total = parseInt(totalResult?.count as string) || 0;

      res.json({
        success: true,
        data: budgets,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total,
          totalPages: Math.ceil(total / parseInt(limit as string)),
        },
      });
    } catch (error) {
      console.error("Failed to get budgets:", error);
      res.status(500).json({ error: "Failed to get budgets" });
    }
  })
);

// GET /api/budgets/:companyId/:budgetId - Get a specific budget with line items
router.get(
  "/:companyId/:budgetId",
  requireCompanyAccess,
  requirePermission("budgets:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, budgetId } = req.params;

    try {
      // Get budget details
      const budget = await db("budgets")
        .where("id", budgetId)
        .where("company_id", companyId)
        .first();

      if (!budget) {
        return res.status(404).json({ error: "Budget not found" });
      }

      // Get budget line items with account details
      const lineItems = await db("budget_line_items")
        .select(
          "budget_line_items.*",
          "accounts.code as account_code",
          "accounts.name as account_name",
          "accounts.type as account_type"
        )
        .leftJoin("accounts", "budget_line_items.account_id", "accounts.id")
        .where("budget_line_items.budget_id", budgetId)
        .orderBy("accounts.code");

      res.json({
        success: true,
        data: {
          ...budget,
          lineItems,
        },
      });
    } catch (error) {
      console.error("Failed to get budget:", error);
      res.status(500).json({ error: "Failed to get budget" });
    }
  })
);

// POST /api/budgets - Create a new budget
router.post(
  "/",
  requirePermission("budgets:create"),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const validatedData = createBudgetSchema.parse(req.body);

      const budgetId = uuidv4();

      await db.transaction(async (trx) => {
        // Create budget
        await trx("budgets").insert({
          id: budgetId,
          company_id: validatedData.companyId,
          name: validatedData.name,
          description: validatedData.description,
          budget_type: validatedData.budgetType,
          fiscal_year: validatedData.fiscalYear,
          start_date: validatedData.startDate,
          end_date: validatedData.endDate,
          currency: validatedData.currency,
          status: validatedData.status,
          created_by: (req as any).user?.id,
        });

        // Create budget line items
        if (validatedData.lineItems && validatedData.lineItems.length > 0) {
          const lineItems = validatedData.lineItems.map((item, index) => ({
            id: uuidv4(),
            budget_id: budgetId,
            account_id: item.accountId,
            amount: item.amount,
            period: item.period,
            notes: item.notes,
            line_number: index + 1,
          }));

          await trx("budget_line_items").insert(lineItems);
        }
      });

      // Fetch the created budget with line items
      const createdBudget = await getBudgetWithLineItems(budgetId);

      res.status(201).json({
        success: true,
        data: createdBudget,
        message: "Budget created successfully",
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: "Validation failed",
          details: error.errors,
        });
      }

      console.error("Failed to create budget:", error);
      res.status(500).json({ error: "Failed to create budget" });
    }
  })
);

// PUT /api/budgets/:companyId/:budgetId - Update a budget
router.put(
  "/:companyId/:budgetId",
  requireCompanyAccess,
  requirePermission("budgets:update"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, budgetId } = req.params;

    try {
      const validatedData = updateBudgetSchema.parse({
        ...req.body,
        id: budgetId,
      });

      // Check if budget exists and belongs to company
      const existingBudget = await db("budgets")
        .where("id", budgetId)
        .where("company_id", companyId)
        .first();

      if (!existingBudget) {
        return res.status(404).json({ error: "Budget not found" });
      }

      // Check if budget is locked
      if (existingBudget.status === "LOCKED") {
        return res.status(400).json({
          error: "Cannot update locked budget",
        });
      }

      await db.transaction(async (trx) => {
        // Update budget
        await trx("budgets").where("id", budgetId).update({
          name: validatedData.name,
          description: validatedData.description,
          budget_type: validatedData.budgetType,
          fiscal_year: validatedData.fiscalYear,
          start_date: validatedData.startDate,
          end_date: validatedData.endDate,
          currency: validatedData.currency,
          status: validatedData.status,
          updated_at: new Date(),
        });

        // Update line items if provided
        if (validatedData.lineItems) {
          // Delete existing line items
          await trx("budget_line_items").where("budget_id", budgetId).del();

          // Insert new line items
          if (validatedData.lineItems.length > 0) {
            const lineItems = validatedData.lineItems.map((item, index) => ({
              id: uuidv4(),
              budget_id: budgetId,
              account_id: item.accountId,
              amount: item.amount,
              period: item.period,
              notes: item.notes,
              line_number: index + 1,
            }));

            await trx("budget_line_items").insert(lineItems);
          }
        }
      });

      // Fetch the updated budget
      const updatedBudget = await getBudgetWithLineItems(budgetId);

      res.json({
        success: true,
        data: updatedBudget,
        message: "Budget updated successfully",
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: "Validation failed",
          details: error.errors,
        });
      }

      console.error("Failed to update budget:", error);
      res.status(500).json({ error: "Failed to update budget" });
    }
  })
);

// Helper function to get budget with line items
async function getBudgetWithLineItems(budgetId: string) {
  const budget = await db("budgets").where("id", budgetId).first();

  const lineItems = await db("budget_line_items")
    .select(
      "budget_line_items.*",
      "accounts.code as account_code",
      "accounts.name as account_name",
      "accounts.type as account_type"
    )
    .leftJoin("accounts", "budget_line_items.account_id", "accounts.id")
    .where("budget_line_items.budget_id", budgetId)
    .orderBy("budget_line_items.line_number");

  return {
    ...budget,
    lineItems,
  };
}

// GET /api/budgets/:companyId/:budgetId/vs-actual - Budget vs Actual report
router.get(
  "/:companyId/:budgetId/vs-actual",
  requireCompanyAccess,
  requirePermission("budgets:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, budgetId } = req.params;
    const { period = "monthly", startDate, endDate, accountIds } = req.query;

    try {
      const report = await generateBudgetVsActualReport({
        companyId,
        budgetId,
        period: period as string,
        startDate: startDate as string,
        endDate: endDate as string,
        accountIds: accountIds ? (accountIds as string).split(",") : undefined,
      });

      res.json({
        success: true,
        data: report,
      });
    } catch (error) {
      console.error("Failed to generate budget vs actual report:", error);
      res.status(500).json({ error: "Failed to generate report" });
    }
  })
);

// GET /api/budgets/:companyId/:budgetId/variance-analysis - Variance analysis
router.get(
  "/:companyId/:budgetId/variance-analysis",
  requireCompanyAccess,
  requirePermission("budgets:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, budgetId } = req.params;
    const { threshold = 10, period = "monthly" } = req.query;

    try {
      const analysis = await generateVarianceAnalysis({
        companyId,
        budgetId,
        threshold: parseFloat(threshold as string),
        period: period as string,
      });

      res.json({
        success: true,
        data: analysis,
      });
    } catch (error) {
      console.error("Failed to generate variance analysis:", error);
      res.status(500).json({ error: "Failed to generate analysis" });
    }
  })
);

// POST /api/budgets/:companyId/:budgetId/refresh-cache - Refresh budget vs actual cache
router.post(
  "/:companyId/:budgetId/refresh-cache",
  requireCompanyAccess,
  requirePermission("budgets:update"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, budgetId } = req.params;

    try {
      await refreshBudgetVsActualCache(budgetId);

      res.json({
        success: true,
        message: "Budget vs actual cache refreshed successfully",
      });
    } catch (error) {
      console.error("Failed to refresh cache:", error);
      res.status(500).json({ error: "Failed to refresh cache" });
    }
  })
);

// Helper function to generate budget vs actual report
async function generateBudgetVsActualReport(params: {
  companyId: string;
  budgetId: string;
  period: string;
  startDate?: string;
  endDate?: string;
  accountIds?: string[];
}) {
  try {
    // Get budget details
    const budget = await db("budgets")
      .where("id", params.budgetId)
      .where("company_id", params.companyId)
      .first();

    if (!budget) {
      throw new Error("Budget not found");
    }

    // Get budget line items
    let budgetQuery = db("budget_line_items")
      .select(
        "budget_line_items.*",
        "accounts.code as account_code",
        "accounts.name as account_name",
        "accounts.type as account_type"
      )
      .leftJoin("accounts", "budget_line_items.account_id", "accounts.id")
      .where("budget_line_items.budget_id", params.budgetId);

    if (params.accountIds) {
      budgetQuery = budgetQuery.whereIn(
        "budget_line_items.account_id",
        params.accountIds
      );
    }

    const budgetLineItems = await budgetQuery.orderBy("accounts.code");

    // Calculate actual amounts for each account
    const reportData = [];

    for (const lineItem of budgetLineItems) {
      const actualAmount = await calculateActualAmount({
        accountId: lineItem.account_id,
        companyId: params.companyId,
        startDate: params.startDate || budget.start_date,
        endDate: params.endDate || budget.end_date,
        period: params.period,
      });

      const variance = actualAmount - lineItem.amount;
      const variancePercentage =
        lineItem.amount !== 0
          ? (variance / Math.abs(lineItem.amount)) * 100
          : 0;

      reportData.push({
        accountId: lineItem.account_id,
        accountCode: lineItem.account_code,
        accountName: lineItem.account_name,
        accountType: lineItem.account_type,
        budgetAmount: parseFloat(lineItem.amount),
        actualAmount: actualAmount,
        variance: variance,
        variancePercentage: variancePercentage,
        status: getVarianceStatus(variancePercentage, lineItem.account_type),
      });
    }

    // Calculate summary
    const summary = {
      totalBudget: reportData.reduce((sum, item) => sum + item.budgetAmount, 0),
      totalActual: reportData.reduce((sum, item) => sum + item.actualAmount, 0),
      totalVariance: reportData.reduce((sum, item) => sum + item.variance, 0),
      favorableVariances: reportData.filter(
        (item) => item.status === "FAVORABLE"
      ).length,
      unfavorableVariances: reportData.filter(
        (item) => item.status === "UNFAVORABLE"
      ).length,
      onTargetVariances: reportData.filter(
        (item) => item.status === "ON_TARGET"
      ).length,
    };

    summary.totalVariancePercentage =
      summary.totalBudget !== 0
        ? (summary.totalVariance / Math.abs(summary.totalBudget)) * 100
        : 0;

    return {
      budget: {
        id: budget.id,
        name: budget.name,
        fiscalYear: budget.fiscal_year,
        budgetType: budget.budget_type,
        startDate: budget.start_date,
        endDate: budget.end_date,
      },
      period: params.period,
      reportDate: new Date().toISOString(),
      summary,
      lineItems: reportData,
    };
  } catch (error) {
    console.error("Error generating budget vs actual report:", error);
    throw error;
  }
}

// Helper function to calculate actual amount for an account
async function calculateActualAmount(params: {
  accountId: string;
  companyId: string;
  startDate: string;
  endDate: string;
  period: string;
}) {
  try {
    const result = await db("transaction_entries")
      .join(
        "transactions",
        "transaction_entries.transaction_id",
        "transactions.id"
      )
      .where("transaction_entries.account_id", params.accountId)
      .where("transactions.company_id", params.companyId)
      .where("transactions.status", "POSTED")
      .where("transactions.transaction_date", ">=", params.startDate)
      .where("transactions.transaction_date", "<=", params.endDate)
      .sum({
        debit_total: "transaction_entries.debit_amount",
        credit_total: "transaction_entries.credit_amount",
      })
      .first();

    const debitTotal = parseFloat(result?.debit_total || 0);
    const creditTotal = parseFloat(result?.credit_total || 0);

    // Get account type to determine if we use debit or credit balance
    const account = await db("accounts").where("id", params.accountId).first();

    // For revenue and liability accounts, credit increases the balance
    // For asset and expense accounts, debit increases the balance
    if (
      account?.type === "REVENUE" ||
      account?.type === "LIABILITY" ||
      account?.type === "EQUITY"
    ) {
      return creditTotal - debitTotal;
    } else {
      return debitTotal - creditTotal;
    }
  } catch (error) {
    console.error("Error calculating actual amount:", error);
    return 0;
  }
}

// Helper function to determine variance status
function getVarianceStatus(
  variancePercentage: number,
  accountType: string
): string {
  const threshold = 5; // 5% threshold

  if (Math.abs(variancePercentage) <= threshold) {
    return "ON_TARGET";
  }

  // For revenue accounts, higher actual is favorable
  // For expense accounts, lower actual is favorable
  if (accountType === "REVENUE") {
    return variancePercentage > 0 ? "FAVORABLE" : "UNFAVORABLE";
  } else if (accountType === "EXPENSE") {
    return variancePercentage < 0 ? "FAVORABLE" : "UNFAVORABLE";
  } else {
    // For other account types, use general logic
    return variancePercentage > 0 ? "OVER_BUDGET" : "UNDER_BUDGET";
  }
}

// Helper function to generate variance analysis
async function generateVarianceAnalysis(params: {
  companyId: string;
  budgetId: string;
  threshold: number;
  period: string;
}) {
  try {
    // Get budget vs actual data
    const reportData = await generateBudgetVsActualReport({
      companyId: params.companyId,
      budgetId: params.budgetId,
      period: params.period,
    });

    // Filter items that exceed the threshold
    const significantVariances = reportData.lineItems.filter(
      (item) => Math.abs(item.variancePercentage) > params.threshold
    );

    // Categorize variances
    const analysis = {
      summary: {
        totalVariances: significantVariances.length,
        favorableVariances: significantVariances.filter(
          (item) => item.status === "FAVORABLE"
        ).length,
        unfavorableVariances: significantVariances.filter(
          (item) => item.status === "UNFAVORABLE"
        ).length,
        averageVariancePercentage:
          significantVariances.length > 0
            ? significantVariances.reduce(
                (sum, item) => sum + Math.abs(item.variancePercentage),
                0
              ) / significantVariances.length
            : 0,
        totalVarianceAmount: significantVariances.reduce(
          (sum, item) => sum + Math.abs(item.variance),
          0
        ),
      },
      topVariances: {
        mostOverBudget: significantVariances
          .filter((item) => item.variance > 0)
          .sort((a, b) => b.variancePercentage - a.variancePercentage)
          .slice(0, 5),
        mostUnderBudget: significantVariances
          .filter((item) => item.variance < 0)
          .sort((a, b) => a.variancePercentage - b.variancePercentage)
          .slice(0, 5),
        largestAbsoluteVariances: significantVariances
          .sort((a, b) => Math.abs(b.variance) - Math.abs(a.variance))
          .slice(0, 10),
      },
      recommendations: generateRecommendations(significantVariances),
      threshold: params.threshold,
      analysisDate: new Date().toISOString(),
    };

    return analysis;
  } catch (error) {
    console.error("Error generating variance analysis:", error);
    throw error;
  }
}

// Helper function to generate recommendations based on variances
function generateRecommendations(variances: any[]): string[] {
  const recommendations: string[] = [];

  const overBudgetExpenses = variances.filter(
    (v) =>
      v.accountType === "EXPENSE" &&
      v.variance > 0 &&
      Math.abs(v.variancePercentage) > 15
  );

  const underBudgetRevenue = variances.filter(
    (v) =>
      v.accountType === "REVENUE" &&
      v.variance < 0 &&
      Math.abs(v.variancePercentage) > 10
  );

  if (overBudgetExpenses.length > 0) {
    recommendations.push(
      `Review expense controls for ${overBudgetExpenses.length} accounts that are significantly over budget.`
    );
  }

  if (underBudgetRevenue.length > 0) {
    recommendations.push(
      `Investigate revenue shortfalls in ${underBudgetRevenue.length} accounts and consider corrective actions.`
    );
  }

  const highVarianceCount = variances.filter(
    (v) => Math.abs(v.variancePercentage) > 25
  ).length;
  if (highVarianceCount > 5) {
    recommendations.push(
      "Consider revising budget assumptions as multiple accounts show significant variances."
    );
  }

  if (recommendations.length === 0) {
    recommendations.push(
      "Budget performance is within acceptable variance thresholds."
    );
  }

  return recommendations;
}

// Helper function to refresh budget vs actual cache
async function refreshBudgetVsActualCache(budgetId: string) {
  try {
    // Get budget details
    const budget = await db("budgets").where("id", budgetId).first();

    if (!budget) {
      throw new Error("Budget not found");
    }

    // Clear existing cache for this budget
    await db("budget_vs_actual_cache").where("budget_id", budgetId).del();

    // Get all budget line items
    const lineItems = await db("budget_line_items").where(
      "budget_id",
      budgetId
    );

    // Calculate and cache data for each line item
    for (const lineItem of lineItems) {
      const actualAmount = await calculateActualAmount({
        accountId: lineItem.account_id,
        companyId: budget.company_id,
        startDate: budget.start_date,
        endDate: budget.end_date,
        period: budget.budget_type.toLowerCase(),
      });

      const variance = actualAmount - lineItem.amount;
      const variancePercentage =
        lineItem.amount !== 0
          ? (variance / Math.abs(lineItem.amount)) * 100
          : 0;

      // Determine period string based on budget type
      let period: string;
      const year = new Date(budget.start_date).getFullYear();

      switch (budget.budget_type) {
        case "ANNUAL":
          period = year.toString();
          break;
        case "QUARTERLY":
          const quarter = Math.ceil(
            (new Date(budget.start_date).getMonth() + 1) / 3
          );
          period = `${year}-Q${quarter}`;
          break;
        case "MONTHLY":
          const month = (new Date(budget.start_date).getMonth() + 1)
            .toString()
            .padStart(2, "0");
          period = `${year}-${month}`;
          break;
        default:
          period = year.toString();
      }

      // Insert cache record
      await db("budget_vs_actual_cache").insert({
        id: uuidv4(),
        budget_id: budgetId,
        account_id: lineItem.account_id,
        period: period,
        budget_amount: lineItem.amount,
        actual_amount: actualAmount,
        variance_amount: variance,
        variance_percentage: variancePercentage,
        calculated_at: new Date(),
      });
    }

    console.log(`Budget vs actual cache refreshed for budget ${budgetId}`);
  } catch (error) {
    console.error("Error refreshing budget vs actual cache:", error);
    throw error;
  }
}

export default router;
