import express from "express";
import type { Request, Response } from "express";
import { z } from "zod";
import axios from "axios";
import crypto from "crypto";
import {
  authenticateToken,
  requireCompanyAccess,
  requirePermission,
} from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { db } from "../config/database";
import { v4 as uuidv4 } from "uuid";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const bankConnectionSchema = z.object({
  companyId: z.string().uuid(),
  bankCode: z.string().min(1),
  accountNumber: z.string().min(1),
  accountName: z.string().min(1),
  bankName: z.string().min(1),
  credentials: z
    .object({
      username: z.string().optional(),
      password: z.string().optional(),
      apiKey: z.string().optional(),
      clientId: z.string().optional(),
      clientSecret: z.string().optional(),
    })
    .optional(),
  isActive: z.boolean().default(true),
});

const transactionSyncSchema = z.object({
  connectionId: z.string().uuid(),
  startDate: z.string(),
  endDate: z.string(),
  autoReconcile: z.boolean().default(false),
});

// GET /api/bank-integrations/:companyId/supported-banks - Get supported banks
router.get(
  "/:companyId/supported-banks",
  requireCompanyAccess,
  requirePermission("bank_integration:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { country = "TZ" } = req.query;

    try {
      const supportedBanks = getSupportedBanks(country as string);

      res.json({
        success: true,
        data: {
          country,
          banks: supportedBanks,
        },
      });
    } catch (error) {
      console.error("Failed to get supported banks:", error);
      res.status(500).json({ error: "Failed to get supported banks" });
    }
  })
);

// GET /api/bank-integrations/:companyId/connections - Get bank connections
router.get(
  "/:companyId/connections",
  requireCompanyAccess,
  requirePermission("bank_integration:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;

    try {
      const connections = await db("bank_connections")
        .select(
          "bank_connections.*",
          db.raw("COUNT(bank_transactions.id) as transaction_count"),
          db.raw("MAX(bank_transactions.synced_at) as last_sync")
        )
        .leftJoin(
          "bank_transactions",
          "bank_connections.id",
          "bank_transactions.connection_id"
        )
        .where("bank_connections.company_id", companyId)
        .groupBy("bank_connections.id")
        .orderBy("bank_connections.created_at", "desc");

      // Remove sensitive credentials from response
      const sanitizedConnections = connections.map((conn) => {
        const { credentials, ...safeConnection } = conn;
        return {
          ...safeConnection,
          hasCredentials: !!credentials,
        };
      });

      res.json({
        success: true,
        data: sanitizedConnections,
      });
    } catch (error) {
      console.error("Failed to get bank connections:", error);
      res.status(500).json({ error: "Failed to get bank connections" });
    }
  })
);

// POST /api/bank-integrations/connect - Create bank connection
router.post(
  "/connect",
  requirePermission("bank_integration:create"),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const validatedData = bankConnectionSchema.parse(req.body);

      // Encrypt credentials
      const encryptedCredentials = validatedData.credentials
        ? encryptCredentials(validatedData.credentials)
        : null;

      // Test connection
      const connectionTest = await testBankConnection({
        bankCode: validatedData.bankCode,
        accountNumber: validatedData.accountNumber,
        credentials: validatedData.credentials,
      });

      if (!connectionTest.success) {
        return res.status(400).json({
          error: "Bank connection test failed",
          details: connectionTest.error,
        });
      }

      const connectionId = uuidv4();

      const [connection] = await db("bank_connections")
        .insert({
          id: connectionId,
          company_id: validatedData.companyId,
          bank_code: validatedData.bankCode,
          bank_name: validatedData.bankName,
          account_number: validatedData.accountNumber,
          account_name: validatedData.accountName,
          credentials: encryptedCredentials,
          is_active: validatedData.isActive,
          connection_status: "CONNECTED",
          last_test_at: new Date(),
          created_by: (req as any).user?.id,
        })
        .returning("*");

      // Remove credentials from response
      const { credentials, ...safeConnection } = connection;

      res.status(201).json({
        success: true,
        data: {
          ...safeConnection,
          hasCredentials: !!credentials,
        },
        message: "Bank connection created successfully",
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: "Validation failed",
          details: error.errors,
        });
      }

      console.error("Failed to create bank connection:", error);
      res.status(500).json({ error: "Failed to create bank connection" });
    }
  })
);

// POST /api/bank-integrations/:companyId/:connectionId/sync - Sync transactions
router.post(
  "/:companyId/:connectionId/sync",
  requireCompanyAccess,
  requirePermission("bank_integration:sync"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, connectionId } = req.params;

    try {
      const validatedData = transactionSyncSchema.parse({
        connectionId,
        ...req.body,
      });

      // Get bank connection
      const connection = await db("bank_connections")
        .where("id", connectionId)
        .where("company_id", companyId)
        .first();

      if (!connection) {
        return res.status(404).json({ error: "Bank connection not found" });
      }

      if (!connection.is_active) {
        return res.status(400).json({ error: "Bank connection is inactive" });
      }

      // Sync transactions
      const syncResult = await syncBankTransactions({
        connection,
        startDate: validatedData.startDate,
        endDate: validatedData.endDate,
        autoReconcile: validatedData.autoReconcile,
        userId: (req as any).user?.id,
      });

      // Update connection last sync
      await db("bank_connections")
        .where("id", connectionId)
        .update({
          last_sync_at: new Date(),
          sync_status: syncResult.success ? "SUCCESS" : "FAILED",
          updated_at: new Date(),
        });

      res.json({
        success: true,
        data: syncResult,
        message: "Transaction sync completed",
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: "Validation failed",
          details: error.errors,
        });
      }

      console.error("Failed to sync transactions:", error);
      res.status(500).json({ error: "Failed to sync transactions" });
    }
  })
);

// GET /api/bank-integrations/:companyId/:connectionId/transactions - Get bank transactions
router.get(
  "/:companyId/:connectionId/transactions",
  requireCompanyAccess,
  requirePermission("bank_integration:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, connectionId } = req.params;
    const { startDate, endDate, status, page = 1, limit = 50 } = req.query;

    try {
      // Verify connection belongs to company
      const connection = await db("bank_connections")
        .where("id", connectionId)
        .where("company_id", companyId)
        .first();

      if (!connection) {
        return res.status(404).json({ error: "Bank connection not found" });
      }

      let query = db("bank_transactions").where("connection_id", connectionId);

      if (startDate) {
        query = query.where("transaction_date", ">=", startDate);
      }

      if (endDate) {
        query = query.where("transaction_date", "<=", endDate);
      }

      if (status) {
        query = query.where("reconciliation_status", status);
      }

      // Get total count
      const totalResult = await query.clone().count("* as count").first();
      const total = parseInt(totalResult?.count as string) || 0;

      // Get paginated results
      const offset = (parseInt(page as string) - 1) * parseInt(limit as string);
      const transactions = await query
        .orderBy("transaction_date", "desc")
        .limit(parseInt(limit as string))
        .offset(offset);

      res.json({
        success: true,
        data: transactions,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total,
          totalPages: Math.ceil(total / parseInt(limit as string)),
        },
      });
    } catch (error) {
      console.error("Failed to get bank transactions:", error);
      res.status(500).json({ error: "Failed to get bank transactions" });
    }
  })
);

// POST /api/bank-integrations/:companyId/:connectionId/test - Test bank connection
router.post(
  "/:companyId/:connectionId/test",
  requireCompanyAccess,
  requirePermission("bank_integration:test"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, connectionId } = req.params;

    try {
      const connection = await db("bank_connections")
        .where("id", connectionId)
        .where("company_id", companyId)
        .first();

      if (!connection) {
        return res.status(404).json({ error: "Bank connection not found" });
      }

      // Decrypt credentials
      const credentials = connection.credentials
        ? decryptCredentials(connection.credentials)
        : null;

      // Test connection
      const testResult = await testBankConnection({
        bankCode: connection.bank_code,
        accountNumber: connection.account_number,
        credentials,
      });

      // Update connection status
      await db("bank_connections")
        .where("id", connectionId)
        .update({
          connection_status: testResult.success ? "CONNECTED" : "FAILED",
          last_test_at: new Date(),
          updated_at: new Date(),
        });

      res.json({
        success: true,
        data: testResult,
        message: testResult.success
          ? "Connection test successful"
          : "Connection test failed",
      });
    } catch (error) {
      console.error("Failed to test bank connection:", error);
      res.status(500).json({ error: "Failed to test bank connection" });
    }
  })
);

// Helper function to get supported banks by country
function getSupportedBanks(country: string) {
  const banks = {
    TZ: [
      // Tanzania
      {
        code: "NBC",
        name: "National Bank of Commerce (NBC)",
        type: "COMMERCIAL",
        apiSupport: "FULL",
        features: ["ACCOUNT_BALANCE", "TRANSACTION_HISTORY", "REAL_TIME_SYNC"],
        authMethods: ["API_KEY", "OAUTH2"],
      },
      {
        code: "CRDB",
        name: "CRDB Bank",
        type: "COMMERCIAL",
        apiSupport: "FULL",
        features: ["ACCOUNT_BALANCE", "TRANSACTION_HISTORY", "REAL_TIME_SYNC"],
        authMethods: ["API_KEY", "OAUTH2"],
      },
      {
        code: "NMB",
        name: "NMB Bank",
        type: "COMMERCIAL",
        apiSupport: "PARTIAL",
        features: ["ACCOUNT_BALANCE", "TRANSACTION_HISTORY"],
        authMethods: ["API_KEY"],
      },
      {
        code: "EXIM",
        name: "Exim Bank Tanzania",
        type: "COMMERCIAL",
        apiSupport: "PARTIAL",
        features: ["ACCOUNT_BALANCE", "TRANSACTION_HISTORY"],
        authMethods: ["API_KEY"],
      },
      {
        code: "DTB",
        name: "Diamond Trust Bank",
        type: "COMMERCIAL",
        apiSupport: "FULL",
        features: ["ACCOUNT_BALANCE", "TRANSACTION_HISTORY", "REAL_TIME_SYNC"],
        authMethods: ["API_KEY", "OAUTH2"],
      },
      {
        code: "STANBIC",
        name: "Stanbic Bank Tanzania",
        type: "COMMERCIAL",
        apiSupport: "FULL",
        features: [
          "ACCOUNT_BALANCE",
          "TRANSACTION_HISTORY",
          "REAL_TIME_SYNC",
          "PAYMENT_INITIATION",
        ],
        authMethods: ["OAUTH2"],
      },
      {
        code: "BOT",
        name: "Bank of Tanzania",
        type: "CENTRAL",
        apiSupport: "LIMITED",
        features: ["EXCHANGE_RATES", "REGULATORY_DATA"],
        authMethods: ["API_KEY"],
      },
    ],
    KE: [
      // Kenya
      {
        code: "KCB",
        name: "Kenya Commercial Bank",
        type: "COMMERCIAL",
        apiSupport: "FULL",
        features: ["ACCOUNT_BALANCE", "TRANSACTION_HISTORY", "REAL_TIME_SYNC"],
        authMethods: ["API_KEY", "OAUTH2"],
      },
      {
        code: "EQUITY",
        name: "Equity Bank Kenya",
        type: "COMMERCIAL",
        apiSupport: "FULL",
        features: ["ACCOUNT_BALANCE", "TRANSACTION_HISTORY", "REAL_TIME_SYNC"],
        authMethods: ["OAUTH2"],
      },
    ],
    UG: [
      // Uganda
      {
        code: "STANBIC_UG",
        name: "Stanbic Bank Uganda",
        type: "COMMERCIAL",
        apiSupport: "FULL",
        features: ["ACCOUNT_BALANCE", "TRANSACTION_HISTORY", "REAL_TIME_SYNC"],
        authMethods: ["OAUTH2"],
      },
    ],
  };

  return banks[country as keyof typeof banks] || [];
}

// Helper function to test bank connection
async function testBankConnection(params: {
  bankCode: string;
  accountNumber: string;
  credentials?: any;
}) {
  try {
    // Mock bank API integration - in production, integrate with actual bank APIs
    switch (params.bankCode) {
      case "NBC":
        return await testNBCConnection(params);
      case "CRDB":
        return await testCRDBConnection(params);
      case "NMB":
        return await testNMBConnection(params);
      case "STANBIC":
        return await testStanbicConnection(params);
      default:
        return await testGenericBankConnection(params);
    }
  } catch (error) {
    console.error("Bank connection test failed:", error);
    return {
      success: false,
      error: error.message || "Connection test failed",
    };
  }
}

// Mock NBC Bank API integration
async function testNBCConnection(params: any) {
  // Mock implementation - replace with actual NBC API
  if (!params.credentials?.apiKey) {
    return { success: false, error: "API key required for NBC Bank" };
  }

  // Simulate API call
  await new Promise((resolve) => setTimeout(resolve, 1000));

  return {
    success: true,
    bankName: "National Bank of Commerce",
    accountName: "Business Account",
    accountType: "CURRENT",
    currency: "TZS",
    balance: 1500000.0,
    lastUpdated: new Date().toISOString(),
  };
}

// Mock CRDB Bank API integration
async function testCRDBConnection(params: any) {
  // Mock implementation - replace with actual CRDB API
  if (!params.credentials?.clientId || !params.credentials?.clientSecret) {
    return {
      success: false,
      error: "Client credentials required for CRDB Bank",
    };
  }

  // Simulate OAuth2 flow
  await new Promise((resolve) => setTimeout(resolve, 1500));

  return {
    success: true,
    bankName: "CRDB Bank",
    accountName: "Corporate Account",
    accountType: "CURRENT",
    currency: "TZS",
    balance: 2750000.0,
    lastUpdated: new Date().toISOString(),
  };
}

// Mock NMB Bank API integration
async function testNMBConnection(params: any) {
  // Mock implementation - replace with actual NMB API
  if (!params.credentials?.username || !params.credentials?.password) {
    return {
      success: false,
      error: "Username and password required for NMB Bank",
    };
  }

  // Simulate authentication
  await new Promise((resolve) => setTimeout(resolve, 800));

  return {
    success: true,
    bankName: "NMB Bank",
    accountName: "Business Savings",
    accountType: "SAVINGS",
    currency: "TZS",
    balance: 980000.0,
    lastUpdated: new Date().toISOString(),
  };
}

// Mock Stanbic Bank API integration
async function testStanbicConnection(params: any) {
  // Mock implementation - replace with actual Stanbic API
  if (!params.credentials?.clientId) {
    return {
      success: false,
      error: "OAuth2 credentials required for Stanbic Bank",
    };
  }

  // Simulate OAuth2 flow
  await new Promise((resolve) => setTimeout(resolve, 1200));

  return {
    success: true,
    bankName: "Stanbic Bank Tanzania",
    accountName: "Premium Business Account",
    accountType: "CURRENT",
    currency: "TZS",
    balance: 5200000.0,
    lastUpdated: new Date().toISOString(),
    features: ["REAL_TIME_SYNC", "PAYMENT_INITIATION"],
  };
}

// Generic bank connection test
async function testGenericBankConnection(params: any) {
  // Generic mock implementation
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    success: true,
    bankName: "Generic Bank",
    accountName: "Business Account",
    accountType: "CURRENT",
    currency: "TZS",
    balance: 1000000.0,
    lastUpdated: new Date().toISOString(),
  };
}

// Helper function to sync bank transactions
async function syncBankTransactions(params: {
  connection: any;
  startDate: string;
  endDate: string;
  autoReconcile: boolean;
  userId: string;
}) {
  try {
    // Decrypt credentials
    const credentials = params.connection.credentials
      ? decryptCredentials(params.connection.credentials)
      : null;

    // Fetch transactions from bank API
    const bankTransactions = await fetchBankTransactions({
      bankCode: params.connection.bank_code,
      accountNumber: params.connection.account_number,
      startDate: params.startDate,
      endDate: params.endDate,
      credentials,
    });

    let syncedCount = 0;
    let reconciledCount = 0;
    const errors = [];

    for (const transaction of bankTransactions) {
      try {
        // Check if transaction already exists
        const existing = await db("bank_transactions")
          .where("connection_id", params.connection.id)
          .where("bank_transaction_id", transaction.id)
          .first();

        if (!existing) {
          // Insert new transaction
          await db("bank_transactions").insert({
            id: uuidv4(),
            connection_id: params.connection.id,
            bank_transaction_id: transaction.id,
            transaction_date: transaction.date,
            description: transaction.description,
            amount: transaction.amount,
            transaction_type: transaction.type,
            balance: transaction.balance,
            reference: transaction.reference,
            reconciliation_status: "UNMATCHED",
            synced_at: new Date(),
            synced_by: params.userId,
          });

          syncedCount++;

          // Auto-reconcile if enabled
          if (params.autoReconcile) {
            const reconciled = await attemptAutoReconciliation({
              bankTransaction: transaction,
              companyId: params.connection.company_id,
            });

            if (reconciled) {
              reconciledCount++;
            }
          }
        }
      } catch (error) {
        errors.push({
          transaction: transaction.id,
          error: error.message,
        });
      }
    }

    return {
      success: true,
      totalFetched: bankTransactions.length,
      syncedCount,
      reconciledCount,
      errors,
      period: {
        startDate: params.startDate,
        endDate: params.endDate,
      },
    };
  } catch (error) {
    console.error("Transaction sync failed:", error);
    return {
      success: false,
      error: error.message || "Transaction sync failed",
    };
  }
}

// Mock function to fetch bank transactions
async function fetchBankTransactions(params: {
  bankCode: string;
  accountNumber: string;
  startDate: string;
  endDate: string;
  credentials?: any;
}) {
  // Mock bank transactions - replace with actual bank API calls
  const mockTransactions = [
    {
      id: "TXN001",
      date: "2024-01-15",
      description: "Customer Payment - INV001",
      amount: 150000.0,
      type: "CREDIT",
      balance: 1650000.0,
      reference: "REF001",
    },
    {
      id: "TXN002",
      date: "2024-01-16",
      description: "Office Rent Payment",
      amount: -50000.0,
      type: "DEBIT",
      balance: 1600000.0,
      reference: "REF002",
    },
    {
      id: "TXN003",
      date: "2024-01-17",
      description: "Supplier Payment - PO123",
      amount: -75000.0,
      type: "DEBIT",
      balance: 1525000.0,
      reference: "REF003",
    },
  ];

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 2000));

  return mockTransactions;
}

// Helper function for auto-reconciliation
async function attemptAutoReconciliation(params: {
  bankTransaction: any;
  companyId: string;
}) {
  try {
    // Look for matching transactions in the accounting system
    const matchingTransactions = await db("transactions")
      .join(
        "transaction_entries",
        "transactions.id",
        "transaction_entries.transaction_id"
      )
      .where("transactions.company_id", params.companyId)
      .where("transactions.status", "POSTED")
      .whereBetween("transactions.transaction_date", [
        new Date(
          new Date(params.bankTransaction.date).getTime() -
            7 * 24 * 60 * 60 * 1000
        ), // 7 days before
        new Date(
          new Date(params.bankTransaction.date).getTime() +
            7 * 24 * 60 * 60 * 1000
        ), // 7 days after
      ])
      .where(function () {
        this.where(
          "transaction_entries.debit_amount",
          Math.abs(params.bankTransaction.amount)
        ).orWhere(
          "transaction_entries.credit_amount",
          Math.abs(params.bankTransaction.amount)
        );
      });

    if (matchingTransactions.length === 1) {
      // Exact match found - create reconciliation
      const match = matchingTransactions[0];

      await db("bank_reconciliation_items").insert({
        id: uuidv4(),
        bank_transaction_id: params.bankTransaction.id,
        accounting_transaction_id: match.id,
        reconciliation_type: "AUTO_MATCHED",
        matched_amount: Math.abs(params.bankTransaction.amount),
        reconciled_at: new Date(),
        reconciled_by: "SYSTEM",
      });

      return true;
    }

    return false;
  } catch (error) {
    console.error("Auto-reconciliation failed:", error);
    return false;
  }
}

// Encryption/Decryption functions for credentials
function encryptCredentials(credentials: any): string {
  const algorithm = "aes-256-gcm";
  const key = process.env.ENCRYPTION_KEY || "default-key-change-in-production";
  const iv = crypto.randomBytes(16);

  const cipher = crypto.createCipher(algorithm, key);
  let encrypted = cipher.update(JSON.stringify(credentials), "utf8", "hex");
  encrypted += cipher.final("hex");

  return `${iv.toString("hex")}:${encrypted}`;
}

function decryptCredentials(encryptedData: string): any {
  const algorithm = "aes-256-gcm";
  const key = process.env.ENCRYPTION_KEY || "default-key-change-in-production";

  const [ivHex, encrypted] = encryptedData.split(":");
  const iv = Buffer.from(ivHex, "hex");

  const decipher = crypto.createDecipher(algorithm, key);
  let decrypted = decipher.update(encrypted, "hex", "utf8");
  decrypted += decipher.final("utf8");

  return JSON.parse(decrypted);
}

export default router;
