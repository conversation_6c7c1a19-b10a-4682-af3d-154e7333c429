import express from "express";
import type { Request, Response } from "express";
import { z } from "zod";
import speakeasy from "speakeasy";
import QRCode from "qrcode";
import {
  authenticateToken,
  requirePermission,
} from "../middleware/auth";
import { asyncHand<PERSON> } from "../middleware/errorHandler";
import { db } from "../config/database";
import { v4 as uuidv4 } from "uuid";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const enable2FASchema = z.object({
  token: z.string().length(6).regex(/^\d{6}$/, "Token must be 6 digits")
});

const verify2FASchema = z.object({
  token: z.string().length(6).regex(/^\d{6}$/, "Token must be 6 digits")
});

const setupBackupCodesSchema = z.object({
  password: z.string().min(1, "Password is required")
});

// GET /api/auth/2fa/setup - Initialize 2FA setup
router.get(
  "/setup",
  asyncHandler(async (req: Request, res: Response) => {
    const userId = (req as any).user?.id;

    try {
      // Check if 2FA is already enabled
      const existing2FA = await db("user_2fa")
        .where("user_id", userId)
        .where("is_enabled", true)
        .first();

      if (existing2FA) {
        return res.status(400).json({
          error: "2FA is already enabled for this account"
        });
      }

      // Generate secret
      const secret = speakeasy.generateSecret({
        name: `Accounting System (${(req as any).user?.email})`,
        issuer: "Accounting System",
        length: 32
      });

      // Store temporary secret (not enabled yet)
      await db("user_2fa")
        .insert({
          id: uuidv4(),
          user_id: userId,
          secret: secret.base32,
          is_enabled: false,
          setup_at: new Date()
        })
        .onConflict("user_id")
        .merge({
          secret: secret.base32,
          is_enabled: false,
          setup_at: new Date(),
          updated_at: new Date()
        });

      // Generate QR code
      const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url!);

      res.json({
        success: true,
        data: {
          secret: secret.base32,
          qrCode: qrCodeUrl,
          manualEntryKey: secret.base32,
          instructions: [
            "1. Install an authenticator app (Google Authenticator, Authy, etc.)",
            "2. Scan the QR code or manually enter the key",
            "3. Enter the 6-digit code from your app to complete setup"
          ]
        }
      });

    } catch (error) {
      console.error("Failed to setup 2FA:", error);
      res.status(500).json({ error: "Failed to setup 2FA" });
    }
  })
);

// POST /api/auth/2fa/enable - Enable 2FA with verification
router.post(
  "/enable",
  asyncHandler(async (req: Request, res: Response) => {
    const userId = (req as any).user?.id;

    try {
      const validatedData = enable2FASchema.parse(req.body);

      // Get the temporary secret
      const user2FA = await db("user_2fa")
        .where("user_id", userId)
        .where("is_enabled", false)
        .first();

      if (!user2FA) {
        return res.status(400).json({
          error: "2FA setup not found. Please start the setup process again."
        });
      }

      // Verify the token
      const verified = speakeasy.totp.verify({
        secret: user2FA.secret,
        encoding: "base32",
        token: validatedData.token,
        window: 2 // Allow 2 time steps (60 seconds) tolerance
      });

      if (!verified) {
        return res.status(400).json({
          error: "Invalid verification code. Please try again."
        });
      }

      // Generate backup codes
      const backupCodes = generateBackupCodes();
      const hashedBackupCodes = await Promise.all(
        backupCodes.map(async (code) => {
          const bcrypt = require("bcrypt");
          return await bcrypt.hash(code, 10);
        })
      );

      // Enable 2FA and save backup codes
      await db.transaction(async (trx) => {
        // Enable 2FA
        await trx("user_2fa")
          .where("user_id", userId)
          .update({
            is_enabled: true,
            enabled_at: new Date(),
            backup_codes: JSON.stringify(hashedBackupCodes),
            updated_at: new Date()
          });

        // Log security event
        await trx("security_logs").insert({
          id: uuidv4(),
          user_id: userId,
          event_type: "2FA_ENABLED",
          ip_address: req.ip,
          user_agent: req.get("User-Agent"),
          details: JSON.stringify({
            method: "TOTP",
            timestamp: new Date().toISOString()
          })
        });
      });

      res.json({
        success: true,
        message: "2FA has been successfully enabled",
        data: {
          backupCodes: backupCodes,
          warning: "Save these backup codes in a secure location. They can be used to access your account if you lose your authenticator device."
        }
      });

    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: "Validation failed",
          details: error.errors
        });
      }

      console.error("Failed to enable 2FA:", error);
      res.status(500).json({ error: "Failed to enable 2FA" });
    }
  })
);

// POST /api/auth/2fa/verify - Verify 2FA token during login
router.post(
  "/verify",
  asyncHandler(async (req: Request, res: Response) => {
    const userId = (req as any).user?.id;

    try {
      const validatedData = verify2FASchema.parse(req.body);

      // Get user's 2FA settings
      const user2FA = await db("user_2fa")
        .where("user_id", userId)
        .where("is_enabled", true)
        .first();

      if (!user2FA) {
        return res.status(400).json({
          error: "2FA is not enabled for this account"
        });
      }

      let verified = false;
      let usedBackupCode = false;

      // First try TOTP verification
      verified = speakeasy.totp.verify({
        secret: user2FA.secret,
        encoding: "base32",
        token: validatedData.token,
        window: 2
      });

      // If TOTP fails, try backup codes
      if (!verified && user2FA.backup_codes) {
        const backupCodes = JSON.parse(user2FA.backup_codes);
        const bcrypt = require("bcrypt");

        for (let i = 0; i < backupCodes.length; i++) {
          if (await bcrypt.compare(validatedData.token, backupCodes[i])) {
            verified = true;
            usedBackupCode = true;

            // Remove used backup code
            backupCodes.splice(i, 1);
            await db("user_2fa")
              .where("user_id", userId)
              .update({
                backup_codes: JSON.stringify(backupCodes),
                updated_at: new Date()
              });
            break;
          }
        }
      }

      if (!verified) {
        // Log failed attempt
        await db("security_logs").insert({
          id: uuidv4(),
          user_id: userId,
          event_type: "2FA_VERIFICATION_FAILED",
          ip_address: req.ip,
          user_agent: req.get("User-Agent"),
          details: JSON.stringify({
            timestamp: new Date().toISOString()
          })
        });

        return res.status(400).json({
          error: "Invalid verification code"
        });
      }

      // Log successful verification
      await db("security_logs").insert({
        id: uuidv4(),
        user_id: userId,
        event_type: "2FA_VERIFICATION_SUCCESS",
        ip_address: req.ip,
        user_agent: req.get("User-Agent"),
        details: JSON.stringify({
          method: usedBackupCode ? "BACKUP_CODE" : "TOTP",
          timestamp: new Date().toISOString()
        })
      });

      res.json({
        success: true,
        message: "2FA verification successful",
        data: {
          usedBackupCode,
          remainingBackupCodes: usedBackupCode ? 
            JSON.parse(user2FA.backup_codes || "[]").length - 1 : 
            JSON.parse(user2FA.backup_codes || "[]").length
        }
      });

    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: "Validation failed",
          details: error.errors
        });
      }

      console.error("Failed to verify 2FA:", error);
      res.status(500).json({ error: "Failed to verify 2FA" });
    }
  })
);

// POST /api/auth/2fa/disable - Disable 2FA
router.post(
  "/disable",
  asyncHandler(async (req: Request, res: Response) => {
    const userId = (req as any).user?.id;
    const { password, token } = req.body;

    try {
      if (!password || !token) {
        return res.status(400).json({
          error: "Password and 2FA token are required to disable 2FA"
        });
      }

      // Verify password
      const user = await db("users").where("id", userId).first();
      const bcrypt = require("bcrypt");
      const passwordValid = await bcrypt.compare(password, user.password);

      if (!passwordValid) {
        return res.status(400).json({
          error: "Invalid password"
        });
      }

      // Verify 2FA token
      const user2FA = await db("user_2fa")
        .where("user_id", userId)
        .where("is_enabled", true)
        .first();

      if (!user2FA) {
        return res.status(400).json({
          error: "2FA is not enabled for this account"
        });
      }

      const verified = speakeasy.totp.verify({
        secret: user2FA.secret,
        encoding: "base32",
        token: token,
        window: 2
      });

      if (!verified) {
        return res.status(400).json({
          error: "Invalid 2FA token"
        });
      }

      // Disable 2FA
      await db.transaction(async (trx) => {
        await trx("user_2fa")
          .where("user_id", userId)
          .update({
            is_enabled: false,
            disabled_at: new Date(),
            updated_at: new Date()
          });

        // Log security event
        await trx("security_logs").insert({
          id: uuidv4(),
          user_id: userId,
          event_type: "2FA_DISABLED",
          ip_address: req.ip,
          user_agent: req.get("User-Agent"),
          details: JSON.stringify({
            timestamp: new Date().toISOString()
          })
        });
      });

      res.json({
        success: true,
        message: "2FA has been disabled for your account"
      });

    } catch (error) {
      console.error("Failed to disable 2FA:", error);
      res.status(500).json({ error: "Failed to disable 2FA" });
    }
  })
);

// Helper function to generate backup codes
function generateBackupCodes(): string[] {
  const codes: string[] = [];
  for (let i = 0; i < 10; i++) {
    // Generate 8-digit backup codes
    const code = Math.random().toString().slice(2, 10);
    codes.push(code);
  }
  return codes;
}

export default router;
