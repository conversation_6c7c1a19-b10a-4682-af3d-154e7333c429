import express from "express";
import type { Request, Response } from "express";
import {
  authenticateToken,
  requireCompanyAccess,
  // requirePermission,
} from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { db } from "../config/database";
import bcrypt from "bcryptjs";
import { z } from "zod";
import { auditService } from "../services/auditService";

// Extend Request interface to include user
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    companyId: string;
  };
}

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const updateUserProfileSchema = z.object({
  firstName: z.string().min(1).max(100).optional(),
  lastName: z.string().min(1).max(100).optional(),
  phone: z.string().max(20).optional(),
  email: z.string().email().optional(),
});

const updatePasswordSchema = z
  .object({
    currentPassword: z.string().min(1),
    newPassword: z.string().min(8).max(100),
    confirmPassword: z.string().min(8).max(100),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

const updateCompanySettingsSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  legalName: z.string().max(255).optional(),
  taxId: z.string().max(50).optional(),
  registrationNumber: z.string().max(50).optional(),
  address: z.string().optional(),
  city: z.string().max(100).optional(),
  state: z.string().max(100).optional(),
  postalCode: z.string().max(20).optional(),
  country: z.string().max(100).optional(),
  phone: z.string().max(20).optional(),
  email: z.string().email().optional(),
  website: z.string().url().optional(),
  baseCurrency: z.string().length(3).optional(),
  settings: z
    .object({
      fiscalYearStart: z.string().optional(),
      timeZone: z.string().optional(),
      dateFormat: z.string().optional(),
      numberFormat: z.string().optional(),
      defaultPaymentTerms: z.string().optional(),
      autoNumbering: z
        .object({
          invoices: z.boolean().optional(),
          transactions: z.boolean().optional(),
          contacts: z.boolean().optional(),
        })
        .optional(),
      notifications: z
        .object({
          emailNotifications: z.boolean().optional(),
          overdueReminders: z.boolean().optional(),
          paymentConfirmations: z.boolean().optional(),
          reportSchedules: z.boolean().optional(),
        })
        .optional(),
      security: z
        .object({
          sessionTimeout: z.number().optional(),
          passwordExpiry: z.number().optional(),
          twoFactorAuth: z.boolean().optional(),
          ipWhitelist: z.array(z.string()).optional(),
        })
        .optional(),
      backup: z
        .object({
          autoBackup: z.boolean().optional(),
          backupFrequency: z.string().optional(),
          retentionDays: z.number().optional(),
        })
        .optional(),
      integrations: z
        .object({
          bankingApi: z
            .object({
              enabled: z.boolean().optional(),
              provider: z.string().optional(),
              apiKey: z.string().optional(),
            })
            .optional(),
          paymentGateway: z
            .object({
              enabled: z.boolean().optional(),
              provider: z.string().optional(),
              apiKey: z.string().optional(),
              webhookUrl: z.string().optional(),
            })
            .optional(),
          exchangeRates: z
            .object({
              enabled: z.boolean().optional(),
              provider: z.string().optional(),
              apiKey: z.string().optional(),
              updateFrequency: z.string().optional(),
            })
            .optional(),
        })
        .optional(),
    })
    .optional(),
});

const updateUserPreferencesSchema = z.object({
  preferences: z.object({
    theme: z.enum(["light", "dark", "system"]).optional(),
    language: z.string().optional(),
    timeZone: z.string().optional(),
    dateFormat: z.string().optional(),
    numberFormat: z.string().optional(),
    dashboardLayout: z.array(z.string()).optional(),
    notifications: z
      .object({
        email: z.boolean().optional(),
        browser: z.boolean().optional(),
        mobile: z.boolean().optional(),
        frequency: z.enum(["immediate", "daily", "weekly"]).optional(),
      })
      .optional(),
    reports: z
      .object({
        defaultPeriod: z.string().optional(),
        autoRefresh: z.boolean().optional(),
        exportFormat: z.enum(["pdf", "csv", "excel"]).optional(),
      })
      .optional(),
  }),
});

// GET /api/settings/:companyId/user-profile
router.get(
  "/:companyId/user-profile",
  requireCompanyAccess,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id;

    try {
      const user = await db("users")
        .select(
          "id",
          "email",
          "first_name as firstName",
          "last_name as lastName",
          "phone",
          "is_active as isActive",
          "email_verified as emailVerified",
          "last_login_at as lastLoginAt",
          "created_at as createdAt",
          "updated_at as updatedAt"
        )
        .where("id", userId)
        .first();

      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      // Get user preferences (stored in a separate table or as JSONB)
      const preferences = await db("user_companies")
        .select("preferences")
        .where("user_id", userId)
        .where("company_id", req.params.companyId)
        .first();

      res.json({
        user: {
          ...user,
          preferences: preferences?.preferences || {},
        },
      });
    } catch (error) {
      console.error("Failed to get user profile:", error);
      res.status(500).json({ error: "Failed to get user profile" });
    }
  })
);

// PUT /api/settings/:companyId/user-profile
router.put(
  "/:companyId/user-profile",
  requireCompanyAccess,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id;
    const validation = updateUserProfileSchema.safeParse(req.body);

    if (!validation.success) {
      return res.status(400).json({
        error: "Invalid input",
        details: validation.error.errors,
      });
    }

    const data = validation.data;

    try {
      // Check if email is being changed and if it's already taken
      if (data.email) {
        const existingUser = await db("users")
          .where("email", data.email)
          .where("id", "!=", userId)
          .first();

        if (existingUser) {
          return res.status(400).json({ error: "Email already in use" });
        }
      }

      // Update user profile
      const [updatedUser] = await db("users")
        .where("id", userId)
        .update({
          first_name: data.firstName,
          last_name: data.lastName,
          phone: data.phone,
          email: data.email,
          updated_at: new Date(),
        })
        .returning([
          "id",
          "email",
          "first_name as firstName",
          "last_name as lastName",
          "phone",
          "is_active as isActive",
          "email_verified as emailVerified",
          "last_login_at as lastLoginAt",
          "created_at as createdAt",
          "updated_at as updatedAt",
        ]);

      res.json({ user: updatedUser });
    } catch (error) {
      console.error("Failed to update user profile:", error);
      res.status(500).json({ error: "Failed to update user profile" });
    }
  })
);

// PUT /api/settings/:companyId/change-password
router.put(
  "/:companyId/change-password",
  requireCompanyAccess,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id;
    const validation = updatePasswordSchema.safeParse(req.body);

    if (!validation.success) {
      return res.status(400).json({
        error: "Invalid input",
        details: validation.error.errors,
      });
    }

    const { currentPassword, newPassword } = validation.data;

    try {
      // Get current user
      const user = await db("users")
        .select("password_hash")
        .where("id", userId)
        .first();

      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      // Verify current password
      const isValidPassword = await bcrypt.compare(
        currentPassword,
        user.password_hash
      );
      if (!isValidPassword) {
        return res.status(400).json({ error: "Current password is incorrect" });
      }

      // Hash new password
      const saltRounds = 12;
      const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

      // Update password
      await db("users").where("id", userId).update({
        password_hash: newPasswordHash,
        updated_at: new Date(),
      });

      res.json({ message: "Password updated successfully" });
    } catch (error) {
      console.error("Failed to change password:", error);
      res.status(500).json({ error: "Failed to change password" });
    }
  })
);

// GET /api/settings/:companyId/company
router.get(
  "/:companyId/company",
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;

    try {
      const company = await db("companies")
        .select(
          "id",
          "name",
          "legal_name as legalName",
          "tax_id as taxId",
          "registration_number as registrationNumber",
          "address",
          "city",
          "state",
          "postal_code as postalCode",
          "country",
          "phone",
          "email",
          "website",
          "base_currency as baseCurrency",
          "settings",
          "is_active as isActive",
          "created_at as createdAt",
          "updated_at as updatedAt"
        )
        .where("id", companyId)
        .first();

      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      res.json({ company });
    } catch (error) {
      console.error("Failed to get company settings:", error);
      res.status(500).json({ error: "Failed to get company settings" });
    }
  })
);

// PUT /api/settings/:companyId/company
router.put(
  "/:companyId/company",
  requireCompanyAccess,
  // requirePermission("company:update"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const validation = updateCompanySettingsSchema.safeParse(req.body);

    if (!validation.success) {
      return res.status(400).json({
        error: "Invalid input",
        details: validation.error.errors,
      });
    }

    const data = validation.data;

    try {
      // Update company settings
      const [updatedCompany] = await db("companies")
        .where("id", companyId)
        .update({
          name: data.name,
          legal_name: data.legalName,
          tax_id: data.taxId,
          registration_number: data.registrationNumber,
          address: data.address,
          city: data.city,
          state: data.state,
          postal_code: data.postalCode,
          country: data.country,
          phone: data.phone,
          email: data.email,
          website: data.website,
          base_currency: data.baseCurrency,
          settings: data.settings ? JSON.stringify(data.settings) : undefined,
          updated_at: new Date(),
        })
        .returning([
          "id",
          "name",
          "legal_name as legalName",
          "tax_id as taxId",
          "registration_number as registrationNumber",
          "address",
          "city",
          "state",
          "postal_code as postalCode",
          "country",
          "phone",
          "email",
          "website",
          "base_currency as baseCurrency",
          "settings",
          "is_active as isActive",
          "created_at as createdAt",
          "updated_at as updatedAt",
        ]);

      res.json({ company: updatedCompany });
    } catch (error) {
      console.error("Failed to update company settings:", error);
      res.status(500).json({ error: "Failed to update company settings" });
    }
  })
);

// PUT /api/settings/:companyId/user-preferences
router.put(
  "/:companyId/user-preferences",
  requireCompanyAccess,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { companyId } = req.params;
    const userId = req.user!.id;
    const validation = updateUserPreferencesSchema.safeParse(req.body);

    if (!validation.success) {
      return res.status(400).json({
        error: "Invalid input",
        details: validation.error.errors,
      });
    }

    const { preferences } = validation.data;

    try {
      // Update user preferences in user_companies table
      await db("user_companies")
        .where("user_id", userId)
        .where("company_id", companyId)
        .update({
          preferences: JSON.stringify(preferences),
          updated_at: new Date(),
        });

      res.json({ preferences });
    } catch (error) {
      console.error("Failed to update user preferences:", error);
      res.status(500).json({ error: "Failed to update user preferences" });
    }
  })
);

// GET /api/settings/:companyId/audit-logs
router.get(
  "/:companyId/audit-logs",
  requireCompanyAccess,
  // requirePermission("audit:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const {
      page = 1,
      limit = 50,
      action,
      userId,
      startDate,
      endDate,
    } = req.query;

    try {
      let query = db("audit_trail")
        .select(
          "id",
          "user_email as userEmail",
          "action",
          "table_name as tableName",
          "record_id as recordId",
          "description",
          "ip_address as ipAddress",
          "created_at as createdAt"
        )
        .where("company_id", companyId)
        .orderBy("created_at", "desc");

      // Apply filters
      if (action) {
        query = query.where("action", action);
      }

      if (userId) {
        query = query.where("user_id", userId);
      }

      if (startDate) {
        query = query.where("created_at", ">=", startDate);
      }

      if (endDate) {
        query = query.where("created_at", "<=", endDate);
      }

      // Get total count
      const totalQuery = query
        .clone()
        .clearSelect()
        .clearOrder()
        .count("* as count");
      const [{ count: total }] = await totalQuery;

      // Apply pagination
      const offset = (Number(page) - 1) * Number(limit);
      const auditLogs = await query.limit(Number(limit)).offset(offset);

      res.json({
        auditLogs,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: Number(total),
          pages: Math.ceil(Number(total) / Number(limit)),
        },
      });
    } catch (error) {
      console.error("Failed to get audit logs:", error);
      res.status(500).json({ error: "Failed to get audit logs" });
    }
  })
);

// GET /api/settings/:companyId/system-info
router.get(
  "/:companyId/system-info",
  requireCompanyAccess,
  // requirePermission("system:read"),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get system information
      const systemInfo = {
        version: process.env.npm_package_version || "1.0.0",
        environment: process.env.NODE_ENV || "development",
        database: {
          type: "PostgreSQL",
          version: "15+",
          status: "Connected",
        },
        server: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          platform: process.platform,
          nodeVersion: process.version,
        },
        features: {
          multiCurrency: true,
          auditTrail: true,
          reporting: true,
          integrations: true,
          backup: true,
          twoFactorAuth: false, // TODO: Implement
          sso: false, // TODO: Implement
        },
      };

      res.json({ systemInfo });
    } catch (error) {
      console.error("Failed to get system info:", error);
      res.status(500).json({ error: "Failed to get system info" });
    }
  })
);

// GET /api/settings/:companyId/data-retention
router.get(
  "/:companyId/data-retention",
  requireCompanyAccess,
  // requirePermission("system:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;

    try {
      const policies = await db("data_retention_policies")
        .select(
          "id",
          "table_name as tableName",
          "retention_days as retentionDays",
          "is_active as isActive",
          "description",
          "created_at as createdAt",
          "updated_at as updatedAt"
        )
        .where("company_id", companyId)
        .orderBy("table_name");

      res.json({ policies });
    } catch (error) {
      console.error("Failed to get data retention policies:", error);
      res.status(500).json({ error: "Failed to get data retention policies" });
    }
  })
);

// POST /api/settings/:companyId/audit-logs/sample-data (Development only)
router.post(
  "/:companyId/audit-logs/sample-data",
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;

    // Only allow in development environment
    if (process.env.NODE_ENV === "production") {
      return res.status(403).json({ error: "Not available in production" });
    }

    try {
      await auditService.createSampleData(companyId);
      res.json({ message: "Sample audit data created successfully" });
    } catch (error) {
      console.error("Failed to create sample audit data:", error);
      res.status(500).json({ error: "Failed to create sample audit data" });
    }
  })
);

export default router;
