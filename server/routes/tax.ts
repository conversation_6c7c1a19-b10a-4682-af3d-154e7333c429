import express from "express";
import type { Request, Response } from "express";
import { z } from "zod";
import db from "../config/database";
import { asyncHandler } from "../middleware/asyncHandler";
import { authenticateToken } from "../middleware/auth";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const createTaxRateSchema = z.object({
  companyId: z.string().uuid(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  rate: z.number().min(0).max(100),
  type: z.enum([
    "sales_tax",
    "vat",
    "gst",
    "income_tax",
    "property_tax",
    "excise_tax",
    "customs_duty",
    "other",
  ]),
  jurisdiction: z.string().min(1).max(255),
  jurisdictionCode: z.string().max(10).optional(),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().default(false),
  effectiveDate: z.string(),
  expiryDate: z.string().optional(),
  applicableToProducts: z.boolean().default(true),
  applicableToServices: z.boolean().default(true),
});

const updateTaxRateSchema = createTaxRateSchema.partial().extend({
  id: z.string().uuid(),
});

const createTaxCategorySchema = z.object({
  companyId: z.string().uuid(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  defaultTaxRateId: z.string().uuid().optional(),
  isActive: z.boolean().default(true),
});

const updateTaxCategorySchema = createTaxCategorySchema.partial().extend({
  id: z.string().uuid(),
});

// GET /api/tax/rates - Get tax rates
router.get(
  "/rates",
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.query;

    if (!companyId) {
      return res.status(400).json({ error: "Company ID is required" });
    }

    try {
      const taxRates = await db("tax_rates")
        .select(
          "id",
          "company_id as companyId",
          "name",
          "description",
          "rate",
          "type",
          "jurisdiction",
          "jurisdiction_code as jurisdictionCode",
          "is_active as isActive",
          "is_default as isDefault",
          "effective_date as effectiveDate",
          "expiry_date as expiryDate",
          "applicable_to_products as applicableToProducts",
          "applicable_to_services as applicableToServices",
          "created_at as createdAt",
          "updated_at as updatedAt"
        )
        .where("company_id", companyId)
        .orderBy("name");

      res.json(taxRates);
    } catch (error) {
      console.error("Failed to get tax rates:", error);
      res.status(500).json({ error: "Failed to get tax rates" });
    }
  })
);

// POST /api/tax/rates - Create tax rate
router.post(
  "/rates",
  asyncHandler(async (req: Request, res: Response) => {
    const validatedData = createTaxRateSchema.parse(req.body);

    try {
      // If this is set as default, unset other defaults
      if (validatedData.isDefault) {
        await db("tax_rates")
          .where("company_id", validatedData.companyId)
          .update({ is_default: false });
      }

      const [taxRate] = await db("tax_rates")
        .insert({
          company_id: validatedData.companyId,
          name: validatedData.name,
          description: validatedData.description,
          rate: validatedData.rate,
          type: validatedData.type,
          jurisdiction: validatedData.jurisdiction,
          jurisdiction_code: validatedData.jurisdictionCode,
          is_active: validatedData.isActive,
          is_default: validatedData.isDefault,
          effective_date: validatedData.effectiveDate,
          expiry_date: validatedData.expiryDate,
          applicable_to_products: validatedData.applicableToProducts,
          applicable_to_services: validatedData.applicableToServices,
        })
        .returning([
          "id",
          "company_id as companyId",
          "name",
          "description",
          "rate",
          "type",
          "jurisdiction",
          "jurisdiction_code as jurisdictionCode",
          "is_active as isActive",
          "is_default as isDefault",
          "effective_date as effectiveDate",
          "expiry_date as expiryDate",
          "applicable_to_products as applicableToProducts",
          "applicable_to_services as applicableToServices",
          "created_at as createdAt",
          "updated_at as updatedAt",
        ]);

      res.status(201).json(taxRate);
    } catch (error) {
      console.error("Failed to create tax rate:", error);
      res.status(500).json({ error: "Failed to create tax rate" });
    }
  })
);

// PUT /api/tax/rates/:id - Update tax rate
router.put(
  "/rates/:id",
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const validatedData = updateTaxRateSchema.parse({ ...req.body, id });

    try {
      // If this is set as default, unset other defaults
      if (validatedData.isDefault) {
        await db("tax_rates")
          .where("company_id", validatedData.companyId)
          .whereNot("id", id)
          .update({ is_default: false });
      }

      const [taxRate] = await db("tax_rates")
        .where("id", id)
        .update({
          name: validatedData.name,
          description: validatedData.description,
          rate: validatedData.rate,
          type: validatedData.type,
          jurisdiction: validatedData.jurisdiction,
          jurisdiction_code: validatedData.jurisdictionCode,
          is_active: validatedData.isActive,
          is_default: validatedData.isDefault,
          effective_date: validatedData.effectiveDate,
          expiry_date: validatedData.expiryDate,
          applicable_to_products: validatedData.applicableToProducts,
          applicable_to_services: validatedData.applicableToServices,
          updated_at: db.fn.now(),
        })
        .returning([
          "id",
          "company_id as companyId",
          "name",
          "description",
          "rate",
          "type",
          "jurisdiction",
          "jurisdiction_code as jurisdictionCode",
          "is_active as isActive",
          "is_default as isDefault",
          "effective_date as effectiveDate",
          "expiry_date as expiryDate",
          "applicable_to_products as applicableToProducts",
          "applicable_to_services as applicableToServices",
          "created_at as createdAt",
          "updated_at as updatedAt",
        ]);

      if (!taxRate) {
        return res.status(404).json({ error: "Tax rate not found" });
      }

      res.json(taxRate);
    } catch (error) {
      console.error("Failed to update tax rate:", error);
      res.status(500).json({ error: "Failed to update tax rate" });
    }
  })
);

// DELETE /api/tax/rates/:id - Delete tax rate
router.delete(
  "/rates/:id",
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    try {
      const deletedCount = await db("tax_rates").where("id", id).del();

      if (deletedCount === 0) {
        return res.status(404).json({ error: "Tax rate not found" });
      }

      res.status(204).send();
    } catch (error) {
      console.error("Failed to delete tax rate:", error);
      res.status(500).json({ error: "Failed to delete tax rate" });
    }
  })
);

// GET /api/tax/categories - Get tax categories
router.get(
  "/categories",
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.query;

    if (!companyId) {
      return res.status(400).json({ error: "Company ID is required" });
    }

    try {
      const categories = await db("tax_categories")
        .select(
          "id",
          "company_id as companyId",
          "name",
          "description",
          "default_tax_rate_id as defaultTaxRateId",
          "is_active as isActive",
          "created_at as createdAt",
          "updated_at as updatedAt"
        )
        .where("company_id", companyId)
        .orderBy("name");

      res.json(categories);
    } catch (error) {
      console.error("Failed to get tax categories:", error);
      res.status(500).json({ error: "Failed to get tax categories" });
    }
  })
);

// POST /api/tax/categories - Create tax category
router.post(
  "/categories",
  asyncHandler(async (req: Request, res: Response) => {
    const validatedData = createTaxCategorySchema.parse(req.body);

    try {
      const [category] = await db("tax_categories")
        .insert({
          company_id: validatedData.companyId,
          name: validatedData.name,
          description: validatedData.description,
          default_tax_rate_id: validatedData.defaultTaxRateId,
          is_active: validatedData.isActive,
        })
        .returning([
          "id",
          "company_id as companyId",
          "name",
          "description",
          "default_tax_rate_id as defaultTaxRateId",
          "is_active as isActive",
          "created_at as createdAt",
          "updated_at as updatedAt",
        ]);

      res.status(201).json(category);
    } catch (error) {
      console.error("Failed to create tax category:", error);
      res.status(500).json({ error: "Failed to create tax category" });
    }
  })
);

// PUT /api/tax/categories/:id - Update tax category
router.put(
  "/categories/:id",
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const validatedData = updateTaxCategorySchema.parse({ ...req.body, id });

    try {
      const [category] = await db("tax_categories")
        .where("id", id)
        .update({
          name: validatedData.name,
          description: validatedData.description,
          default_tax_rate_id: validatedData.defaultTaxRateId,
          is_active: validatedData.isActive,
          updated_at: db.fn.now(),
        })
        .returning([
          "id",
          "company_id as companyId",
          "name",
          "description",
          "default_tax_rate_id as defaultTaxRateId",
          "is_active as isActive",
          "created_at as createdAt",
          "updated_at as updatedAt",
        ]);

      if (!category) {
        return res.status(404).json({ error: "Tax category not found" });
      }

      res.json(category);
    } catch (error) {
      console.error("Failed to update tax category:", error);
      res.status(500).json({ error: "Failed to update tax category" });
    }
  })
);

// DELETE /api/tax/categories/:id - Delete tax category
router.delete(
  "/categories/:id",
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    try {
      const deletedCount = await db("tax_categories").where("id", id).del();

      if (deletedCount === 0) {
        return res.status(404).json({ error: "Tax category not found" });
      }

      res.status(204).send();
    } catch (error) {
      console.error("Failed to delete tax category:", error);
      res.status(500).json({ error: "Failed to delete tax category" });
    }
  })
);

// GET /api/tax/settings - Get tax settings
router.get(
  "/settings",
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.query;

    if (!companyId) {
      return res.status(400).json({ error: "Company ID is required" });
    }

    try {
      const settings = await db("tax_settings")
        .select(
          "id",
          "company_id as companyId",
          "default_tax_rate_id as defaultTaxRateId",
          "tax_calculation_method as taxCalculationMethod",
          "rounding_method as roundingMethod",
          "rounding_precision as roundingPrecision",
          "include_tax_in_price as includeTaxInPrice",
          "enable_tax_exemptions as enableTaxExemptions",
          "require_tax_certificates as requireTaxCertificates",
          "auto_calculate_tax as autoCalculateTax",
          "tax_reporting_currency as taxReportingCurrency",
          "settings",
          "created_at as createdAt",
          "updated_at as updatedAt"
        )
        .where("company_id", companyId)
        .first();

      if (!settings) {
        // Create default settings if none exist
        const [newSettings] = await db("tax_settings")
          .insert({
            company_id: companyId,
            tax_calculation_method: "exclusive",
            rounding_method: "round",
            rounding_precision: 2,
            include_tax_in_price: false,
            enable_tax_exemptions: true,
            require_tax_certificates: false,
            auto_calculate_tax: true,
            tax_reporting_currency: "USD",
            settings: {},
          })
          .returning([
            "id",
            "company_id as companyId",
            "default_tax_rate_id as defaultTaxRateId",
            "tax_calculation_method as taxCalculationMethod",
            "rounding_method as roundingMethod",
            "rounding_precision as roundingPrecision",
            "include_tax_in_price as includeTaxInPrice",
            "enable_tax_exemptions as enableTaxExemptions",
            "require_tax_certificates as requireTaxCertificates",
            "auto_calculate_tax as autoCalculateTax",
            "tax_reporting_currency as taxReportingCurrency",
            "settings",
            "created_at as createdAt",
            "updated_at as updatedAt",
          ]);

        return res.json(newSettings);
      }

      res.json(settings);
    } catch (error) {
      console.error("Failed to get tax settings:", error);
      res.status(500).json({ error: "Failed to get tax settings" });
    }
  })
);

// POST /api/tax/calculate - Calculate tax for an amount
router.post(
  "/calculate",
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, amount, taxRateId, isInclusive = false } = req.body;

    if (!companyId || !amount || !taxRateId) {
      return res.status(400).json({
        error: "Company ID, amount, and tax rate ID are required",
      });
    }

    try {
      const taxRate = await db("tax_rates")
        .select("rate", "name", "type")
        .where("id", taxRateId)
        .where("company_id", companyId)
        .where("is_active", true)
        .first();

      if (!taxRate) {
        return res.status(404).json({ error: "Tax rate not found" });
      }

      const rate = taxRate.rate / 100; // Convert percentage to decimal
      let taxableAmount: number;
      let taxAmount: number;
      let totalAmount: number;

      if (isInclusive) {
        // Tax is included in the amount
        totalAmount = amount;
        taxableAmount = amount / (1 + rate);
        taxAmount = amount - taxableAmount;
      } else {
        // Tax is added to the amount
        taxableAmount = amount;
        taxAmount = amount * rate;
        totalAmount = amount + taxAmount;
      }

      // Round to 2 decimal places
      taxableAmount = Math.round(taxableAmount * 100) / 100;
      taxAmount = Math.round(taxAmount * 100) / 100;
      totalAmount = Math.round(totalAmount * 100) / 100;

      const calculation = {
        taxableAmount,
        taxAmount,
        totalAmount,
        taxRate: taxRate.rate,
        taxRateName: taxRate.name,
        taxRateType: taxRate.type,
        calculationMethod: isInclusive ? "inclusive" : "exclusive",
      };

      res.json(calculation);
    } catch (error) {
      console.error("Failed to calculate tax:", error);
      res.status(500).json({ error: "Failed to calculate tax" });
    }
  })
);

// GET /api/tax/reports - Get tax reports
router.get(
  "/reports",
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, reportType, periodType, startDate, endDate } = req.query;

    if (!companyId) {
      return res.status(400).json({ error: "Company ID is required" });
    }

    try {
      let query = db("tax_reports")
        .select(
          "id",
          "company_id as companyId",
          "report_type as reportType",
          "period_type as periodType",
          "period_start as periodStart",
          "period_end as periodEnd",
          "status",
          "total_sales as totalSales",
          "taxable_sales as taxableSales",
          "tax_collected as taxCollected",
          "tax_paid as taxPaid",
          "tax_due as taxDue",
          "due_date as dueDate",
          "filed_date as filedDate",
          "paid_date as paidDate",
          "generated_by as generatedBy",
          "created_at as createdAt",
          "updated_at as updatedAt"
        )
        .where("company_id", companyId)
        .orderBy("created_at", "desc");

      // Apply filters
      if (reportType) {
        query = query.where("report_type", reportType);
      }
      if (periodType) {
        query = query.where("period_type", periodType);
      }
      if (startDate) {
        query = query.where("period_start", ">=", startDate);
      }
      if (endDate) {
        query = query.where("period_end", "<=", endDate);
      }

      const reports = await query;
      console.log(`🔍 Tax reports for company ${companyId}:`, reports);
      res.json(reports);
    } catch (error) {
      console.error("Failed to get tax reports:", error);
      res.status(500).json({ error: "Failed to get tax reports" });
    }
  })
);

// POST /api/tax/reports/generate - Generate a new tax report
router.post(
  "/reports/generate",
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, reportType, periodType, startDate, endDate } = req.body;

    if (!companyId || !reportType || !periodType || !startDate || !endDate) {
      return res.status(400).json({
        error:
          "Company ID, report type, period type, start date, and end date are required",
      });
    }

    try {
      // Calculate tax data for the period (simplified calculation)
      // In a real implementation, this would aggregate data from invoices and transactions
      const totalSales = 10000; // Mock data
      const taxableSales = 8500; // Mock data
      const taxCollected = 680; // Mock data (8% average tax rate)
      const taxPaid = 0;
      const taxDue = taxCollected - taxPaid;

      // Calculate due date (typically 30 days after period end)
      const dueDate = new Date(endDate);
      dueDate.setDate(dueDate.getDate() + 30);

      const [report] = await db("tax_reports")
        .insert({
          company_id: companyId,
          report_type: reportType,
          period_type: periodType,
          period_start: startDate,
          period_end: endDate,
          status: "GENERATED",
          total_sales: totalSales,
          taxable_sales: taxableSales,
          tax_collected: taxCollected,
          tax_paid: taxPaid,
          tax_due: taxDue,
          due_date: dueDate.toISOString().split("T")[0],
          report_data: {
            generatedAt: new Date().toISOString(),
            calculationMethod: "simplified",
          },
          generated_by: (req as any).user?.id,
        })
        .returning([
          "id",
          "company_id as companyId",
          "report_type as reportType",
          "period_type as periodType",
          "period_start as periodStart",
          "period_end as periodEnd",
          "status",
          "total_sales as totalSales",
          "taxable_sales as taxableSales",
          "tax_collected as taxCollected",
          "tax_paid as taxPaid",
          "tax_due as taxDue",
          "due_date as dueDate",
          "filed_date as filedDate",
          "paid_date as paidDate",
          "generated_by as generatedBy",
          "created_at as createdAt",
          "updated_at as updatedAt",
        ]);

      console.log(`🔍 Generated tax report:`, report);
      res.status(201).json(report);
    } catch (error) {
      console.error("Failed to generate tax report:", error);
      res.status(500).json({ error: "Failed to generate tax report" });
    }
  })
);

// GET /api/tax/reports/:id/download - Download a tax report
router.get(
  "/reports/:id/download",
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    try {
      const report = await db("tax_reports")
        .select("*")
        .where("id", id)
        .first();

      if (!report) {
        return res.status(404).json({ error: "Tax report not found" });
      }

      // In a real implementation, this would generate a PDF or Excel file
      // For now, we'll return a JSON response
      const reportData = {
        reportId: report.id,
        reportType: report.report_type,
        periodStart: report.period_start,
        periodEnd: report.period_end,
        totalSales: report.total_sales,
        taxableSales: report.taxable_sales,
        taxCollected: report.tax_collected,
        taxDue: report.tax_due,
        generatedAt: report.created_at,
      };

      res.setHeader("Content-Type", "application/json");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="tax-report-${report.id}.json"`
      );
      res.json(reportData);
    } catch (error) {
      console.error("Failed to download tax report:", error);
      res.status(500).json({ error: "Failed to download tax report" });
    }
  })
);

// POST /api/tax/calculate-multiple - Calculate tax for multiple line items
router.post(
  "/calculate-multiple",
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, lineItems, taxSettings } = req.body;

    if (!companyId || !lineItems || !Array.isArray(lineItems)) {
      return res.status(400).json({
        error: "Company ID and line items array are required",
      });
    }

    try {
      const calculations = await calculateMultipleLineTax({
        companyId,
        lineItems,
        taxSettings,
      });

      res.json({
        success: true,
        calculations,
        summary: {
          totalTaxableAmount: calculations.reduce(
            (sum, calc) => sum + calc.taxableAmount,
            0
          ),
          totalTaxAmount: calculations.reduce(
            (sum, calc) => sum + calc.taxAmount,
            0
          ),
          totalAmount: calculations.reduce(
            (sum, calc) => sum + calc.totalAmount,
            0
          ),
        },
      });
    } catch (error) {
      console.error("Failed to calculate multiple line tax:", error);
      res.status(500).json({ error: "Failed to calculate tax" });
    }
  })
);

// POST /api/tax/validate-exemption - Validate tax exemption
router.post(
  "/validate-exemption",
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, exemptionId, customerId, amount, taxRateId } = req.body;

    if (!companyId || !exemptionId) {
      return res.status(400).json({
        error: "Company ID and exemption ID are required",
      });
    }

    try {
      const validation = await validateTaxExemption({
        companyId,
        exemptionId,
        customerId,
        amount,
        taxRateId,
      });

      res.json({
        success: true,
        ...validation,
      });
    } catch (error) {
      console.error("Failed to validate tax exemption:", error);
      res.status(500).json({ error: "Failed to validate exemption" });
    }
  })
);

// GET /api/tax/jurisdiction/:code - Get tax rates by jurisdiction
router.get(
  "/jurisdiction/:code",
  asyncHandler(async (req: Request, res: Response) => {
    const { code } = req.params;
    const { companyId } = req.query;

    if (!companyId) {
      return res.status(400).json({ error: "Company ID is required" });
    }

    try {
      const taxRates = await getTaxRatesByJurisdiction(
        companyId as string,
        code
      );

      res.json({
        success: true,
        jurisdiction: code,
        taxRates,
      });
    } catch (error) {
      console.error("Failed to get jurisdiction tax rates:", error);
      res.status(500).json({ error: "Failed to get tax rates" });
    }
  })
);

// Helper function to calculate tax for multiple line items
async function calculateMultipleLineTax(data: {
  companyId: string;
  lineItems: any[];
  taxSettings?: any;
}) {
  const calculations = [];

  for (const item of data.lineItems) {
    try {
      // Get tax rate for this item
      const taxRate = await getTaxRateForItem(data.companyId, item);

      if (!taxRate) {
        calculations.push({
          lineItemId: item.id,
          taxableAmount: item.amount,
          taxAmount: 0,
          totalAmount: item.amount,
          taxRate: 0,
          taxRateName: "No Tax",
          isExempt: true,
        });
        continue;
      }

      // Calculate tax for this line item
      const calculation = calculateLineTax({
        amount: item.amount,
        taxRate: taxRate.rate,
        isInclusive: item.taxInclusive || false,
        taxSettings: data.taxSettings,
      });

      calculations.push({
        lineItemId: item.id,
        taxRateId: taxRate.id,
        taxRateName: taxRate.name,
        taxRate: taxRate.rate,
        ...calculation,
        isExempt: false,
      });
    } catch (error) {
      console.error(`Error calculating tax for line item ${item.id}:`, error);
      calculations.push({
        lineItemId: item.id,
        error: "Tax calculation failed",
        taxableAmount: item.amount,
        taxAmount: 0,
        totalAmount: item.amount,
      });
    }
  }

  return calculations;
}

// Helper function to get tax rate for a specific item
async function getTaxRateForItem(companyId: string, item: any) {
  try {
    // If item has specific tax rate ID, use that
    if (item.taxRateId) {
      return await db("tax_rates")
        .where("id", item.taxRateId)
        .where("company_id", companyId)
        .where("is_active", true)
        .first();
    }

    // If item has tax category, get default tax rate for that category
    if (item.taxCategoryId) {
      const category = await db("tax_categories")
        .where("id", item.taxCategoryId)
        .where("company_id", companyId)
        .first();

      if (category && category.default_tax_rate_id) {
        return await db("tax_rates")
          .where("id", category.default_tax_rate_id)
          .where("is_active", true)
          .first();
      }
    }

    // Get default tax rate for the item type (product vs service)
    const itemType = item.type || "product";
    const applicableField =
      itemType === "service"
        ? "applicable_to_services"
        : "applicable_to_products";

    return await db("tax_rates")
      .where("company_id", companyId)
      .where("is_active", true)
      .where("is_default", true)
      .where(applicableField, true)
      .first();
  } catch (error) {
    console.error("Error getting tax rate for item:", error);
    return null;
  }
}

// Helper function to calculate tax for a single line item
function calculateLineTax(data: {
  amount: number;
  taxRate: number;
  isInclusive: boolean;
  taxSettings?: any;
}) {
  const rate = data.taxRate / 100; // Convert percentage to decimal
  let taxableAmount: number;
  let taxAmount: number;
  let totalAmount: number;

  if (data.isInclusive) {
    // Tax is included in the amount
    totalAmount = data.amount;
    taxableAmount = data.amount / (1 + rate);
    taxAmount = data.amount - taxableAmount;
  } else {
    // Tax is added to the amount
    taxableAmount = data.amount;
    taxAmount = data.amount * rate;
    totalAmount = data.amount + taxAmount;
  }

  // Apply rounding based on tax settings
  if (data.taxSettings?.roundingMethod) {
    taxAmount = applyTaxRounding(taxAmount, data.taxSettings);
    totalAmount = taxableAmount + taxAmount;
  }

  return {
    taxableAmount: Math.round(taxableAmount * 100) / 100,
    taxAmount: Math.round(taxAmount * 100) / 100,
    totalAmount: Math.round(totalAmount * 100) / 100,
  };
}

// Helper function to apply tax rounding
function applyTaxRounding(amount: number, taxSettings: any): number {
  const precision = taxSettings.roundingPrecision || 2;
  const multiplier = Math.pow(10, precision);

  switch (taxSettings.roundingMethod) {
    case "round_up":
      return Math.ceil(amount * multiplier) / multiplier;
    case "round_down":
      return Math.floor(amount * multiplier) / multiplier;
    case "truncate":
      return Math.trunc(amount * multiplier) / multiplier;
    case "round":
    default:
      return Math.round(amount * multiplier) / multiplier;
  }
}

// Helper function to validate tax exemption
async function validateTaxExemption(data: {
  companyId: string;
  exemptionId: string;
  customerId?: string;
  amount?: number;
  taxRateId?: string;
}) {
  try {
    // Get exemption details
    const exemption = await db("tax_exemptions")
      .where("id", data.exemptionId)
      .where("company_id", data.companyId)
      .where("is_active", true)
      .first();

    if (!exemption) {
      return {
        isValid: false,
        reason: "Exemption not found or inactive",
      };
    }

    // Check if exemption has expired
    if (exemption.expiry_date && new Date(exemption.expiry_date) < new Date()) {
      return {
        isValid: false,
        reason: "Exemption has expired",
      };
    }

    // Check if exemption applies to this customer
    if (exemption.customer_id && exemption.customer_id !== data.customerId) {
      return {
        isValid: false,
        reason: "Exemption does not apply to this customer",
      };
    }

    // Check if exemption applies to this tax rate
    if (exemption.tax_rate_id && exemption.tax_rate_id !== data.taxRateId) {
      return {
        isValid: false,
        reason: "Exemption does not apply to this tax rate",
      };
    }

    // Check amount limits
    if (data.amount) {
      if (exemption.min_amount && data.amount < exemption.min_amount) {
        return {
          isValid: false,
          reason: `Amount below minimum threshold of ${exemption.min_amount}`,
        };
      }

      if (exemption.max_amount && data.amount > exemption.max_amount) {
        return {
          isValid: false,
          reason: `Amount exceeds maximum threshold of ${exemption.max_amount}`,
        };
      }
    }

    return {
      isValid: true,
      exemption: {
        id: exemption.id,
        name: exemption.name,
        description: exemption.description,
        exemptionType: exemption.exemption_type,
        certificateNumber: exemption.certificate_number,
      },
    };
  } catch (error) {
    console.error("Error validating tax exemption:", error);
    return {
      isValid: false,
      reason: "Error validating exemption",
    };
  }
}

// Helper function to get tax rates by jurisdiction
async function getTaxRatesByJurisdiction(
  companyId: string,
  jurisdictionCode: string
) {
  try {
    return await db("tax_rates")
      .select(
        "id",
        "name",
        "description",
        "rate",
        "type",
        "jurisdiction",
        "jurisdiction_code as jurisdictionCode",
        "is_active as isActive",
        "is_default as isDefault",
        "effective_date as effectiveDate",
        "expiry_date as expiryDate",
        "applicable_to_products as applicableToProducts",
        "applicable_to_services as applicableToServices"
      )
      .where("company_id", companyId)
      .where("jurisdiction_code", jurisdictionCode)
      .where("is_active", true)
      .where("effective_date", "<=", new Date())
      .where(function () {
        this.whereNull("expiry_date").orWhere("expiry_date", ">", new Date());
      })
      .orderBy("name");
  } catch (error) {
    console.error("Error getting tax rates by jurisdiction:", error);
    throw error;
  }
}

export default router;
