import express from "express";
import type { Request, Response } from "express";
import { z } from "zod";
import {
  authenticateToken,
  requireCompanyAccess,
  requirePermission,
} from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { db } from "../config/database";
import { v4 as uuidv4 } from "uuid";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const createReportSchema = z.object({
  companyId: z.string().uuid(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  reportType: z.enum(["TABLE", "CHART", "PIVOT", "DASHBOARD"]),
  dataSource: z.object({
    tables: z.array(z.string()),
    joins: z
      .array(
        z.object({
          table: z.string(),
          joinType: z.enum(["INNER", "LEFT", "RIGHT", "FULL"]),
          condition: z.string(),
        })
      )
      .optional(),
    filters: z
      .array(
        z.object({
          field: z.string(),
          operator: z.enum([
            "=",
            "!=",
            ">",
            "<",
            ">=",
            "<=",
            "LIKE",
            "IN",
            "BETWEEN",
          ]),
          value: z.any(),
          logicalOperator: z.enum(["AND", "OR"]).optional(),
        })
      )
      .optional(),
  }),
  columns: z.array(
    z.object({
      field: z.string(),
      alias: z.string().optional(),
      aggregation: z
        .enum(["SUM", "COUNT", "AVG", "MIN", "MAX", "NONE"])
        .default("NONE"),
      format: z
        .enum(["CURRENCY", "NUMBER", "PERCENTAGE", "DATE", "TEXT"])
        .default("TEXT"),
      sortOrder: z.number().optional(),
      sortDirection: z.enum(["ASC", "DESC"]).optional(),
    })
  ),
  groupBy: z.array(z.string()).optional(),
  orderBy: z
    .array(
      z.object({
        field: z.string(),
        direction: z.enum(["ASC", "DESC"]),
      })
    )
    .optional(),
  chartConfig: z
    .object({
      chartType: z.enum(["BAR", "LINE", "PIE", "AREA", "SCATTER"]).optional(),
      xAxis: z.string().optional(),
      yAxis: z.string().optional(),
      series: z.array(z.string()).optional(),
    })
    .optional(),
  isPublic: z.boolean().default(false),
  tags: z.array(z.string()).optional(),
});

const executeReportSchema = z.object({
  parameters: z.record(z.any()).optional(),
  dateRange: z
    .object({
      startDate: z.string().optional(),
      endDate: z.string().optional(),
    })
    .optional(),
  limit: z.number().int().positive().max(10000).optional(),
  offset: z.number().int().min(0).optional(),
});

// GET /api/custom-reports/:companyId - Get all custom reports for a company
router.get(
  "/:companyId",
  requireCompanyAccess,
  requirePermission("reports:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { reportType, tags, isPublic, page = 1, limit = 20 } = req.query;

    try {
      let query = db("custom_reports")
        .select(
          "custom_reports.*",
          "users.name as created_by_name",
          db.raw("COUNT(report_executions.id) as execution_count"),
          db.raw("MAX(report_executions.executed_at) as last_executed")
        )
        .leftJoin("users", "custom_reports.created_by", "users.id")
        .leftJoin(
          "report_executions",
          "custom_reports.id",
          "report_executions.report_id"
        )
        .where("custom_reports.company_id", companyId)
        .groupBy("custom_reports.id", "users.name");

      // Apply filters
      if (reportType) {
        query = query.where("custom_reports.report_type", reportType);
      }
      if (isPublic !== undefined) {
        query = query.where("custom_reports.is_public", isPublic === "true");
      }
      if (tags) {
        const tagArray = (tags as string).split(",");
        query = query.where(function () {
          tagArray.forEach((tag) => {
            this.orWhereRaw("custom_reports.tags @> ?", [
              JSON.stringify([tag]),
            ]);
          });
        });
      }

      // Pagination
      const offset = (parseInt(page as string) - 1) * parseInt(limit as string);
      const reports = await query
        .orderBy("custom_reports.updated_at", "desc")
        .limit(parseInt(limit as string))
        .offset(offset);

      // Get total count
      const totalResult = await db("custom_reports")
        .where("company_id", companyId)
        .count("* as count")
        .first();
      const total = parseInt(totalResult?.count as string) || 0;

      res.json({
        success: true,
        data: reports,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total,
          totalPages: Math.ceil(total / parseInt(limit as string)),
        },
      });
    } catch (error) {
      console.error("Failed to get custom reports:", error);
      res.status(500).json({ error: "Failed to get custom reports" });
    }
  })
);

// GET /api/custom-reports/:companyId/:reportId - Get a specific custom report
router.get(
  "/:companyId/:reportId",
  requireCompanyAccess,
  requirePermission("reports:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, reportId } = req.params;

    try {
      const report = await db("custom_reports")
        .select("custom_reports.*", "users.name as created_by_name")
        .leftJoin("users", "custom_reports.created_by", "users.id")
        .where("custom_reports.id", reportId)
        .where("custom_reports.company_id", companyId)
        .first();

      if (!report) {
        return res.status(404).json({ error: "Custom report not found" });
      }

      res.json({
        success: true,
        data: report,
      });
    } catch (error) {
      console.error("Failed to get custom report:", error);
      res.status(500).json({ error: "Failed to get custom report" });
    }
  })
);

// POST /api/custom-reports - Create a new custom report
router.post(
  "/",
  requirePermission("reports:create"),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const validatedData = createReportSchema.parse(req.body);

      const reportId = uuidv4();

      const [report] = await db("custom_reports")
        .insert({
          id: reportId,
          company_id: validatedData.companyId,
          name: validatedData.name,
          description: validatedData.description,
          report_type: validatedData.reportType,
          data_source: JSON.stringify(validatedData.dataSource),
          columns: JSON.stringify(validatedData.columns),
          group_by: validatedData.groupBy
            ? JSON.stringify(validatedData.groupBy)
            : null,
          order_by: validatedData.orderBy
            ? JSON.stringify(validatedData.orderBy)
            : null,
          chart_config: validatedData.chartConfig
            ? JSON.stringify(validatedData.chartConfig)
            : null,
          is_public: validatedData.isPublic,
          tags: validatedData.tags ? JSON.stringify(validatedData.tags) : null,
          created_by: (req as any).user?.id,
        })
        .returning("*");

      res.status(201).json({
        success: true,
        data: report,
        message: "Custom report created successfully",
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: "Validation failed",
          details: error.errors,
        });
      }

      console.error("Failed to create custom report:", error);
      res.status(500).json({ error: "Failed to create custom report" });
    }
  })
);

// POST /api/custom-reports/:companyId/:reportId/execute - Execute a custom report
router.post(
  "/:companyId/:reportId/execute",
  requireCompanyAccess,
  requirePermission("reports:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, reportId } = req.params;

    try {
      const validatedData = executeReportSchema.parse(req.body);

      // Get report definition
      const report = await db("custom_reports")
        .where("id", reportId)
        .where("company_id", companyId)
        .first();

      if (!report) {
        return res.status(404).json({ error: "Custom report not found" });
      }

      // Execute the report
      const result = await executeCustomReport({
        report,
        parameters: validatedData.parameters,
        dateRange: validatedData.dateRange,
        limit: validatedData.limit,
        offset: validatedData.offset,
        userId: (req as any).user?.id,
      });

      // Log execution
      await db("report_executions").insert({
        id: uuidv4(),
        report_id: reportId,
        executed_by: (req as any).user?.id,
        execution_time_ms: result.executionTime,
        row_count: result.data.length,
        parameters: validatedData.parameters
          ? JSON.stringify(validatedData.parameters)
          : null,
        executed_at: new Date(),
      });

      res.json({
        success: true,
        data: result.data,
        metadata: {
          reportName: report.name,
          reportType: report.report_type,
          executionTime: result.executionTime,
          rowCount: result.data.length,
          columns: result.columns,
        },
      });
    } catch (error) {
      console.error("Failed to execute custom report:", error);
      res.status(500).json({ error: "Failed to execute custom report" });
    }
  })
);

// Helper function to execute custom report
async function executeCustomReport(params: {
  report: any;
  parameters?: any;
  dateRange?: { startDate?: string; endDate?: string };
  limit?: number;
  offset?: number;
  userId: string;
}) {
  const startTime = Date.now();

  try {
    const dataSource = JSON.parse(params.report.data_source);
    const columns = JSON.parse(params.report.columns);
    const groupBy = params.report.group_by
      ? JSON.parse(params.report.group_by)
      : null;
    const orderBy = params.report.order_by
      ? JSON.parse(params.report.order_by)
      : null;

    // Build the SQL query
    const query = buildReportQuery({
      dataSource,
      columns,
      groupBy,
      orderBy,
      parameters: params.parameters,
      dateRange: params.dateRange,
      limit: params.limit,
      offset: params.offset,
    });

    // Execute the query
    const data = await db.raw(query.sql, query.bindings);
    const rows = data.rows || data;

    // Format the data based on column definitions
    const formattedData = formatReportData(rows, columns);

    const executionTime = Date.now() - startTime;

    return {
      data: formattedData,
      columns: columns.map((col: any) => ({
        field: col.alias || col.field,
        format: col.format,
        aggregation: col.aggregation,
      })),
      executionTime,
    };
  } catch (error) {
    console.error("Error executing custom report:", error);
    throw error;
  }
}

// Helper function to build SQL query from report definition
function buildReportQuery(params: {
  dataSource: any;
  columns: any[];
  groupBy?: string[];
  orderBy?: any[];
  parameters?: any;
  dateRange?: { startDate?: string; endDate?: string };
  limit?: number;
  offset?: number;
}) {
  let sql = "SELECT ";
  const bindings: any[] = [];

  // Build SELECT clause
  const selectFields = params.columns.map((col) => {
    if (col.aggregation && col.aggregation !== "NONE") {
      return `${col.aggregation}(${col.field}) as "${col.alias || col.field}"`;
    } else {
      return `${col.field} as "${col.alias || col.field}"`;
    }
  });
  sql += selectFields.join(", ");

  // Build FROM clause
  sql += ` FROM ${params.dataSource.tables[0]}`;

  // Build JOIN clauses
  if (params.dataSource.joins) {
    params.dataSource.joins.forEach((join: any) => {
      sql += ` ${join.joinType} JOIN ${join.table} ON ${join.condition}`;
    });
  }

  // Build WHERE clause
  const whereConditions: string[] = [];

  // Add data source filters
  if (params.dataSource.filters) {
    params.dataSource.filters.forEach((filter: any) => {
      const condition = buildFilterCondition(filter, bindings);
      if (condition) {
        whereConditions.push(condition);
      }
    });
  }

  // Add date range filter if provided
  if (params.dateRange) {
    if (params.dateRange.startDate) {
      whereConditions.push("transaction_date >= ?");
      bindings.push(params.dateRange.startDate);
    }
    if (params.dateRange.endDate) {
      whereConditions.push("transaction_date <= ?");
      bindings.push(params.dateRange.endDate);
    }
  }

  // Add parameter filters
  if (params.parameters) {
    Object.entries(params.parameters).forEach(([key, value]) => {
      whereConditions.push(`${key} = ?`);
      bindings.push(value);
    });
  }

  if (whereConditions.length > 0) {
    sql += " WHERE " + whereConditions.join(" AND ");
  }

  // Build GROUP BY clause
  if (params.groupBy && params.groupBy.length > 0) {
    sql += " GROUP BY " + params.groupBy.join(", ");
  }

  // Build ORDER BY clause
  if (params.orderBy && params.orderBy.length > 0) {
    const orderFields = params.orderBy.map(
      (order: any) => `${order.field} ${order.direction}`
    );
    sql += " ORDER BY " + orderFields.join(", ");
  }

  // Add LIMIT and OFFSET
  if (params.limit) {
    sql += " LIMIT ?";
    bindings.push(params.limit);
  }
  if (params.offset) {
    sql += " OFFSET ?";
    bindings.push(params.offset);
  }

  return { sql, bindings };
}

// Helper function to build filter condition
function buildFilterCondition(filter: any, bindings: any[]): string {
  const { field, operator, value } = filter;

  switch (operator) {
    case "=":
    case "!=":
    case ">":
    case "<":
    case ">=":
    case "<=":
      bindings.push(value);
      return `${field} ${operator} ?`;

    case "LIKE":
      bindings.push(`%${value}%`);
      return `${field} LIKE ?`;

    case "IN":
      if (Array.isArray(value)) {
        const placeholders = value.map(() => "?").join(", ");
        bindings.push(...value);
        return `${field} IN (${placeholders})`;
      }
      return "";

    case "BETWEEN":
      if (Array.isArray(value) && value.length === 2) {
        bindings.push(value[0], value[1]);
        return `${field} BETWEEN ? AND ?`;
      }
      return "";

    default:
      return "";
  }
}

// Helper function to format report data
function formatReportData(data: any[], columns: any[]): any[] {
  return data.map((row) => {
    const formattedRow: any = {};

    columns.forEach((col) => {
      const fieldName = col.alias || col.field;
      const value = row[fieldName];

      if (value !== null && value !== undefined) {
        switch (col.format) {
          case "CURRENCY":
            formattedRow[fieldName] = {
              raw: value,
              formatted: formatCurrency(parseFloat(value) || 0),
            };
            break;

          case "NUMBER":
            formattedRow[fieldName] = {
              raw: value,
              formatted: formatNumber(parseFloat(value) || 0),
            };
            break;

          case "PERCENTAGE":
            formattedRow[fieldName] = {
              raw: value,
              formatted: formatPercentage(parseFloat(value) || 0),
            };
            break;

          case "DATE":
            formattedRow[fieldName] = {
              raw: value,
              formatted: formatDate(value),
            };
            break;

          default:
            formattedRow[fieldName] = {
              raw: value,
              formatted: value,
            };
        }
      } else {
        formattedRow[fieldName] = {
          raw: null,
          formatted: "",
        };
      }
    });

    return formattedRow;
  });
}

// Formatting helper functions
function formatCurrency(amount: number, currency: string = "USD"): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency,
  }).format(amount);
}

function formatNumber(value: number): string {
  return new Intl.NumberFormat("en-US").format(value);
}

function formatPercentage(value: number): string {
  return new Intl.NumberFormat("en-US", {
    style: "percent",
    minimumFractionDigits: 2,
  }).format(value / 100);
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

export default router;
