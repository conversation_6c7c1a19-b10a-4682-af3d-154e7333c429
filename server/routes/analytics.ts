import express from "express";
import type { Request, Response } from "express";
import { z } from "zod";
import {
  authenticateToken,
  requireCompanyAccess,
  requirePermission,
} from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { db } from "../config/database";
import { v4 as uuidv4 } from "uuid";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const dashboardSchema = z.object({
  companyId: z.string().uuid(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  layout: z.object({
    widgets: z.array(
      z.object({
        id: z.string(),
        type: z.enum(["CHART", "KPI", "TABLE", "METRIC"]),
        position: z.object({
          x: z.number(),
          y: z.number(),
          width: z.number(),
          height: z.number(),
        }),
        config: z.any(),
      })
    ),
  }),
  isDefault: z.boolean().default(false),
  isPublic: z.boolean().default(false),
});

// GET /api/analytics/:companyId/overview - Company analytics overview
router.get(
  "/:companyId/overview",
  requireCompanyAccess,
  requirePermission("analytics:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { period = "30d", compareWith = "previous_period" } = req.query;

    try {
      const overview = await generateAnalyticsOverview({
        companyId,
        period: period as string,
        compareWith: compareWith as string,
      });

      res.json({
        success: true,
        data: overview,
      });
    } catch (error) {
      console.error("Failed to get analytics overview:", error);
      res.status(500).json({ error: "Failed to get analytics overview" });
    }
  })
);

// GET /api/analytics/:companyId/financial-metrics - Financial performance metrics
router.get(
  "/:companyId/financial-metrics",
  requireCompanyAccess,
  requirePermission("analytics:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { startDate, endDate, granularity = "monthly" } = req.query;

    try {
      const metrics = await calculateFinancialMetrics({
        companyId,
        startDate: startDate as string,
        endDate: endDate as string,
        granularity: granularity as string,
      });

      res.json({
        success: true,
        data: metrics,
      });
    } catch (error) {
      console.error("Failed to get financial metrics:", error);
      res.status(500).json({ error: "Failed to get financial metrics" });
    }
  })
);

// GET /api/analytics/:companyId/trends - Business trend analysis
router.get(
  "/:companyId/trends",
  requireCompanyAccess,
  requirePermission("analytics:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { metric = "revenue", period = "12m", forecast = false } = req.query;

    try {
      const trends = await analyzeTrends({
        companyId,
        metric: metric as string,
        period: period as string,
        includeForecast: forecast === "true",
      });

      res.json({
        success: true,
        data: trends,
      });
    } catch (error) {
      console.error("Failed to get trend analysis:", error);
      res.status(500).json({ error: "Failed to get trend analysis" });
    }
  })
);

// GET /api/analytics/:companyId/kpis - Key Performance Indicators
router.get(
  "/:companyId/kpis",
  requireCompanyAccess,
  requirePermission("analytics:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { category = "all" } = req.query;

    try {
      const kpis = await calculateKPIs({
        companyId,
        category: category as string,
      });

      res.json({
        success: true,
        data: kpis,
      });
    } catch (error) {
      console.error("Failed to get KPIs:", error);
      res.status(500).json({ error: "Failed to get KPIs" });
    }
  })
);

// GET /api/analytics/:companyId/dashboards - Get all dashboards
router.get(
  "/:companyId/dashboards",
  requireCompanyAccess,
  requirePermission("analytics:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;

    try {
      const dashboards = await db("analytics_dashboards")
        .select("analytics_dashboards.*", "users.name as created_by_name")
        .leftJoin("users", "analytics_dashboards.created_by", "users.id")
        .where("analytics_dashboards.company_id", companyId)
        .orderBy("analytics_dashboards.is_default", "desc")
        .orderBy("analytics_dashboards.updated_at", "desc");

      res.json({
        success: true,
        data: dashboards,
      });
    } catch (error) {
      console.error("Failed to get dashboards:", error);
      res.status(500).json({ error: "Failed to get dashboards" });
    }
  })
);

// POST /api/analytics/dashboards - Create a new dashboard
router.post(
  "/dashboards",
  requirePermission("analytics:create"),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const validatedData = dashboardSchema.parse(req.body);

      // If this is set as default, unset any existing default dashboard
      if (validatedData.isDefault) {
        await db("analytics_dashboards")
          .where("company_id", validatedData.companyId)
          .update({ is_default: false });
      }

      const dashboardId = uuidv4();

      const [dashboard] = await db("analytics_dashboards")
        .insert({
          id: dashboardId,
          company_id: validatedData.companyId,
          name: validatedData.name,
          description: validatedData.description,
          layout: JSON.stringify(validatedData.layout),
          is_default: validatedData.isDefault,
          is_public: validatedData.isPublic,
          created_by: (req as any).user?.id,
        })
        .returning("*");

      res.status(201).json({
        success: true,
        data: dashboard,
        message: "Dashboard created successfully",
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: "Validation failed",
          details: error.errors,
        });
      }

      console.error("Failed to create dashboard:", error);
      res.status(500).json({ error: "Failed to create dashboard" });
    }
  })
);

// GET /api/analytics/:companyId/predictive - Predictive analytics
router.get(
  "/:companyId/predictive",
  requireCompanyAccess,
  requirePermission("analytics:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { metric = "revenue", horizon = "3m", confidence = 0.8 } = req.query;

    try {
      const predictions = await generatePredictiveAnalytics({
        companyId,
        metric: metric as string,
        horizon: horizon as string,
        confidence: parseFloat(confidence as string),
      });

      res.json({
        success: true,
        data: predictions,
      });
    } catch (error) {
      console.error("Failed to get predictive analytics:", error);
      res.status(500).json({ error: "Failed to get predictive analytics" });
    }
  })
);

// GET /api/analytics/:companyId/benchmarks - Industry benchmarks
router.get(
  "/:companyId/benchmarks",
  requireCompanyAccess,
  requirePermission("analytics:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { industry, size } = req.query;

    try {
      const benchmarks = await generateBenchmarkAnalysis({
        companyId,
        industry: industry as string,
        companySize: size as string,
      });

      res.json({
        success: true,
        data: benchmarks,
      });
    } catch (error) {
      console.error("Failed to get benchmark analysis:", error);
      res.status(500).json({ error: "Failed to get benchmark analysis" });
    }
  })
);

// Helper function to generate analytics overview
async function generateAnalyticsOverview(params: {
  companyId: string;
  period: string;
  compareWith: string;
}) {
  try {
    const { startDate, endDate } = getPeriodDates(params.period);
    const { compareStartDate, compareEndDate } = getComparisonDates(
      params.compareWith,
      startDate,
      endDate
    );

    // Current period metrics
    const currentMetrics = await calculatePeriodMetrics(
      params.companyId,
      startDate,
      endDate
    );

    // Comparison period metrics
    const comparisonMetrics = await calculatePeriodMetrics(
      params.companyId,
      compareStartDate,
      compareEndDate
    );

    // Calculate growth rates
    const growth = {
      revenue: calculateGrowthRate(
        currentMetrics.revenue,
        comparisonMetrics.revenue
      ),
      expenses: calculateGrowthRate(
        currentMetrics.expenses,
        comparisonMetrics.expenses
      ),
      profit: calculateGrowthRate(
        currentMetrics.profit,
        comparisonMetrics.profit
      ),
      cashFlow: calculateGrowthRate(
        currentMetrics.cashFlow,
        comparisonMetrics.cashFlow
      ),
    };

    // Get top performing accounts
    const topAccounts = await getTopPerformingAccounts(
      params.companyId,
      startDate,
      endDate
    );

    // Get recent transactions summary
    const transactionsSummary = await getTransactionsSummary(
      params.companyId,
      startDate,
      endDate
    );

    return {
      period: {
        startDate,
        endDate,
        label: params.period,
      },
      currentMetrics,
      comparisonMetrics,
      growth,
      topAccounts,
      transactionsSummary,
      generatedAt: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error generating analytics overview:", error);
    throw error;
  }
}

// Helper function to calculate financial metrics
async function calculateFinancialMetrics(params: {
  companyId: string;
  startDate: string;
  endDate: string;
  granularity: string;
}) {
  try {
    const dateFormat = getDateFormat(params.granularity);

    // Revenue trend
    const revenueTrend = await db.raw(
      `
      SELECT
        DATE_TRUNC(?, t.transaction_date) as period,
        SUM(te.credit_amount) as revenue
      FROM transactions t
      JOIN transaction_entries te ON t.id = te.transaction_id
      JOIN accounts a ON te.account_id = a.id
      WHERE t.company_id = ?
        AND t.transaction_date BETWEEN ? AND ?
        AND t.status = 'POSTED'
        AND a.type = 'REVENUE'
      GROUP BY DATE_TRUNC(?, t.transaction_date)
      ORDER BY period
    `,
      [
        params.granularity,
        params.companyId,
        params.startDate,
        params.endDate,
        params.granularity,
      ]
    );

    // Expense trend
    const expenseTrend = await db.raw(
      `
      SELECT
        DATE_TRUNC(?, t.transaction_date) as period,
        SUM(te.debit_amount) as expenses
      FROM transactions t
      JOIN transaction_entries te ON t.id = te.transaction_id
      JOIN accounts a ON te.account_id = a.id
      WHERE t.company_id = ?
        AND t.transaction_date BETWEEN ? AND ?
        AND t.status = 'POSTED'
        AND a.type = 'EXPENSE'
      GROUP BY DATE_TRUNC(?, t.transaction_date)
      ORDER BY period
    `,
      [
        params.granularity,
        params.companyId,
        params.startDate,
        params.endDate,
        params.granularity,
      ]
    );

    // Profit margin calculation
    const profitMargins = calculateProfitMargins(
      revenueTrend.rows,
      expenseTrend.rows
    );

    // Cash flow analysis
    const cashFlow = await calculateCashFlowMetrics(
      params.companyId,
      params.startDate,
      params.endDate,
      params.granularity
    );

    // Financial ratios
    const ratios = await calculateFinancialRatios(
      params.companyId,
      params.endDate
    );

    return {
      revenueTrend: revenueTrend.rows || revenueTrend,
      expenseTrend: expenseTrend.rows || expenseTrend,
      profitMargins,
      cashFlow,
      ratios,
      period: {
        startDate: params.startDate,
        endDate: params.endDate,
        granularity: params.granularity,
      },
    };
  } catch (error) {
    console.error("Error calculating financial metrics:", error);
    throw error;
  }
}

// Helper function to analyze trends
async function analyzeTrends(params: {
  companyId: string;
  metric: string;
  period: string;
  includeForecast: boolean;
}) {
  try {
    const { startDate, endDate } = getPeriodDates(params.period);

    // Get historical data
    const historicalData = await getHistoricalData(
      params.companyId,
      params.metric,
      startDate,
      endDate
    );

    // Calculate trend statistics
    const trendStats = calculateTrendStatistics(historicalData);

    // Generate forecast if requested
    let forecast = null;
    if (params.includeForecast) {
      forecast = generateSimpleForecast(historicalData, 3); // 3 periods ahead
    }

    return {
      metric: params.metric,
      period: params.period,
      historicalData,
      trendStats,
      forecast,
      insights: generateTrendInsights(trendStats),
    };
  } catch (error) {
    console.error("Error analyzing trends:", error);
    throw error;
  }
}

// Helper function to calculate KPIs
async function calculateKPIs(params: { companyId: string; category: string }) {
  try {
    const kpis: any = {};

    // Financial KPIs
    if (params.category === "all" || params.category === "financial") {
      kpis.financial = await calculateFinancialKPIs(params.companyId);
    }

    // Operational KPIs
    if (params.category === "all" || params.category === "operational") {
      kpis.operational = await calculateOperationalKPIs(params.companyId);
    }

    // Customer KPIs
    if (params.category === "all" || params.category === "customer") {
      kpis.customer = await calculateCustomerKPIs(params.companyId);
    }

    return kpis;
  } catch (error) {
    console.error("Error calculating KPIs:", error);
    throw error;
  }
}

// Helper function to generate predictive analytics
async function generatePredictiveAnalytics(params: {
  companyId: string;
  metric: string;
  horizon: string;
  confidence: number;
}) {
  try {
    // Get historical data for the metric
    const historicalData = await getHistoricalData(
      params.companyId,
      params.metric,
      new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0], // 1 year ago
      new Date().toISOString().split("T")[0]
    );

    // Simple linear regression for prediction
    const prediction = performLinearRegression(historicalData, params.horizon);

    // Calculate confidence intervals
    const confidenceInterval = calculateConfidenceInterval(
      prediction,
      params.confidence
    );

    return {
      metric: params.metric,
      horizon: params.horizon,
      prediction,
      confidenceInterval,
      confidence: params.confidence,
      methodology: "Linear Regression",
      dataPoints: historicalData.length,
      generatedAt: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error generating predictive analytics:", error);
    throw error;
  }
}

// Helper function to generate benchmark analysis
async function generateBenchmarkAnalysis(params: {
  companyId: string;
  industry?: string;
  companySize?: string;
}) {
  try {
    // Get company metrics
    const companyMetrics = await calculatePeriodMetrics(
      params.companyId,
      new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0],
      new Date().toISOString().split("T")[0]
    );

    // Mock industry benchmarks (in production, integrate with industry data)
    const industryBenchmarks = getMockIndustryBenchmarks(
      params.industry,
      params.companySize
    );

    // Calculate performance vs benchmarks
    const performance = {
      profitMargin: {
        company: (companyMetrics.profit / companyMetrics.revenue) * 100,
        industry: industryBenchmarks.profitMargin,
        percentile: calculatePercentile(
          (companyMetrics.profit / companyMetrics.revenue) * 100,
          industryBenchmarks.profitMargin
        ),
      },
      revenueGrowth: {
        company: companyMetrics.revenueGrowth || 0,
        industry: industryBenchmarks.revenueGrowth,
        percentile: calculatePercentile(
          companyMetrics.revenueGrowth || 0,
          industryBenchmarks.revenueGrowth
        ),
      },
    };

    return {
      industry: params.industry || "General",
      companySize: params.companySize || "Unknown",
      companyMetrics,
      industryBenchmarks,
      performance,
      recommendations: generateBenchmarkRecommendations(performance),
    };
  } catch (error) {
    console.error("Error generating benchmark analysis:", error);
    throw error;
  }
}

// Utility functions for analytics calculations

function getPeriodDates(period: string): {
  startDate: string;
  endDate: string;
} {
  const endDate = new Date();
  let startDate = new Date();

  switch (period) {
    case "7d":
      startDate.setDate(endDate.getDate() - 7);
      break;
    case "30d":
      startDate.setDate(endDate.getDate() - 30);
      break;
    case "90d":
      startDate.setDate(endDate.getDate() - 90);
      break;
    case "12m":
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
    default:
      startDate.setDate(endDate.getDate() - 30);
  }

  return {
    startDate: startDate.toISOString().split("T")[0],
    endDate: endDate.toISOString().split("T")[0],
  };
}

function getComparisonDates(
  compareWith: string,
  startDate: string,
  endDate: string
): { compareStartDate: string; compareEndDate: string } {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const periodLength = end.getTime() - start.getTime();

  let compareEndDate = new Date(start.getTime() - 1);
  let compareStartDate = new Date(compareEndDate.getTime() - periodLength);

  return {
    compareStartDate: compareStartDate.toISOString().split("T")[0],
    compareEndDate: compareEndDate.toISOString().split("T")[0],
  };
}

async function calculatePeriodMetrics(
  companyId: string,
  startDate: string,
  endDate: string
) {
  // Revenue calculation
  const revenueResult = await db.raw(
    `
    SELECT COALESCE(SUM(te.credit_amount), 0) as revenue
    FROM transactions t
    JOIN transaction_entries te ON t.id = te.transaction_id
    JOIN accounts a ON te.account_id = a.id
    WHERE t.company_id = ? AND t.transaction_date BETWEEN ? AND ?
      AND t.status = 'POSTED' AND a.type = 'REVENUE'
  `,
    [companyId, startDate, endDate]
  );

  // Expenses calculation
  const expensesResult = await db.raw(
    `
    SELECT COALESCE(SUM(te.debit_amount), 0) as expenses
    FROM transactions t
    JOIN transaction_entries te ON t.id = te.transaction_id
    JOIN accounts a ON te.account_id = a.id
    WHERE t.company_id = ? AND t.transaction_date BETWEEN ? AND ?
      AND t.status = 'POSTED' AND a.type = 'EXPENSE'
  `,
    [companyId, startDate, endDate]
  );

  const revenue = parseFloat(
    (revenueResult.rows?.[0] || revenueResult[0])?.revenue || 0
  );
  const expenses = parseFloat(
    (expensesResult.rows?.[0] || expensesResult[0])?.expenses || 0
  );
  const profit = revenue - expenses;
  const cashFlow = profit; // Simplified cash flow calculation

  return { revenue, expenses, profit, cashFlow };
}

function calculateGrowthRate(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / Math.abs(previous)) * 100;
}

async function getTopPerformingAccounts(
  companyId: string,
  startDate: string,
  endDate: string
) {
  const result = await db.raw(
    `
    SELECT
      a.name,
      a.type,
      SUM(CASE WHEN a.type = 'REVENUE' THEN te.credit_amount ELSE te.debit_amount END) as total_amount
    FROM accounts a
    JOIN transaction_entries te ON a.id = te.account_id
    JOIN transactions t ON te.transaction_id = t.id
    WHERE t.company_id = ? AND t.transaction_date BETWEEN ? AND ?
      AND t.status = 'POSTED'
    GROUP BY a.id, a.name, a.type
    ORDER BY total_amount DESC
    LIMIT 5
  `,
    [companyId, startDate, endDate]
  );

  return result.rows || result;
}

async function getTransactionsSummary(
  companyId: string,
  startDate: string,
  endDate: string
) {
  const result = await db.raw(
    `
    SELECT
      COUNT(*) as total_transactions,
      AVG(total_amount) as average_amount,
      SUM(total_amount) as total_amount
    FROM transactions
    WHERE company_id = ? AND transaction_date BETWEEN ? AND ?
      AND status = 'POSTED'
  `,
    [companyId, startDate, endDate]
  );

  return (
    result.rows?.[0] ||
    result[0] || { total_transactions: 0, average_amount: 0, total_amount: 0 }
  );
}

function getDateFormat(granularity: string): string {
  switch (granularity) {
    case "daily":
      return "day";
    case "weekly":
      return "week";
    case "monthly":
      return "month";
    case "quarterly":
      return "quarter";
    case "yearly":
      return "year";
    default:
      return "month";
  }
}

function calculateProfitMargins(revenueTrend: any[], expenseTrend: any[]) {
  return revenueTrend.map((revenueItem) => {
    const expenseItem = expenseTrend.find(
      (e) => e.period === revenueItem.period
    );
    const revenue = parseFloat(revenueItem.revenue || 0);
    const expenses = parseFloat(expenseItem?.expenses || 0);
    const profit = revenue - expenses;
    const margin = revenue > 0 ? (profit / revenue) * 100 : 0;

    return {
      period: revenueItem.period,
      revenue,
      expenses,
      profit,
      margin,
    };
  });
}

async function calculateCashFlowMetrics(
  companyId: string,
  startDate: string,
  endDate: string,
  granularity: string
) {
  // Simplified cash flow calculation
  const result = await db.raw(
    `
    SELECT
      DATE_TRUNC(?, t.transaction_date) as period,
      SUM(CASE WHEN a.type IN ('REVENUE', 'LIABILITY') THEN te.credit_amount - te.debit_amount
               ELSE te.debit_amount - te.credit_amount END) as cash_flow
    FROM transactions t
    JOIN transaction_entries te ON t.id = te.transaction_id
    JOIN accounts a ON te.account_id = a.id
    WHERE t.company_id = ? AND t.transaction_date BETWEEN ? AND ?
      AND t.status = 'POSTED'
    GROUP BY DATE_TRUNC(?, t.transaction_date)
    ORDER BY period
  `,
    [granularity, companyId, startDate, endDate, granularity]
  );

  return result.rows || result;
}

async function calculateFinancialRatios(companyId: string, asOfDate: string) {
  // Mock financial ratios - in production, calculate from balance sheet
  return {
    currentRatio: 1.5,
    quickRatio: 1.2,
    debtToEquity: 0.3,
    returnOnAssets: 0.08,
    returnOnEquity: 0.12,
  };
}

// Mock functions for demonstration
function calculateTrendStatistics(data: any[]) {
  if (data.length < 2)
    return { trend: "insufficient_data", slope: 0, correlation: 0 };

  const values = data.map((d) => parseFloat(d.value || 0));
  const n = values.length;
  const sumX = (n * (n + 1)) / 2;
  const sumY = values.reduce((a, b) => a + b, 0);
  const sumXY = values.reduce((sum, y, i) => sum + (i + 1) * y, 0);
  const sumX2 = (n * (n + 1) * (2 * n + 1)) / 6;

  const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
  const trend = slope > 0 ? "increasing" : slope < 0 ? "decreasing" : "stable";

  return { trend, slope, correlation: Math.abs(slope) };
}

function generateSimpleForecast(data: any[], periods: number) {
  if (data.length < 2) return [];

  const values = data.map((d) => parseFloat(d.value || 0));
  const trend = values[values.length - 1] - values[values.length - 2];

  const forecast = [];
  for (let i = 1; i <= periods; i++) {
    forecast.push({
      period: `Future Period ${i}`,
      value: values[values.length - 1] + trend * i,
      confidence: Math.max(0.5, 1 - i * 0.1),
    });
  }

  return forecast;
}

function generateTrendInsights(trendStats: any) {
  const insights = [];

  if (trendStats.trend === "increasing") {
    insights.push("Positive growth trend detected");
  } else if (trendStats.trend === "decreasing") {
    insights.push("Declining trend requires attention");
  } else {
    insights.push("Stable performance with minimal variation");
  }

  return insights;
}

async function calculateFinancialKPIs(companyId: string) {
  // Mock KPIs - in production, calculate from actual data
  return {
    grossProfitMargin: 35.5,
    netProfitMargin: 12.3,
    currentRatio: 1.8,
    quickRatio: 1.4,
    debtToEquityRatio: 0.25,
  };
}

async function calculateOperationalKPIs(companyId: string) {
  return {
    transactionVolume: 1250,
    averageTransactionValue: 850.75,
    processingTime: 2.3,
    errorRate: 0.02,
  };
}

async function calculateCustomerKPIs(companyId: string) {
  return {
    totalCustomers: 145,
    activeCustomers: 128,
    customerRetentionRate: 88.3,
    averageCustomerValue: 2450.5,
  };
}

async function getHistoricalData(
  companyId: string,
  metric: string,
  startDate: string,
  endDate: string
) {
  // Mock historical data - in production, fetch based on metric type
  const data = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  for (let d = new Date(start); d <= end; d.setMonth(d.getMonth() + 1)) {
    data.push({
      period: d.toISOString().split("T")[0],
      value: Math.random() * 10000 + 5000, // Mock data
    });
  }

  return data;
}

function performLinearRegression(data: any[], horizon: string) {
  // Simple linear regression implementation
  const values = data.map((d) => parseFloat(d.value || 0));
  const n = values.length;

  if (n < 2) return { value: 0, trend: "insufficient_data" };

  const avgValue = values.reduce((a, b) => a + b, 0) / n;
  const trend = values[n - 1] - values[0];

  return {
    value: avgValue + trend,
    trend: trend > 0 ? "increasing" : "decreasing",
    confidence: 0.75,
  };
}

function calculateConfidenceInterval(prediction: any, confidence: number) {
  const margin = prediction.value * (1 - confidence) * 0.5;
  return {
    lower: prediction.value - margin,
    upper: prediction.value + margin,
  };
}

function getMockIndustryBenchmarks(industry?: string, size?: string) {
  return {
    profitMargin: 15.2,
    revenueGrowth: 8.5,
    currentRatio: 1.6,
    debtToEquity: 0.4,
  };
}

function calculatePercentile(value: number, benchmark: number): number {
  return value > benchmark ? 75 : value > benchmark * 0.8 ? 50 : 25;
}

function generateBenchmarkRecommendations(performance: any): string[] {
  const recommendations = [];

  if (performance.profitMargin.percentile < 50) {
    recommendations.push(
      "Consider cost reduction strategies to improve profit margins"
    );
  }

  if (performance.revenueGrowth.percentile < 50) {
    recommendations.push("Focus on revenue growth initiatives");
  }

  return recommendations;
}

export default router;
