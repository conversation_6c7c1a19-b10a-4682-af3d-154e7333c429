import express from "express";
import type { Request, Response } from "express";
import {
  authenticateToken,
  requireCompanyAccess,
  requirePermission,
} from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { efdComplianceService } from "../services/efdComplianceService";
import { auditLogger } from "../middleware/auditMiddleware";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Register EFD Device
router.post(
  "/:companyId/devices",
  requireCompanyAccess,
  requirePermission("efd:manage"),
  auditLogger,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const deviceData = { ...req.body, companyId };

    // Validate required fields
    const requiredFields = ['deviceSerial', 'deviceModel', 'manufacturer', 'traDeviceId', 'connectionType'];
    for (const field of requiredFields) {
      if (!deviceData[field]) {
        return res.status(400).json({
          error: `${field} is required`
        });
      }
    }

    // Set audit data
    (req as any).auditData = {
      action: 'CREATE',
      tableName: 'efd_devices',
      description: `EFD device registered: ${deviceData.deviceSerial}`,
      newValues: deviceData
    };

    const deviceId = await efdComplianceService.registerDevice(deviceData);

    res.status(201).json({
      success: true,
      data: { id: deviceId },
      message: "EFD device registered successfully"
    });
  })
);

// Get EFD Devices
router.get(
  "/:companyId/devices",
  requireCompanyAccess,
  requirePermission("efd:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { limit = 10, offset = 0 } = req.query;

    const result = await efdComplianceService.getDevices(
      companyId,
      parseInt(limit as string),
      parseInt(offset as string)
    );

    res.json({
      success: true,
      data: result.devices,
      total: result.total,
      message: "EFD devices retrieved successfully"
    });
  })
);

// Record EFD Transaction
router.post(
  "/:companyId/transactions",
  requireCompanyAccess,
  requirePermission("efd:manage"),
  auditLogger,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const transactionData = { ...req.body, companyId };

    // Validate transaction data
    const validationErrors = efdComplianceService.validateTransaction(transactionData);
    if (validationErrors.length > 0) {
      return res.status(400).json({
        error: "Validation failed",
        details: validationErrors
      });
    }

    // Check if EFD is required for this amount
    if (!efdComplianceService.requiresEFD(transactionData.grossAmount)) {
      return res.status(400).json({
        error: `EFD not required for amounts below 5,000 TZS. Amount: ${transactionData.grossAmount} TZS`
      });
    }

    // Set audit data
    (req as any).auditData = {
      action: 'CREATE',
      tableName: 'efd_transactions',
      description: `EFD transaction recorded: ${transactionData.efdReceiptNumber}`,
      newValues: transactionData
    };

    const transactionId = await efdComplianceService.recordTransaction(transactionData);

    res.status(201).json({
      success: true,
      data: { id: transactionId },
      message: "EFD transaction recorded successfully"
    });
  })
);

// Get EFD Transactions
router.get(
  "/:companyId/transactions",
  requireCompanyAccess,
  requirePermission("efd:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { efdDeviceId, limit = 10, offset = 0 } = req.query;

    const result = await efdComplianceService.getTransactions(
      companyId,
      efdDeviceId as string,
      parseInt(limit as string),
      parseInt(offset as string)
    );

    res.json({
      success: true,
      data: result.transactions,
      total: result.total,
      message: "EFD transactions retrieved successfully"
    });
  })
);

// Generate Z-Report
router.post(
  "/:companyId/devices/:deviceId/z-report",
  requireCompanyAccess,
  requirePermission("efd:manage"),
  auditLogger,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, deviceId } = req.params;
    const { reportDate } = req.body;

    if (!reportDate) {
      return res.status(400).json({
        error: "Report date is required"
      });
    }

    const date = new Date(reportDate);
    if (isNaN(date.getTime())) {
      return res.status(400).json({
        error: "Invalid report date format"
      });
    }

    // Set audit data
    (req as any).auditData = {
      action: 'GENERATE',
      tableName: 'efd_z_reports',
      description: `Z-Report generated for device ${deviceId}`,
      newValues: { deviceId, reportDate }
    };

    const zReport = await efdComplianceService.generateZReport(companyId, deviceId, date);
    const zReportId = await efdComplianceService.saveZReport(zReport);

    res.json({
      success: true,
      data: { ...zReport, id: zReportId },
      message: "Z-Report generated successfully"
    });
  })
);

// Check EFD Requirement
router.post(
  "/:companyId/check-requirement",
  requireCompanyAccess,
  requirePermission("efd:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { amount } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        error: "Valid amount is required"
      });
    }

    const requiresEFD = efdComplianceService.requiresEFD(parseFloat(amount));
    const threshold = 5000; // TZS

    res.json({
      success: true,
      data: {
        requiresEFD,
        amount: parseFloat(amount),
        threshold,
        message: requiresEFD 
          ? `EFD required for amounts ${threshold} TZS and above`
          : `EFD not required for amounts below ${threshold} TZS`
      },
      message: "EFD requirement checked successfully"
    });
  })
);

// Validate EFD Transaction
router.post(
  "/:companyId/validate-transaction",
  requireCompanyAccess,
  requirePermission("efd:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const transactionData = req.body;

    const validationErrors = efdComplianceService.validateTransaction(transactionData);
    const isValid = validationErrors.length === 0;

    res.json({
      success: true,
      data: {
        isValid,
        errors: validationErrors,
        requiresEFD: transactionData.grossAmount ? 
          efdComplianceService.requiresEFD(transactionData.grossAmount) : false
      },
      message: isValid ? "Transaction is valid" : "Transaction validation failed"
    });
  })
);

// Get EFD Statistics
router.get(
  "/:companyId/statistics",
  requireCompanyAccess,
  requirePermission("efd:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { period = 'current-month' } = req.query;

    // This would be implemented to provide EFD statistics
    // For now, return mock data
    const statistics = {
      devices: {
        total: 2,
        active: 2,
        inactive: 0,
        expired: 0
      },
      transactions: {
        today: 45,
        thisWeek: 312,
        thisMonth: 1247,
        totalAmount: 15750000 // TZS
      },
      zReports: {
        generated: 30,
        submitted: 28,
        pending: 2
      },
      compliance: {
        score: 98,
        issues: 1,
        lastZReport: new Date().toISOString()
      }
    };

    res.json({
      success: true,
      data: statistics,
      message: "EFD statistics retrieved successfully"
    });
  })
);

// Get Device Status
router.get(
  "/:companyId/devices/:deviceId/status",
  requireCompanyAccess,
  requirePermission("efd:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, deviceId } = req.params;

    // This would check actual device status
    // For now, return mock status
    const deviceStatus = {
      deviceId,
      status: "ACTIVE",
      lastHeartbeat: new Date(),
      connectionStatus: "CONNECTED",
      certificateStatus: "VALID",
      certificateExpiry: "2025-01-01",
      lastTransaction: new Date(),
      todayTransactions: 23,
      todayAmount: 875000, // TZS
      lastZReport: "2024-12-01",
      pendingSubmissions: 0
    };

    res.json({
      success: true,
      data: deviceStatus,
      message: "Device status retrieved successfully"
    });
  })
);

export default router;
