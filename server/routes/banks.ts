import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { authenticateToken, requireCompanyAccess } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { bankIntegrationService } from '../services/bankIntegrationService';

// Extend Request interface to include user
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    companyId: string;
  };
}

const router = Router();

// Validation schemas
const linkTokenSchema = z.object({
  products: z.array(z.string()).default(['transactions', 'accounts']),
});

const connectAccountSchema = z.object({
  publicToken: z.string(),
});

const syncTransactionsSchema = z.object({
  bankAccountId: z.string().optional(),
});

const reconcileTransactionSchema = z.object({
  accountingTransactionId: z.string().optional(),
});

// POST /api/banks/link-token - Create Plaid link token
router.post(
  '/:companyId/link-token',
  authenticateToken,
  requireCompanyAccess,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { companyId } = req.params;
    const { products } = linkTokenSchema.parse(req.body);
    
    try {
      const linkToken = await bankIntegrationService.createLinkToken(
        companyId,
        req.user!.id,
        products
      );
      
      res.json(linkToken);
    } catch (error) {
      console.error('Failed to create link token:', error);
      res.status(500).json({ error: 'Failed to create bank connection link' });
    }
  })
);

// POST /api/banks/connect - Connect bank account
router.post(
  '/:companyId/connect',
  authenticateToken,
  requireCompanyAccess,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { companyId } = req.params;
    const { publicToken } = connectAccountSchema.parse(req.body);
    
    try {
      const result = await bankIntegrationService.connectBankAccount(
        companyId,
        req.user!.id,
        publicToken
      );
      
      res.json(result);
    } catch (error) {
      console.error('Failed to connect bank account:', error);
      res.status(500).json({ error: 'Failed to connect bank account' });
    }
  })
);

// GET /api/banks/:companyId/accounts - Get bank accounts
router.get(
  '/:companyId/accounts',
  authenticateToken,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;

    try {
      const accounts = await bankIntegrationService.getBankAccounts(companyId);
      res.json(accounts);
    } catch (error) {
      console.error('Failed to get bank accounts:', error);
      res.status(500).json({ error: 'Failed to get bank accounts' });
    }
  })
);

// POST /api/banks/:companyId/sync - Sync transactions
router.post(
  '/:companyId/sync',
  authenticateToken,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { bankAccountId } = syncTransactionsSchema.parse(req.body);
    
    try {
      const result = await bankIntegrationService.syncTransactions(companyId);
      res.json(result);
    } catch (error) {
      console.error('Failed to sync transactions:', error);
      res.status(500).json({ error: 'Failed to sync bank transactions' });
    }
  })
);

// GET /api/banks/:companyId/transactions - Get bank transactions
router.get(
  '/:companyId/transactions',
  authenticateToken,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const {
      bankAccountId,
      startDate,
      endDate,
      category,
      reconciled,
      page = '1',
      limit = '50'
    } = req.query;
    
    try {
      const options = {
        bankAccountId: bankAccountId as string,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        category: category as string,
        reconciled: reconciled === 'true' ? true : reconciled === 'false' ? false : undefined,
        page: parseInt(page as string),
        limit: parseInt(limit as string),
      };
      
      const transactions = await bankIntegrationService.getBankTransactions(companyId, options);
      res.json(transactions);
    } catch (error) {
      console.error('Failed to get bank transactions:', error);
      res.status(500).json({ error: 'Failed to get bank transactions' });
    }
  })
);

// PUT /api/banks/:companyId/transactions/:transactionId/reconcile - Reconcile transaction
router.put(
  '/:companyId/transactions/:transactionId/reconcile',
  authenticateToken,
  requireCompanyAccess,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { transactionId } = req.params;
    const { accountingTransactionId } = reconcileTransactionSchema.parse(req.body);
    
    try {
      await bankIntegrationService.reconcileTransaction(
        transactionId,
        req.user!.id,
        accountingTransactionId
      );
      
      res.json({ message: 'Transaction reconciled successfully' });
    } catch (error) {
      console.error('Failed to reconcile transaction:', error);
      res.status(500).json({ error: 'Failed to reconcile transaction' });
    }
  })
);

// DELETE /api/banks/:companyId/accounts/:accountId - Disconnect bank account
router.delete(
  '/:companyId/accounts/:accountId',
  authenticateToken,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, accountId } = req.params;
    
    try {
      await bankIntegrationService.disconnectBankAccount(companyId, accountId);
      res.json({ message: 'Bank account disconnected successfully' });
    } catch (error) {
      console.error('Failed to disconnect bank account:', error);
      res.status(500).json({ error: 'Failed to disconnect bank account' });
    }
  })
);

// POST /api/banks/webhooks/plaid - Plaid webhook handler
router.post(
  '/webhooks/plaid',
  asyncHandler(async (req: Request, res: Response) => {
    try {
      await bankIntegrationService.handleWebhook(req.body);
      res.status(200).json({ received: true });
    } catch (error) {
      console.error('Failed to handle Plaid webhook:', error);
      res.status(500).json({ error: 'Failed to handle webhook' });
    }
  })
);

export default router;
