import express from "express";
import type { Request, Response } from "express";
import {
  authenticateToken,
  requireCompanyAccess,
  // requirePermission,
} from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { db } from "../config/database";
import { v4 as uuidv4 } from "uuid";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

interface ContactFilters {
  contactType?: string;
  isActive?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

interface CreateContactRequest {
  companyId: string;
  contactType: string;
  name: string;
  displayName?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  website?: string;
  taxId?: string;
  billingAddress?: string;
  billingCity?: string;
  billingState?: string;
  billingPostalCode?: string;
  billingCountry?: string;
  shippingAddress?: string;
  shippingCity?: string;
  shippingState?: string;
  shippingPostalCode?: string;
  shippingCountry?: string;
  currency?: string;
  paymentTerms?: string;
  creditLimit?: number;
  customFields?: Record<string, any>;
  notes?: string;
}

// GET /api/contacts/:companyId
router.get(
  "/:companyId",
  requireCompanyAccess,
  // requirePermission("contacts:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const {
      contactType,
      isActive,
      search,
      page = 1,
      limit = 50,
    } = req.query as ContactFilters;

    let query = db("contacts").select("*").where("company_id", companyId);

    // Apply filters
    if (contactType) {
      query = query.where("contact_type", contactType);
    }

    if (isActive !== undefined) {
      query = query.where("is_active", isActive);
    }

    if (search) {
      query = query.where((builder) => {
        builder
          .where("name", "ilike", `%${search}%`)
          .orWhere("display_name", "ilike", `%${search}%`)
          .orWhere("email", "ilike", `%${search}%`)
          .orWhere("contact_number", "ilike", `%${search}%`)
          .orWhere("phone", "ilike", `%${search}%`)
          .orWhere("mobile", "ilike", `%${search}%`);
      });
    }

    // Get total count
    const countQuery = db("contacts")
      .where("company_id", companyId)
      .count("* as count")
      .first();

    if (contactType) {
      countQuery.where("contact_type", contactType);
    }

    if (isActive !== undefined) {
      countQuery.where("is_active", isActive);
    }

    if (search) {
      countQuery.where((builder) => {
        builder
          .where("name", "ilike", `%${search}%`)
          .orWhere("display_name", "ilike", `%${search}%`)
          .orWhere("email", "ilike", `%${search}%`)
          .orWhere("contact_number", "ilike", `%${search}%`)
          .orWhere("phone", "ilike", `%${search}%`)
          .orWhere("mobile", "ilike", `%${search}%`);
      });
    }

    // Apply pagination
    const offset = (Number(page) - 1) * Number(limit);
    query = query.orderBy("name", "asc").limit(Number(limit)).offset(offset);

    const [contacts, totalResult] = await Promise.all([query, countQuery]);

    const total = Number(totalResult?.count || 0);

    res.json({
      data: contacts,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        totalPages: Math.ceil(total / Number(limit)),
      },
    });
  })
);

// GET /api/contacts/:companyId/:contactId
router.get(
  "/:companyId/:contactId",
  requireCompanyAccess,
  // requirePermission("contacts:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, contactId } = req.params;

    const contact = await db("contacts")
      .where("company_id", companyId)
      .where("id", contactId)
      .first();

    if (!contact) {
      return res.status(404).json({ error: "Contact not found" });
    }

    res.json(contact);
  })
);

// POST /api/contacts
router.post(
  "/",
  // requirePermission("contacts:create"),
  asyncHandler(async (req: Request, res: Response) => {
    const {
      companyId,
      contactType,
      name,
      displayName,
      email,
      phone,
      mobile,
      website,
      taxId,
      billingAddress,
      billingCity,
      billingState,
      billingPostalCode,
      billingCountry,
      shippingAddress,
      shippingCity,
      shippingState,
      shippingPostalCode,
      shippingCountry,
      currency = "USD",
      paymentTerms,
      creditLimit = 0,
      customFields = {},
      notes,
    }: CreateContactRequest = req.body;

    // Validate required fields
    if (!companyId || !contactType || !name) {
      return res.status(400).json({
        error: "Missing required fields: companyId, contactType, name",
      });
    }

    // Generate contact number
    const lastContact = await db("contacts")
      .where("company_id", companyId)
      .orderBy("created_at", "desc")
      .first();

    const lastNumber = lastContact?.contact_number
      ? parseInt(lastContact.contact_number.replace(/\D/g, ""))
      : 0;
    const contactNumber = `${contactType
      .substring(0, 3)
      .toUpperCase()}-${String(lastNumber + 1).padStart(6, "0")}`;

    const contactId = uuidv4();
    const [newContact] = await db("contacts")
      .insert({
        id: contactId,
        company_id: companyId,
        contact_number: contactNumber,
        contact_type: contactType,
        name,
        display_name: displayName,
        email,
        phone,
        mobile,
        website,
        tax_id: taxId,
        billing_address: billingAddress,
        billing_city: billingCity,
        billing_state: billingState,
        billing_postal_code: billingPostalCode,
        billing_country: billingCountry,
        shipping_address: shippingAddress,
        shipping_city: shippingCity,
        shipping_state: shippingState,
        shipping_postal_code: shippingPostalCode,
        shipping_country: shippingCountry,
        currency,
        payment_terms: paymentTerms,
        credit_limit: creditLimit,
        custom_fields: customFields,
        notes,
      })
      .returning("*");

    res.status(201).json(newContact);
  })
);

// PUT /api/contacts/:companyId/:contactId
router.put(
  "/:companyId/:contactId",
  requireCompanyAccess,
  // requirePermission("contacts:update"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, contactId } = req.params;
    const updateData = req.body;

    const contact = await db("contacts")
      .where("company_id", companyId)
      .where("id", contactId)
      .first();

    if (!contact) {
      return res.status(404).json({ error: "Contact not found" });
    }

    // Remove fields that shouldn't be updated
    delete updateData.id;
    delete updateData.companyId;
    delete updateData.contactNumber;
    delete updateData.createdAt;

    // Convert camelCase to snake_case for database
    const dbUpdateData: any = {};
    Object.keys(updateData).forEach((key) => {
      const snakeKey = key.replace(
        /[A-Z]/g,
        (letter) => `_${letter.toLowerCase()}`
      );
      dbUpdateData[snakeKey] = updateData[key];
    });

    const [updatedContact] = await db("contacts")
      .where("id", contactId)
      .update({
        ...dbUpdateData,
        updated_at: new Date(),
      })
      .returning("*");

    res.json(updatedContact);
  })
);

// DELETE /api/contacts/:companyId/:contactId
router.delete(
  "/:companyId/:contactId",
  requireCompanyAccess,
  // requirePermission("contacts:delete"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, contactId } = req.params;

    const contact = await db("contacts")
      .where("company_id", companyId)
      .where("id", contactId)
      .first();

    if (!contact) {
      return res.status(404).json({ error: "Contact not found" });
    }

    // Check if contact is used in any invoices or transactions
    const invoiceCount = await db("invoices")
      .where("contact_id", contactId)
      .count("* as count")
      .first();

    if (Number(invoiceCount?.count || 0) > 0) {
      return res.status(400).json({
        error:
          "Cannot delete contact that has associated invoices. Deactivate instead.",
      });
    }

    await db("contacts").where("id", contactId).del();

    res.status(204).send();
  })
);

export default router;
