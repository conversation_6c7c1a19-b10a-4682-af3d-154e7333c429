import express from "express";
import type { Request, Response } from "express";
import {
  authenticateToken,
  requireCompanyAccess,
  // requirePermission,
} from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { db } from "../config/database";
import { v4 as uuidv4 } from "uuid";
import { invoiceLedgerService } from "../services/invoiceLedgerService";

// Extend Request interface to include user
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    companyId: string;
  };
}

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

interface InvoiceFilters {
  invoiceType?: string;
  status?: string;
  contactId?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  page?: number;
  limit?: number;
}

interface CreateInvoiceRequest {
  companyId: string;
  contactId: string;
  invoiceType: string;
  invoiceDate: string;
  dueDate: string;
  currency?: string;
  exchangeRate?: number;
  termsAndConditions?: string;
  notes?: string;
  lineItems: Array<{
    itemCode?: string;
    description: string;
    quantity: number;
    unit?: string;
    unitPrice: number;
    discountPercent?: number;
    accountId: string;
    taxDetails?: Record<string, any>;
  }>;
}

// GET /api/invoices/:companyId
router.get(
  "/:companyId",
  requireCompanyAccess,
  // requirePermission("invoices:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const {
      invoiceType,
      status,
      contactId,
      search,
      dateFrom,
      dateTo,
      page = 1,
      limit = 50,
    } = req.query as InvoiceFilters;

    let query = db("invoices")
      .select(
        "invoices.*",
        "contacts.name as contact_name",
        "contacts.display_name as contact_display_name",
        "contacts.contact_number as contact_number"
      )
      .leftJoin("contacts", "invoices.contact_id", "contacts.id")
      .where("invoices.company_id", companyId);

    // Apply filters
    if (invoiceType) {
      query = query.where("invoices.invoice_type", invoiceType);
    }

    if (status) {
      query = query.where("invoices.status", status);
    }

    if (contactId) {
      query = query.where("invoices.contact_id", contactId);
    }

    if (search) {
      query = query.where((builder) => {
        builder
          .where("invoices.invoice_number", "ilike", `%${search}%`)
          .orWhere("contacts.name", "ilike", `%${search}%`)
          .orWhere("contacts.display_name", "ilike", `%${search}%`)
          .orWhere("invoices.notes", "ilike", `%${search}%`);
      });
    }

    if (dateFrom) {
      query = query.where("invoices.invoice_date", ">=", dateFrom);
    }

    if (dateTo) {
      query = query.where("invoices.invoice_date", "<=", dateTo);
    }

    // Get total count
    const countQuery = db("invoices")
      .leftJoin("contacts", "invoices.contact_id", "contacts.id")
      .where("invoices.company_id", companyId)
      .count("* as count")
      .first();

    // Apply same filters to count query
    if (invoiceType) {
      countQuery.where("invoices.invoice_type", invoiceType);
    }
    if (status) {
      countQuery.where("invoices.status", status);
    }
    if (contactId) {
      countQuery.where("invoices.contact_id", contactId);
    }
    if (search) {
      countQuery.where((builder) => {
        builder
          .where("invoices.invoice_number", "ilike", `%${search}%`)
          .orWhere("contacts.name", "ilike", `%${search}%`)
          .orWhere("contacts.display_name", "ilike", `%${search}%`)
          .orWhere("invoices.notes", "ilike", `%${search}%`);
      });
    }
    if (dateFrom) {
      countQuery.where("invoices.invoice_date", ">=", dateFrom);
    }
    if (dateTo) {
      countQuery.where("invoices.invoice_date", "<=", dateTo);
    }

    // Apply pagination
    const offset = (Number(page) - 1) * Number(limit);
    query = query
      .orderBy("invoices.created_at", "desc")
      .limit(Number(limit))
      .offset(offset);

    const [invoices, totalResult] = await Promise.all([query, countQuery]);

    const total = Number(totalResult?.count || 0);

    // Convert decimal fields to numbers
    const processedInvoices = invoices.map((invoice: any) => ({
      ...invoice,
      subtotal: parseFloat(invoice.subtotal || 0),
      tax_amount: parseFloat(invoice.tax_amount || 0),
      discount_amount: parseFloat(invoice.discount_amount || 0),
      total_amount: parseFloat(invoice.total_amount || 0),
      paid_amount: parseFloat(invoice.paid_amount || 0),
      balance_due: parseFloat(invoice.balance_due || 0),
      exchange_rate: parseFloat(invoice.exchange_rate || 1),
    }));

    res.json({
      data: processedInvoices,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        totalPages: Math.ceil(total / Number(limit)),
      },
    });
  })
);

// GET /api/invoices/:companyId/:invoiceId
router.get(
  "/:companyId/:invoiceId",
  requireCompanyAccess,
  // requirePermission("invoices:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, invoiceId } = req.params;

    const invoice = await db("invoices")
      .select(
        "invoices.*",
        "contacts.name as contact_name",
        "contacts.display_name as contact_display_name",
        "contacts.contact_number as contact_number",
        "contacts.email as contact_email",
        "contacts.billing_address",
        "contacts.billing_city",
        "contacts.billing_state",
        "contacts.billing_postal_code",
        "contacts.billing_country"
      )
      .leftJoin("contacts", "invoices.contact_id", "contacts.id")
      .where("invoices.company_id", companyId)
      .where("invoices.id", invoiceId)
      .first();

    if (!invoice) {
      return res.status(404).json({ error: "Invoice not found" });
    }

    // Get line items
    const lineItems = await db("invoice_line_items")
      .select(
        "invoice_line_items.*",
        "accounts.code as account_code",
        "accounts.name as account_name"
      )
      .leftJoin("accounts", "invoice_line_items.account_id", "accounts.id")
      .where("invoice_line_items.invoice_id", invoiceId)
      .orderBy("invoice_line_items.line_number");

    // Convert decimal fields to numbers for invoice
    const processedInvoice = {
      ...invoice,
      subtotal: parseFloat(invoice.subtotal || 0),
      tax_amount: parseFloat(invoice.tax_amount || 0),
      discount_amount: parseFloat(invoice.discount_amount || 0),
      total_amount: parseFloat(invoice.total_amount || 0),
      paid_amount: parseFloat(invoice.paid_amount || 0),
      balance_due: parseFloat(invoice.balance_due || 0),
      exchange_rate: parseFloat(invoice.exchange_rate || 1),
    };

    // Convert decimal fields to numbers for line items
    const processedLineItems = lineItems.map((item: any) => ({
      ...item,
      quantity: parseFloat(item.quantity || 0),
      unit_price: parseFloat(item.unit_price || 0),
      discount_percent: parseFloat(item.discount_percent || 0),
      discount_amount: parseFloat(item.discount_amount || 0),
      line_total: parseFloat(item.line_total || 0),
    }));

    res.json({
      ...processedInvoice,
      lineItems: processedLineItems,
    });
  })
);

// POST /api/invoices
router.post(
  "/",
  // requirePermission("invoices:create"),
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const {
      companyId,
      contactId,
      invoiceType,
      invoiceDate,
      dueDate,
      currency = "USD",
      exchangeRate = 1,
      termsAndConditions,
      notes,
      lineItems,
    }: CreateInvoiceRequest = req.body;

    // Validate required fields
    if (
      !companyId ||
      !contactId ||
      !invoiceType ||
      !invoiceDate ||
      !dueDate ||
      !lineItems?.length
    ) {
      return res.status(400).json({
        error:
          "Missing required fields: companyId, contactId, invoiceType, invoiceDate, dueDate, lineItems",
      });
    }

    // Generate invoice number
    const lastInvoice = await db("invoices")
      .where("company_id", companyId)
      .where("invoice_type", invoiceType)
      .orderBy("created_at", "desc")
      .first();

    const lastNumber = lastInvoice?.invoice_number
      ? parseInt(lastInvoice.invoice_number.replace(/\D/g, ""))
      : 0;
    const invoiceNumber = `${invoiceType === "SALES" ? "INV" : "BILL"}-${String(
      lastNumber + 1
    ).padStart(6, "0")}`;

    // Calculate totals
    let subtotal = 0;
    let taxAmount = 0;
    let discountAmount = 0;

    const processedLineItems = lineItems.map((item: any, index: number) => {
      const lineDiscountAmount = item.discountPercent
        ? (item.quantity * item.unitPrice * item.discountPercent) / 100
        : 0;
      const lineTotal = item.quantity * item.unitPrice - lineDiscountAmount;

      subtotal += lineTotal;
      discountAmount += lineDiscountAmount;

      return {
        ...item,
        lineNumber: index + 1,
        discountAmount: lineDiscountAmount,
        lineTotal,
      };
    });

    const totalAmount = subtotal + taxAmount - discountAmount;
    const balanceDue = totalAmount;

    const invoiceId = uuidv4();

    try {
      await db.transaction(async (trx) => {
        // Insert invoice
        const [newInvoice] = await trx("invoices")
          .insert({
            id: invoiceId,
            company_id: companyId,
            invoice_number: invoiceNumber,
            contact_id: contactId,
            invoice_type: invoiceType,
            invoice_date: invoiceDate,
            due_date: dueDate,
            status: "DRAFT",
            subtotal,
            tax_amount: taxAmount,
            discount_amount: discountAmount,
            total_amount: totalAmount,
            paid_amount: 0,
            balance_due: balanceDue,
            currency,
            exchange_rate: exchangeRate,
            terms_and_conditions: termsAndConditions,
            notes,
            created_by: req.user?.id,
          })
          .returning("*");

        // Insert line items
        const lineItemsToInsert = processedLineItems.map((item: any) => ({
          id: uuidv4(),
          invoice_id: invoiceId,
          line_number: item.lineNumber,
          item_code: item.itemCode,
          description: item.description,
          quantity: item.quantity,
          unit: item.unit || "EA",
          unit_price: item.unitPrice,
          discount_percent: item.discountPercent || 0,
          discount_amount: item.discountAmount,
          line_total: item.lineTotal,
          account_id: item.accountId,
          tax_details: item.taxDetails || {},
        }));

        await trx("invoice_line_items").insert(lineItemsToInsert);

        // Convert decimal fields to numbers for response
        const processedInvoice = {
          ...newInvoice,
          subtotal: parseFloat(newInvoice.subtotal || 0),
          tax_amount: parseFloat(newInvoice.tax_amount || 0),
          discount_amount: parseFloat(newInvoice.discount_amount || 0),
          total_amount: parseFloat(newInvoice.total_amount || 0),
          paid_amount: parseFloat(newInvoice.paid_amount || 0),
          balance_due: parseFloat(newInvoice.balance_due || 0),
          exchange_rate: parseFloat(newInvoice.exchange_rate || 1),
        };

        const processedLineItemsResponse = lineItemsToInsert.map(
          (item: any) => ({
            ...item,
            quantity: parseFloat(item.quantity || 0),
            unit_price: parseFloat(item.unit_price || 0),
            discount_percent: parseFloat(item.discount_percent || 0),
            discount_amount: parseFloat(item.discount_amount || 0),
            line_total: parseFloat(item.line_total || 0),
          })
        );

        res.status(201).json({
          ...processedInvoice,
          lineItems: processedLineItemsResponse,
        });
      });
    } catch (error) {
      console.error("Failed to create invoice:", error);
      res.status(500).json({ error: "Failed to create invoice" });
    }
  })
);

// PUT /api/invoices/:companyId/:invoiceId
router.put(
  "/:companyId/:invoiceId",
  requireCompanyAccess,
  // requirePermission("invoices:update"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, invoiceId } = req.params;
    const updateData = req.body;

    const invoice = await db("invoices")
      .where("company_id", companyId)
      .where("id", invoiceId)
      .first();

    if (!invoice) {
      return res.status(404).json({ error: "Invoice not found" });
    }

    // Only allow updates to DRAFT invoices
    if (invoice.status !== "DRAFT") {
      return res.status(400).json({
        error: "Only draft invoices can be updated",
      });
    }

    try {
      await db.transaction(async (trx) => {
        // Update invoice
        const [updatedInvoice] = await trx("invoices")
          .where("id", invoiceId)
          .update({
            ...updateData,
            updated_at: new Date(),
          })
          .returning("*");

        // If line items are provided, update them
        if (updateData.lineItems) {
          // Delete existing line items
          await trx("invoice_line_items").where("invoice_id", invoiceId).del();

          // Insert new line items
          const lineItemsToInsert = updateData.lineItems.map(
            (item: any, index: number) => ({
              id: uuidv4(),
              invoice_id: invoiceId,
              line_number: index + 1,
              item_code: item.itemCode,
              description: item.description,
              quantity: item.quantity,
              unit: item.unit || "EA",
              unit_price: item.unitPrice,
              discount_percent: item.discountPercent || 0,
              discount_amount: item.discountAmount || 0,
              line_total: item.lineTotal,
              account_id: item.accountId,
              tax_details: item.taxDetails || {},
            })
          );

          await trx("invoice_line_items").insert(lineItemsToInsert);
        }

        // Convert decimal fields to numbers for response
        const processedInvoice = {
          ...updatedInvoice,
          subtotal: parseFloat(updatedInvoice.subtotal || 0),
          tax_amount: parseFloat(updatedInvoice.tax_amount || 0),
          discount_amount: parseFloat(updatedInvoice.discount_amount || 0),
          total_amount: parseFloat(updatedInvoice.total_amount || 0),
          paid_amount: parseFloat(updatedInvoice.paid_amount || 0),
          balance_due: parseFloat(updatedInvoice.balance_due || 0),
          exchange_rate: parseFloat(updatedInvoice.exchange_rate || 1),
        };

        res.json(processedInvoice);
      });
    } catch (error) {
      console.error("Failed to update invoice:", error);
      res.status(500).json({ error: "Failed to update invoice" });
    }
  })
);

// DELETE /api/invoices/:companyId/:invoiceId
router.delete(
  "/:companyId/:invoiceId",
  requireCompanyAccess,
  // requirePermission("invoices:delete"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, invoiceId } = req.params;

    const invoice = await db("invoices")
      .where("company_id", companyId)
      .where("id", invoiceId)
      .first();

    if (!invoice) {
      return res.status(404).json({ error: "Invoice not found" });
    }

    // Only allow deletion of DRAFT invoices
    if (invoice.status !== "DRAFT") {
      return res.status(400).json({
        error: "Only draft invoices can be deleted",
      });
    }

    try {
      await db.transaction(async (trx) => {
        // Delete line items first
        await trx("invoice_line_items").where("invoice_id", invoiceId).del();

        // Delete invoice
        await trx("invoices").where("id", invoiceId).del();
      });

      res.status(204).send();
    } catch (error) {
      console.error("Failed to delete invoice:", error);
      res.status(500).json({ error: "Failed to delete invoice" });
    }
  })
);

// POST /api/invoices/:companyId/:invoiceId/send
router.post(
  "/:companyId/:invoiceId/send",
  requireCompanyAccess,
  // requirePermission("invoices:send"),
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { companyId, invoiceId } = req.params;

    const invoice = await db("invoices")
      .where("company_id", companyId)
      .where("id", invoiceId)
      .first();

    if (!invoice) {
      return res.status(404).json({ error: "Invoice not found" });
    }

    if (invoice.status !== "DRAFT") {
      return res.status(400).json({
        error: "Only draft invoices can be sent",
      });
    }

    try {
      // Update invoice status
      await db("invoices")
        .where("id", invoiceId)
        .update({
          status: "SENT",
          updated_at: new Date(),
        })
        .returning("*");

      // Get updated invoice with contact information
      const invoiceWithContact = await db("invoices")
        .select(
          "invoices.*",
          "contacts.name as contact_name",
          "contacts.display_name as contact_display_name",
          "contacts.contact_number as contact_number",
          "contacts.email as contact_email",
          "contacts.billing_address",
          "contacts.billing_city",
          "contacts.billing_state",
          "contacts.billing_postal_code",
          "contacts.billing_country"
        )
        .leftJoin("contacts", "invoices.contact_id", "contacts.id")
        .where("invoices.id", invoiceId)
        .first();

      // Create journal entry for revenue/expense recognition
      await invoiceLedgerService.createInvoiceJournalEntry({
        invoiceId: invoiceId,
        invoiceNumber: invoice.invoice_number,
        invoiceType: invoice.invoice_type as "SALES" | "PURCHASE",
        totalAmount: parseFloat(invoice.total_amount),
        contactId: invoice.contact_id,
        invoiceDate: invoice.invoice_date,
        companyId: companyId,
        userId: req.user?.id || "",
      });

      // Get line items for the response
      const lineItems = await db("invoice_line_items")
        .select(
          "invoice_line_items.*",
          "accounts.code as account_code",
          "accounts.name as account_name"
        )
        .leftJoin("accounts", "invoice_line_items.account_id", "accounts.id")
        .where("invoice_line_items.invoice_id", invoiceId)
        .orderBy("invoice_line_items.line_number");

      // Convert decimal fields to numbers for response
      const processedInvoice = {
        ...invoiceWithContact,
        subtotal: parseFloat(invoiceWithContact.subtotal || 0),
        tax_amount: parseFloat(invoiceWithContact.tax_amount || 0),
        discount_amount: parseFloat(invoiceWithContact.discount_amount || 0),
        total_amount: parseFloat(invoiceWithContact.total_amount || 0),
        paid_amount: parseFloat(invoiceWithContact.paid_amount || 0),
        balance_due: parseFloat(invoiceWithContact.balance_due || 0),
        exchange_rate: parseFloat(invoiceWithContact.exchange_rate || 1),
      };

      // Convert decimal fields to numbers for line items
      const processedLineItems = lineItems.map((item: any) => ({
        ...item,
        quantity: parseFloat(item.quantity || 0),
        unit_price: parseFloat(item.unit_price || 0),
        discount_percent: parseFloat(item.discount_percent || 0),
        discount_amount: parseFloat(item.discount_amount || 0),
        line_total: parseFloat(item.line_total || 0),
      }));

      res.json({
        ...processedInvoice,
        lineItems: processedLineItems,
      });
    } catch (error) {
      console.error("Failed to send invoice:", error);
      res
        .status(500)
        .json({ error: "Failed to send invoice and create journal entry" });
    }
  })
);

// POST /api/invoices/:companyId/:invoiceId/mark-paid
router.post(
  "/:companyId/:invoiceId/mark-paid",
  requireCompanyAccess,
  // requirePermission("invoices:update"),
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { companyId, invoiceId } = req.params;
    const { paidAmount, paymentDate } = req.body;

    const invoice = await db("invoices")
      .where("company_id", companyId)
      .where("id", invoiceId)
      .first();

    if (!invoice) {
      return res.status(404).json({ error: "Invoice not found" });
    }

    const actualPaidAmount = parseFloat(paidAmount || invoice.total_amount);
    const totalPaid = parseFloat(invoice.paid_amount || 0) + actualPaidAmount;
    const balanceDue = parseFloat(invoice.total_amount) - totalPaid;
    const status = balanceDue <= 0 ? "PAID" : "SENT";

    try {
      // Update invoice payment status
      await db("invoices")
        .where("id", invoiceId)
        .update({
          paid_amount: totalPaid,
          balance_due: balanceDue,
          status,
          updated_at: new Date(),
        })
        .returning("*");

      // Get updated invoice with contact information
      const invoiceWithContact = await db("invoices")
        .select(
          "invoices.*",
          "contacts.name as contact_name",
          "contacts.display_name as contact_display_name",
          "contacts.contact_number as contact_number",
          "contacts.email as contact_email",
          "contacts.billing_address",
          "contacts.billing_city",
          "contacts.billing_state",
          "contacts.billing_postal_code",
          "contacts.billing_country"
        )
        .leftJoin("contacts", "invoices.contact_id", "contacts.id")
        .where("invoices.id", invoiceId)
        .first();

      // Create journal entry for payment
      await invoiceLedgerService.createPaymentJournalEntry({
        invoiceId: invoiceId,
        invoiceNumber: invoice.invoice_number,
        invoiceType: invoice.invoice_type as "SALES" | "PURCHASE",
        paidAmount: actualPaidAmount,
        paymentDate: paymentDate || new Date().toISOString().split("T")[0],
        companyId: companyId,
        userId: req.user?.id || "",
      });

      // Get line items for the response
      const lineItems = await db("invoice_line_items")
        .select(
          "invoice_line_items.*",
          "accounts.code as account_code",
          "accounts.name as account_name"
        )
        .leftJoin("accounts", "invoice_line_items.account_id", "accounts.id")
        .where("invoice_line_items.invoice_id", invoiceId)
        .orderBy("invoice_line_items.line_number");

      // Convert decimal fields to numbers for response
      const processedInvoice = {
        ...invoiceWithContact,
        subtotal: parseFloat(invoiceWithContact.subtotal || 0),
        tax_amount: parseFloat(invoiceWithContact.tax_amount || 0),
        discount_amount: parseFloat(invoiceWithContact.discount_amount || 0),
        total_amount: parseFloat(invoiceWithContact.total_amount || 0),
        paid_amount: parseFloat(invoiceWithContact.paid_amount || 0),
        balance_due: parseFloat(invoiceWithContact.balance_due || 0),
        exchange_rate: parseFloat(invoiceWithContact.exchange_rate || 1),
      };

      // Convert decimal fields to numbers for line items
      const processedLineItems = lineItems.map((item: any) => ({
        ...item,
        quantity: parseFloat(item.quantity || 0),
        unit_price: parseFloat(item.unit_price || 0),
        discount_percent: parseFloat(item.discount_percent || 0),
        discount_amount: parseFloat(item.discount_amount || 0),
        line_total: parseFloat(item.line_total || 0),
      }));

      res.json({
        ...processedInvoice,
        lineItems: processedLineItems,
      });
    } catch (error) {
      console.error("Failed to mark invoice as paid:", error);
      res
        .status(500)
        .json({ error: "Failed to process payment and create journal entry" });
    }
  })
);

// GET /api/invoices/:companyId/:invoiceId/pdf
router.get(
  "/:companyId/:invoiceId/pdf",
  requireCompanyAccess,
  // requirePermission("invoices:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, invoiceId } = req.params;

    try {
      // Get invoice with all details
      const invoice = await getInvoiceWithDetails(companyId, invoiceId);

      if (!invoice) {
        return res.status(404).json({ error: "Invoice not found" });
      }

      // Get company details
      const company = await db("companies").where("id", companyId).first();

      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      // Generate PDF
      const pdfBuffer = await generateInvoicePDF(invoice, company);

      // Set response headers for PDF
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="Invoice-${invoice.invoice_number}.pdf"`
      );
      res.setHeader("Content-Length", pdfBuffer.length);

      // Send PDF
      res.send(pdfBuffer);
    } catch (error) {
      console.error("Error generating invoice PDF:", error);
      res.status(500).json({ error: "Failed to generate invoice PDF" });
    }
  })
);

// Helper function to get invoice with all details
async function getInvoiceWithDetails(companyId: string, invoiceId: string) {
  try {
    // Get invoice with contact details
    const invoice = await db("invoices")
      .select(
        "invoices.*",
        "contacts.name as contact_name",
        "contacts.display_name as contact_display_name",
        "contacts.contact_number as contact_number",
        "contacts.email as contact_email",
        "contacts.billing_address",
        "contacts.billing_city",
        "contacts.billing_state",
        "contacts.billing_postal_code",
        "contacts.billing_country"
      )
      .leftJoin("contacts", "invoices.contact_id", "contacts.id")
      .where("invoices.company_id", companyId)
      .where("invoices.id", invoiceId)
      .first();

    if (!invoice) {
      return null;
    }

    // Get line items
    const lineItems = await db("invoice_line_items")
      .select(
        "invoice_line_items.*",
        "accounts.code as account_code",
        "accounts.name as account_name"
      )
      .leftJoin("accounts", "invoice_line_items.account_id", "accounts.id")
      .where("invoice_line_items.invoice_id", invoiceId)
      .orderBy("invoice_line_items.line_number");

    return {
      ...invoice,
      lineItems,
    };
  } catch (error) {
    console.error("Error getting invoice details:", error);
    throw error;
  }
}

// Helper function to generate invoice PDF
async function generateInvoicePDF(invoice: any, company: any): Promise<Buffer> {
  const PDFDocument = require("pdfkit");

  return new Promise((resolve, reject) => {
    try {
      const doc = new PDFDocument({ margin: 50 });
      const buffers: Buffer[] = [];

      doc.on("data", (chunk: Buffer) => buffers.push(chunk));
      doc.on("end", () => resolve(Buffer.concat(buffers)));
      doc.on("error", reject);

      // Header with company info
      doc
        .fontSize(20)
        .text(company.name, 50, 50)
        .fontSize(10)
        .text(company.address || "", 50, 80)
        .text(
          `${company.city || ""} ${company.state || ""} ${
            company.postal_code || ""
          }`,
          50,
          95
        )
        .text(company.phone || "", 50, 110)
        .text(company.email || "", 50, 125);

      // Invoice title and number
      doc
        .fontSize(24)
        .text("INVOICE", 400, 50)
        .fontSize(12)
        .text(`Invoice #: ${invoice.invoice_number}`, 400, 80)
        .text(`Date: ${formatDate(invoice.invoice_date)}`, 400, 100)
        .text(`Due Date: ${formatDate(invoice.due_date)}`, 400, 120);

      // Bill to section
      doc
        .fontSize(14)
        .text("Bill To:", 50, 180)
        .fontSize(12)
        .text(
          invoice.contact_display_name || invoice.contact_name || "",
          50,
          200
        )
        .text(invoice.billing_address || "", 50, 220)
        .text(
          `${invoice.billing_city || ""} ${invoice.billing_state || ""} ${
            invoice.billing_postal_code || ""
          }`,
          50,
          240
        )
        .text(invoice.contact_email || "", 50, 260);

      // Line items table header
      const tableTop = 320;
      doc
        .fontSize(10)
        .text("Description", 50, tableTop)
        .text("Qty", 250, tableTop)
        .text("Unit Price", 300, tableTop)
        .text("Discount", 380, tableTop)
        .text("Total", 450, tableTop);

      // Draw header line
      doc
        .moveTo(50, tableTop + 15)
        .lineTo(500, tableTop + 15)
        .stroke();

      // Line items
      let yPosition = tableTop + 30;
      let subtotal = 0;

      invoice.lineItems.forEach((item: any) => {
        const lineTotal = parseFloat(item.line_total) || 0;
        subtotal += lineTotal;

        doc
          .text(item.description, 50, yPosition, { width: 180 })
          .text(item.quantity.toString(), 250, yPosition)
          .text(formatCurrency(item.unit_price), 300, yPosition)
          .text(
            item.discount_percent ? `${item.discount_percent}%` : "-",
            380,
            yPosition
          )
          .text(formatCurrency(lineTotal), 450, yPosition);

        yPosition += 25;
      });

      // Totals section
      yPosition += 20;
      const totalsX = 350;

      doc.text(
        `Subtotal: ${formatCurrency(invoice.subtotal)}`,
        totalsX,
        yPosition
      );
      yPosition += 20;

      if (invoice.discount_amount > 0) {
        doc.text(
          `Discount: ${formatCurrency(invoice.discount_amount)}`,
          totalsX,
          yPosition
        );
        yPosition += 20;
      }

      if (invoice.tax_amount > 0) {
        doc.text(
          `Tax: ${formatCurrency(invoice.tax_amount)}`,
          totalsX,
          yPosition
        );
        yPosition += 20;
      }

      doc
        .fontSize(12)
        .text(
          `Total: ${formatCurrency(invoice.total_amount)}`,
          totalsX,
          yPosition
        );

      // Terms and conditions
      if (invoice.terms_and_conditions) {
        yPosition += 60;
        doc
          .fontSize(10)
          .text("Terms and Conditions:", 50, yPosition)
          .text(invoice.terms_and_conditions, 50, yPosition + 15, {
            width: 500,
          });
      }

      // Notes
      if (invoice.notes) {
        yPosition += 80;
        doc
          .fontSize(10)
          .text("Notes:", 50, yPosition)
          .text(invoice.notes, 50, yPosition + 15, { width: 500 });
      }

      doc.end();
    } catch (error) {
      reject(error);
    }
  });
}

// Helper function to format currency
function formatCurrency(amount: number, currency: string = "USD"): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency,
  }).format(amount);
}

// Helper function to format date
function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

export default router;
