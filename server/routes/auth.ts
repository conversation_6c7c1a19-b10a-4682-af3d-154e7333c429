import express from "express";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { z } from "zod";
import {
  asyncHandler,
  ValidationError,
  UnauthorizedError,
} from "../middleware/errorHandler";
import {
  authenticateToken,
  type AuthenticatedRequest,
} from "../middleware/auth";
import db from "../config/database";

const router = express.Router();

// Validation schemas
const loginSchema = z.object({
  email: z.string().email("Invalid email format"),
  password: z.string().min(1, "Password is required"),
});

const registerSchema = z.object({
  email: z.string().email("Invalid email format"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  phone: z.string().optional(),
});

// POST /api/auth/login
router.post(
  "/login",
  asyncHandler(async (req, res) => {
    const { email, password } = loginSchema.parse(req.body);

    // Find user with role information
    const user = await db("users")
      .select(
        "users.id",
        "users.email",
        "users.password_hash",
        "users.first_name",
        "users.last_name",
        "users.is_active",
        "users.role_id",
        "roles.name as role_name",
        "roles.permissions"
      )
      .join("roles", "users.role_id", "roles.id")
      .where("users.email", email.toLowerCase())
      .first();

    // Debug: Check if user exists and log available users
    if (!user) {
      console.log(`Login attempt for email: ${email}`);
      const allUsers = await db("users").select("email", "is_active");
      console.log("Available users in database:", allUsers);
      throw new UnauthorizedError("Invalid credentials");
    }

    if (!user.is_active) {
      console.log(`Login attempt for inactive user: ${email}`);
      throw new UnauthorizedError("Invalid credentials");
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      throw new UnauthorizedError("Invalid credentials");
    }

    // Update last login
    await db("users")
      .where("id", user.id)
      .update({ last_login_at: new Date() });

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new Error("JWT_SECRET not configured");
    }

    const payload = {
      userId: user.id,
      email: user.email,
      roleId: user.role_id,
    };

    const token = jwt.sign(payload, jwtSecret, {
      expiresIn: "7d",
    });

    // Get user's companies
    const companies = await db("user_companies")
      .select(
        "companies.id",
        "companies.name",
        "companies.base_currency",
        "user_companies.role_id"
      )
      .join("companies", "user_companies.company_id", "companies.id")
      .where("user_companies.user_id", user.id)
      .where("user_companies.is_active", true)
      .where("companies.is_active", true);

    res.json({
      token,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: {
          id: user.role_id,
          name: user.role_name,
          permissions: user.permissions,
        },
        companies,
      },
    });
  })
);

// POST /api/auth/register
router.post(
  "/register",
  asyncHandler(async (req, res) => {
    const { email, password, firstName, lastName, phone } =
      registerSchema.parse(req.body);

    // Check if user already exists
    const existingUser = await db("users")
      .where("email", email.toLowerCase())
      .first();

    if (existingUser) {
      throw new ValidationError("User with this email already exists");
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Get default role (assuming 'user' role exists)
    const defaultRole = await db("roles")
      .where("name", "user")
      .where("is_active", true)
      .first();

    if (!defaultRole) {
      throw new Error("Default user role not found");
    }

    // Create user
    const [newUser] = await db("users")
      .insert({
        email: email.toLowerCase(),
        password_hash: passwordHash,
        first_name: firstName,
        last_name: lastName,
        phone,
        role_id: defaultRole.id,
      })
      .returning(["id", "email", "first_name", "last_name"]);

    res.status(201).json({
      message: "User registered successfully",
      user: {
        id: newUser.id,
        email: newUser.email,
        firstName: newUser.first_name,
        lastName: newUser.last_name,
      },
    });
  })
);

// GET /api/auth/me
router.get(
  "/me",
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    if (!req.user) {
      throw new UnauthorizedError("User not authenticated");
    }

    // Prevent caching of user data
    res.set({
      "Cache-Control": "no-cache, no-store, must-revalidate",
      Pragma: "no-cache",
      Expires: "0",
    });

    // Get user details with role information
    const user = await db("users")
      .select(
        "users.id",
        "users.email",
        "users.first_name",
        "users.last_name",
        "users.is_active",
        "users.role_id",
        "roles.name as role_name",
        "roles.permissions"
      )
      .join("roles", "users.role_id", "roles.id")
      .where("users.id", req.user.id)
      .first();

    if (!user || !user.is_active) {
      throw new UnauthorizedError("User not found or inactive");
    }

    // Get user's companies
    const companies = await db("user_companies")
      .select(
        "companies.id",
        "companies.name",
        "companies.base_currency",
        "user_companies.role_id"
      )
      .join("companies", "user_companies.company_id", "companies.id")
      .where("user_companies.user_id", user.id)
      .where("user_companies.is_active", true)
      .where("companies.is_active", true);

    res.json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: {
          id: user.role_id,
          name: user.role_name,
          permissions: user.permissions,
        },
        companies,
      },
    });
  })
);

// POST /api/auth/logout
router.post("/logout", (_req, res) => {
  // In a stateless JWT system, logout is handled client-side
  // You could implement token blacklisting here if needed
  res.json({ message: "Logged out successfully" });
});

export default router;
