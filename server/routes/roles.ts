import express from "express";
import type { Request, Response } from "express";
import { z } from "zod";
import { as<PERSON><PERSON><PERSON><PERSON>, NotFoundError, ValidationError } from "../middleware/errorHandler";
import { authenticateToken, requirePermission } from "../middleware/auth";
import db from "../config/database";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const createRoleSchema = z.object({
  name: z.string().min(1).max(50),
  description: z.string().optional(),
  permissions: z.array(z.string()),
  isActive: z.boolean().default(true),
});

const updateRoleSchema = z.object({
  name: z.string().min(1).max(50).optional(),
  description: z.string().optional(),
  permissions: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
});

// GET /api/roles
router.get(
  "/",
  requirePermission("users:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { search, isActive } = req.query;

    let query = db("roles")
      .select("*")
      .orderBy("name", "asc");

    if (search) {
      query = query.where(function () {
        this.where("name", "ilike", `%${search}%`)
          .orWhere("description", "ilike", `%${search}%`);
      });
    }

    if (isActive !== undefined) {
      query = query.where("is_active", isActive === "true");
    }

    const roles = await query;

    res.json(roles.map(role => ({
      id: role.id,
      name: role.name,
      description: role.description,
      permissions: role.permissions || [],
      isActive: role.is_active,
      createdAt: role.created_at,
      updatedAt: role.updated_at,
    })));
  })
);

// GET /api/roles/:roleId
router.get(
  "/:roleId",
  requirePermission("users:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { roleId } = req.params;

    const role = await db("roles").where("id", roleId).first();

    if (!role) {
      throw new NotFoundError("Role not found");
    }

    res.json({
      id: role.id,
      name: role.name,
      description: role.description,
      permissions: role.permissions || [],
      isActive: role.is_active,
      createdAt: role.created_at,
      updatedAt: role.updated_at,
    });
  })
);

// POST /api/roles
router.post(
  "/",
  requirePermission("users:create"),
  asyncHandler(async (req: Request, res: Response) => {
    const data = createRoleSchema.parse(req.body);

    // Check if role name already exists
    const existingRole = await db("roles")
      .where("name", data.name)
      .first();

    if (existingRole) {
      throw new ValidationError("Role with this name already exists");
    }

    // Create role
    const [newRole] = await db("roles")
      .insert({
        name: data.name,
        description: data.description,
        permissions: JSON.stringify(data.permissions),
        is_active: data.isActive,
      })
      .returning("*");

    res.status(201).json({
      id: newRole.id,
      name: newRole.name,
      description: newRole.description,
      permissions: newRole.permissions || [],
      isActive: newRole.is_active,
      createdAt: newRole.created_at,
      updatedAt: newRole.updated_at,
    });
  })
);

// PUT /api/roles/:roleId
router.put(
  "/:roleId",
  requirePermission("users:update"),
  asyncHandler(async (req: Request, res: Response) => {
    const { roleId } = req.params;
    const data = updateRoleSchema.parse(req.body);

    // Check if role exists
    const existingRole = await db("roles").where("id", roleId).first();
    if (!existingRole) {
      throw new NotFoundError("Role not found");
    }

    // Check if name is being updated and already exists
    if (data.name && data.name !== existingRole.name) {
      const nameExists = await db("roles")
        .where("name", data.name)
        .where("id", "!=", roleId)
        .first();

      if (nameExists) {
        throw new ValidationError("Role name already exists");
      }
    }

    // Update role
    const updateData: any = {
      ...data,
      is_active: data.isActive,
      updated_at: new Date(),
    };

    if (data.permissions) {
      updateData.permissions = JSON.stringify(data.permissions);
    }

    const [updatedRole] = await db("roles")
      .where("id", roleId)
      .update(updateData)
      .returning("*");

    res.json({
      id: updatedRole.id,
      name: updatedRole.name,
      description: updatedRole.description,
      permissions: updatedRole.permissions || [],
      isActive: updatedRole.is_active,
      createdAt: updatedRole.created_at,
      updatedAt: updatedRole.updated_at,
    });
  })
);

// DELETE /api/roles/:roleId
router.delete(
  "/:roleId",
  requirePermission("users:delete"),
  asyncHandler(async (req: Request, res: Response) => {
    const { roleId } = req.params;

    // Check if role exists
    const existingRole = await db("roles").where("id", roleId).first();
    if (!existingRole) {
      throw new NotFoundError("Role not found");
    }

    // Check if role is in use
    const usersWithRole = await db("users").where("role_id", roleId).first();
    if (usersWithRole) {
      throw new ValidationError("Cannot delete role that is assigned to users");
    }

    // Delete role
    await db("roles").where("id", roleId).del();

    res.status(204).send();
  })
);

// GET /api/roles/:roleId/users
router.get(
  "/:roleId/users",
  requirePermission("users:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { roleId } = req.params;

    // Check if role exists
    const role = await db("roles").where("id", roleId).first();
    if (!role) {
      throw new NotFoundError("Role not found");
    }

    // Get users with this role
    const users = await db("users")
      .select(
        "users.id",
        "users.email",
        "users.first_name",
        "users.last_name",
        "users.is_active",
        "users.last_login_at",
        "users.created_at"
      )
      .where("users.role_id", roleId)
      .orderBy("users.first_name", "asc");

    res.json({
      role: {
        id: role.id,
        name: role.name,
        description: role.description,
      },
      users: users.map(user => ({
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        isActive: user.is_active,
        lastLoginAt: user.last_login_at,
        createdAt: user.created_at,
      })),
    });
  })
);

// GET /api/roles/permissions/available
router.get(
  "/permissions/available",
  requirePermission("users:read"),
  asyncHandler(async (req: Request, res: Response) => {
    // Return available permissions
    const permissions = [
      // User Management
      { category: "User Management", permissions: [
        { key: "users:read", name: "View Users", description: "View user list and details" },
        { key: "users:create", name: "Create Users", description: "Create new user accounts" },
        { key: "users:update", name: "Update Users", description: "Edit user information" },
        { key: "users:delete", name: "Delete Users", description: "Delete user accounts" },
      ]},
      
      // Company Management
      { category: "Company Management", permissions: [
        { key: "companies:read", name: "View Companies", description: "View company information" },
        { key: "companies:create", name: "Create Companies", description: "Create new companies" },
        { key: "companies:update", name: "Update Companies", description: "Edit company settings" },
        { key: "companies:delete", name: "Delete Companies", description: "Delete companies" },
      ]},
      
      // Accounting
      { category: "Accounting", permissions: [
        { key: "accounts:read", name: "View Accounts", description: "View chart of accounts" },
        { key: "accounts:create", name: "Create Accounts", description: "Create new accounts" },
        { key: "accounts:update", name: "Update Accounts", description: "Edit account information" },
        { key: "accounts:delete", name: "Delete Accounts", description: "Delete accounts" },
        { key: "transactions:read", name: "View Transactions", description: "View transaction records" },
        { key: "transactions:create", name: "Create Transactions", description: "Create new transactions" },
        { key: "transactions:update", name: "Update Transactions", description: "Edit transactions" },
        { key: "transactions:delete", name: "Delete Transactions", description: "Delete transactions" },
        { key: "transactions:approve", name: "Approve Transactions", description: "Approve pending transactions" },
      ]},
      
      // Invoicing
      { category: "Invoicing", permissions: [
        { key: "invoices:read", name: "View Invoices", description: "View invoice records" },
        { key: "invoices:create", name: "Create Invoices", description: "Create new invoices" },
        { key: "invoices:update", name: "Update Invoices", description: "Edit invoice information" },
        { key: "invoices:delete", name: "Delete Invoices", description: "Delete invoices" },
        { key: "invoices:send", name: "Send Invoices", description: "Send invoices to customers" },
      ]},
      
      // Contacts
      { category: "Contacts", permissions: [
        { key: "contacts:read", name: "View Contacts", description: "View contact records" },
        { key: "contacts:create", name: "Create Contacts", description: "Create new contacts" },
        { key: "contacts:update", name: "Update Contacts", description: "Edit contact information" },
        { key: "contacts:delete", name: "Delete Contacts", description: "Delete contacts" },
      ]},
      
      // Reports
      { category: "Reports", permissions: [
        { key: "reports:read", name: "View Reports", description: "Access financial reports" },
        { key: "reports:export", name: "Export Reports", description: "Export reports to various formats" },
      ]},
      
      // Settings
      { category: "Settings", permissions: [
        { key: "settings:read", name: "View Settings", description: "View system settings" },
        { key: "settings:update", name: "Update Settings", description: "Modify system settings" },
      ]},
      
      // Audit
      { category: "Audit", permissions: [
        { key: "audit:read", name: "View Audit Logs", description: "Access audit trail and logs" },
      ]},
    ];

    res.json(permissions);
  })
);

export default router;
