import { Router, Request, Response } from "express";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import db from "../config/database";
import { requireAuth, requireCompanyAccess } from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";

const router = Router();

// Validation schemas
const createPurchaseOrderSchema = z.object({
  companyId: z.string().uuid(),
  vendorId: z.string().uuid(),
  poDate: z.string(),
  expectedDeliveryDate: z.string().optional(),
  currency: z.string().default("USD"),
  exchangeRate: z.number().default(1),
  billingAddress: z.string().optional(),
  shippingAddress: z.string().optional(),
  notes: z.string().optional(),
  termsAndConditions: z.string().optional(),
  referenceNumber: z.string().optional(),
  lineItems: z.array(
    z.object({
      inventoryItemId: z.string().uuid().optional(),
      itemCode: z.string().optional(),
      description: z.string(),
      unitOfMeasure: z.string().optional(),
      quantityOrdered: z.number(),
      unitCost: z.number(),
      discountPercentage: z.number().default(0),
      taxRate: z.number().default(0),
      expectedDeliveryDate: z.string().optional(),
      notes: z.string().optional(),
    })
  ),
});

const updatePurchaseOrderSchema = createPurchaseOrderSchema
  .partial()
  .omit({ companyId: true });

const purchaseOrderFiltersSchema = z.object({
  status: z.string().optional(),
  vendorId: z.string().uuid().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  search: z.string().optional(),
  page: z.number().default(1),
  limit: z.number().default(50),
});

// GET /api/purchase-orders/:companyId - Get all purchase orders
router.get(
  "/:companyId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const filters = purchaseOrderFiltersSchema.parse(req.query);

    let query = db("purchase_orders")
      .select(
        "purchase_orders.*",
        "contacts.name as vendor_name",
        "contacts.display_name as vendor_display_name",
        "users.name as created_by_name"
      )
      .leftJoin("contacts", "purchase_orders.vendor_id", "contacts.id")
      .leftJoin("users", "purchase_orders.created_by", "users.id")
      .where("purchase_orders.company_id", companyId);

    // Apply filters
    if (filters.status) {
      query = query.where("purchase_orders.status", filters.status);
    }
    if (filters.vendorId) {
      query = query.where("purchase_orders.vendor_id", filters.vendorId);
    }
    if (filters.dateFrom) {
      query = query.where("purchase_orders.po_date", ">=", filters.dateFrom);
    }
    if (filters.dateTo) {
      query = query.where("purchase_orders.po_date", "<=", filters.dateTo);
    }
    if (filters.search) {
      query = query.where(function () {
        this.where("purchase_orders.po_number", "ilike", `%${filters.search}%`)
          .orWhere("contacts.name", "ilike", `%${filters.search}%`)
          .orWhere(
            "purchase_orders.reference_number",
            "ilike",
            `%${filters.search}%`
          );
      });
    }

    // Pagination
    const offset = (filters.page - 1) * filters.limit;
    const totalQuery = query.clone().clearSelect().count("* as count").first();
    const dataQuery = query
      .offset(offset)
      .limit(filters.limit)
      .orderBy("purchase_orders.po_date", "desc");

    const [total, purchaseOrders] = await Promise.all([totalQuery, dataQuery]);

    res.json({
      data: purchaseOrders,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total: Number(total?.count || 0),
        pages: Math.ceil(Number(total?.count || 0) / filters.limit),
      },
    });
  })
);

// GET /api/purchase-orders/:companyId/:poId - Get single purchase order
router.get(
  "/:companyId/:poId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, poId } = req.params;

    const purchaseOrder = await db("purchase_orders")
      .select(
        "purchase_orders.*",
        "contacts.name as vendor_name",
        "contacts.display_name as vendor_display_name",
        "contacts.email as vendor_email",
        "contacts.phone as vendor_phone",
        "contacts.billing_address as vendor_billing_address",
        "users.name as created_by_name"
      )
      .leftJoin("contacts", "purchase_orders.vendor_id", "contacts.id")
      .leftJoin("users", "purchase_orders.created_by", "users.id")
      .where("purchase_orders.company_id", companyId)
      .where("purchase_orders.id", poId)
      .first();

    if (!purchaseOrder) {
      return res.status(404).json({ error: "Purchase order not found" });
    }

    // Get line items
    const lineItems = await db("purchase_order_line_items")
      .select(
        "purchase_order_line_items.*",
        "inventory_items.name as inventory_item_name",
        "inventory_items.sku as inventory_item_sku"
      )
      .leftJoin(
        "inventory_items",
        "purchase_order_line_items.inventory_item_id",
        "inventory_items.id"
      )
      .where("purchase_order_id", poId)
      .orderBy("line_number");

    // Get attachments
    const attachments = await db("purchase_order_attachments")
      .select("*")
      .where("purchase_order_id", poId);

    // Get history
    const history = await db("purchase_order_history")
      .select("purchase_order_history.*", "users.name as changed_by_name")
      .leftJoin("users", "purchase_order_history.changed_by", "users.id")
      .where("purchase_order_id", poId)
      .orderBy("changed_at", "desc");

    res.json({
      ...purchaseOrder,
      lineItems,
      attachments,
      history,
    });
  })
);

// POST /api/purchase-orders - Create purchase order
router.post(
  "/",
  requireAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const data = createPurchaseOrderSchema.parse(req.body);
    const userId = (req as any).user.id;

    const trx = await db.transaction();

    try {
      // Generate PO number
      const lastPO = await trx("purchase_orders")
        .where("company_id", data.companyId)
        .orderBy("created_at", "desc")
        .first();

      const lastNumber = lastPO?.po_number
        ? parseInt(lastPO.po_number.replace(/\D/g, ""))
        : 0;
      const poNumber = `PO-${String(lastNumber + 1).padStart(6, "0")}`;

      // Calculate totals
      let subtotal = 0;
      let taxAmount = 0;
      const processedLineItems = data.lineItems.map((item, index) => {
        const discountAmount =
          (item.unitCost * item.quantityOrdered * item.discountPercentage) /
          100;
        const lineTotal = item.unitCost * item.quantityOrdered - discountAmount;
        const lineTaxAmount = (lineTotal * item.taxRate) / 100;

        subtotal += lineTotal;
        taxAmount += lineTaxAmount;

        return {
          ...item,
          lineNumber: index + 1,
          discountAmount,
          lineTotal,
          taxAmount: lineTaxAmount,
        };
      });

      const totalAmount = subtotal + taxAmount;

      // Create purchase order
      const poId = uuidv4();
      await trx("purchase_orders").insert({
        id: poId,
        company_id: data.companyId,
        po_number: poNumber,
        vendor_id: data.vendorId,
        po_date: data.poDate,
        expected_delivery_date: data.expectedDeliveryDate,
        currency: data.currency,
        exchange_rate: data.exchangeRate,
        subtotal,
        tax_amount: taxAmount,
        total_amount: totalAmount,
        billing_address: data.billingAddress,
        shipping_address: data.shippingAddress,
        notes: data.notes,
        terms_and_conditions: data.termsAndConditions,
        reference_number: data.referenceNumber,
        created_by: userId,
      });

      // Create line items
      const lineItemsToInsert = processedLineItems.map((item) => ({
        id: uuidv4(),
        purchase_order_id: poId,
        line_number: item.lineNumber,
        inventory_item_id: item.inventoryItemId,
        item_code: item.itemCode,
        description: item.description,
        unit_of_measure: item.unitOfMeasure,
        quantity_ordered: item.quantityOrdered,
        unit_cost: item.unitCost,
        discount_percentage: item.discountPercentage,
        discount_amount: item.discountAmount,
        line_total: item.lineTotal,
        tax_rate: item.taxRate,
        tax_amount: item.taxAmount,
        expected_delivery_date: item.expectedDeliveryDate,
        notes: item.notes,
      }));

      await trx("purchase_order_line_items").insert(lineItemsToInsert);

      // Create history entry
      await trx("purchase_order_history").insert({
        id: uuidv4(),
        purchase_order_id: poId,
        old_status: null,
        new_status: "DRAFT",
        notes: "Purchase order created",
        changed_by: userId,
      });

      await trx.commit();

      // Fetch the created purchase order with related data
      const createdPO = await db("purchase_orders")
        .select(
          "purchase_orders.*",
          "contacts.name as vendor_name",
          "contacts.display_name as vendor_display_name"
        )
        .leftJoin("contacts", "purchase_orders.vendor_id", "contacts.id")
        .where("purchase_orders.id", poId)
        .first();

      res.status(201).json(createdPO);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// PUT /api/purchase-orders/:companyId/:poId - Update purchase order
router.put(
  "/:companyId/:poId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, poId } = req.params;
    const data = updatePurchaseOrderSchema.parse(req.body);
    const userId = (req as any).user.id;

    // Check if PO exists and is editable
    const existingPO = await db("purchase_orders")
      .where("company_id", companyId)
      .where("id", poId)
      .first();

    if (!existingPO) {
      return res.status(404).json({ error: "Purchase order not found" });
    }

    if (["FULLY_RECEIVED", "CANCELLED", "CLOSED"].includes(existingPO.status)) {
      return res.status(400).json({
        error: "Cannot edit purchase order in current status",
      });
    }

    const trx = await db.transaction();

    try {
      const updateData: any = {
        updated_by: userId,
        updated_at: new Date(),
      };

      // Update basic fields
      if (data.vendorId) updateData.vendor_id = data.vendorId;
      if (data.poDate) updateData.po_date = data.poDate;
      if (data.expectedDeliveryDate)
        updateData.expected_delivery_date = data.expectedDeliveryDate;
      if (data.currency) updateData.currency = data.currency;
      if (data.exchangeRate) updateData.exchange_rate = data.exchangeRate;
      if (data.billingAddress !== undefined)
        updateData.billing_address = data.billingAddress;
      if (data.shippingAddress !== undefined)
        updateData.shipping_address = data.shippingAddress;
      if (data.notes !== undefined) updateData.notes = data.notes;
      if (data.termsAndConditions !== undefined)
        updateData.terms_and_conditions = data.termsAndConditions;
      if (data.referenceNumber !== undefined)
        updateData.reference_number = data.referenceNumber;

      // Update line items if provided
      if (data.lineItems) {
        // Delete existing line items
        await trx("purchase_order_line_items")
          .where("purchase_order_id", poId)
          .del();

        // Calculate new totals
        let subtotal = 0;
        let taxAmount = 0;
        const processedLineItems = data.lineItems.map((item, index) => {
          const discountAmount =
            (item.unitCost! *
              item.quantityOrdered! *
              (item.discountPercentage || 0)) /
            100;
          const lineTotal =
            item.unitCost! * item.quantityOrdered! - discountAmount;
          const lineTaxAmount = (lineTotal * (item.taxRate || 0)) / 100;

          subtotal += lineTotal;
          taxAmount += lineTaxAmount;

          return {
            ...item,
            lineNumber: index + 1,
            discountAmount,
            lineTotal,
            taxAmount: lineTaxAmount,
          };
        });

        const totalAmount = subtotal + taxAmount;

        // Update totals
        updateData.subtotal = subtotal;
        updateData.tax_amount = taxAmount;
        updateData.total_amount = totalAmount;

        // Insert new line items
        const lineItemsToInsert = processedLineItems.map((item) => ({
          id: uuidv4(),
          purchase_order_id: poId,
          line_number: item.lineNumber,
          inventory_item_id: item.inventoryItemId,
          item_code: item.itemCode,
          description: item.description!,
          unit_of_measure: item.unitOfMeasure,
          quantity_ordered: item.quantityOrdered!,
          unit_cost: item.unitCost!,
          discount_percentage: item.discountPercentage || 0,
          discount_amount: item.discountAmount,
          line_total: item.lineTotal,
          tax_rate: item.taxRate || 0,
          tax_amount: item.taxAmount,
          expected_delivery_date: item.expectedDeliveryDate,
          notes: item.notes,
        }));

        await trx("purchase_order_line_items").insert(lineItemsToInsert);
      }

      // Update purchase order
      await trx("purchase_orders").where("id", poId).update(updateData);

      await trx.commit();

      // Fetch updated purchase order
      const updatedPO = await db("purchase_orders")
        .select(
          "purchase_orders.*",
          "contacts.name as vendor_name",
          "contacts.display_name as vendor_display_name"
        )
        .leftJoin("contacts", "purchase_orders.vendor_id", "contacts.id")
        .where("purchase_orders.id", poId)
        .first();

      res.json(updatedPO);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// POST /api/purchase-orders/:companyId/:poId/status - Update PO status
router.post(
  "/:companyId/:poId/status",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, poId } = req.params;
    const { status, notes } = req.body;
    const userId = (req as any).user.id;

    const validStatuses = [
      "DRAFT",
      "PENDING_APPROVAL",
      "APPROVED",
      "SENT",
      "PARTIALLY_RECEIVED",
      "FULLY_RECEIVED",
      "CANCELLED",
      "CLOSED",
    ];

    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: "Invalid status" });
    }

    const existingPO = await db("purchase_orders")
      .where("company_id", companyId)
      .where("id", poId)
      .first();

    if (!existingPO) {
      return res.status(404).json({ error: "Purchase order not found" });
    }

    const trx = await db.transaction();

    try {
      const updateData: any = {
        status,
        updated_by: userId,
        updated_at: new Date(),
      };

      // Set approval fields if approving
      if (status === "APPROVED") {
        updateData.approved_by = userId;
        updateData.approved_at = new Date();
      }

      // Update purchase order
      await trx("purchase_orders").where("id", poId).update(updateData);

      // Create history entry
      await trx("purchase_order_history").insert({
        id: uuidv4(),
        purchase_order_id: poId,
        old_status: existingPO.status,
        new_status: status,
        notes: notes || `Status changed to ${status}`,
        changed_by: userId,
      });

      await trx.commit();

      res.json({ message: "Status updated successfully", status });
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// DELETE /api/purchase-orders/:companyId/:poId - Delete purchase order
router.delete(
  "/:companyId/:poId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, poId } = req.params;

    const existingPO = await db("purchase_orders")
      .where("company_id", companyId)
      .where("id", poId)
      .first();

    if (!existingPO) {
      return res.status(404).json({ error: "Purchase order not found" });
    }

    if (!["DRAFT", "CANCELLED"].includes(existingPO.status)) {
      return res.status(400).json({
        error: "Can only delete draft or cancelled purchase orders",
      });
    }

    // Delete purchase order (cascade will handle related records)
    await db("purchase_orders").where("id", poId).del();

    res.json({ message: "Purchase order deleted successfully" });
  })
);

export default router;
