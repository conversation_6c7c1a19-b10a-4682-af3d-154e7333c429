import express from "express";
import type { Request, Response } from "express";
import {
  authenticateToken,
  requirePermission,
} from "../middleware/auth";
import { asyncHand<PERSON> } from "../middleware/errorHandler";
import { performanceMonitor } from "../middleware/performanceMiddleware";
import { cacheService } from "../services/cacheService";
import { db } from "../config/database";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// GET /api/performance/stats - Get performance statistics
router.get(
  "/stats",
  requirePermission("system:admin"),
  asyncHandler(async (req: Request, res: Response) => {
    const { timeframe = 'hour' } = req.query;

    try {
      const stats = await performanceMonitor.getStats(timeframe as 'hour' | 'day' | 'week');
      
      if (!stats) {
        return res.json({
          success: true,
          data: {
            message: "No performance data available for the specified timeframe",
            timeframe
          }
        });
      }

      res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      console.error("Failed to get performance stats:", error);
      res.status(500).json({ error: "Failed to get performance stats" });
    }
  })
);

// GET /api/performance/health - System health check
router.get(
  "/health",
  requirePermission("system:admin"),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const health = await performanceMonitor.healthCheck();
      const cacheHealth = await cacheService.healthCheck();
      
      // Database health check
      const dbStart = Date.now();
      await db.raw('SELECT 1');
      const dbLatency = Date.now() - dbStart;

      const systemHealth = {
        ...health,
        database: {
          status: 'healthy',
          latency: dbLatency
        },
        cache: cacheHealth,
        timestamp: new Date().toISOString()
      };

      res.json({
        success: true,
        data: systemHealth
      });

    } catch (error) {
      console.error("Health check failed:", error);
      res.status(500).json({ 
        error: "Health check failed",
        details: error.message
      });
    }
  })
);

// GET /api/performance/slow-queries - Get slow database queries
router.get(
  "/slow-queries",
  requirePermission("system:admin"),
  asyncHandler(async (req: Request, res: Response) => {
    const { limit = 50 } = req.query;

    try {
      const slowQueries = performanceMonitor.getSlowQueries(parseInt(limit as string));

      res.json({
        success: true,
        data: {
          queries: slowQueries,
          total: slowQueries.length
        }
      });

    } catch (error) {
      console.error("Failed to get slow queries:", error);
      res.status(500).json({ error: "Failed to get slow queries" });
    }
  })
);

// GET /api/performance/memory - Memory usage statistics
router.get(
  "/memory",
  requirePermission("system:admin"),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const memoryUsage = process.memoryUsage();
      const cachedMemory = await cacheService.get('system:memory_usage');
      
      const memoryStats = {
        current: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024),
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          external: Math.round(memoryUsage.external / 1024 / 1024),
          arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024)
        },
        cached: cachedMemory,
        timestamp: new Date().toISOString()
      };

      res.json({
        success: true,
        data: memoryStats
      });

    } catch (error) {
      console.error("Failed to get memory stats:", error);
      res.status(500).json({ error: "Failed to get memory stats" });
    }
  })
);

// GET /api/performance/cache-stats - Cache performance statistics
router.get(
  "/cache-stats",
  requirePermission("system:admin"),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const cacheStats = await cacheService.getStats();
      const cacheHealth = await cacheService.healthCheck();

      res.json({
        success: true,
        data: {
          health: cacheHealth,
          stats: cacheStats,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error("Failed to get cache stats:", error);
      res.status(500).json({ error: "Failed to get cache stats" });
    }
  })
);

// GET /api/performance/database-stats - Database performance statistics
router.get(
  "/database-stats",
  requirePermission("system:admin"),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get database connection pool stats
      const poolStats = (db as any).client?.pool?.numUsed ? {
        used: (db as any).client.pool.numUsed(),
        free: (db as any).client.pool.numFree(),
        pending: (db as any).client.pool.numPendingAcquires(),
        total: (db as any).client.pool.max
      } : null;

      // Get table sizes (PostgreSQL specific)
      const tableSizes = await db.raw(`
        SELECT 
          schemaname,
          tablename,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
          pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        LIMIT 20
      `);

      // Get active connections
      const activeConnections = await db.raw(`
        SELECT count(*) as active_connections
        FROM pg_stat_activity 
        WHERE state = 'active'
      `);

      const dbStats = {
        connectionPool: poolStats,
        tableSizes: tableSizes.rows || tableSizes,
        activeConnections: (activeConnections.rows?.[0] || activeConnections[0])?.active_connections || 0,
        timestamp: new Date().toISOString()
      };

      res.json({
        success: true,
        data: dbStats
      });

    } catch (error) {
      console.error("Failed to get database stats:", error);
      res.status(500).json({ error: "Failed to get database stats" });
    }
  })
);

// POST /api/performance/optimize - Run performance optimizations
router.post(
  "/optimize",
  requirePermission("system:admin"),
  asyncHandler(async (req: Request, res: Response) => {
    const { actions = [] } = req.body;

    try {
      const results = {
        cacheCleared: false,
        memoryGC: false,
        dbOptimized: false,
        errors: []
      };

      // Clear cache if requested
      if (actions.includes('clear_cache')) {
        try {
          await cacheService.flush();
          results.cacheCleared = true;
        } catch (error) {
          results.errors.push(`Cache clear failed: ${error.message}`);
        }
      }

      // Force garbage collection if requested
      if (actions.includes('garbage_collect')) {
        try {
          if (global.gc) {
            global.gc();
            results.memoryGC = true;
          } else {
            results.errors.push('Garbage collection not available (run with --expose-gc)');
          }
        } catch (error) {
          results.errors.push(`Garbage collection failed: ${error.message}`);
        }
      }

      // Database optimization if requested
      if (actions.includes('optimize_db')) {
        try {
          // Run VACUUM ANALYZE on all tables (PostgreSQL)
          await db.raw('VACUUM ANALYZE');
          results.dbOptimized = true;
        } catch (error) {
          results.errors.push(`Database optimization failed: ${error.message}`);
        }
      }

      res.json({
        success: true,
        data: results,
        message: "Performance optimization completed"
      });

    } catch (error) {
      console.error("Performance optimization failed:", error);
      res.status(500).json({ error: "Performance optimization failed" });
    }
  })
);

// GET /api/performance/alerts - Get performance alerts
router.get(
  "/alerts",
  requirePermission("system:admin"),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const alerts = [];
      
      // Memory usage alerts
      const memoryUsage = process.memoryUsage();
      const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
      
      if (heapUsedMB > 512) {
        alerts.push({
          type: 'memory',
          severity: heapUsedMB > 1024 ? 'critical' : 'warning',
          message: `High memory usage: ${heapUsedMB}MB heap used`,
          value: heapUsedMB,
          threshold: 512
        });
      }

      // Performance alerts
      const stats = await performanceMonitor.getStats('hour');
      if (stats) {
        if (stats.averageResponseTime > 5000) {
          alerts.push({
            type: 'response_time',
            severity: stats.averageResponseTime > 10000 ? 'critical' : 'warning',
            message: `Slow average response time: ${stats.averageResponseTime.toFixed(2)}ms`,
            value: stats.averageResponseTime,
            threshold: 5000
          });
        }

        if (stats.errorRequests / stats.totalRequests > 0.05) {
          alerts.push({
            type: 'error_rate',
            severity: 'warning',
            message: `High error rate: ${((stats.errorRequests / stats.totalRequests) * 100).toFixed(2)}%`,
            value: (stats.errorRequests / stats.totalRequests) * 100,
            threshold: 5
          });
        }

        if (stats.cacheHitRate < 70) {
          alerts.push({
            type: 'cache_hit_rate',
            severity: 'warning',
            message: `Low cache hit rate: ${stats.cacheHitRate.toFixed(2)}%`,
            value: stats.cacheHitRate,
            threshold: 70
          });
        }
      }

      // Slow queries alert
      const slowQueries = performanceMonitor.getSlowQueries(10);
      if (slowQueries.length > 5) {
        alerts.push({
          type: 'slow_queries',
          severity: 'warning',
          message: `Multiple slow queries detected: ${slowQueries.length} queries`,
          value: slowQueries.length,
          threshold: 5
        });
      }

      res.json({
        success: true,
        data: {
          alerts,
          total: alerts.length,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error("Failed to get performance alerts:", error);
      res.status(500).json({ error: "Failed to get performance alerts" });
    }
  })
);

// GET /api/performance/recommendations - Get performance recommendations
router.get(
  "/recommendations",
  requirePermission("system:admin"),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const recommendations = [];
      
      // Analyze performance data and generate recommendations
      const stats = await performanceMonitor.getStats('day');
      const slowQueries = performanceMonitor.getSlowQueries(20);
      const memoryUsage = process.memoryUsage();

      // Memory recommendations
      const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
      if (heapUsedMB > 512) {
        recommendations.push({
          category: 'memory',
          priority: 'high',
          title: 'Optimize Memory Usage',
          description: 'High memory usage detected. Consider implementing memory optimization strategies.',
          actions: [
            'Review and optimize data structures',
            'Implement object pooling for frequently created objects',
            'Add memory monitoring and alerts',
            'Consider increasing server memory'
          ]
        });
      }

      // Cache recommendations
      if (stats && stats.cacheHitRate < 80) {
        recommendations.push({
          category: 'cache',
          priority: 'medium',
          title: 'Improve Cache Hit Rate',
          description: `Current cache hit rate is ${stats.cacheHitRate.toFixed(2)}%. Consider optimizing caching strategy.`,
          actions: [
            'Review cache TTL settings',
            'Implement cache warming for frequently accessed data',
            'Add more granular cache invalidation',
            'Consider increasing cache memory allocation'
          ]
        });
      }

      // Database recommendations
      if (slowQueries.length > 10) {
        recommendations.push({
          category: 'database',
          priority: 'high',
          title: 'Optimize Database Performance',
          description: `${slowQueries.length} slow queries detected. Database optimization needed.`,
          actions: [
            'Add missing database indexes',
            'Optimize slow queries',
            'Consider query result caching',
            'Review database connection pooling settings'
          ]
        });
      }

      // Response time recommendations
      if (stats && stats.averageResponseTime > 3000) {
        recommendations.push({
          category: 'response_time',
          priority: 'medium',
          title: 'Improve Response Times',
          description: `Average response time is ${stats.averageResponseTime.toFixed(2)}ms. Consider performance optimizations.`,
          actions: [
            'Implement response caching',
            'Optimize database queries',
            'Add CDN for static assets',
            'Consider horizontal scaling'
          ]
        });
      }

      res.json({
        success: true,
        data: {
          recommendations,
          total: recommendations.length,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error("Failed to get performance recommendations:", error);
      res.status(500).json({ error: "Failed to get performance recommendations" });
    }
  })
);

export default router;
