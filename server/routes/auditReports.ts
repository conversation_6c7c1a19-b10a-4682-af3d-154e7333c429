import express from 'express';
import { AuditReportService } from '../services/auditReportService.js';
import { authenticateToken } from '../middleware/auth.js';
import { requireRole } from '../middleware/roleAuth.js';

const router = express.Router();

// Apply authentication to all audit report routes
router.use(authenticateToken);

/**
 * GET /api/audit-reports/logs
 * Get audit logs with filtering and pagination
 */
router.get('/logs', requireRole(['ADMIN', 'SUPER_ADMIN']), async (req, res) => {
  try {
    const filters = {
      startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
      endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
      userId: req.query.userId as string,
      userEmail: req.query.userEmail as string,
      companyId: req.query.companyId as string,
      tableName: req.query.tableName as string,
      actionType: req.query.actionType as 'INSERT' | 'UPDATE' | 'DELETE',
      riskLevel: req.query.riskLevel as 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL',
      ipAddress: req.query.ipAddress as string,
      limit: req.query.limit ? parseInt(req.query.limit as string) : 50,
      offset: req.query.offset ? parseInt(req.query.offset as string) : 0
    };

    const result = await AuditReportService.getAuditLogs(filters);
    
    res.json({
      success: true,
      data: result.logs,
      pagination: {
        total: result.total,
        page: result.page,
        totalPages: result.totalPages,
        limit: filters.limit,
        offset: filters.offset
      }
    });

  } catch (error) {
    console.error('❌ Error getting audit logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get audit logs',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/audit-reports/access
 * Get access logs with filtering and pagination
 */
router.get('/access', requireRole(['ADMIN', 'SUPER_ADMIN']), async (req, res) => {
  try {
    const filters = {
      startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
      endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
      userId: req.query.userId as string,
      action: req.query.action as string,
      resource: req.query.resource as string,
      success: req.query.success ? req.query.success === 'true' : undefined,
      ipAddress: req.query.ipAddress as string,
      limit: req.query.limit ? parseInt(req.query.limit as string) : 50,
      offset: req.query.offset ? parseInt(req.query.offset as string) : 0
    };

    const result = await AuditReportService.getAccessLogs(filters);
    
    res.json({
      success: true,
      data: result.logs,
      pagination: {
        total: result.total,
        page: result.page,
        totalPages: result.totalPages,
        limit: filters.limit,
        offset: filters.offset
      }
    });

  } catch (error) {
    console.error('❌ Error getting access logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get access logs',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/audit-reports/financial
 * Get financial audit trail with filtering and pagination
 */
router.get('/financial', requireRole(['ADMIN', 'SUPER_ADMIN']), async (req, res) => {
  try {
    const filters = {
      startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
      endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
      accountId: req.query.accountId as string,
      actionType: req.query.actionType as string,
      minAmount: req.query.minAmount ? parseFloat(req.query.minAmount as string) : undefined,
      maxAmount: req.query.maxAmount ? parseFloat(req.query.maxAmount as string) : undefined,
      currency: req.query.currency as string,
      authorizationLevel: req.query.authorizationLevel as string,
      limit: req.query.limit ? parseInt(req.query.limit as string) : 50,
      offset: req.query.offset ? parseInt(req.query.offset as string) : 0
    };

    const result = await AuditReportService.getFinancialAuditTrail(filters);
    
    res.json({
      success: true,
      data: result.logs,
      pagination: {
        total: result.total,
        page: result.page,
        totalPages: result.totalPages,
        limit: filters.limit,
        offset: filters.offset
      }
    });

  } catch (error) {
    console.error('❌ Error getting financial audit trail:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get financial audit trail',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/audit-reports/summary
 * Get audit summary statistics
 */
router.get('/summary', requireRole(['ADMIN', 'SUPER_ADMIN']), async (req, res) => {
  try {
    const filters = {
      startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
      endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
      companyId: req.query.companyId as string
    };

    const summary = await AuditReportService.getAuditSummary(filters);
    
    res.json({
      success: true,
      data: summary
    });

  } catch (error) {
    console.error('❌ Error getting audit summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get audit summary',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/audit-reports/suspicious
 * Get suspicious activity report
 */
router.get('/suspicious', requireRole(['ADMIN', 'SUPER_ADMIN']), async (req, res) => {
  try {
    const filters = {
      startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
      endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
      companyId: req.query.companyId as string
    };

    const suspiciousActivity = await AuditReportService.getSuspiciousActivity(filters);
    
    res.json({
      success: true,
      data: suspiciousActivity
    });

  } catch (error) {
    console.error('❌ Error getting suspicious activity:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get suspicious activity',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/audit-reports/export/:type
 * Export audit data to CSV
 */
router.get('/export/:type', requireRole(['ADMIN', 'SUPER_ADMIN']), async (req, res) => {
  try {
    const type = req.params.type as 'audit' | 'access' | 'financial';
    
    if (!['audit', 'access', 'financial'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid export type. Must be: audit, access, or financial'
      });
    }

    const filters = {
      startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
      endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
      companyId: req.query.companyId as string
    };

    const csvData = await AuditReportService.exportToCsv(type, filters);
    
    // Set headers for CSV download
    const filename = `${type}_audit_report_${new Date().toISOString().split('T')[0]}.csv`;
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    
    res.send(csvData);

  } catch (error) {
    console.error('❌ Error exporting audit data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export audit data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/audit-reports/stats
 * Get quick audit statistics for dashboard
 */
router.get('/stats', requireRole(['ADMIN', 'SUPER_ADMIN']), async (req, res) => {
  try {
    const companyId = req.query.companyId as string;
    const timeframe = req.query.timeframe as string || '24h';
    
    // Calculate date range based on timeframe
    let startDate: Date;
    switch (timeframe) {
      case '1h':
        startDate = new Date(Date.now() - 60 * 60 * 1000);
        break;
      case '24h':
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
    }

    const filters = { startDate, companyId };
    
    // Get basic counts
    const auditLogs = await AuditReportService.getAuditLogs({ ...filters, limit: 1 });
    const accessLogs = await AuditReportService.getAccessLogs({ ...filters, limit: 1 });
    const financialLogs = await AuditReportService.getFinancialAuditTrail({ ...filters, limit: 1 });
    const failedAccess = await AuditReportService.getAccessLogs({ ...filters, success: false, limit: 1 });
    const highRiskLogs = await AuditReportService.getAuditLogs({ ...filters, riskLevel: 'HIGH', limit: 1 });

    res.json({
      success: true,
      data: {
        timeframe,
        totalAuditLogs: auditLogs.total,
        totalAccessLogs: accessLogs.total,
        totalFinancialLogs: financialLogs.total,
        failedAccessAttempts: failedAccess.total,
        highRiskActivities: highRiskLogs.total
      }
    });

  } catch (error) {
    console.error('❌ Error getting audit stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get audit stats',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
