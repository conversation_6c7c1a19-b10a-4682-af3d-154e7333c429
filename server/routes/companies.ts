import express from "express";
import { z } from "zod";
import {
  as<PERSON><PERSON><PERSON><PERSON>,
  ValidationError,
  NotFoundError,
} from "../middleware/errorHandler";
import { authenticateToken, requirePermission } from "../middleware/auth";
import {db} from "../config/database";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const createCompanySchema = z.object({
  name: z.string().min(1).max(255),
  legalName: z.string().optional(),
  taxId: z.string().optional(),
  registrationNumber: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email().optional(),
  website: z.string().optional(),
  baseCurrency: z.string().length(3).default("USD"),
  settings: z.record(z.any()).default({}),
});

const updateCompanySchema = createCompanySchema.partial();

// GET /api/companies
router.get(
  "/",
  asyncHandler(async (req: any, res) => {
    const companies = await db("user_companies")
      .select(
        "companies.id",
        "companies.name",
        "companies.legal_name",
        "companies.base_currency",
        "companies.is_active",
        "companies.created_at",
        "user_companies.role_id"
      )
      .join("companies", "user_companies.company_id", "companies.id")
      .where("user_companies.user_id", req.user.id)
      .where("user_companies.is_active", true)
      .where("companies.is_active", true)
      .orderBy("companies.name");

    res.json({
      data: companies.map((company) => ({
        id: company.id,
        name: company.name,
        legalName: company.legal_name,
        baseCurrency: company.base_currency,
        isActive: company.is_active,
        createdAt: company.created_at,
        userRoleId: company.role_id,
      })),
      total: companies.length,
    });
  })
);

// GET /api/companies/:id
router.get(
  "/:id",
  asyncHandler(async (req: any, res) => {
    const { id } = req.params;

    // Check if user has access to this company
    const userCompany = await db("user_companies")
      .where("user_id", req.user.id)
      .where("company_id", id)
      .where("is_active", true)
      .first();

    if (!userCompany) {
      throw new NotFoundError("Company not found or access denied");
    }

    const company = await db("companies")
      .where("id", id)
      .where("is_active", true)
      .first();

    if (!company) {
      throw new NotFoundError("Company not found");
    }

    res.json({
      id: company.id,
      name: company.name,
      legalName: company.legal_name,
      taxId: company.tax_id,
      registrationNumber: company.registration_number,
      address: company.address,
      city: company.city,
      state: company.state,
      postalCode: company.postal_code,
      country: company.country,
      phone: company.phone,
      email: company.email,
      website: company.website,
      baseCurrency: company.base_currency,
      settings: company.settings,
      isActive: company.is_active,
      createdAt: company.created_at,
      updatedAt: company.updated_at,
    });
  })
);

// POST /api/companies
router.post(
  "/",
  requirePermission("companies:create"),
  asyncHandler(async (req: any, res) => {
    const data = createCompanySchema.parse(req.body);

    // Check if company name already exists for this user
    const existingCompany = await db("companies")
      .join("user_companies", "companies.id", "user_companies.company_id")
      .where("user_companies.user_id", req.user.id)
      .where("companies.name", data.name)
      .where("companies.is_active", true)
      .first();

    if (existingCompany) {
      throw new ValidationError("Company with this name already exists");
    }

    // Get admin role for the new company
    const adminRole = await db("roles")
      .where("name", "admin")
      .where("is_active", true)
      .first();

    if (!adminRole) {
      throw new Error("Admin role not found");
    }

    // Create company
    const [newCompany] = await db("companies")
      .insert({
        name: data.name,
        legal_name: data.legalName,
        tax_id: data.taxId,
        registration_number: data.registrationNumber,
        address: data.address,
        city: data.city,
        state: data.state,
        postal_code: data.postalCode,
        country: data.country,
        phone: data.phone,
        email: data.email,
        website: data.website,
        base_currency: data.baseCurrency,
        settings: data.settings,
      })
      .returning("*");

    // Add user as admin of the new company
    await db("user_companies").insert({
      user_id: req.user.id,
      company_id: newCompany.id,
      role_id: adminRole.id,
    });

    res.status(201).json({
      id: newCompany.id,
      name: newCompany.name,
      legalName: newCompany.legal_name,
      baseCurrency: newCompany.base_currency,
      isActive: newCompany.is_active,
      createdAt: newCompany.created_at,
      updatedAt: newCompany.updated_at,
    });
  })
);

// PUT /api/companies/:id
router.put(
  "/:id",
  requirePermission("companies:update"),
  asyncHandler(async (req: any, res) => {
    const { id } = req.params;
    const data = updateCompanySchema.parse(req.body);

    // Check if user has access to this company
    const userCompany = await db("user_companies")
      .where("user_id", req.user.id)
      .where("company_id", id)
      .where("is_active", true)
      .first();

    if (!userCompany) {
      throw new NotFoundError("Company not found or access denied");
    }

    const [updatedCompany] = await db("companies")
      .where("id", id)
      .update({
        name: data.name,
        legal_name: data.legalName,
        tax_id: data.taxId,
        registration_number: data.registrationNumber,
        address: data.address,
        city: data.city,
        state: data.state,
        postal_code: data.postalCode,
        country: data.country,
        phone: data.phone,
        email: data.email,
        website: data.website,
        base_currency: data.baseCurrency,
        settings: data.settings,
        updated_at: new Date(),
      })
      .returning("*");

    res.json({
      id: updatedCompany.id,
      name: updatedCompany.name,
      legalName: updatedCompany.legal_name,
      baseCurrency: updatedCompany.base_currency,
      isActive: updatedCompany.is_active,
      createdAt: updatedCompany.created_at,
      updatedAt: updatedCompany.updated_at,
    });
  })
);

export default router;
