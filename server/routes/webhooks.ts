import express from "express";
import type { Request, Response } from "express";
import { z } from "zod";
import crypto from "crypto";
import {
  authenticateToken,
  requireCompanyAccess,
  requirePermission,
} from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { db } from "../config/database";
import { v4 as uuidv4 } from "uuid";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const createWebhookSchema = z.object({
  companyId: z.string().uuid(),
  name: z.string().min(1).max(255),
  url: z.string().url(),
  events: z.array(z.string()).min(1),
  secret: z.string().optional(),
  isActive: z.boolean().default(true),
  retryPolicy: z
    .object({
      maxRetries: z.number().int().min(0).max(10).default(3),
      retryDelay: z.number().int().min(1000).max(300000).default(5000), // milliseconds
      backoffMultiplier: z.number().min(1).max(5).default(2),
    })
    .optional(),
  headers: z.record(z.string()).optional(),
});

const updateWebhookSchema = createWebhookSchema.partial().extend({
  id: z.string().uuid(),
});

// GET /api/webhooks/:companyId - Get all webhooks for a company
router.get(
  "/:companyId",
  requireCompanyAccess,
  requirePermission("webhooks:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { includeInactive = false } = req.query;

    try {
      let query = db("webhooks")
        .select(
          "webhooks.*",
          db.raw("COUNT(webhook_deliveries.id) as total_deliveries"),
          db.raw(
            "COUNT(CASE WHEN webhook_deliveries.status = 'SUCCESS' THEN 1 END) as successful_deliveries"
          ),
          db.raw("MAX(webhook_deliveries.delivered_at) as last_delivery")
        )
        .leftJoin(
          "webhook_deliveries",
          "webhooks.id",
          "webhook_deliveries.webhook_id"
        )
        .where("webhooks.company_id", companyId)
        .groupBy("webhooks.id");

      if (!includeInactive) {
        query = query.where("webhooks.is_active", true);
      }

      const webhooks = await query.orderBy("webhooks.created_at", "desc");

      res.json({
        success: true,
        data: webhooks,
      });
    } catch (error) {
      console.error("Failed to get webhooks:", error);
      res.status(500).json({ error: "Failed to get webhooks" });
    }
  })
);

// POST /api/webhooks - Create a new webhook
router.post(
  "/",
  requirePermission("webhooks:create"),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const validatedData = createWebhookSchema.parse(req.body);

      // Generate secret if not provided
      const secret = validatedData.secret || generateWebhookSecret();

      const webhookId = uuidv4();

      const [webhook] = await db("webhooks")
        .insert({
          id: webhookId,
          company_id: validatedData.companyId,
          name: validatedData.name,
          url: validatedData.url,
          events: JSON.stringify(validatedData.events),
          secret: secret,
          is_active: validatedData.isActive,
          retry_policy: validatedData.retryPolicy
            ? JSON.stringify(validatedData.retryPolicy)
            : null,
          headers: validatedData.headers
            ? JSON.stringify(validatedData.headers)
            : null,
          created_by: (req as any).user?.id,
        })
        .returning("*");

      // Don't return the secret in the response
      const { secret: _, ...webhookResponse } = webhook;

      res.status(201).json({
        success: true,
        data: {
          ...webhookResponse,
          secretPreview: `${secret.substring(0, 8)}...`,
        },
        message: "Webhook created successfully",
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: "Validation failed",
          details: error.errors,
        });
      }

      console.error("Failed to create webhook:", error);
      res.status(500).json({ error: "Failed to create webhook" });
    }
  })
);

// PUT /api/webhooks/:companyId/:webhookId - Update a webhook
router.put(
  "/:companyId/:webhookId",
  requireCompanyAccess,
  requirePermission("webhooks:update"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, webhookId } = req.params;

    try {
      const validatedData = updateWebhookSchema.parse({
        ...req.body,
        id: webhookId,
      });

      // Check if webhook exists and belongs to company
      const existingWebhook = await db("webhooks")
        .where("id", webhookId)
        .where("company_id", companyId)
        .first();

      if (!existingWebhook) {
        return res.status(404).json({ error: "Webhook not found" });
      }

      const updateData: any = {
        updated_at: new Date(),
      };

      if (validatedData.name) updateData.name = validatedData.name;
      if (validatedData.url) updateData.url = validatedData.url;
      if (validatedData.events)
        updateData.events = JSON.stringify(validatedData.events);
      if (validatedData.secret) updateData.secret = validatedData.secret;
      if (validatedData.isActive !== undefined)
        updateData.is_active = validatedData.isActive;
      if (validatedData.retryPolicy)
        updateData.retry_policy = JSON.stringify(validatedData.retryPolicy);
      if (validatedData.headers)
        updateData.headers = JSON.stringify(validatedData.headers);

      await db("webhooks").where("id", webhookId).update(updateData);

      // Fetch updated webhook
      const updatedWebhook = await db("webhooks")
        .where("id", webhookId)
        .first();

      const { secret: _, ...webhookResponse } = updatedWebhook;

      res.json({
        success: true,
        data: {
          ...webhookResponse,
          secretPreview: `${updatedWebhook.secret.substring(0, 8)}...`,
        },
        message: "Webhook updated successfully",
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: "Validation failed",
          details: error.errors,
        });
      }

      console.error("Failed to update webhook:", error);
      res.status(500).json({ error: "Failed to update webhook" });
    }
  })
);

// POST /api/webhooks/:companyId/:webhookId/test - Test a webhook
router.post(
  "/:companyId/:webhookId/test",
  requireCompanyAccess,
  requirePermission("webhooks:test"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, webhookId } = req.params;

    try {
      const webhook = await db("webhooks")
        .where("id", webhookId)
        .where("company_id", companyId)
        .first();

      if (!webhook) {
        return res.status(404).json({ error: "Webhook not found" });
      }

      // Create test payload
      const testPayload = {
        event: "webhook.test",
        timestamp: new Date().toISOString(),
        data: {
          message: "This is a test webhook delivery",
          webhook_id: webhookId,
          company_id: companyId,
        },
      };

      // Send test webhook
      const delivery = await sendWebhook(webhook, testPayload, "webhook.test");

      res.json({
        success: true,
        data: delivery,
        message: "Test webhook sent successfully",
      });
    } catch (error) {
      console.error("Failed to test webhook:", error);
      res.status(500).json({ error: "Failed to test webhook" });
    }
  })
);

// GET /api/webhooks/:companyId/:webhookId/deliveries - Get webhook deliveries
router.get(
  "/:companyId/:webhookId/deliveries",
  requireCompanyAccess,
  requirePermission("webhooks:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, webhookId } = req.params;
    const { status, page = 1, limit = 20 } = req.query;

    try {
      // Verify webhook belongs to company
      const webhook = await db("webhooks")
        .where("id", webhookId)
        .where("company_id", companyId)
        .first();

      if (!webhook) {
        return res.status(404).json({ error: "Webhook not found" });
      }

      let query = db("webhook_deliveries").where("webhook_id", webhookId);

      if (status) {
        query = query.where("status", status);
      }

      // Get total count
      const totalResult = await query.clone().count("* as count").first();
      const total = parseInt(totalResult?.count as string) || 0;

      // Get paginated results
      const offset = (parseInt(page as string) - 1) * parseInt(limit as string);
      const deliveries = await query
        .orderBy("delivered_at", "desc")
        .limit(parseInt(limit as string))
        .offset(offset);

      res.json({
        success: true,
        data: deliveries,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total,
          totalPages: Math.ceil(total / parseInt(limit as string)),
        },
      });
    } catch (error) {
      console.error("Failed to get webhook deliveries:", error);
      res.status(500).json({ error: "Failed to get webhook deliveries" });
    }
  })
);

// Helper function to generate webhook secret
function generateWebhookSecret(): string {
  return crypto.randomBytes(32).toString("hex");
}

// Helper function to send webhook
async function sendWebhook(webhook: any, payload: any, eventType: string) {
  const deliveryId = uuidv4();
  const timestamp = new Date().toISOString();

  try {
    // Create signature
    const signature = createWebhookSignature(
      JSON.stringify(payload),
      webhook.secret
    );

    // Prepare headers
    const headers: any = {
      "Content-Type": "application/json",
      "User-Agent": "Accounting-System-Webhook/1.0",
      "X-Webhook-Signature": signature,
      "X-Webhook-Delivery": deliveryId,
      "X-Webhook-Event": eventType,
      "X-Webhook-Timestamp": timestamp,
    };

    // Add custom headers
    if (webhook.headers) {
      const customHeaders = JSON.parse(webhook.headers);
      Object.assign(headers, customHeaders);
    }

    // Send HTTP request
    const axios = require("axios");
    const startTime = Date.now();

    const response = await axios.post(webhook.url, payload, {
      headers,
      timeout: 30000, // 30 seconds timeout
      validateStatus: (status: number) => status < 500, // Don't throw on 4xx errors
    });

    const responseTime = Date.now() - startTime;

    // Log successful delivery
    const delivery = await db("webhook_deliveries")
      .insert({
        id: deliveryId,
        webhook_id: webhook.id,
        event_type: eventType,
        payload: JSON.stringify(payload),
        status: response.status < 400 ? "SUCCESS" : "FAILED",
        response_status: response.status,
        response_body: JSON.stringify(response.data).substring(0, 10000), // Limit response size
        response_time_ms: responseTime,
        delivered_at: new Date(),
        signature: signature,
      })
      .returning("*");

    return delivery[0];
  } catch (error) {
    console.error("Webhook delivery failed:", error);

    // Log failed delivery
    const delivery = await db("webhook_deliveries")
      .insert({
        id: deliveryId,
        webhook_id: webhook.id,
        event_type: eventType,
        payload: JSON.stringify(payload),
        status: "FAILED",
        response_status: error.response?.status || 0,
        response_body: error.message,
        response_time_ms: 0,
        delivered_at: new Date(),
        signature: createWebhookSignature(
          JSON.stringify(payload),
          webhook.secret
        ),
        error_message: error.message,
      })
      .returning("*");

    // Schedule retry if configured
    if (webhook.retry_policy) {
      await scheduleWebhookRetry(webhook, payload, eventType, 1);
    }

    return delivery[0];
  }
}

// Helper function to create webhook signature
function createWebhookSignature(payload: string, secret: string): string {
  return crypto.createHmac("sha256", secret).update(payload).digest("hex");
}

// Helper function to schedule webhook retry
async function scheduleWebhookRetry(
  webhook: any,
  payload: any,
  eventType: string,
  attempt: number
) {
  try {
    const retryPolicy = JSON.parse(webhook.retry_policy);

    if (attempt > retryPolicy.maxRetries) {
      console.log(`Max retries exceeded for webhook ${webhook.id}`);
      return;
    }

    const delay =
      retryPolicy.retryDelay *
      Math.pow(retryPolicy.backoffMultiplier, attempt - 1);

    // In a production system, you would use a job queue like Bull or Agenda
    // For now, we'll use setTimeout (not recommended for production)
    setTimeout(async () => {
      console.log(`Retrying webhook ${webhook.id}, attempt ${attempt}`);

      try {
        await sendWebhook(webhook, payload, eventType);
      } catch (error) {
        console.error(
          `Retry ${attempt} failed for webhook ${webhook.id}:`,
          error
        );
        await scheduleWebhookRetry(webhook, payload, eventType, attempt + 1);
      }
    }, delay);
  } catch (error) {
    console.error("Error scheduling webhook retry:", error);
  }
}

// Function to trigger webhooks for events (to be called from other parts of the system)
export async function triggerWebhooks(
  companyId: string,
  eventType: string,
  data: any
) {
  try {
    // Get all active webhooks for this company that listen to this event
    const webhooks = await db("webhooks")
      .where("company_id", companyId)
      .where("is_active", true)
      .whereRaw("JSON_EXTRACT(events, '$') LIKE ?", [`%"${eventType}"%`]);

    const payload = {
      event: eventType,
      timestamp: new Date().toISOString(),
      data: data,
      company_id: companyId,
    };

    // Send webhooks concurrently
    const deliveryPromises = webhooks.map((webhook) =>
      sendWebhook(webhook, payload, eventType)
    );

    await Promise.allSettled(deliveryPromises);
  } catch (error) {
    console.error("Error triggering webhooks:", error);
  }
}

export default router;
