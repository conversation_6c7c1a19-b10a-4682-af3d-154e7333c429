import express from "express";
import type { Request, Response } from "express";
import { z } from "zod";
import {
  authenticateToken,
  requireCompanyAccess,
  requirePermission,
} from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { db } from "../config/database";
import { v4 as uuidv4 } from "uuid";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const createCurrencySchema = z.object({
  companyId: z.string().uuid(),
  code: z.string().length(3),
  name: z.string().min(1).max(100),
  symbol: z.string().min(1).max(10),
  decimalPlaces: z.number().int().min(0).max(6).default(2),
  isActive: z.boolean().default(true),
  isBaseCurrency: z.boolean().default(false),
});

const exchangeRateSchema = z.object({
  fromCurrency: z.string().length(3),
  toCurrency: z.string().length(3),
  rate: z.number().positive(),
  effectiveDate: z.string(),
  source: z.string().optional().default("MANUAL"),
});

// GET /api/currencies/:companyId - Get all currencies for a company
router.get(
  "/:companyId",
  requireCompanyAccess,
  requirePermission("currencies:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { includeInactive = false } = req.query;

    try {
      let query = db("currencies").where("company_id", companyId);

      if (!includeInactive) {
        query = query.where("is_active", true);
      }

      const currencies = await query
        .orderBy("is_base_currency", "desc")
        .orderBy("code");

      res.json({
        success: true,
        data: currencies,
      });
    } catch (error) {
      console.error("Failed to get currencies:", error);
      res.status(500).json({ error: "Failed to get currencies" });
    }
  })
);

// POST /api/currencies - Create a new currency
router.post(
  "/",
  requirePermission("currencies:create"),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const validatedData = createCurrencySchema.parse(req.body);

      // Check if currency code already exists for this company
      const existingCurrency = await db("currencies")
        .where("company_id", validatedData.companyId)
        .where("code", validatedData.code)
        .first();

      if (existingCurrency) {
        return res.status(400).json({
          error: "Currency code already exists for this company",
        });
      }

      // If this is set as base currency, unset any existing base currency
      if (validatedData.isBaseCurrency) {
        await db("currencies")
          .where("company_id", validatedData.companyId)
          .update({ is_base_currency: false });
      }

      const currencyId = uuidv4();

      const [currency] = await db("currencies")
        .insert({
          id: currencyId,
          company_id: validatedData.companyId,
          code: validatedData.code,
          name: validatedData.name,
          symbol: validatedData.symbol,
          decimal_places: validatedData.decimalPlaces,
          is_active: validatedData.isActive,
          is_base_currency: validatedData.isBaseCurrency,
          created_by: (req as any).user?.id,
        })
        .returning("*");

      res.status(201).json({
        success: true,
        data: currency,
        message: "Currency created successfully",
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: "Validation failed",
          details: error.errors,
        });
      }

      console.error("Failed to create currency:", error);
      res.status(500).json({ error: "Failed to create currency" });
    }
  })
);

// GET /api/currencies/:companyId/exchange-rates - Get exchange rates
router.get(
  "/:companyId/exchange-rates",
  requireCompanyAccess,
  requirePermission("currencies:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const {
      fromCurrency,
      toCurrency,
      date = new Date().toISOString().split("T")[0],
    } = req.query;

    try {
      let query = db("exchange_rates")
        .where("company_id", companyId)
        .where("effective_date", "<=", date)
        .orderBy("effective_date", "desc");

      if (fromCurrency) {
        query = query.where("from_currency", fromCurrency);
      }
      if (toCurrency) {
        query = query.where("to_currency", toCurrency);
      }

      const rates = await query;

      // Get the latest rate for each currency pair
      const latestRates = rates.reduce((acc: any[], rate: any) => {
        const key = `${rate.from_currency}-${rate.to_currency}`;
        if (!acc.find((r) => `${r.from_currency}-${r.to_currency}` === key)) {
          acc.push(rate);
        }
        return acc;
      }, []);

      res.json({
        success: true,
        data: latestRates,
        date: date,
      });
    } catch (error) {
      console.error("Failed to get exchange rates:", error);
      res.status(500).json({ error: "Failed to get exchange rates" });
    }
  })
);

// POST /api/currencies/:companyId/exchange-rates - Create/update exchange rate
router.post(
  "/:companyId/exchange-rates",
  requireCompanyAccess,
  requirePermission("currencies:update"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;

    try {
      const validatedData = exchangeRateSchema.parse(req.body);

      // Check if currencies exist
      const fromCurrency = await db("currencies")
        .where("company_id", companyId)
        .where("code", validatedData.fromCurrency)
        .where("is_active", true)
        .first();

      const toCurrency = await db("currencies")
        .where("company_id", companyId)
        .where("code", validatedData.toCurrency)
        .where("is_active", true)
        .first();

      if (!fromCurrency || !toCurrency) {
        return res.status(400).json({
          error: "One or both currencies not found or inactive",
        });
      }

      const rateId = uuidv4();

      const [exchangeRate] = await db("exchange_rates")
        .insert({
          id: rateId,
          company_id: companyId,
          from_currency: validatedData.fromCurrency,
          to_currency: validatedData.toCurrency,
          rate: validatedData.rate,
          effective_date: validatedData.effectiveDate,
          source: validatedData.source,
          created_by: (req as any).user?.id,
        })
        .returning("*");

      // Create inverse rate automatically
      if (validatedData.rate !== 0) {
        await db("exchange_rates").insert({
          id: uuidv4(),
          company_id: companyId,
          from_currency: validatedData.toCurrency,
          to_currency: validatedData.fromCurrency,
          rate: 1 / validatedData.rate,
          effective_date: validatedData.effectiveDate,
          source: validatedData.source,
          created_by: (req as any).user?.id,
        });
      }

      res.status(201).json({
        success: true,
        data: exchangeRate,
        message: "Exchange rate created successfully",
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: "Validation failed",
          details: error.errors,
        });
      }

      console.error("Failed to create exchange rate:", error);
      res.status(500).json({ error: "Failed to create exchange rate" });
    }
  })
);

// POST /api/currencies/:companyId/convert - Convert amount between currencies
router.post(
  "/:companyId/convert",
  requireCompanyAccess,
  requirePermission("currencies:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const {
      amount,
      fromCurrency,
      toCurrency,
      date = new Date().toISOString().split("T")[0],
    } = req.body;

    try {
      if (!amount || !fromCurrency || !toCurrency) {
        return res.status(400).json({
          error: "Amount, fromCurrency, and toCurrency are required",
        });
      }

      const conversion = await convertCurrency({
        companyId,
        amount: parseFloat(amount),
        fromCurrency,
        toCurrency,
        date,
      });

      res.json({
        success: true,
        data: conversion,
      });
    } catch (error) {
      console.error("Failed to convert currency:", error);
      res.status(500).json({ error: "Failed to convert currency" });
    }
  })
);

// Helper function to convert currency
async function convertCurrency(params: {
  companyId: string;
  amount: number;
  fromCurrency: string;
  toCurrency: string;
  date: string;
}) {
  try {
    // If same currency, return original amount
    if (params.fromCurrency === params.toCurrency) {
      return {
        originalAmount: params.amount,
        convertedAmount: params.amount,
        fromCurrency: params.fromCurrency,
        toCurrency: params.toCurrency,
        exchangeRate: 1,
        date: params.date,
      };
    }

    // Get exchange rate
    const exchangeRate = await db("exchange_rates")
      .where("company_id", params.companyId)
      .where("from_currency", params.fromCurrency)
      .where("to_currency", params.toCurrency)
      .where("effective_date", "<=", params.date)
      .orderBy("effective_date", "desc")
      .first();

    if (!exchangeRate) {
      throw new Error(
        `Exchange rate not found for ${params.fromCurrency} to ${params.toCurrency}`
      );
    }

    const convertedAmount = params.amount * exchangeRate.rate;

    return {
      originalAmount: params.amount,
      convertedAmount: Math.round(convertedAmount * 100) / 100,
      fromCurrency: params.fromCurrency,
      toCurrency: params.toCurrency,
      exchangeRate: exchangeRate.rate,
      date: params.date,
      rateDate: exchangeRate.effective_date,
    };
  } catch (error) {
    console.error("Error converting currency:", error);
    throw error;
  }
}

// POST /api/currencies/:companyId/revaluate - Perform currency revaluation
router.post(
  "/:companyId/revaluate",
  requireCompanyAccess,
  requirePermission("currencies:update"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const {
      revaluationDate = new Date().toISOString().split("T")[0],
      accountIds,
      currencies,
    } = req.body;

    try {
      const revaluationResult = await performCurrencyRevaluation({
        companyId,
        revaluationDate,
        accountIds,
        currencies,
        userId: (req as any).user?.id,
      });

      res.json({
        success: true,
        data: revaluationResult,
        message: "Currency revaluation completed successfully",
      });
    } catch (error) {
      console.error("Failed to perform currency revaluation:", error);
      res.status(500).json({ error: "Failed to perform currency revaluation" });
    }
  })
);

// GET /api/currencies/:companyId/gain-loss-report - Currency gain/loss report
router.get(
  "/:companyId/gain-loss-report",
  requireCompanyAccess,
  requirePermission("currencies:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { startDate, endDate, currency, reportType = "summary" } = req.query;

    try {
      const report = await generateCurrencyGainLossReport({
        companyId,
        startDate: startDate as string,
        endDate: endDate as string,
        currency: currency as string,
        reportType: reportType as string,
      });

      res.json({
        success: true,
        data: report,
      });
    } catch (error) {
      console.error("Failed to generate gain/loss report:", error);
      res.status(500).json({ error: "Failed to generate report" });
    }
  })
);

// POST /api/currencies/:companyId/update-rates - Update exchange rates from external API
router.post(
  "/:companyId/update-rates",
  requireCompanyAccess,
  requirePermission("currencies:update"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { provider = "ECB", baseCurrency } = req.body;

    try {
      const updateResult = await updateExchangeRatesFromAPI({
        companyId,
        provider,
        baseCurrency,
        userId: (req as any).user?.id,
      });

      res.json({
        success: true,
        data: updateResult,
        message: "Exchange rates updated successfully",
      });
    } catch (error) {
      console.error("Failed to update exchange rates:", error);
      res.status(500).json({ error: "Failed to update exchange rates" });
    }
  })
);

// Helper function to perform currency revaluation
async function performCurrencyRevaluation(params: {
  companyId: string;
  revaluationDate: string;
  accountIds?: string[];
  currencies?: string[];
  userId: string;
}) {
  try {
    const results = [];

    // Get accounts with foreign currency balances
    let accountQuery = db("accounts")
      .select(
        "accounts.*",
        db.raw(
          "COALESCE(SUM(te.debit_amount - te.credit_amount), 0) as balance"
        )
      )
      .leftJoin("transaction_entries as te", "accounts.id", "te.account_id")
      .leftJoin("transactions as t", "te.transaction_id", "t.id")
      .where("accounts.company_id", params.companyId)
      .where(
        "accounts.currency",
        "!=",
        db.raw(
          "(SELECT base_currency FROM currency_settings WHERE company_id = ?)",
          [params.companyId]
        )
      )
      .where(function () {
        this.whereNull("t.transaction_date").orWhere(
          "t.transaction_date",
          "<=",
          params.revaluationDate
        );
      })
      .where("t.status", "POSTED")
      .groupBy("accounts.id");

    if (params.accountIds) {
      accountQuery = accountQuery.whereIn("accounts.id", params.accountIds);
    }
    if (params.currencies) {
      accountQuery = accountQuery.whereIn(
        "accounts.currency",
        params.currencies
      );
    }

    const accounts = await accountQuery;

    // Get base currency
    const currencySettings = await db("currency_settings")
      .where("company_id", params.companyId)
      .first();

    if (!currencySettings) {
      throw new Error("Currency settings not found");
    }

    // Process each account
    for (const account of accounts) {
      if (account.balance === 0) continue;

      // Get current exchange rate
      const exchangeRate = await db("exchange_rates")
        .where("company_id", params.companyId)
        .where("from_currency", account.currency)
        .where("to_currency", currencySettings.base_currency)
        .where("effective_date", "<=", params.revaluationDate)
        .orderBy("effective_date", "desc")
        .first();

      if (!exchangeRate) {
        console.warn(
          `No exchange rate found for ${account.currency} to ${currencySettings.base_currency}`
        );
        continue;
      }

      // Calculate revalued balance
      const originalBalance = parseFloat(account.balance);
      const revaluedBalance = originalBalance * exchangeRate.rate;

      // Get previous revalued balance
      const previousRevaluation = await db("currency_revaluations")
        .where("account_id", account.id)
        .where("currency", account.currency)
        .orderBy("revaluation_date", "desc")
        .first();

      const previousRevaluedBalance = previousRevaluation
        ? parseFloat(previousRevaluation.revalued_balance)
        : originalBalance;

      const revaluationGainLoss = revaluedBalance - previousRevaluedBalance;

      // Create revaluation record
      const revaluationId = uuidv4();
      await db("currency_revaluations").insert({
        id: revaluationId,
        company_id: params.companyId,
        account_id: account.id,
        currency: account.currency,
        revaluation_date: params.revaluationDate,
        original_balance: originalBalance,
        revalued_balance: revaluedBalance,
        revaluation_gain_loss: revaluationGainLoss,
        exchange_rate: exchangeRate.rate,
        created_by: params.userId,
      });

      results.push({
        accountId: account.id,
        accountName: account.name,
        currency: account.currency,
        originalBalance,
        revaluedBalance,
        revaluationGainLoss,
        exchangeRate: exchangeRate.rate,
      });
    }

    return {
      revaluationDate: params.revaluationDate,
      baseCurrency: currencySettings.base_currency,
      accountsProcessed: results.length,
      totalGainLoss: results.reduce((sum, r) => sum + r.revaluationGainLoss, 0),
      results,
    };
  } catch (error) {
    console.error("Error performing currency revaluation:", error);
    throw error;
  }
}

// Helper function to generate currency gain/loss report
async function generateCurrencyGainLossReport(params: {
  companyId: string;
  startDate?: string;
  endDate?: string;
  currency?: string;
  reportType: string;
}) {
  try {
    let query = db("currency_revaluations")
      .select(
        "currency_revaluations.*",
        "accounts.name as account_name",
        "accounts.code as account_code"
      )
      .leftJoin("accounts", "currency_revaluations.account_id", "accounts.id")
      .where("currency_revaluations.company_id", params.companyId);

    if (params.startDate) {
      query = query.where(
        "currency_revaluations.revaluation_date",
        ">=",
        params.startDate
      );
    }
    if (params.endDate) {
      query = query.where(
        "currency_revaluations.revaluation_date",
        "<=",
        params.endDate
      );
    }
    if (params.currency) {
      query = query.where("currency_revaluations.currency", params.currency);
    }

    const revaluations = await query.orderBy(
      "currency_revaluations.revaluation_date",
      "desc"
    );

    // Calculate summary statistics
    const summary = {
      totalGains: revaluations
        .filter((r) => r.revaluation_gain_loss > 0)
        .reduce((sum, r) => sum + parseFloat(r.revaluation_gain_loss), 0),
      totalLosses: revaluations
        .filter((r) => r.revaluation_gain_loss < 0)
        .reduce(
          (sum, r) => sum + Math.abs(parseFloat(r.revaluation_gain_loss)),
          0
        ),
      netGainLoss: revaluations.reduce(
        (sum, r) => sum + parseFloat(r.revaluation_gain_loss),
        0
      ),
      currenciesAffected: [...new Set(revaluations.map((r) => r.currency))],
      accountsAffected: [...new Set(revaluations.map((r) => r.account_id))]
        .length,
    };

    if (params.reportType === "summary") {
      return {
        summary,
        reportPeriod: {
          startDate: params.startDate,
          endDate: params.endDate,
        },
      };
    } else {
      return {
        summary,
        details: revaluations,
        reportPeriod: {
          startDate: params.startDate,
          endDate: params.endDate,
        },
      };
    }
  } catch (error) {
    console.error("Error generating currency gain/loss report:", error);
    throw error;
  }
}

// Helper function to update exchange rates from external API
async function updateExchangeRatesFromAPI(params: {
  companyId: string;
  provider: string;
  baseCurrency?: string;
  userId: string;
}) {
  try {
    // Get company currencies
    const currencies = await db("currencies")
      .where("company_id", params.companyId)
      .where("is_active", true);

    const baseCurrency =
      params.baseCurrency ||
      currencies.find((c) => c.is_base_currency)?.code ||
      "USD";

    const updatedRates = [];
    const errors = [];

    // Mock API call - in production, integrate with real exchange rate API
    for (const currency of currencies) {
      if (currency.code === baseCurrency) continue;

      try {
        // Simulate API call with mock rates
        const mockRate = generateMockExchangeRate(baseCurrency, currency.code);

        const rateId = uuidv4();
        await db("exchange_rates").insert({
          id: rateId,
          company_id: params.companyId,
          from_currency: baseCurrency,
          to_currency: currency.code,
          rate: mockRate,
          effective_date: new Date().toISOString().split("T")[0],
          source: "API",
          provider: params.provider,
          created_by: params.userId,
        });

        // Create inverse rate
        await db("exchange_rates").insert({
          id: uuidv4(),
          company_id: params.companyId,
          from_currency: currency.code,
          to_currency: baseCurrency,
          rate: 1 / mockRate,
          effective_date: new Date().toISOString().split("T")[0],
          source: "API",
          provider: params.provider,
          created_by: params.userId,
        });

        updatedRates.push({
          fromCurrency: baseCurrency,
          toCurrency: currency.code,
          rate: mockRate,
        });
      } catch (error) {
        errors.push({
          currency: currency.code,
          error: error.message,
        });
      }
    }

    return {
      provider: params.provider,
      baseCurrency,
      updatedRates: updatedRates.length,
      rates: updatedRates,
      errors,
    };
  } catch (error) {
    console.error("Error updating exchange rates from API:", error);
    throw error;
  }
}

// Helper function to generate mock exchange rates (replace with real API in production)
function generateMockExchangeRate(
  fromCurrency: string,
  toCurrency: string
): number {
  // Mock exchange rates - in production, fetch from real API
  const mockRates: { [key: string]: number } = {
    "USD-EUR": 0.85,
    "USD-GBP": 0.73,
    "USD-TZS": 2300,
    "USD-KES": 110,
    "USD-UGX": 3700,
    "USD-RWF": 1050,
    "USD-ZAR": 18.5,
    "USD-JPY": 110,
    "USD-CNY": 6.4,
    "USD-INR": 74,
  };

  const key = `${fromCurrency}-${toCurrency}`;
  return mockRates[key] || 1;
}

export default router;
