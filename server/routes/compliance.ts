import express from "express";
import type { Request, Response } from "express";
import { z } from "zod";
import {
  authenticateToken,
  requireCompanyAccess,
  requirePermission,
} from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { db } from "../config/database";
import { v4 as uuidv4 } from "uuid";

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const complianceReportSchema = z.object({
  companyId: z.string().uuid(),
  reportType: z.enum([
    "VAT_RETURN",
    "INCOME_TAX",
    "PAYROLL_TAX",
    "WITHHOLDING_TAX",
    "AUDIT_TRAIL",
    "FINANCIAL_STATEMENTS",
  ]),
  period: z.object({
    startDate: z.string(),
    endDate: z.string(),
    fiscalYear: z.string().optional(),
  }),
  jurisdiction: z.string().default("TZ"), // Tanzania by default
  parameters: z.record(z.any()).optional(),
});

// GET /api/compliance/:companyId/reports - Get available compliance reports
router.get(
  "/:companyId/reports",
  requireCompanyAccess,
  requirePermission("compliance:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { jurisdiction = "TZ" } = req.query;

    try {
      const availableReports = getAvailableComplianceReports(
        jurisdiction as string
      );

      res.json({
        success: true,
        data: {
          jurisdiction,
          reports: availableReports,
        },
      });
    } catch (error) {
      console.error("Failed to get compliance reports:", error);
      res.status(500).json({ error: "Failed to get compliance reports" });
    }
  })
);

// POST /api/compliance/generate - Generate compliance report
router.post(
  "/generate",
  requirePermission("compliance:create"),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const validatedData = complianceReportSchema.parse(req.body);

      // Generate the compliance report based on type
      const reportData = await generateComplianceReport(validatedData);

      // Store the report for audit purposes
      const reportId = uuidv4();
      await db("compliance_reports").insert({
        id: reportId,
        company_id: validatedData.companyId,
        report_type: validatedData.reportType,
        jurisdiction: validatedData.jurisdiction,
        period_start: validatedData.period.startDate,
        period_end: validatedData.period.endDate,
        fiscal_year: validatedData.period.fiscalYear,
        report_data: JSON.stringify(reportData),
        generated_by: (req as any).user?.id,
        generated_at: new Date(),
        status: "GENERATED",
      });

      res.json({
        success: true,
        data: {
          reportId,
          reportType: validatedData.reportType,
          jurisdiction: validatedData.jurisdiction,
          period: validatedData.period,
          ...reportData,
        },
        message: "Compliance report generated successfully",
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: "Validation failed",
          details: error.errors,
        });
      }

      console.error("Failed to generate compliance report:", error);
      res.status(500).json({ error: "Failed to generate compliance report" });
    }
  })
);

// GET /api/compliance/:companyId/vat-return - Generate VAT return (Tanzania)
router.get(
  "/:companyId/vat-return",
  requireCompanyAccess,
  requirePermission("compliance:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { startDate, endDate, quarter } = req.query;

    try {
      const vatReturn = await generateTanzaniaVATReturn({
        companyId,
        startDate: startDate as string,
        endDate: endDate as string,
        quarter: quarter as string,
      });

      res.json({
        success: true,
        data: vatReturn,
      });
    } catch (error) {
      console.error("Failed to generate VAT return:", error);
      res.status(500).json({ error: "Failed to generate VAT return" });
    }
  })
);

// GET /api/compliance/:companyId/withholding-tax - Generate withholding tax report
router.get(
  "/:companyId/withholding-tax",
  requireCompanyAccess,
  requirePermission("compliance:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { startDate, endDate, month } = req.query;

    try {
      const withholdingTax = await generateWithholdingTaxReport({
        companyId,
        startDate: startDate as string,
        endDate: endDate as string,
        month: month as string,
      });

      res.json({
        success: true,
        data: withholdingTax,
      });
    } catch (error) {
      console.error("Failed to generate withholding tax report:", error);
      res
        .status(500)
        .json({ error: "Failed to generate withholding tax report" });
    }
  })
);

// GET /api/compliance/:companyId/audit-trail - Generate audit trail report
router.get(
  "/:companyId/audit-trail",
  requireCompanyAccess,
  requirePermission("compliance:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { startDate, endDate, userId, transactionType } = req.query;

    try {
      const auditTrail = await generateAuditTrailReport({
        companyId,
        startDate: startDate as string,
        endDate: endDate as string,
        userId: userId as string,
        transactionType: transactionType as string,
      });

      res.json({
        success: true,
        data: auditTrail,
      });
    } catch (error) {
      console.error("Failed to generate audit trail:", error);
      res.status(500).json({ error: "Failed to generate audit trail" });
    }
  })
);

// GET /api/compliance/:companyId/financial-statements - Generate regulatory financial statements
router.get(
  "/:companyId/financial-statements",
  requireCompanyAccess,
  requirePermission("compliance:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { fiscalYear, format = "IFRS" } = req.query;

    try {
      const financialStatements = await generateRegulatoryFinancialStatements({
        companyId,
        fiscalYear: fiscalYear as string,
        format: format as string,
      });

      res.json({
        success: true,
        data: financialStatements,
      });
    } catch (error) {
      console.error("Failed to generate financial statements:", error);
      res
        .status(500)
        .json({ error: "Failed to generate financial statements" });
    }
  })
);

// POST /api/compliance/:companyId/submit - Submit compliance report to authorities
router.post(
  "/:companyId/submit",
  requireCompanyAccess,
  requirePermission("compliance:submit"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { reportId, submissionMethod = "ELECTRONIC" } = req.body;

    try {
      // Get the report
      const report = await db("compliance_reports")
        .where("id", reportId)
        .where("company_id", companyId)
        .first();

      if (!report) {
        return res.status(404).json({ error: "Compliance report not found" });
      }

      // Submit the report (mock implementation - in production, integrate with government APIs)
      const submissionResult = await submitComplianceReport({
        report,
        submissionMethod,
        submittedBy: (req as any).user?.id,
      });

      // Update report status
      await db("compliance_reports")
        .where("id", reportId)
        .update({
          status: "SUBMITTED",
          submitted_at: new Date(),
          submitted_by: (req as any).user?.id,
          submission_reference: submissionResult.referenceNumber,
        });

      res.json({
        success: true,
        data: submissionResult,
        message: "Compliance report submitted successfully",
      });
    } catch (error) {
      console.error("Failed to submit compliance report:", error);
      res.status(500).json({ error: "Failed to submit compliance report" });
    }
  })
);

// GET /api/compliance/:companyId/history - Get compliance report history
router.get(
  "/:companyId/history",
  requireCompanyAccess,
  requirePermission("compliance:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { reportType, status, page = 1, limit = 20 } = req.query;

    try {
      let query = db("compliance_reports")
        .select(
          "compliance_reports.*",
          "users.name as generated_by_name",
          "submitted_users.name as submitted_by_name"
        )
        .leftJoin("users", "compliance_reports.generated_by", "users.id")
        .leftJoin(
          "users as submitted_users",
          "compliance_reports.submitted_by",
          "submitted_users.id"
        )
        .where("compliance_reports.company_id", companyId);

      if (reportType) {
        query = query.where("compliance_reports.report_type", reportType);
      }

      if (status) {
        query = query.where("compliance_reports.status", status);
      }

      // Get total count
      const totalResult = await query.clone().count("* as count").first();
      const total = parseInt(totalResult?.count as string) || 0;

      // Get paginated results
      const offset = (parseInt(page as string) - 1) * parseInt(limit as string);
      const reports = await query
        .orderBy("compliance_reports.generated_at", "desc")
        .limit(parseInt(limit as string))
        .offset(offset);

      res.json({
        success: true,
        data: reports,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total,
          totalPages: Math.ceil(total / parseInt(limit as string)),
        },
      });
    } catch (error) {
      console.error("Failed to get compliance history:", error);
      res.status(500).json({ error: "Failed to get compliance history" });
    }
  })
);

// Helper function to get available compliance reports by jurisdiction
function getAvailableComplianceReports(jurisdiction: string) {
  const reports = {
    TZ: [
      // Tanzania
      {
        type: "VAT_RETURN",
        name: "VAT Return (VRN)",
        description: "Quarterly VAT return filing",
        frequency: "QUARTERLY",
        dueDate: "20th of the month following the quarter",
        authority: "Tanzania Revenue Authority (TRA)",
      },
      {
        type: "WITHHOLDING_TAX",
        name: "Withholding Tax Return",
        description: "Monthly withholding tax return",
        frequency: "MONTHLY",
        dueDate: "7th of the following month",
        authority: "Tanzania Revenue Authority (TRA)",
      },
      {
        type: "INCOME_TAX",
        name: "Corporate Income Tax",
        description: "Annual corporate income tax return",
        frequency: "ANNUALLY",
        dueDate: "6 months after year end",
        authority: "Tanzania Revenue Authority (TRA)",
      },
      {
        type: "PAYROLL_TAX",
        name: "PAYE and SDL Returns",
        description: "Monthly PAYE and Skills Development Levy",
        frequency: "MONTHLY",
        dueDate: "7th of the following month",
        authority: "Tanzania Revenue Authority (TRA)",
      },
      {
        type: "AUDIT_TRAIL",
        name: "Audit Trail Report",
        description: "Complete transaction audit trail for compliance",
        frequency: "ON_DEMAND",
        authority: "Tanzania Revenue Authority (TRA)",
      },
      {
        type: "FINANCIAL_STATEMENTS",
        name: "Regulatory Financial Statements",
        description: "Annual financial statements for regulatory filing",
        frequency: "ANNUALLY",
        dueDate: "6 months after year end",
        authority: "Business Registrations and Licensing Agency (BRELA)",
      },
    ],
    KE: [
      // Kenya
      {
        type: "VAT_RETURN",
        name: "VAT Return",
        description: "Monthly VAT return filing",
        frequency: "MONTHLY",
        authority: "Kenya Revenue Authority (KRA)",
      },
    ],
    UG: [
      // Uganda
      {
        type: "VAT_RETURN",
        name: "VAT Return",
        description: "Monthly VAT return filing",
        frequency: "MONTHLY",
        authority: "Uganda Revenue Authority (URA)",
      },
    ],
  };

  return reports[jurisdiction as keyof typeof reports] || [];
}

// Helper function to generate compliance reports
async function generateComplianceReport(params: any) {
  switch (params.reportType) {
    case "VAT_RETURN":
      return await generateTanzaniaVATReturn(params);
    case "WITHHOLDING_TAX":
      return await generateWithholdingTaxReport(params);
    case "AUDIT_TRAIL":
      return await generateAuditTrailReport(params);
    case "FINANCIAL_STATEMENTS":
      return await generateRegulatoryFinancialStatements(params);
    default:
      throw new Error(`Unsupported report type: ${params.reportType}`);
  }
}

// Tanzania VAT Return generation
async function generateTanzaniaVATReturn(params: {
  companyId: string;
  startDate: string;
  endDate: string;
  quarter?: string;
}) {
  try {
    // Get company VAT registration details
    const company = await db("companies")
      .select("name", "vat_number", "address", "phone", "email")
      .where("id", params.companyId)
      .first();

    // Calculate VAT on sales (output VAT)
    const outputVAT = await db.raw(
      `
      SELECT
        COALESCE(SUM(te.credit_amount * (tr.rate / 100)), 0) as output_vat,
        COALESCE(SUM(te.credit_amount), 0) as taxable_sales
      FROM transactions t
      JOIN transaction_entries te ON t.id = te.transaction_id
      JOIN accounts a ON te.account_id = a.id
      LEFT JOIN tax_rates tr ON t.tax_rate_id = tr.id
      WHERE t.company_id = ?
        AND t.transaction_date BETWEEN ? AND ?
        AND t.status = 'POSTED'
        AND a.type = 'REVENUE'
        AND tr.tax_type = 'VAT'
    `,
      [params.companyId, params.startDate, params.endDate]
    );

    // Calculate VAT on purchases (input VAT)
    const inputVAT = await db.raw(
      `
      SELECT
        COALESCE(SUM(te.debit_amount * (tr.rate / 100)), 0) as input_vat,
        COALESCE(SUM(te.debit_amount), 0) as taxable_purchases
      FROM transactions t
      JOIN transaction_entries te ON t.id = te.transaction_id
      JOIN accounts a ON te.account_id = a.id
      LEFT JOIN tax_rates tr ON t.tax_rate_id = tr.id
      WHERE t.company_id = ?
        AND t.transaction_date BETWEEN ? AND ?
        AND t.status = 'POSTED'
        AND a.type = 'EXPENSE'
        AND tr.tax_type = 'VAT'
    `,
      [params.companyId, params.startDate, params.endDate]
    );

    const outputData = outputVAT.rows?.[0] || outputVAT[0] || {};
    const inputData = inputVAT.rows?.[0] || inputVAT[0] || {};

    const netVAT =
      parseFloat(outputData.output_vat || 0) -
      parseFloat(inputData.input_vat || 0);

    return {
      reportType: "VAT_RETURN",
      jurisdiction: "TZ",
      company: {
        name: company.name,
        vatNumber: company.vat_number,
        address: company.address,
        phone: company.phone,
        email: company.email,
      },
      period: {
        startDate: params.startDate,
        endDate: params.endDate,
        quarter: params.quarter,
      },
      vatCalculation: {
        taxableSales: parseFloat(outputData.taxable_sales || 0),
        outputVAT: parseFloat(outputData.output_vat || 0),
        taxablePurchases: parseFloat(inputData.taxable_purchases || 0),
        inputVAT: parseFloat(inputData.input_vat || 0),
        netVAT: netVAT,
        vatPayable: Math.max(netVAT, 0),
        vatRefundable: Math.max(-netVAT, 0),
      },
      generatedAt: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error generating Tanzania VAT return:", error);
    throw error;
  }
}

// Withholding Tax Report generation
async function generateWithholdingTaxReport(params: {
  companyId: string;
  startDate: string;
  endDate: string;
  month?: string;
}) {
  try {
    // Get withholding tax transactions
    const withholdingTax = await db.raw(
      `
      SELECT
        t.description,
        t.transaction_date,
        te.debit_amount as payment_amount,
        tr.rate as withholding_rate,
        (te.debit_amount * tr.rate / 100) as withholding_amount,
        tr.name as tax_type
      FROM transactions t
      JOIN transaction_entries te ON t.id = te.transaction_id
      JOIN accounts a ON te.account_id = a.id
      LEFT JOIN tax_rates tr ON t.tax_rate_id = tr.id
      WHERE t.company_id = ?
        AND t.transaction_date BETWEEN ? AND ?
        AND t.status = 'POSTED'
        AND tr.tax_type = 'WITHHOLDING'
      ORDER BY t.transaction_date
    `,
      [params.companyId, params.startDate, params.endDate]
    );

    const transactions = withholdingTax.rows || withholdingTax;
    const totalWithholding = transactions.reduce(
      (sum: number, tx: any) => sum + parseFloat(tx.withholding_amount || 0),
      0
    );

    return {
      reportType: "WITHHOLDING_TAX",
      jurisdiction: "TZ",
      period: {
        startDate: params.startDate,
        endDate: params.endDate,
        month: params.month,
      },
      summary: {
        totalTransactions: transactions.length,
        totalPayments: transactions.reduce(
          (sum: number, tx: any) => sum + parseFloat(tx.payment_amount || 0),
          0
        ),
        totalWithholding: totalWithholding,
      },
      transactions: transactions,
      generatedAt: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error generating withholding tax report:", error);
    throw error;
  }
}

// Audit Trail Report generation
async function generateAuditTrailReport(params: {
  companyId: string;
  startDate: string;
  endDate: string;
  userId?: string;
  transactionType?: string;
}) {
  try {
    let query = db("audit_logs")
      .select(
        "audit_logs.*",
        "users.name as user_name",
        "users.email as user_email"
      )
      .leftJoin("users", "audit_logs.user_id", "users.id")
      .where("audit_logs.company_id", params.companyId)
      .whereBetween("audit_logs.created_at", [
        params.startDate,
        params.endDate,
      ]);

    if (params.userId) {
      query = query.where("audit_logs.user_id", params.userId);
    }

    if (params.transactionType) {
      query = query.where("audit_logs.action", params.transactionType);
    }

    const auditEntries = await query.orderBy("audit_logs.created_at", "desc");

    return {
      reportType: "AUDIT_TRAIL",
      period: {
        startDate: params.startDate,
        endDate: params.endDate,
      },
      filters: {
        userId: params.userId,
        transactionType: params.transactionType,
      },
      summary: {
        totalEntries: auditEntries.length,
        uniqueUsers: [...new Set(auditEntries.map((e) => e.user_id))].length,
        actionTypes: [...new Set(auditEntries.map((e) => e.action))],
      },
      entries: auditEntries,
      generatedAt: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error generating audit trail report:", error);
    throw error;
  }
}

// Regulatory Financial Statements generation
async function generateRegulatoryFinancialStatements(params: {
  companyId: string;
  fiscalYear: string;
  format?: string;
}) {
  try {
    const startDate = `${params.fiscalYear}-01-01`;
    const endDate = `${params.fiscalYear}-12-31`;

    // Generate standard financial statements
    const balanceSheet = await generateBalanceSheet(params.companyId, endDate);
    const incomeStatement = await generateIncomeStatement(
      params.companyId,
      startDate,
      endDate
    );
    const cashFlow = await generateCashFlowStatement(
      params.companyId,
      startDate,
      endDate
    );

    // Add regulatory notes and disclosures
    const regulatoryNotes = await generateRegulatoryNotes(
      params.companyId,
      params.fiscalYear
    );

    return {
      reportType: "FINANCIAL_STATEMENTS",
      format: params.format || "IFRS",
      fiscalYear: params.fiscalYear,
      period: { startDate, endDate },
      statements: {
        balanceSheet,
        incomeStatement,
        cashFlow,
      },
      regulatoryNotes,
      compliance: {
        ifrsCompliant: true,
        auditRequired: true,
        filingDeadline: `${parseInt(params.fiscalYear) + 1}-06-30`,
      },
      generatedAt: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error generating regulatory financial statements:", error);
    throw error;
  }
}

// Mock submission function (integrate with government APIs in production)
async function submitComplianceReport(params: {
  report: any;
  submissionMethod: string;
  submittedBy: string;
}) {
  // Mock submission - in production, integrate with TRA, BRELA, etc. APIs
  const referenceNumber = `REF-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}`;

  return {
    success: true,
    referenceNumber,
    submissionMethod: params.submissionMethod,
    submittedAt: new Date().toISOString(),
    status: "SUBMITTED",
    acknowledgment: "Report submitted successfully to regulatory authority",
  };
}

// Helper functions for financial statements (simplified versions)
async function generateBalanceSheet(companyId: string, asOfDate: string) {
  // Simplified balance sheet generation
  return {
    asOfDate,
    assets: { current: 0, nonCurrent: 0, total: 0 },
    liabilities: { current: 0, nonCurrent: 0, total: 0 },
    equity: { total: 0 },
  };
}

async function generateIncomeStatement(
  companyId: string,
  startDate: string,
  endDate: string
) {
  // Simplified income statement generation
  return {
    period: { startDate, endDate },
    revenue: 0,
    expenses: 0,
    netIncome: 0,
  };
}

async function generateCashFlowStatement(
  companyId: string,
  startDate: string,
  endDate: string
) {
  // Simplified cash flow statement generation
  return {
    period: { startDate, endDate },
    operating: 0,
    investing: 0,
    financing: 0,
    netCashFlow: 0,
  };
}

async function generateRegulatoryNotes(companyId: string, fiscalYear: string) {
  return [
    "Accounting policies are in accordance with International Financial Reporting Standards (IFRS)",
    "All amounts are presented in Tanzanian Shillings (TZS)",
    "The financial statements have been prepared on a going concern basis",
  ];
}

export default router;
