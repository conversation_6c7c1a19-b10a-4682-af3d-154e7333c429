import express from "express";
import { advancedPermissionService } from "../services/advancedPermissionService";
import { requireAuth } from "../middleware/auth";
import { requireCompanyAccess } from "../middleware/companyAccess";
import { asyncHandler } from "../middleware/asyncHandler";

const router = express.Router();

// Middleware to check admin permissions
const requireAdminPermission = async (req: any, res: any, next: any) => {
  const permission = await advancedPermissionService.checkPermission({
    userId: req.user.id,
    resource: "permissions",
    action: "admin",
    context: {
      ipAddress: req.ip,
      timestamp: new Date().toISOString(),
      sessionId: req.sessionID,
    },
  });

  if (!permission.allowed) {
    return res.status(403).json({ error: "Admin permissions required" });
  }

  next();
};

// GET /api/permissions/:companyId/check
router.post(
  "/:companyId/check",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req, res) => {
    const { resource, action, field, resourceId } = req.body;

    const result = await advancedPermissionService.checkPermission({
      userId: req.user.id,
      resource,
      action,
      field,
      resourceId,
      context: {
        ipAddress: req.ip,
        timestamp: new Date().toISOString(),
        sessionId: req.sessionID,
      },
    });

    res.json(result);
  })
);

// GET /api/permissions/:companyId/field-permissions/:roleId
router.get(
  "/:companyId/field-permissions/:roleId",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { roleId } = req.params;
    const { resource } = req.query;

    const permissions = await advancedPermissionService.getFieldPermissions(
      roleId,
      resource as string
    );

    res.json(permissions);
  })
);

// POST /api/permissions/:companyId/field-permissions
router.post(
  "/:companyId/field-permissions",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { roleId, resource, field, permission, conditions } = req.body;

    await advancedPermissionService.setFieldPermission(
      roleId,
      resource,
      field,
      permission,
      conditions
    );

    res.json({ success: true });
  })
);

// GET /api/permissions/:companyId/time-based-access
router.get(
  "/:companyId/time-based-access",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;
    const { userId, roleId } = req.query;

    const restrictions = await advancedPermissionService.getTimeBasedAccess({
      companyId,
      userId: userId as string,
      roleId: roleId as string,
    });

    res.json(restrictions);
  })
);

// POST /api/permissions/:companyId/time-based-access
router.post(
  "/:companyId/time-based-access",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const data = req.body;

    await advancedPermissionService.createTimeBasedAccess({
      ...data,
      createdBy: req.user.id,
    });

    res.status(201).json({ success: true });
  })
);

// GET /api/permissions/:companyId/ip-restrictions
router.get(
  "/:companyId/ip-restrictions",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;
    const { userId, roleId } = req.query;

    const restrictions = await advancedPermissionService.getIPRestrictions({
      companyId,
      userId: userId as string,
      roleId: roleId as string,
    });

    res.json(restrictions);
  })
);

// POST /api/permissions/:companyId/ip-restrictions
router.post(
  "/:companyId/ip-restrictions",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;
    const data = req.body;

    await advancedPermissionService.createIPRestriction({
      ...data,
      companyId,
      createdBy: req.user.id,
    });

    res.status(201).json({ success: true });
  })
);

// DELETE /api/permissions/:companyId/ip-restrictions/:restrictionId
router.delete(
  "/:companyId/ip-restrictions/:restrictionId",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { restrictionId } = req.params;

    await advancedPermissionService.deleteIPRestriction(restrictionId);

    res.json({ success: true });
  })
);

// GET /api/permissions/:companyId/audit-logs
router.get(
  "/:companyId/audit-logs",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;
    const {
      userId,
      resource,
      action,
      success,
      dateFrom,
      dateTo,
      page = 1,
      limit = 50,
    } = req.query;

    const logs = await advancedPermissionService.getAuditLogs(companyId, {
      userId: userId as string,
      resource: resource as string,
      action: action as string,
      success:
        success === "true" ? true : success === "false" ? false : undefined,
      dateFrom: dateFrom as string,
      dateTo: dateTo as string,
      limit: parseInt(limit as string),
      offset: (parseInt(page as string) - 1) * parseInt(limit as string),
    });

    res.json(logs);
  })
);

// GET /api/permissions/:companyId/security-policy
router.get(
  "/:companyId/security-policy",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;

    const policy = await advancedPermissionService.getSecurityPolicy(companyId);

    res.json(policy);
  })
);

// PUT /api/permissions/:companyId/security-policy
router.put(
  "/:companyId/security-policy",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;
    const policyData = req.body;

    await advancedPermissionService.updateSecurityPolicy(companyId, {
      ...policyData,
      updatedBy: req.user.id,
    });

    res.json({ success: true });
  })
);

// GET /api/permissions/:companyId/sessions
router.get(
  "/:companyId/sessions",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;
    const { userId } = req.query;

    const sessions = await advancedPermissionService.getActiveSessions({
      companyId,
      userId: userId as string,
    });

    res.json(sessions);
  })
);

// DELETE /api/permissions/:companyId/sessions/:sessionId
router.delete(
  "/:companyId/sessions/:sessionId",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params;

    await advancedPermissionService.terminateSession(sessionId);

    res.json({ success: true });
  })
);

// POST /api/permissions/:companyId/bulk-permissions
router.post(
  "/:companyId/bulk-permissions",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { roleId, permissions } = req.body;

    await advancedPermissionService.setBulkFieldPermissions(
      roleId,
      permissions
    );

    res.json({ success: true });
  })
);

// GET /api/permissions/matrix/:companyId - Get permission matrix for company
router.get(
  "/matrix/:companyId",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;

    try {
      const matrix = await generatePermissionMatrix(companyId);

      res.json({
        success: true,
        data: matrix,
      });
    } catch (error) {
      console.error("Failed to get permission matrix:", error);
      res.status(500).json({ error: "Failed to get permission matrix" });
    }
  })
);

// POST /api/permissions/matrix/:companyId/bulk-update - Bulk update permissions
router.post(
  "/matrix/:companyId/bulk-update",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;
    const { updates } = req.body;

    try {
      const result = await bulkUpdatePermissions(
        companyId,
        updates,
        req.user.id
      );

      res.json({
        success: true,
        data: result,
        message: "Permissions updated successfully",
      });
    } catch (error) {
      console.error("Failed to bulk update permissions:", error);
      res.status(500).json({ error: "Failed to update permissions" });
    }
  })
);

// GET /api/permissions/audit/:companyId - Get permission audit log
router.get(
  "/audit/:companyId",
  requireAuth,
  requireCompanyAccess,
  requireAdminPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;
    const {
      startDate,
      endDate,
      userId,
      action,
      page = 1,
      limit = 50,
    } = req.query;

    try {
      const auditLog = await getPermissionAuditLog({
        companyId,
        startDate: startDate as string,
        endDate: endDate as string,
        userId: userId as string,
        action: action as string,
        page: parseInt(page as string),
        limit: parseInt(limit as string),
      });

      res.json({
        success: true,
        data: auditLog,
      });
    } catch (error) {
      console.error("Failed to get permission audit log:", error);
      res.status(500).json({ error: "Failed to get audit log" });
    }
  })
);

// Helper function to generate permission matrix
async function generatePermissionMatrix(companyId: string) {
  try {
    // Get all users in the company
    const users = await db("users")
      .select("users.id", "users.name", "users.email")
      .join("company_users", "users.id", "company_users.user_id")
      .where("company_users.company_id", companyId)
      .where("users.is_active", true);

    // Get all roles in the company
    const roles = await db("roles")
      .where("company_id", companyId)
      .orderBy("priority", "desc");

    // Get all available permissions
    const availablePermissions = await getAvailablePermissions();

    // Build matrix
    const matrix = {
      users: [],
      roles: [],
      permissions: availablePermissions,
    };

    // Get user permissions
    for (const user of users) {
      const userRoles = await db("user_roles")
        .select("roles.id", "roles.name", "roles.priority")
        .join("roles", "user_roles.role_id", "roles.id")
        .where("user_roles.user_id", user.id)
        .where("roles.company_id", companyId)
        .orderBy("roles.priority", "desc");

      const userPermissions = await getUserPermissions(user.id, companyId);

      matrix.users.push({
        ...user,
        roles: userRoles,
        permissions: userPermissions,
        effectivePermissions: calculateEffectivePermissions(
          userPermissions,
          availablePermissions
        ),
      });
    }

    // Get role permissions
    for (const role of roles) {
      const rolePermissions = await db("role_permissions")
        .select("permission")
        .where("role_id", role.id);

      matrix.roles.push({
        ...role,
        permissions: rolePermissions.map((p) => p.permission),
        userCount: await db("user_roles")
          .where("role_id", role.id)
          .count("* as count")
          .first(),
      });
    }

    return matrix;
  } catch (error) {
    console.error("Error generating permission matrix:", error);
    throw error;
  }
}

// Helper function to bulk update permissions
async function bulkUpdatePermissions(
  companyId: string,
  updates: any[],
  updatedBy: string
) {
  try {
    const results = {
      successful: 0,
      failed: 0,
      errors: [],
    };

    await db.transaction(async (trx) => {
      for (const update of updates) {
        try {
          const { type, targetId, permissions, action } = update;

          if (type === "role") {
            if (action === "replace") {
              // Replace all permissions for role
              await trx("role_permissions").where("role_id", targetId).del();

              if (permissions.length > 0) {
                const rolePermissions = permissions.map(
                  (permission: string) => ({
                    id: require("uuid").v4(),
                    role_id: targetId,
                    permission: permission,
                  })
                );
                await trx("role_permissions").insert(rolePermissions);
              }
            } else if (action === "add") {
              // Add permissions to role
              const existingPermissions = await trx("role_permissions")
                .select("permission")
                .where("role_id", targetId);

              const existingPerms = existingPermissions.map(
                (p) => p.permission
              );
              const newPermissions = permissions.filter(
                (p: string) => !existingPerms.includes(p)
              );

              if (newPermissions.length > 0) {
                const rolePermissions = newPermissions.map(
                  (permission: string) => ({
                    id: require("uuid").v4(),
                    role_id: targetId,
                    permission: permission,
                  })
                );
                await trx("role_permissions").insert(rolePermissions);
              }
            } else if (action === "remove") {
              // Remove permissions from role
              await trx("role_permissions")
                .where("role_id", targetId)
                .whereIn("permission", permissions)
                .del();
            }
          } else if (type === "user") {
            if (action === "assign_roles") {
              // Assign roles to user
              await trx("user_roles")
                .where("user_id", targetId)
                .whereIn("role_id", function () {
                  this.select("id")
                    .from("roles")
                    .where("company_id", companyId);
                })
                .del();

              if (permissions.length > 0) {
                const userRoles = permissions.map((roleId: string) => ({
                  id: require("uuid").v4(),
                  user_id: targetId,
                  role_id: roleId,
                  assigned_by: updatedBy,
                  assigned_at: new Date(),
                }));
                await trx("user_roles").insert(userRoles);
              }
            }
          }

          // Log the permission change
          await trx("permission_audit_log").insert({
            id: require("uuid").v4(),
            company_id: companyId,
            target_type: type,
            target_id: targetId,
            action: action,
            permissions: JSON.stringify(permissions),
            changed_by: updatedBy,
            changed_at: new Date(),
          });

          results.successful++;
        } catch (error) {
          results.failed++;
          results.errors.push({
            update,
            error: error.message,
          });
        }
      }
    });

    return results;
  } catch (error) {
    console.error("Error in bulk update permissions:", error);
    throw error;
  }
}

// Helper function to get available permissions
async function getAvailablePermissions() {
  return [
    // Financial Management
    {
      key: "transactions:read",
      category: "Financial",
      description: "View transactions",
    },
    {
      key: "transactions:create",
      category: "Financial",
      description: "Create transactions",
    },
    {
      key: "transactions:update",
      category: "Financial",
      description: "Update transactions",
    },
    {
      key: "transactions:delete",
      category: "Financial",
      description: "Delete transactions",
    },
    {
      key: "transactions:approve",
      category: "Financial",
      description: "Approve transactions",
    },
    {
      key: "transactions:reverse",
      category: "Financial",
      description: "Reverse transactions",
    },

    // Accounts Management
    {
      key: "accounts:read",
      category: "Accounts",
      description: "View chart of accounts",
    },
    {
      key: "accounts:create",
      category: "Accounts",
      description: "Create accounts",
    },
    {
      key: "accounts:update",
      category: "Accounts",
      description: "Update accounts",
    },
    {
      key: "accounts:delete",
      category: "Accounts",
      description: "Delete accounts",
    },

    // Invoicing
    {
      key: "invoices:read",
      category: "Invoicing",
      description: "View invoices",
    },
    {
      key: "invoices:create",
      category: "Invoicing",
      description: "Create invoices",
    },
    {
      key: "invoices:update",
      category: "Invoicing",
      description: "Update invoices",
    },
    {
      key: "invoices:delete",
      category: "Invoicing",
      description: "Delete invoices",
    },
    {
      key: "invoices:send",
      category: "Invoicing",
      description: "Send invoices",
    },

    // Payments
    {
      key: "payments:read",
      category: "Payments",
      description: "View payments",
    },
    {
      key: "payments:create",
      category: "Payments",
      description: "Process payments",
    },
    {
      key: "payments:refund",
      category: "Payments",
      description: "Process refunds",
    },

    // Reports
    { key: "reports:read", category: "Reports", description: "View reports" },
    {
      key: "reports:create",
      category: "Reports",
      description: "Create custom reports",
    },
    {
      key: "reports:export",
      category: "Reports",
      description: "Export reports",
    },

    // Analytics
    {
      key: "analytics:read",
      category: "Analytics",
      description: "View analytics",
    },
    {
      key: "analytics:create",
      category: "Analytics",
      description: "Create dashboards",
    },

    // Bank Reconciliation
    {
      key: "reconciliation:read",
      category: "Banking",
      description: "View reconciliation",
    },
    {
      key: "reconciliation:create",
      category: "Banking",
      description: "Perform reconciliation",
    },
    {
      key: "reconciliation:approve",
      category: "Banking",
      description: "Approve reconciliation",
    },

    // Budgets
    { key: "budgets:read", category: "Budgets", description: "View budgets" },
    {
      key: "budgets:create",
      category: "Budgets",
      description: "Create budgets",
    },
    {
      key: "budgets:update",
      category: "Budgets",
      description: "Update budgets",
    },
    {
      key: "budgets:approve",
      category: "Budgets",
      description: "Approve budgets",
    },

    // Tax Management
    { key: "tax:read", category: "Tax", description: "View tax information" },
    { key: "tax:create", category: "Tax", description: "Create tax rates" },
    { key: "tax:update", category: "Tax", description: "Update tax settings" },

    // Currencies
    {
      key: "currencies:read",
      category: "Currencies",
      description: "View currencies",
    },
    {
      key: "currencies:create",
      category: "Currencies",
      description: "Create currencies",
    },
    {
      key: "currencies:update",
      category: "Currencies",
      description: "Update exchange rates",
    },

    // User Management
    { key: "users:read", category: "Users", description: "View users" },
    { key: "users:create", category: "Users", description: "Create users" },
    { key: "users:update", category: "Users", description: "Update users" },
    { key: "users:delete", category: "Users", description: "Delete users" },

    // Permissions
    {
      key: "permissions:read",
      category: "Permissions",
      description: "View permissions",
    },
    {
      key: "permissions:create",
      category: "Permissions",
      description: "Create roles",
    },
    {
      key: "permissions:update",
      category: "Permissions",
      description: "Update permissions",
    },
    {
      key: "permissions:assign",
      category: "Permissions",
      description: "Assign permissions",
    },
    {
      key: "permissions:admin",
      category: "Permissions",
      description: "Full permission admin",
    },

    // Company Settings
    {
      key: "company:read",
      category: "Company",
      description: "View company settings",
    },
    {
      key: "company:update",
      category: "Company",
      description: "Update company settings",
    },
    {
      key: "company:admin",
      category: "Company",
      description: "Full company admin",
    },
  ];
}

// Helper function to get user permissions
async function getUserPermissions(userId: string, companyId: string) {
  const permissions = await db("role_permissions")
    .select("role_permissions.permission")
    .join("user_roles", "role_permissions.role_id", "user_roles.role_id")
    .join("roles", "user_roles.role_id", "roles.id")
    .where("user_roles.user_id", userId)
    .where("roles.company_id", companyId)
    .distinct();

  return permissions.map((p) => p.permission);
}

// Helper function to calculate effective permissions
function calculateEffectivePermissions(
  userPermissions: string[],
  availablePermissions: any[]
) {
  return availablePermissions.map((permission) => ({
    ...permission,
    granted: userPermissions.includes(permission.key),
  }));
}

// Helper function to get permission audit log
async function getPermissionAuditLog(params: {
  companyId: string;
  startDate?: string;
  endDate?: string;
  userId?: string;
  action?: string;
  page: number;
  limit: number;
}) {
  try {
    let query = db("permission_audit_log")
      .select(
        "permission_audit_log.*",
        "users.name as changed_by_name",
        "target_users.name as target_user_name",
        "roles.name as target_role_name"
      )
      .leftJoin("users", "permission_audit_log.changed_by", "users.id")
      .leftJoin("users as target_users", function () {
        this.on("permission_audit_log.target_id", "target_users.id").andOn(
          "permission_audit_log.target_type",
          db.raw("?", ["user"])
        );
      })
      .leftJoin("roles", function () {
        this.on("permission_audit_log.target_id", "roles.id").andOn(
          "permission_audit_log.target_type",
          db.raw("?", ["role"])
        );
      })
      .where("permission_audit_log.company_id", params.companyId);

    // Apply filters
    if (params.startDate) {
      query = query.where(
        "permission_audit_log.changed_at",
        ">=",
        params.startDate
      );
    }
    if (params.endDate) {
      query = query.where(
        "permission_audit_log.changed_at",
        "<=",
        params.endDate
      );
    }
    if (params.userId) {
      query = query.where("permission_audit_log.changed_by", params.userId);
    }
    if (params.action) {
      query = query.where("permission_audit_log.action", params.action);
    }

    // Get total count
    const totalResult = await query.clone().count("* as count").first();
    const total = parseInt(totalResult?.count as string) || 0;

    // Get paginated results
    const offset = (params.page - 1) * params.limit;
    const auditEntries = await query
      .orderBy("permission_audit_log.changed_at", "desc")
      .limit(params.limit)
      .offset(offset);

    return {
      entries: auditEntries,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit),
      },
    };
  } catch (error) {
    console.error("Error getting permission audit log:", error);
    throw error;
  }
}

export default router;
