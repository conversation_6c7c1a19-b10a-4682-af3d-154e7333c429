import express from "express";
import type { Request, Response } from "express";
import {
  authenticateToken,
  requireCompanyAccess,
  // requirePermission,
} from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { db } from "../config/database";
import cron from "node-cron";
import nodemailer from "nodemailer";
import { promisify } from "util";

// Initialize email transporter (configure based on your environment)
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || "smtp.example.com",
  port: Number(process.env.EMAIL_PORT) || 587,
  secure: process.env.EMAIL_SECURE === "true",
  auth: {
    user: process.env.EMAIL_USER || "<EMAIL>",
    pass: process.env.EMAIL_PASS || "your-password",
  },
});

// Promisify sendMail for async/await usage
const sendMail = promisify(transporter.sendMail.bind(transporter));

// Map to store scheduled jobs
const scheduledJobs: Record<string, any> = {};

// Helper function to generate CSV from report data
function generateCsvFromReport(reportData: any): string {
  const { parse } = require("json2csv");
  try {
    if (Array.isArray(reportData) && reportData.length > 0) {
      return parse(reportData);
    } else if (reportData && typeof reportData === "object") {
      // Handle case where reportData is an object with a data property
      const data = reportData.data || reportData;
      if (Array.isArray(data)) {
        return parse(data);
      }
    }
    return "";
  } catch (err) {
    console.error("Error generating CSV:", err);
    return "";
  }
}

// Helper function to generate PDF from report data
async function generatePdfFromReport(reportData: any): Promise<Buffer> {
  const pdfMake = require("pdfmake/build/pdfmake");
  const pdfFonts = require("pdfmake/build/vfs_fonts");
  pdfMake.vfs = pdfFonts.pdfMake.vfs;

  try {
    const docDefinition = {
      content: [
        { text: "Report", style: "header" },
        {
          text: JSON.stringify(reportData, null, 2),
          style: "body",
        },
      ],
      styles: {
        header: {
          fontSize: 18,
          bold: true,
          margin: [0, 0, 0, 10],
        },
        body: {
          fontSize: 12,
          font: "Courier",
        },
      },
    };

    const pdfDoc = pdfMake.createPdf(docDefinition);
    return new Promise((resolve, reject) => {
      pdfDoc.getBuffer((buffer: Buffer) => {
        resolve(buffer);
      });
    });
  } catch (err) {
    console.error("Error generating PDF:", err);
    return Buffer.from("");
  }
}

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Helper function to get account balances
async function getAccountBalances(
  companyId: string,
  asOfDate: string,
  accountType?: string
) {
  let query = db("accounts")
    .select(
      "accounts.*",
      db.raw(
        `
        COALESCE(
          accounts.opening_balance +
          (SELECT
            CASE
              WHEN accounts.account_type IN ('ASSET', 'EXPENSE') THEN
                SUM(te.debit_amount - te.credit_amount)
              ELSE
                SUM(te.credit_amount - te.debit_amount)
            END
           FROM transaction_entries te
           JOIN transactions t ON te.transaction_id = t.id
           WHERE te.account_id = accounts.id
           AND t.transaction_date <= ?
           AND t.status = 'POSTED'),
          accounts.opening_balance
        ) as current_balance
      `,
        [asOfDate]
      )
    )
    .where("accounts.company_id", companyId)
    .where("accounts.is_active", true);

  if (accountType) {
    query = query.where("accounts.account_type", accountType);
  }

  return await query.orderBy("accounts.code");
}

// Helper function to get transaction entries for a period
async function getTransactionEntries(
  companyId: string,
  startDate: string,
  endDate: string,
  accountType?: string
) {
  let query = db("transaction_entries as te")
    .select(
      "te.*",
      "accounts.code as account_code",
      "accounts.name as account_name",
      "accounts.account_type",
      "transactions.transaction_date",
      "transactions.description as transaction_description",
      "transactions.transaction_number"
    )
    .join("accounts", "te.account_id", "accounts.id")
    .join("transactions", "te.transaction_id", "transactions.id")
    .where("accounts.company_id", companyId)
    .where("transactions.transaction_date", ">=", startDate)
    .where("transactions.transaction_date", "<=", endDate)
    .where("transactions.status", "POSTED");

  if (accountType) {
    query = query.where("accounts.account_type", accountType);
  }

  return await query.orderBy("transactions.transaction_date", "desc");
}

// Helper function to generate realistic report data for custom reports
async function generateReportData(report: any, companyId: string) {
  const dataSource = report.data_source;
  const reportType = report.type;
  const config = report.config ? JSON.parse(report.config) : {};

  try {
    switch (dataSource) {
      case "transactions":
        return await generateTransactionsData(companyId, reportType, config);
      case "accounts":
        return await generateAccountsData(companyId, reportType, config);
      case "contacts":
        return await generateContactsData(companyId, reportType, config);
      case "invoices":
        return await generateInvoicesData(companyId, reportType, config);
      case "bank_transactions":
        return await generateBankTransactionsData(
          companyId,
          reportType,
          config
        );
      case "payments":
        return await generatePaymentsData(companyId, reportType, config);
      case "taxes":
        return await generateTaxesData(companyId, reportType, config);
      default:
        return generateSampleData(reportType);
    }
  } catch (error) {
    console.error("Error generating report data:", error);
    return generateSampleData(reportType);
  }
}

async function generateTransactionsData(
  companyId: string,
  reportType: string,
  config: any = {}
) {
  try {
    // Get actual transaction data from the database using the correct schema
    let query = db("transactions")
      .join(
        "transaction_entries",
        "transactions.id",
        "transaction_entries.transaction_id"
      )
      .join("accounts", "transaction_entries.account_id", "accounts.id")
      .where("transactions.company_id", companyId)
      .whereIn("transactions.status", ["APPROVED", "POSTED"])
      .select(
        "transactions.id",
        "transactions.transaction_date as date",
        "transactions.description",
        "transactions.total_amount as amount",
        "transactions.status",
        "accounts.name as account_name",
        "accounts.code as account_code",
        "transaction_entries.debit_amount",
        "transaction_entries.credit_amount"
      );

    // Apply date filters from config if provided
    if (config.startDate) {
      query = query.where(
        "transactions.transaction_date",
        ">=",
        config.startDate
      );
    }
    if (config.endDate) {
      query = query.where(
        "transactions.transaction_date",
        "<=",
        config.endDate
      );
    }
    // Apply account type filter if provided
    if (config.accountType) {
      query = query.where("accounts.account_type", config.accountType);
    }
    // Apply limit from config or default to 100
    const limit = config.limit || 100;
    query = query.limit(limit).orderBy("transactions.transaction_date", "desc");

    const transactions = await query;

    if (reportType === "SUMMARY") {
      const totalDebits = transactions.reduce(
        (sum, t) => sum + (t.debit_amount || 0),
        0
      );

      const totalCredits = transactions.reduce(
        (sum, t) => sum + (t.credit_amount || 0),
        0
      );

      return {
        data: transactions,
        summary: {
          total_transactions: transactions.length,
          total_debits: totalDebits,
          total_credits: totalCredits,
          net_amount: totalCredits - totalDebits,
        },
        recordCount: transactions.length,
      };
    } else if (reportType === "CHART") {
      // Group by date for chart data (e.g., by month)
      const groupedByDate = transactions.reduce((acc: any, t: any) => {
        const date = t.date.split("T")[0].substring(0, 7); // YYYY-MM
        if (!acc[date]) {
          acc[date] = {
            date,
            debit: 0,
            credit: 0,
            net: 0,
            count: 0,
          };
        }
        acc[date].debit += t.debit_amount || 0;
        acc[date].credit += t.credit_amount || 0;
        acc[date].net += (t.credit_amount || 0) - (t.debit_amount || 0);
        acc[date].count += 1;
        return acc;
      }, {});

      const chartData = Object.values(groupedByDate).sort((a: any, b: any) =>
        a.date.localeCompare(b.date)
      );

      return {
        data: chartData,
        summary: {
          total_transactions: transactions.length,
          total_debits: transactions.reduce(
            (sum, t) => sum + (t.debit_amount || 0),
            0
          ),
          total_credits: transactions.reduce(
            (sum, t) => sum + (t.credit_amount || 0),
            0
          ),
        },
        recordCount: chartData.length,
        chartType: config.chartType || "line", // Default to line chart
        xAxis: "date",
        yAxis: ["debit", "credit", "net"],
      };
    }

    return {
      data: transactions,
      summary: {
        total_records: transactions.length,
      },
      recordCount: transactions.length,
    };
  } catch (error) {
    console.error("Error in generateTransactionsData:", error);
    // Return sample data if database query fails
    return generateSampleTransactionsData(reportType);
  }
}

async function generateAccountsData(
  companyId: string,
  reportType: string,
  config: any = {}
) {
  // Get actual account data from the database with current balances
  let query = db("accounts")
    .leftJoin(
      "transaction_entries",
      "accounts.id",
      "transaction_entries.account_id"
    )
    .leftJoin("transactions", function () {
      this.on(
        "transaction_entries.transaction_id",
        "=",
        "transactions.id"
      ).andOnIn("transactions.status", ["APPROVED", "POSTED"]);
    })
    .where("accounts.company_id", companyId)
    .where("accounts.is_active", true);

  // Apply account type filter if provided
  if (config.accountType) {
    query = query.where("accounts.account_type", config.accountType);
  }

  const accounts = await query
    .groupBy(
      "accounts.id",
      "accounts.code",
      "accounts.name",
      "accounts.account_type",
      "accounts.opening_balance"
    )
    .select(
      "accounts.id",
      "accounts.code",
      "accounts.name",
      "accounts.account_type",
      "accounts.opening_balance",
      db.raw(
        "COALESCE(SUM(transaction_entries.debit_amount), 0) as total_debits"
      ),
      db.raw(
        "COALESCE(SUM(transaction_entries.credit_amount), 0) as total_credits"
      ),
      db.raw(
        "accounts.opening_balance + COALESCE(SUM(transaction_entries.debit_amount), 0) - COALESCE(SUM(transaction_entries.credit_amount), 0) as current_balance"
      )
    )
    .orderBy("accounts.code");

  if (reportType === "SUMMARY") {
    const totalAssets = accounts
      .filter((a) => a.account_type === "ASSET")
      .reduce((sum, a) => sum + (a.current_balance || 0), 0);

    const totalLiabilities = accounts
      .filter((a) => a.account_type === "LIABILITY")
      .reduce((sum, a) => sum + (a.current_balance || 0), 0);

    const totalEquity = accounts
      .filter((a) => a.account_type === "EQUITY")
      .reduce((sum, a) => sum + (a.current_balance || 0), 0);

    return {
      data: accounts,
      summary: {
        total_accounts: accounts.length,
        total_assets: totalAssets,
        total_liabilities: totalLiabilities,
        total_equity: totalEquity,
        net_worth: totalAssets - totalLiabilities,
      },
      recordCount: accounts.length,
    };
  } else if (reportType === "CHART") {
    // Group by account type for chart data
    const groupedByType = accounts.reduce((acc: any, a: any) => {
      const type = a.account_type;
      if (!acc[type]) {
        acc[type] = {
          type,
          balance: 0,
          count: 0,
        };
      }
      acc[type].balance += a.current_balance || 0;
      acc[type].count += 1;
      return acc;
    }, {});

    const chartData = Object.values(groupedByType);

    return {
      data: chartData,
      summary: {
        total_accounts: accounts.length,
        total_balance: accounts.reduce(
          (sum, a) => sum + (a.current_balance || 0),
          0
        ),
      },
      recordCount: chartData.length,
      chartType: config.chartType || "bar", // Default to bar chart
      xAxis: "type",
      yAxis: ["balance"],
    };
  }

  return {
    data: accounts,
    summary: {
      total_records: accounts.length,
    },
    recordCount: accounts.length,
  };
}

async function generateContactsData(
  companyId: string,
  reportType: string,
  config: any = {}
) {
  let query = db("contacts")
    .where("company_id", companyId)
    .select("id", "name", "email", "phone", "contact_type", "created_at");

  // Apply contact type filter if provided
  if (config.contactType) {
    query = query.where("contact_type", config.contactType);
  }

  const contacts = await query.orderBy("name");

  if (reportType === "SUMMARY") {
    const customers = contacts.filter(
      (c) => c.contact_type === "CUSTOMER"
    ).length;
    const vendors = contacts.filter((c) => c.contact_type === "VENDOR").length;

    return {
      data: contacts,
      summary: {
        total_contacts: contacts.length,
        customers: customers,
        vendors: vendors,
      },
      recordCount: contacts.length,
    };
  } else if (reportType === "CHART") {
    // Group by contact type for chart data
    const groupedByType = contacts.reduce((acc: any, c: any) => {
      const type = c.contact_type;
      if (!acc[type]) {
        acc[type] = {
          type,
          count: 0,
        };
      }
      acc[type].count += 1;
      return acc;
    }, {});

    const chartData = Object.values(groupedByType);

    return {
      data: chartData,
      summary: {
        total_contacts: contacts.length,
      },
      recordCount: chartData.length,
      chartType: config.chartType || "pie", // Default to pie chart
      xAxis: "type",
      yAxis: ["count"],
    };
  }

  return {
    data: contacts,
    summary: {
      total_records: contacts.length,
    },
    recordCount: contacts.length,
  };
}

function generateSampleTransactionsData(reportType: string) {
  const sampleTransactions = [
    {
      id: 1,
      date: "2025-06-10",
      description: "Office Rent Payment",
      amount: 1200.0,
      status: "APPROVED",
      account_name: "Rent Expense",
      account_code: "5001",
      debit_amount: 1200.0,
      credit_amount: 0,
    },
    {
      id: 2,
      date: "2025-06-09",
      description: "Client Payment Received",
      amount: 2500.0,
      status: "APPROVED",
      account_name: "Accounts Receivable",
      account_code: "1200",
      debit_amount: 2500.0,
      credit_amount: 0,
    },
    {
      id: 3,
      date: "2025-06-08",
      description: "Office Supplies Purchase",
      amount: 350.0,
      status: "APPROVED",
      account_name: "Office Supplies",
      account_code: "5100",
      debit_amount: 350.0,
      credit_amount: 0,
    },
    {
      id: 4,
      date: "2025-06-07",
      description: "Service Revenue",
      amount: 3200.0,
      status: "APPROVED",
      account_name: "Service Revenue",
      account_code: "4000",
      debit_amount: 0,
      credit_amount: 3200.0,
    },
  ];

  if (reportType === "SUMMARY") {
    const totalDebits = sampleTransactions.reduce(
      (sum, t) => sum + (t.debit_amount || 0),
      0
    );

    const totalCredits = sampleTransactions.reduce(
      (sum, t) => sum + (t.credit_amount || 0),
      0
    );

    return {
      data: sampleTransactions,
      summary: {
        total_transactions: sampleTransactions.length,
        total_debits: totalDebits,
        total_credits: totalCredits,
        net_amount: totalCredits - totalDebits,
      },
      recordCount: sampleTransactions.length,
    };
  }

  return {
    data: sampleTransactions,
    summary: {
      total_records: sampleTransactions.length,
    },
    recordCount: sampleTransactions.length,
  };
}

function generateSampleData(reportType: string) {
  const sampleData = [
    {
      id: 1,
      name: "Sample Item 1",
      value: 100,
      category: "Category A",
      date: "2025-06-01",
    },
    {
      id: 2,
      name: "Sample Item 2",
      value: 200,
      category: "Category B",
      date: "2025-06-02",
    },
  ];

  if (reportType === "SUMMARY") {
    return {
      data: sampleData,
      summary: {
        total_items: sampleData.length,
        total_value: sampleData.reduce((sum, item) => sum + item.value, 0),
      },
      recordCount: sampleData.length,
    };
  }

  return {
    data: sampleData,
    summary: {
      total_records: sampleData.length,
    },
    recordCount: sampleData.length,
  };
}

async function generateInvoicesData(
  companyId: string,
  reportType: string,
  config: any = {}
) {
  try {
    // Get actual invoice data from the database
    let query = db("invoices")
      .select(
        "invoices.id",
        "invoices.invoice_number",
        "invoices.invoice_date as date",
        "invoices.total_amount as amount",
        "invoices.status",
        "contacts.name as customer_name"
      )
      .leftJoin("contacts", "invoices.contact_id", "contacts.id")
      .where("invoices.company_id", companyId);

    // Apply date filters from config if provided
    if (config.startDate) {
      query = query.where("invoices.invoice_date", ">=", config.startDate);
    }
    if (config.endDate) {
      query = query.where("invoices.invoice_date", "<=", config.endDate);
    }
    // Apply status filter if provided
    if (config.status) {
      query = query.where("invoices.status", config.status);
    }
    // Apply limit from config or default to 100
    const limit = config.limit || 100;
    query = query.limit(limit).orderBy("invoices.invoice_date", "desc");

    const invoices = await query;

    if (reportType === "SUMMARY") {
      const totalAmount = invoices.reduce((sum, inv) => sum + inv.amount, 0);
      const paidAmount = invoices
        .filter((inv) => inv.status === "PAID")
        .reduce((sum, inv) => sum + inv.amount, 0);

      return {
        data: invoices,
        summary: {
          total_invoices: invoices.length,
          total_amount: totalAmount,
          paid_amount: paidAmount,
          outstanding_amount: totalAmount - paidAmount,
        },
        recordCount: invoices.length,
      };
    } else if (reportType === "CHART") {
      // Group by date for chart data (e.g., by month)
      const groupedByDate = invoices.reduce((acc: any, inv: any) => {
        const date = inv.date.split("T")[0].substring(0, 7); // YYYY-MM
        if (!acc[date]) {
          acc[date] = {
            date,
            total: 0,
            paid: 0,
            pending: 0,
            count: 0,
          };
        }
        acc[date].total += inv.amount;
        if (inv.status === "PAID") {
          acc[date].paid += inv.amount;
        } else {
          acc[date].pending += inv.amount;
        }
        acc[date].count += 1;
        return acc;
      }, {});

      const chartData = Object.values(groupedByDate).sort((a: any, b: any) =>
        a.date.localeCompare(b.date)
      );

      return {
        data: chartData,
        summary: {
          total_invoices: invoices.length,
          total_amount: invoices.reduce((sum, inv) => sum + inv.amount, 0),
        },
        recordCount: chartData.length,
        chartType: config.chartType || "line", // Default to line chart
        xAxis: "date",
        yAxis: ["total", "paid", "pending"],
      };
    }

    return {
      data: invoices,
      summary: {
        total_records: invoices.length,
      },
      recordCount: invoices.length,
    };
  } catch (error) {
    console.error("Error in generateInvoicesData:", error);
    // Return sample data if database query fails
    const sampleInvoices = [
      {
        id: 1,
        invoice_number: "INV-001",
        customer_name: "Acme Corp",
        amount: 1500.0,
        status: "PAID",
        date: "2025-06-01",
      },
      {
        id: 2,
        invoice_number: "INV-002",
        customer_name: "Tech Solutions",
        amount: 2300.0,
        status: "PENDING",
        date: "2025-06-05",
      },
    ];

    if (reportType === "SUMMARY") {
      const totalAmount = sampleInvoices.reduce(
        (sum, inv) => sum + inv.amount,
        0
      );
      const paidAmount = sampleInvoices
        .filter((inv) => inv.status === "PAID")
        .reduce((sum, inv) => sum + inv.amount, 0);

      return {
        data: sampleInvoices,
        summary: {
          total_invoices: sampleInvoices.length,
          total_amount: totalAmount,
          paid_amount: paidAmount,
          outstanding_amount: totalAmount - paidAmount,
        },
        recordCount: sampleInvoices.length,
      };
    } else if (reportType === "CHART") {
      // Group by date for chart data (e.g., by month)
      const groupedByDate = sampleInvoices.reduce((acc: any, inv: any) => {
        const date = inv.date.substring(0, 7); // YYYY-MM
        if (!acc[date]) {
          acc[date] = {
            date,
            total: 0,
            paid: 0,
            pending: 0,
            count: 0,
          };
        }
        acc[date].total += inv.amount;
        if (inv.status === "PAID") {
          acc[date].paid += inv.amount;
        } else {
          acc[date].pending += inv.amount;
        }
        acc[date].count += 1;
        return acc;
      }, {});

      const chartData = Object.values(groupedByDate).sort((a: any, b: any) =>
        a.date.localeCompare(b.date)
      );

      return {
        data: chartData,
        summary: {
          total_invoices: sampleInvoices.length,
          total_amount: sampleInvoices.reduce(
            (sum, inv) => sum + inv.amount,
            0
          ),
        },
        recordCount: chartData.length,
        chartType: config.chartType || "line", // Default to line chart
        xAxis: "date",
        yAxis: ["total", "paid", "pending"],
      };
    }

    return {
      data: sampleInvoices,
      summary: {
        total_records: sampleInvoices.length,
      },
      recordCount: sampleInvoices.length,
    };
  }
}

async function generateBankTransactionsData(
  companyId: string,
  reportType: string,
  config: any = {}
) {
  try {
    // Get actual bank transaction data from the database
    let query = db("bank_transactions")
      .select(
        "bank_transactions.id",
        "bank_transactions.transaction_date as date",
        "bank_transactions.description",
        "bank_transactions.amount",
        "bank_transactions.transaction_type as type",
        "bank_transactions.balance"
      )
      .where("bank_transactions.company_id", companyId);

    // Apply date filters from config if provided
    if (config.startDate) {
      query = query.where(
        "bank_transactions.transaction_date",
        ">=",
        config.startDate
      );
    }
    if (config.endDate) {
      query = query.where(
        "bank_transactions.transaction_date",
        "<=",
        config.endDate
      );
    }
    // Apply transaction type filter if provided
    if (config.transactionType) {
      query = query.where(
        "bank_transactions.transaction_type",
        config.transactionType
      );
    }
    // Apply limit from config or default to 100
    const limit = config.limit || 100;
    query = query
      .limit(limit)
      .orderBy("bank_transactions.transaction_date", "desc");

    const bankTransactions = await query;

    if (reportType === "SUMMARY") {
      const totalCredits = bankTransactions
        .filter((t) => t.type === "CREDIT")
        .reduce((sum, t) => sum + t.amount, 0);

      const totalDebits = Math.abs(
        bankTransactions
          .filter((t) => t.type === "DEBIT")
          .reduce((sum, t) => sum + t.amount, 0)
      );

      return {
        data: bankTransactions,
        summary: {
          total_transactions: bankTransactions.length,
          total_credits: totalCredits,
          total_debits: totalDebits,
          net_change: totalCredits - totalDebits,
        },
        recordCount: bankTransactions.length,
      };
    } else if (reportType === "CHART") {
      // Group by date for chart data (e.g., by month)
      const groupedByDate = bankTransactions.reduce((acc: any, t: any) => {
        const date = t.date.split("T")[0].substring(0, 7); // YYYY-MM
        if (!acc[date]) {
          acc[date] = {
            date,
            credits: 0,
            debits: 0,
            net: 0,
            count: 0,
          };
        }
        if (t.type === "CREDIT") {
          acc[date].credits += t.amount;
          acc[date].net += t.amount;
        } else {
          acc[date].debits += Math.abs(t.amount);
          acc[date].net -= Math.abs(t.amount);
        }
        acc[date].count += 1;
        return acc;
      }, {});

      const chartData = Object.values(groupedByDate).sort((a: any, b: any) =>
        a.date.localeCompare(b.date)
      );

      return {
        data: chartData,
        summary: {
          total_transactions: bankTransactions.length,
          total_credits: bankTransactions
            .filter((t) => t.type === "CREDIT")
            .reduce((sum, t) => sum + t.amount, 0),
          total_debits: Math.abs(
            bankTransactions
              .filter((t) => t.type === "DEBIT")
              .reduce((sum, t) => sum + t.amount, 0)
          ),
        },
        recordCount: chartData.length,
        chartType: config.chartType || "line", // Default to line chart
        xAxis: "date",
        yAxis: ["credits", "debits", "net"],
      };
    }

    return {
      data: bankTransactions,
      summary: {
        total_records: bankTransactions.length,
      },
      recordCount: bankTransactions.length,
    };
  } catch (error) {
    console.error("Error in generateBankTransactionsData:", error);
    // Return sample data if database query fails
    const sampleBankTransactions = [
      {
        id: 1,
        date: "2025-06-10",
        description: "Payment from Customer A",
        amount: 1500.0,
        type: "CREDIT",
        balance: 15000.0,
      },
      {
        id: 2,
        date: "2025-06-09",
        description: "Office Rent Payment",
        amount: -1200.0,
        type: "DEBIT",
        balance: 13500.0,
      },
    ];

    if (reportType === "SUMMARY") {
      const totalCredits = sampleBankTransactions
        .filter((t) => t.type === "CREDIT")
        .reduce((sum, t) => sum + t.amount, 0);

      const totalDebits = Math.abs(
        sampleBankTransactions
          .filter((t) => t.type === "DEBIT")
          .reduce((sum, t) => sum + t.amount, 0)
      );

      return {
        data: sampleBankTransactions,
        summary: {
          total_transactions: sampleBankTransactions.length,
          total_credits: totalCredits,
          total_debits: totalDebits,
          net_change: totalCredits - totalDebits,
        },
        recordCount: sampleBankTransactions.length,
      };
    } else if (reportType === "CHART") {
      // Group by date for chart data (e.g., by month)
      const groupedByDate = sampleBankTransactions.reduce(
        (acc: any, t: any) => {
          const date = t.date.substring(0, 7); // YYYY-MM
          if (!acc[date]) {
            acc[date] = {
              date,
              credits: 0,
              debits: 0,
              net: 0,
              count: 0,
            };
          }
          if (t.type === "CREDIT") {
            acc[date].credits += t.amount;
            acc[date].net += t.amount;
          } else {
            acc[date].debits += Math.abs(t.amount);
            acc[date].net -= Math.abs(t.amount);
          }
          acc[date].count += 1;
          return acc;
        },
        {}
      );

      const chartData = Object.values(groupedByDate).sort((a: any, b: any) =>
        a.date.localeCompare(b.date)
      );

      return {
        data: chartData,
        summary: {
          total_transactions: sampleBankTransactions.length,
          total_credits: sampleBankTransactions
            .filter((t) => t.type === "CREDIT")
            .reduce((sum, t) => sum + t.amount, 0),
          total_debits: Math.abs(
            sampleBankTransactions
              .filter((t) => t.type === "DEBIT")
              .reduce((sum, t) => sum + t.amount, 0)
          ),
        },
        recordCount: chartData.length,
        chartType: config.chartType || "line", // Default to line chart
        xAxis: "date",
        yAxis: ["credits", "debits", "net"],
      };
    }

    return {
      data: sampleBankTransactions,
      summary: {
        total_records: sampleBankTransactions.length,
      },
      recordCount: sampleBankTransactions.length,
    };
  }
}

// Helper function to generate payments data for custom reports
async function generatePaymentsData(
  companyId: string,
  reportType: string,
  config: any = {}
) {
  try {
    // Get actual payment data from the database
    let query = db("payments")
      .select(
        "payments.id",
        "payments.payment_date as date",
        "payments.amount",
        "payments.payment_method",
        "payments.status",
        "contacts.name as contact_name"
      )
      .leftJoin("contacts", "payments.contact_id", "contacts.id")
      .where("payments.company_id", companyId);

    // Apply date filters from config if provided
    if (config.startDate) {
      query = query.where("payments.payment_date", ">=", config.startDate);
    }
    if (config.endDate) {
      query = query.where("payments.payment_date", "<=", config.endDate);
    }
    // Apply status filter if provided
    if (config.status) {
      query = query.where("payments.status", config.status);
    }
    // Apply payment method filter if provided
    if (config.paymentMethod) {
      query = query.where("payments.payment_method", config.paymentMethod);
    }
    // Apply limit from config or default to 100
    const limit = config.limit || 100;
    query = query.limit(limit).orderBy("payments.payment_date", "desc");

    const payments = await query;

    if (reportType === "SUMMARY") {
      const totalAmount = payments.reduce((sum, p) => sum + p.amount, 0);
      const completedAmount = payments
        .filter((p) => p.status === "COMPLETED")
        .reduce((sum, p) => sum + p.amount, 0);

      return {
        data: payments,
        summary: {
          total_payments: payments.length,
          total_amount: totalAmount,
          completed_amount: completedAmount,
          pending_amount: totalAmount - completedAmount,
        },
        recordCount: payments.length,
      };
    } else if (reportType === "CHART") {
      // Group by date for chart data (e.g., by month)
      const groupedByDate = payments.reduce((acc: any, p: any) => {
        const date = p.date.split("T")[0].substring(0, 7); // YYYY-MM
        if (!acc[date]) {
          acc[date] = {
            date,
            total: 0,
            completed: 0,
            pending: 0,
            count: 0,
          };
        }
        acc[date].total += p.amount;
        if (p.status === "COMPLETED") {
          acc[date].completed += p.amount;
        } else {
          acc[date].pending += p.amount;
        }
        acc[date].count += 1;
        return acc;
      }, {});

      const chartData = Object.values(groupedByDate).sort((a: any, b: any) =>
        a.date.localeCompare(b.date)
      );

      return {
        data: chartData,
        summary: {
          total_payments: payments.length,
          total_amount: payments.reduce((sum, p) => sum + p.amount, 0),
        },
        recordCount: chartData.length,
        chartType: config.chartType || "line", // Default to line chart
        xAxis: "date",
        yAxis: ["total", "completed", "pending"],
      };
    }

    return {
      data: payments,
      summary: {
        total_records: payments.length,
      },
      recordCount: payments.length,
    };
  } catch (error) {
    console.error("Error in generatePaymentsData:", error);
    // Return sample data if database query fails
    const samplePayments = [
      {
        id: 1,
        date: "2025-06-10",
        contact_name: "Acme Corp",
        amount: 1500.0,
        payment_method: "BANK_TRANSFER",
        status: "COMPLETED",
      },
      {
        id: 2,
        date: "2025-06-05",
        contact_name: "Tech Solutions",
        amount: 800.0,
        payment_method: "CASH",
        status: "PENDING",
      },
    ];

    if (reportType === "SUMMARY") {
      const totalAmount = samplePayments.reduce((sum, p) => sum + p.amount, 0);
      const completedAmount = samplePayments
        .filter((p) => p.status === "COMPLETED")
        .reduce((sum, p) => sum + p.amount, 0);

      return {
        data: samplePayments,
        summary: {
          total_payments: samplePayments.length,
          total_amount: totalAmount,
          completed_amount: completedAmount,
          pending_amount: totalAmount - completedAmount,
        },
        recordCount: samplePayments.length,
      };
    } else if (reportType === "CHART") {
      // Group by date for chart data (e.g., by month)
      const groupedByDate = samplePayments.reduce((acc: any, p: any) => {
        const date = p.date.substring(0, 7); // YYYY-MM
        if (!acc[date]) {
          acc[date] = {
            date,
            total: 0,
            completed: 0,
            pending: 0,
            count: 0,
          };
        }
        acc[date].total += p.amount;
        if (p.status === "COMPLETED") {
          acc[date].completed += p.amount;
        } else {
          acc[date].pending += p.amount;
        }
        acc[date].count += 1;
        return acc;
      }, {});

      const chartData = Object.values(groupedByDate).sort((a: any, b: any) =>
        a.date.localeCompare(b.date)
      );

      return {
        data: chartData,
        summary: {
          total_payments: samplePayments.length,
          total_amount: samplePayments.reduce((sum, p) => sum + p.amount, 0),
        },
        recordCount: chartData.length,
        chartType: config.chartType || "line", // Default to line chart
        xAxis: "date",
        yAxis: ["total", "completed", "pending"],
      };
    }

    return {
      data: samplePayments,
      summary: {
        total_records: samplePayments.length,
      },
      recordCount: samplePayments.length,
    };
  }
}

// Helper function to generate taxes data for custom reports
async function generateTaxesData(
  companyId: string,
  reportType: string,
  config: any = {}
) {
  try {
    // Get actual tax data from the database
    let query = db("tax_transactions")
      .select(
        "tax_transactions.id",
        "tax_transactions.transaction_date as date",
        "tax_transactions.tax_type",
        "tax_transactions.amount",
        "tax_transactions.status",
        "tax_transactions.description"
      )
      .where("tax_transactions.company_id", companyId);

    // Apply date filters from config if provided
    if (config.startDate) {
      query = query.where(
        "tax_transactions.transaction_date",
        ">=",
        config.startDate
      );
    }
    if (config.endDate) {
      query = query.where(
        "tax_transactions.transaction_date",
        "<=",
        config.endDate
      );
    }
    // Apply tax type filter if provided
    if (config.taxType) {
      query = query.where("tax_transactions.tax_type", config.taxType);
    }
    // Apply limit from config or default to 100
    const limit = config.limit || 100;
    query = query
      .limit(limit)
      .orderBy("tax_transactions.transaction_date", "desc");

    const taxes = await query;

    if (reportType === "SUMMARY") {
      const totalAmount = taxes.reduce((sum, t) => sum + t.amount, 0);
      const paidAmount = taxes
        .filter((t) => t.status === "PAID")
        .reduce((sum, t) => sum + t.amount, 0);

      return {
        data: taxes,
        summary: {
          total_tax_transactions: taxes.length,
          total_amount: totalAmount,
          paid_amount: paidAmount,
          outstanding_amount: totalAmount - paidAmount,
        },
        recordCount: taxes.length,
      };
    } else if (reportType === "CHART") {
      // Group by date for chart data (e.g., by month)
      const groupedByDate = taxes.reduce((acc: any, t: any) => {
        const date = t.date.split("T")[0].substring(0, 7); // YYYY-MM
        if (!acc[date]) {
          acc[date] = {
            date,
            total: 0,
            paid: 0,
            pending: 0,
            count: 0,
          };
        }
        acc[date].total += t.amount;
        if (t.status === "PAID") {
          acc[date].paid += t.amount;
        } else {
          acc[date].pending += t.amount;
        }
        acc[date].count += 1;
        return acc;
      }, {});

      const chartData = Object.values(groupedByDate).sort((a: any, b: any) =>
        a.date.localeCompare(b.date)
      );

      return {
        data: chartData,
        summary: {
          total_tax_transactions: taxes.length,
          total_amount: taxes.reduce((sum, t) => sum + t.amount, 0),
        },
        recordCount: chartData.length,
        chartType: config.chartType || "line", // Default to line chart
        xAxis: "date",
        yAxis: ["total", "paid", "pending"],
      };
    }

    return {
      data: taxes,
      summary: {
        total_records: taxes.length,
      },
      recordCount: taxes.length,
    };
  } catch (error) {
    console.error("Error in generateTaxesData:", error);
    // Return sample data if database query fails
    const sampleTaxes = [
      {
        id: 1,
        date: "2025-06-10",
        tax_type: "VAT",
        amount: 180.0,
        status: "PAID",
        description: "VAT Payment for June",
      },
      {
        id: 2,
        date: "2025-06-05",
        tax_type: "INCOME_TAX",
        amount: 500.0,
        status: "PENDING",
        description: "Income Tax Q2",
      },
    ];

    if (reportType === "SUMMARY") {
      const totalAmount = sampleTaxes.reduce((sum, t) => sum + t.amount, 0);
      const paidAmount = sampleTaxes
        .filter((t) => t.status === "PAID")
        .reduce((sum, t) => sum + t.amount, 0);

      return {
        data: sampleTaxes,
        summary: {
          total_tax_transactions: sampleTaxes.length,
          total_amount: totalAmount,
          paid_amount: paidAmount,
          outstanding_amount: totalAmount - paidAmount,
        },
        recordCount: sampleTaxes.length,
      };
    } else if (reportType === "CHART") {
      // Group by date for chart data (e.g., by month)
      const groupedByDate = sampleTaxes.reduce((acc: any, t: any) => {
        const date = t.date.substring(0, 7); // YYYY-MM
        if (!acc[date]) {
          acc[date] = {
            date,
            total: 0,
            paid: 0,
            pending: 0,
            count: 0,
          };
        }
        acc[date].total += t.amount;
        if (t.status === "PAID") {
          acc[date].paid += t.amount;
        } else {
          acc[date].pending += t.amount;
        }
        acc[date].count += 1;
        return acc;
      }, {});

      const chartData = Object.values(groupedByDate).sort((a: any, b: any) =>
        a.date.localeCompare(b.date)
      );

      return {
        data: chartData,
        summary: {
          total_tax_transactions: sampleTaxes.length,
          total_amount: sampleTaxes.reduce((sum, t) => sum + t.amount, 0),
        },
        recordCount: chartData.length,
        chartType: config.chartType || "line", // Default to line chart
        xAxis: "date",
        yAxis: ["total", "paid", "pending"],
      };
    }

    return {
      data: sampleTaxes,
      summary: {
        total_records: sampleTaxes.length,
      },
      recordCount: sampleTaxes.length,
    };
  }
}

// GET /api/reports/:companyId/balance-sheet
router.get(
  "/:companyId/balance-sheet",
  requireCompanyAccess,
  // requirePermission("reports:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { asOfDate = new Date().toISOString().split("T")[0] } = req.query;

    try {
      // Get all accounts with calculated balances as of the specified date
      const accounts = await getAccountBalances(companyId, asOfDate as string);

      // Group accounts by type and subtype
      const assets = groupAccountsBySubtype(
        accounts.filter((a) => a.account_type === "ASSET")
      );
      const liabilities = groupAccountsBySubtype(
        accounts.filter((a) => a.account_type === "LIABILITY")
      );
      const equity = groupAccountsBySubtype(
        accounts.filter((a) => a.account_type === "EQUITY")
      );

      // Calculate totals
      const totalAssets = calculateAccountGroupTotal(assets);
      const totalLiabilities = calculateAccountGroupTotal(liabilities);
      const totalEquity = calculateAccountGroupTotal(equity);

      // Calculate net income for the period (this should be added to equity)
      const netIncome = await calculateNetIncomeAsOfDate(
        companyId,
        asOfDate as string
      );
      const totalEquityWithNetIncome = totalEquity + netIncome;
      const totalLiabilitiesAndEquity =
        totalLiabilities + totalEquityWithNetIncome;

      // Check if balance sheet balances
      const isBalanced =
        Math.abs(totalAssets - totalLiabilitiesAndEquity) < 0.01;

      const balanceSheetData = {
        reportType: "BALANCE_SHEET",
        asOfDate: asOfDate as string,
        data: {
          assets: {
            accounts: assets,
            total: totalAssets,
          },
          liabilities: {
            accounts: liabilities,
            total: totalLiabilities,
          },
          equity: {
            accounts: equity,
            total: totalEquity,
          },
          totals: {
            totalAssets,
            totalLiabilities,
            totalEquity,
            netIncome,
            totalEquityWithNetIncome,
            totalLiabilitiesAndEquity,
            isBalanced,
          },
        },
      };

      res.json(balanceSheetData);
    } catch (error) {
      console.error("Error generating balance sheet:", error);
      res.status(500).json({ error: "Failed to generate balance sheet" });
    }
  })
);

// Helper function to group accounts by subtype
function groupAccountsBySubtype(accounts: any[]) {
  const grouped: Record<string, any[]> = {};

  accounts.forEach((account) => {
    const subtype = account.account_subtype || "OTHER";
    if (!grouped[subtype]) {
      grouped[subtype] = [];
    }
    grouped[subtype].push({
      id: account.id,
      code: account.code,
      name: account.name,
      balance: parseFloat(account.current_balance) || 0,
    });
  });

  return grouped;
}

// Helper function to calculate total for account groups
function calculateAccountGroupTotal(
  accountGroups: Record<string, any[]>
): number {
  let total = 0;
  Object.values(accountGroups).forEach((accounts) => {
    accounts.forEach((account) => {
      total += account.balance;
    });
  });
  return total;
}

// Helper function to calculate net income as of a specific date
async function calculateNetIncomeAsOfDate(
  companyId: string,
  asOfDate: string
): Promise<number> {
  try {
    // Get revenue accounts
    const revenueAccounts = await db("accounts")
      .select(
        "accounts.*",
        db.raw(
          `
          COALESCE(
            accounts.opening_balance +
            (SELECT
              SUM(te.credit_amount - te.debit_amount)
             FROM transaction_entries te
             JOIN transactions t ON te.transaction_id = t.id
             WHERE te.account_id = accounts.id
             AND t.transaction_date <= ?
             AND t.status = 'POSTED'),
            accounts.opening_balance
          ) as current_balance
        `,
          [asOfDate]
        )
      )
      .where("accounts.company_id", companyId)
      .where("accounts.account_type", "REVENUE")
      .where("accounts.is_active", true);

    // Get expense accounts
    const expenseAccounts = await db("accounts")
      .select(
        "accounts.*",
        db.raw(
          `
          COALESCE(
            accounts.opening_balance +
            (SELECT
              SUM(te.debit_amount - te.credit_amount)
             FROM transaction_entries te
             JOIN transactions t ON te.transaction_id = t.id
             WHERE te.account_id = accounts.id
             AND t.transaction_date <= ?
             AND t.status = 'POSTED'),
            accounts.opening_balance
          ) as current_balance
        `,
          [asOfDate]
        )
      )
      .where("accounts.company_id", companyId)
      .where("accounts.account_type", "EXPENSE")
      .where("accounts.is_active", true);

    // Calculate totals
    const totalRevenue = revenueAccounts.reduce(
      (sum, account) => sum + (parseFloat(account.current_balance) || 0),
      0
    );
    const totalExpenses = expenseAccounts.reduce(
      (sum, account) => sum + (parseFloat(account.current_balance) || 0),
      0
    );

    return totalRevenue - totalExpenses;
  } catch (error) {
    console.error("Error calculating net income:", error);
    return 0;
  }
}

// Helper function to get account activity for a period
async function getAccountActivity(
  companyId: string,
  accountType: string,
  startDate: string,
  endDate: string
) {
  try {
    const accounts = await db("accounts")
      .select(
        "accounts.*",
        db.raw(
          `
          COALESCE(
            (SELECT
              CASE
                WHEN accounts.account_type IN ('ASSET', 'EXPENSE') THEN
                  SUM(te.debit_amount - te.credit_amount)
                ELSE
                  SUM(te.credit_amount - te.debit_amount)
              END
             FROM transaction_entries te
             JOIN transactions t ON te.transaction_id = t.id
             WHERE te.account_id = accounts.id
             AND t.transaction_date >= ?
             AND t.transaction_date <= ?
             AND t.status = 'POSTED'),
            0
          ) as period_activity
        `,
          [startDate, endDate]
        )
      )
      .where("accounts.company_id", companyId)
      .where("accounts.account_type", accountType)
      .where("accounts.is_active", true);

    return accounts;
  } catch (error) {
    console.error("Error getting account activity:", error);
    return [];
  }
}

// GET /api/reports/:companyId/income-statement
router.get(
  "/:companyId/income-statement",
  requireCompanyAccess,
  // requirePermission("reports:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const {
      startDate = new Date(new Date().getFullYear(), 0, 1)
        .toISOString()
        .split("T")[0], // Start of current year
      endDate = new Date().toISOString().split("T")[0], // Today
    } = req.query;

    try {
      // Get revenue accounts with period activity
      const revenueAccounts = await getAccountActivity(
        companyId,
        "REVENUE",
        startDate as string,
        endDate as string
      );

      // Get expense accounts with period activity
      const expenseAccounts = await getAccountActivity(
        companyId,
        "EXPENSE",
        startDate as string,
        endDate as string
      );

      // Group accounts by subtype
      const revenueBySubtype = groupAccountsBySubtype(revenueAccounts);
      const expensesBySubtype = groupAccountsBySubtype(expenseAccounts);

      // Calculate totals
      const totalRevenue = calculateAccountGroupTotal(revenueBySubtype);
      const totalExpenses = calculateAccountGroupTotal(expensesBySubtype);
      const netIncome = totalRevenue - totalExpenses;

      // Calculate gross profit (if COGS exists)
      const cogsAccounts = expenseAccounts.filter(
        (acc) => acc.account_subtype === "COST_OF_GOODS_SOLD"
      );
      const totalCOGS = cogsAccounts.reduce(
        (sum, acc) => sum + (parseFloat(acc.period_activity) || 0),
        0
      );
      const grossProfit = totalRevenue - totalCOGS;

      // Separate operating and non-operating
      const operatingRevenue = revenueAccounts
        .filter((acc) => acc.account_subtype === "OPERATING_REVENUE")
        .reduce((sum, acc) => sum + (parseFloat(acc.period_activity) || 0), 0);

      const operatingExpenses = expenseAccounts
        .filter((acc) => acc.account_subtype === "OPERATING_EXPENSE")
        .reduce((sum, acc) => sum + (parseFloat(acc.period_activity) || 0), 0);

      const operatingIncome = operatingRevenue - operatingExpenses - totalCOGS;

      const incomeStatementData = {
        reportType: "INCOME_STATEMENT",
        startDate: startDate as string,
        endDate: endDate as string,
        data: {
          revenue: {
            accounts: revenueBySubtype,
            total: totalRevenue,
          },
          expenses: {
            accounts: expensesBySubtype,
            total: totalExpenses,
          },
          costOfGoodsSold: {
            accounts: cogsAccounts.map((acc) => ({
              id: acc.id,
              code: acc.code,
              name: acc.name,
              balance: parseFloat(acc.period_activity) || 0,
            })),
            total: totalCOGS,
          },
        },
        totals: {
          totalRevenue,
          totalExpenses,
          totalCOGS,
          grossProfit,
          operatingRevenue,
          operatingExpenses,
          operatingIncome,
          netIncome,
        },
      };

      res.json(incomeStatementData);
    } catch (error) {
      console.error("Error generating income statement:", error);
      res.status(500).json({ error: "Failed to generate income statement" });
    }
  })
);

// Helper function to calculate net income for a specific period
async function calculateNetIncomeForPeriod(
  companyId: string,
  startDate: string,
  endDate: string
): Promise<number> {
  try {
    // Get revenue for the period
    const revenueAccounts = await getAccountActivity(
      companyId,
      "REVENUE",
      startDate,
      endDate
    );
    const totalRevenue = revenueAccounts.reduce(
      (sum, account) => sum + (parseFloat(account.period_activity) || 0),
      0
    );

    // Get expenses for the period
    const expenseAccounts = await getAccountActivity(
      companyId,
      "EXPENSE",
      startDate,
      endDate
    );
    const totalExpenses = expenseAccounts.reduce(
      (sum, account) => sum + (parseFloat(account.period_activity) || 0),
      0
    );

    return totalRevenue - totalExpenses;
  } catch (error) {
    console.error("Error calculating net income for period:", error);
    return 0;
  }
}

// Helper function to get operating cash flow adjustments
async function getOperatingCashFlow(
  companyId: string,
  startDate: string,
  endDate: string
) {
  try {
    // This is a simplified version - in a real implementation, you would:
    // 1. Add back depreciation and amortization
    // 2. Adjust for changes in working capital (accounts receivable, inventory, accounts payable)
    // 3. Add back other non-cash expenses

    const adjustments = [
      {
        description: "Depreciation and Amortization",
        amount: await getDepreciationForPeriod(companyId, startDate, endDate),
      },
      {
        description: "Changes in Accounts Receivable",
        amount: await getAccountsReceivableChange(
          companyId,
          startDate,
          endDate
        ),
      },
      {
        description: "Changes in Accounts Payable",
        amount: await getAccountsPayableChange(companyId, startDate, endDate),
      },
    ];

    return adjustments.filter((adj) => adj.amount !== 0);
  } catch (error) {
    console.error("Error getting operating cash flow:", error);
    return [];
  }
}

// Helper functions for cash flow calculations
async function getDepreciationForPeriod(
  companyId: string,
  startDate: string,
  endDate: string
): Promise<number> {
  // Simplified - in real implementation, this would calculate actual depreciation
  return 0;
}

async function getAccountsReceivableChange(
  companyId: string,
  startDate: string,
  endDate: string
): Promise<number> {
  // Simplified - in real implementation, this would calculate AR changes
  return 0;
}

async function getAccountsPayableChange(
  companyId: string,
  startDate: string,
  endDate: string
): Promise<number> {
  // Simplified - in real implementation, this would calculate AP changes
  return 0;
}

// Helper function to get investing cash flow
async function getInvestingCashFlow(
  companyId: string,
  startDate: string,
  endDate: string
) {
  try {
    // Get transactions related to fixed assets, investments, etc.
    const investingTransactions = await db("transactions")
      .join(
        "transaction_entries",
        "transactions.id",
        "transaction_entries.transaction_id"
      )
      .join("accounts", "transaction_entries.account_id", "accounts.id")
      .where("transactions.company_id", companyId)
      .where("transactions.transaction_date", ">=", startDate)
      .where("transactions.transaction_date", "<=", endDate)
      .where("transactions.status", "POSTED")
      .where("accounts.account_subtype", "NON_CURRENT_ASSET")
      .select(
        "transactions.description",
        db.raw(
          "SUM(transaction_entries.credit_amount - transaction_entries.debit_amount) as amount"
        )
      )
      .groupBy("transactions.id", "transactions.description")
      .having(
        db.raw(
          "SUM(transaction_entries.credit_amount - transaction_entries.debit_amount)"
        ),
        "!=",
        0
      );

    return investingTransactions.map((tx: any) => ({
      description: tx.description,
      amount: parseFloat(tx.amount) || 0,
    }));
  } catch (error) {
    console.error("Error getting investing cash flow:", error);
    return [];
  }
}

// Helper function to get financing cash flow
async function getFinancingCashFlow(
  companyId: string,
  startDate: string,
  endDate: string
) {
  try {
    // Get transactions related to equity and long-term debt
    const financingTransactions = await db("transactions")
      .join(
        "transaction_entries",
        "transactions.id",
        "transaction_entries.transaction_id"
      )
      .join("accounts", "transaction_entries.account_id", "accounts.id")
      .where("transactions.company_id", companyId)
      .where("transactions.transaction_date", ">=", startDate)
      .where("transactions.transaction_date", "<=", endDate)
      .where("transactions.status", "POSTED")
      .whereIn("accounts.account_type", ["EQUITY"])
      .orWhere("accounts.account_subtype", "NON_CURRENT_LIABILITY")
      .select(
        "transactions.description",
        db.raw(
          "SUM(transaction_entries.credit_amount - transaction_entries.debit_amount) as amount"
        )
      )
      .groupBy("transactions.id", "transactions.description")
      .having(
        db.raw(
          "SUM(transaction_entries.credit_amount - transaction_entries.debit_amount)"
        ),
        "!=",
        0
      );

    return financingTransactions.map((tx: any) => ({
      description: tx.description,
      amount: parseFloat(tx.amount) || 0,
    }));
  } catch (error) {
    console.error("Error getting financing cash flow:", error);
    return [];
  }
}

// Helper function to get cash balance as of a specific date
async function getCashBalanceAsOfDate(
  companyId: string,
  asOfDate: string,
  isBeginning: boolean = false
): Promise<number> {
  try {
    // Adjust date for beginning balance (day before start date)
    const targetDate = isBeginning
      ? new Date(new Date(asOfDate).getTime() - 24 * 60 * 60 * 1000)
          .toISOString()
          .split("T")[0]
      : asOfDate;

    // Get cash and cash equivalent accounts
    const cashAccounts = await db("accounts")
      .select(
        "accounts.*",
        db.raw(
          `
          COALESCE(
            accounts.opening_balance +
            (SELECT
              SUM(te.debit_amount - te.credit_amount)
             FROM transaction_entries te
             JOIN transactions t ON te.transaction_id = t.id
             WHERE te.account_id = accounts.id
             AND t.transaction_date <= ?
             AND t.status = 'POSTED'),
            accounts.opening_balance
          ) as current_balance
        `,
          [targetDate]
        )
      )
      .where("accounts.company_id", companyId)
      .where("accounts.account_type", "ASSET")
      .where("accounts.account_subtype", "CURRENT_ASSET")
      .where(function () {
        this.where("accounts.name", "like", "%cash%").orWhere(
          "accounts.name",
          "like",
          "%bank%"
        );
      })
      .where("accounts.is_active", true);

    return cashAccounts.reduce(
      (sum, account) => sum + (parseFloat(account.current_balance) || 0),
      0
    );
  } catch (error) {
    console.error("Error getting cash balance:", error);
    return 0;
  }
}

// GET /api/reports/:companyId/cash-flow
router.get(
  "/:companyId/cash-flow",
  requireCompanyAccess,
  // requirePermission("reports:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const {
      startDate = new Date(new Date().getFullYear(), 0, 1)
        .toISOString()
        .split("T")[0], // Start of current year
      endDate = new Date().toISOString().split("T")[0], // Today
    } = req.query;

    try {
      // Get net income from income statement
      const netIncome = await calculateNetIncomeForPeriod(
        companyId,
        startDate as string,
        endDate as string
      );

      // Get operating activities
      const operatingActivities = await getOperatingCashFlow(
        companyId,
        startDate as string,
        endDate as string
      );

      // Get investing activities
      const investingActivities = await getInvestingCashFlow(
        companyId,
        startDate as string,
        endDate as string
      );

      // Get financing activities
      const financingActivities = await getFinancingCashFlow(
        companyId,
        startDate as string,
        endDate as string
      );

      // Calculate totals
      const totalOperating = operatingActivities.reduce(
        (sum: number, item: any) => sum + item.amount,
        netIncome
      );
      const totalInvesting = investingActivities.reduce(
        (sum: number, item: any) => sum + item.amount,
        0
      );
      const totalFinancing = financingActivities.reduce(
        (sum: number, item: any) => sum + item.amount,
        0
      );

      const netCashFlow = totalOperating + totalInvesting + totalFinancing;

      // Get beginning and ending cash balances
      const beginningCash = await getCashBalanceAsOfDate(
        companyId,
        startDate as string,
        true
      );
      const endingCash = beginningCash + netCashFlow;

      const cashFlowData = {
        reportType: "CASH_FLOW",
        startDate: startDate as string,
        endDate: endDate as string,
        data: {
          operatingActivities: {
            netIncome,
            adjustments: operatingActivities,
            total: totalOperating,
          },
          investingActivities: {
            activities: investingActivities,
            total: totalInvesting,
          },
          financingActivities: {
            activities: financingActivities,
            total: totalFinancing,
          },
        },
        totals: {
          netIncome,
          totalOperating,
          totalInvesting,
          totalFinancing,
          netCashFlow,
          beginningCash,
          endingCash,
        },
      };

      res.json(cashFlowData);
    } catch (error) {
      console.error("Error generating cash flow statement:", error);
      res.status(500).json({ error: "Failed to generate cash flow statement" });
    }
  })
);

// GET /api/reports/:companyId/trial-balance
router.get(
  "/:companyId/trial-balance",
  requireCompanyAccess,
  // requirePermission("reports:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { asOfDate = new Date().toISOString().split("T")[0] } = req.query;

    try {
      // Get all accounts with their balances as of the specified date
      const accounts = await getAccountBalances(companyId, asOfDate as string);

      // Process accounts for trial balance
      const trialBalanceAccounts = accounts.map((account) => {
        const balance = parseFloat(account.current_balance) || 0;

        // Determine debit/credit based on account type and balance
        let debitAmount = 0;
        let creditAmount = 0;

        if (balance !== 0) {
          // For asset and expense accounts, positive balance = debit
          if (
            account.account_type === "ASSET" ||
            account.account_type === "EXPENSE"
          ) {
            if (balance > 0) {
              debitAmount = balance;
            } else {
              creditAmount = Math.abs(balance);
            }
          }
          // For liability, equity, and revenue accounts, positive balance = credit
          else if (
            account.account_type === "LIABILITY" ||
            account.account_type === "EQUITY" ||
            account.account_type === "REVENUE"
          ) {
            if (balance > 0) {
              creditAmount = balance;
            } else {
              debitAmount = Math.abs(balance);
            }
          }
        }

        return {
          id: account.id,
          code: account.code,
          name: account.name,
          accountType: account.account_type,
          accountSubtype: account.account_subtype,
          debitAmount,
          creditAmount,
          balance,
        };
      });

      // Filter out zero balance accounts if requested
      const { includeZeroBalances = false } = req.query;
      const filteredAccounts = includeZeroBalances
        ? trialBalanceAccounts
        : trialBalanceAccounts.filter(
            (acc) => acc.debitAmount !== 0 || acc.creditAmount !== 0
          );

      // Calculate totals
      const totalDebits = filteredAccounts.reduce(
        (sum, acc) => sum + acc.debitAmount,
        0
      );
      const totalCredits = filteredAccounts.reduce(
        (sum, acc) => sum + acc.creditAmount,
        0
      );
      const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01;

      // Group accounts by type for better presentation
      const accountsByType = {
        ASSET: filteredAccounts.filter((acc) => acc.accountType === "ASSET"),
        LIABILITY: filteredAccounts.filter(
          (acc) => acc.accountType === "LIABILITY"
        ),
        EQUITY: filteredAccounts.filter((acc) => acc.accountType === "EQUITY"),
        REVENUE: filteredAccounts.filter(
          (acc) => acc.accountType === "REVENUE"
        ),
        EXPENSE: filteredAccounts.filter(
          (acc) => acc.accountType === "EXPENSE"
        ),
      };

      const trialBalanceData = {
        reportType: "TRIAL_BALANCE",
        asOfDate: asOfDate as string,
        includeZeroBalances: includeZeroBalances as boolean,
        data: {
          accounts: filteredAccounts,
          accountsByType,
        },
        totals: {
          totalDebits,
          totalCredits,
          difference: totalDebits - totalCredits,
          isBalanced,
        },
        summary: {
          totalAccounts: filteredAccounts.length,
          accountsWithDebits: filteredAccounts.filter(
            (acc) => acc.debitAmount > 0
          ).length,
          accountsWithCredits: filteredAccounts.filter(
            (acc) => acc.creditAmount > 0
          ).length,
        },
      };

      res.json(trialBalanceData);
    } catch (error) {
      console.error("Error generating trial balance:", error);
      res.status(500).json({ error: "Failed to generate trial balance" });
    }
  })
);

// GET /api/reports/:companyId/aging-report
router.get(
  "/:companyId/aging-report",
  requireCompanyAccess,
  // requirePermission("reports:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const {
      reportType = "receivables",
      asOfDate = new Date().toISOString().split("T")[0],
    } = req.query;

    try {
      // Determine which accounts to query based on report type
      const accountTypes =
        reportType === "receivables"
          ? ["ACCOUNTS_RECEIVABLE"]
          : ["ACCOUNTS_PAYABLE"];

      // Get all relevant accounts with their balances
      const accountsQuery = knex("accounts")
        .select([
          "accounts.id",
          "accounts.code",
          "accounts.name",
          "accounts.account_type",
          "accounts.current_balance",
        ])
        .where("accounts.company_id", companyId)
        .whereIn("accounts.account_type", accountTypes)
        .where("accounts.is_active", true);

      const accounts = await accountsQuery;

      // Get transactions for aging calculation
      const transactionsQuery = knex("transactions")
        .select([
          "transactions.id",
          "transactions.account_id",
          "transactions.transaction_date",
          "transactions.description",
          "transactions.debit_amount",
          "transactions.credit_amount",
          "transactions.reference_number",
          "accounts.code as account_code",
          "accounts.name as account_name",
        ])
        .join("accounts", "transactions.account_id", "accounts.id")
        .where("accounts.company_id", companyId)
        .whereIn("accounts.account_type", accountTypes)
        .where("transactions.transaction_date", "<=", asOfDate as string)
        .orderBy("transactions.transaction_date", "desc");

      const transactions = await transactionsQuery;

      // Calculate aging buckets
      const asOfDateObj = new Date(asOfDate as string);
      const agingData: Record<string, any> = {};

      // Initialize aging buckets for each account
      accounts.forEach((account) => {
        agingData[account.id] = {
          accountId: account.id,
          accountCode: account.code,
          accountName: account.name,
          accountType: account.account_type,
          current: 0,
          "1-30": 0,
          "31-60": 0,
          "61-90": 0,
          over90: 0,
          total: 0,
        };
      });

      // Process transactions and calculate aging
      transactions.forEach((transaction) => {
        const transactionDate = new Date(transaction.transaction_date);
        const daysDiff = Math.floor(
          (asOfDateObj.getTime() - transactionDate.getTime()) /
            (1000 * 60 * 60 * 24)
        );

        // Calculate transaction amount based on report type
        let amount = 0;
        if (reportType === "receivables") {
          amount =
            (parseFloat(transaction.debit_amount) || 0) -
            (parseFloat(transaction.credit_amount) || 0);
        } else {
          amount =
            (parseFloat(transaction.credit_amount) || 0) -
            (parseFloat(transaction.debit_amount) || 0);
        }

        if (amount !== 0 && agingData[transaction.account_id]) {
          // Determine aging bucket
          let bucket = "current";
          if (daysDiff > 90) {
            bucket = "over90";
          } else if (daysDiff > 60) {
            bucket = "61-90";
          } else if (daysDiff > 30) {
            bucket = "31-60";
          } else if (daysDiff > 0) {
            bucket = "1-30";
          }

          agingData[transaction.account_id][bucket] += amount;
          agingData[transaction.account_id].total += amount;
        }
      });

      // Filter out accounts with zero balances and convert to array
      const agingAccounts = Object.values(agingData).filter(
        (account: any) => Math.abs(account.total) > 0.01
      );

      // Calculate totals
      const totals = {
        current: 0,
        "1-30": 0,
        "31-60": 0,
        "61-90": 0,
        over90: 0,
        total: 0,
      };

      agingAccounts.forEach((account: any) => {
        totals.current += account.current;
        totals["1-30"] += account["1-30"];
        totals["31-60"] += account["31-60"];
        totals["61-90"] += account["61-90"];
        totals.over90 += account.over90;
        totals.total += account.total;
      });

      const agingReportData = {
        reportType: "AGING_REPORT",
        asOfDate: asOfDate as string,
        reportSubType: reportType as string,
        data: {
          accounts: agingAccounts,
          buckets: ["current", "1-30", "31-60", "61-90", "over90"],
        },
        totals,
        summary: {
          totalAccounts: agingAccounts.length,
          totalOutstanding: totals.total,
          currentPercentage:
            totals.total !== 0 ? (totals.current / totals.total) * 100 : 0,
          overduePercentage:
            totals.total !== 0
              ? ((totals["1-30"] +
                  totals["31-60"] +
                  totals["61-90"] +
                  totals.over90) /
                  totals.total) *
                100
              : 0,
        },
      };

      res.json(agingReportData);
    } catch (error) {
      console.error("Error generating aging report:", error);
      res.status(500).json({ error: "Failed to generate aging report" });
    }
  })
);

export default router;
