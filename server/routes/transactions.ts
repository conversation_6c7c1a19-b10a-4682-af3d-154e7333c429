import express from "express";
import type { Request, Response } from "express";
import {
  authenticateToken,
  requireCompanyAccess,
  // requirePermission,
} from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { db } from "../config/database";
import { v4 as uuidv4 } from "uuid";
import {
  auditLogger,
  auditCrud,
  auditTransactionWorkflow,
  type AuditableRequest,
} from "../middleware/auditMiddleware";
import { auditService } from "../services/auditService";

const router = express.Router();

// Apply authentication and audit logging to all routes
router.use(authenticateToken);
router.use(auditLogger);

interface TransactionFilters {
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  page?: number;
  limit?: number;
}

interface CreateTransactionRequest {
  companyId: string;
  transactionDate: string;
  description: string;
  reference?: string;
  entries: Array<{
    accountId: string;
    description?: string;
    debitAmount: number;
    creditAmount: number;
  }>;
}

// GET /api/transactions/:companyId
router.get(
  "/:companyId",
  requireCompanyAccess,
  // requirePermission("transactions:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const {
      status,
      dateFrom,
      dateTo,
      search,
      page = 1,
      limit = 50,
    } = req.query as TransactionFilters;

    let query = db("transactions")
      .select(
        "transactions.*",
        db.raw(`
          json_agg(
            json_build_object(
              'id', te.id,
              'accountId', te.account_id,
              'accountCode', a.code,
              'accountName', a.name,
              'description', te.description,
              'debitAmount', te.debit_amount,
              'creditAmount', te.credit_amount,
              'lineNumber', te.line_number
            ) ORDER BY te.line_number
          ) as entries
        `)
      )
      .leftJoin(
        "transaction_entries as te",
        "transactions.id",
        "te.transaction_id"
      )
      .leftJoin("accounts as a", "te.account_id", "a.id")
      .where("transactions.company_id", companyId)
      .groupBy("transactions.id");

    // Apply filters
    if (status) {
      query = query.where("transactions.status", status);
    }

    if (dateFrom) {
      query = query.where("transactions.transaction_date", ">=", dateFrom);
    }

    if (dateTo) {
      query = query.where("transactions.transaction_date", "<=", dateTo);
    }

    if (search) {
      query = query.where((builder) => {
        builder
          .where("transactions.description", "ilike", `%${search}%`)
          .orWhere("transactions.transaction_number", "ilike", `%${search}%`)
          .orWhere("transactions.reference", "ilike", `%${search}%`);
      });
    }

    // Get total count
    const countQuery = db("transactions")
      .where("transactions.company_id", companyId)
      .count("* as count")
      .first();

    if (status) {
      countQuery.where("status", status);
    }

    if (dateFrom) {
      countQuery.where("transaction_date", ">=", dateFrom);
    }

    if (dateTo) {
      countQuery.where("transaction_date", "<=", dateTo);
    }

    if (search) {
      countQuery.where((builder) => {
        builder
          .where("description", "ilike", `%${search}%`)
          .orWhere("transaction_number", "ilike", `%${search}%`)
          .orWhere("reference", "ilike", `%${search}%`);
      });
    }

    // Apply pagination
    const offset = (Number(page) - 1) * Number(limit);
    query = query
      .orderBy("transactions.transaction_date", "desc")
      .orderBy("transactions.created_at", "desc")
      .limit(Number(limit))
      .offset(offset);

    const [transactions, totalResult] = await Promise.all([query, countQuery]);

    const total = Number(totalResult?.count || 0);

    res.json({
      data: transactions,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        totalPages: Math.ceil(total / Number(limit)),
      },
    });
  })
);

// GET /api/transactions/:companyId/:transactionId
router.get(
  "/:companyId/:transactionId",
  requireCompanyAccess,
  // requirePermission("transactions:read"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, transactionId } = req.params;

    const transaction = await db("transactions")
      .select(
        "transactions.*",
        db.raw(`
          json_agg(
            json_build_object(
              'id', te.id,
              'accountId', te.account_id,
              'accountCode', a.code,
              'accountName', a.name,
              'description', te.description,
              'debitAmount', te.debit_amount,
              'creditAmount', te.credit_amount,
              'lineNumber', te.line_number
            ) ORDER BY te.line_number
          ) as entries
        `)
      )
      .leftJoin(
        "transaction_entries as te",
        "transactions.id",
        "te.transaction_id"
      )
      .leftJoin("accounts as a", "te.account_id", "a.id")
      .where("transactions.company_id", companyId)
      .where("transactions.id", transactionId)
      .groupBy("transactions.id")
      .first();

    if (!transaction) {
      return res.status(404).json({ error: "Transaction not found" });
    }

    res.json(transaction);
  })
);

// POST /api/transactions
router.post(
  "/",
  // requirePermission("transactions:create"),
  asyncHandler(async (req: Request, res: Response) => {
    const {
      companyId,
      transactionDate,
      description,
      reference,
      entries,
    }: CreateTransactionRequest = req.body;

    // Validate required fields
    if (
      !companyId ||
      !transactionDate ||
      !description ||
      !entries ||
      entries.length === 0
    ) {
      return res.status(400).json({
        error:
          "Missing required fields: companyId, transactionDate, description, entries",
      });
    }

    // Validate double-entry bookkeeping
    const totalDebits = entries.reduce(
      (sum, entry) => sum + (entry.debitAmount || 0),
      0
    );
    const totalCredits = entries.reduce(
      (sum, entry) => sum + (entry.creditAmount || 0),
      0
    );

    if (Math.abs(totalDebits - totalCredits) > 0.01) {
      return res.status(400).json({
        error:
          "Transaction is not balanced. Total debits must equal total credits.",
        details: { totalDebits, totalCredits },
      });
    }

    // Validate that each entry has either debit or credit (not both)
    for (const entry of entries) {
      if (
        (entry.debitAmount > 0 && entry.creditAmount > 0) ||
        (entry.debitAmount === 0 && entry.creditAmount === 0)
      ) {
        return res.status(400).json({
          error:
            "Each entry must have either a debit amount or credit amount, but not both or neither.",
        });
      }
    }

    const trx = await db.transaction();

    try {
      // Generate transaction number
      const lastTransaction = await trx("transactions")
        .where("company_id", companyId)
        .orderBy("created_at", "desc")
        .first();

      const lastNumber = lastTransaction?.transaction_number
        ? parseInt(lastTransaction.transaction_number.replace(/\D/g, ""))
        : 0;
      const transactionNumber = `TXN-${String(lastNumber + 1).padStart(
        6,
        "0"
      )}`;

      // Create transaction
      const transactionId = uuidv4();
      await trx("transactions").insert({
        id: transactionId,
        company_id: companyId,
        transaction_number: transactionNumber,
        transaction_date: transactionDate,
        description,
        reference,
        status: "DRAFT",
        total_amount: totalDebits,
        created_by: (req as any).user?.id,
      });

      // Create transaction entries
      const transactionEntries = entries.map((entry, index) => ({
        id: uuidv4(),
        transaction_id: transactionId,
        account_id: entry.accountId,
        description: entry.description,
        debit_amount: entry.debitAmount || 0,
        credit_amount: entry.creditAmount || 0,
        line_number: index + 1,
      }));

      await trx("transaction_entries").insert(transactionEntries);

      await trx.commit();

      // Fetch the complete transaction with entries
      const completeTransaction = await db("transactions")
        .select(
          "transactions.*",
          db.raw(`
            json_agg(
              json_build_object(
                'id', te.id,
                'accountId', te.account_id,
                'accountCode', a.code,
                'accountName', a.name,
                'description', te.description,
                'debitAmount', te.debit_amount,
                'creditAmount', te.credit_amount,
                'lineNumber', te.line_number
              ) ORDER BY te.line_number
            ) as entries
          `)
        )
        .leftJoin(
          "transaction_entries as te",
          "transactions.id",
          "te.transaction_id"
        )
        .leftJoin("accounts as a", "te.account_id", "a.id")
        .where("transactions.id", transactionId)
        .groupBy("transactions.id")
        .first();

      res.status(201).json(completeTransaction);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// PUT /api/transactions/:companyId/:transactionId
router.put(
  "/:companyId/:transactionId",
  requireCompanyAccess,
  // requirePermission("transactions:update"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, transactionId } = req.params;
    const { transactionDate, description, reference, entries } = req.body;

    // Check if transaction exists and is editable
    const existingTransaction = await db("transactions")
      .where("company_id", companyId)
      .where("id", transactionId)
      .first();

    if (!existingTransaction) {
      return res.status(404).json({ error: "Transaction not found" });
    }

    if (
      existingTransaction.status === "POSTED" ||
      existingTransaction.status === "APPROVED"
    ) {
      return res.status(400).json({
        error: "Cannot edit posted or approved transactions",
      });
    }

    // Validate double-entry bookkeeping if entries are provided
    if (entries) {
      const totalDebits = entries.reduce(
        (sum: number, entry: any) => sum + (entry.debitAmount || 0),
        0
      );
      const totalCredits = entries.reduce(
        (sum: number, entry: any) => sum + (entry.creditAmount || 0),
        0
      );

      if (Math.abs(totalDebits - totalCredits) > 0.01) {
        return res.status(400).json({
          error:
            "Transaction is not balanced. Total debits must equal total credits.",
          details: { totalDebits, totalCredits },
        });
      }
    }

    const trx = await db.transaction();

    try {
      // Update transaction
      await trx("transactions")
        .where("id", transactionId)
        .update({
          transaction_date: transactionDate,
          description,
          reference,
          total_amount: entries
            ? entries.reduce(
                (sum: number, entry: any) => sum + (entry.debitAmount || 0),
                0
              )
            : existingTransaction.total_amount,
          updated_at: new Date(),
        });

      // Update entries if provided
      if (entries) {
        // Delete existing entries
        await trx("transaction_entries")
          .where("transaction_id", transactionId)
          .del();

        // Create new entries
        const transactionEntries = entries.map((entry: any, index: number) => ({
          id: uuidv4(),
          transaction_id: transactionId,
          account_id: entry.accountId,
          description: entry.description,
          debit_amount: entry.debitAmount || 0,
          credit_amount: entry.creditAmount || 0,
          line_number: index + 1,
        }));

        await trx("transaction_entries").insert(transactionEntries);
      }

      await trx.commit();

      // Fetch updated transaction
      const updatedTransaction = await db("transactions")
        .select(
          "transactions.*",
          db.raw(`
            json_agg(
              json_build_object(
                'id', te.id,
                'accountId', te.account_id,
                'accountCode', a.code,
                'accountName', a.name,
                'description', te.description,
                'debitAmount', te.debit_amount,
                'creditAmount', te.credit_amount,
                'lineNumber', te.line_number
              ) ORDER BY te.line_number
            ) as entries
          `)
        )
        .leftJoin(
          "transaction_entries as te",
          "transactions.id",
          "te.transaction_id"
        )
        .leftJoin("accounts as a", "te.account_id", "a.id")
        .where("transactions.id", transactionId)
        .groupBy("transactions.id")
        .first();

      res.json(updatedTransaction);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// DELETE /api/transactions/:companyId/:transactionId
router.delete(
  "/:companyId/:transactionId",
  requireCompanyAccess,
  // requirePermission("transactions:delete"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, transactionId } = req.params;

    const transaction = await db("transactions")
      .where("company_id", companyId)
      .where("id", transactionId)
      .first();

    if (!transaction) {
      return res.status(404).json({ error: "Transaction not found" });
    }

    if (transaction.status === "POSTED" || transaction.status === "APPROVED") {
      return res.status(400).json({
        error: "Cannot delete posted or approved transactions",
      });
    }

    const trx = await db.transaction();

    try {
      // Delete transaction entries first (due to foreign key constraint)
      await trx("transaction_entries")
        .where("transaction_id", transactionId)
        .del();

      // Delete transaction
      await trx("transactions").where("id", transactionId).del();

      await trx.commit();

      res.status(204).send();
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// POST /api/transactions/:companyId/:transactionId/submit
router.post(
  "/:companyId/:transactionId/submit",
  requireCompanyAccess,
  // requirePermission("transactions:submit"),
  asyncHandler(async (req: AuditableRequest, res: Response) => {
    const { companyId, transactionId } = req.params;

    const transaction = await db("transactions")
      .where("company_id", companyId)
      .where("id", transactionId)
      .first();

    if (!transaction) {
      return res.status(404).json({ error: "Transaction not found" });
    }

    if (transaction.status !== "DRAFT") {
      return res.status(400).json({
        error: "Only draft transactions can be submitted for approval",
      });
    }

    await db("transactions").where("id", transactionId).update({
      status: "PENDING",
      updated_at: new Date(),
    });

    // Set audit data for logging
    req.auditData = {
      action: "UPDATE",
      tableName: "transactions",
      recordId: transactionId,
      description: `Transaction ${transaction.transaction_number} submitted for approval (DRAFT → PENDING)`,
      metadata: {
        transactionNumber: transaction.transaction_number,
        fromStatus: "DRAFT",
        toStatus: "PENDING",
      },
    };

    // Fetch updated transaction with entries
    const updatedTransaction = await db("transactions")
      .select(
        "transactions.*",
        db.raw(`
          json_agg(
            json_build_object(
              'id', te.id,
              'accountId', te.account_id,
              'accountCode', a.code,
              'accountName', a.name,
              'description', te.description,
              'debitAmount', te.debit_amount,
              'creditAmount', te.credit_amount,
              'lineNumber', te.line_number
            ) ORDER BY te.line_number
          ) as entries
        `)
      )
      .leftJoin(
        "transaction_entries as te",
        "transactions.id",
        "te.transaction_id"
      )
      .leftJoin("accounts as a", "te.account_id", "a.id")
      .where("transactions.id", transactionId)
      .groupBy("transactions.id")
      .first();

    res.json(updatedTransaction);
  })
);

// POST /api/transactions/:companyId/:transactionId/cancel
router.post(
  "/:companyId/:transactionId/cancel",
  requireCompanyAccess,
  // requirePermission("transactions:cancel"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, transactionId } = req.params;

    const transaction = await db("transactions")
      .where("company_id", companyId)
      .where("id", transactionId)
      .first();

    if (!transaction) {
      return res.status(404).json({ error: "Transaction not found" });
    }

    if (!["DRAFT", "PENDING", "APPROVED"].includes(transaction.status)) {
      return res.status(400).json({
        error: "Only draft, pending, or approved transactions can be cancelled",
      });
    }

    await db("transactions").where("id", transactionId).update({
      status: "CANCELLED",
      updated_at: new Date(),
    });

    // Fetch updated transaction with entries
    const updatedTransaction = await db("transactions")
      .select(
        "transactions.*",
        db.raw(`
          json_agg(
            json_build_object(
              'id', te.id,
              'accountId', te.account_id,
              'accountCode', a.code,
              'accountName', a.name,
              'description', te.description,
              'debitAmount', te.debit_amount,
              'creditAmount', te.credit_amount,
              'lineNumber', te.line_number
            ) ORDER BY te.line_number
          ) as entries
        `)
      )
      .leftJoin(
        "transaction_entries as te",
        "transactions.id",
        "te.transaction_id"
      )
      .leftJoin("accounts as a", "te.account_id", "a.id")
      .where("transactions.id", transactionId)
      .groupBy("transactions.id")
      .first();

    res.json(updatedTransaction);
  })
);

// POST /api/transactions/:companyId/:transactionId/approve
router.post(
  "/:companyId/:transactionId/approve",
  requireCompanyAccess,
  // requirePermission("transactions:approve"),
  asyncHandler(async (req: AuditableRequest, res: Response) => {
    const { companyId, transactionId } = req.params;

    const transaction = await db("transactions")
      .where("company_id", companyId)
      .where("id", transactionId)
      .first();

    if (!transaction) {
      return res.status(404).json({ error: "Transaction not found" });
    }

    if (transaction.status !== "PENDING") {
      return res.status(400).json({
        error: "Only pending transactions can be approved",
      });
    }

    const trx = await db.transaction();

    try {
      // Update transaction status
      await trx("transactions")
        .where("id", transactionId)
        .update({
          status: "APPROVED",
          approved_by: (req as any).user?.id,
          approved_at: new Date(),
          updated_at: new Date(),
        });

      // Generate journal entries and update account balances
      await generateJournalEntries(trx, transactionId);

      // Create audit log entry
      await createAuditLogEntry(trx, {
        transactionId,
        actionType: "TRANSACTION_APPROVED",
        userId: (req as any).user?.id,
        companyId,
        details: {
          transactionNumber: transaction.transaction_number,
          amount: transaction.total_amount,
        },
      });

      await trx.commit();
    } catch (error) {
      await trx.rollback();
      throw error;
    }

    // Fetch updated transaction
    const updatedTransaction = await db("transactions")
      .select(
        "transactions.*",
        db.raw(`
          json_agg(
            json_build_object(
              'id', te.id,
              'accountId', te.account_id,
              'accountCode', a.code,
              'accountName', a.name,
              'description', te.description,
              'debitAmount', te.debit_amount,
              'creditAmount', te.credit_amount,
              'lineNumber', te.line_number
            ) ORDER BY te.line_number
          ) as entries
        `)
      )
      .leftJoin(
        "transaction_entries as te",
        "transactions.id",
        "te.transaction_id"
      )
      .leftJoin("accounts as a", "te.account_id", "a.id")
      .where("transactions.id", transactionId)
      .groupBy("transactions.id")
      .first();

    res.json(updatedTransaction);
  })
);

// POST /api/transactions/:companyId/:transactionId/post
router.post(
  "/:companyId/:transactionId/post",
  requireCompanyAccess,
  // requirePermission("transactions:post"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, transactionId } = req.params;

    const transaction = await db("transactions")
      .where("company_id", companyId)
      .where("id", transactionId)
      .first();

    if (!transaction) {
      return res.status(404).json({ error: "Transaction not found" });
    }

    if (transaction.status !== "APPROVED") {
      return res.status(400).json({
        error: "Only approved transactions can be posted",
      });
    }

    const trx = await db.transaction();

    try {
      // Update transaction status
      await trx("transactions").where("id", transactionId).update({
        status: "POSTED",
        updated_at: new Date(),
      });

      // Create audit log entry for posting
      await createAuditLogEntry(trx, {
        transactionId,
        actionType: "TRANSACTION_POSTED",
        userId: (req as any).user?.id,
        companyId,
        details: {
          transactionNumber: transaction.transaction_number,
          amount: transaction.total_amount,
        },
      });

      await trx.commit();

      // Fetch updated transaction
      const updatedTransaction = await db("transactions")
        .select(
          "transactions.*",
          db.raw(`
            json_agg(
              json_build_object(
                'id', te.id,
                'accountId', te.account_id,
                'accountCode', a.code,
                'accountName', a.name,
                'description', te.description,
                'debitAmount', te.debit_amount,
                'creditAmount', te.credit_amount,
                'lineNumber', te.line_number
              ) ORDER BY te.line_number
            ) as entries
          `)
        )
        .leftJoin(
          "transaction_entries as te",
          "transactions.id",
          "te.transaction_id"
        )
        .leftJoin("accounts as a", "te.account_id", "a.id")
        .where("transactions.id", transactionId)
        .groupBy("transactions.id")
        .first();

      res.json(updatedTransaction);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// POST /api/transactions/:companyId/:transactionId/reverse
router.post(
  "/:companyId/:transactionId/reverse",
  requireCompanyAccess,
  // requirePermission("transactions:reverse"),
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, transactionId } = req.params;

    const transaction = await db("transactions")
      .where("company_id", companyId)
      .where("id", transactionId)
      .first();

    if (!transaction) {
      return res.status(404).json({ error: "Transaction not found" });
    }

    if (transaction.status !== "POSTED") {
      return res.status(400).json({
        error: "Only posted transactions can be reversed",
      });
    }

    const trx = await db.transaction();

    try {
      // Create reversal transaction
      const reversalId = uuidv4();
      const lastTransaction = await trx("transactions")
        .where("company_id", companyId)
        .orderBy("created_at", "desc")
        .first();

      const lastNumber = lastTransaction?.transaction_number
        ? parseInt(lastTransaction.transaction_number.replace(/\D/g, ""))
        : 0;
      const reversalNumber = `REV-${String(lastNumber + 1).padStart(6, "0")}`;

      // Create reversal transaction
      await trx("transactions").insert({
        id: reversalId,
        company_id: companyId,
        transaction_number: reversalNumber,
        transaction_date: new Date().toISOString().split("T")[0],
        description: `Reversal of ${transaction.transaction_number}: ${transaction.description}`,
        reference: `Reversal of ${transaction.transaction_number}`,
        status: "POSTED",
        total_amount: transaction.total_amount,
        created_by: (req as any).user?.id,
      });

      // Get original entries and create reversed entries
      const originalEntries = await trx("transaction_entries").where(
        "transaction_id",
        transactionId
      );

      const reversalEntries = originalEntries.map((entry, index) => ({
        id: uuidv4(),
        transaction_id: reversalId,
        account_id: entry.account_id,
        description: `Reversal: ${entry.description || ""}`,
        debit_amount: entry.credit_amount, // Swap debit and credit
        credit_amount: entry.debit_amount,
        line_number: index + 1,
      }));

      await trx("transaction_entries").insert(reversalEntries);

      // Generate journal entries for the reversal transaction
      await generateJournalEntries(trx, reversalId);

      // Update original transaction
      await trx("transactions")
        .where("id", transactionId)
        .update({
          status: "REVERSED",
          reversed_by: (req as any).user?.id,
          reversed_at: new Date(),
          reversal_transaction_id: reversalId,
          updated_at: new Date(),
        });

      // Create audit log entries
      await createAuditLogEntry(trx, {
        transactionId,
        actionType: "TRANSACTION_REVERSED",
        userId: (req as any).user?.id,
        companyId,
        details: {
          originalTransactionNumber: transaction.transaction_number,
          reversalTransactionNumber: reversalNumber,
          amount: transaction.total_amount,
          reversalId,
        },
      });

      await createAuditLogEntry(trx, {
        transactionId: reversalId,
        actionType: "REVERSAL_TRANSACTION_CREATED",
        userId: (req as any).user?.id,
        companyId,
        details: {
          originalTransactionId: transactionId,
          originalTransactionNumber: transaction.transaction_number,
          reversalTransactionNumber: reversalNumber,
          amount: transaction.total_amount,
        },
      });

      await trx.commit();

      // Fetch updated transaction
      const updatedTransaction = await db("transactions")
        .select(
          "transactions.*",
          db.raw(`
            json_agg(
              json_build_object(
                'id', te.id,
                'accountId', te.account_id,
                'accountCode', a.code,
                'accountName', a.name,
                'description', te.description,
                'debitAmount', te.debit_amount,
                'creditAmount', te.credit_amount,
                'lineNumber', te.line_number
              ) ORDER BY te.line_number
            ) as entries
          `)
        )
        .leftJoin(
          "transaction_entries as te",
          "transactions.id",
          "te.transaction_id"
        )
        .leftJoin("accounts as a", "te.account_id", "a.id")
        .where("transactions.id", transactionId)
        .groupBy("transactions.id")
        .first();

      res.json(updatedTransaction);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// Helper function to generate journal entries when transaction is approved
async function generateJournalEntries(trx: any, transactionId: string) {
  try {
    // Get transaction with entries
    const transaction = await trx("transactions")
      .where("id", transactionId)
      .first();

    const entries = await trx("transaction_entries").where(
      "transaction_id",
      transactionId
    );

    // Validate double-entry bookkeeping
    const totalDebits = entries.reduce(
      (sum: number, entry: any) => sum + parseFloat(entry.debit_amount || 0),
      0
    );
    const totalCredits = entries.reduce(
      (sum: number, entry: any) => sum + parseFloat(entry.credit_amount || 0),
      0
    );

    if (Math.abs(totalDebits - totalCredits) > 0.01) {
      throw new Error(
        `Transaction ${transaction.transaction_number} is not balanced. Debits: ${totalDebits}, Credits: ${totalCredits}`
      );
    }

    // Create general ledger entries
    for (const entry of entries) {
      await trx("general_ledger").insert({
        id: require("uuid").v4(),
        account_id: entry.account_id,
        transaction_id: transactionId,
        transaction_date: transaction.transaction_date,
        description: entry.description || transaction.description,
        debit_amount: entry.debit_amount || 0,
        credit_amount: entry.credit_amount || 0,
        currency: entry.currency || transaction.currency || "USD",
        exchange_rate: entry.exchange_rate || transaction.exchange_rate || 1,
        reference: transaction.reference,
        created_at: new Date(),
        updated_at: new Date(),
      });

      // Update account balances
      await updateAccountBalance(
        trx,
        entry.account_id,
        entry.debit_amount || 0,
        entry.credit_amount || 0
      );
    }

    console.log(
      `Generated journal entries for transaction ${transaction.transaction_number}`
    );
  } catch (error) {
    console.error("Error generating journal entries:", error);
    throw error;
  }
}

// Helper function to update account balances
async function updateAccountBalance(
  trx: any,
  accountId: string,
  debitAmount: number,
  creditAmount: number
) {
  try {
    // Get account details
    const account = await trx("accounts").where("id", accountId).first();

    if (!account) {
      throw new Error(`Account not found: ${accountId}`);
    }

    // Calculate balance change based on account type
    let balanceChange = 0;

    // For asset and expense accounts: debits increase balance, credits decrease
    if (
      account.account_type === "ASSET" ||
      account.account_type === "EXPENSE"
    ) {
      balanceChange = parseFloat(debitAmount) - parseFloat(creditAmount);
    }
    // For liability, equity, and revenue accounts: credits increase balance, debits decrease
    else if (
      account.account_type === "LIABILITY" ||
      account.account_type === "EQUITY" ||
      account.account_type === "REVENUE"
    ) {
      balanceChange = parseFloat(creditAmount) - parseFloat(debitAmount);
    }

    // Update account balance
    await trx("accounts")
      .where("id", accountId)
      .update({
        current_balance: trx.raw(
          `COALESCE(current_balance, opening_balance, 0) + ?`,
          [balanceChange]
        ),
        updated_at: new Date(),
      });

    console.log(
      `Updated balance for account ${account.code} by ${balanceChange}`
    );
  } catch (error) {
    console.error("Error updating account balance:", error);
    throw error;
  }
}

// Helper function to create audit log entries
async function createAuditLogEntry(
  trx: any,
  auditData: {
    transactionId: string;
    actionType: string;
    userId: string;
    companyId: string;
    details: any;
  }
) {
  try {
    await trx("audit_logs").insert({
      id: require("uuid").v4(),
      entity_type: "TRANSACTION",
      entity_id: auditData.transactionId,
      action: auditData.actionType,
      user_id: auditData.userId,
      company_id: auditData.companyId,
      changes: JSON.stringify(auditData.details),
      ip_address: null, // Could be passed from request
      user_agent: null, // Could be passed from request
      created_at: new Date(),
    });

    console.log(`Created audit log entry for ${auditData.actionType}`);
  } catch (error) {
    console.error("Error creating audit log entry:", error);
    // Don't throw error for audit log failures - log and continue
  }
}

export default router;
