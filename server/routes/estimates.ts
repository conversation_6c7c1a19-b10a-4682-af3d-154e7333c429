import { Router, Request, Response } from "express";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import db from "../config/database";
import { requireAuth, requireCompanyAccess } from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";

const router = Router();

// Validation schemas
const createEstimateSchema = z.object({
  companyId: z.string().uuid(),
  customerId: z.string().uuid(),
  estimateDate: z.string(),
  expiryDate: z.string().optional(),
  currency: z.string().default("USD"),
  exchangeRate: z.number().default(1),
  billingAddress: z.string().optional(),
  shippingAddress: z.string().optional(),
  notes: z.string().optional(),
  termsAndConditions: z.string().optional(),
  lineItems: z.array(
    z.object({
      inventoryItemId: z.string().uuid().optional(),
      itemCode: z.string().optional(),
      description: z.string(),
      unitOfMeasure: z.string().optional(),
      quantity: z.number(),
      unitPrice: z.number(),
      discountPercentage: z.number().default(0),
      taxRate: z.number().default(0),
      taxCode: z.string().optional(),
      notes: z.string().optional(),
    })
  ),
});

const updateEstimateSchema = createEstimateSchema
  .partial()
  .omit({ companyId: true });

const estimateFiltersSchema = z.object({
  status: z.string().optional(),
  customerId: z.string().uuid().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  expiryDateFrom: z.string().optional(),
  expiryDateTo: z.string().optional(),
  search: z.string().optional(),
  page: z
    .string()
    .transform((val) => parseInt(val) || 1)
    .default("1"),
  limit: z
    .string()
    .transform((val) => parseInt(val) || 50)
    .default("50"),
});

// GET /api/estimates/:companyId - Get all estimates
router.get(
  "/:companyId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const filters = estimateFiltersSchema.parse(req.query);

    let query = db("estimates")
      .select(
        "estimates.*",
        "contacts.name as customer_name",
        "contacts.display_name as customer_display_name",
        "users.name as created_by_name"
      )
      .leftJoin("contacts", "estimates.customer_id", "contacts.id")
      .leftJoin("users", "estimates.created_by", "users.id")
      .where("estimates.company_id", companyId);

    // Apply filters
    if (filters.status) {
      query = query.where("estimates.status", filters.status);
    }
    if (filters.customerId) {
      query = query.where("estimates.customer_id", filters.customerId);
    }
    if (filters.dateFrom) {
      query = query.where("estimates.estimate_date", ">=", filters.dateFrom);
    }
    if (filters.dateTo) {
      query = query.where("estimates.estimate_date", "<=", filters.dateTo);
    }
    if (filters.expiryDateFrom) {
      query = query.where(
        "estimates.expiry_date",
        ">=",
        filters.expiryDateFrom
      );
    }
    if (filters.expiryDateTo) {
      query = query.where("estimates.expiry_date", "<=", filters.expiryDateTo);
    }
    if (filters.search) {
      query = query.where(function () {
        this.where(
          "estimates.estimate_number",
          "ilike",
          `%${filters.search}%`
        ).orWhere("contacts.name", "ilike", `%${filters.search}%`);
      });
    }

    // Pagination
    const offset = (filters.page - 1) * filters.limit;
    const totalQuery = query.clone().clearSelect().count("* as count").first();
    const dataQuery = query
      .offset(offset)
      .limit(filters.limit)
      .orderBy("estimates.estimate_date", "desc");

    const [total, estimates] = await Promise.all([totalQuery, dataQuery]);

    res.json({
      data: estimates,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total: Number(total?.count || 0),
        pages: Math.ceil(Number(total?.count || 0) / filters.limit),
      },
    });
  })
);

// GET /api/estimates/:companyId/:estimateId - Get single estimate
router.get(
  "/:companyId/:estimateId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, estimateId } = req.params;

    const estimate = await db("estimates")
      .select(
        "estimates.*",
        "contacts.name as customer_name",
        "contacts.display_name as customer_display_name",
        "contacts.email as customer_email",
        "contacts.phone as customer_phone",
        "contacts.billing_address as customer_billing_address",
        "users.name as created_by_name"
      )
      .leftJoin("contacts", "estimates.customer_id", "contacts.id")
      .leftJoin("users", "estimates.created_by", "users.id")
      .where("estimates.company_id", companyId)
      .where("estimates.id", estimateId)
      .first();

    if (!estimate) {
      return res.status(404).json({ error: "Estimate not found" });
    }

    // Get line items
    const lineItems = await db("estimate_line_items")
      .select(
        "estimate_line_items.*",
        "inventory_items.name as inventory_item_name",
        "inventory_items.sku as inventory_item_sku"
      )
      .leftJoin(
        "inventory_items",
        "estimate_line_items.inventory_item_id",
        "inventory_items.id"
      )
      .where("estimate_id", estimateId)
      .orderBy("line_number");

    // Get attachments
    const attachments = await db("estimate_attachments")
      .select("*")
      .where("estimate_id", estimateId);

    // Get history
    const history = await db("estimate_history")
      .select("estimate_history.*", "users.name as changed_by_name")
      .leftJoin("users", "estimate_history.changed_by", "users.id")
      .where("estimate_id", estimateId)
      .orderBy("changed_at", "desc");

    // Get comments
    const comments = await db("estimate_comments")
      .select("estimate_comments.*", "users.name as created_by_name")
      .leftJoin("users", "estimate_comments.created_by", "users.id")
      .where("estimate_id", estimateId)
      .orderBy("created_at", "desc");

    res.json({
      ...estimate,
      lineItems,
      attachments,
      history,
      comments,
    });
  })
);

// POST /api/estimates - Create estimate
router.post(
  "/",
  requireAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const data = createEstimateSchema.parse(req.body);
    const userId = (req as any).user.id;

    const trx = await db.transaction();

    try {
      // Get customer information
      const customer = await trx("contacts")
        .where("id", data.customerId)
        .where("company_id", data.companyId)
        .first();

      if (!customer) {
        return res.status(404).json({ error: "Customer not found" });
      }

      // Generate estimate number
      const estimateNumber = await trx.raw(
        "SELECT generate_estimate_number(?) as number",
        [data.companyId]
      );

      // Calculate totals
      let subtotal = 0;
      let taxAmount = 0;
      let discountAmount = 0;
      const processedLineItems = data.lineItems.map((item, index) => {
        const lineSubtotal = item.quantity * item.unitPrice;
        const lineDiscountAmount =
          (lineSubtotal * item.discountPercentage) / 100;
        const lineTotal = lineSubtotal - lineDiscountAmount;
        const lineTaxAmount = (lineTotal * item.taxRate) / 100;

        subtotal += lineSubtotal;
        discountAmount += lineDiscountAmount;
        taxAmount += lineTaxAmount;

        return {
          ...item,
          lineNumber: index + 1,
          discountAmount: lineDiscountAmount,
          lineTotal,
          taxAmount: lineTaxAmount,
        };
      });

      const totalAmount = subtotal - discountAmount + taxAmount;

      // Create estimate
      const estimateId = uuidv4();
      await trx("estimates").insert({
        id: estimateId,
        company_id: data.companyId,
        customer_id: data.customerId,
        estimate_number: estimateNumber.rows[0].number,
        estimate_date: data.estimateDate,
        expiry_date: data.expiryDate,
        currency: data.currency,
        exchange_rate: data.exchangeRate,
        subtotal,
        tax_amount: taxAmount,
        discount_amount: discountAmount,
        total_amount: totalAmount,
        customer_name: customer.display_name || customer.name,
        customer_email: customer.email,
        customer_phone: customer.phone,
        customer_address: customer.billing_address,
        billing_address: data.billingAddress,
        shipping_address: data.shippingAddress,
        notes: data.notes,
        terms_and_conditions: data.termsAndConditions,
        created_by: userId,
      });

      // Create line items
      const lineItemsToInsert = processedLineItems.map((item) => ({
        id: uuidv4(),
        estimate_id: estimateId,
        line_number: item.lineNumber,
        inventory_item_id: item.inventoryItemId,
        item_code: item.itemCode,
        description: item.description,
        unit_of_measure: item.unitOfMeasure,
        quantity: item.quantity,
        unit_price: item.unitPrice,
        discount_percentage: item.discountPercentage,
        discount_amount: item.discountAmount,
        line_total: item.lineTotal,
        tax_rate: item.taxRate,
        tax_amount: item.taxAmount,
        tax_code: item.taxCode,
        notes: item.notes,
      }));

      await trx("estimate_line_items").insert(lineItemsToInsert);

      // Create history entry
      await trx("estimate_history").insert({
        id: uuidv4(),
        estimate_id: estimateId,
        old_status: null,
        new_status: "DRAFT",
        notes: "Estimate created",
        changed_by: userId,
      });

      await trx.commit();

      // Fetch the created estimate with related data
      const createdEstimate = await db("estimates")
        .select(
          "estimates.*",
          "contacts.name as customer_name",
          "contacts.display_name as customer_display_name"
        )
        .leftJoin("contacts", "estimates.customer_id", "contacts.id")
        .where("estimates.id", estimateId)
        .first();

      res.status(201).json(createdEstimate);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// PUT /api/estimates/:companyId/:estimateId - Update estimate
router.put(
  "/:companyId/:estimateId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, estimateId } = req.params;
    const data = updateEstimateSchema.parse(req.body);
    const userId = (req as any).user.id;

    const trx = await db.transaction();

    try {
      // Check if estimate exists and can be edited
      const existingEstimate = await trx("estimates")
        .where("id", estimateId)
        .where("company_id", companyId)
        .first();

      if (!existingEstimate) {
        return res.status(404).json({ error: "Estimate not found" });
      }

      if (!["DRAFT", "SENT"].includes(existingEstimate.status)) {
        return res.status(400).json({
          error: "Cannot edit estimate in current status",
        });
      }

      // Update estimate basic info
      const updateData: any = {
        updated_by: userId,
        updated_at: new Date(),
      };

      if (data.customerId) {
        const customer = await trx("contacts")
          .where("id", data.customerId)
          .where("company_id", companyId)
          .first();

        if (!customer) {
          return res.status(404).json({ error: "Customer not found" });
        }

        updateData.customer_id = data.customerId;
        updateData.customer_name = customer.display_name || customer.name;
        updateData.customer_email = customer.email;
        updateData.customer_phone = customer.phone;
        updateData.customer_address = customer.billing_address;
      }

      if (data.estimateDate) updateData.estimate_date = data.estimateDate;
      if (data.expiryDate) updateData.expiry_date = data.expiryDate;
      if (data.currency) updateData.currency = data.currency;
      if (data.exchangeRate) updateData.exchange_rate = data.exchangeRate;
      if (data.billingAddress !== undefined)
        updateData.billing_address = data.billingAddress;
      if (data.shippingAddress !== undefined)
        updateData.shipping_address = data.shippingAddress;
      if (data.notes !== undefined) updateData.notes = data.notes;
      if (data.termsAndConditions !== undefined)
        updateData.terms_and_conditions = data.termsAndConditions;

      // Handle line items if provided
      if (data.lineItems) {
        // Delete existing line items
        await trx("estimate_line_items").where("estimate_id", estimateId).del();

        // Calculate new totals
        let subtotal = 0;
        let taxAmount = 0;
        let discountAmount = 0;
        const processedLineItems = data.lineItems.map((item, index) => {
          const lineSubtotal = item.quantity * item.unitPrice;
          const lineDiscountAmount =
            (lineSubtotal * (item.discountPercentage || 0)) / 100;
          const lineTotal = lineSubtotal - lineDiscountAmount;
          const lineTaxAmount = (lineTotal * (item.taxRate || 0)) / 100;

          subtotal += lineSubtotal;
          discountAmount += lineDiscountAmount;
          taxAmount += lineTaxAmount;

          return {
            ...item,
            lineNumber: index + 1,
            discountAmount: lineDiscountAmount,
            lineTotal,
            taxAmount: lineTaxAmount,
          };
        });

        const totalAmount = subtotal - discountAmount + taxAmount;

        // Update totals
        updateData.subtotal = subtotal;
        updateData.tax_amount = taxAmount;
        updateData.discount_amount = discountAmount;
        updateData.total_amount = totalAmount;

        // Insert new line items
        const lineItemsToInsert = processedLineItems.map((item) => ({
          id: uuidv4(),
          estimate_id: estimateId,
          line_number: item.lineNumber,
          inventory_item_id: item.inventoryItemId,
          item_code: item.itemCode,
          description: item.description,
          unit_of_measure: item.unitOfMeasure,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          discount_percentage: item.discountPercentage || 0,
          discount_amount: item.discountAmount,
          line_total: item.lineTotal,
          tax_rate: item.taxRate || 0,
          tax_amount: item.taxAmount,
          tax_code: item.taxCode,
          notes: item.notes,
        }));

        await trx("estimate_line_items").insert(lineItemsToInsert);
      }

      // Update estimate
      await trx("estimates").where("id", estimateId).update(updateData);

      // Create history entry
      await trx("estimate_history").insert({
        id: uuidv4(),
        estimate_id: estimateId,
        old_status: existingEstimate.status,
        new_status: existingEstimate.status,
        old_amount: existingEstimate.total_amount,
        new_amount: updateData.total_amount || existingEstimate.total_amount,
        notes: "Estimate updated",
        changed_by: userId,
      });

      await trx.commit();

      // Fetch updated estimate
      const updatedEstimate = await db("estimates")
        .select(
          "estimates.*",
          "contacts.name as customer_name",
          "contacts.display_name as customer_display_name"
        )
        .leftJoin("contacts", "estimates.customer_id", "contacts.id")
        .where("estimates.id", estimateId)
        .first();

      res.json(updatedEstimate);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// POST /api/estimates/:companyId/:estimateId/status - Update estimate status
router.post(
  "/:companyId/:estimateId/status",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, estimateId } = req.params;
    const { status, notes } = req.body;
    const userId = (req as any).user.id;

    const validStatuses = [
      "DRAFT",
      "SENT",
      "VIEWED",
      "ACCEPTED",
      "DECLINED",
      "EXPIRED",
      "CONVERTED",
      "CANCELLED",
    ];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: "Invalid status" });
    }

    const trx = await db.transaction();

    try {
      const estimate = await trx("estimates")
        .where("id", estimateId)
        .where("company_id", companyId)
        .first();

      if (!estimate) {
        return res.status(404).json({ error: "Estimate not found" });
      }

      // Update status
      await trx("estimates")
        .where("id", estimateId)
        .update({
          status,
          updated_by: userId,
          updated_at: new Date(),
          ...(status === "SENT" && { sent_at: new Date() }),
          ...(status === "VIEWED" && { viewed_at: new Date() }),
          ...(status === "ACCEPTED" && { accepted_at: new Date() }),
          ...(status === "DECLINED" && { declined_at: new Date() }),
        });

      // Create history entry
      await trx("estimate_history").insert({
        id: uuidv4(),
        estimate_id: estimateId,
        old_status: estimate.status,
        new_status: status,
        notes: notes || `Status changed to ${status}`,
        changed_by: userId,
      });

      await trx.commit();

      res.json({
        message: "Estimate status updated successfully",
        status,
      });
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

// DELETE /api/estimates/:companyId/:estimateId - Delete estimate
router.delete(
  "/:companyId/:estimateId",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, estimateId } = req.params;

    const estimate = await db("estimates")
      .where("id", estimateId)
      .where("company_id", companyId)
      .first();

    if (!estimate) {
      return res.status(404).json({ error: "Estimate not found" });
    }

    if (!["DRAFT", "CANCELLED"].includes(estimate.status)) {
      return res.status(400).json({
        error: "Cannot delete estimate in current status",
      });
    }

    await db("estimates").where("id", estimateId).del();

    res.json({ message: "Estimate deleted successfully" });
  })
);

// POST /api/estimates/:companyId/:estimateId/convert-to-invoice - Convert to invoice
router.post(
  "/:companyId/:estimateId/convert-to-invoice",
  requireAuth,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId, estimateId } = req.params;
    const userId = (req as any).user.id;

    const trx = await db.transaction();

    try {
      const estimate = await trx("estimates")
        .where("id", estimateId)
        .where("company_id", companyId)
        .first();

      if (!estimate) {
        return res.status(404).json({ error: "Estimate not found" });
      }

      if (estimate.status !== "ACCEPTED") {
        return res.status(400).json({
          error: "Can only convert accepted estimates to invoices",
        });
      }

      // Get line items
      const lineItems = await trx("estimate_line_items").where(
        "estimate_id",
        estimateId
      );

      // TODO: Create invoice (when invoice system is implemented)
      // For now, just mark as converted
      await trx("estimates").where("id", estimateId).update({
        status: "CONVERTED",
        converted_at: new Date(),
        updated_by: userId,
        updated_at: new Date(),
      });

      // Create history entry
      await trx("estimate_history").insert({
        id: uuidv4(),
        estimate_id: estimateId,
        old_status: estimate.status,
        new_status: "CONVERTED",
        notes: "Estimate converted to invoice",
        changed_by: userId,
      });

      await trx.commit();

      res.json({
        message: "Estimate converted to invoice successfully",
        status: "CONVERTED",
      });
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  })
);

export default router;
