import express from "express";
import { bankReconciliationService } from "../services/bankReconciliationService";
import { advancedPermissionService } from "../services/advancedPermissionService";
import { requireAuth } from "../middleware/auth";
import { requireCompanyAccess } from "../middleware/companyAccess";
import { asyncHandler } from "../middleware/asyncHandler";

const router = express.Router();

// Middleware to check permissions
const checkReconciliationPermission = async (req: any, res: any, next: any) => {
  try {
    const permission = await advancedPermissionService.checkPermission({
      userId: req.user.id,
      resource: "bank_reconciliation",
      action: req.method === "GET" ? "read" : "write",
      context: {
        ipAddress: req.ip,
        timestamp: new Date().toISOString(),
        sessionId: req.sessionID,
      },
    });

    if (!permission.allowed) {
      return res.status(403).json({ error: permission.reason });
    }

    req.permissions = permission;
    next();
  } catch (error) {
    console.error("Permission check error:", error);
    return res.status(500).json({ error: "Permission check failed" });
  }
};

// GET /api/reconciliation/:companyId/reconciliations
router.get(
  "/:companyId/reconciliations",
  requireAuth,
  requireCompanyAccess,
  checkReconciliationPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;
    const {
      bankAccountId,
      status,
      dateFrom,
      dateTo,
      page = 1,
      limit = 20,
    } = req.query;

    const reconciliations = await bankReconciliationService.getReconciliations({
      companyId,
      bankAccountId: bankAccountId as string,
      status: status as any,
      dateFrom: dateFrom as string,
      dateTo: dateTo as string,
      page: parseInt(page as string),
      limit: parseInt(limit as string),
    });

    res.json(reconciliations);
  })
);

// POST /api/reconciliation/:companyId/reconciliations
router.post(
  "/:companyId/reconciliations",
  requireAuth,
  requireCompanyAccess,
  checkReconciliationPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;
    const {
      bankAccountId,
      statementDate,
      statementBeginningBalance,
      statementEndingBalance,
    } = req.body;

    const reconciliation = await bankReconciliationService.createReconciliation(
      {
        companyId,
        bankAccountId,
        statementDate,
        statementBeginningBalance,
        statementEndingBalance,
        reconciledBy: req.user.id,
      }
    );

    res.status(201).json(reconciliation);
  })
);

// GET /api/reconciliation/:companyId/reconciliations/:reconciliationId
router.get(
  "/:companyId/reconciliations/:reconciliationId",
  requireAuth,
  requireCompanyAccess,
  checkReconciliationPermission,
  asyncHandler(async (req, res) => {
    const { reconciliationId } = req.params;

    const data = await bankReconciliationService.getReconciliationWithMatches(
      reconciliationId
    );

    res.json(data);
  })
);

// POST /api/reconciliation/:companyId/reconciliations/:reconciliationId/match
router.post(
  "/:companyId/reconciliations/:reconciliationId/match",
  requireAuth,
  requireCompanyAccess,
  checkReconciliationPermission,
  asyncHandler(async (req, res) => {
    const { reconciliationId } = req.params;
    const { bankTransactionId, transactionId, matchType } = req.body;

    await bankReconciliationService.matchTransactions({
      reconciliationId,
      bankTransactionId,
      transactionId,
      matchType,
      matchedBy: req.user.id,
    });

    res.json({ success: true });
  })
);

// POST /api/reconciliation/:companyId/reconciliations/:reconciliationId/bulk-action
router.post(
  "/:companyId/reconciliations/:reconciliationId/bulk-action",
  requireAuth,
  requireCompanyAccess,
  checkReconciliationPermission,
  asyncHandler(async (req, res) => {
    const { reconciliationId } = req.params;
    const { action, itemIds, notes } = req.body;

    await bankReconciliationService.performBulkAction({
      action,
      itemIds,
      reconciliationId,
      notes,
    });

    res.json({ success: true });
  })
);

// POST /api/reconciliation/:companyId/reconciliations/:reconciliationId/adjustment
router.post(
  "/:companyId/reconciliations/:reconciliationId/adjustment",
  requireAuth,
  requireCompanyAccess,
  checkReconciliationPermission,
  asyncHandler(async (req, res) => {
    const { reconciliationId } = req.params;
    const { type, description, amount, accountId, reference, notes } = req.body;

    const adjustment = await bankReconciliationService.createAdjustment({
      reconciliationId,
      type,
      description,
      amount,
      accountId,
      reference,
      notes,
      createdBy: req.user.id,
    });

    res.status(201).json(adjustment);
  })
);

// PUT /api/reconciliation/:companyId/reconciliations/:reconciliationId/status
router.put(
  "/:companyId/reconciliations/:reconciliationId/status",
  requireAuth,
  requireCompanyAccess,
  checkReconciliationPermission,
  asyncHandler(async (req, res) => {
    const { reconciliationId } = req.params;
    const { status, notes } = req.body;

    await bankReconciliationService.updateReconciliationStatus({
      reconciliationId,
      status,
      notes,
      updatedBy: req.user.id,
    });

    res.json({ success: true });
  })
);

// GET /api/reconciliation/:companyId/reconciliations/:reconciliationId/suggestions
router.get(
  "/:companyId/reconciliations/:reconciliationId/suggestions",
  requireAuth,
  requireCompanyAccess,
  checkReconciliationPermission,
  asyncHandler(async (req, res) => {
    const { reconciliationId } = req.params;

    const suggestions =
      await bankReconciliationService.generateMatchingSuggestions(
        reconciliationId
      );

    res.json(suggestions);
  })
);

// POST /api/reconciliation/:companyId/reconciliations/:reconciliationId/auto-match
router.post(
  "/:companyId/reconciliations/:reconciliationId/auto-match",
  requireAuth,
  requireCompanyAccess,
  checkReconciliationPermission,
  asyncHandler(async (req, res) => {
    const { reconciliationId } = req.params;
    const { confidenceThreshold = 0.9 } = req.body;

    const results = await bankReconciliationService.performAutoMatching({
      reconciliationId,
      confidenceThreshold,
      matchedBy: req.user.id,
    });

    res.json(results);
  })
);

// GET /api/reconciliation/:companyId/rules
router.get(
  "/:companyId/rules",
  requireAuth,
  requireCompanyAccess,
  checkReconciliationPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;

    const rules = await bankReconciliationService.getReconciliationRules(
      companyId
    );

    res.json(rules);
  })
);

// POST /api/reconciliation/:companyId/rules
router.post(
  "/:companyId/rules",
  requireAuth,
  requireCompanyAccess,
  checkReconciliationPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;
    const ruleData = req.body;

    const rule = await bankReconciliationService.createReconciliationRule({
      ...ruleData,
      companyId,
      createdBy: req.user.id,
    });

    res.status(201).json(rule);
  })
);

// POST /api/reconciliation/:companyId/import-statement
router.post(
  "/:companyId/import-statement",
  requireAuth,
  requireCompanyAccess,
  checkReconciliationPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;
    const { bankAccountId, fileFormat, fileContent, fileName } = req.body;

    try {
      // Validate required fields
      if (!bankAccountId || !fileFormat || !fileContent) {
        return res.status(400).json({
          error:
            "Missing required fields: bankAccountId, fileFormat, fileContent",
        });
      }

      // Import bank statement
      const importResult = await importBankStatement({
        companyId,
        bankAccountId,
        fileFormat,
        fileContent,
        fileName,
        importedBy: req.user.id,
      });

      res.json({
        success: true,
        message: "Bank statement imported successfully",
        ...importResult,
      });
    } catch (error) {
      console.error("Bank statement import error:", error);
      res.status(500).json({
        error: "Failed to import bank statement",
        details: error.message,
      });
    }
  })
);

// POST /api/reconciliation/:companyId/auto-match
router.post(
  "/:companyId/auto-match",
  requireAuth,
  requireCompanyAccess,
  checkReconciliationPermission,
  asyncHandler(async (req, res) => {
    const { companyId } = req.params;
    const { reconciliationId, confidenceThreshold = 0.8 } = req.body;

    try {
      const matchResult = await performAutoMatching({
        reconciliationId,
        confidenceThreshold,
        matchedBy: req.user.id,
      });

      res.json({
        success: true,
        message: "Auto-matching completed",
        ...matchResult,
      });
    } catch (error) {
      console.error("Auto-matching error:", error);
      res.status(500).json({
        error: "Failed to perform auto-matching",
        details: error.message,
      });
    }
  })
);

// Helper function to import bank statement
async function importBankStatement(data: {
  companyId: string;
  bankAccountId: string;
  fileFormat: "CSV" | "OFX" | "QIF";
  fileContent: string;
  fileName?: string;
  importedBy: string;
}) {
  try {
    let transactions: any[] = [];

    // Parse file based on format
    switch (data.fileFormat) {
      case "CSV":
        transactions = parseCSVStatement(data.fileContent);
        break;
      case "OFX":
        transactions = parseOFXStatement(data.fileContent);
        break;
      case "QIF":
        transactions = parseQIFStatement(data.fileContent);
        break;
      default:
        throw new Error(`Unsupported file format: ${data.fileFormat}`);
    }

    let newTransactions = 0;
    let duplicateTransactions = 0;
    let errorTransactions = 0;

    // Process each transaction
    for (const transaction of transactions) {
      try {
        // Check for duplicates
        const existingTransaction = await db("bank_transactions")
          .where("bank_account_id", data.bankAccountId)
          .where("transaction_id", transaction.transactionId)
          .orWhere(function () {
            this.where("bank_account_id", data.bankAccountId)
              .where("amount", transaction.amount)
              .where("date", transaction.date)
              .where(
                "description",
                "like",
                `%${transaction.description.substring(0, 20)}%`
              );
          })
          .first();

        if (existingTransaction) {
          duplicateTransactions++;
          continue;
        }

        // Insert new bank transaction
        await db("bank_transactions").insert({
          id: require("uuid").v4(),
          bank_account_id: data.bankAccountId,
          company_id: data.companyId,
          transaction_id:
            transaction.transactionId ||
            `IMPORT-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          amount: transaction.amount,
          description: transaction.description,
          category: transaction.category || "Other",
          subcategory: transaction.subcategory || null,
          date: transaction.date,
          pending: false,
          merchant_name: transaction.merchantName || null,
          payment_channel: transaction.paymentChannel || "OTHER",
          is_reconciled: false,
          created_at: new Date(),
          updated_at: new Date(),
        });

        newTransactions++;
      } catch (error) {
        console.error("Error processing transaction:", error);
        errorTransactions++;
      }
    }

    return {
      totalTransactions: transactions.length,
      newTransactions,
      duplicateTransactions,
      errorTransactions,
      fileName: data.fileName,
    };
  } catch (error) {
    console.error("Bank statement import error:", error);
    throw error;
  }
}

// Helper function to parse CSV bank statement
function parseCSVStatement(csvContent: string): any[] {
  const lines = csvContent.split("\n");
  const transactions: any[] = [];

  // Skip header row
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;

    // Parse CSV line (assuming common format: Date, Description, Amount, Balance)
    const columns = line.split(",").map((col) => col.replace(/"/g, "").trim());

    if (columns.length >= 3) {
      transactions.push({
        date: new Date(columns[0]),
        description: columns[1],
        amount: parseFloat(columns[2]) || 0,
        balance: columns[3] ? parseFloat(columns[3]) : null,
        transactionId: null,
        category: "Other",
        paymentChannel: "OTHER",
      });
    }
  }

  return transactions;
}

// Helper function to parse OFX bank statement
function parseOFXStatement(ofxContent: string): any[] {
  // Simplified OFX parser - in production, use a proper OFX library
  const transactions: any[] = [];

  // Extract transaction blocks
  const transactionBlocks =
    ofxContent.match(/<STMTTRN>[\s\S]*?<\/STMTTRN>/g) || [];

  transactionBlocks.forEach((block) => {
    const dateMatch = block.match(/<DTPOSTED>(\d{8})/);
    const amountMatch = block.match(/<TRNAMT>([-\d.]+)/);
    const descMatch = block.match(/<NAME>(.*?)<\/NAME>/);
    const idMatch = block.match(/<FITID>(.*?)<\/FITID>/);

    if (dateMatch && amountMatch && descMatch) {
      const dateStr = dateMatch[1];
      const date = new Date(
        parseInt(dateStr.substr(0, 4)),
        parseInt(dateStr.substr(4, 2)) - 1,
        parseInt(dateStr.substr(6, 2))
      );

      transactions.push({
        date,
        description: descMatch[1],
        amount: parseFloat(amountMatch[1]),
        transactionId: idMatch ? idMatch[1] : null,
        category: "Other",
        paymentChannel: "OTHER",
      });
    }
  });

  return transactions;
}

// Helper function to parse QIF bank statement
function parseQIFStatement(qifContent: string): any[] {
  const transactions: any[] = [];
  const lines = qifContent.split("\n");

  let currentTransaction: any = {};

  for (const line of lines) {
    const trimmedLine = line.trim();

    if (trimmedLine === "^") {
      // End of transaction
      if (currentTransaction.date && currentTransaction.amount !== undefined) {
        transactions.push({
          ...currentTransaction,
          category: currentTransaction.category || "Other",
          paymentChannel: "OTHER",
        });
      }
      currentTransaction = {};
    } else if (trimmedLine.startsWith("D")) {
      // Date
      currentTransaction.date = new Date(trimmedLine.substring(1));
    } else if (trimmedLine.startsWith("T")) {
      // Amount
      currentTransaction.amount = parseFloat(trimmedLine.substring(1)) || 0;
    } else if (trimmedLine.startsWith("P")) {
      // Payee/Description
      currentTransaction.description = trimmedLine.substring(1);
    } else if (trimmedLine.startsWith("N")) {
      // Number/Reference
      currentTransaction.transactionId = trimmedLine.substring(1);
    } else if (trimmedLine.startsWith("L")) {
      // Category
      currentTransaction.category = trimmedLine.substring(1);
    }
  }

  return transactions;
}

// Helper function to perform auto-matching
async function performAutoMatching(data: {
  reconciliationId: string;
  confidenceThreshold: number;
  matchedBy: string;
}) {
  try {
    // Get reconciliation details
    const reconciliation = await db("bank_reconciliations")
      .where("id", data.reconciliationId)
      .first();

    if (!reconciliation) {
      throw new Error("Reconciliation not found");
    }

    // Get unreconciled bank transactions
    const bankTransactions = await db("bank_transactions")
      .where("bank_account_id", reconciliation.bank_account_id)
      .where("is_reconciled", false)
      .where("date", ">=", reconciliation.statement_date)
      .where("date", "<=", reconciliation.statement_date);

    // Get unreconciled accounting transactions
    const accountingTransactions = await db("transactions")
      .join(
        "transaction_entries",
        "transactions.id",
        "transaction_entries.transaction_id"
      )
      .where("transactions.company_id", reconciliation.company_id)
      .where("transactions.status", "POSTED")
      .whereNotExists(function () {
        this.select("*")
          .from("reconciliation_matches")
          .whereRaw("reconciliation_matches.transaction_id = transactions.id");
      })
      .select(
        "transactions.*",
        "transaction_entries.debit_amount",
        "transaction_entries.credit_amount"
      );

    let matchedCount = 0;
    let suggestedMatches = 0;

    // Perform matching
    for (const bankTx of bankTransactions) {
      const matches = findPotentialMatches(bankTx, accountingTransactions);

      for (const match of matches) {
        if (match.confidence >= data.confidenceThreshold) {
          // Auto-match high confidence matches
          await createReconciliationMatch({
            bankTransactionId: bankTx.id,
            transactionId: match.transactionId,
            matchType: "AUTO",
            confidence: match.confidence,
            matchedBy: data.matchedBy,
          });
          matchedCount++;
        } else if (match.confidence >= 0.5) {
          // Create suggestions for manual review
          suggestedMatches++;
        }
      }
    }

    return {
      matchedCount,
      suggestedMatches,
      confidenceThreshold: data.confidenceThreshold,
    };
  } catch (error) {
    console.error("Auto-matching error:", error);
    throw error;
  }
}

// Helper function to find potential matches
function findPotentialMatches(
  bankTransaction: any,
  accountingTransactions: any[]
): any[] {
  const matches: any[] = [];

  for (const accTx of accountingTransactions) {
    let confidence = 0;
    const factors: any = {};

    // Amount matching (most important)
    const bankAmount = Math.abs(bankTransaction.amount);
    const accAmount = Math.abs(accTx.debit_amount || accTx.credit_amount);

    if (Math.abs(bankAmount - accAmount) < 0.01) {
      confidence += 0.5;
      factors.amount = true;
    } else if (Math.abs(bankAmount - accAmount) < bankAmount * 0.1) {
      confidence += 0.2;
      factors.amount = false;
    }

    // Date matching (within 7 days)
    const bankDate = new Date(bankTransaction.date);
    const accDate = new Date(accTx.transaction_date);
    const daysDiff = Math.abs(
      (bankDate.getTime() - accDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (daysDiff <= 1) {
      confidence += 0.3;
      factors.date = true;
    } else if (daysDiff <= 7) {
      confidence += 0.1;
      factors.date = false;
    }

    // Description matching (fuzzy)
    const bankDesc = bankTransaction.description.toLowerCase();
    const accDesc = accTx.description.toLowerCase();

    if (bankDesc.includes(accDesc) || accDesc.includes(bankDesc)) {
      confidence += 0.2;
      factors.description = true;
    }

    if (confidence >= 0.3) {
      matches.push({
        transactionId: accTx.id,
        confidence,
        factors,
      });
    }
  }

  return matches.sort((a, b) => b.confidence - a.confidence);
}

// Helper function to create reconciliation match
async function createReconciliationMatch(data: {
  bankTransactionId: string;
  transactionId: string;
  matchType: string;
  confidence: number;
  matchedBy: string;
}) {
  await db("reconciliation_matches").insert({
    id: require("uuid").v4(),
    bank_transaction_id: data.bankTransactionId,
    transaction_id: data.transactionId,
    match_type: data.matchType,
    confidence: data.confidence,
    matching_factors: JSON.stringify({}),
    suggested_action: "AUTO_MATCH",
    is_accepted: true,
    accepted_by: data.matchedBy,
    accepted_at: new Date(),
    created_at: new Date(),
    updated_at: new Date(),
  });

  // Mark transactions as reconciled
  await db("bank_transactions")
    .where("id", data.bankTransactionId)
    .update({ is_reconciled: true, reconciled_at: new Date() });
}

export default router;
