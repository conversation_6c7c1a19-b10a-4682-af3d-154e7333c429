import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { authenticateToken, requireCompanyAccess } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { exportService } from '../services/exportService';

// Extend Request interface to include user
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    companyId: string;
  };
}

const router = Router();

// Validation schemas
const exportOptionsSchema = z.object({
  format: z.enum(['PDF', 'EXCEL', 'CSV']),
  template: z.string().optional(),
  includeCharts: z.boolean().default(false),
  includeDetails: z.boolean().default(true),
  orientation: z.enum(['portrait', 'landscape']).default('portrait'),
  paperSize: z.enum(['A4', 'Letter', 'Legal']).default('A4'),
  companyLogo: z.boolean().default(true),
  watermark: z.string().optional(),
  password: z.string().optional(),
});

const reportDataSchema = z.object({
  title: z.string(),
  subtitle: z.string().optional(),
  companyInfo: z.object({
    name: z.string(),
    address: z.string().optional(),
    phone: z.string().optional(),
    email: z.string().optional(),
    logo: z.string().optional(),
  }),
  reportDate: z.string(),
  periodInfo: z.object({
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    asOfDate: z.string().optional(),
  }).optional(),
  tables: z.array(z.object({
    headers: z.array(z.string()),
    rows: z.array(z.array(z.union([z.string(), z.number()]))),
    title: z.string().optional(),
    subtitle: z.string().optional(),
    footer: z.string().optional(),
  })),
  charts: z.array(z.object({
    type: z.enum(['bar', 'pie', 'line', 'area']),
    title: z.string(),
    data: z.array(z.any()),
    labels: z.array(z.string()),
    colors: z.array(z.string()).optional(),
  })).optional(),
  summary: z.object({
    totalAssets: z.number().optional(),
    totalLiabilities: z.number().optional(),
    totalEquity: z.number().optional(),
    netIncome: z.number().optional(),
    totalRevenue: z.number().optional(),
    totalExpenses: z.number().optional(),
  }).optional(),
  notes: z.array(z.string()).optional(),
  metadata: z.object({
    generatedBy: z.string(),
    generatedAt: z.string(),
    reportType: z.string(),
    version: z.string(),
  }).optional(),
});

const scheduleSchema = z.object({
  reportType: z.string(),
  frequency: z.enum(['DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'ANNUALLY']),
  dayOfWeek: z.number().min(0).max(6).optional(),
  dayOfMonth: z.number().min(1).max(31).optional(),
  time: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
  timezone: z.string(),
  recipients: z.array(z.string().email()),
  format: z.enum(['PDF', 'EXCEL', 'BOTH']),
  includeCharts: z.boolean(),
  password: z.string().optional(),
});

// POST /api/exports/pdf - Export report to PDF
router.post(
  '/pdf',
  authenticateToken,
  asyncHandler(async (req: Request, res: Response) => {
    const { reportData, options } = req.body;
    
    // Validate input
    const validatedReportData = reportDataSchema.parse(reportData);
    const validatedOptions = exportOptionsSchema.parse(options);
    
    try {
      const pdfBuffer = await exportService.generatePDF(validatedReportData as any, validatedOptions as any);
      
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${validatedReportData.title}.pdf"`);
      res.send(pdfBuffer);
    } catch (error) {
      console.error('PDF export failed:', error);
      res.status(500).json({ error: 'Failed to generate PDF' });
    }
  })
);

// POST /api/exports/excel - Export report to Excel
router.post(
  '/excel',
  authenticateToken,
  asyncHandler(async (req: Request, res: Response) => {
    const { reportData, options } = req.body;
    
    // Validate input
    const validatedReportData = reportDataSchema.parse(reportData);
    const validatedOptions = exportOptionsSchema.parse(options);
    
    try {
      const excelBuffer = await exportService.generateExcel(validatedReportData as any, validatedOptions as any);
      
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${validatedReportData.title}.xlsx"`);
      res.send(excelBuffer);
    } catch (error) {
      console.error('Excel export failed:', error);
      res.status(500).json({ error: 'Failed to generate Excel' });
    }
  })
);

// POST /api/exports/report-package - Generate comprehensive report package
router.post(
  '/report-package',
  authenticateToken,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { reportType, period, options } = req.body;
    
    try {
      const reportPackage = await exportService.generateReportPackage(
        companyId,
        reportType,
        period,
        options
      );
      
      if (options.format === 'BOTH') {
        // Return ZIP file with both PDF and Excel
        res.setHeader('Content-Type', 'application/zip');
        res.setHeader('Content-Disposition', `attachment; filename="report-package-${Date.now()}.zip"`);
      } else if (options.format === 'PDF') {
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="report-package-${Date.now()}.pdf"`);
      } else {
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="report-package-${Date.now()}.xlsx"`);
      }
      
      res.send(reportPackage);
    } catch (error) {
      console.error('Report package generation failed:', error);
      res.status(500).json({ error: 'Failed to generate report package' });
    }
  })
);

// POST /api/exports/schedule - Schedule automated report generation
router.post(
  '/schedule',
  authenticateToken,
  requireCompanyAccess,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { companyId } = req.params;
    const { schedule } = req.body;
    
    // Validate input
    const validatedSchedule = scheduleSchema.parse(schedule);
    
    try {
      const result = await exportService.scheduleReport(companyId, validatedSchedule, req.user!.id);
      res.json(result);
    } catch (error) {
      console.error('Report scheduling failed:', error);
      res.status(500).json({ error: 'Failed to schedule report' });
    }
  })
);

// GET /api/exports/schedules/:companyId - Get scheduled reports
router.get(
  '/schedules/:companyId',
  authenticateToken,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    
    try {
      const schedules = await exportService.getScheduledReports(companyId);
      res.json(schedules);
    } catch (error) {
      console.error('Failed to get scheduled reports:', error);
      res.status(500).json({ error: 'Failed to get scheduled reports' });
    }
  })
);

// DELETE /api/exports/schedules/:scheduleId - Delete scheduled report
router.delete(
  '/schedules/:scheduleId',
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { scheduleId } = req.params;
    
    try {
      await exportService.deleteScheduledReport(scheduleId, req.user!.id);
      res.json({ message: 'Scheduled report deleted successfully' });
    } catch (error) {
      console.error('Failed to delete scheduled report:', error);
      res.status(500).json({ error: 'Failed to delete scheduled report' });
    }
  })
);

// GET /api/exports/templates - Get export templates
router.get(
  '/templates',
  authenticateToken,
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const templates = await exportService.getExportTemplates();
      res.json(templates);
    } catch (error) {
      console.error('Failed to get export templates:', error);
      res.status(500).json({ error: 'Failed to get export templates' });
    }
  })
);

// POST /api/exports/templates - Create custom export template
router.post(
  '/templates',
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { template } = req.body;
    
    try {
      const result = await exportService.createTemplate(template, req.user!.id);
      res.json(result);
    } catch (error) {
      console.error('Failed to create template:', error);
      res.status(500).json({ error: 'Failed to create template' });
    }
  })
);

// GET /api/exports/history/:companyId - Get export history
router.get(
  '/history/:companyId',
  authenticateToken,
  requireCompanyAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { companyId } = req.params;
    const { page = 1, limit = 20 } = req.query;
    
    try {
      const history = await exportService.getExportHistory(
        companyId,
        parseInt(page as string),
        parseInt(limit as string)
      );
      res.json(history);
    } catch (error) {
      console.error('Failed to get export history:', error);
      res.status(500).json({ error: 'Failed to get export history' });
    }
  })
);

export default router;
