#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix primary button colors in Workflows page
 * This script will update all primary buttons to use blue background consistently
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, '../../src/pages/Workflows.tsx');

function fixWorkflowsPrimaryButtons() {
  console.log('🔧 Fixing primary button colors in Workflows page...\n');

  try {
    let content = fs.readFileSync(filePath, 'utf8');

    // Define replacements for primary buttons
    const replacements = [
      // Primary button variations - convert to blue
      { 
        from: /bg-primary-600 hover:bg-primary-700/g, 
        to: 'bg-blue-600 hover:bg-blue-700' 
      },
      { 
        from: /bg-primary-500 hover:bg-primary-600/g, 
        to: 'bg-blue-600 hover:bg-blue-700' 
      },
      { 
        from: /focus:ring-primary-500/g, 
        to: 'focus:ring-blue-500' 
      },
      { 
        from: /focus:border-primary-500/g, 
        to: 'focus:border-blue-500' 
      },
      { 
        from: /text-primary-600/g, 
        to: 'text-blue-600' 
      },
      { 
        from: /border-primary-600/g, 
        to: 'border-blue-600' 
      },
      
      // Green buttons that should be blue primary buttons
      { 
        from: /bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2/g, 
        to: 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2' 
      },
      { 
        from: /bg-green-500 hover:bg-green-600/g, 
        to: 'bg-blue-600 hover:bg-blue-700' 
      },
      
      // Indigo buttons that should be blue
      { 
        from: /bg-indigo-600 hover:bg-indigo-700/g, 
        to: 'bg-blue-600 hover:bg-blue-700' 
      },
      
      // Common button patterns
      { 
        from: /className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"/g, 
        to: 'className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"' 
      },
      
      // Create/Save button patterns
      { 
        from: /className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"/g, 
        to: 'className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"' 
      },
      
      { 
        from: /className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"/g, 
        to: 'className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"' 
      },
      
      // Button with focus states
      { 
        from: /className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"/g, 
        to: 'className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"' 
      },
      
      // Full width buttons
      { 
        from: /className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"/g, 
        to: 'className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"' 
      },
    ];

    // Apply replacements
    let changeCount = 0;
    replacements.forEach(({ from, to }) => {
      const matches = content.match(from);
      if (matches) {
        content = content.replace(from, to);
        changeCount += matches.length;
        console.log(`✅ Applied button color fix (${matches.length} occurrences)`);
      }
    });

    // Write the updated content back to the file
    fs.writeFileSync(filePath, content, 'utf8');

    console.log(`\n🎉 Successfully updated Workflows.tsx!`);
    console.log(`📊 Total button changes made: ${changeCount}`);
    console.log(`📁 File: ${filePath}`);

  } catch (error) {
    console.error('❌ Error fixing Workflows primary buttons:', error);
    process.exit(1);
  }
}

// Run the fix
fixWorkflowsPrimaryButtons();
