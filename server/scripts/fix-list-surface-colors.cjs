#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to fix surface colors in all list components
 * This script will ensure all lists have proper dark mode surface colors for readability
 */

const fs = require('fs');
const path = require('path');

function fixListSurfaceColors() {
  console.log('🔧 Fixing surface colors in all list components...\n');

  // Define the specific list components that need fixing
  const listComponents = [
    { name: 'Chart of Accounts', path: 'src/pages/accounts/ChartOfAccounts.tsx' },
    { name: 'Account Tree', path: 'src/components/accounts/AccountTree.tsx' },
    { name: 'Transaction List', path: 'src/components/transactions/TransactionList.tsx' },
    { name: 'Transactions Page', path: 'src/pages/transactions/Transactions.tsx' },
    { name: 'Contact List', path: 'src/components/contacts/ContactList.tsx' },
    { name: 'Contacts Page', path: 'src/pages/contacts/Contacts.tsx' },
    { name: 'Invoice List', path: 'src/components/invoices/InvoiceList.tsx' },
    { name: 'Invoices Page', path: 'src/pages/invoices/Invoices.tsx' },
    { name: 'Tax Rate List', path: 'src/components/tax/TaxRateList.tsx' },
    { name: 'Tax Category Management', path: 'src/components/tax/TaxCategoryManagement.tsx' },
    { name: 'Tax Management Page', path: 'src/pages/admin/TaxManagement.tsx' },
    { name: 'User List', path: 'src/components/admin/UserList.tsx' },
    { name: 'User Management Page', path: 'src/pages/admin/UserManagement.tsx' },
    { name: 'Role Management', path: 'src/components/admin/RoleManagement.tsx' }
  ];

  // Define comprehensive replacements for list surface colors
  const replacements = [
    // Table and list containers
    { from: /className="([^"]*\s+)?overflow-hidden(\s+[^"]*)?"/g, 
      to: (match) => {
        if (match.includes('dark:bg-gray-800')) return match;
        return match.replace('overflow-hidden', 'overflow-hidden bg-white dark:bg-gray-800');
      }
    },
    
    // Table wrapper divs
    { from: /className="([^"]*\s+)?shadow(\s+[^"]*)?overflow-hidden(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800')) return match;
        return match.replace('shadow', 'shadow bg-white dark:bg-gray-800');
      }
    },
    
    // Table elements
    { from: /className="([^"]*\s+)?min-w-full(\s+[^"]*)?divide-y(\s+[^"]*)?divide-gray-200(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800') && match.includes('dark:divide-gray-700')) return match;
        let result = match.replace('divide-gray-200', 'divide-gray-200 dark:divide-gray-700');
        if (!result.includes('bg-white')) {
          result = result.replace('min-w-full', 'min-w-full bg-white dark:bg-gray-800');
        }
        return result;
      }
    },
    
    // Table headers
    { from: /className="([^"]*\s+)?bg-gray-50(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-700')) return match;
        return match.replace('bg-gray-50', 'bg-gray-50 dark:bg-gray-700');
      }
    },
    
    // Table rows
    { from: /className="([^"]*\s+)?bg-white(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800')) return match;
        return match.replace('bg-white', 'bg-white dark:bg-gray-800');
      }
    },
    
    // Alternating row colors
    { from: /className="([^"]*\s+)?even:bg-gray-50(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:even:bg-gray-700')) return match;
        return match.replace('even:bg-gray-50', 'even:bg-gray-50 dark:even:bg-gray-700');
      }
    },
    
    { from: /className="([^"]*\s+)?odd:bg-white(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:odd:bg-gray-800')) return match;
        return match.replace('odd:bg-white', 'odd:bg-white dark:odd:bg-gray-800');
      }
    },
    
    // List item containers
    { from: /className="([^"]*\s+)?border-b(\s+[^"]*)?border-gray-200(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:border-gray-700')) return match;
        let result = match.replace('border-gray-200', 'border-gray-200 dark:border-gray-700');
        if (!result.includes('bg-white') && !result.includes('bg-gray')) {
          result = result.replace('border-b', 'border-b bg-white dark:bg-gray-800');
        }
        return result;
      }
    },
    
    // Card-style list items
    { from: /className="([^"]*\s+)?p-4(\s+[^"]*)?border(\s+[^"]*)?border-gray-200(\s+[^"]*)?rounded(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800') && match.includes('dark:border-gray-700')) return match;
        let result = match.replace('border-gray-200', 'border-gray-200 dark:border-gray-700');
        if (!result.includes('bg-white')) {
          result = result.replace('p-4', 'p-4 bg-white dark:bg-gray-800');
        }
        return result;
      }
    },
    
    // List containers with padding
    { from: /className="([^"]*\s+)?p-6(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800')) return match;
        if (match.includes('bg-white')) return match;
        return match.replace('p-6', 'p-6 bg-white dark:bg-gray-800');
      }
    },
    
    // Grid containers
    { from: /className="([^"]*\s+)?grid(\s+[^"]*)?gap-4(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800')) return match;
        if (match.includes('bg-white') || match.includes('bg-gray')) return match;
        return match.replace('grid', 'grid bg-white dark:bg-gray-800');
      }
    },
    
    // Flex containers for lists
    { from: /className="([^"]*\s+)?flex(\s+[^"]*)?items-center(\s+[^"]*)?justify-between(\s+[^"]*)?p-4(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800')) return match;
        if (match.includes('bg-white')) return match;
        return match.replace('flex', 'flex bg-white dark:bg-gray-800');
      }
    },
    
    // Space-y containers
    { from: /className="([^"]*\s+)?space-y-4(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800')) return match;
        if (match.includes('bg-white') || match.includes('bg-gray')) return match;
        return match.replace('space-y-4', 'space-y-4 bg-white dark:bg-gray-800');
      }
    },
    
    // Hover states for list items
    { from: /className="([^"]*\s+)?hover:bg-gray-50(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:hover:bg-gray-700')) return match;
        return match.replace('hover:bg-gray-50', 'hover:bg-gray-50 dark:hover:bg-gray-700');
      }
    },
    
    // Focus states for list items
    { from: /className="([^"]*\s+)?focus:bg-gray-50(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:focus:bg-gray-700')) return match;
        return match.replace('focus:bg-gray-50', 'focus:bg-gray-50 dark:focus:bg-gray-700');
      }
    },
    
    // Selected states
    { from: /className="([^"]*\s+)?bg-blue-50(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-blue-900')) return match;
        return match.replace('bg-blue-50', 'bg-blue-50 dark:bg-blue-900/20');
      }
    }
  ];

  let totalChanges = 0;
  let processedFiles = 0;

  listComponents.forEach(component => {
    const filePath = path.join(__dirname, '../../', component.path);
    
    if (!fs.existsSync(filePath)) {
      console.log(`⏭️  Skipping ${component.name} - file not found: ${component.path}`);
      return;
    }

    console.log(`🔄 Processing ${component.name}...`);
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let fileChanges = 0;

      // Apply replacements
      replacements.forEach(({ from, to }) => {
        if (typeof to === 'function') {
          const beforeContent = content;
          content = content.replace(from, to);
          if (content !== beforeContent) {
            fileChanges++;
          }
        } else {
          const matches = content.match(from);
          if (matches) {
            content = content.replace(from, to);
            fileChanges += matches.length;
          }
        }
      });

      if (fileChanges > 0) {
        // Write the updated content back to the file
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`  ✅ Updated ${component.name} (${fileChanges} surface color fixes)`);
        totalChanges += fileChanges;
      } else {
        console.log(`  ⏭️  ${component.name} - no surface color changes needed`);
      }
      
      processedFiles++;

    } catch (error) {
      console.error(`  ❌ Error processing ${component.name}:`, error.message);
    }
  });

  console.log(`\n🎉 Successfully fixed list surface colors!`);
  console.log(`📊 Total files processed: ${processedFiles}`);
  console.log(`📊 Total surface color fixes applied: ${totalChanges}`);
  console.log(`📁 List components updated: ${listComponents.map(c => c.name).join(', ')}`);
}

// Run the fix
fixListSurfaceColors();
