#!/usr/bin/env node

/**
 * Script to force dark mode classes using alternative approaches
 * This script will use more aggressive patterns to ensure dark mode is applied
 */

const fs = require('fs');
const path = require('path');

function forceDarkModeClasses() {
  console.log('🔧 Force applying dark mode classes with alternative approaches...\n');

  // Define specific problematic files that need special attention
  const problematicFiles = [
    'src/pages/accounts/ChartOfAccounts.tsx',
    'src/pages/transactions/Transactions.tsx',
    'src/pages/contacts/Contacts.tsx',
    'src/pages/invoices/Invoices.tsx',
    'src/pages/admin/TaxManagement.tsx',
    'src/pages/admin/UserManagement.tsx',
    'src/components/accounts/AccountTree.tsx',
    'src/components/transactions/TransactionList.tsx',
    'src/components/contacts/ContactList.tsx',
    'src/components/invoices/InvoiceList.tsx',
    'src/components/tax/TaxRateList.tsx',
    'src/components/admin/UserList.tsx'
  ];

  // More aggressive replacements
  const aggressiveReplacements = [
    // Force table containers
    { from: /<div className="overflow-x-auto">/g,
      to: '<div className="overflow-x-auto bg-white dark:bg-gray-800">' },
    
    { from: /<div className="overflow-hidden shadow rounded-lg">/g,
      to: '<div className="overflow-hidden shadow rounded-lg bg-white dark:bg-gray-800">' },
    
    // Force table elements
    { from: /<table className="min-w-full divide-y divide-gray-200">/g,
      to: '<table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">' },
    
    { from: /<table className="min-w-full">/g,
      to: '<table className="min-w-full bg-white dark:bg-gray-800">' },
    
    // Force tbody
    { from: /<tbody className="bg-white divide-y divide-gray-200">/g,
      to: '<tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">' },
    
    { from: /<tbody className="divide-y divide-gray-200">/g,
      to: '<tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">' },
    
    // Force thead
    { from: /<thead className="bg-gray-50">/g,
      to: '<thead className="bg-gray-50 dark:bg-gray-700">' },
    
    // Force tr elements
    { from: /<tr className="hover:bg-gray-50">/g,
      to: '<tr className="hover:bg-gray-50 dark:hover:bg-gray-700">' },
    
    { from: /<tr>/g,
      to: '<tr className="bg-white dark:bg-gray-800">' },
    
    // Force td elements
    { from: /<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">/g,
      to: '<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">' },
    
    { from: /<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">/g,
      to: '<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">' },
    
    // Force th elements
    { from: /<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">/g,
      to: '<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">' },
    
    // Force div containers
    { from: /<div className="bg-white shadow rounded-lg">/g,
      to: '<div className="bg-white dark:bg-gray-800 shadow rounded-lg">' },
    
    { from: /<div className="bg-white p-6">/g,
      to: '<div className="bg-white dark:bg-gray-800 p-6">' },
    
    { from: /<div className="bg-white border border-gray-200 rounded-lg">/g,
      to: '<div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">' },
    
    // Force list containers
    { from: /<div className="space-y-4">/g,
      to: '<div className="space-y-4 bg-white dark:bg-gray-800">' },
    
    { from: /<div className="divide-y divide-gray-200">/g,
      to: '<div className="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">' },
    
    // Force grid containers
    { from: /<div className="grid grid-cols-1 gap-4">/g,
      to: '<div className="grid grid-cols-1 gap-4 bg-white dark:bg-gray-800">' },
    
    { from: /<div className="grid grid-cols-2 gap-4">/g,
      to: '<div className="grid grid-cols-2 gap-4 bg-white dark:bg-gray-800">' },
    
    { from: /<div className="grid grid-cols-3 gap-4">/g,
      to: '<div className="grid grid-cols-3 gap-4 bg-white dark:bg-gray-800">' },
    
    // Force text colors
    { from: /<h1 className="text-2xl font-bold text-gray-900">/g,
      to: '<h1 className="text-2xl font-bold text-gray-900 dark:text-white">' },
    
    { from: /<h2 className="text-lg font-medium text-gray-900">/g,
      to: '<h2 className="text-lg font-medium text-gray-900 dark:text-white">' },
    
    { from: /<h3 className="text-base font-medium text-gray-900">/g,
      to: '<h3 className="text-base font-medium text-gray-900 dark:text-white">' },
    
    { from: /<p className="text-sm text-gray-600">/g,
      to: '<p className="text-sm text-gray-600 dark:text-gray-400">' },
    
    { from: /<span className="text-sm text-gray-500">/g,
      to: '<span className="text-sm text-gray-500 dark:text-gray-400">' },
    
    // Force button backgrounds
    { from: /<button className="bg-white text-gray-700 border border-gray-300">/g,
      to: '<button className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600">' },
    
    // Force form elements
    { from: /<input className="block w-full px-3 py-2 border border-gray-300 rounded-md">/g,
      to: '<input className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white">' },
    
    { from: /<select className="block w-full px-3 py-2 border border-gray-300 rounded-md">/g,
      to: '<select className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white">' },
    
    // Force empty states
    { from: /<div className="text-center py-12">/g,
      to: '<div className="text-center py-12 bg-white dark:bg-gray-800">' },
    
    // Force loading states
    { from: /<div className="animate-pulse">/g,
      to: '<div className="animate-pulse bg-white dark:bg-gray-800">' }
  ];

  let totalChanges = 0;
  let processedFiles = 0;

  // Process all files in the directories
  const directories = ['src/pages', 'src/components'];
  
  directories.forEach(dir => {
    const dirPath = path.join(__dirname, '../../', dir);
    
    if (!fs.existsSync(dirPath)) {
      console.log(`⏭️  Skipping ${dir} - directory not found`);
      return;
    }

    console.log(`🔄 Processing ${dir}...`);
    
    const files = getAllFiles(dirPath, ['.tsx', '.ts']);
    
    files.forEach(filePath => {
      const relativePath = path.relative(path.join(__dirname, '../../'), filePath);
      
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        let fileChanges = 0;

        // Apply aggressive replacements
        aggressiveReplacements.forEach(({ from, to }) => {
          const matches = content.match(from);
          if (matches) {
            content = content.replace(from, to);
            fileChanges += matches.length;
          }
        });

        if (fileChanges > 0) {
          // Write the updated content back to the file
          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`  ✅ ${relativePath}: ${fileChanges} forced fixes`);
          totalChanges += fileChanges;
        }
        
        processedFiles++;

      } catch (error) {
        console.error(`  ❌ Error processing ${relativePath}:`, error.message);
      }
    });
  });

  console.log(`\n🎉 Force dark mode application completed!`);
  console.log(`📊 Total files processed: ${processedFiles}`);
  console.log(`📊 Total forced fixes applied: ${totalChanges}`);
  console.log(`📁 Directories scanned: ${directories.join(', ')}`);
}

function getAllFiles(dir, extensions) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other irrelevant directories
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          files = files.concat(getAllFiles(fullPath, extensions));
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    });
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

// Run the force dark mode fix
forceDarkModeClasses();
