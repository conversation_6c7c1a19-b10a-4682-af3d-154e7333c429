#!/usr/bin/env ts-node

import { promises as fs } from 'fs';
import path from 'path';
import { db } from '../config/database-optimized';

interface Migration {
  id: string;
  filename: string;
  content: string;
  checksum: string;
}

interface MigrationRecord {
  id: string;
  filename: string;
  checksum: string;
  executed_at: Date;
}

class MigrationRunner {
  private migrationsDir: string;

  constructor() {
    this.migrationsDir = path.join(__dirname, '../migrations');
  }

  /**
   * Initialize migration tracking table
   */
  private async initializeMigrationTable(): Promise<void> {
    await db.raw(`
      CREATE TABLE IF NOT EXISTS schema_migrations (
        id VARCHAR(255) PRIMARY KEY,
        filename VARCHAR(255) NOT NULL,
        checksum VARCHAR(64) NOT NULL,
        executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);

    await db.raw(`
      CREATE INDEX IF NOT EXISTS idx_schema_migrations_executed_at 
      ON schema_migrations(executed_at DESC)
    `);
  }

  /**
   * Get list of migration files
   */
  private async getMigrationFiles(): Promise<Migration[]> {
    try {
      const files = await fs.readdir(this.migrationsDir);
      const migrationFiles = files
        .filter(file => file.endsWith('.sql'))
        .sort();

      const migrations: Migration[] = [];

      for (const filename of migrationFiles) {
        const filePath = path.join(this.migrationsDir, filename);
        const content = await fs.readFile(filePath, 'utf-8');
        const checksum = this.calculateChecksum(content);
        const id = filename.replace('.sql', '');

        migrations.push({
          id,
          filename,
          content,
          checksum,
        });
      }

      return migrations;
    } catch (error) {
      console.error('Error reading migration files:', error);
      throw error;
    }
  }

  /**
   * Get executed migrations from database
   */
  private async getExecutedMigrations(): Promise<MigrationRecord[]> {
    try {
      const records = await db('schema_migrations')
        .select('*')
        .orderBy('executed_at', 'asc');

      return records;
    } catch (error) {
      // Table might not exist yet
      return [];
    }
  }

  /**
   * Calculate checksum for migration content
   */
  private calculateChecksum(content: string): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  /**
   * Execute a single migration
   */
  private async executeMigration(migration: Migration): Promise<void> {
    console.log(`Executing migration: ${migration.filename}`);
    
    const startTime = Date.now();
    
    try {
      // Execute migration in a transaction
      await db.transaction(async (trx) => {
        // Execute the migration SQL
        await trx.raw(migration.content);
        
        // Record the migration
        await trx('schema_migrations').insert({
          id: migration.id,
          filename: migration.filename,
          checksum: migration.checksum,
          executed_at: new Date(),
        });
      });

      const duration = Date.now() - startTime;
      console.log(`✅ Migration ${migration.filename} completed in ${duration}ms`);
    } catch (error) {
      console.error(`❌ Migration ${migration.filename} failed:`, error);
      throw error;
    }
  }

  /**
   * Validate migration checksum
   */
  private validateMigration(migration: Migration, record: MigrationRecord): boolean {
    if (migration.checksum !== record.checksum) {
      console.error(`❌ Checksum mismatch for migration ${migration.filename}`);
      console.error(`Expected: ${migration.checksum}`);
      console.error(`Found: ${record.checksum}`);
      return false;
    }
    return true;
  }

  /**
   * Run all pending migrations
   */
  public async migrate(): Promise<void> {
    try {
      console.log('🚀 Starting database migration...');
      
      // Initialize migration table
      await this.initializeMigrationTable();
      
      // Get all migrations and executed records
      const [migrations, executedRecords] = await Promise.all([
        this.getMigrationFiles(),
        this.getExecutedMigrations(),
      ]);

      console.log(`Found ${migrations.length} migration files`);
      console.log(`Found ${executedRecords.length} executed migrations`);

      // Create a map of executed migrations
      const executedMap = new Map<string, MigrationRecord>();
      executedRecords.forEach(record => {
        executedMap.set(record.id, record);
      });

      // Validate executed migrations
      for (const migration of migrations) {
        const record = executedMap.get(migration.id);
        if (record && !this.validateMigration(migration, record)) {
          throw new Error(`Migration validation failed: ${migration.filename}`);
        }
      }

      // Find pending migrations
      const pendingMigrations = migrations.filter(migration => 
        !executedMap.has(migration.id)
      );

      if (pendingMigrations.length === 0) {
        console.log('✅ No pending migrations');
        return;
      }

      console.log(`Found ${pendingMigrations.length} pending migrations:`);
      pendingMigrations.forEach(migration => {
        console.log(`  - ${migration.filename}`);
      });

      // Execute pending migrations
      for (const migration of pendingMigrations) {
        await this.executeMigration(migration);
      }

      console.log('✅ All migrations completed successfully');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Rollback last migration (if supported)
   */
  public async rollback(): Promise<void> {
    try {
      console.log('🔄 Rolling back last migration...');
      
      const lastMigration = await db('schema_migrations')
        .orderBy('executed_at', 'desc')
        .first();

      if (!lastMigration) {
        console.log('No migrations to rollback');
        return;
      }

      console.log(`Rolling back migration: ${lastMigration.filename}`);
      
      // Note: This is a basic implementation
      // In a production system, you'd want to have explicit rollback scripts
      console.warn('⚠️  Rollback functionality requires manual intervention');
      console.warn('Please create a rollback script for:', lastMigration.filename);
      
    } catch (error) {
      console.error('❌ Rollback failed:', error);
      throw error;
    }
  }

  /**
   * Show migration status
   */
  public async status(): Promise<void> {
    try {
      console.log('📊 Migration Status\n');
      
      const [migrations, executedRecords] = await Promise.all([
        this.getMigrationFiles(),
        this.getExecutedMigrations(),
      ]);

      const executedMap = new Map<string, MigrationRecord>();
      executedRecords.forEach(record => {
        executedMap.set(record.id, record);
      });

      console.log('Migration Files:');
      console.log('================');
      
      migrations.forEach(migration => {
        const record = executedMap.get(migration.id);
        const status = record ? '✅ EXECUTED' : '⏳ PENDING';
        const executedAt = record ? record.executed_at.toISOString() : '';
        
        console.log(`${status} ${migration.filename} ${executedAt}`);
      });

      const pendingCount = migrations.filter(m => !executedMap.has(m.id)).length;
      
      console.log('\nSummary:');
      console.log('========');
      console.log(`Total migrations: ${migrations.length}`);
      console.log(`Executed: ${executedRecords.length}`);
      console.log(`Pending: ${pendingCount}`);
      
    } catch (error) {
      console.error('❌ Failed to get migration status:', error);
      throw error;
    }
  }

  /**
   * Create a new migration file
   */
  public async create(name: string): Promise<void> {
    try {
      const timestamp = new Date().toISOString().replace(/[-:]/g, '').split('.')[0];
      const filename = `${timestamp}_${name.toLowerCase().replace(/\s+/g, '_')}.sql`;
      const filePath = path.join(this.migrationsDir, filename);

      const template = `-- Migration: ${name}
-- Description: Add description here
-- Date: ${new Date().toISOString().split('T')[0]}

BEGIN;

-- Add your migration SQL here

COMMIT;
`;

      await fs.writeFile(filePath, template);
      console.log(`✅ Created migration file: ${filename}`);
    } catch (error) {
      console.error('❌ Failed to create migration:', error);
      throw error;
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const migrationRunner = new MigrationRunner();

  try {
    switch (command) {
      case 'migrate':
      case 'up':
        await migrationRunner.migrate();
        break;
        
      case 'rollback':
      case 'down':
        await migrationRunner.rollback();
        break;
        
      case 'status':
        await migrationRunner.status();
        break;
        
      case 'create':
        const name = args[1];
        if (!name) {
          console.error('❌ Migration name is required');
          console.log('Usage: npm run migrate create "migration name"');
          process.exit(1);
        }
        await migrationRunner.create(name);
        break;
        
      default:
        console.log('Database Migration Tool');
        console.log('=======================');
        console.log('');
        console.log('Commands:');
        console.log('  migrate, up     - Run all pending migrations');
        console.log('  rollback, down  - Rollback last migration');
        console.log('  status          - Show migration status');
        console.log('  create <name>   - Create new migration file');
        console.log('');
        console.log('Examples:');
        console.log('  npm run migrate');
        console.log('  npm run migrate status');
        console.log('  npm run migrate create "add user preferences"');
        break;
    }
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await db.destroy();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { MigrationRunner };
