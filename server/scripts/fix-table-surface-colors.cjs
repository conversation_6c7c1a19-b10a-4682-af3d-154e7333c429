#!/usr/bin/env node

/**
 * <PERSON>ript to fix table-specific surface colors
 * This script ensures all tables have proper dark mode backgrounds and readability
 */

const fs = require('fs');
const path = require('path');

function fixTableSurfaceColors() {
  console.log('🔧 Fixing table-specific surface colors...\n');

  // Define all directories to scan for table components
  const directories = [
    'src/pages',
    'src/components'
  ];

  // Table-specific surface color replacements
  const replacements = [
    // Table wrapper with proper background
    { from: /<div className="([^"]*\s+)?overflow-x-auto(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800')) return match;
        return match.replace('overflow-x-auto', 'overflow-x-auto bg-white dark:bg-gray-800');
      }
    },
    
    // Table element itself
    { from: /<table className="([^"]*\s+)?min-w-full(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800')) return match;
        return match.replace('min-w-full', 'min-w-full bg-white dark:bg-gray-800');
      }
    },
    
    // Table body
    { from: /<tbody className="([^"]*\s+)?bg-white(\s+[^"]*)?divide-y(\s+[^"]*)?divide-gray-200(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800') && match.includes('dark:divide-gray-700')) return match;
        let result = match.replace('bg-white', 'bg-white dark:bg-gray-800');
        result = result.replace('divide-gray-200', 'divide-gray-200 dark:divide-gray-700');
        return result;
      }
    },
    
    // Table header
    { from: /<thead className="([^"]*\s+)?bg-gray-50(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-700')) return match;
        return match.replace('bg-gray-50', 'bg-gray-50 dark:bg-gray-700');
      }
    },
    
    // Table rows
    { from: /<tr className="([^"]*\s+)?hover:bg-gray-50(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:hover:bg-gray-700')) return match;
        return match.replace('hover:bg-gray-50', 'hover:bg-gray-50 dark:hover:bg-gray-700');
      }
    },
    
    // Table cells with proper text colors
    { from: /<td className="([^"]*\s+)?px-6(\s+[^"]*)?py-4(\s+[^"]*)?whitespace-nowrap(\s+[^"]*)?text-sm(\s+[^"]*)?text-gray-900(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:text-white')) return match;
        return match.replace('text-gray-900', 'text-gray-900 dark:text-white');
      }
    },
    
    { from: /<td className="([^"]*\s+)?px-6(\s+[^"]*)?py-4(\s+[^"]*)?whitespace-nowrap(\s+[^"]*)?text-sm(\s+[^"]*)?text-gray-500(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:text-gray-400')) return match;
        return match.replace('text-gray-500', 'text-gray-500 dark:text-gray-400');
      }
    },
    
    // Table header cells
    { from: /<th className="([^"]*\s+)?px-6(\s+[^"]*)?py-3(\s+[^"]*)?text-left(\s+[^"]*)?text-xs(\s+[^"]*)?font-medium(\s+[^"]*)?text-gray-500(\s+[^"]*)?uppercase(\s+[^"]*)?tracking-wider(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:text-gray-400')) return match;
        return match.replace('text-gray-500', 'text-gray-500 dark:text-gray-400');
      }
    },
    
    // List containers that act like tables
    { from: /<div className="([^"]*\s+)?divide-y(\s+[^"]*)?divide-gray-200(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:divide-gray-700')) return match;
        let result = match.replace('divide-gray-200', 'divide-gray-200 dark:divide-gray-700');
        if (!result.includes('bg-white') && !result.includes('bg-gray')) {
          result = result.replace('divide-y', 'divide-y bg-white dark:bg-gray-800');
        }
        return result;
      }
    },
    
    // Card-style list items
    { from: /<div className="([^"]*\s+)?bg-white(\s+[^"]*)?shadow(\s+[^"]*)?rounded-lg(\s+[^"]*)?p-6(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800')) return match;
        return match.replace('bg-white', 'bg-white dark:bg-gray-800');
      }
    },
    
    // Grid items
    { from: /<div className="([^"]*\s+)?bg-white(\s+[^"]*)?border(\s+[^"]*)?border-gray-200(\s+[^"]*)?rounded(\s+[^"]*)?p-4(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800') && match.includes('dark:border-gray-700')) return match;
        let result = match.replace('bg-white', 'bg-white dark:bg-gray-800');
        result = result.replace('border-gray-200', 'border-gray-200 dark:border-gray-700');
        return result;
      }
    },
    
    // Empty state containers
    { from: /<div className="([^"]*\s+)?text-center(\s+[^"]*)?py-12(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800')) return match;
        if (match.includes('bg-white') || match.includes('bg-gray')) return match;
        return match.replace('text-center', 'text-center bg-white dark:bg-gray-800');
      }
    }
  ];

  let totalChanges = 0;
  let processedFiles = 0;

  directories.forEach(dir => {
    const dirPath = path.join(__dirname, '../../', dir);
    
    if (!fs.existsSync(dirPath)) {
      console.log(`⏭️  Skipping ${dir} - directory not found`);
      return;
    }

    console.log(`🔄 Processing ${dir}...`);
    
    const files = getAllFiles(dirPath, ['.tsx', '.ts']);
    
    files.forEach(filePath => {
      const relativePath = path.relative(path.join(__dirname, '../../'), filePath);
      
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        let fileChanges = 0;

        // Apply replacements
        replacements.forEach(({ from, to }) => {
          if (typeof to === 'function') {
            const beforeContent = content;
            content = content.replace(from, to);
            if (content !== beforeContent) {
              fileChanges++;
            }
          } else {
            const matches = content.match(from);
            if (matches) {
              content = content.replace(from, to);
              fileChanges += matches.length;
            }
          }
        });

        if (fileChanges > 0) {
          // Write the updated content back to the file
          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`  ✅ ${relativePath}: ${fileChanges} table fixes`);
          totalChanges += fileChanges;
        }
        
        processedFiles++;

      } catch (error) {
        console.error(`  ❌ Error processing ${relativePath}:`, error.message);
      }
    });
  });

  console.log(`\n🎉 Table surface color fix completed!`);
  console.log(`📊 Total files processed: ${processedFiles}`);
  console.log(`📊 Total table fixes applied: ${totalChanges}`);
  console.log(`📁 Directories scanned: ${directories.join(', ')}`);
}

function getAllFiles(dir, extensions) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other irrelevant directories
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          files = files.concat(getAllFiles(fullPath, extensions));
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    });
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

// Run the table surface color fix
fixTableSurfaceColors();
