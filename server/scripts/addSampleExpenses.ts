import { db } from "../config/database";

/**
 * <PERSON><PERSON><PERSON> to add sample expense transactions for testing the expense breakdown chart
 */
async function addSampleExpenses() {
  try {
    console.log("Adding sample expense transactions...");

    // Get the first company
    const companies = await db('companies').select('*').limit(1);
    if (companies.length === 0) {
      console.log("No companies found. Please create a company first.");
      return;
    }
    const companyId = companies[0].id;
    console.log(`Using company: ${companies[0].name} (${companyId})`);

    // Get or create expense accounts
    const expenseAccounts = [
      { name: "Office Rent", code: "5001", type: "EXPENSE" },
      { name: "Utilities", code: "5002", type: "EXPENSE" },
      { name: "Marketing Expenses", code: "5003", type: "EXPENSE" },
      { name: "Cost of Goods Sold", code: "5004", type: "EXPENSE" },
      { name: "Payroll Expenses", code: "5005", type: "EXPENSE" },
      { name: "Professional Services", code: "5006", type: "EXPENSE" },
      { name: "Travel Expenses", code: "5007", type: "EXPENSE" },
    ];

    // Create accounts if they don't exist
    for (const account of expenseAccounts) {
      const existing = await db('accounts')
        .where({ company_id: companyId, code: account.code })
        .first();
      
      if (!existing) {
        await db('accounts').insert({
          id: crypto.randomUUID(),
          company_id: companyId,
          name: account.name,
          code: account.code,
          account_type: account.type,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
        console.log(`Created account: ${account.name}`);
      }
    }

    // Get all expense accounts
    const accounts = await db('accounts')
      .where({ company_id: companyId, account_type: 'EXPENSE' })
      .select('*');

    // Get cash account for the credit side
    let cashAccount = await db('accounts')
      .where({ company_id: companyId, account_type: 'ASSET' })
      .where(function() {
        this.where('name', 'like', '%cash%')
          .orWhere('name', 'like', '%bank%')
          .orWhere('code', '1000')
          .orWhere('code', '1001');
      })
      .first();

    if (!cashAccount) {
      // Create a cash account
      const cashAccountId = crypto.randomUUID();
      await db('accounts').insert({
        id: cashAccountId,
        company_id: companyId,
        name: "Cash",
        code: "1000",
        account_type: "ASSET",
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
      cashAccount = await db('accounts').where({ id: cashAccountId }).first();
      console.log("Created Cash account");
    }

    // Sample expense transactions for the current month
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    const sampleExpenses = [
      { accountName: "Office Rent", amount: 2500, day: 1 },
      { accountName: "Utilities", amount: 450, day: 5 },
      { accountName: "Marketing Expenses", amount: 1200, day: 8 },
      { accountName: "Cost of Goods Sold", amount: 3500, day: 10 },
      { accountName: "Payroll Expenses", amount: 8000, day: 15 },
      { accountName: "Professional Services", amount: 1800, day: 18 },
      { accountName: "Travel Expenses", amount: 650, day: 22 },
      { accountName: "Office Rent", amount: 2500, day: 25 }, // Second rent payment
      { accountName: "Marketing Expenses", amount: 800, day: 28 },
    ];

    let transactionCount = 0;

    for (const expense of sampleExpenses) {
      const account = accounts.find(acc => acc.name === expense.accountName);
      if (!account) {
        console.log(`Account not found: ${expense.accountName}`);
        continue;
      }

      const transactionDate = new Date(currentYear, currentMonth, expense.day);
      const transactionId = crypto.randomUUID();

      // Create transaction
      await db('transactions').insert({
        id: transactionId,
        company_id: companyId,
        transaction_date: transactionDate.toISOString().split('T')[0],
        description: `${expense.accountName} - ${transactionDate.toLocaleDateString()}`,
        reference_number: `EXP-${String(transactionCount + 1).padStart(4, '0')}`,
        status: 'POSTED',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      // Create transaction entries (double-entry bookkeeping)
      const entries = [
        {
          id: crypto.randomUUID(),
          transaction_id: transactionId,
          account_id: account.id,
          debit_amount: expense.amount,
          credit_amount: 0,
          description: `${expense.accountName} expense`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: crypto.randomUUID(),
          transaction_id: transactionId,
          account_id: cashAccount.id,
          debit_amount: 0,
          credit_amount: expense.amount,
          description: `Cash payment for ${expense.accountName}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      ];

      await db('transaction_entries').insert(entries);
      transactionCount++;

      console.log(`Created expense transaction: ${expense.accountName} - $${expense.amount}`);
    }

    console.log(`\n✅ Successfully added ${transactionCount} expense transactions!`);
    console.log("The expense breakdown chart should now display real data.");

  } catch (error) {
    console.error("Error adding sample expenses:", error);
  } finally {
    await db.destroy();
  }
}

// Run the script
addSampleExpenses();
