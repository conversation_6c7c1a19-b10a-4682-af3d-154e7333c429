#!/usr/bin/env node

/**
 * Surface Color Consistency Fix Script
 * This script ensures all surface colors are consistent and provide proper contrast
 */

const fs = require('fs');
const path = require('path');

function fixSurfaceColorConsistency() {
  console.log('🎨 Fixing surface color consistency and contrast issues...\n');

  // Define all directories to scan
  const directories = [
    'src/pages',
    'src/components'
  ];

  // Surface color consistency replacements - using simple string replacements
  const replacements = [
    // Additional missed patterns
    { from: /\bbg-white\s+p-6\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800 p-6' },
    { from: /\bbg-white\s+px-4\s+py-5\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800 px-4 py-5' },
    { from: /\bbg-white\s+px-6\s+py-4\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800 px-6 py-4' },
    { from: /\bbg-white\s+overflow-hidden\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800 overflow-hidden' },
    { from: /\bbg-white\s+rounded\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800 rounded' },

    // Table and list patterns
    { from: /\bbg-gray-50\s+px-6\s+py-3\s+text-left\b(?!\s+dark:)/g, to: 'bg-gray-50 dark:bg-gray-700 px-6 py-3 text-left' },
    { from: /\bbg-gray-50\s+text-xs\s+font-medium\s+text-gray-500\b(?!\s+dark:)/g, to: 'bg-gray-50 dark:bg-gray-700 text-xs font-medium text-gray-500 dark:text-gray-400' },

    // Form patterns
    { from: /\bbg-white\s+border\s+border-gray-300\s+rounded-md\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md dark:text-white' },
    { from: /\bbg-white\s+border\s+border-gray-300\s+px-3\s+py-2\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 px-3 py-2 dark:text-white' },

    // Button patterns
    { from: /\bbg-white\s+text-gray-700\s+border\s+border-gray-300\s+hover:bg-gray-50\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600' },

    // Card patterns
    { from: /\bbg-white\s+shadow-sm\s+rounded-lg\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800 shadow-sm rounded-lg' },
    { from: /\bbg-white\s+shadow\s+rounded-lg\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800 shadow rounded-lg' },

    // Modal patterns
    { from: /\bbg-white\s+rounded-lg\s+shadow-lg\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800 rounded-lg shadow-lg' },
    { from: /\bbg-white\s+rounded-md\s+shadow\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800 rounded-md shadow' },

    // Dropdown patterns
    { from: /\bbg-white\s+border\s+border-gray-300\s+rounded\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded dark:text-white' },

    // Additional text patterns
    { from: /\btext-gray-900\s+font-medium\b(?!\s+dark:)/g, to: 'text-gray-900 dark:text-white font-medium' },
    { from: /\btext-gray-900\s+font-semibold\b(?!\s+dark:)/g, to: 'text-gray-900 dark:text-white font-semibold' },
    { from: /\btext-gray-600\s+text-sm\b(?!\s+dark:)/g, to: 'text-gray-600 dark:text-gray-400 text-sm' },

    // Additional border patterns
    { from: /\bborder-t\s+border-gray-200\b(?!\s+dark:)/g, to: 'border-t border-gray-200 dark:border-gray-700' },
    { from: /\bborder-b\s+border-gray-200\b(?!\s+dark:)/g, to: 'border-b border-gray-200 dark:border-gray-700' },
    { from: /\bborder-l\s+border-gray-200\b(?!\s+dark:)/g, to: 'border-l border-gray-200 dark:border-gray-700' },
    { from: /\bborder-r\s+border-gray-200\b(?!\s+dark:)/g, to: 'border-r border-gray-200 dark:border-gray-700' },
  ];

  let totalChanges = 0;
  let processedFiles = 0;

  directories.forEach(dir => {
    const dirPath = path.join(__dirname, '../../', dir);
    
    if (!fs.existsSync(dirPath)) {
      console.log(`⏭️  Skipping ${dir} - directory not found`);
      return;
    }

    console.log(`🔄 Processing ${dir}...`);
    
    const files = getAllFiles(dirPath, ['.tsx', '.ts']);
    
    files.forEach(filePath => {
      const relativePath = path.relative(path.join(__dirname, '../../'), filePath);
      
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        let fileChanges = 0;
        let originalContent = content;

        // Apply replacements
        replacements.forEach(({ from, to }) => {
          const matches = content.match(from);
          if (matches) {
            content = content.replace(from, to);
            fileChanges += matches.length;
          }
        });

        if (fileChanges > 0) {
          // Write the updated content back to the file
          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`  ✅ ${relativePath}: ${fileChanges} consistency fixes`);
          totalChanges += fileChanges;
        }
        
        processedFiles++;

      } catch (error) {
        console.error(`  ❌ Error processing ${relativePath}:`, error.message);
      }
    });
  });

  console.log(`\n🎉 Surface color consistency fix completed!`);
  console.log(`📊 Total files processed: ${processedFiles}`);
  console.log(`📊 Total consistency fixes applied: ${totalChanges}`);
  console.log(`📁 Directories scanned: ${directories.join(', ')}`);
}

function getAllFiles(dir, extensions) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other irrelevant directories
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          files = files.concat(getAllFiles(fullPath, extensions));
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    });
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

// Run the surface color consistency fix
fixSurfaceColorConsistency();
