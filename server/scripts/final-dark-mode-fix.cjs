#!/usr/bin/env node

/**
 * Final Dark Mode Fix Script
 * This script will fix ALL remaining white elements and ensure complete dark mode coverage
 */

const fs = require('fs');
const path = require('path');

function finalDarkModeFix() {
  console.log('🔧 Final Dark Mode Fix - Ensuring complete coverage...\n');

  // Define all directories to scan
  const directories = [
    'src/pages',
    'src/components'
  ];

  // Final comprehensive replacements
  const replacements = [
    // Any remaining bg-white without dark mode
    { from: /className="([^"]*\s+)?bg-white(\s+[^"]*)?"/g, 
      to: (match) => {
        if (match.includes('dark:bg-gray-800') || match.includes('dark:bg-gray-700')) return match;
        return match.replace('bg-white', 'bg-white dark:bg-gray-800');
      }
    },
    
    // Any remaining bg-gray-50 without dark mode
    { from: /className="([^"]*\s+)?bg-gray-50(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-700')) return match;
        return match.replace('bg-gray-50', 'bg-gray-50 dark:bg-gray-700');
      }
    },
    
    // Any remaining text-gray-900 without dark mode
    { from: /className="([^"]*\s+)?text-gray-900(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:text-white')) return match;
        return match.replace('text-gray-900', 'text-gray-900 dark:text-white');
      }
    },
    
    // Any remaining text-gray-600 without dark mode
    { from: /className="([^"]*\s+)?text-gray-600(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:text-gray-400')) return match;
        return match.replace('text-gray-600', 'text-gray-600 dark:text-gray-400');
      }
    },
    
    // Any remaining text-gray-700 without dark mode
    { from: /className="([^"]*\s+)?text-gray-700(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:text-gray-300')) return match;
        return match.replace('text-gray-700', 'text-gray-700 dark:text-gray-300');
      }
    },
    
    // Any remaining border-gray-200 without dark mode
    { from: /className="([^"]*\s+)?border-gray-200(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:border-gray-700')) return match;
        return match.replace('border-gray-200', 'border-gray-200 dark:border-gray-700');
      }
    },
    
    // Any remaining border-gray-300 without dark mode
    { from: /className="([^"]*\s+)?border-gray-300(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:border-gray-600')) return match;
        return match.replace('border-gray-300', 'border-gray-300 dark:border-gray-600');
      }
    },
    
    // Any remaining hover:bg-gray-50 without dark mode
    { from: /className="([^"]*\s+)?hover:bg-gray-50(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:hover:bg-gray-700')) return match;
        return match.replace('hover:bg-gray-50', 'hover:bg-gray-50 dark:hover:bg-gray-700');
      }
    },
    
    // Form inputs that need dark mode
    { from: /className="([^"]*\s+)?bg-white(\s+[^"]*)?border(\s+[^"]*)?border-gray-300(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-700') && match.includes('dark:border-gray-600')) return match;
        let result = match.replace('bg-white', 'bg-white dark:bg-gray-700');
        result = result.replace('border-gray-300', 'border-gray-300 dark:border-gray-600');
        if (!result.includes('dark:text-white')) {
          result = result.replace('"', ' dark:text-white"');
        }
        return result;
      }
    }
  ];

  let totalChanges = 0;
  let processedFiles = 0;

  directories.forEach(dir => {
    const dirPath = path.join(__dirname, '../../', dir);
    
    if (!fs.existsSync(dirPath)) {
      console.log(`⏭️  Skipping ${dir} - directory not found`);
      return;
    }

    console.log(`🔄 Processing ${dir}...`);
    
    const files = getAllFiles(dirPath, ['.tsx', '.ts']);
    
    files.forEach(filePath => {
      const relativePath = path.relative(path.join(__dirname, '../../'), filePath);
      
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        let fileChanges = 0;
        let originalContent = content;

        // Apply replacements
        replacements.forEach(({ from, to }) => {
          const beforeContent = content;
          content = content.replace(from, to);
          if (content !== beforeContent) {
            fileChanges++;
          }
        });

        if (fileChanges > 0) {
          // Write the updated content back to the file
          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`  ✅ ${relativePath}: ${fileChanges} final fixes`);
          totalChanges += fileChanges;
        }
        
        processedFiles++;

      } catch (error) {
        console.error(`  ❌ Error processing ${relativePath}:`, error.message);
      }
    });
  });

  console.log(`\n🎉 Final dark mode fix completed!`);
  console.log(`📊 Total files processed: ${processedFiles}`);
  console.log(`📊 Total final fixes applied: ${totalChanges}`);
  console.log(`📁 Directories scanned: ${directories.join(', ')}`);
  
  if (totalChanges === 0) {
    console.log(`\n✨ Perfect! No additional fixes needed - dark mode is complete!`);
  } else {
    console.log(`\n✨ Applied ${totalChanges} final fixes to ensure complete dark mode coverage!`);
  }
}

function getAllFiles(dir, extensions) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other irrelevant directories
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          files = files.concat(getAllFiles(fullPath, extensions));
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    });
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

// Run the final dark mode fix
finalDarkModeFix();
