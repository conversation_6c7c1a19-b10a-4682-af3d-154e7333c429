#!/usr/bin/env node

/**
 * <PERSON>rip<PERSON> to fix individual list item backgrounds to match sidebar colors
 * This script will ensure all list items (tr, li, div items) use sidebar colors
 */

const fs = require('fs');
const path = require('path');

function fixListItemBackgrounds() {
  console.log('🔧 Fixing individual list item backgrounds to match sidebar colors...\n');

  // Define all directories to scan
  const directories = [
    'src/pages',
    'src/components'
  ];

  // Replacements to fix individual list item backgrounds
  const listItemReplacements = [
    // Fix table rows (tr elements) - most common list item pattern
    { from: /<tr\s+className="([^"]*\s+)?hover:bg-gray-50(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle = '', suffix = '') => {
        return `<tr className="${prefix}hover:bg-gray-50${middle}dark:bg-gray-800${suffix}"`;
      }
    },
    
    // Fix table rows with different patterns
    { from: /<tr\s+className="([^"]*\s+)?dark:bg-gray-700(\s+[^"]*)?hover:bg-gray-50(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle = '', suffix = '') => {
        return `<tr className="${prefix}dark:bg-gray-800${middle}hover:bg-gray-50${suffix}"`;
      }
    },
    
    // Fix table rows with hover states
    { from: /<tr\s+className="([^"]*\s+)?hover:bg-gray-50(\s+[^"]*)?dark:hover:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle = '', suffix = '') => {
        return `<tr className="${prefix}hover:bg-gray-50${middle}dark:hover:bg-gray-700${suffix}"`;
      }
    },
    
    // Fix list items (li elements)
    { from: /<li\s+className="([^"]*\s+)?bg-white(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle = '', suffix = '') => {
        return `<li className="${prefix}bg-white${middle}dark:bg-gray-800${suffix}"`;
      }
    },
    
    // Fix div list items
    { from: /<div\s+className="([^"]*\s+)?bg-white(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?border(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', suffix = '') => {
        return `<div className="${prefix}bg-white${middle1}dark:bg-gray-800${middle2}border${suffix}"`;
      }
    },
    
    // Fix card-style list items
    { from: /<div\s+className="([^"]*\s+)?p-4(\s+[^"]*)?bg-white(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', suffix = '') => {
        return `<div className="${prefix}p-4${middle1}bg-white${middle2}dark:bg-gray-800${suffix}"`;
      }
    },
    
    // Fix list items with hover states
    { from: /<div\s+className="([^"]*\s+)?hover:bg-gray-50(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle = '', suffix = '') => {
        return `<div className="${prefix}hover:bg-gray-50${middle}dark:bg-gray-800${suffix}"`;
      }
    },
    
    // Fix alternating row colors - even rows
    { from: /className="([^"]*\s+)?even:bg-gray-50(\s+[^"]*)?dark:even:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle = '', suffix = '') => {
        return `className="${prefix}even:bg-gray-50${middle}dark:even:bg-gray-800${suffix}"`;
      }
    },
    
    // Fix alternating row colors - odd rows
    { from: /className="([^"]*\s+)?odd:bg-white(\s+[^"]*)?dark:odd:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle = '', suffix = '') => {
        return `className="${prefix}odd:bg-white${middle}dark:odd:bg-gray-800${suffix}"`;
      }
    },
    
    // Fix any remaining tr className patterns
    { from: /className="([^"]*\s+)?dark:bg-gray-700(\s+[^"]*)?group(\s+[^"]*)?cursor-pointer(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', suffix = '') => {
        return `className="${prefix}dark:bg-gray-800${middle1}group${middle2}cursor-pointer${suffix}"`;
      }
    },
    
    // Fix list items with specific patterns
    { from: /className="([^"]*\s+)?border-b(\s+[^"]*)?border-gray-200(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', suffix = '') => {
        return `className="${prefix}border-b${middle1}border-gray-200${middle2}dark:bg-gray-800${suffix}"`;
      }
    },
    
    // Fix flex list items
    { from: /className="([^"]*\s+)?flex(\s+[^"]*)?items-center(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', suffix = '') => {
        return `className="${prefix}flex${middle1}items-center${middle2}dark:bg-gray-800${suffix}"`;
      }
    },
    
    // Fix grid list items
    { from: /className="([^"]*\s+)?grid(\s+[^"]*)?grid-cols-(\d+)(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', cols = '', middle2 = '', suffix = '') => {
        return `className="${prefix}grid${middle1}grid-cols-${cols}${middle2}dark:bg-gray-800${suffix}"`;
      }
    },
    
    // Fix any remaining dark:bg-gray-700 in list contexts
    { from: /className="([^"]*\s+)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', suffix = '') => {
        // Skip if this is a header (bg-gray-50), hover state, or focus state
        if (match.includes('bg-gray-50') || match.includes('hover:') || match.includes('focus:') || match.includes('thead')) {
          return match;
        }
        return `className="${prefix}dark:bg-gray-800${suffix}"`;
      }
    }
  ];

  let totalChanges = 0;
  let processedFiles = 0;

  directories.forEach(dir => {
    const dirPath = path.join(__dirname, '../../', dir);
    
    if (!fs.existsSync(dirPath)) {
      console.log(`⏭️  Skipping ${dir} - directory not found`);
      return;
    }

    console.log(`🔄 Processing ${dir}...`);
    
    const files = getAllFiles(dirPath, ['.tsx', '.ts']);
    
    files.forEach(filePath => {
      const relativePath = path.relative(path.join(__dirname, '../../'), filePath);
      
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        let fileChanges = 0;

        // Apply list item background replacements
        listItemReplacements.forEach(({ from, to }) => {
          if (typeof to === 'function') {
            const beforeContent = content;
            content = content.replace(from, to);
            if (content !== beforeContent) {
              fileChanges++;
            }
          } else {
            const matches = content.match(from);
            if (matches) {
              content = content.replace(from, to);
              fileChanges += matches.length;
            }
          }
        });

        if (fileChanges > 0) {
          // Write the updated content back to the file
          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`  ✅ ${relativePath}: ${fileChanges} list item fixes`);
          totalChanges += fileChanges;
        }
        
        processedFiles++;

      } catch (error) {
        console.error(`  ❌ Error processing ${relativePath}:`, error.message);
      }
    });
  });

  console.log(`\n🎉 List item background fix completed!`);
  console.log(`📊 Total files processed: ${processedFiles}`);
  console.log(`📊 Total list item fixes applied: ${totalChanges}`);
  console.log(`📁 Directories scanned: ${directories.join(', ')}`);
  console.log(`\n🎨 List Item Colors Applied:`);
  console.log(`   Individual Items: bg-white dark:bg-gray-800 (matches sidebar)`);
  console.log(`   Hover States: hover:bg-gray-50 dark:hover:bg-gray-700 (matches sidebar)`);
  console.log(`   Even/Odd Rows: even:bg-gray-50 dark:even:bg-gray-800 (consistent)`);
  console.log(`   Table Headers: bg-gray-50 dark:bg-gray-700 (contrast maintained)`);
}

function getAllFiles(dir, extensions) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other irrelevant directories
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          files = files.concat(getAllFiles(fullPath, extensions));
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    });
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

// Run the list item background fix
fixListItemBackgrounds();
