#!/usr/bin/env node

/**
 * Simple Audit System Test Script (JavaScript)
 * Tests Feature 1: Basic Database Audit Schema and Core Functionality
 */

const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  user: process.env.DB_USER || 'curtis',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'accounting_system',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 5432,
});

async function testBasicAuditFunctionality() {
  console.log('🧪 Testing Basic Audit Functionality...\n');

  try {
    // Test 1: Insert test data into audit_logs (without foreign key constraints)
    console.log('📝 Test 1: Testing audit_logs table...');
    const auditResult = await pool.query(`
      INSERT INTO audit_logs (
        table_name, record_id, action_type, new_values, user_email,
        ip_address, risk_level, reason
      ) VALUES (
        'accounts',
        '123e4567-e89b-12d3-a456-************',
        'INSERT',
        '{"name": "Test Account", "type": "ASSET", "balance": 1000.00}',
        '<EMAIL>',
        '*************',
        'LOW',
        'Testing audit functionality'
      ) RETURNING id
    `);
    console.log(`✅ Audit log created with ID: ${auditResult.rows[0].id}`);

    // Test 2: Insert test data into access_audit_logs (using existing structure)
    console.log('\n🔐 Test 2: Testing access_audit_logs table...');

    // First get a valid user_id and company_id
    const userResult = await pool.query('SELECT id FROM users LIMIT 1');
    const companyResult = await pool.query('SELECT id FROM companies LIMIT 1');

    if (userResult.rows.length === 0 || companyResult.rows.length === 0) {
      console.log('⚠️  Skipping access_audit_logs test - no users or companies found');
    } else {
      const accessResult = await pool.query(`
        INSERT INTO access_audit_logs (
          user_id, company_id, action, resource, success, ip_address
        ) VALUES (
          $1, $2, 'LOGIN', '/dashboard', true, '*************'
        ) RETURNING id
      `, [userResult.rows[0].id, companyResult.rows[0].id]);
      console.log(`✅ Access log created with ID: ${accessResult.rows[0].id}`);
    }

    // Test 3: Insert test data into financial_audit_trail
    console.log('\n💰 Test 3: Testing financial_audit_trail table...');
    const financialResult = await pool.query(`
      INSERT INTO financial_audit_trail (
        action_type, amount, currency, previous_balance, new_balance, authorization_level
      ) VALUES (
        'DEBIT',
        500.00,
        'TZS',
        1000.00,
        500.00,
        'MANAGER'
      ) RETURNING id
    `);
    console.log(`✅ Financial audit log created with ID: ${financialResult.rows[0].id}`);

    // Test 4: Query audit logs
    console.log('\n📊 Test 4: Retrieving audit logs...');
    const auditLogs = await pool.query(`
      SELECT table_name, action_type, user_email, risk_level, timestamp 
      FROM audit_logs 
      ORDER BY timestamp DESC 
      LIMIT 5
    `);
    console.log(`✅ Retrieved ${auditLogs.rows.length} audit logs:`);
    auditLogs.rows.forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.action_type} on ${log.table_name} by ${log.user_email} (Risk: ${log.risk_level})`);
    });

    // Test 5: Query access logs
    console.log('\n🔍 Test 5: Retrieving access logs...');
    const accessLogs = await pool.query(`
      SELECT action, success, ip_address, timestamp 
      FROM access_audit_logs 
      ORDER BY timestamp DESC 
      LIMIT 5
    `);
    console.log(`✅ Retrieved ${accessLogs.rows.length} access logs:`);
    accessLogs.rows.forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.action} - ${log.success ? 'SUCCESS' : 'FAILED'} from ${log.ip_address}`);
    });

    // Test 6: Query financial audit trail
    console.log('\n💳 Test 6: Retrieving financial audit trail...');
    const financialLogs = await pool.query(`
      SELECT action_type, amount, currency, authorization_level, timestamp 
      FROM financial_audit_trail 
      ORDER BY timestamp DESC 
      LIMIT 5
    `);
    console.log(`✅ Retrieved ${financialLogs.rows.length} financial audit logs:`);
    financialLogs.rows.forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.action_type} - ${log.amount} ${log.currency} (Auth: ${log.authorization_level})`);
    });

    // Test 7: Test table structure
    console.log('\n🔒 Test 7: Verifying table structures...');
    
    const auditTableInfo = await pool.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'audit_logs' 
      ORDER BY ordinal_position
    `);
    console.log(`✅ audit_logs table has ${auditTableInfo.rows.length} columns`);

    const accessTableInfo = await pool.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'access_audit_logs' 
      ORDER BY ordinal_position
    `);
    console.log(`✅ access_audit_logs table has ${accessTableInfo.rows.length} columns`);

    const financialTableInfo = await pool.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'financial_audit_trail' 
      ORDER BY ordinal_position
    `);
    console.log(`✅ financial_audit_trail table has ${financialTableInfo.rows.length} columns`);

    // Test 8: Count records
    console.log('\n📈 Test 8: Record counts...');
    const auditCount = await pool.query('SELECT COUNT(*) as count FROM audit_logs');
    const accessCount = await pool.query('SELECT COUNT(*) as count FROM access_audit_logs');
    const financialCount = await pool.query('SELECT COUNT(*) as count FROM financial_audit_trail');
    
    console.log(`✅ Record counts:`);
    console.log(`   - Audit logs: ${auditCount.rows[0].count} records`);
    console.log(`   - Access logs: ${accessCount.rows[0].count} records`);
    console.log(`   - Financial logs: ${financialCount.rows[0].count} records`);

    console.log('\n🎉 All basic audit functionality tests passed!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Database schema working correctly');
    console.log('   ✅ audit_logs table functional');
    console.log('   ✅ access_audit_logs table functional');
    console.log('   ✅ financial_audit_trail table functional');
    console.log('   ✅ Data insertion works');
    console.log('   ✅ Data retrieval works');
    console.log('   ✅ Table structures verified');
    
    console.log('\n🚀 Feature 1 (Basic Database Audit Schema) is working correctly!');
    console.log('🚀 Ready to proceed to Feature 2: Audit Service Integration');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the test
testBasicAuditFunctionality();
