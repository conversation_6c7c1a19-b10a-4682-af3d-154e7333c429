#!/usr/bin/env node

/**
 * <PERSON>ript to fix CSS overrides and specificity issues in dark mode
 * This script will find and fix more specific patterns that might be overriding dark mode classes
 */

const fs = require('fs');
const path = require('path');

function fixCSSOverridesDarkMode() {
  console.log('🔧 Fixing CSS overrides and specificity issues for dark mode...\n');

  // Define all directories to scan
  const directories = [
    'src/pages',
    'src/components'
  ];

  // More specific and comprehensive replacements to handle overrides
  const replacements = [
    // Handle className with multiple bg-white occurrences
    { from: /className="([^"]*?)bg-white([^"]*?)bg-white([^"]*?)"/g,
      to: 'className="$1bg-white dark:bg-gray-800$2$3"' },
    
    // Handle inline styles that might override (remove them for dark mode compatibility)
    { from: /style=\{\{[^}]*backgroundColor:\s*['"]white['"][^}]*\}\}/g,
      to: 'className="bg-white dark:bg-gray-800"' },
    
    // Handle very specific table patterns
    { from: /className="([^"]*?)min-w-full([^"]*?)bg-white([^"]*?)"/g,
      to: (match, prefix, middle, suffix) => {
        if (match.includes('dark:bg-gray-800')) return match;
        return `className="${prefix}min-w-full${middle}bg-white dark:bg-gray-800${suffix}"`;
      }
    },
    
    // Handle table body with specific patterns
    { from: /className="([^"]*?)bg-white([^"]*?)divide-y([^"]*?)divide-gray-200([^"]*?)"/g,
      to: (match, prefix, middle1, middle2, suffix) => {
        if (match.includes('dark:bg-gray-800') && match.includes('dark:divide-gray-700')) return match;
        let result = match.replace('bg-white', 'bg-white dark:bg-gray-800');
        result = result.replace('divide-gray-200', 'divide-gray-200 dark:divide-gray-700');
        return result;
      }
    },
    
    // Handle overflow containers
    { from: /className="([^"]*?)overflow-hidden([^"]*?)shadow([^"]*?)"/g,
      to: (match, prefix, middle, suffix) => {
        if (match.includes('dark:bg-gray-800')) return match;
        return `className="${prefix}overflow-hidden${middle}shadow bg-white dark:bg-gray-800${suffix}"`;
      }
    },
    
    // Handle flex containers that might be lists
    { from: /className="([^"]*?)flex([^"]*?)flex-col([^"]*?)space-y-([^"]*?)"/g,
      to: (match, prefix, middle1, middle2, suffix) => {
        if (match.includes('dark:bg-gray-800') || match.includes('bg-gray')) return match;
        return `className="${prefix}flex${middle1}flex-col bg-white dark:bg-gray-800${middle2}space-y-${suffix}"`;
      }
    },
    
    // Handle grid containers
    { from: /className="([^"]*?)grid([^"]*?)grid-cols-([^"]*?)gap-([^"]*?)"/g,
      to: (match, prefix, middle1, middle2, suffix) => {
        if (match.includes('dark:bg-gray-800') || match.includes('bg-gray') || match.includes('bg-white')) return match;
        return `className="${prefix}grid bg-white dark:bg-gray-800${middle1}grid-cols-${middle2}gap-${suffix}"`;
      }
    },
    
    // Handle specific list item patterns
    { from: /className="([^"]*?)p-4([^"]*?)border-b([^"]*?)border-gray-200([^"]*?)"/g,
      to: (match, prefix, middle1, middle2, suffix) => {
        if (match.includes('dark:bg-gray-800') && match.includes('dark:border-gray-700')) return match;
        let result = match.replace('border-gray-200', 'border-gray-200 dark:border-gray-700');
        if (!result.includes('bg-white') && !result.includes('bg-gray')) {
          result = result.replace('p-4', 'p-4 bg-white dark:bg-gray-800');
        }
        return result;
      }
    },
    
    // Handle card patterns with padding
    { from: /className="([^"]*?)p-6([^"]*?)rounded-lg([^"]*?)shadow([^"]*?)"/g,
      to: (match, prefix, middle1, middle2, suffix) => {
        if (match.includes('dark:bg-gray-800')) return match;
        if (!match.includes('bg-white') && !match.includes('bg-gray')) {
          return `className="${prefix}p-6 bg-white dark:bg-gray-800${middle1}rounded-lg${middle2}shadow${suffix}"`;
        }
        return match;
      }
    },
    
    // Handle specific table row patterns
    { from: /className="([^"]*?)hover:bg-gray-50([^"]*?)cursor-pointer([^"]*?)"/g,
      to: (match, prefix, middle, suffix) => {
        if (match.includes('dark:hover:bg-gray-700')) return match;
        return match.replace('hover:bg-gray-50', 'hover:bg-gray-50 dark:hover:bg-gray-700');
      }
    },
    
    // Handle text color overrides
    { from: /className="([^"]*?)text-gray-900([^"]*?)font-medium([^"]*?)"/g,
      to: (match, prefix, middle, suffix) => {
        if (match.includes('dark:text-white')) return match;
        return match.replace('text-gray-900', 'text-gray-900 dark:text-white');
      }
    },
    
    // Handle specific component patterns that might be missed
    { from: /className="([^"]*?)bg-white([^"]*?)border([^"]*?)border-gray-200([^"]*?)rounded-lg([^"]*?)"/g,
      to: (match, prefix, middle1, middle2, middle3, suffix) => {
        if (match.includes('dark:bg-gray-800') && match.includes('dark:border-gray-700')) return match;
        let result = match.replace('bg-white', 'bg-white dark:bg-gray-800');
        result = result.replace('border-gray-200', 'border-gray-200 dark:border-gray-700');
        return result;
      }
    },
    
    // Handle list containers with specific patterns
    { from: /className="([^"]*?)space-y-4([^"]*?)divide-y([^"]*?)divide-gray-200([^"]*?)"/g,
      to: (match, prefix, middle1, middle2, suffix) => {
        if (match.includes('dark:divide-gray-700')) return match;
        let result = match.replace('divide-gray-200', 'divide-gray-200 dark:divide-gray-700');
        if (!result.includes('bg-white') && !result.includes('bg-gray')) {
          result = result.replace('space-y-4', 'space-y-4 bg-white dark:bg-gray-800');
        }
        return result;
      }
    },
    
    // Handle empty state containers
    { from: /className="([^"]*?)text-center([^"]*?)py-12([^"]*?)text-gray-500([^"]*?)"/g,
      to: (match, prefix, middle1, middle2, suffix) => {
        if (match.includes('dark:text-gray-400')) return match;
        let result = match.replace('text-gray-500', 'text-gray-500 dark:text-gray-400');
        if (!result.includes('bg-white') && !result.includes('bg-gray')) {
          result = result.replace('text-center', 'text-center bg-white dark:bg-gray-800');
        }
        return result;
      }
    },
    
    // Handle loading states
    { from: /className="([^"]*?)animate-pulse([^"]*?)bg-gray-200([^"]*?)"/g,
      to: (match, prefix, middle, suffix) => {
        if (match.includes('dark:bg-gray-600')) return match;
        return match.replace('bg-gray-200', 'bg-gray-200 dark:bg-gray-600');
      }
    }
  ];

  let totalChanges = 0;
  let processedFiles = 0;

  directories.forEach(dir => {
    const dirPath = path.join(__dirname, '../../', dir);
    
    if (!fs.existsSync(dirPath)) {
      console.log(`⏭️  Skipping ${dir} - directory not found`);
      return;
    }

    console.log(`🔄 Processing ${dir}...`);
    
    const files = getAllFiles(dirPath, ['.tsx', '.ts']);
    
    files.forEach(filePath => {
      const relativePath = path.relative(path.join(__dirname, '../../'), filePath);
      
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        let fileChanges = 0;

        // Apply replacements
        replacements.forEach(({ from, to }) => {
          if (typeof to === 'function') {
            const beforeContent = content;
            content = content.replace(from, to);
            if (content !== beforeContent) {
              fileChanges++;
            }
          } else {
            const matches = content.match(from);
            if (matches) {
              content = content.replace(from, to);
              fileChanges += matches.length;
            }
          }
        });

        if (fileChanges > 0) {
          // Write the updated content back to the file
          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`  ✅ ${relativePath}: ${fileChanges} override fixes`);
          totalChanges += fileChanges;
        }
        
        processedFiles++;

      } catch (error) {
        console.error(`  ❌ Error processing ${relativePath}:`, error.message);
      }
    });
  });

  console.log(`\n🎉 CSS override fix completed!`);
  console.log(`📊 Total files processed: ${processedFiles}`);
  console.log(`📊 Total override fixes applied: ${totalChanges}`);
  console.log(`📁 Directories scanned: ${directories.join(', ')}`);
}

function getAllFiles(dir, extensions) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other irrelevant directories
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          files = files.concat(getAllFiles(fullPath, extensions));
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    });
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

// Run the CSS override fix
fixCSSOverridesDarkMode();
