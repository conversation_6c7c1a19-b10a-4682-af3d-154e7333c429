#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix dark mode classes in all Settings components
 * This script will update all hardcoded light mode classes to support dark mode
 */

const fs = require('fs');
const path = require('path');

const settingsDir = path.join(__dirname, '../../src/components/settings');

function fixSettingsDarkMode() {
  console.log('🔧 Fixing all Settings components dark mode classes...\n');

  // Define replacements for dark mode
  const replacements = [
    // Headers and text
    { from: /\btext-gray-900\b/g, to: 'text-gray-900 dark:text-white' },
    { from: /\btext-gray-600\b/g, to: 'text-gray-600 dark:text-gray-400' },
    { from: /\btext-gray-700\b/g, to: 'text-gray-700 dark:text-gray-300' },
    { from: /\btext-gray-500\b/g, to: 'text-gray-500 dark:text-gray-400' },
    
    // Backgrounds
    { from: /\bbg-white shadow\b/g, to: 'bg-white dark:bg-gray-800 shadow' },
    { from: /\bbg-white hover:bg-gray-50\b/g, to: 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700' },
    { from: /\bbg-gray-50\b/g, to: 'bg-gray-50 dark:bg-gray-700' },
    { from: /\bbg-green-100\b/g, to: 'bg-green-100 dark:bg-green-900/20' },
    { from: /\bbg-red-50\b/g, to: 'bg-red-50 dark:bg-red-900/20' },
    { from: /\bbg-yellow-50\b/g, to: 'bg-yellow-50 dark:bg-yellow-900/20' },
    
    // Borders
    { from: /\bborder-gray-200\b/g, to: 'border-gray-200 dark:border-gray-700' },
    { from: /\bborder-gray-300\b/g, to: 'border-gray-300 dark:border-gray-600' },
    
    // Status colors
    { from: /\btext-green-600\b/g, to: 'text-green-600 dark:text-green-400' },
    { from: /\btext-green-500\b/g, to: 'text-green-500 dark:text-green-400' },
    { from: /\btext-yellow-600\b/g, to: 'text-yellow-600 dark:text-yellow-400' },
    { from: /\btext-red-700\b/g, to: 'text-red-700 dark:text-red-400' },
    { from: /\btext-red-600\b/g, to: 'text-red-600 dark:text-red-400' },
    
    // Form elements
    { from: /\bborder border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500\b/g, 
      to: 'border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white' },
    
    // Specific form classes
    { from: /\bblock w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500\b/g,
      to: 'block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white' },
    
    { from: /\bflex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500\b/g,
      to: 'flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white' },
    
    // Checkbox styling
    { from: /\bh-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\b/g,
      to: 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700' },
  ];

  // Get all TypeScript files in the settings directory
  const files = fs.readdirSync(settingsDir)
    .filter(file => file.endsWith('.tsx'))
    .map(file => path.join(settingsDir, file));

  let totalChanges = 0;

  files.forEach(filePath => {
    const fileName = path.basename(filePath);
    console.log(`🔄 Processing ${fileName}...`);

    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let fileChanges = 0;

      // Apply replacements
      replacements.forEach(({ from, to }) => {
        const matches = content.match(from);
        if (matches) {
          content = content.replace(from, to);
          fileChanges += matches.length;
        }
      });

      if (fileChanges > 0) {
        // Write the updated content back to the file
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`  ✅ Updated ${fileName} (${fileChanges} changes)`);
        totalChanges += fileChanges;
      } else {
        console.log(`  ⏭️  ${fileName} - no changes needed`);
      }

    } catch (error) {
      console.error(`  ❌ Error processing ${fileName}:`, error.message);
    }
  });

  console.log(`\n🎉 Successfully updated all Settings components!`);
  console.log(`📊 Total files processed: ${files.length}`);
  console.log(`📊 Total changes made: ${totalChanges}`);
  console.log(`📁 Directory: ${settingsDir}`);
}

// Run the fix
fixSettingsDarkMode();
