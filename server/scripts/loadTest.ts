import axios from 'axios';
import { performance } from 'perf_hooks';

interface LoadTestConfig {
  baseUrl: string;
  endpoints: TestEndpoint[];
  concurrency: number;
  duration: number; // in seconds
  rampUp: number; // in seconds
  authToken?: string;
  companyId?: string;
}

interface TestEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  path: string;
  weight: number; // probability weight
  payload?: any;
  headers?: Record<string, string>;
}

interface LoadTestResult {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  requestsPerSecond: number;
  errorRate: number;
  responseTimePercentiles: {
    p50: number;
    p90: number;
    p95: number;
    p99: number;
  };
  errors: Array<{
    endpoint: string;
    error: string;
    count: number;
  }>;
  endpointStats: Array<{
    endpoint: string;
    requests: number;
    averageTime: number;
    errorRate: number;
  }>;
}

class LoadTester {
  private config: LoadTestConfig;
  private results: Array<{
    endpoint: string;
    responseTime: number;
    success: boolean;
    error?: string;
    timestamp: number;
  }> = [];
  private isRunning = false;
  private startTime = 0;

  constructor(config: LoadTestConfig) {
    this.config = config;
  }

  async runLoadTest(): Promise<LoadTestResult> {
    console.log('🚀 Starting load test...');
    console.log(`Configuration:
      - Base URL: ${this.config.baseUrl}
      - Concurrency: ${this.config.concurrency}
      - Duration: ${this.config.duration}s
      - Ramp-up: ${this.config.rampUp}s
      - Endpoints: ${this.config.endpoints.length}
    `);

    this.isRunning = true;
    this.startTime = performance.now();
    this.results = [];

    // Create worker promises
    const workers = [];
    for (let i = 0; i < this.config.concurrency; i++) {
      const delay = (i / this.config.concurrency) * this.config.rampUp * 1000;
      workers.push(this.createWorker(delay));
    }

    // Wait for all workers to complete
    await Promise.all(workers);

    // Calculate and return results
    return this.calculateResults();
  }

  private async createWorker(initialDelay: number): Promise<void> {
    // Initial delay for ramp-up
    if (initialDelay > 0) {
      await this.sleep(initialDelay);
    }

    while (this.isRunning && this.getElapsedTime() < this.config.duration * 1000) {
      try {
        const endpoint = this.selectRandomEndpoint();
        await this.makeRequest(endpoint);
        
        // Small delay between requests to avoid overwhelming
        await this.sleep(Math.random() * 100);
      } catch (error) {
        console.error('Worker error:', error);
      }
    }
  }

  private async makeRequest(endpoint: TestEndpoint): Promise<void> {
    const startTime = performance.now();
    const url = `${this.config.baseUrl}${endpoint.path}`;
    
    try {
      const headers = {
        'Content-Type': 'application/json',
        ...endpoint.headers
      };

      if (this.config.authToken) {
        headers['Authorization'] = `Bearer ${this.config.authToken}`;
      }

      const requestConfig = {
        method: endpoint.method,
        url,
        headers,
        timeout: 30000,
        ...(endpoint.payload && { data: endpoint.payload })
      };

      const response = await axios(requestConfig);
      const responseTime = performance.now() - startTime;

      this.results.push({
        endpoint: `${endpoint.method} ${endpoint.path}`,
        responseTime,
        success: response.status >= 200 && response.status < 400,
        timestamp: Date.now()
      });

    } catch (error) {
      const responseTime = performance.now() - startTime;
      
      this.results.push({
        endpoint: `${endpoint.method} ${endpoint.path}`,
        responseTime,
        success: false,
        error: error.response?.status ? `HTTP ${error.response.status}` : error.message,
        timestamp: Date.now()
      });
    }
  }

  private selectRandomEndpoint(): TestEndpoint {
    const totalWeight = this.config.endpoints.reduce((sum, ep) => sum + ep.weight, 0);
    let random = Math.random() * totalWeight;
    
    for (const endpoint of this.config.endpoints) {
      random -= endpoint.weight;
      if (random <= 0) {
        return endpoint;
      }
    }
    
    return this.config.endpoints[0];
  }

  private calculateResults(): LoadTestResult {
    const totalRequests = this.results.length;
    const successfulRequests = this.results.filter(r => r.success).length;
    const failedRequests = totalRequests - successfulRequests;
    
    const responseTimes = this.results.map(r => r.responseTime);
    responseTimes.sort((a, b) => a - b);
    
    const averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const minResponseTime = responseTimes[0] || 0;
    const maxResponseTime = responseTimes[responseTimes.length - 1] || 0;
    
    const testDuration = this.getElapsedTime() / 1000;
    const requestsPerSecond = totalRequests / testDuration;
    const errorRate = (failedRequests / totalRequests) * 100;

    // Calculate percentiles
    const p50 = this.getPercentile(responseTimes, 50);
    const p90 = this.getPercentile(responseTimes, 90);
    const p95 = this.getPercentile(responseTimes, 95);
    const p99 = this.getPercentile(responseTimes, 99);

    // Calculate error statistics
    const errorMap = new Map<string, number>();
    this.results.filter(r => !r.success).forEach(r => {
      const key = `${r.endpoint}: ${r.error}`;
      errorMap.set(key, (errorMap.get(key) || 0) + 1);
    });

    const errors = Array.from(errorMap.entries()).map(([key, count]) => {
      const [endpoint, error] = key.split(': ');
      return { endpoint, error, count };
    });

    // Calculate endpoint statistics
    const endpointMap = new Map<string, { times: number[]; errors: number }>();
    this.results.forEach(r => {
      if (!endpointMap.has(r.endpoint)) {
        endpointMap.set(r.endpoint, { times: [], errors: 0 });
      }
      const stats = endpointMap.get(r.endpoint)!;
      stats.times.push(r.responseTime);
      if (!r.success) stats.errors++;
    });

    const endpointStats = Array.from(endpointMap.entries()).map(([endpoint, stats]) => ({
      endpoint,
      requests: stats.times.length,
      averageTime: stats.times.reduce((sum, time) => sum + time, 0) / stats.times.length,
      errorRate: (stats.errors / stats.times.length) * 100
    }));

    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime,
      minResponseTime,
      maxResponseTime,
      requestsPerSecond,
      errorRate,
      responseTimePercentiles: { p50, p90, p95, p99 },
      errors,
      endpointStats
    };
  }

  private getPercentile(sortedArray: number[], percentile: number): number {
    if (sortedArray.length === 0) return 0;
    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
    return sortedArray[Math.max(0, Math.min(index, sortedArray.length - 1))];
  }

  private getElapsedTime(): number {
    return performance.now() - this.startTime;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  stop(): void {
    this.isRunning = false;
  }
}

// Predefined test configurations
export const testConfigurations = {
  // Light load test
  light: {
    concurrency: 10,
    duration: 60,
    rampUp: 10
  },
  
  // Medium load test
  medium: {
    concurrency: 50,
    duration: 300,
    rampUp: 30
  },
  
  // Heavy load test
  heavy: {
    concurrency: 100,
    duration: 600,
    rampUp: 60
  },
  
  // Stress test
  stress: {
    concurrency: 200,
    duration: 300,
    rampUp: 30
  }
};

// Common endpoint configurations for accounting system
export const accountingEndpoints: TestEndpoint[] = [
  // Dashboard and overview (high frequency)
  { method: 'GET', path: '/api/dashboard/:companyId', weight: 20 },
  { method: 'GET', path: '/api/analytics/:companyId/overview', weight: 15 },
  
  // Financial reports (medium frequency)
  { method: 'GET', path: '/api/reports/:companyId/balance-sheet', weight: 10 },
  { method: 'GET', path: '/api/reports/:companyId/income-statement', weight: 10 },
  { method: 'GET', path: '/api/reports/:companyId/cash-flow', weight: 8 },
  
  // Transactions (medium frequency)
  { method: 'GET', path: '/api/transactions/:companyId', weight: 12 },
  { method: 'POST', path: '/api/transactions', weight: 5, payload: {
    companyId: ':companyId',
    description: 'Load test transaction',
    amount: 100.00,
    date: new Date().toISOString().split('T')[0]
  }},
  
  // Accounts (low frequency)
  { method: 'GET', path: '/api/accounts/:companyId', weight: 8 },
  
  // Invoices (medium frequency)
  { method: 'GET', path: '/api/invoices/:companyId', weight: 7 },
  
  // Analytics (low frequency)
  { method: 'GET', path: '/api/analytics/:companyId/financial-metrics', weight: 5 }
];

// Main load test function
export async function runAccountingLoadTest(
  baseUrl: string,
  authToken: string,
  companyId: string,
  testType: keyof typeof testConfigurations = 'light'
): Promise<LoadTestResult> {
  
  // Replace :companyId placeholders in endpoints
  const endpoints = accountingEndpoints.map(endpoint => ({
    ...endpoint,
    path: endpoint.path.replace(':companyId', companyId),
    payload: endpoint.payload ? JSON.parse(
      JSON.stringify(endpoint.payload).replace(':companyId', companyId)
    ) : undefined
  }));

  const config: LoadTestConfig = {
    baseUrl,
    endpoints,
    authToken,
    companyId,
    ...testConfigurations[testType]
  };

  const loadTester = new LoadTester(config);
  
  try {
    const results = await loadTester.runLoadTest();
    
    console.log('\n📊 Load Test Results:');
    console.log(`Total Requests: ${results.totalRequests}`);
    console.log(`Successful: ${results.successfulRequests} (${((results.successfulRequests / results.totalRequests) * 100).toFixed(2)}%)`);
    console.log(`Failed: ${results.failedRequests} (${results.errorRate.toFixed(2)}%)`);
    console.log(`Average Response Time: ${results.averageResponseTime.toFixed(2)}ms`);
    console.log(`Requests/Second: ${results.requestsPerSecond.toFixed(2)}`);
    console.log(`Response Time Percentiles:`);
    console.log(`  50th: ${results.responseTimePercentiles.p50.toFixed(2)}ms`);
    console.log(`  90th: ${results.responseTimePercentiles.p90.toFixed(2)}ms`);
    console.log(`  95th: ${results.responseTimePercentiles.p95.toFixed(2)}ms`);
    console.log(`  99th: ${results.responseTimePercentiles.p99.toFixed(2)}ms`);
    
    if (results.errors.length > 0) {
      console.log('\n❌ Errors:');
      results.errors.forEach(error => {
        console.log(`  ${error.endpoint}: ${error.error} (${error.count} times)`);
      });
    }
    
    return results;
    
  } catch (error) {
    console.error('Load test failed:', error);
    throw error;
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const baseUrl = args[0] || 'http://localhost:3000';
  const authToken = args[1] || '';
  const companyId = args[2] || '';
  const testType = (args[3] as keyof typeof testConfigurations) || 'light';

  if (!authToken || !companyId) {
    console.error('Usage: ts-node loadTest.ts <baseUrl> <authToken> <companyId> [testType]');
    console.error('Test types: light, medium, heavy, stress');
    process.exit(1);
  }

  runAccountingLoadTest(baseUrl, authToken, companyId, testType)
    .then(() => {
      console.log('\n✅ Load test completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Load test failed:', error);
      process.exit(1);
    });
}
