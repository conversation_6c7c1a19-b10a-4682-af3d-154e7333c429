#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to apply sidebar colors to all list surfaces for visual consistency
 * This script will ensure all lists use the same color scheme as the sidebar
 */

const fs = require('fs');
const path = require('path');

function applySidebarColorsToLists() {
  console.log('🎨 Applying sidebar colors to all list surfaces for visual consistency...\n');

  // Sidebar uses: bg-white dark:bg-gray-800
  // We need to ensure all list surfaces use the same colors

  // Define all directories to scan
  const directories = [
    'src/pages',
    'src/components'
  ];

  // Replacements to ensure consistent sidebar colors
  const sidebarColorReplacements = [
    // Replace any dark:bg-gray-700 with dark:bg-gray-800 to match sidebar
    { from: /className="([^"]*\s+)?bg-white(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle = '', suffix = '') => {
        return `className="${prefix}bg-white${middle}dark:bg-gray-800${suffix}"`;
      }
    },
    
    // Replace any bg-gray-50 dark:bg-gray-700 with bg-gray-50 dark:bg-gray-800
    { from: /className="([^"]*\s+)?bg-gray-50(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle = '', suffix = '') => {
        return `className="${prefix}bg-gray-50${middle}dark:bg-gray-800${suffix}"`;
      }
    },
    
    // Ensure table containers use sidebar colors
    { from: /className="([^"]*\s+)?overflow-x-auto(\s+[^"]*)?bg-white(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', suffix = '') => {
        return `className="${prefix}overflow-x-auto${middle1}bg-white${middle2}dark:bg-gray-800${suffix}"`;
      }
    },
    
    // Ensure table elements use sidebar colors
    { from: /className="([^"]*\s+)?min-w-full(\s+[^"]*)?bg-white(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', suffix = '') => {
        return `className="${prefix}min-w-full${middle1}bg-white${middle2}dark:bg-gray-800${suffix}"`;
      }
    },
    
    // Ensure tbody elements use sidebar colors
    { from: /className="([^"]*\s+)?bg-white(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?divide-y(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', middle3 = '', suffix = '') => {
        return `className="${prefix}bg-white${middle1}dark:bg-gray-800${middle2}divide-y${middle3}${suffix}"`;
      }
    },
    
    // Ensure card containers use sidebar colors
    { from: /className="([^"]*\s+)?bg-white(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?shadow(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', middle3 = '', suffix = '') => {
        return `className="${prefix}bg-white${middle1}dark:bg-gray-800${middle2}shadow${middle3}${suffix}"`;
      }
    },
    
    // Ensure list containers use sidebar colors
    { from: /className="([^"]*\s+)?space-y-4(\s+[^"]*)?bg-white(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', middle3 = '', suffix = '') => {
        return `className="${prefix}space-y-4${middle1}bg-white${middle2}dark:bg-gray-800${middle3}${suffix}"`;
      }
    },
    
    // Ensure grid containers use sidebar colors
    { from: /className="([^"]*\s+)?grid(\s+[^"]*)?bg-white(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', middle3 = '', suffix = '') => {
        return `className="${prefix}grid${middle1}bg-white${middle2}dark:bg-gray-800${middle3}${suffix}"`;
      }
    },
    
    // Ensure flex containers use sidebar colors
    { from: /className="([^"]*\s+)?flex(\s+[^"]*)?bg-white(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', middle3 = '', suffix = '') => {
        return `className="${prefix}flex${middle1}bg-white${middle2}dark:bg-gray-800${middle3}${suffix}"`;
      }
    },
    
    // Ensure div containers use sidebar colors
    { from: /className="([^"]*\s+)?p-6(\s+[^"]*)?bg-white(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', middle3 = '', suffix = '') => {
        return `className="${prefix}p-6${middle1}bg-white${middle2}dark:bg-gray-800${middle3}${suffix}"`;
      }
    },
    
    // Ensure rounded containers use sidebar colors
    { from: /className="([^"]*\s+)?rounded-lg(\s+[^"]*)?bg-white(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', middle3 = '', suffix = '') => {
        return `className="${prefix}rounded-lg${middle1}bg-white${middle2}dark:bg-gray-800${middle3}${suffix}"`;
      }
    },
    
    // Ensure border containers use sidebar colors
    { from: /className="([^"]*\s+)?border(\s+[^"]*)?bg-white(\s+[^"]*)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle1 = '', middle2 = '', middle3 = '', suffix = '') => {
        return `className="${prefix}border${middle1}bg-white${middle2}dark:bg-gray-800${middle3}${suffix}"`;
      }
    },
    
    // Update hover states to match sidebar pattern (hover:bg-gray-50 dark:hover:bg-gray-700)
    { from: /className="([^"]*\s+)?hover:bg-gray-50(\s+[^"]*)?dark:hover:bg-gray-800(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle = '', suffix = '') => {
        return `className="${prefix}hover:bg-gray-50${middle}dark:hover:bg-gray-700${suffix}"`;
      }
    },
    
    // Ensure focus states match sidebar pattern
    { from: /className="([^"]*\s+)?focus:bg-white(\s+[^"]*)?dark:focus:bg-gray-800(\s+[^"]*)?"/g,
      to: (match, prefix = '', middle = '', suffix = '') => {
        return `className="${prefix}focus:bg-white${middle}dark:focus:bg-gray-700${suffix}"`;
      }
    },
    
    // Keep table headers as bg-gray-50 dark:bg-gray-700 for contrast
    // This is intentional - headers should be slightly different from the main surface
    
    // Ensure any remaining dark:bg-gray-700 on main surfaces becomes dark:bg-gray-800
    { from: /className="([^"]*\s+)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', suffix = '') => {
        // Skip if this is a header (bg-gray-50) or hover state
        if (match.includes('bg-gray-50') || match.includes('hover:') || match.includes('focus:')) {
          return match;
        }
        return `className="${prefix}dark:bg-gray-800${suffix}"`;
      }
    }
  ];

  let totalChanges = 0;
  let processedFiles = 0;

  directories.forEach(dir => {
    const dirPath = path.join(__dirname, '../../', dir);
    
    if (!fs.existsSync(dirPath)) {
      console.log(`⏭️  Skipping ${dir} - directory not found`);
      return;
    }

    console.log(`🔄 Processing ${dir}...`);
    
    const files = getAllFiles(dirPath, ['.tsx', '.ts']);
    
    files.forEach(filePath => {
      const relativePath = path.relative(path.join(__dirname, '../../'), filePath);
      
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        let fileChanges = 0;

        // Apply sidebar color replacements
        sidebarColorReplacements.forEach(({ from, to }) => {
          if (typeof to === 'function') {
            const beforeContent = content;
            content = content.replace(from, to);
            if (content !== beforeContent) {
              fileChanges++;
            }
          } else {
            const matches = content.match(from);
            if (matches) {
              content = content.replace(from, to);
              fileChanges += matches.length;
            }
          }
        });

        if (fileChanges > 0) {
          // Write the updated content back to the file
          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`  ✅ ${relativePath}: ${fileChanges} sidebar color fixes`);
          totalChanges += fileChanges;
        }
        
        processedFiles++;

      } catch (error) {
        console.error(`  ❌ Error processing ${relativePath}:`, error.message);
      }
    });
  });

  console.log(`\n🎉 Sidebar color application completed!`);
  console.log(`📊 Total files processed: ${processedFiles}`);
  console.log(`📊 Total sidebar color fixes applied: ${totalChanges}`);
  console.log(`📁 Directories scanned: ${directories.join(', ')}`);
  console.log(`\n🎨 Color Scheme Applied:`);
  console.log(`   Main Surfaces: bg-white dark:bg-gray-800 (matches sidebar)`);
  console.log(`   Secondary Surfaces: bg-gray-50 dark:bg-gray-700 (headers, contrast)`);
  console.log(`   Hover States: hover:bg-gray-50 dark:hover:bg-gray-700 (matches sidebar)`);
  console.log(`   Focus States: focus:bg-white dark:focus:bg-gray-700 (matches sidebar)`);
}

function getAllFiles(dir, extensions) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other irrelevant directories
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          files = files.concat(getAllFiles(fullPath, extensions));
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    });
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

// Run the sidebar color application
applySidebarColorsToLists();
