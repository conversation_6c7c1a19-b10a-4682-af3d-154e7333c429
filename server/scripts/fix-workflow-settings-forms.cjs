#!/usr/bin/env node

/**
 * Script to fix remaining form elements in WorkflowSettings component
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, '../../src/components/settings/WorkflowSettings.tsx');

function fixWorkflowSettingsForms() {
  console.log('🔧 Fixing WorkflowSettings form elements...\n');

  try {
    let content = fs.readFileSync(filePath, 'utf8');

    // Define replacements for form elements
    const replacements = [
      // Select elements
      { 
        from: /className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"(?!\s+dark:bg-gray-700)/g, 
        to: 'className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"' 
      },
      
      // Button hover states
      { 
        from: /className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:bg-gray-700"/g, 
        to: 'className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-800"' 
      },
      
      // Status badges
      { 
        from: /'bg-gray-100 text-gray-800'/g, 
        to: "'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'" 
      },
      
      // Divide colors
      { 
        from: /divide-gray-200/g, 
        to: 'divide-gray-200 dark:divide-gray-700' 
      },
      
      // Icon hover states
      { 
        from: /className="text-gray-600 dark:text-gray-400 hover:text-gray-500 dark:text-gray-400"/g, 
        to: 'className="text-gray-600 dark:text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"' 
      },
    ];

    // Apply replacements
    let changeCount = 0;
    replacements.forEach(({ from, to }) => {
      const matches = content.match(from);
      if (matches) {
        content = content.replace(from, to);
        changeCount += matches.length;
        console.log(`✅ Applied form fix (${matches.length} occurrences)`);
      }
    });

    // Write the updated content back to the file
    fs.writeFileSync(filePath, content, 'utf8');

    console.log(`\n🎉 Successfully updated WorkflowSettings.tsx!`);
    console.log(`📊 Total changes made: ${changeCount}`);
    console.log(`📁 File: ${filePath}`);

  } catch (error) {
    console.error('❌ Error fixing WorkflowSettings forms:', error);
    process.exit(1);
  }
}

// Run the fix
fixWorkflowSettingsForms();
