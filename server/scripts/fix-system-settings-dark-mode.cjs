#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to fix dark mode classes in SystemSettings component
 * This script will update all hardcoded light mode classes to support dark mode
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, '../../src/components/settings/SystemSettings.tsx');

function fixSystemSettingsDarkMode() {
  console.log('🔧 Fixing SystemSettings dark mode classes...\n');

  try {
    let content = fs.readFileSync(filePath, 'utf8');

    // Define replacements for dark mode
    const replacements = [
      // Headers and text
      { from: 'text-gray-900', to: 'text-gray-900 dark:text-white' },
      { from: 'text-gray-600', to: 'text-gray-600 dark:text-gray-400' },
      { from: 'text-gray-700', to: 'text-gray-700 dark:text-gray-300' },
      
      // Backgrounds
      { from: 'bg-white shadow', to: 'bg-white dark:bg-gray-800 shadow' },
      { from: 'bg-white hover:bg-gray-50', to: 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700' },
      { from: 'bg-green-100', to: 'bg-green-100 dark:bg-green-900/20' },
      
      // Borders
      { from: 'border-gray-200', to: 'border-gray-200 dark:border-gray-700' },
      { from: 'border-gray-300', to: 'border-gray-300 dark:border-gray-600' },
      
      // Status colors
      { from: 'text-green-600', to: 'text-green-600 dark:text-green-400' },
      { from: 'text-yellow-600', to: 'text-yellow-600 dark:text-yellow-400' },
      { from: 'text-red-700', to: 'text-red-700 dark:text-red-400' },
      { from: 'bg-red-50', to: 'bg-red-50 dark:bg-red-900/20' },
    ];

    // Apply replacements
    let changeCount = 0;
    replacements.forEach(({ from, to }) => {
      const regex = new RegExp(`\\b${from}\\b`, 'g');
      const matches = content.match(regex);
      if (matches) {
        content = content.replace(regex, to);
        changeCount += matches.length;
        console.log(`✅ Replaced "${from}" with "${to}" (${matches.length} occurrences)`);
      }
    });

    // Write the updated content back to the file
    fs.writeFileSync(filePath, content, 'utf8');

    console.log(`\n🎉 Successfully updated SystemSettings.tsx!`);
    console.log(`📊 Total changes made: ${changeCount}`);
    console.log(`📁 File: ${filePath}`);

  } catch (error) {
    console.error('❌ Error fixing SystemSettings dark mode:', error);
    process.exit(1);
  }
}

// Run the fix
fixSystemSettingsDarkMode();
