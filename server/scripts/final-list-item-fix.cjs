#!/usr/bin/env node

/**
 * Final comprehensive fix for all remaining list items
 * This script will catch any remaining list items that don't match sidebar colors
 */

const fs = require('fs');
const path = require('path');

function finalListItemFix() {
  console.log('🔧 Final comprehensive fix for all remaining list items...\n');

  // Define all directories to scan
  const directories = [
    'src/pages',
    'src/components'
  ];

  // Final comprehensive replacements
  const finalReplacements = [
    // Catch any remaining dark:bg-gray-700 that should be dark:bg-gray-800
    { from: /className="([^"]*\s+)?dark:bg-gray-700(\s+[^"]*)?"/g,
      to: (match, prefix = '', suffix = '') => {
        // Keep headers and hover states as gray-700 for contrast
        if (match.includes('bg-gray-50') || 
            match.includes('hover:bg-gray-50') || 
            match.includes('focus:') || 
            match.includes('thead') ||
            match.includes('th ') ||
            match.includes('header')) {
          return match;
        }
        return `className="${prefix}dark:bg-gray-800${suffix}"`;
      }
    },
    
    // Specifically target table rows
    { from: /<tr([^>]*?)className="([^"]*?)dark:bg-gray-700([^"]*?)"/g,
      to: (match, beforeClass, classContent, afterClass) => {
        return match.replace('dark:bg-gray-700', 'dark:bg-gray-800');
      }
    },
    
    // Specifically target list items
    { from: /<li([^>]*?)className="([^"]*?)dark:bg-gray-700([^"]*?)"/g,
      to: (match, beforeClass, classContent, afterClass) => {
        return match.replace('dark:bg-gray-700', 'dark:bg-gray-800');
      }
    },
    
    // Specifically target div list items
    { from: /<div([^>]*?)className="([^"]*?)dark:bg-gray-700([^"]*?)"/g,
      to: (match, beforeClass, classContent, afterClass) => {
        // Skip headers and hover states
        if (match.includes('bg-gray-50') || match.includes('hover:bg-gray-50')) {
          return match;
        }
        return match.replace('dark:bg-gray-700', 'dark:bg-gray-800');
      }
    },
    
    // Fix any remaining even/odd patterns
    { from: /dark:even:bg-gray-700/g,
      to: 'dark:even:bg-gray-800'
    },
    
    { from: /dark:odd:bg-gray-700/g,
      to: 'dark:odd:bg-gray-800'
    },
    
    // Fix any remaining hover states that should be gray-700
    { from: /dark:hover:bg-gray-800/g,
      to: 'dark:hover:bg-gray-700'
    },
    
    // Fix any remaining focus states
    { from: /dark:focus:bg-gray-800/g,
      to: 'dark:focus:bg-gray-700'
    }
  ];

  let totalChanges = 0;
  let processedFiles = 0;

  directories.forEach(dir => {
    const dirPath = path.join(__dirname, '../../', dir);
    
    if (!fs.existsSync(dirPath)) {
      console.log(`⏭️  Skipping ${dir} - directory not found`);
      return;
    }

    console.log(`🔄 Processing ${dir}...`);
    
    const files = getAllFiles(dirPath, ['.tsx', '.ts']);
    
    files.forEach(filePath => {
      const relativePath = path.relative(path.join(__dirname, '../../'), filePath);
      
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        let fileChanges = 0;

        // Apply final replacements
        finalReplacements.forEach(({ from, to }) => {
          if (typeof to === 'function') {
            const beforeContent = content;
            content = content.replace(from, to);
            if (content !== beforeContent) {
              fileChanges++;
            }
          } else {
            const matches = content.match(from);
            if (matches) {
              content = content.replace(from, to);
              fileChanges += matches.length;
            }
          }
        });

        if (fileChanges > 0) {
          // Write the updated content back to the file
          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`  ✅ ${relativePath}: ${fileChanges} final fixes`);
          totalChanges += fileChanges;
        }
        
        processedFiles++;

      } catch (error) {
        console.error(`  ❌ Error processing ${relativePath}:`, error.message);
      }
    });
  });

  console.log(`\n🎉 Final list item fix completed!`);
  console.log(`📊 Total files processed: ${processedFiles}`);
  console.log(`📊 Total final fixes applied: ${totalChanges}`);
  console.log(`📁 Directories scanned: ${directories.join(', ')}`);
  
  if (totalChanges === 0) {
    console.log(`\n✨ Perfect! All list items already use correct sidebar colors!`);
  } else {
    console.log(`\n✨ Applied ${totalChanges} final fixes to ensure all list items match sidebar!`);
  }
  
  console.log(`\n🎨 Final Color Scheme:`);
  console.log(`   List Items: bg-white dark:bg-gray-800 (matches sidebar exactly)`);
  console.log(`   Table Headers: bg-gray-50 dark:bg-gray-700 (contrast for readability)`);
  console.log(`   Hover States: hover:bg-gray-50 dark:hover:bg-gray-700 (matches sidebar)`);
  console.log(`   Focus States: focus:bg-white dark:focus:bg-gray-700 (matches sidebar)`);
}

function getAllFiles(dir, extensions) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other irrelevant directories
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          files = files.concat(getAllFiles(fullPath, extensions));
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    });
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

// Run the final list item fix
finalListItemFix();
