#!/usr/bin/env node

/**
 * Script to fix dark mode classes in Workflows page
 * This script will update all hardcoded light mode classes to support dark mode
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, '../../src/pages/Workflows.tsx');

function fixWorkflowsDarkMode() {
  console.log('🔧 Fixing Workflows page dark mode classes...\n');

  try {
    let content = fs.readFileSync(filePath, 'utf8');

    // Define replacements for dark mode
    const replacements = [
      // Background colors
      { from: /\bbg-white shadow\b/g, to: 'bg-white dark:bg-gray-800 shadow' },
      { from: /\bbg-white rounded-lg shadow\b/g, to: 'bg-white dark:bg-gray-800 rounded-lg shadow' },
      { from: /\bbg-white hover:bg-gray-50\b/g, to: 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700' },
      { from: /\bhover:bg-gray-50\b/g, to: 'hover:bg-gray-50 dark:hover:bg-gray-700' },
      
      // Text colors
      { from: /\btext-gray-900\b/g, to: 'text-gray-900 dark:text-white' },
      { from: /\btext-gray-600\b/g, to: 'text-gray-600 dark:text-gray-400' },
      { from: /\btext-gray-700\b/g, to: 'text-gray-700 dark:text-gray-300' },
      { from: /\btext-gray-500\b/g, to: 'text-gray-500 dark:text-gray-400' },
      { from: /\btext-gray-400\b/g, to: 'text-gray-400 dark:text-gray-500' },
      
      // Border colors
      { from: /\bborder-gray-200\b/g, to: 'border-gray-200 dark:border-gray-700' },
      { from: /\bborder-gray-300\b/g, to: 'border-gray-300 dark:border-gray-600' },
      { from: /\bdivide-gray-200\b/g, to: 'divide-gray-200 dark:divide-gray-700' },
      
      // Button colors
      { from: /\bbg-green-100 text-green-800 hover:bg-green-200\b/g, to: 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/30' },
      { from: /\bbg-yellow-100 text-yellow-800 hover:bg-yellow-200\b/g, to: 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400 hover:bg-yellow-200 dark:hover:bg-yellow-900/30' },
      
      // Form elements
      { from: /\bborder border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\b/g, 
        to: 'border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white' },
      
      // Select elements
      { from: /\bblock w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\b/g,
        to: 'block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white' },
      
      // Input elements
      { from: /\bblock w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500\b/g,
        to: 'block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white' },
      
      // Textarea elements
      { from: /\bblock w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 resize-none\b/g,
        to: 'block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white resize-none' },
      
      // Checkbox elements
      { from: /\bh-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\b/g,
        to: 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700' },
      
      // Modal backgrounds (if any)
      { from: /\bbg-black bg-opacity-50\b/g, to: 'bg-black bg-opacity-50' }, // Keep existing modal backgrounds
    ];

    // Apply replacements
    let changeCount = 0;
    replacements.forEach(({ from, to }) => {
      const matches = content.match(from);
      if (matches) {
        content = content.replace(from, to);
        changeCount += matches.length;
        console.log(`✅ Applied dark mode fix (${matches.length} occurrences)`);
      }
    });

    // Write the updated content back to the file
    fs.writeFileSync(filePath, content, 'utf8');

    console.log(`\n🎉 Successfully updated Workflows.tsx!`);
    console.log(`📊 Total changes made: ${changeCount}`);
    console.log(`📁 File: ${filePath}`);

  } catch (error) {
    console.error('❌ Error fixing Workflows dark mode:', error);
    process.exit(1);
  }
}

// Run the fix
fixWorkflowsDarkMode();
