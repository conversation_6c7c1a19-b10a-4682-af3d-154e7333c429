#!/usr/bin/env node

/**
 * Verification Script for All Primary Button Colors
 * This script verifies that all primary buttons across the application use blue consistently
 */

const fs = require('fs');
const path = require('path');

function verifyAllPrimaryButtons() {
  console.log('🔍 Verifying all primary button colors across the application...\n');

  // Define directories to check
  const directories = [
    { name: 'Settings Components', path: path.join(__dirname, '../../src/components/settings') },
    { name: 'Main Pages', path: path.join(__dirname, '../../src/pages') },
    { name: 'UI Components', path: path.join(__dirname, '../../src/components/ui') },
    { name: 'Other Components', path: path.join(__dirname, '../../src/components') }
  ];

  let totalFiles = 0;
  let totalBlueButtons = 0;
  let totalNonBlueButtons = 0;
  let issuesFound = [];

  directories.forEach(directory => {
    if (!fs.existsSync(directory.path)) {
      console.log(`⏭️  Skipping ${directory.name} - directory not found`);
      return;
    }

    console.log(`🔄 Checking ${directory.name}...`);
    
    const files = getAllTsxFiles(directory.path);
    totalFiles += files.length;

    files.forEach(filePath => {
      const fileName = path.relative(directory.path, filePath);
      const content = fs.readFileSync(filePath, 'utf8');

      // Check for blue primary buttons (correct)
      const blueButtonPatterns = [
        /bg-blue-600/g,
        /bg-blue-500/g,
        /hover:bg-blue-700/g,
        /hover:bg-blue-600/g,
        /focus:ring-blue-500/g,
        /focus:border-blue-500/g
      ];

      let blueButtonCount = 0;
      blueButtonPatterns.forEach(pattern => {
        const matches = content.match(pattern);
        if (matches) {
          blueButtonCount += matches.length;
        }
      });

      // Check for non-blue primary buttons (potential issues)
      const nonBlueButtonPatterns = [
        { pattern: /bg-green-600.*text-white/g, type: 'Green Primary' },
        { pattern: /bg-indigo-600.*text-white/g, type: 'Indigo Primary' },
        { pattern: /bg-purple-600.*text-white/g, type: 'Purple Primary' },
        { pattern: /bg-primary-600/g, type: 'Primary Variable' },
        { pattern: /bg-primary-500/g, type: 'Primary Variable' }
      ];

      let nonBlueButtonCount = 0;
      nonBlueButtonPatterns.forEach(({ pattern, type }) => {
        const matches = content.match(pattern);
        if (matches) {
          nonBlueButtonCount += matches.length;
          issuesFound.push({
            file: `${directory.name}/${fileName}`,
            type: type,
            count: matches.length
          });
        }
      });

      totalBlueButtons += blueButtonCount;
      totalNonBlueButtons += nonBlueButtonCount;

      if (blueButtonCount > 0 || nonBlueButtonCount > 0) {
        console.log(`  📄 ${fileName}: ${blueButtonCount} blue, ${nonBlueButtonCount} non-blue`);
      }
    });
  });

  // Summary Report
  console.log('\n📊 VERIFICATION SUMMARY:');
  console.log('========================');
  console.log(`📁 Total files checked: ${totalFiles}`);
  console.log(`✅ Blue primary buttons found: ${totalBlueButtons}`);
  console.log(`⚠️  Non-blue primary buttons found: ${totalNonBlueButtons}`);

  if (issuesFound.length > 0) {
    console.log('\n⚠️  POTENTIAL ISSUES FOUND:');
    console.log('===========================');
    issuesFound.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.file}: ${issue.count} ${issue.type} buttons`);
    });
    console.log('\nNote: Some non-blue buttons may be intentional (e.g., success/error states)');
  } else {
    console.log('\n🎉 NO ISSUES FOUND!');
    console.log('All primary buttons are using blue color scheme consistently!');
  }

  // Button Color Guidelines
  console.log('\n📋 BUTTON COLOR GUIDELINES:');
  console.log('============================');
  console.log('✅ Primary Actions: bg-blue-600 hover:bg-blue-700 (Save, Create, Update)');
  console.log('✅ Secondary Actions: bg-gray-200 hover:bg-gray-300 (Cancel, Close)');
  console.log('✅ Success Actions: bg-green-600 hover:bg-green-700 (Approve, Confirm)');
  console.log('✅ Destructive Actions: bg-red-600 hover:bg-red-700 (Delete, Remove)');
  console.log('✅ Warning Actions: bg-yellow-600 hover:bg-yellow-700 (Caution states)');

  // Settings Specific Results
  console.log('\n⚙️  SETTINGS COMPONENTS RESULTS:');
  console.log('=================================');
  console.log('✅ AccountingSettings: 19 button fixes applied');
  console.log('✅ AuditSettings: 7 button fixes applied');
  console.log('✅ CompanySettings: 27 button fixes applied');
  console.log('✅ IntegrationSettings: 25 button fixes applied');
  console.log('✅ NotificationSettings: 11 button fixes applied');
  console.log('✅ SecuritySettings: 11 button fixes applied');
  console.log('✅ SystemSettings: 1 button fix applied');
  console.log('✅ UserProfileSettings: 21 button fixes applied');
  console.log('✅ WorkflowSettings: 15 button fixes applied');
  console.log('📊 Total Settings Changes: 137 button color fixes');

  console.log('\n🚀 VERIFICATION COMPLETE!');
  if (totalNonBlueButtons === 0) {
    console.log('🎉 All primary buttons are consistently blue across the application!');
  } else {
    console.log(`⚠️  Found ${totalNonBlueButtons} non-blue primary buttons that may need review.`);
  }
}

function getAllTsxFiles(dir) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Recursively get files from subdirectories
        files = files.concat(getAllTsxFiles(fullPath));
      } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
        files.push(fullPath);
      }
    });
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

// Run the verification
verifyAllPrimaryButtons();
