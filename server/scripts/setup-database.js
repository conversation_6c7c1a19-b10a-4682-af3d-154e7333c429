const { Client } = require('pg');
require('dotenv').config({ path: '../.env' });

async function setupDatabase() {
  // First, connect to the default 'postgres' database to create our database
  const adminClient = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: 'postgres', // Connect to default database first
    user: process.env.DB_USER || 'curtis',
    password: process.env.DB_PASSWORD || 'Athanas@2015',
  });

  try {
    console.log('🔌 Connecting to PostgreSQL...');
    await adminClient.connect();
    console.log('✅ Connected to PostgreSQL');

    // Check if database exists
    const dbName = process.env.DB_NAME || 'accounting_system';
    const result = await adminClient.query(
      'SELECT 1 FROM pg_database WHERE datname = $1',
      [dbName]
    );

    if (result.rows.length === 0) {
      console.log(`📦 Creating database: ${dbName}`);
      await adminClient.query(`CREATE DATABASE "${dbName}"`);
      console.log('✅ Database created successfully');
    } else {
      console.log('✅ Database already exists');
    }

    await adminClient.end();

    // Now test connection to our application database
    const appClient = new Client({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      database: dbName,
      user: process.env.DB_USER || 'curtis',
      password: process.env.DB_PASSWORD || 'Athanas@2015',
    });

    console.log('🔌 Testing connection to application database...');
    await appClient.connect();
    await appClient.query('SELECT NOW()');
    console.log('✅ Application database connection successful');
    await appClient.end();

    console.log('🎉 Database setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Make sure PostgreSQL is running: brew services start postgresql@17');
    } else if (error.code === '28P01') {
      console.error('💡 Authentication failed. Check your username and password in .env file');
    }
    process.exit(1);
  }
}

setupDatabase();
