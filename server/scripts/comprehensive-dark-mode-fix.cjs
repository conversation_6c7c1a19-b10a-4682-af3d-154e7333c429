#!/usr/bin/env node

/**
 * Comprehensive Dark Mode Fix Script
 * This script will fix ALL remaining white elements and surface color inconsistencies
 */

const fs = require('fs');
const path = require('path');

function comprehensiveDarkModeFix() {
  console.log('🔧 Comprehensive Dark Mode Fix - Catching ALL missed elements...\n');

  // Define all directories to scan
  const directories = [
    'src/pages',
    'src/components',
    'src/layouts'
  ];

  // More comprehensive replacements to catch everything
  const replacements = [
    // Basic backgrounds that were missed
    { from: /\bbg-white\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800' },
    { from: /\bbg-gray-50\b(?!\s+dark:)/g, to: 'bg-gray-50 dark:bg-gray-700' },
    { from: /\bbg-gray-100\b(?!\s+dark:)/g, to: 'bg-gray-100 dark:bg-gray-700' },
    { from: /\bbg-gray-200\b(?!\s+dark:)/g, to: 'bg-gray-200 dark:bg-gray-600' },
    
    // Text colors that were missed
    { from: /\btext-gray-900\b(?!\s+dark:)/g, to: 'text-gray-900 dark:text-white' },
    { from: /\btext-gray-800\b(?!\s+dark:)/g, to: 'text-gray-800 dark:text-gray-200' },
    { from: /\btext-gray-700\b(?!\s+dark:)/g, to: 'text-gray-700 dark:text-gray-300' },
    { from: /\btext-gray-600\b(?!\s+dark:)/g, to: 'text-gray-600 dark:text-gray-400' },
    { from: /\btext-gray-500\b(?!\s+dark:)/g, to: 'text-gray-500 dark:text-gray-400' },
    { from: /\btext-gray-400\b(?!\s+dark:)/g, to: 'text-gray-400 dark:text-gray-500' },
    
    // Border colors that were missed
    { from: /\bborder-gray-200\b(?!\s+dark:)/g, to: 'border-gray-200 dark:border-gray-700' },
    { from: /\bborder-gray-300\b(?!\s+dark:)/g, to: 'border-gray-300 dark:border-gray-600' },
    { from: /\bborder-gray-400\b(?!\s+dark:)/g, to: 'border-gray-400 dark:border-gray-500' },
    
    // Divide colors
    { from: /\bdivide-gray-200\b(?!\s+dark:)/g, to: 'divide-gray-200 dark:divide-gray-700' },
    { from: /\bdivide-gray-300\b(?!\s+dark:)/g, to: 'divide-gray-300 dark:divide-gray-600' },
    
    // Ring colors for focus states
    { from: /\bring-gray-200\b(?!\s+dark:)/g, to: 'ring-gray-200 dark:ring-gray-700' },
    { from: /\bring-gray-300\b(?!\s+dark:)/g, to: 'ring-gray-300 dark:ring-gray-600' },
    
    // Placeholder text
    { from: /\bplaceholder-gray-400\b(?!\s+dark:)/g, to: 'placeholder-gray-400 dark:placeholder-gray-500' },
    { from: /\bplaceholder-gray-500\b(?!\s+dark:)/g, to: 'placeholder-gray-500 dark:placeholder-gray-400' },
    
    // Status colors that were missed
    { from: /\btext-green-600\b(?!\s+dark:)/g, to: 'text-green-600 dark:text-green-400' },
    { from: /\btext-green-500\b(?!\s+dark:)/g, to: 'text-green-500 dark:text-green-400' },
    { from: /\btext-red-600\b(?!\s+dark:)/g, to: 'text-red-600 dark:text-red-400' },
    { from: /\btext-red-500\b(?!\s+dark:)/g, to: 'text-red-500 dark:text-red-400' },
    { from: /\btext-yellow-600\b(?!\s+dark:)/g, to: 'text-yellow-600 dark:text-yellow-400' },
    { from: /\btext-yellow-500\b(?!\s+dark:)/g, to: 'text-yellow-500 dark:text-yellow-400' },
    { from: /\btext-blue-600\b(?!\s+dark:)/g, to: 'text-blue-600 dark:text-blue-400' },
    { from: /\btext-blue-500\b(?!\s+dark:)/g, to: 'text-blue-500 dark:text-blue-400' },
    
    // Status backgrounds
    { from: /\bbg-green-50\b(?!\s+dark:)/g, to: 'bg-green-50 dark:bg-green-900/20' },
    { from: /\bbg-green-100\b(?!\s+dark:)/g, to: 'bg-green-100 dark:bg-green-900/20' },
    { from: /\bbg-red-50\b(?!\s+dark:)/g, to: 'bg-red-50 dark:bg-red-900/20' },
    { from: /\bbg-red-100\b(?!\s+dark:)/g, to: 'bg-red-100 dark:bg-red-900/20' },
    { from: /\bbg-yellow-50\b(?!\s+dark:)/g, to: 'bg-yellow-50 dark:bg-yellow-900/20' },
    { from: /\bbg-yellow-100\b(?!\s+dark:)/g, to: 'bg-yellow-100 dark:bg-yellow-900/20' },
    { from: /\bbg-blue-50\b(?!\s+dark:)/g, to: 'bg-blue-50 dark:bg-blue-900/20' },
    { from: /\bbg-blue-100\b(?!\s+dark:)/g, to: 'bg-blue-100 dark:bg-blue-900/20' },
    
    // Hover states that were missed
    { from: /\bhover:bg-gray-50\b(?!\s+dark:)/g, to: 'hover:bg-gray-50 dark:hover:bg-gray-700' },
    { from: /\bhover:bg-gray-100\b(?!\s+dark:)/g, to: 'hover:bg-gray-100 dark:hover:bg-gray-600' },
    { from: /\bhover:bg-white\b(?!\s+dark:)/g, to: 'hover:bg-white dark:hover:bg-gray-700' },
    
    // Focus states
    { from: /\bfocus:bg-white\b(?!\s+dark:)/g, to: 'focus:bg-white dark:focus:bg-gray-700' },
    
    // Form elements that were missed
    { from: /\bbg-white border border-gray-300\b(?!\s+.*dark:bg-gray-700)/g, 
      to: 'bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 dark:text-white' },
    
    // Table headers and cells
    { from: /\bbg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\b/g,
      to: 'bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider' },
    
    // Card and panel backgrounds
    { from: /\bbg-white shadow-sm\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800 shadow-sm' },
    { from: /\bbg-white shadow-lg\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800 shadow-lg' },
    { from: /\bbg-white shadow-xl\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800 shadow-xl' },
    
    // Modal and overlay backgrounds
    { from: /\bbg-white rounded-lg\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800 rounded-lg' },
    { from: /\bbg-white rounded-md\b(?!\s+dark:)/g, to: 'bg-white dark:bg-gray-800 rounded-md' },
    
    // Dropdown and select backgrounds
    { from: /\bbg-white border\b(?!\s+.*dark:bg-gray-700)/g, to: 'bg-white dark:bg-gray-700 border dark:border-gray-600 dark:text-white' },
    
    // Input field patterns
    { from: /className="([^"]*\s+)?border border-gray-300([^"]*)"(?!\s+.*dark:bg-gray-700)/g,
      to: 'className="$1border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white$2"' },
    
    // Button patterns that might have been missed
    { from: /\bbg-white text-gray-700 border border-gray-300\b(?!\s+dark:)/g,
      to: 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600' },
  ];

  let totalChanges = 0;
  let processedFiles = 0;

  directories.forEach(dir => {
    const dirPath = path.join(__dirname, '../../', dir);
    
    if (!fs.existsSync(dirPath)) {
      console.log(`⏭️  Skipping ${dir} - directory not found`);
      return;
    }

    console.log(`🔄 Processing ${dir}...`);
    
    const files = getAllFiles(dirPath, ['.tsx', '.ts']);
    
    files.forEach(filePath => {
      const relativePath = path.relative(path.join(__dirname, '../../'), filePath);
      
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        let fileChanges = 0;
        let originalContent = content;

        // Apply replacements
        replacements.forEach(({ from, to }) => {
          const matches = content.match(from);
          if (matches) {
            content = content.replace(from, to);
            fileChanges += matches.length;
          }
        });

        if (fileChanges > 0) {
          // Write the updated content back to the file
          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`  ✅ ${relativePath}: ${fileChanges} fixes`);
          totalChanges += fileChanges;
        }
        
        processedFiles++;

      } catch (error) {
        console.error(`  ❌ Error processing ${relativePath}:`, error.message);
      }
    });
  });

  console.log(`\n🎉 Comprehensive dark mode fix completed!`);
  console.log(`📊 Total files processed: ${processedFiles}`);
  console.log(`📊 Total fixes applied: ${totalChanges}`);
  console.log(`📁 Directories scanned: ${directories.join(', ')}`);
}

function getAllFiles(dir, extensions) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other irrelevant directories
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          files = files.concat(getAllFiles(fullPath, extensions));
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    });
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

// Run the comprehensive fix
comprehensiveDarkModeFix();
