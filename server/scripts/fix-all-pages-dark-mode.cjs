#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix dark mode classes in all main pages
 * This script will update all hardcoded light mode classes to support dark mode
 */

const fs = require('fs');
const path = require('path');

function fixAllPagesDarkMode() {
  console.log('🔧 Fixing dark mode classes in all main pages...\n');

  // Define the pages to fix
  const pagesToFix = [
    { name: 'Chart of Accounts', path: 'src/pages/accounts/ChartOfAccounts.tsx' },
    { name: 'Transactions', path: 'src/pages/transactions/Transactions.tsx' },
    { name: 'Contacts', path: 'src/pages/contacts/Contacts.tsx' },
    { name: 'Invoices', path: 'src/pages/invoices/Invoices.tsx' },
    { name: 'Bank Reconciliation', path: 'src/pages/BankReconciliationPage.tsx' },
    { name: 'Reports', path: 'src/pages/reports/Reports.tsx' },
    { name: 'Tax Management', path: 'src/pages/admin/TaxManagement.tsx' },
    { name: 'Banking', path: 'src/pages/Banking.tsx' }
  ];

  // Define replacements for dark mode
  const replacements = [
    // Headers and text
    { from: /\btext-gray-900\b/g, to: 'text-gray-900 dark:text-white' },
    { from: /\btext-gray-600\b/g, to: 'text-gray-600 dark:text-gray-400' },
    { from: /\btext-gray-700\b/g, to: 'text-gray-700 dark:text-gray-300' },
    { from: /\btext-gray-500\b/g, to: 'text-gray-500 dark:text-gray-400' },
    { from: /\btext-gray-800\b/g, to: 'text-gray-800 dark:text-gray-200' },
    { from: /\btext-gray-400\b/g, to: 'text-gray-400 dark:text-gray-500' },
    
    // Backgrounds
    { from: /\bbg-white shadow\b/g, to: 'bg-white dark:bg-gray-800 shadow' },
    { from: /\bbg-white rounded-lg shadow\b/g, to: 'bg-white dark:bg-gray-800 rounded-lg shadow' },
    { from: /\bbg-white hover:bg-gray-50\b/g, to: 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700' },
    { from: /\bhover:bg-gray-50\b/g, to: 'hover:bg-gray-50 dark:hover:bg-gray-700' },
    { from: /\bbg-gray-50\b/g, to: 'bg-gray-50 dark:bg-gray-700' },
    { from: /\bbg-gray-100\b/g, to: 'bg-gray-100 dark:bg-gray-700' },
    
    // Status backgrounds
    { from: /\bbg-green-100\b/g, to: 'bg-green-100 dark:bg-green-900/20' },
    { from: /\bbg-red-50\b/g, to: 'bg-red-50 dark:bg-red-900/20' },
    { from: /\bbg-yellow-50\b/g, to: 'bg-yellow-50 dark:bg-yellow-900/20' },
    { from: /\bbg-blue-50\b/g, to: 'bg-blue-50 dark:bg-blue-900/20' },
    
    // Borders
    { from: /\bborder-gray-200\b/g, to: 'border-gray-200 dark:border-gray-700' },
    { from: /\bborder-gray-300\b/g, to: 'border-gray-300 dark:border-gray-600' },
    { from: /\bdivide-gray-200\b/g, to: 'divide-gray-200 dark:divide-gray-700' },
    
    // Status colors
    { from: /\btext-green-600\b/g, to: 'text-green-600 dark:text-green-400' },
    { from: /\btext-green-500\b/g, to: 'text-green-500 dark:text-green-400' },
    { from: /\btext-yellow-600\b/g, to: 'text-yellow-600 dark:text-yellow-400' },
    { from: /\btext-red-700\b/g, to: 'text-red-700 dark:text-red-400' },
    { from: /\btext-red-600\b/g, to: 'text-red-600 dark:text-red-400' },
    { from: /\btext-blue-600\b/g, to: 'text-blue-600 dark:text-blue-400' },
    { from: /\btext-blue-500\b/g, to: 'text-blue-500 dark:text-blue-400' },
    
    // Form elements
    { from: /\bborder border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\b/g, 
      to: 'border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white' },
    
    // Input elements
    { from: /\bblock w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500\b/g,
      to: 'block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white' },
    
    // Select elements
    { from: /\bblock w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\b/g,
      to: 'block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white' },
    
    // Textarea elements
    { from: /\bblock w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 resize-none\b/g,
      to: 'block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white resize-none' },
    
    // Checkbox elements
    { from: /\bh-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\b/g,
      to: 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700' },
    
    // Table elements
    { from: /\bbg-white divide-y divide-gray-200\b/g, to: 'bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700' },
    { from: /\bbg-gray-50 px-6 py-3\b/g, to: 'bg-gray-50 dark:bg-gray-700 px-6 py-3' },
    
    // Card elements
    { from: /\bbg-white overflow-hidden shadow rounded-lg\b/g, to: 'bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg' },
    { from: /\bbg-white px-4 py-5 sm:p-6\b/g, to: 'bg-white dark:bg-gray-800 px-4 py-5 sm:p-6' },
  ];

  let totalChanges = 0;
  let processedFiles = 0;

  pagesToFix.forEach(page => {
    const filePath = path.join(__dirname, '../../', page.path);
    
    if (!fs.existsSync(filePath)) {
      console.log(`⏭️  Skipping ${page.name} - file not found: ${page.path}`);
      return;
    }

    console.log(`🔄 Processing ${page.name}...`);
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let fileChanges = 0;

      // Apply replacements
      replacements.forEach(({ from, to }) => {
        const matches = content.match(from);
        if (matches) {
          content = content.replace(from, to);
          fileChanges += matches.length;
        }
      });

      if (fileChanges > 0) {
        // Write the updated content back to the file
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`  ✅ Updated ${page.name} (${fileChanges} changes)`);
        totalChanges += fileChanges;
      } else {
        console.log(`  ⏭️  ${page.name} - no changes needed`);
      }
      
      processedFiles++;

    } catch (error) {
      console.error(`  ❌ Error processing ${page.name}:`, error.message);
    }
  });

  console.log(`\n🎉 Successfully processed all main pages!`);
  console.log(`📊 Total files processed: ${processedFiles}`);
  console.log(`📊 Total changes made: ${totalChanges}`);
  console.log(`📁 Pages updated: ${pagesToFix.map(p => p.name).join(', ')}`);
}

// Run the fix
fixAllPagesDarkMode();
