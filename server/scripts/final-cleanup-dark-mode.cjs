#!/usr/bin/env node

/**
 * Final cleanup script for dark mode
 * This script will catch any remaining edge cases and ensure complete coverage
 */

const fs = require('fs');
const path = require('path');

function finalCleanupDarkMode() {
  console.log('🔧 Final cleanup for dark mode - catching all remaining cases...\n');

  // Define all directories to scan
  const directories = [
    'src/pages',
    'src/components'
  ];

  // Final cleanup replacements to catch edge cases
  const cleanupReplacements = [
    // Catch any remaining className with bg-white that doesn't have dark mode
    { from: /className="([^"]*\s+)?bg-white(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-800') || match.includes('dark:bg-gray-700')) return match;
        return match.replace('bg-white', 'bg-white dark:bg-gray-800');
      }
    },
    
    // Catch any remaining bg-gray-50 without dark mode
    { from: /className="([^"]*\s+)?bg-gray-50(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-gray-700')) return match;
        return match.replace('bg-gray-50', 'bg-gray-50 dark:bg-gray-700');
      }
    },
    
    // Catch any remaining text-gray-900 without dark mode
    { from: /className="([^"]*\s+)?text-gray-900(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:text-white')) return match;
        return match.replace('text-gray-900', 'text-gray-900 dark:text-white');
      }
    },
    
    // Catch any remaining text-gray-600 without dark mode
    { from: /className="([^"]*\s+)?text-gray-600(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:text-gray-400')) return match;
        return match.replace('text-gray-600', 'text-gray-600 dark:text-gray-400');
      }
    },
    
    // Catch any remaining text-gray-700 without dark mode
    { from: /className="([^"]*\s+)?text-gray-700(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:text-gray-300')) return match;
        return match.replace('text-gray-700', 'text-gray-700 dark:text-gray-300');
      }
    },
    
    // Catch any remaining text-gray-500 without dark mode
    { from: /className="([^"]*\s+)?text-gray-500(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:text-gray-400')) return match;
        return match.replace('text-gray-500', 'text-gray-500 dark:text-gray-400');
      }
    },
    
    // Catch any remaining border-gray-200 without dark mode
    { from: /className="([^"]*\s+)?border-gray-200(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:border-gray-700')) return match;
        return match.replace('border-gray-200', 'border-gray-200 dark:border-gray-700');
      }
    },
    
    // Catch any remaining border-gray-300 without dark mode
    { from: /className="([^"]*\s+)?border-gray-300(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:border-gray-600')) return match;
        return match.replace('border-gray-300', 'border-gray-300 dark:border-gray-600');
      }
    },
    
    // Catch any remaining divide-gray-200 without dark mode
    { from: /className="([^"]*\s+)?divide-gray-200(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:divide-gray-700')) return match;
        return match.replace('divide-gray-200', 'divide-gray-200 dark:divide-gray-700');
      }
    },
    
    // Catch any remaining hover:bg-gray-50 without dark mode
    { from: /className="([^"]*\s+)?hover:bg-gray-50(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:hover:bg-gray-700')) return match;
        return match.replace('hover:bg-gray-50', 'hover:bg-gray-50 dark:hover:bg-gray-700');
      }
    },
    
    // Catch any remaining focus:bg-white without dark mode
    { from: /className="([^"]*\s+)?focus:bg-white(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:focus:bg-gray-700')) return match;
        return match.replace('focus:bg-white', 'focus:bg-white dark:focus:bg-gray-700');
      }
    },
    
    // Catch any remaining status colors without dark mode
    { from: /className="([^"]*\s+)?text-green-600(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:text-green-400')) return match;
        return match.replace('text-green-600', 'text-green-600 dark:text-green-400');
      }
    },
    
    { from: /className="([^"]*\s+)?text-red-600(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:text-red-400')) return match;
        return match.replace('text-red-600', 'text-red-600 dark:text-red-400');
      }
    },
    
    { from: /className="([^"]*\s+)?text-yellow-600(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:text-yellow-400')) return match;
        return match.replace('text-yellow-600', 'text-yellow-600 dark:text-yellow-400');
      }
    },
    
    { from: /className="([^"]*\s+)?text-blue-600(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:text-blue-400')) return match;
        return match.replace('text-blue-600', 'text-blue-600 dark:text-blue-400');
      }
    },
    
    // Catch any remaining status backgrounds without dark mode
    { from: /className="([^"]*\s+)?bg-green-100(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-green-900')) return match;
        return match.replace('bg-green-100', 'bg-green-100 dark:bg-green-900/20');
      }
    },
    
    { from: /className="([^"]*\s+)?bg-red-50(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-red-900')) return match;
        return match.replace('bg-red-50', 'bg-red-50 dark:bg-red-900/20');
      }
    },
    
    { from: /className="([^"]*\s+)?bg-yellow-50(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-yellow-900')) return match;
        return match.replace('bg-yellow-50', 'bg-yellow-50 dark:bg-yellow-900/20');
      }
    },
    
    { from: /className="([^"]*\s+)?bg-blue-50(\s+[^"]*)?"/g,
      to: (match) => {
        if (match.includes('dark:bg-blue-900')) return match;
        return match.replace('bg-blue-50', 'bg-blue-50 dark:bg-blue-900/20');
      }
    }
  ];

  let totalChanges = 0;
  let processedFiles = 0;

  directories.forEach(dir => {
    const dirPath = path.join(__dirname, '../../', dir);
    
    if (!fs.existsSync(dirPath)) {
      console.log(`⏭️  Skipping ${dir} - directory not found`);
      return;
    }

    console.log(`🔄 Processing ${dir}...`);
    
    const files = getAllFiles(dirPath, ['.tsx', '.ts']);
    
    files.forEach(filePath => {
      const relativePath = path.relative(path.join(__dirname, '../../'), filePath);
      
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        let fileChanges = 0;

        // Apply cleanup replacements
        cleanupReplacements.forEach(({ from, to }) => {
          if (typeof to === 'function') {
            const beforeContent = content;
            content = content.replace(from, to);
            if (content !== beforeContent) {
              fileChanges++;
            }
          } else {
            const matches = content.match(from);
            if (matches) {
              content = content.replace(from, to);
              fileChanges += matches.length;
            }
          }
        });

        if (fileChanges > 0) {
          // Write the updated content back to the file
          fs.writeFileSync(filePath, content, 'utf8');
          console.log(`  ✅ ${relativePath}: ${fileChanges} cleanup fixes`);
          totalChanges += fileChanges;
        }
        
        processedFiles++;

      } catch (error) {
        console.error(`  ❌ Error processing ${relativePath}:`, error.message);
      }
    });
  });

  console.log(`\n🎉 Final cleanup completed!`);
  console.log(`📊 Total files processed: ${processedFiles}`);
  console.log(`📊 Total cleanup fixes applied: ${totalChanges}`);
  console.log(`📁 Directories scanned: ${directories.join(', ')}`);
  
  if (totalChanges === 0) {
    console.log(`\n✨ Perfect! No additional fixes needed - dark mode is complete!`);
  } else {
    console.log(`\n✨ Applied ${totalChanges} final cleanup fixes to ensure complete dark mode coverage!`);
  }
}

function getAllFiles(dir, extensions) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other irrelevant directories
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          files = files.concat(getAllFiles(fullPath, extensions));
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    });
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return files;
}

// Run the final cleanup
finalCleanupDarkMode();
