#!/usr/bin/env node

/**
 * Basic Audit System Test Script
 * Tests Feature 1: Basic Database Audit Schema and Core Functionality
 */

import { auditService } from '../services/auditService.js';
import { db } from '../config/database.js';

async function testBasicAuditFunctionality() {
  console.log('🧪 Testing Basic Audit Functionality...\n');

  try {
    // Test 1: Log a data change
    console.log('📝 Test 1: Logging data change...');
    const auditId1 = await auditService.logDataChange({
      tableName: 'accounts',
      recordId: '123e4567-e89b-12d3-a456-************',
      actionType: 'INSERT',
      newValues: {
        name: 'Test Account',
        type: 'ASSET',
        balance: 1000.00
      },
      userId: '456e7890-e89b-12d3-a456-************',
      userEmail: '<EMAIL>',
      userRole: 'ADMIN',
      companyId: '789e0123-e89b-12d3-a456-************',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 Test Browser',
      reason: 'Creating new test account',
      riskLevel: 'LOW'
    });
    console.log(`✅ Data change logged with ID: ${auditId1}`);

    // Test 2: Log an access attempt
    console.log('\n🔐 Test 2: Logging access attempt...');
    const auditId2 = await auditService.logAccess({
      userId: '456e7890-e89b-12d3-a456-************',
      action: 'LOGIN',
      resource: '/dashboard',
      success: true,
      ipAddress: '*************',
      deviceInfo: {
        browser: 'Chrome',
        os: 'Windows 10',
        device: 'Desktop'
      }
    });
    console.log(`✅ Access logged with ID: ${auditId2}`);

    // Test 3: Log a financial transaction
    console.log('\n💰 Test 3: Logging financial transaction...');
    const auditId3 = await auditService.logFinancialTransaction({
      transactionId: '321e6547-e89b-12d3-a456-************',
      accountId: '123e4567-e89b-12d3-a456-************',
      amount: 500.00,
      currency: 'TZS',
      actionType: 'DEBIT',
      previousBalance: 1000.00,
      newBalance: 500.00,
      authorizationLevel: 'MANAGER',
      approverId: '456e7890-e89b-12d3-a456-************',
      complianceCheckResult: {
        traCompliant: true,
        amlScreened: true,
        riskScore: 0.2
      }
    });
    console.log(`✅ Financial transaction logged with ID: ${auditId3}`);

    // Test 4: Log a failed access attempt
    console.log('\n❌ Test 4: Logging failed access attempt...');
    const auditId4 = await auditService.logAccess({
      userId: '456e7890-e89b-12d3-a456-************',
      action: 'LOGIN',
      resource: '/admin',
      success: false,
      failureReason: 'Insufficient permissions',
      ipAddress: '*************'
    });
    console.log(`✅ Failed access logged with ID: ${auditId4}`);

    // Test 5: Retrieve audit logs
    console.log('\n📊 Test 5: Retrieving audit logs...');
    const auditLogs = await db('audit_logs')
      .select('*')
      .orderBy('timestamp', 'desc')
      .limit(5);
    
    console.log(`✅ Retrieved ${auditLogs.length} audit logs:`);
    auditLogs.forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.action_type} on ${log.table_name}:${log.record_id} by ${log.user_email} (Risk: ${log.risk_level})`);
    });

    // Test 6: Retrieve access logs
    console.log('\n🔍 Test 6: Retrieving access logs...');
    const accessLogs = await db('access_audit_logs')
      .select('*')
      .orderBy('timestamp', 'desc')
      .limit(5);
    
    console.log(`✅ Retrieved ${accessLogs.length} access logs:`);
    accessLogs.forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.action} - ${log.success ? 'SUCCESS' : 'FAILED'} from ${log.ip_address}`);
    });

    // Test 7: Retrieve financial audit trail
    console.log('\n💳 Test 7: Retrieving financial audit trail...');
    const financialLogs = await db('financial_audit_trail')
      .select('*')
      .orderBy('timestamp', 'desc')
      .limit(5);
    
    console.log(`✅ Retrieved ${financialLogs.length} financial audit logs:`);
    financialLogs.forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.action_type} - ${log.amount} ${log.currency} (Auth: ${log.authorization_level})`);
    });

    // Test 8: Test data integrity
    console.log('\n🔒 Test 8: Testing data integrity...');
    const totalAuditLogs = await db('audit_logs').count('* as count').first();
    const totalAccessLogs = await db('access_audit_logs').count('* as count').first();
    const totalFinancialLogs = await db('financial_audit_trail').count('* as count').first();
    
    console.log(`✅ Data integrity check:`);
    console.log(`   - Audit logs: ${totalAuditLogs?.count} records`);
    console.log(`   - Access logs: ${totalAccessLogs?.count} records`);
    console.log(`   - Financial logs: ${totalFinancialLogs?.count} records`);

    console.log('\n🎉 All basic audit functionality tests passed!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Database schema created successfully');
    console.log('   ✅ Data change logging works');
    console.log('   ✅ Access logging works');
    console.log('   ✅ Financial transaction logging works');
    console.log('   ✅ Data retrieval works');
    console.log('   ✅ Data integrity maintained');
    
    console.log('\n🚀 Ready to proceed to Feature 2: Audit Middleware Integration');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    await db.destroy();
  }
}

// Run the test
if (require.main === module) {
  testBasicAuditFunctionality();
}
