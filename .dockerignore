# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
dist
build

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Testing
coverage
.nyc_output
test-results

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Documentation
README.md
docs

# Docker
Dockerfile*
docker-compose*
.dockerignore

# CI/CD
.github
.gitlab-ci.yml
.travis.yml
.circleci

# Temporary files
tmp
temp
.tmp

# Development tools
.eslintrc*
.prettierrc*
jest.config.js
playwright.config.ts
vite.config.ts
tsconfig*.json

# Database files (for local development)
*.sqlite
*.db
