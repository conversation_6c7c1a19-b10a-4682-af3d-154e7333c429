# 🐳 Docker & CI/CD Setup - Comprehensive Accounting System

## 🎯 Overview

This document provides complete instructions for containerizing and deploying the Comprehensive Accounting System using Docker and automated CI/CD pipelines.

## 🚀 Quick Start

### Prerequisites

- Docker 20.10+ and Docker Compose 2.0+
- Node.js 20+ (for local development)
- Git (for version control)

### Development Environment

```bash
# Clone the repository
git clone <your-repo-url>
cd accounting-system

# Copy environment configuration
cp .env.example .env

# Start development environment
npm run docker:dev

# Access the application
# Frontend: http://localhost:5173
# Backend API: http://localhost:3002
# Database: localhost:5433
```

### Production Deployment

```bash
# Build and deploy production environment
npm run docker:prod:build

# Access the application
# Application: http://localhost:80
# API: http://localhost:80/api
```

## 📁 Docker Configuration Files

### Core Files

- **`Dockerfile`** - Multi-stage build configuration
- **`docker-compose.yml`** - Production environment
- **`docker-compose.dev.yml`** - Development environment
- **`.dockerignore`** - Files excluded from Docker context
- **`nginx.conf`** - Reverse proxy configuration

### Environment Files

- **`.env.example`** - Environment template
- **`.env.production`** - Production configuration
- **`.env`** - Local development (create from example)

## 🏗️ Architecture

### Production Stack

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Nginx       │    │   Application   │    │   PostgreSQL    │
│  (Port 80/443)  │────│   (Port 3002)   │────│   (Port 5432)   │
│  Load Balancer  │    │   Node.js API   │    │    Database     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │      Redis      │
                       │   (Port 6379)   │
                       │     Cache       │
                       └─────────────────┘
```

### Development Stack

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Frontend    │    │   Application   │    │   PostgreSQL    │
│  (Port 5173)    │    │   (Port 3002)   │    │   (Port 5433)   │
│   Vite Dev      │    │   Hot Reload    │    │   Dev Database  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐    ┌─────────────────┐
                       │      Redis      │    │   PostgreSQL    │
                       │   (Port 6380)   │    │   (Port 5434)   │
                       │   Dev Cache     │    │  Test Database  │
                       └─────────────────┘    └─────────────────┘
```

## 🛠️ Available Commands

### Docker Commands

```bash
# Development
npm run docker:dev              # Start development environment
npm run docker:dev:build        # Build and start development
npm run docker:dev:down         # Stop development environment

# Production
npm run docker:prod             # Start production environment
npm run docker:prod:build       # Build and start production
npm run docker:prod:down        # Stop production environment

# Utilities
npm run docker:logs             # View all service logs
npm run docker:clean            # Clean up Docker resources
```

### Deployment Commands

```bash
# Deploy to environments
npm run deploy:dev              # Deploy to development
npm run deploy:staging          # Deploy to staging
npm run deploy:prod             # Deploy to production

# Database operations
npm run backup:db               # Create database backup
npm run restore:db              # Restore from backup
```

### Manual Deployment Scripts

```bash
# Test application
./scripts/deploy.sh development test

# Build application and Docker image
./scripts/deploy.sh production build

# Deploy to environment
./scripts/deploy.sh production deploy

# Check application health
./scripts/deploy.sh production health

# View application logs
./scripts/deploy.sh production logs

# Stop application
./scripts/deploy.sh production stop
```

## 🔧 Configuration

### Environment Variables

#### Required Variables

```bash
# Application
NODE_ENV=production
PORT=3002
APP_NAME="Comprehensive Accounting System"

# Database
DB_HOST=postgres
DB_PORT=5432
DB_NAME=accounting_system
DB_USER=postgres
DB_PASSWORD=your-secure-password

# Security
JWT_SECRET=your-super-secret-jwt-key
CORS_ORIGIN=https://yourdomain.com
```

#### Optional Variables

```bash
# Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis-password

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Monitoring
SENTRY_DSN=https://your-sentry-dsn
LOG_LEVEL=info
```

### SSL/HTTPS Configuration

1. **Generate SSL certificates:**
```bash
mkdir -p ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/key.pem -out ssl/cert.pem
```

2. **Update nginx.conf:**
```bash
# Uncomment HTTPS server block
# Update certificate paths
```

3. **Update environment:**
```bash
CORS_ORIGIN=https://yourdomain.com
```

## 🚀 CI/CD Pipeline

### GitHub Actions Workflow

The project includes automated CI/CD that runs on:

- **Pull Requests:** Testing, linting, security scans
- **Main Branch:** Full deployment pipeline
- **Develop Branch:** Staging deployment

### Pipeline Stages

1. **Test Stage**
   - Linting and code quality
   - Unit and integration tests
   - E2E testing with Playwright
   - Security vulnerability scanning
   - Test coverage reporting

2. **Build Stage**
   - Application compilation
   - Docker image building
   - Artifact generation

3. **Deploy Stage**
   - Docker image publishing
   - Environment deployment
   - Health checks
   - Rollback on failure

### Required Secrets

Configure these in your GitHub repository:

```bash
# Docker Hub
DOCKER_USERNAME=your-docker-username
DOCKER_PASSWORD=your-docker-password

# Production Server
PRODUCTION_HOST=your-server.com
PRODUCTION_USER=deploy
PRODUCTION_SSH_KEY=your-private-ssh-key

# Database
DB_PASSWORD=your-database-password
JWT_SECRET=your-jwt-secret

# Optional
CODECOV_TOKEN=your-codecov-token
SENTRY_DSN=your-sentry-dsn
```

## 🔍 Monitoring and Maintenance

### Health Checks

```bash
# Application health
curl http://localhost:3002/health

# Service status
docker-compose ps

# View logs
docker-compose logs -f app
```

### Database Operations

```bash
# Create backup
./scripts/backup-db.sh production backup

# List backups
./scripts/backup-db.sh production list

# Restore backup
./scripts/backup-db.sh production restore backup_file.sql.gz

# Connect to database
docker-compose exec postgres psql -U postgres -d accounting_system
```

### Performance Monitoring

- Application metrics at `/metrics`
- Health status at `/health`
- Detailed health at `/health/detailed`

## 🛡️ Security Features

### Container Security

- Non-root user execution
- Multi-stage builds for minimal attack surface
- Regular security scanning with Trivy
- Dependency vulnerability monitoring

### Network Security

- Internal Docker networks
- Nginx reverse proxy with security headers
- Rate limiting and DDoS protection
- SSL/TLS encryption support

### Application Security

- JWT token authentication
- Input validation with Zod
- SQL injection prevention
- CORS protection
- Helmet.js security headers

## 🔧 Troubleshooting

### Common Issues

1. **Port conflicts:**
```bash
lsof -i :3002
# Change port in .env or kill conflicting process
```

2. **Database connection:**
```bash
docker-compose logs postgres
docker-compose restart postgres
```

3. **Permission issues:**
```bash
sudo chown -R $USER:$USER .
chmod +x scripts/*.sh
```

4. **Out of disk space:**
```bash
docker system prune -f
docker system df
```

### Validation

Run the validation script to check configuration:

```bash
./scripts/validate-docker.sh
```

## 📚 Additional Resources

- [Docker Documentation](docs/DOCKER_DEPLOYMENT.md)
- [API Documentation](docs/API.md)
- [Database Schema](docs/DATABASE.md)
- [Security Guide](docs/SECURITY.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes and test locally
4. Run validation: `./scripts/validate-docker.sh`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
