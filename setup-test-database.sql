-- Setup test database for accounting system
-- Run this as postgres superuser: psql -U postgres -f setup-test-database.sql

\echo 'Setting up test database...'

-- Terminate any existing connections to the test database
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname = 'accounting_system_test' AND pid <> pg_backend_pid();

-- Drop and recreate test database
DROP DATABASE IF EXISTS accounting_system_test;
CREATE DATABASE accounting_system_test OWNER curtis;

-- Connect to the test database
\c accounting_system_test

-- Grant all privileges on the database to curtis
GRANT ALL PRIVILEGES ON DATABASE accounting_system_test TO curtis;

-- Grant usage and create on schema
GRANT USAGE, CREATE ON SCHEMA public TO curtis;

-- Grant all privileges on all tables in public schema
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO curtis;

-- Grant all privileges on all sequences in public schema
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO curtis;

-- Grant all privileges on all functions in public schema
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO curtis;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO curtis;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO curtis;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO curtis;

-- Make curtis owner of public schema
ALTER SCHEMA public OWNER TO curtis;

\echo 'Test database setup complete!'
\echo 'Database: accounting_system_test'
\echo 'Owner: curtis'
