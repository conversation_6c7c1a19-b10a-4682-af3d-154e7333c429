# NyotaBalance Enterprise Deployment Guide

## 📋 **Overview**

This guide covers the deployment of NyotaBalance with Phase 2 performance optimizations and enterprise features. The system now supports small to medium enterprise deployments (10-500 users) with advanced features like transaction templates, batch operations, approval workflows, advanced reporting, and enhanced bank integration.

## 🏗️ **Architecture Overview**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │     Frontend    │    │    Database     │
│    (Nginx)      │────│   (React/Vite)  │    │  (PostgreSQL)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │     Backend     │    │      Redis      │
                       │   (Node.js)     │────│    (Cache)      │
                       └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   Job Queue     │
                       │   (Bull/Redis)  │
                       └─────────────────┘
```

## 🔧 **Prerequisites**

### **System Requirements**

#### **Minimum (Small Enterprise: 10-50 users)**
- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 100GB SSD
- **Network**: 100 Mbps

#### **Recommended (Medium Enterprise: 50-500 users)**
- **CPU**: 8 cores
- **RAM**: 16GB
- **Storage**: 500GB SSD
- **Network**: 1 Gbps

#### **High Availability (Large Enterprise: 500+ users)**
- **CPU**: 16+ cores
- **RAM**: 32GB+
- **Storage**: 1TB+ SSD (with replication)
- **Network**: 10 Gbps

### **Software Dependencies**

- **Node.js**: 18.x or higher
- **PostgreSQL**: 14.x or higher
- **Redis**: 6.x or higher
- **Nginx**: 1.20.x or higher (for production)
- **Docker**: 20.x or higher (optional)
- **PM2**: Latest (for process management)

## 🚀 **Deployment Steps**

### **1. Environment Setup**

#### **Create Environment Files**

```bash
# Production environment (.env.production)
NODE_ENV=production
PORT=3000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=nyotabalance_prod
DB_USER=nyotabalance_user
DB_PASSWORD=your_secure_password
DB_POOL_MIN=10
DB_POOL_MAX=50

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# Cache Configuration
CACHE_PREFIX=nyotabalance_prod
CACHE_TTL=3600

# Security
JWT_SECRET=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here
SESSION_SECRET=your_session_secret_here

# External Services
PLAID_CLIENT_ID=your_plaid_client_id
PLAID_SECRET=your_plaid_secret
PLAID_ENV=production

# File Storage
UPLOAD_DIR=/var/uploads/nyotabalance
MAX_FILE_SIZE=50MB

# Monitoring
LOG_LEVEL=info
ENABLE_METRICS=true
SENTRY_DSN=your_sentry_dsn

# Performance
ENABLE_COMPRESSION=true
ENABLE_RATE_LIMITING=true
MAX_REQUESTS_PER_MINUTE=1000
```

#### **Database Setup**

```bash
# 1. Create PostgreSQL database and user
sudo -u postgres psql
CREATE DATABASE nyotabalance_prod;
CREATE USER nyotabalance_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE nyotabalance_prod TO nyotabalance_user;
ALTER USER nyotabalance_user CREATEDB;
\q

# 2. Configure PostgreSQL for performance
sudo nano /etc/postgresql/14/main/postgresql.conf

# Add these optimizations:
shared_buffers = 256MB                    # 25% of RAM
effective_cache_size = 1GB                # 75% of RAM
work_mem = 4MB                           # Per connection
maintenance_work_mem = 64MB              # For maintenance operations
checkpoint_completion_target = 0.9       # Spread checkpoints
wal_buffers = 16MB                       # WAL buffer size
default_statistics_target = 100          # Query planner statistics

# 3. Restart PostgreSQL
sudo systemctl restart postgresql
```

#### **Redis Setup**

```bash
# 1. Install and configure Redis
sudo apt update
sudo apt install redis-server

# 2. Configure Redis for production
sudo nano /etc/redis/redis.conf

# Key configurations:
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
requirepass your_redis_password

# 3. Start Redis
sudo systemctl enable redis-server
sudo systemctl start redis-server
```

### **2. Application Deployment**

#### **Build and Deploy**

```bash
# 1. Clone repository
git clone https://github.com/your-org/nyotabalance.git
cd nyotabalance

# 2. Install dependencies
npm install

# 3. Build frontend
npm run build

# 4. Run database migrations
npm run migrate

# 5. Seed initial data (if needed)
npm run seed:production

# 6. Start application with PM2
npm install -g pm2
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

#### **PM2 Configuration (ecosystem.config.js)**

```javascript
module.exports = {
  apps: [
    {
      name: 'nyotabalance-api',
      script: './server/index.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'development',
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
      },
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=2048',
    },
    {
      name: 'nyotabalance-worker',
      script: './server/workers/index.js',
      instances: 2,
      env_production: {
        NODE_ENV: 'production',
        WORKER_TYPE: 'background',
      },
      max_memory_restart: '512M',
    },
  ],
};
```

### **3. Nginx Configuration**

```nginx
# /etc/nginx/sites-available/nyotabalance
upstream nyotabalance_backend {
    least_conn;
    server 127.0.0.1:3000;
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
    server 127.0.0.1:3003;
}

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL Configuration
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    # Static Files
    location /static/ {
        alias /var/www/nyotabalance/dist/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API Routes
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://nyotabalance_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # Login Rate Limiting
    location /api/auth/login {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://nyotabalance_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Frontend Application
    location / {
        try_files $uri $uri/ /index.html;
        root /var/www/nyotabalance/dist;
        index index.html;
        
        # Cache HTML files for short time
        location ~* \.html$ {
            expires 5m;
            add_header Cache-Control "public, must-revalidate";
        }
    }

    # Health Check
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

### **4. SSL Certificate Setup**

```bash
# Using Let's Encrypt (Certbot)
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### **5. Monitoring and Logging**

#### **Log Rotation**

```bash
# /etc/logrotate.d/nyotabalance
/var/www/nyotabalance/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reloadLogs
    endscript
}
```

#### **Health Monitoring Script**

```bash
#!/bin/bash
# /usr/local/bin/health-check.sh

# Check application health
curl -f http://localhost:3000/health || {
    echo "Application health check failed"
    pm2 restart nyotabalance-api
}

# Check database connection
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT 1;" || {
    echo "Database connection failed"
    # Send alert
}

# Check Redis connection
redis-cli -h $REDIS_HOST -p $REDIS_PORT ping || {
    echo "Redis connection failed"
    # Send alert
}

# Check disk space
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is above 80%: $DISK_USAGE%"
    # Send alert
fi
```

## 🔒 **Security Checklist**

### **Application Security**
- [ ] Environment variables secured
- [ ] JWT secrets rotated regularly
- [ ] Rate limiting configured
- [ ] Input validation implemented
- [ ] SQL injection protection enabled
- [ ] XSS protection headers set
- [ ] CSRF protection enabled

### **Infrastructure Security**
- [ ] Firewall configured (only necessary ports open)
- [ ] SSH key-based authentication
- [ ] Regular security updates applied
- [ ] Database access restricted
- [ ] Redis password protected
- [ ] SSL/TLS certificates valid

### **Data Security**
- [ ] Database backups encrypted
- [ ] Sensitive data encrypted at rest
- [ ] Audit logging enabled
- [ ] Access controls implemented
- [ ] Data retention policies defined

## 📊 **Performance Optimization**

### **Database Optimization**
```sql
-- Create performance indexes
CREATE INDEX CONCURRENTLY idx_transactions_company_date_status 
ON transactions(company_id, transaction_date DESC, status);

CREATE INDEX CONCURRENTLY idx_transaction_entries_account_date 
ON transaction_entries(account_id, created_at DESC);

-- Analyze tables for query optimization
ANALYZE transactions;
ANALYZE transaction_entries;
ANALYZE accounts;
```

### **Application Optimization**
```javascript
// Enable compression
app.use(compression());

// Enable caching headers
app.use((req, res, next) => {
  if (req.url.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg)$/)) {
    res.setHeader('Cache-Control', 'public, max-age=********');
  }
  next();
});

// Connection pooling
const pool = {
  min: 10,
  max: 50,
  acquireTimeoutMillis: 30000,
  createTimeoutMillis: 30000,
  destroyTimeoutMillis: 5000,
  idleTimeoutMillis: 30000,
};
```

## 🔄 **Backup and Recovery**

### **Database Backup**
```bash
#!/bin/bash
# /usr/local/bin/backup-db.sh

BACKUP_DIR="/var/backups/nyotabalance"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/nyotabalance_$DATE.sql"

# Create backup directory
mkdir -p $BACKUP_DIR

# Create database backup
PGPASSWORD=$DB_PASSWORD pg_dump -h $DB_HOST -U $DB_USER $DB_NAME > $BACKUP_FILE

# Compress backup
gzip $BACKUP_FILE

# Upload to cloud storage (optional)
# aws s3 cp $BACKUP_FILE.gz s3://your-backup-bucket/

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

echo "Backup completed: $BACKUP_FILE.gz"
```

### **Automated Backup Schedule**
```bash
# Add to crontab
0 2 * * * /usr/local/bin/backup-db.sh
0 3 * * 0 /usr/local/bin/backup-files.sh  # Weekly file backup
```

## 📈 **Scaling Considerations**

### **Horizontal Scaling**
- Load balancer with multiple application instances
- Database read replicas for read-heavy operations
- Redis cluster for cache scaling
- CDN for static asset delivery

### **Vertical Scaling**
- Increase server resources (CPU, RAM, Storage)
- Optimize database configuration
- Tune application performance
- Monitor and adjust based on metrics

## 🚨 **Troubleshooting**

### **Common Issues**

#### **High Memory Usage**
```bash
# Check memory usage
free -h
pm2 monit

# Restart application if needed
pm2 restart all
```

#### **Database Connection Issues**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check connections
sudo -u postgres psql -c "SELECT count(*) FROM pg_stat_activity;"

# Check logs
sudo tail -f /var/log/postgresql/postgresql-14-main.log
```

#### **Redis Connection Issues**
```bash
# Check Redis status
sudo systemctl status redis-server

# Test connection
redis-cli ping

# Check memory usage
redis-cli info memory
```

## 📞 **Support and Maintenance**

### **Regular Maintenance Tasks**
- [ ] Weekly: Review application logs
- [ ] Weekly: Check system resources
- [ ] Monthly: Update dependencies
- [ ] Monthly: Review security patches
- [ ] Quarterly: Performance optimization review
- [ ] Quarterly: Backup restoration test

### **Emergency Contacts**
- **System Administrator**: [contact info]
- **Database Administrator**: [contact info]
- **Development Team**: [contact info]
- **Hosting Provider**: [contact info]

---

## 🎯 **Post-Deployment Verification**

After deployment, verify these critical functions:

1. **User Authentication**: Login/logout functionality
2. **Transaction Creation**: Create and edit transactions
3. **Template System**: Create and apply templates
4. **Batch Operations**: Import/export functionality
5. **Bank Integration**: Connect and sync bank accounts
6. **Reporting**: Generate custom reports
7. **Workflow System**: Create and execute workflows
8. **Performance**: Response times under load
9. **Security**: SSL certificates and headers
10. **Monitoring**: Logs and metrics collection

**Deployment Status**: ✅ Ready for Medium Enterprise (50-500 users)

For additional support or advanced configuration, please refer to the technical documentation or contact the development team.
