version: '3.8'

services:
  # PostgreSQL Database for development
  postgres-dev:
    image: postgres:16-alpine
    container_name: accounting-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-accounting_system_dev}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres}
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./setup-database.sql:/docker-entrypoint-initdb.d/01-setup.sql
    ports:
      - "${DB_PORT:-5433}:5432"
    networks:
      - accounting-dev-network

  # PostgreSQL Database for testing
  postgres-test:
    image: postgres:16-alpine
    container_name: accounting-postgres-test
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${TEST_DB_NAME:-accounting_system_test}
      POSTGRES_USER: ${TEST_DB_USER:-postgres}
      POSTGRES_PASSWORD: ${TEST_DB_PASSWORD:-postgres}
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./setup-test-database.sql:/docker-entrypoint-initdb.d/01-setup.sql
    ports:
      - "${TEST_DB_PORT:-5434}:5432"
    networks:
      - accounting-dev-network

  # Redis for development
  redis-dev:
    image: redis:7-alpine
    container_name: accounting-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "${REDIS_PORT:-6380}:6379"
    networks:
      - accounting-dev-network

  # Development application
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: dev
    container_name: accounting-app-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3002
      DB_HOST: postgres-dev
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-accounting_system_dev}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-postgres}
      TEST_DB_HOST: postgres-test
      TEST_DB_PORT: 5432
      TEST_DB_NAME: ${TEST_DB_NAME:-accounting_system_test}
      TEST_DB_USER: ${TEST_DB_USER:-postgres}
      TEST_DB_PASSWORD: ${TEST_DB_PASSWORD:-postgres}
      REDIS_HOST: redis-dev
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET:-dev-jwt-secret-key}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:5173}
    ports:
      - "${APP_PORT:-3002}:3002"
      - "${VITE_PORT:-5173}:5173"
    depends_on:
      - postgres-dev
      - postgres-test
      - redis-dev
    networks:
      - accounting-dev-network
    volumes:
      - .:/app
      - /app/node_modules
      - dev_logs:/app/logs
    command: npm run dev

volumes:
  postgres_dev_data:
    driver: local
  postgres_test_data:
    driver: local
  redis_dev_data:
    driver: local
  dev_logs:
    driver: local

networks:
  accounting-dev-network:
    driver: bridge
